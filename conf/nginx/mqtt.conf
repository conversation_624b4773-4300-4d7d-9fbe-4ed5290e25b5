# Useless for now
server {
    listen       8084 ssl;
    server_name spendr-mqtt-test.virtualcards.us;
    ssl_certificate /etc/letsencrypt/live/spendr-mqtt-test.virtualcards.us-0002/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/spendr-mqtt-test.virtualcards.us-0002/privkey.pem;
    # ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1.2 TLSv1.3;

    location / {
        proxy_redirect off;
        proxy_pass http://localhost:8083;
        proxy_set_header Host $host;
        proxy_set_header X-Real_IP $remote_addr;
        proxy_set_header X-Forwarded-For $remote_addr:$remote_port;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}