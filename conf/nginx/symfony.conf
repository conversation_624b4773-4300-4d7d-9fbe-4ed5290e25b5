    client_max_body_size 100M;
    proxy_read_timeout 3600;
    proxy_connect_timeout 3600;
    proxy_send_timeout 3600;
    fastcgi_read_timeout 3600;

    tcp_nodelay on;
    keepalive_timeout 65;

    index index.php index.html;
    
    location ~* \.(js|css|png|jpg|gif|jpeg)$ {
        add_header Access-Control-Allow-Origin *;
        access_log off;
        log_not_found off;
        expires max;
    }
    
    location ~\.(yml|yaml|local)$ {
        deny all;
        return 404;
    }

    location / {
        try_files $uri /index.php$is_args$args;
    }

    location ~ ^/index\.php(/|$) {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
     #   fastcgi_pass localhost:9000;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;

        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;

        internal;
    }

    location ~ \.php$ {
        deny all;
        return 404;
    }

    location ^~ /_profiler/open {
        deny all;
        return 404;
    }
    
    location ^~ /_fragment {
        deny all;
        return 404;
    }
    
    location ^~ /_proxy {
        deny all;
        return 404;
    }

    location ~ /\.ht {
        deny all;
        return 404;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
