program:pf_message_consumer]
command=php /path/to/span-web/bin/console enqueue:consume --setup-broker --message-limit=50 --time-limit="now + 10 minutes" --env=prod --no-debug
process_name=%(program_name)s_%(process_num)02d
numprocs=1
autostart=true
autorestart=true
startsecs=0
user=www-data
redirect_stderr=true

[program:pf_message_consumer_privacy]
command=php /path/to/span-web/bin/console enqueue:consume --setup-broker --message-limit=50 --time-limit="now + 10 minutes" --client=privacy --env=prod --no-debug
process_name=%(program_name)s_%(process_num)02d
numprocs=1
autostart=true
autorestart=true
startsecs=0
user=www-data
redirect_stderr=true

[program:pf_message_consumer_rapyd]
command=php /path/to/span-web/bin/console enqueue:consume --setup-broker --message-limit=50 --time-limit="now + 10 minutes" --client=rapyd --env=prod --no-debug
process_name=%(program_name)s_%(process_num)02d
numprocs=1
autostart=true
autorestart=true
startsecs=0
user=www-data
redirect_stderr=true

[program:pf_message_consumer_coinflow]
command=php /path/to/span-web/bin/console enqueue:consume --setup-broker --message-limit=50 --time-limit="now + 10 minutes" --client=coinflow --env=prod --no-debug
process_name=%(program_name)s_%(process_num)02d
numprocs=1
autostart=true
autorestart=true
startsecs=0
user=www-data
redirect_stderr=true

[program:pf_message_consumer_spendr]
command=php /path/to/span-web/bin/console enqueue:consume --setup-broker --message-limit=50 --time-limit="now + 10 minutes" --client=spendr --env=prod --no-debug
process_name=%(program_name)s_%(process_num)02d
numprocs=1
autostart=true
autorestart=true
startsecs=0
user=www-data
redirect_stderr=true

[program:pf_message_consumer_spendr_promo]
command=php /path/to/span-web/bin/console enqueue:consume --setup-broker --message-limit=50 --time-limit="now + 10 minutes" --client=spendr_promo --env=prod --no-debug
process_name=%(program_name)s_%(process_num)02d
numprocs=1
autostart=true
autorestart=true
startsecs=0
user=www-data
redirect_stderr=true

[program:pf_message_consumer_spendr_bank_ledger]
command=php /path/to/span-web/bin/console enqueue:consume --setup-broker --message-limit=50 --time-limit="now + 10 minutes" --client=spendr_bank_ledger --env=prod --no-debug
process_name=%(program_name)s_%(process_num)02d
numprocs=1
autostart=true
autorestart=true
startsecs=0
user=www-data
redirect_stderr=true
