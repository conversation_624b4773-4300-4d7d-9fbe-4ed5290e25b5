
INSERT INTO `country` VALUES (3, 'Belgium', 'BE', 'BEL', '32', '0', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (6, 'Romania', 'RO', 'ROU', '40', '0', 1, 1, 1, 'Europe', 'EMEA', 'RON');
INSERT INTO `country` VALUES (7, 'Bulgaria', 'BG', 'BGR', '359', '', 1, 1, 1, 'Europe', 'EMEA', 'BGN');
INSERT INTO `country` VALUES (8, 'Czech Republic', 'CZ', 'CZE', '420', '2', 1, 1, 1, 'Europe', 'EMEA', 'CZK');
INSERT INTO `country` VALUES (9, 'Denmark', 'DK', 'DNK', '45', '', 1, 1, 1, 'Europe', 'EMEA', 'DKK');
INSERT INTO `country` VALUES (10, 'Estonia', 'EE', 'EST', '372', '32', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (11, 'Hungary', 'HU', 'HUN', '36', '6', 1, 1, 1, 'Europe', 'EMEA', 'HUF');
INSERT INTO `country` VALUES (12, 'Latvia', 'LV', 'LVA', '371', '8', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (13, 'Lithuania', 'LT', 'LTU', '370', '0', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (14, 'Poland', 'PL', 'POL', '48', '0', 1, 1, 1, 'Europe', 'EMEA', 'PLN');
INSERT INTO `country` VALUES (15, 'Slovakia', 'SK', 'SVK', '421', '0', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (17, 'Malta', 'MT', 'MLT', '356', '0', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (18, 'Austria', 'AT', 'AUT', '43', '0', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (19, 'Cyprus', 'CY', 'CYP', '357', '22', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (20, 'Finland', 'FI', 'FIN', '358', '0', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (21, 'France', 'FR', 'FRA', '33', '0', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (22, 'Germany', 'DE', 'DEU', '49', '0', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (23, 'Greece', 'GR', 'GRC', '30', '21', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (24, 'Ireland', 'IE', 'IRL', '353', '0', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (25, 'Italy', 'IT', 'ITA', '39', '2', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (26, 'Luxembourg', 'LU', 'LUX', '352', '', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (27, 'Netherlands', 'NL', 'NLD', '31', '0', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (28, 'Portugal', 'PT', 'PRT', '351', '21', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (30, 'Sweden', 'SE', 'SWE', '46', '0', 1, 1, 1, 'Europe', 'EMEA', 'SEK');
INSERT INTO `country` VALUES (32, 'United Kingdom', 'GB', 'GBR', '44', '0', 1, 1, 1, 'Europe', 'EMEA', 'GBP');
INSERT INTO `country` VALUES (33, 'Spain', 'ES', 'ESP', '34', '822', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (34, 'Sierra Leone', 'SL', 'SLE', '232', '0', 1, 0, 0, 'Africa', 'EMEA', 'SLL');
INSERT INTO `country` VALUES (36, 'Albania', 'AL', 'ALB', '355', '0', 1, 1, 1, 'Europe', 'EMEA', 'ALL');
INSERT INTO `country` VALUES (37, 'Algeria', 'DZ', 'DZA', '213', '', 1, 1, 1, 'Africa', 'EMEA', 'DZD');
INSERT INTO `country` VALUES (38, 'American Samoa', 'AS', 'ASM', '1', '684', 1, 0, 1, '', 'AMERICAS', 'USD');
INSERT INTO `country` VALUES (39, 'Andorra', 'AD', 'AND', '376', '7', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (40, 'Angola', 'AO', 'AGO', '244', '0', 1, 1, 1, 'Africa', 'EMEA', 'AOA');
INSERT INTO `country` VALUES (41, 'Anguilla', 'AI', 'AIA', '1', '1+', 1, 1, 1, 'C-America', 'AMERICAS', 'XCD');
INSERT INTO `country` VALUES (42, 'Antigua and Barbuda', 'AG', 'ATG', '1', '1+', 1, 1, 1, 'C-America', 'AMERICAS', 'XCD');
INSERT INTO `country` VALUES (43, 'Argentina', 'AR', 'ARG', '54', '0', 1, 1, 1, 'S-America', 'AMERICAS', 'ARS');
INSERT INTO `country` VALUES (44, 'Armenia', 'AM', 'ARM', '374', '0', 1, 1, 1, 'Europe', 'EMEA', 'AMD');
INSERT INTO `country` VALUES (45, 'Aruba', 'AW', 'ABW', '297', '', 1, 1, 1, 'C-America', 'AMERICAS', 'AWG');
INSERT INTO `country` VALUES (46, 'Australia', 'AU', 'AUS', '61', '0', 1, 1, 1, 'Oceania', 'APAC', 'AUD');
INSERT INTO `country` VALUES (47, 'Azerbaijan', 'AZ', 'AZE', '994', '8', 1, 1, 1, 'Europe', 'EMEA', 'AZN');
INSERT INTO `country` VALUES (48, 'Bahamas', 'BS', 'BHS', '1', '1+', 1, 1, 1, 'C-America', 'AMERICAS', 'BSD');
INSERT INTO `country` VALUES (49, 'Bahrain', 'BH', 'BHR', '973', '', 1, 1, 1, 'Asia', 'APAC', 'BHD');
INSERT INTO `country` VALUES (50, 'Bangladesh', 'BD', 'BGD', '880', '0', 1, 1, 1, 'Asia', 'APAC', 'BDT');
INSERT INTO `country` VALUES (51, 'Barbados', 'BB', 'BRB', '1', '1+', 1, 1, 1, 'C-America', 'AMERICAS', 'BBD');
INSERT INTO `country` VALUES (53, 'Belize', 'BZ', 'BLZ', '501', '2', 1, 1, 1, 'C-America', 'AMERICAS', 'BZD');
INSERT INTO `country` VALUES (54, 'Benin', 'BJ', 'BEN', '229', '20', 1, 1, 1, 'Africa', 'EMEA', 'XOF');
INSERT INTO `country` VALUES (55, 'Bermuda', 'BM', 'BMU', '1', '1+', 1, 1, 1, 'C-America', 'AMERICAS', 'BMD');
INSERT INTO `country` VALUES (56, 'Bhutan', 'BT', 'BTN', '975', '2', 1, 1, 1, 'Asia', 'APAC', 'BTN');
INSERT INTO `country` VALUES (57, 'Bolivia', 'BO', 'BOL', '591', '0', 1, 1, 1, 'S-America', 'AMERICAS', 'BOB');
INSERT INTO `country` VALUES (59, 'Bosnia Herz.', 'BA', 'BIH', '387', '0', 1, 1, 1, 'Europe', 'EMEA', 'BAM');
INSERT INTO `country` VALUES (60, 'Botswana', 'BW', 'BWA', '267', '3', 1, 1, 1, 'Africa', 'EMEA', 'BWP');
INSERT INTO `country` VALUES (61, 'Brazil', 'BR', 'BRA', '55', '0', 1, 1, 1, 'S-America', 'AMERICAS', 'BRL');
INSERT INTO `country` VALUES (62, 'Brunei', 'BN', 'BRN', '673', '2', 1, 1, 1, 'Asia', 'APAC', 'BND');
INSERT INTO `country` VALUES (63, 'Burkina Faso', 'BF', 'BFA', '226', '20', 1, 1, 1, 'Africa', 'EMEA', 'XOF');
INSERT INTO `country` VALUES (64, 'Burundi', 'BI', 'BDI', '257', '', 1, 1, 1, 'Africa', 'EMEA', 'BIF');
INSERT INTO `country` VALUES (65, 'Cambodia', 'KH', 'KHM', '855', '0', 1, 1, 1, 'Asia', 'APAC', 'HKD');
INSERT INTO `country` VALUES (66, 'Cameroon', 'CM', 'CMR', '237', '', 1, 1, 1, 'Africa', 'EMEA', 'XAF');
INSERT INTO `country` VALUES (67, 'Canada', 'CA', 'CAN', '1', '1+', 1, 1, 1, 'N-America', 'AMERICAS', 'CAD');
INSERT INTO `country` VALUES (68, 'Cape Verde Islands', 'CV', 'CPV', '238', '', 1, 1, 1, 'Africa', 'EMEA', 'CVE');
INSERT INTO `country` VALUES (69, 'Cayman Islands', 'KY', 'CYM', '1', '1+', 1, 1, 1, 'C-America', 'AMERICAS', 'KYD');
INSERT INTO `country` VALUES (70, 'Central African Rep', 'CF', 'CAF', '236', '', 1, 1, 1, 'Africa', 'EMEA', 'XAF');
INSERT INTO `country` VALUES (71, 'Chad', 'TD', 'TCD', '235', '250', 1, 0, 0, 'Africa', 'EMEA', 'XAF');
INSERT INTO `country` VALUES (72, 'Chile', 'CL', 'CHL', '56', '2', 1, 1, 1, 'S-America', 'AMERICAS', 'CLP');
INSERT INTO `country` VALUES (73, 'China', 'CN', 'CHN', '86', '0', 1, 1, 1, 'Asia', 'APAC', 'CNY');
INSERT INTO `country` VALUES (74, 'Colombia', 'CO', 'COL', '57', '0X', 1, 1, 1, 'S-America', 'AMERICAS', 'COP');
INSERT INTO `country` VALUES (75, 'Congo', 'CG', 'COG', '242', '21', 1, 1, 1, '', '', '');
INSERT INTO `country` VALUES (76, 'Cook Islands', 'CK', 'COK', '682', '0', 0, 1, 1, 'Oceania', 'APAC', 'NZD');
INSERT INTO `country` VALUES (77, 'Costa Rica', 'CR', 'CRI', '506', '', 1, 1, 1, 'C-America', 'AMERICAS', 'CRC');
INSERT INTO `country` VALUES (79, 'Djibouti', 'DJ', 'DJI', '253', '', 1, 1, 1, 'Africa', 'EMEA', 'DJF');
INSERT INTO `country` VALUES (81, 'Ecuador', 'EC', 'ECU', '593', '0', 1, 1, 1, 'S-America', 'AMERICAS', 'USD');
INSERT INTO `country` VALUES (82, 'Egypt', 'EG', 'EGY', '20', '0', 1, 1, 1, 'Africa', 'EMEA', 'EGP');
INSERT INTO `country` VALUES (83, 'El Salvador', 'SV', 'SLV', '503', '20', 1, 1, 1, 'C-America', 'AMERICAS', 'SVC');
INSERT INTO `country` VALUES (84, 'Equa Guinea', 'GQ', 'GNQ', '240', '7', 1, 0, 0, '', '', '');
INSERT INTO `country` VALUES (86, 'Ethiopia', 'ET', 'ETH', '251', '0', 1, 1, 1, 'Africa', 'EMEA', 'ETB');
INSERT INTO `country` VALUES (87, 'Faroe Islands', 'FO', 'FRO', '298', '', 1, 1, 1, 'Europe', 'EMEA', 'DKK');
INSERT INTO `country` VALUES (88, 'Fiji', 'FJ', 'FJI', '679', '32', 1, 1, 1, 'Oceania', 'APAC', 'FJD');
INSERT INTO `country` VALUES (89, 'French Guiana', 'GF', 'GUF', '594', '594', 1, 1, 1, 'S-America', 'AMERICAS', 'EUR');
INSERT INTO `country` VALUES (90, 'Gabon', 'GA', 'GAB', '241', '0', 1, 1, 1, 'Africa', 'EMEA', 'XAF');
INSERT INTO `country` VALUES (91, 'Gambia', 'GM', 'GMB', '220', '42', 1, 1, 1, 'Africa', 'EMEA', 'GMD');
INSERT INTO `country` VALUES (92, 'Georgia', 'GE', 'GEO', '995', '8', 1, 1, 1, 'Europe', 'EMEA', 'GEL');
INSERT INTO `country` VALUES (93, 'Ghana', 'GH', 'GHA', '233', '0', 1, 1, 1, 'Africa', 'EMEA', 'GHC');
INSERT INTO `country` VALUES (94, 'Gibraltar', 'GI', 'GIB', '350', '4', 1, 1, 1, 'Europe', 'EMEA', 'GIP');
INSERT INTO `country` VALUES (95, 'Greenland', 'GL', 'GRL', '299', '3', 1, 1, 1, 'Europe', 'EMEA', 'DKK');
INSERT INTO `country` VALUES (96, 'Grenada', 'GD', 'GRD', '1', '1+', 1, 1, 1, 'C-America', 'AMERICAS', 'XCD');
INSERT INTO `country` VALUES (97, 'Guadeloupe', 'GP', 'GLP', '590', '', 1, 1, 1, 'C-America', 'AMERICAS', 'EUR');
INSERT INTO `country` VALUES (98, 'Guam', 'GU', 'GUM', '1', '1+', 1, 1, 1, 'Oceania', 'APAC', 'USD');
INSERT INTO `country` VALUES (99, 'Guatemala', 'GT', 'GTM', '502', '2', 1, 1, 1, 'C-America', 'AMERICAS', 'GTQ');
INSERT INTO `country` VALUES (101, 'Guinea-Bissau', 'GW', 'GNB', '245', '', 1, 1, 1, 'Africa', 'EMEA', 'GWP');
INSERT INTO `country` VALUES (102, 'Guyana', 'GY', 'GUY', '594', '594', 1, 1, 1, 'S-America', 'AMERICAS', 'GYD');
INSERT INTO `country` VALUES (103, 'Haiti', 'HT', 'HTI', '509', '', 1, 1, 1, 'C-America', 'AMERICAS', 'HTG');
INSERT INTO `country` VALUES (104, 'Honduras', 'HN', 'HND', '504', '2', 1, 1, 1, 'C-America', 'AMERICAS', 'HNL');
INSERT INTO `country` VALUES (105, 'Hong Kong', 'HK', 'HKG', '852', '2', 1, 1, 1, 'Asia', 'APAC', 'HKD');
INSERT INTO `country` VALUES (106, 'Iceland', 'IS', 'ISL', '354', '4', 1, 1, 1, 'Europe', 'EMEA', 'ISK');
INSERT INTO `country` VALUES (107, 'India', 'IN', 'IND', '91', '0', 1, 1, 1, 'Asia', 'APAC', 'INR');
INSERT INTO `country` VALUES (108, 'Indonesia', 'ID', 'IDN', '62', '0', 1, 1, 1, 'Asia', 'APAC', 'IDR');
INSERT INTO `country` VALUES (110, 'Israel', 'IL', 'ISR', '972', '0', 1, 1, 1, 'Europe', 'EMEA', 'ILS');
INSERT INTO `country` VALUES (112, 'Jamaica', 'JM', 'JAM', '1', '1+', 1, 1, 1, 'C-America', 'AMERICAS', 'JMD');
INSERT INTO `country` VALUES (113, 'Japan', 'JP', 'JPN', '81', '0', 1, 1, 1, 'Asia', 'APAC', 'JPY');
INSERT INTO `country` VALUES (114, 'Jordan', 'JO', 'JOR', '962', '0', 1, 1, 1, 'Asia', 'EMEA', 'JOD');
INSERT INTO `country` VALUES (115, 'Kazakhstan', 'KZ', 'KAZ', '7', '8', 1, 1, 1, 'Europe', 'EMEA', 'KZT');
INSERT INTO `country` VALUES (116, 'Kenya', 'KE', 'KEN', '254', '0', 1, 1, 1, 'Africa', 'EMEA', 'KES');
INSERT INTO `country` VALUES (117, 'Korea (South)', 'KR', 'KOR', '82', '0', 1, 1, 1, 'Asia', 'APAC', 'KRW');
INSERT INTO `country` VALUES (118, 'Kuwait', 'KW', 'KWT', '965', '224', 1, 1, 1, 'Asia', 'EMEA', 'KWD');
INSERT INTO `country` VALUES (119, 'Kyrgyzstan', 'KG', 'KGZ', '996', '0', 1, 1, 1, 'Asia', 'APAC', 'KGS');
INSERT INTO `country` VALUES (120, 'Laos', 'LA', 'LAO', '856', '0', 1, 1, 1, 'Asia', 'APAC', 'LAK');
INSERT INTO `country` VALUES (122, 'Lesotho', 'LS', 'LSO', '266', '', 1, 1, 1, 'Africa', 'EMEA', 'LSL');
INSERT INTO `country` VALUES (124, 'Liechtenstein', 'LI', 'LIE', '423', '2', 1, 1, 1, 'Europe', 'EMEA', 'CHF');
INSERT INTO `country` VALUES (125, 'Macau', 'MO', 'MAC', '853', '', 1, 1, 1, 'Asia', 'APAC', 'MOP');
INSERT INTO `country` VALUES (127, 'Madagascar', 'MG', 'MDG', '261', '', 1, 0, 1, 'Africa', 'EMEA', 'MGA');
INSERT INTO `country` VALUES (128, 'Malawi', 'MW', 'MWI', '265', '', 1, 1, 1, 'Africa', 'EMEA', 'MWK');
INSERT INTO `country` VALUES (129, 'Malaysia', 'MY', 'MYS', '60', '0', 1, 1, 1, 'Asia', 'APAC', 'MYR');
INSERT INTO `country` VALUES (130, 'Maldives', 'MV', 'MDV', '960', '331', 0, 1, 1, 'Asia', 'APAC', 'MVR');
INSERT INTO `country` VALUES (131, 'Mali', 'ML', 'MLI', '223', '', 1, 1, 1, 'Africa', 'EMEA', 'XOF');
INSERT INTO `country` VALUES (132, 'Marshall Islands', 'MH', 'MHL', '692', '', 1, 0, 0, 'Oceania', 'APAC', 'USD');
INSERT INTO `country` VALUES (133, 'Martinique', 'MQ', 'MTQ', '596', '596', 1, 1, 1, 'C-America', 'AMERICAS', 'EUR');
INSERT INTO `country` VALUES (134, 'Mauritania', 'MR', 'MRT', '222', '', 1, 1, 1, 'Africa', 'EMEA', 'MRO');
INSERT INTO `country` VALUES (135, 'Mauritius', 'MU', 'MUS', '230', '201', 1, 1, 1, 'Africa', 'EMEA', 'MUR');
INSERT INTO `country` VALUES (136, 'Mexico', 'MX', 'MEX', '52', '1', 1, 1, 1, 'C-America', 'AMERICAS', 'MXN');
INSERT INTO `country` VALUES (137, 'Micronesia', 'FM', 'FSM', '691', '1+', 1, 1, 1, 'Oceania', 'APAC', 'USD');
INSERT INTO `country` VALUES (138, 'Moldova Rep of', 'MD', 'MDA', '373', '0', 1, 1, 1, 'Europe', 'EMEA', 'MDL');
INSERT INTO `country` VALUES (139, 'Monaco', 'MC', 'MCO', '377', '9', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (140, 'Mongolia', 'MN', 'MNG', '976', '', 1, 1, 1, 'Asia', 'APAC', 'MNT');
INSERT INTO `country` VALUES (141, 'Montserrat', 'MS', 'MSR', '1', '1+', 1, 0, 0, 'C-America', 'AMERICAS', 'XCD');
INSERT INTO `country` VALUES (142, 'Morocco', 'MA', 'MAR', '212', '0', 1, 1, 1, 'Africa', 'EMEA', 'MAD');
INSERT INTO `country` VALUES (143, 'Mozambique', 'MZ', 'MOZ', '258', '', 1, 1, 1, 'Africa', 'EMEA', 'MZN');
INSERT INTO `country` VALUES (144, 'Namibia', 'NA', 'NAM', '264', '0', 1, 1, 1, 'Africa', 'EMEA', 'NAD');
INSERT INTO `country` VALUES (145, 'Nepal', 'NP', 'NPL', '977', '0', 1, 1, 1, 'Asia', 'APAC', 'NPR');
INSERT INTO `country` VALUES (146, 'Nevis', 'KN', 'KNA', '1', '1+', 1, 1, 1, 'C-America', 'AMERICAS', 'XCD');
INSERT INTO `country` VALUES (147, 'New Caledonia', 'NC', 'NCL', '687', '23', 1, 0, 0, 'Oceania', 'APAC', 'XPF');
INSERT INTO `country` VALUES (148, 'New Zealand', 'NZ', 'NZL', '64', '0', 1, 1, 1, 'Oceania', 'APAC', 'NZD');
INSERT INTO `country` VALUES (149, 'Nicaragua', 'NI', 'NIC', '505', '2', 1, 1, 1, 'C-America', 'AMERICAS', 'NIO');
INSERT INTO `country` VALUES (150, 'Niger', 'NE', 'NER', '227', '', 1, 1, 1, 'Africa', 'EMEA', 'XOF');
INSERT INTO `country` VALUES (151, 'Nigeria', 'NG', 'NGA', '234', '0', 0, 1, 1, 'Africa', 'EMEA', 'NGN');
INSERT INTO `country` VALUES (152, 'Norway', 'NO', 'NOR', '47', '2', 1, 1, 1, 'Europe', 'EMEA', 'NOK');
INSERT INTO `country` VALUES (153, 'Oman', 'OM', 'OMN', '968', '23', 1, 1, 1, 'Asia', 'APAC', 'OMR');
INSERT INTO `country` VALUES (154, 'Pakistan', 'PK', 'PAK', '92', '0', 1, 1, 1, 'Asia', 'EMEA', 'PKR');
INSERT INTO `country` VALUES (155, 'Palau', 'PW', 'PLW', '680', '255', 1, 1, 1, '', 'AMERICAS', 'USD');
INSERT INTO `country` VALUES (157, 'Panama', 'PA', 'PAN', '507', '2', 1, 1, 1, 'C-America', 'AMERICAS', 'PAB');
INSERT INTO `country` VALUES (158, 'Papua New Guinea', 'PG', 'PNG', '675', '30', 1, 1, 1, 'Oceania', 'APAC', 'PGK');
INSERT INTO `country` VALUES (159, 'Paraguay', 'PY', 'PRY', '595', '0', 1, 1, 1, 'S-America', 'AMERICAS', 'PYG');
INSERT INTO `country` VALUES (160, 'Peru', 'PE', 'PER', '51', '0', 1, 1, 1, 'S-America', 'AMERICAS', 'PEN');
INSERT INTO `country` VALUES (161, 'Philippines', 'PH', 'PHL', '63', '0', 1, 1, 1, 'Asia', 'APAC', 'PHP');
INSERT INTO `country` VALUES (162, 'Qatar', 'QA', 'QAT', '974', '', 1, 1, 1, 'Asia', 'APAC', 'QAR');
INSERT INTO `country` VALUES (163, 'Reunion Island', 'RE', 'REU', '262', '262', 1, 1, 0, 'Africa', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (164, 'Russian Federation', 'RU', 'RUS', '7', '8', 1, 1, 1, 'Europe', 'EMEA', 'RUB');
INSERT INTO `country` VALUES (165, 'Rwanda', 'RW', 'RWA', '250', '', 1, 0, 1, 'Africa', 'EMEA', 'RWF');
INSERT INTO `country` VALUES (166, 'Saipan', 'MP', 'MNP', '1', '1+', 0, 0, 0, '', 'AMERICAS', 'USD');
INSERT INTO `country` VALUES (167, 'Samoa', 'WS', 'WSM', '685', '', 1, 0, 0, 'Oceania', 'APAC', 'WST');
INSERT INTO `country` VALUES (168, 'Saudi Arabia', 'SA', 'SAU', '966', '0', 1, 1, 1, 'Asia', 'EMEA', 'SAR');
INSERT INTO `country` VALUES (169, 'Senegal', 'SN', 'SEN', '221', '8', 1, 1, 1, 'Africa', 'EMEA', 'XOF');
INSERT INTO `country` VALUES (170, 'Serbia', 'RS', 'SRB', '381', '0', 0, 1, 1, 'Europe', 'EMEA', 'RSD');
INSERT INTO `country` VALUES (171, 'Seychelles', 'SC', 'SYC', '248', '222', 1, 1, 1, 'Africa', 'EMEA', 'USD');
INSERT INTO `country` VALUES (172, 'Singapore', 'SG', 'SGP', '65', '635', 1, 1, 1, 'Asia', 'APAC', 'SGD');
INSERT INTO `country` VALUES (174, 'South Africa', 'ZA', 'ZAF', '27', '0', 1, 1, 1, 'Africa', 'EMEA', 'ZAR');
INSERT INTO `country` VALUES (175, 'Sri Lanka', 'LK', 'LKA', '94', '0', 1, 1, 1, 'Asia', 'APAC', 'LKR');
INSERT INTO `country` VALUES (177, 'St Lucia', 'LC', 'LCA', '1', '1+', 1, 1, 1, 'C-America', 'AMERICAS', 'XCD');
INSERT INTO `country` VALUES (179, 'St Vincent', 'VC', 'VCT', '1', '1+', 1, 1, 1, 'C-America', 'AMERICAS', 'XCD');
INSERT INTO `country` VALUES (181, 'Suriname', 'SR', 'SUR', '597', '0', 1, 1, 1, 'S-America', 'AMERICAS', 'SRD');
INSERT INTO `country` VALUES (182, 'Switzerland', 'CH', 'CHE', '41', '0', 1, 1, 1, 'Europe', 'EMEA', 'CHF');
INSERT INTO `country` VALUES (184, 'Taiwan', 'TW', 'TWN', '886', '2', 1, 1, 1, 'Asia', 'APAC', 'TWD');
INSERT INTO `country` VALUES (185, 'Tanzania', 'TZ', 'TZA', '255', '0', 1, 1, 1, 'Africa', 'EMEA', 'TZS');
INSERT INTO `country` VALUES (186, 'Thailand', 'TH', 'THA', '66', '0', 1, 1, 1, 'Asia', 'APAC', 'THB');
INSERT INTO `country` VALUES (187, 'Togo', 'TG', 'TGO', '228', '2', 1, 0, 0, 'Africa', 'EMEA', 'XOF');
INSERT INTO `country` VALUES (188, 'Tonga', 'TO', 'TON', '676', '20', 1, 1, 1, 'Oceania', 'APAC', 'TOP');
INSERT INTO `country` VALUES (189, 'Trinidad & tobago', 'TT', 'TTO', '1', '1+', 1, 1, 1, 'C-America', 'AMERICAS', 'TTD');
INSERT INTO `country` VALUES (191, 'Turkey', 'TR', 'TUR', '90', '0', 1, 1, 1, 'Europe', 'EMEA', 'TRY');
INSERT INTO `country` VALUES (192, 'Turkmenistan', 'TM', 'TKM', '993', '8', 1, 1, 1, 'Asia', 'APAC', 'TMM');
INSERT INTO `country` VALUES (193, 'Turks & caicos', 'TC', 'TCA', '1', '1', 1, 1, 1, 'Oceania', 'APAC', 'USD');
INSERT INTO `country` VALUES (194, 'United Arab Emirates', 'AE', 'ARE', '971', '0', 1, 1, 1, 'Asia', 'APAC', 'AED');
INSERT INTO `country` VALUES (195, 'Uganda', 'UG', 'UGA', '256', '0', 1, 1, 1, 'Africa', 'EMEA', 'UGX');
INSERT INTO `country` VALUES (196, 'Ukraine', 'UA', 'UKR', '380', '8~0', 1, 1, 1, 'Europe', 'EMEA', 'UAH');
INSERT INTO `country` VALUES (197, 'Uruguay', 'UY', 'URY', '598', '0', 1, 1, 1, 'S-America', 'AMERICAS', 'UYU');
INSERT INTO `country` VALUES (198, 'Uzbekistan', 'UZ', 'UZB', '998', '8', 1, 1, 1, 'Asia', 'APAC', 'UZS');
INSERT INTO `country` VALUES (199, 'Vanuatu', 'VU', 'VUT', '678', '', 0, 1, 1, 'Oceania', 'APAC', 'VUV');
INSERT INTO `country` VALUES (200, 'Vatican city', 'VA', 'VAT', '379', '', 1, 0, 0, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (201, 'Venezuela', 'VE', 'VEN', '58', '0', 1, 1, 1, 'S-America', 'AMERICAS', 'VEB');
INSERT INTO `country` VALUES (202, 'Vietnam', 'VN', 'VNM', '84', '0', 1, 1, 1, 'Asia', 'APAC', 'VND');
INSERT INTO `country` VALUES (203, 'Virgin islands', 'VG', 'VGB', '1', '1+', 1, 1, 1, 'C-America', 'AMERICAS', 'USD');
INSERT INTO `country` VALUES (204, 'Wallis and futuna', 'WF', 'WLF', '681', '50', 0, 0, 0, '', '', '');
INSERT INTO `country` VALUES (205, 'Waziland', 'SZ', 'SWZ', '268', '207', 0, 0, 0, 'Africa', 'EMEA', 'SZL');
INSERT INTO `country` VALUES (207, 'Zambia', 'ZM', 'ZMB', '260', '0', 1, 1, 1, 'Africa', 'EMEA', 'ZMK');
INSERT INTO `country` VALUES (211, 'Netherlands Antilles', 'AN', 'ANT', '599', '0', 1, 1, 1, 'C-America', 'AMERICAS', 'ANG');
INSERT INTO `country` VALUES (213, 'Aland Islands', 'AX', 'ALA', '358', '0', 1, 0, 0, '', '', '');
INSERT INTO `country` VALUES (233, 'Tajikistan', 'TJ', 'TJK', '992', '8', 1, 0, 0, 'Asia', 'APAC', 'TJS');
INSERT INTO `country` VALUES (216, 'Guernsey ', 'CE', 'GGY', '44', '0', 0, 0, 0, '', '', '');
INSERT INTO `country` VALUES (217, 'Channel Islands (Jersey) ', 'JE', 'JEY', '44', '0', 0, 0, 0, '', '', '');
INSERT INTO `country` VALUES (221, 'Dominica', 'DM', 'DMA', '1', '1+', 1, 1, 1, 'C-America', 'AMERICAS', 'XCD');
INSERT INTO `country` VALUES (222, 'Dominican Republic ', 'DO', 'DOM', '1', '1+', 1, 1, 1, 'C-America', 'AMERICAS', 'DOP');
INSERT INTO `country` VALUES (224, 'Kiribati', 'KI', 'KIR', '686', '27', 0, 0, 0, 'Oceania', 'APAC', 'AUD');
INSERT INTO `country` VALUES (226, 'Montenegro', 'ME', 'MNE', '382', '0', 1, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (227, 'Norfolk Island ', 'NF', 'NFK', '672', '0', 0, 0, 0, '', '', '');
INSERT INTO `country` VALUES (228, 'French Polynesia', 'PF', 'PYF', '689', '', 1, 1, 1, 'Oceania', 'APAC', 'EUR');
INSERT INTO `country` VALUES (271, 'Salomon Islands', 'xSB', 'SLB', '677', '20', 0, 0, 0, 'Oceania', 'APAC', 'SBD');
INSERT INTO `country` VALUES (230, 'US Virgin Islands', 'VI', 'VIR', '1', '1+', 1, 1, 1, 'C-America', 'AMERICAS', 'USD');
INSERT INTO `country` VALUES (232, 'United States', 'US', 'USA', '1', '1+', 1, 1, 1, 'N-America', 'AMERICAS', 'USD');
INSERT INTO `country` VALUES (235, 'Afghanistan', 'AF', 'AFG', '', '', 0, 1, 1, 'Asia', 'EMEA', 'AFN');
INSERT INTO `country` VALUES (236, 'Belarus', 'BY', 'BLR', '', '', 0, 1, 1, 'Europe', 'EMEA', 'BYR');
INSERT INTO `country` VALUES (237, 'Comoros', 'KM', 'COM', '269', '00', 0, 1, 1, '', 'EMEA', 'KMF');
INSERT INTO `country` VALUES (238, 'Congo', 'CD', 'ZAR', '', '', 0, 0, 0, 'Africa', 'EMEA', 'CDF');
INSERT INTO `country` VALUES (239, 'Cote dvoire', 'CI', 'CIV', '', '', 0, 0, 0, 'Africa', 'EMEA', 'XOF');
INSERT INTO `country` VALUES (240, 'Croatia', 'HR', 'HRV', '', '', 0, 1, 1, 'Europe', 'EMEA', 'HRK');
INSERT INTO `country` VALUES (241, 'Cuba', 'CU', 'CUB', '', '', 0, 0, 0, 'C-America', 'AMERICAS', 'CUP');
INSERT INTO `country` VALUES (242, 'East Timor', 'TL', 'TLS', '670', '', 0, 0, 0, 'Asia', 'APAC', 'USD');
INSERT INTO `country` VALUES (243, 'Eritrea', 'ER', 'ERI', '291', '', 0, 1, 1, 'Africa', 'EMEA', 'ERN');
INSERT INTO `country` VALUES (244, 'Falkland Islands', 'FK', 'FLK', '500', '', 0, 0, 0, 'S-America', 'AMERICAS', 'FKP');
INSERT INTO `country` VALUES (245, 'Guinea', 'GN', 'GIN', '224', '', 0, 1, 1, 'Africa', 'EMEA', 'GNF');
INSERT INTO `country` VALUES (246, 'Iran', 'IR', 'IRN', '', '', 0, 0, 1, 'Asia', 'EMEA', 'IRR');
INSERT INTO `country` VALUES (247, 'Iraq', 'IQ', 'IRQ', '', '', 0, 0, 1, 'Asia', 'EMEA', 'IQD');
INSERT INTO `country` VALUES (248, 'Korea, North', 'KP', 'PRK', '', '', 0, 0, 0, 'Asia', 'APAC', 'KPW');
INSERT INTO `country` VALUES (249, 'Lebanon', 'LB', 'LBN', '', '', 0, 1, 1, 'Asia', 'EMEA', 'LBP');
INSERT INTO `country` VALUES (250, 'Liberia', 'LR', 'LBR', '231', '', 0, 0, 1, 'Africa', 'EMEA', 'LRD');
INSERT INTO `country` VALUES (251, 'Libya', 'LY', 'LBY', '', '', 0, 1, 1, 'Africa', 'EMEA', 'LYD');
INSERT INTO `country` VALUES (252, 'Macedonia', 'MK', 'MKD', '', '', 0, 1, 1, 'Europe', 'EMEA', 'MKD');
INSERT INTO `country` VALUES (253, 'Myanmar', 'MM', 'MMR', '', '', 0, 1, 1, 'Asia', 'APAC', 'MMK');
INSERT INTO `country` VALUES (254, 'Nauru', 'NR', 'NRU', '674', '', 0, 0, 0, 'Oceania', 'APAC', 'AUD');
INSERT INTO `country` VALUES (255, 'Palestinian Territory', 'PS', 'PSE', '', '', 0, 0, 0, '', 'EMEA', 'USD');
INSERT INTO `country` VALUES (256, 'Puerto Rico', 'PR', 'PRI', '1', '', 0, 0, 0, 'C-America', 'AMERICAS', 'USD');
INSERT INTO `country` VALUES (257, 'Saint Helena', 'SH', 'SHN', '290', '', 0, 0, 0, 'Africa', 'EMEA', 'SHP');
INSERT INTO `country` VALUES (258, 'Saint Pierre and Miquelon', 'PM', 'SPM', '508', '', 0, 0, 0, 'C-America', 'AMERICAS', 'EUR');
INSERT INTO `country` VALUES (259, 'San Marino', 'SM', 'SMR', '378', '', 0, 0, 0, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (260, 'Sao Tome and Principe', 'ST', 'STP', '239', '', 0, 0, 0, 'Africa', 'EMEA', 'STD');
INSERT INTO `country` VALUES (261, 'Slovenia', 'SI', 'SVN', '386', '', 0, 1, 1, 'Europe', 'EMEA', 'EUR');
INSERT INTO `country` VALUES (262, 'Somalia', 'SO', 'SOM', '', '', 0, 0, 0, 'Africa', 'EMEA', 'SOS');
INSERT INTO `country` VALUES (263, 'Sudan', 'SD', 'SDN', '249', '', 0, 0, 1, 'Africa', 'EMEA', 'SDG');
INSERT INTO `country` VALUES (264, 'Syrian Arab Republic', 'SY', 'SYR', '', '', 0, 0, 1, 'Asia', 'EMEA', 'SYP');
INSERT INTO `country` VALUES (265, 'Tunisia', 'TN', 'TUN', '216', '', 0, 1, 1, 'Africa', 'EMEA', 'TND');
INSERT INTO `country` VALUES (266, 'Yemen', 'YE', 'YEM', '', '', 0, 0, 1, 'Asia', 'APAC', 'YER');
INSERT INTO `country` VALUES (267, 'Zimbabwe', 'ZW', 'ZWE', '', '', 0, 1, 1, 'Africa', 'EMEA', 'ZAR');
INSERT INTO `country` VALUES (270, 'Kosovo', 'XK', 'RKS', '383', '', 1, 0, 1, 'Europe', 'EMEA', '');
INSERT INTO `country` VALUES (269, 'Solomon Islands', 'SB', '', '', '', 0, 1, 1, 'Oceania', 'APAC', '');

