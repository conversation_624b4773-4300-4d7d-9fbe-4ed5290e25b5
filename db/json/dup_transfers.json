[{"Transfer ID": 23442, "Amount": "51.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_467e16bea5e7cf6996f1f604828ff8c7", "Employer": "Heritage Farms, LLC", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>", "All Payouts": {"payout_fd5f96c474d54e882d718561ef2c5eb5": ["Completed", 49.57, "Completed", 49.57], "payout_467e16bea5e7cf6996f1f604828ff8c7": ["Completed", 49.57, "Completed", 49.57]}}, {"Transfer ID": 23441, "Amount": "40.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_201062240379bf34e4f279e19c2453c5", "Employer": "Heritage Farms, LLC", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_384fc5c084c0b7c8407cab8a34866508": ["Completed", 38.88, "Completed", 38.88], "payout_201062240379bf34e4f279e19c2453c5": ["Completed", 38.88, "Completed", 38.88]}}, {"Transfer ID": 23440, "Amount": "151.61", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_08a1f91e41163574df18a4a1d5d8174b", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ANGEL SIMON RODRIGUEZ MELGAREJO", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_501df3e5557a44778b95d004d04120ca": ["Completed", 147.36, "Completed", 147.36], "payout_08a1f91e41163574df18a4a1d5d8174b": ["Completed", 147.36, "Completed", 147.36]}}, {"Transfer ID": 23439, "Amount": "150.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_cc56184b34e375afd6321e3561be529e", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON> ", "All Payouts": {"payout_0372350ccaf75a2ce66f81c701bcc9eb": ["Completed", 145.8, "Completed", 145.8], "payout_cc56184b34e375afd6321e3561be529e": ["Completed", 145.8, "Completed", 145.8]}}, {"Transfer ID": 23438, "Amount": "69.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_cc5b4b545f4e3d12949cc34f4306fc9f", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "RUTILO MARTINEZ SOTO", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON><PERSON>", "All Payouts": {"payout_90c49521bf52a9cad3d4cb848761a1eb": ["Completed", 67.07, "Completed", 67.07], "payout_cc5b4b545f4e3d12949cc34f4306fc9f": ["Completed", 67.07, "Completed", 67.07]}}, {"Transfer ID": 23437, "Amount": "50.54", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_8be2ac682098cdf9c7d34200d5133dae", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "MISAEL PADRON RODRIGUEZ", "Sender User Email": "misael<PERSON><EMAIL>", "Recipient Name": "<PERSON><PERSON>", "All Payouts": {"payout_3e43416a3a915bc7daa3447b4f2bc994": ["Completed", 49.12, "Completed", 49.12], "payout_8be2ac682098cdf9c7d34200d5133dae": ["Completed", 49.12, "Completed", 49.12]}}, {"Transfer ID": 23436, "Amount": "320.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_1f1644390475f8d94eb1668afc23c66e", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "BERTIN CASTRO CARRIZAL", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> ", "All Payouts": {"payout_7c0bed33b8807dfde35052b2b8153918": ["Completed", 311.04, "Completed", 311.04], "payout_1f1644390475f8d94eb1668afc23c66e": ["Completed", 311.04, "Completed", 311.04]}}, {"Transfer ID": 23435, "Amount": "500.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_933be53f351c894fe7ca72281f768fff", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "PABLO NAVA NAVA", "Sender User Email": "<EMAIL>", "Recipient Name": "pablo nava", "All Payouts": {"payout_6e505bfdc915a4e854a35555423cc376": ["Completed", 486, "Completed", 486], "payout_933be53f351c894fe7ca72281f768fff": ["Completed", 486, "Completed", 486]}}, {"Transfer ID": 23434, "Amount": "350.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_a336b9af4adeff3a36ae1ac257c708b0", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "armando lopez", "All Payouts": {"payout_7d46b49859de9ea63c678ed0274bf08f": ["Completed", 340.2, "Completed", 340.2], "payout_a336b9af4adeff3a36ae1ac257c708b0": ["Completed", 340.2, "Completed", 340.2]}}, {"Transfer ID": 23433, "Amount": "30.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_34e13c06a1ed4ae933503a594aee45af", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>", "All Payouts": {"payout_602e61bf63d3540b064c28003503e103": ["Completed", 29.16, "Completed", 29.16], "payout_34e13c06a1ed4ae933503a594aee45af": ["Completed", 29.16, "Completed", 29.16]}}, {"Transfer ID": 23432, "Amount": "40.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_129c4f60f149fb5fb756048bebbd9e5f", "Employer": "Jordahl Farms", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "jose<PERSON><PERSON><PERSON><EMAIL>", "Recipient Name": "Cinthya V R Rmz", "All Payouts": {"payout_f0629085c7bd237b035d4d384393ca5b": ["Completed", 38.88, "Completed", 38.88], "payout_129c4f60f149fb5fb756048bebbd9e5f": ["Completed", 38.88, "Completed", 38.88]}}, {"Transfer ID": 23431, "Amount": "41.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_5804169f62fa60075fa4e1d9d19fc30a", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JUAN HEDIBERTO PIOQUINTO PEDRO", "Sender User Email": "juan<PERSON><PERSON><EMAIL>", "Recipient Name": "sara <PERSON>", "All Payouts": {"payout_fbaf257691c8d7ed308ec1b8ba543fac": ["Completed", 39.85, "Completed", 39.85], "payout_5804169f62fa60075fa4e1d9d19fc30a": ["Completed", 39.85, "Completed", 39.85]}}, {"Transfer ID": 23430, "Amount": "309.00", "Status": "Completed", "Type": "bank", "Method": "mx_hsbc_bank", "Rapyd Payout ID": "payout_3bfa4ceb67b71e6f3e1faf2acb229208", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "jose luis martinez sarabia", "All Payouts": {"payout_baf26b60bdba430222c097ac689c0f5d": ["Completed", 300.35, "Completed", 300.35], "payout_3bfa4ceb67b71e6f3e1faf2acb229208": ["Completed", 300.35, "Completed", 300.35]}}, {"Transfer ID": 23429, "Amount": "40.00", "Status": "Completed", "Type": "bank", "Method": "mx_stp_bank", "Rapyd Payout ID": "payout_0e6fb84a6ab6dc4b9b34da472761c215", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON> ", "All Payouts": {"payout_e60c46239d4a2364572739f07a525a17": ["Completed", 38.88, "Completed", 38.88], "payout_0e6fb84a6ab6dc4b9b34da472761c215": ["Completed", 38.88, "Completed", 38.88]}}, {"Transfer ID": 23428, "Amount": "50.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_bbc03e961663b3c5b6f14c0e97cdd391", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "rosa  san <PERSON><PERSON><PERSON> ", "All Payouts": {"payout_4e3246b59467b518320cbbcbc8cbaff6": ["Completed", 48.6, "Completed", 48.6], "payout_bbc03e961663b3c5b6f14c0e97cdd391": ["Completed", 48.6, "Completed", 48.6]}}, {"Transfer ID": 23427, "Amount": "90.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_53f8359b7a6e152b9c9728786bf0aa4b", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_a2c0be422297ab5e2d43be1879799224": ["Completed", 87.48, "Completed", 87.48], "payout_53f8359b7a6e152b9c9728786bf0aa4b": ["Completed", 87.48, "Completed", 87.48]}}, {"Transfer ID": 23426, "Amount": "60.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_d54c514093a99eb2eb2d71b79f7d9132", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON> <PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> jesus <PERSON> canales", "All Payouts": {"payout_c86b701d8754218ff10149ba1a72e458": ["Completed", 58.32, "Completed", 58.32], "payout_d54c514093a99eb2eb2d71b79f7d9132": ["Completed", 58.32, "Completed", 58.32]}}, {"Transfer ID": 23425, "Amount": "52.00", "Status": "Completed", "Type": "bank", "Method": "mx_banorte_ixi_bank", "Rapyd Payout ID": "payout_c07c2a8f284618aa58e90c4f9a38f869", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_aa8f065a9c64042fe42c18feae907847": ["Completed", 50.54, "Completed", 50.54], "payout_c07c2a8f284618aa58e90c4f9a38f869": ["Completed", 50.54, "Completed", 50.54]}}, {"Transfer ID": 23424, "Amount": "517.81", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_d0171d9e2d79064bc7ba23f21940f97c", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON> ", "All Payouts": {"payout_393250ef7a6b00c313147cc417369862": ["Completed", 503.31, "Completed", 503.31], "payout_d0171d9e2d79064bc7ba23f21940f97c": ["Completed", 503.31, "Completed", 503.31]}}, {"Transfer ID": 23423, "Amount": "26.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_870240683c1b1eff294affdc049d90f8", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "jose gerardo cruz ramirez", "All Payouts": {"payout_422e6e14ae420009284258b32d866ff8": ["Completed", 25.27, "Completed", 25.27], "payout_870240683c1b1eff294affdc049d90f8": ["Completed", 25.27, "Completed", 25.27]}}, {"Transfer ID": 23422, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_6bc784e3a74b85699927600b8510dcaa", "Employer": " Trevino Ag Services", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "lizeldy guadalupe flores Martinez ", "All Payouts": {"payout_dbce61b9b289248c56c6069f4d99f5dd": ["Completed", 24.3, "Completed", 24.3], "payout_6bc784e3a74b85699927600b8510dcaa": ["Completed", 24.3, "Completed", 24.3]}}, {"Transfer ID": 23421, "Amount": "26.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_133e56ecca3f461dc33d64f331b9f75b", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "eli god<PERSON><PERSON>", "All Payouts": {"payout_98e05c629f937f3fc347712b5b268448": ["Completed", 25.27, "Completed", 25.27], "payout_133e56ecca3f461dc33d64f331b9f75b": ["Completed", 25.27, "Completed", 25.27]}}, {"Transfer ID": 23420, "Amount": "30.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_6a2e9944b892b9671fbe76c765cfe7eb", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "EDUARDO CUATRA SANCHEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_76e7b70c3c5a896a1676577d2f3a6511": ["Completed", 29.16, "Completed", 29.16], "payout_6a2e9944b892b9671fbe76c765cfe7eb": ["Completed", 29.16, "Completed", 29.16]}}, {"Transfer ID": 23419, "Amount": "200.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_eacd884c2b798810fcbdf5be19eb2d59", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "esau felix  lujan ", "All Payouts": {"payout_4f636cc5f97fe504b3ebc3531a030de5": ["Completed", 194.4, "Completed", 194.4], "payout_eacd884c2b798810fcbdf5be19eb2d59": ["Completed", 194.4, "Completed", 194.4]}}, {"Transfer ID": 23418, "Amount": "300.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_a0d789d8e32df94e000917623066c481", "Employer": " Trevino Ag Services", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "LIZELDY GUADALUPE FLORES MARTINEZ", "All Payouts": {"payout_c5eced3974d5eb7f0d38da395132d9ce": ["Completed", 291.6, "Completed", 291.6], "payout_a0d789d8e32df94e000917623066c481": ["Completed", 291.6, "Completed", 291.6]}}, {"Transfer ID": 23417, "Amount": "300.00", "Status": "Completed", "Type": "bank", "Method": "mx_banamex_bank", "Rapyd Payout ID": "payout_9a89a634cab80e9afb6188cacea70bb7", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON> ", "All Payouts": {"payout_8bc561a43a6141ca82ed7d10599b5fa6": ["Completed", 291.6, "Completed", 291.6], "payout_9a89a634cab80e9afb6188cacea70bb7": ["Completed", 291.6, "Completed", 291.6]}}, {"Transfer ID": 23416, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_f4683411d5485fcae563f96bc5a5db5c", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ARMANDO MUNOZ REYES", "Sender User Email": "<EMAIL>", "Recipient Name": "johana itzel gomes cortez", "All Payouts": {"payout_ca9a53766e2d360b6f000e640cf92e41": ["Completed", 24.3, "Completed", 24.3], "payout_f4683411d5485fcae563f96bc5a5db5c": ["Completed", 24.3, "Completed", 24.3]}}, {"Transfer ID": 23415, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_a61d048d07f1f9a04843e8f680a11559", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "EFRAIN RAMIREZ MANCINAS", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_cbfd219044b6a6c6a33451c38858f1c2": ["Completed", 24.3, "Completed", 24.3], "payout_a61d048d07f1f9a04843e8f680a11559": ["Completed", 24.3, "Completed", 24.3]}}, {"Transfer ID": 23414, "Amount": "150.00", "Status": "Completed", "Type": "bank", "Method": "mx_banorte_ixi_bank", "Rapyd Payout ID": "payout_1bf785222e139b521d1cd0c66cb7863c", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_4ffe0de73d6efb22d66d017a68ffe8ab": ["Completed", 145.8, "Completed", 145.8], "payout_1bf785222e139b521d1cd0c66cb7863c": ["Completed", 145.8, "Completed", 145.8]}}, {"Transfer ID": 23413, "Amount": "400.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_97e77aa864b098223c062d3bd29b4993", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "adilene soto", "All Payouts": {"payout_2967532040954780b09de5255433e30a": ["Completed", 388.8, "Completed", 388.8], "payout_97e77aa864b098223c062d3bd29b4993": ["Completed", 388.8, "Completed", 388.8]}}, {"Transfer ID": 23412, "Amount": "234.41", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_0822961dcc6c7daafdcbb440aca8a4ef", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "JESUS AARON ATONDO PEREZ", "Sender User Email": "<EMAIL>", "Recipient Name": "jesus atondo perez ", "All Payouts": {"payout_3cef0ef28202b854097d75bba03e523c": ["Completed", 227.85, "Completed", 227.85], "payout_0822961dcc6c7daafdcbb440aca8a4ef": ["Completed", 227.85, "Completed", 227.85]}}, {"Transfer ID": 23411, "Amount": "39.70", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_ba0bb2d92a44c21bee4aa565b56133a1", "Employer": "Tanimura and Antle Fresh Foods", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "paola morales duarte", "All Payouts": {"payout_fa29ddde96c23dfb5b85e71d696ea800": ["Completed", 38.59, "Completed", 38.59], "payout_ba0bb2d92a44c21bee4aa565b56133a1": ["Completed", 38.59, "Completed", 38.59]}}, {"Transfer ID": 23410, "Amount": "500.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_b9c9054f92ee7e1225768bfaafb390c1", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>", "All Payouts": {"payout_c5af72c95afbd7349e12ff11799a14db": ["Completed", 486, "Completed", 486], "payout_b9c9054f92ee7e1225768bfaafb390c1": ["Completed", 486, "Completed", 486]}}, {"Transfer ID": 23409, "Amount": "300.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_12ae4661626243cfa2a2889b75adb3a3", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JACINTO MENDOZA MENDOZA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON><PERSON> ", "All Payouts": {"payout_31ec3f00b646a82eb79840a82222df49": ["Completed", 291.6, "Completed", 291.6], "payout_12ae4661626243cfa2a2889b75adb3a3": ["Completed", 291.6, "Completed", 291.6]}}, {"Transfer ID": 23408, "Amount": "26.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_3f4b985f10e2383d46204ef58bc84100", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "Dora Luz  Razo de Dios", "All Payouts": {"payout_c0be99e48b0b5ed204f043fc133227c3": ["Completed", 25.27, "Completed", 25.27], "payout_3f4b985f10e2383d46204ef58bc84100": ["Completed", 25.27, "Completed", 25.27]}}, {"Transfer ID": 23407, "Amount": "810.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_a6d46045acf6c982dc62bfd86d2f629d", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "LUIS GERARDO MARTINEZ PRIETO", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_22bef64d86aa2e26bac11ce28375649e": ["Completed", 787.32, "Completed", 787.32], "payout_a6d46045acf6c982dc62bfd86d2f629d": ["Completed", 787.32, "Completed", 787.32]}}, {"Transfer ID": 23406, "Amount": "825.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_f5a98c11ac88fffea0f1e8cf6aadf1ff", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "FRANCISCO DEL ANGEL TENORIO", "Sender User Email": "<EMAIL>", "Recipient Name": "rufina  de <PERSON> ", "All Payouts": {"payout_50fc522a7bffffdaca9509c3669d0668": ["Completed", 801.9, "Completed", 801.9], "payout_f5a98c11ac88fffea0f1e8cf6aadf1ff": ["Completed", 801.9, "Completed", 801.9]}}, {"Transfer ID": 23405, "Amount": "1,500.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_460d9d12fa98e5360a157df06de0696a", "Employer": "Jordahl Farms", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_3224b75aa172df29874fa8164f5f2f0a": ["Completed", 1458, "Completed", 1458], "payout_460d9d12fa98e5360a157df06de0696a": ["Completed", 1458, "Completed", 1458]}}, {"Transfer ID": 23404, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_6b4874c5a2d066062d228688c90d20d1", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "PEDRO ZUNIGA ZUNIGA", "Sender User Email": "<EMAIL>", "Recipient Name": "HILDA  Salazar ", "All Payouts": {"payout_b1e1a869a2c99ddef651d0b048fe71af": ["Completed", 24.3, "Completed", 24.3], "payout_6b4874c5a2d066062d228688c90d20d1": ["Completed", 24.3, "Completed", 24.3]}}, {"Transfer ID": 23403, "Amount": "556.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_e115e9d6187fd026c91933893867e7e9", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "OMAR JIMENEZ PEREZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_e188577f67432aab5c2f38b0924b14f1": ["Completed", 540.43, "Completed", 540.43], "payout_e115e9d6187fd026c91933893867e7e9": ["Completed", 540.43, "Completed", 540.43]}}, {"Transfer ID": 23402, "Amount": "487.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_9a994a73c1491c0f5fdc5083458eee39", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> ", "All Payouts": {"payout_43f38b170b903d1074bd49ce7bba5448": ["Completed", 473.36, "Completed", 473.36], "payout_9a994a73c1491c0f5fdc5083458eee39": ["Completed", 473.36, "Completed", 473.36]}}, {"Transfer ID": 23401, "Amount": "123.00", "Status": "Completed", "Type": "bank", "Method": "mx_banamex_bank", "Rapyd Payout ID": "payout_cde16ece9269c63c787bc127259406c3", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_22ce3db0560b9f8111bd7843da1191d6": ["Completed", 119.56, "Completed", 119.56], "payout_cde16ece9269c63c787bc127259406c3": ["Completed", 119.56, "Completed", 119.56]}}, {"Transfer ID": 23400, "Amount": "445.00", "Status": "Completed", "Type": "bank", "Method": "mx_banorte_ixi_bank", "Rapyd Payout ID": "payout_cb74f6d616d1f947b4b2aa34130c9599", "Employer": "Heritage Farms, LLC", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_7f45fd532ba262585d173e844929445d": ["Completed", 432.54, "Completed", 432.54], "payout_cb74f6d616d1f947b4b2aa34130c9599": ["Completed", 432.54, "Completed", 432.54]}}, {"Transfer ID": 23399, "Amount": "515.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_7cc648267a3560b05e3187b988d2ea36", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "APOLINAR HERNANDEZ HERNANDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "guadalupe diaz", "All Payouts": {"payout_1c46f61b637db23b4cf6d1619f81ea5b": ["Completed", 500.58, "Completed", 500.58], "payout_7cc648267a3560b05e3187b988d2ea36": ["Completed", 500.58, "Completed", 500.58]}}, {"Transfer ID": 23398, "Amount": "203.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_ec92910d10dfc48031e64d883bb11b65", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> ", "All Payouts": {"payout_345dd3912bfa569e98e18e699799d1ce": ["Completed", 197.32, "Completed", 197.32], "payout_ec92910d10dfc48031e64d883bb11b65": ["Completed", 197.32, "Completed", 197.32]}}, {"Transfer ID": 23397, "Amount": "76.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_2b76fd324ae78bd9f566f6946583847d", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> azu<PERSON>na Ortega <PERSON>lva", "All Payouts": {"payout_7107f36befa44b8614e49af4422d5fcd": ["Completed", 73.87, "Completed", 73.87], "payout_2b76fd324ae78bd9f566f6946583847d": ["Completed", 73.87, "Completed", 73.87]}}, {"Transfer ID": 23396, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_feaaa4000dbf1bb8d84aba33558eb278", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "DANIEL VAZQUEZ GARCIA", "Sender User Email": "<EMAIL>", "Recipient Name": "brise<PERSON>  vazquez ", "All Payouts": {"payout_6ffa341a441b1bb6d7c16c6ad19972ba": ["Completed", 24.3, "Completed", 24.3], "payout_feaaa4000dbf1bb8d84aba33558eb278": ["Completed", 24.3, "Completed", 24.3]}}, {"Transfer ID": 23395, "Amount": "70.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_eafb252ca1e4ac9c8b2fd6b1352c4764", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "GUSTAVO ADOLFO ESQUIVEL PASTRANA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>", "All Payouts": {"payout_b9bbf2d353d64d41a66ae13e88b459d6": ["Completed", 68.04, "Completed", 68.04], "payout_eafb252ca1e4ac9c8b2fd6b1352c4764": ["Completed", 68.04, "Completed", 68.04]}}, {"Transfer ID": 23394, "Amount": "80.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_7a19e8aa6b645fb8957036b818dafcb8", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>", "All Payouts": {"payout_314e5dd4136ed5adc359538159ab05a2": ["Completed", 77.76, "Completed", 77.76], "payout_7a19e8aa6b645fb8957036b818dafcb8": ["Completed", 77.76, "Completed", 77.76]}}, {"Transfer ID": 23393, "Amount": "613.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_501444a8dabafb2788a2bd616d25b2b8", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "VALENTIN MARTINEZ HERNANDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "valentin  martinez", "All Payouts": {"payout_561fd679ac1155f439a510dfd7ce6ef6": ["Completed", 595.84, "Completed", 595.84], "payout_501444a8dabafb2788a2bd616d25b2b8": ["Completed", 595.84, "Completed", 595.84]}}, {"Transfer ID": 23391, "Amount": "2,530.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_be10062d5e4908ab3b6d6b64ebfa9b2f", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ROBERTO CARLOS ZARATE RAMIREZ", "Sender User Email": "<EMAIL>", "Recipient Name": "maria sepulveda", "All Payouts": {"payout_7814058877888fa71800050f41820f7a": ["Completed", 2459.16, "Completed", 2459.16], "payout_be10062d5e4908ab3b6d6b64ebfa9b2f": ["Completed", 2459.16, "Completed", 2459.16]}}, {"Transfer ID": 23390, "Amount": "100.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_232a235ade8efa6a9dc4598b163b572e", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ANTONIO RUBIO SANTOS", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_72eb7fd04e552fee27c3123195433926": ["Completed", 97.2, "Completed", 97.2], "payout_232a235ade8efa6a9dc4598b163b572e": ["Completed", 97.2, "Completed", 97.2]}}, {"Transfer ID": 23389, "Amount": "205.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_e705c9d516181966497b744bb8b71bf2", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "DAVID JONATHAN RUIZ FLORES", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_727d892c1830b0ebe0a35a625aa92ebb": ["Completed", 199.26, "Completed", 199.26], "payout_e705c9d516181966497b744bb8b71bf2": ["Completed", 199.26, "Completed", 199.26]}}, {"Transfer ID": 23388, "Amount": "700.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_2ccb64f217fe5390befb655ee67fe01d", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "RAUL CRUZ SALVADOR", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>  cruz salvador", "All Payouts": {"payout_45cb9850c3dcc1cf2bea54f91ecca64b": ["Completed", 680.4, "Completed", 680.4], "payout_2ccb64f217fe5390befb655ee67fe01d": ["Completed", 680.4, "Completed", 680.4]}}, {"Transfer ID": 23387, "Amount": "26.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_b6b0983dbaca9e915f1533388647e9fd", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "DANIEL LUGO BONILLA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_6c69f0515705886ab11a1ba4b9324a1c": ["Completed", 25.27, "Completed", 25.27], "payout_b6b0983dbaca9e915f1533388647e9fd": ["Completed", 25.27, "Completed", 25.27]}}, {"Transfer ID": 23386, "Amount": "530.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_0d4edae6f8645042ee1577a6ace5aa83", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "MARIO MARTINEZ VAZQUEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "IDALIA NARANJO", "All Payouts": {"payout_5aaac406f310651880ddcd092e78c417": ["Completed", 515.16, "Completed", 515.16], "payout_0d4edae6f8645042ee1577a6ace5aa83": ["Completed", 515.16, "Completed", 515.16]}}, {"Transfer ID": 23385, "Amount": "35.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_723e0f5ec01f2ec018e4bfcc1445268f", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> con<PERSON> v<PERSON>dez", "All Payouts": {"payout_041cff419f7aded5bf1d0fd3310a5f92": ["Completed", 34.02, "Completed", 34.02], "payout_723e0f5ec01f2ec018e4bfcc1445268f": ["Completed", 34.02, "Completed", 34.02]}}, {"Transfer ID": 23384, "Amount": "400.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_e91a73e4946ba89004b5ac8cbbdd7628", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "RODRIGO GABRIEL ACATITLAN", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_cfa413431975edf1dc42ebe7d4eb85b6": ["Completed", 388.8, "Completed", 388.8], "payout_e91a73e4946ba89004b5ac8cbbdd7628": ["Completed", 388.8, "Completed", 388.8]}}, {"Transfer ID": 23382, "Amount": "520.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_aefe06dc7c2c149713e1ee256322bf13", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "Emeter<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>", "All Payouts": {"payout_c8b94c8d0b0f56509f34a2171ac0d1ee": ["Completed", 505.44, "Completed", 505.44], "payout_aefe06dc7c2c149713e1ee256322bf13": ["Completed", 505.44, "Completed", 505.44]}}, {"Transfer ID": 23381, "Amount": "41.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_b7b4a8f2fbdf0220483f02f360b1fc08", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON><PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>", "All Payouts": {"payout_8fbb0061095c95081cc54bfa7e156984": ["Completed", 39.85, "Completed", 39.85], "payout_b7b4a8f2fbdf0220483f02f360b1fc08": ["Completed", 39.85, "Completed", 39.85]}}, {"Transfer ID": 23380, "Amount": "260.00", "Status": "Completed", "Type": "bank", "Method": "mx_banorte_ixi_bank", "Rapyd Payout ID": "payout_cd54234eafa8f1cf87054cab12153968", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "JOSE JAVIER ALONZO ZARATE", "Sender User Email": "<EMAIL>", "Recipient Name": "j<PERSON><PERSON> ", "All Payouts": {"payout_634cf42ace763294e4895a1dd9bf35c9": ["Completed", 252.72, "Completed", 252.72], "payout_cd54234eafa8f1cf87054cab12153968": ["Completed", 252.72, "Completed", 252.72]}}, {"Transfer ID": 23379, "Amount": "225.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_13be8c9f06ff23e7381ada2f137f2784", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_2728cf42810c4b8c3233487d5694919a": ["Completed", 218.7, "Completed", 218.7], "payout_13be8c9f06ff23e7381ada2f137f2784": ["Completed", 218.7, "Completed", 218.7]}}, {"Transfer ID": 23378, "Amount": "800.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_78f9fe9918ea3550f0579c561cb31f8f", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "MARTIN GUADALUPE MORALES SANTIAGO", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>", "All Payouts": {"payout_f424e44567e8a57f18dc6350c92190fd": ["Completed", 777.6, "Completed", 777.6], "payout_78f9fe9918ea3550f0579c561cb31f8f": ["Completed", 777.6, "Completed", 777.6]}}, {"Transfer ID": 23377, "Amount": "27.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_8afe37854b82e80601a4fd55071190db", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "NEMESIO LOPEZ MENDOZA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>", "All Payouts": {"payout_78c9ca0a8844706c86046d56d7092bee": ["Completed", 26.24, "Completed", 26.24], "payout_8afe37854b82e80601a4fd55071190db": ["Completed", 26.24, "Completed", 26.24]}}, {"Transfer ID": 23376, "Amount": "80.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_0eb3790e9ff16ca1314182f4a3cd1036", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "jessica lizbeth ronda guzman", "All Payouts": {"payout_61cf897c6cd338492658ad66c32cd75a": ["Completed", 77.76, "Completed", 77.76], "payout_0eb3790e9ff16ca1314182f4a3cd1036": ["Completed", 77.76, "Completed", 77.76]}}, {"Transfer ID": 23375, "Amount": "127.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_e13199ee23fda4435ae7144a9432e42e", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ANTONIO MARTINEZ HERNANDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>  mart<PERSON>", "All Payouts": {"payout_fabdbdc0be24281a6ba9b54de4de9319": ["Completed", 123.44, "Completed", 123.44], "payout_e13199ee23fda4435ae7144a9432e42e": ["Completed", 123.44, "Completed", 123.44]}}, {"Transfer ID": 23374, "Amount": "44.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_7ee72a403a5287e00d12bceb9515a386", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "EFRAIN RAMIREZ MANCINAS", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> c<PERSON>z", "All Payouts": {"payout_5573cbf59c4c5e1d58edbe0c47b9109d": ["Completed", 42.77, "Completed", 42.77], "payout_7ee72a403a5287e00d12bceb9515a386": ["Completed", 42.77, "Completed", 42.77]}}, {"Transfer ID": 23373, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_f3f5b0c85edd6b1845a4b973e5a31149", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "JOSE ARMANDO LAZARO GASPAR", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>", "All Payouts": {"payout_b9a9deb458d094d9ce09b7eb26cef91f": ["Completed", 24.3, "Completed", 24.3], "payout_f3f5b0c85edd6b1845a4b973e5a31149": ["Completed", 24.3, "Completed", 24.3]}}, {"Transfer ID": 23372, "Amount": "428.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_1c943a13edde8353b54455fbe8f077a3", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "LUCIO HERNANDEZ JOSE", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON><PERSON> ", "All Payouts": {"payout_2dd0bc165a25ef7399e1943821f4f384": ["Completed", 416.02, "Completed", 416.02], "payout_1c943a13edde8353b54455fbe8f077a3": ["Completed", 416.02, "Completed", 416.02]}}, {"Transfer ID": 23371, "Amount": "50.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_686e77ac6606f658f642f5064e001148", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "RUFINO VAZQUEZ LOPEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_e5c0be0d5dd1d3856484a2b148f72aae": ["Completed", 48.6, "Completed", 48.6], "payout_686e77ac6606f658f642f5064e001148": ["Completed", 48.6, "Completed", 48.6]}}, {"Transfer ID": 23370, "Amount": "50.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_ec55363b24a918dc30faa1cfe000b557", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>", "All Payouts": {"payout_aaf29b9ce47255ffd2b4ce00e08173a5": ["Completed", 48.6, "Completed", 48.6], "payout_ec55363b24a918dc30faa1cfe000b557": ["Completed", 48.6, "Completed", 48.6]}}, {"Transfer ID": 23369, "Amount": "150.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_1bf2ad471a0e34e36b618da6ee973c3c", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "HECTOR AGUSTIN MARTINEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "maria chavez", "All Payouts": {"payout_f6476687c43598992892adc000daca90": ["Completed", 145.8, "Completed", 145.8], "payout_1bf2ad471a0e34e36b618da6ee973c3c": ["Completed", 145.8, "Completed", 145.8]}}, {"Transfer ID": 23368, "Amount": "51.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_ea9eeecec938368cec400f2daf806ad4", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>", "All Payouts": {"payout_76757c4845e3399fd51db0ed619550d3": ["Completed", 49.57, "Completed", 49.57], "payout_ea9eeecec938368cec400f2daf806ad4": ["Completed", 49.57, "Completed", 49.57]}}, {"Transfer ID": 23367, "Amount": "550.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_962def407b9ed4e31f07e7d08aa0b34e", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "SALOMON SANCHEZ VELASCO", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>", "All Payouts": {"payout_71cc7e85bdb800bccc572593e7bf0af9": ["Completed", 534.6, "Completed", 534.6], "payout_962def407b9ed4e31f07e7d08aa0b34e": ["Completed", 534.6, "Completed", 534.6]}}, {"Transfer ID": 23366, "Amount": "51.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_54bbfbe4552b9af12ff5c643553f7aa0", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "juan <PERSON>  salas cuevas", "All Payouts": {"payout_d6c0e1b3655da5b7f8ecad4bea390e34": ["Completed", 49.57, "Completed", 49.57], "payout_54bbfbe4552b9af12ff5c643553f7aa0": ["Completed", 49.57, "Completed", 49.57]}}, {"Transfer ID": 23365, "Amount": "500.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_e14c8114a753af991526adee3ce265ed", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON> ", "All Payouts": {"payout_fe7410768e8c97725613726fea3806f6": ["Completed", 486, "Completed", 486], "payout_e14c8114a753af991526adee3ce265ed": ["Completed", 486, "Completed", 486]}}, {"Transfer ID": 23364, "Amount": "34.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_07348e43c02bdf1b453d602d5c207dab", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "FRANCISCO ELIGIO FLORES", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>  t<PERSON>", "All Payouts": {"payout_db9b01404cd2753080da2ced53273018": ["Completed", 33.05, "Completed", 33.05], "payout_07348e43c02bdf1b453d602d5c207dab": ["Completed", 33.05, "Completed", 33.05]}}, {"Transfer ID": 23363, "Amount": "506.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_4c9171a866927d90936e13cf15b2d7e0", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ARMANDO MUNOZ REYES", "Sender User Email": "<EMAIL>", "Recipient Name": "johana itzel gomes cortez", "All Payouts": {"payout_24348b7a61390558c8d62ea9514124bc": ["Completed", 491.83, "Completed", 491.83], "payout_4c9171a866927d90936e13cf15b2d7e0": ["Completed", 491.83, "Completed", 491.83]}}, {"Transfer ID": 23362, "Amount": "950.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_3051a32c5bde3c40cbaf8c85bfab4fa7", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "ERIC SAMPAYO BAUTISTA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> morales", "All Payouts": {"payout_7abe193da310efa868920ee163af6209": ["Completed", 923.4, "Completed", 923.4], "payout_3051a32c5bde3c40cbaf8c85bfab4fa7": ["Completed", 923.4, "Completed", 923.4]}}, {"Transfer ID": 23361, "Amount": "600.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_306304486f59305a9ee662bbc46b950a", "Employer": " Trevino Ag Services", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>", "All Payouts": {"payout_d45d8855d90f9aec2edc74679737a4a5": ["Completed", 583.2, "Completed", 583.2], "payout_306304486f59305a9ee662bbc46b950a": ["Completed", 583.2, "Completed", 583.2]}}, {"Transfer ID": 23360, "Amount": "3,500.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_3b4a5793a69e54934b7427c7419b4ece", "Employer": " Trevino Ag Services", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>", "All Payouts": {"payout_b49f30438bd5c8b56e6b269ca0a62740": ["Completed", 3402, "Completed", 3402], "payout_3b4a5793a69e54934b7427c7419b4ece": ["Completed", 3402, "Completed", 3402]}}, {"Transfer ID": 23359, "Amount": "506.00", "Status": "Completed", "Type": "bank", "Method": "mx_banorte_ixi_bank", "Rapyd Payout ID": "payout_2230ec1d32a1efc03980b847936cf8be", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_35fa51af497dec8bfd12dbafb874b249": ["Completed", 491.83, "Completed", 491.83], "payout_2230ec1d32a1efc03980b847936cf8be": ["Completed", 491.83, "Completed", 491.83]}}, {"Transfer ID": 23358, "Amount": "350.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_f4d8bb9144969634aee4828f5705a448", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "maria a<PERSON>cena  go<PERSON>z castro", "All Payouts": {"payout_ecead8cbbfe0972044aa7fa4cf35626c": ["Completed", 340.2, "Completed", 340.2], "payout_f4d8bb9144969634aee4828f5705a448": ["Completed", 340.2, "Completed", 340.2]}}, {"Transfer ID": 23357, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_ac68cd32738a53a15939e9b402eade91", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "GILBERTO LOPEZ ANTONIO", "Sender User Email": "<EMAIL>", "Recipient Name": "juan<PERSON>", "All Payouts": {"payout_2cec251c190168123120eb31127965e2": ["Completed", 24.3, "Completed", 24.3], "payout_ac68cd32738a53a15939e9b402eade91": ["Completed", 24.3, "Completed", 24.3]}}, {"Transfer ID": 23356, "Amount": "40.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_c55552a93347d79a6ef1d76d3a60ed26", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_a08c4142248f996a8a4dc35ee8f7f85d": ["Completed", 38.88, "Completed", 38.88], "payout_c55552a93347d79a6ef1d76d3a60ed26": ["Completed", 38.88, "Completed", 38.88]}}, {"Transfer ID": 23355, "Amount": "1,183.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_884bd71b7e2db8e6b7759a71f12a7bf1", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_fd2a59767813f1ba396ab614876cb33b": ["Completed", 1149.88, "Completed", 1149.88], "payout_884bd71b7e2db8e6b7759a71f12a7bf1": ["Completed", 1149.88, "Completed", 1149.88]}}, {"Transfer ID": 23354, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_9bfcf98103117d95e6e6dbcac6cc97a0", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> romano ", "All Payouts": {"payout_d94b8fafbbebe42858426b8d05b9039e": ["Completed", 24.3, "Completed", 24.3], "payout_9bfcf98103117d95e6e6dbcac6cc97a0": ["Completed", 24.3, "Completed", 24.3]}}, {"Transfer ID": 23353, "Amount": "31.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_053cb90b0a9976d74d987334db2cc40c", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "Santiago Tolentino Prieto", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON> veliz", "All Payouts": {"payout_c2046c798902b8d502960e7bd5c9ac03": ["Completed", 30.13, "Completed", 30.13], "payout_053cb90b0a9976d74d987334db2cc40c": ["Completed", 30.13, "Completed", 30.13]}}, {"Transfer ID": 23352, "Amount": "146.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_f2f73c15a7c724489badde4fe90be772", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "EMANUEL HERNANDEZ HERNANDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> b<PERSON> ", "All Payouts": {"payout_413035a7a79a59ae61790a3185d2409a": ["Completed", 141.91, "Completed", 141.91], "payout_f2f73c15a7c724489badde4fe90be772": ["Completed", 141.91, "Completed", 141.91]}}, {"Transfer ID": 23351, "Amount": "520.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_a228e288da9e9bb08f6c39a3e3129aab", "Employer": "Huggins Farms - Prefunded", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_c6202dca77f0504b036af402dc78088d": ["Completed", 505.44, "Completed", 505.44], "payout_a228e288da9e9bb08f6c39a3e3129aab": ["Completed", 505.44, "Completed", 505.44]}}, {"Transfer ID": 23349, "Amount": "588.29", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_43566bc4110ba565ae117e5acbca9d62", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "martina  lezama ", "All Payouts": {"payout_f0151dba5ded0f6a85daba3043eebb31": ["Completed", 571.82, "Completed", 571.82], "payout_43566bc4110ba565ae117e5acbca9d62": ["Completed", 571.82, "Completed", 571.82]}}, {"Transfer ID": 23348, "Amount": "1,195.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_c2d73f0f454e522d74a635aa18edbfc3", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "clara anahi herrera reyes", "All Payouts": {"payout_6a13f590a10d789b013082bd1569bbcc": ["Completed", 1161.54, "Completed", 1161.54], "payout_c2d73f0f454e522d74a635aa18edbfc3": ["Completed", 1161.54, "Completed", 1161.54]}}, {"Transfer ID": 23347, "Amount": "804.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_83a8c4ada95e99f48799981952c59037", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "RAUL CHAVEZ GARCIA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>", "All Payouts": {"payout_5e11143d08106ec42c3cc58bb9ccfbae": ["Completed", 781.49, "Completed", 781.49], "payout_83a8c4ada95e99f48799981952c59037": ["Completed", 781.49, "Completed", 781.49]}}, {"Transfer ID": 23346, "Amount": "304.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_c64a287ce9962a11d809c44cd8a28bee", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_142fb6dfcc44b862c4d2e9b52608e1be": ["Completed", 295.49, "Completed", 295.49], "payout_c64a287ce9962a11d809c44cd8a28bee": ["Completed", 295.49, "Completed", 295.49]}}, {"Transfer ID": 23345, "Amount": "520.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_c7e710bb4945bae2d74adfd9d882ff47", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ABRAHAM ZENON SALDANA", "Sender User Email": "<EMAIL>", "Recipient Name": "Eusebio Zenon Nava", "All Payouts": {"payout_ad4d1829a20e9a3f01814873495b2444": ["Completed", 505.44, "Completed", 505.44], "payout_c7e710bb4945bae2d74adfd9d882ff47": ["Completed", 505.44, "Completed", 505.44]}}, {"Transfer ID": 23344, "Amount": "150.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_6b153ef173763c8e5938cd4b6aa8e909", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "SALVADOR GARCIA SALDANA", "Sender User Email": "<EMAIL>", "Recipient Name": "Eusebio  Zenón Nava", "All Payouts": {"payout_1b2e90ce594f324d7fd8779fd2e31a26": ["Completed", 145.8, "Completed", 145.8], "payout_6b153ef173763c8e5938cd4b6aa8e909": ["Completed", 145.8, "Completed", 145.8]}}, {"Transfer ID": 23343, "Amount": "506.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_ca86100cc1380fddb8e77b0f99e426a1", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "leticia emilia   mart<PERSON> ", "All Payouts": {"payout_2805e24ffb79d61d79dc6baf56debede": ["Completed", 491.83, "Completed", 491.83], "payout_ca86100cc1380fddb8e77b0f99e426a1": ["Completed", 491.83, "Completed", 491.83]}}, {"Transfer ID": 23342, "Amount": "253.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_3dff03e4bb4d37013ed2638fec4e111c", "Employer": "Heritage Farms, LLC", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "j<PERSON>o gonza<PERSON>", "All Payouts": {"payout_708a3a03d4cf738dfc39c88f6604a274": ["Completed", 245.92, "Completed", 245.92], "payout_3dff03e4bb4d37013ed2638fec4e111c": ["Completed", 245.92, "Completed", 245.92]}}, {"Transfer ID": 23341, "Amount": "38.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_9292ea0fc4115f3e7acb91889211089b", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_7df27924a99d065d3536fb468b2d477b": ["Completed", 36.94, "Completed", 36.94], "payout_9292ea0fc4115f3e7acb91889211089b": ["Completed", 36.94, "Completed", 36.94]}}, {"Transfer ID": 23340, "Amount": "300.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_9f73e18beeb7b9b326f6bf5f93906d6b", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "delfina Espinoza santamaria", "All Payouts": {"payout_a30b5cc7e947494cd44e2d9b45c61c69": ["Completed", 291.6, "Completed", 291.6], "payout_9f73e18beeb7b9b326f6bf5f93906d6b": ["Completed", 291.6, "Completed", 291.6]}}, {"Transfer ID": 23339, "Amount": "53.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_9f80578744b85b2b971941b66ea67ed1", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>", "All Payouts": {"payout_2641ef4dbd6ca65dd49234b5ea46fd41": ["Completed", 51.52, "Completed", 51.52], "payout_9f80578744b85b2b971941b66ea67ed1": ["Completed", 51.52, "Completed", 51.52]}}, {"Transfer ID": 23338, "Amount": "1,050.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_b7af73edb8245bd2094073d83caf548f", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "mayra  garcia", "All Payouts": {"payout_52443fca8a626e021cb288d9401fdf5e": ["Completed", 1020.6, "Completed", 1020.6], "payout_b7af73edb8245bd2094073d83caf548f": ["Completed", 1020.6, "Completed", 1020.6]}}, {"Transfer ID": 23337, "Amount": "41.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_635ef97bf4e7781ea905efa292044364", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "juancar<PERSON>made<PERSON>resend<PERSON>@gmail.com", "Recipient Name": "benito  navarrete nava", "All Payouts": {"payout_ed660828d4390736659c938b2774f749": ["Completed", 39.85, "Completed", 39.85], "payout_635ef97bf4e7781ea905efa292044364": ["Completed", 39.85, "Completed", 39.85]}}, {"Transfer ID": 23335, "Amount": "28.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_39bc3c8c845b35d7bd66b1a702f7f899", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "German <PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "EVELIA  GODINEZ GARCIA", "All Payouts": {"payout_077e039a11234384cc0c1d71df7ee7eb": ["Completed", 27.22, "Completed", 27.22], "payout_39bc3c8c845b35d7bd66b1a702f7f899": ["Completed", 27.22, "Completed", 27.22]}}, {"Transfer ID": 23334, "Amount": "30.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_2b6e742433004954a2b24d11c48583e4", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "SERGIO TENTZOHUA SANCHEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_311bf64ad5c9b4c1545e33cc60a41172": ["Completed", 29.16, "Completed", 29.16], "payout_2b6e742433004954a2b24d11c48583e4": ["Completed", 29.16, "Completed", 29.16]}}, {"Transfer ID": 23333, "Amount": "26.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_080d427b143c46e71a5e39988f089ddb", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "MARIO ALBERTO HERNANDEZ LLANAS", "Sender User Email": "hernan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "Recipient Name": "<PERSON>", "All Payouts": {"payout_51250eedae79a12d45ea097c2eb697ec": ["Completed", 25.27, "Completed", 25.27], "payout_080d427b143c46e71a5e39988f089ddb": ["Completed", 25.27, "Completed", 25.27]}}, {"Transfer ID": 23332, "Amount": "30.00", "Status": "Completed", "Type": "bank", "Method": "mx_banorte_ixi_bank", "Rapyd Payout ID": "payout_beb4d19a720a7f7b4c8b52e73a5d41e3", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "SERGIO ALEXIS LEDESMA MENDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> ", "All Payouts": {"payout_ec3d9b8ca3aa8a32d7135fe7ebab8b9c": ["Completed", 29.16, "Completed", 29.16], "payout_beb4d19a720a7f7b4c8b52e73a5d41e3": ["Completed", 29.16, "Completed", 29.16]}}, {"Transfer ID": 23331, "Amount": "1,000.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_510b9f5bdf0f2b4423bf4b88aab10ed3", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ANDRES CARDENAS TORRES", "Sender User Email": "<EMAIL>", "Recipient Name": "andres  cardenas ", "All Payouts": {"payout_4f580a72d1949d942586f1c02452e205": ["Completed", 972, "Completed", 972], "payout_510b9f5bdf0f2b4423bf4b88aab10ed3": ["Completed", 972, "Completed", 972]}}, {"Transfer ID": 23330, "Amount": "300.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_2c9ce3fb1c86eaa7545188fc90fb4ebc", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "MARTIN JESUS RODRIGUEZ CHAVEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "jessica  barboza", "All Payouts": {"payout_e8d7bfbf2d342282239290c82383bb07": ["Completed", 291.6, "Completed", 291.6], "payout_2c9ce3fb1c86eaa7545188fc90fb4ebc": ["Completed", 291.6, "Completed", 291.6]}}, {"Transfer ID": 23329, "Amount": "70.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_53dd869f45055ac7e99cef25c3d07a58", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_958d6e6a39f87b41905a9d1ee379c49f": ["Completed", 68.04, "Completed", 68.04], "payout_53dd869f45055ac7e99cef25c3d07a58": ["Completed", 68.04, "Completed", 68.04]}}, {"Transfer ID": 23328, "Amount": "100.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_85bfe932cfee9f1d9a4ac56566c38c7e", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "GUADALUPE VIELMA ORTIZ", "Sender User Email": "<EMAIL>", "Recipient Name": "Guadalupe vielma ortiz", "All Payouts": {"payout_5ca94fafbdfeccef84646a2acf7b1da7": ["Completed", 97.2, "Completed", 97.2], "payout_85bfe932cfee9f1d9a4ac56566c38c7e": ["Completed", 97.2, "Completed", 97.2]}}, {"Transfer ID": 23327, "Amount": "51.00", "Status": "Completed", "Type": "bank", "Method": "mx_scotiabank_bank", "Rapyd Payout ID": "payout_ad6778c57d799b6b77d02c35e9b39553", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JACINTO YAIR GARCIA MOLINA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> ", "All Payouts": {"payout_194937f433827c4ad3d873076e94ef1f": ["Completed", 49.57, "Completed", 49.57], "payout_ad6778c57d799b6b77d02c35e9b39553": ["Completed", 49.57, "Completed", 49.57]}}, {"Transfer ID": 23326, "Amount": "102.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_cb0081f2d0cb40f084bf310b0605f749", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON> Valenzuel<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "ma<PERSON><PERSON> cu<PERSON> Valenzuela", "All Payouts": {"payout_bd730bc2080e4a992639cef86fd093f4": ["Completed", 99.14, "Completed", 99.14], "payout_cb0081f2d0cb40f084bf310b0605f749": ["Completed", 99.14, "Completed", 99.14]}}, {"Transfer ID": 23325, "Amount": "500.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_84bd299f9ff6a78ab99f09bd8859e530", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "NAZARIO TEMOXTLE PANZO", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON> Temox<PERSON> Panzo", "All Payouts": {"payout_de17499150001497c660005bb8eb6d56": ["Completed", 486, "Completed", 486], "payout_84bd299f9ff6a78ab99f09bd8859e530": ["Completed", 486, "Completed", 486]}}, {"Transfer ID": 23324, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_a3b6b40cb8ce7fda605076fb8a42c2c3", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "alexia <PERSON>", "All Payouts": {"payout_923519126637a35b519dfde19ee24616": ["Completed", 24.3, "Completed", 24.3], "payout_a3b6b40cb8ce7fda605076fb8a42c2c3": ["Completed", 24.3, "Completed", 24.3]}}, {"Transfer ID": 23323, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_f346c97b759fb56b241fab9d2cfcd0d5", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "JOSE ALBERTO BETANZO GARCIA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_958a252b01a3da4b902643f3ab98cd0f": ["Completed", 24.3, "Completed", 24.3], "payout_f346c97b759fb56b241fab9d2cfcd0d5": ["Completed", 24.3, "Completed", 24.3]}}, {"Transfer ID": 23322, "Amount": "184.00", "Status": "Completed", "Type": "bank", "Method": "mx_santander_bank", "Rapyd Payout ID": "payout_b6a0bc5c61ede0556922743f3699904f", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "irving  <PERSON> ", "All Payouts": {"payout_49272985a530cd5e69f531a7d4962207": ["Completed", 178.85, "Completed", 178.85], "payout_b6a0bc5c61ede0556922743f3699904f": ["Completed", 178.85, "Completed", 178.85]}}, {"Transfer ID": 23321, "Amount": "30.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_753c648d0018183d7d9c22ce693d3ecc", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "RIGOBERTO GUERRA HERNANDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "cristina guadalupe villanueva perez", "All Payouts": {"payout_51572869856286963452ea93a59477a6": ["Completed", 29.16, "Completed", 29.16], "payout_753c648d0018183d7d9c22ce693d3ecc": ["Completed", 29.16, "Completed", 29.16]}}, {"Transfer ID": 23320, "Amount": "530.65", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_a2a1bddb9215a1c1d116aaf4a66b0ede", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_3fc319208a37c229e416332ce5d14f85": ["Completed", 515.79, "Completed", 515.79], "payout_a2a1bddb9215a1c1d116aaf4a66b0ede": ["Completed", 515.79, "Completed", 515.79]}}, {"Transfer ID": 23319, "Amount": "230.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_098c39a5f4b78bfa5b84255df3bad4f5", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "PEDRO APARICIO GONZALEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>", "All Payouts": {"payout_f3460acdfe1d830249337f5faccbf1b4": ["Completed", 223.56, "Completed", 223.56], "payout_098c39a5f4b78bfa5b84255df3bad4f5": ["Completed", 223.56, "Completed", 223.56]}}, {"Transfer ID": 23318, "Amount": "60.65", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_9392267e59ac5301c8402c23b8dd9c48", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JESUS ALEJANDRO AVALOS RIVERA", "Sender User Email": "jesus<PERSON><EMAIL>", "Recipient Name": "juana baez ", "All Payouts": {"payout_21ea885c6e9046e3475191856e06d83d": ["Completed", 58.95, "Completed", 58.95], "payout_9392267e59ac5301c8402c23b8dd9c48": ["Completed", 58.95, "Completed", 58.95]}}, {"Transfer ID": 23317, "Amount": "169.00", "Status": "Completed", "Type": "bank", "Method": "mx_hsbc_bank", "Rapyd Payout ID": "payout_ef585f5bbe152fdf98d3f3e0e2a6b3c7", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "RICARDO DE JESUS HERNANDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_c939a398efd07c3c3ac3812979ae5315": ["Completed", 164.27, "Completed", 164.27], "payout_ef585f5bbe152fdf98d3f3e0e2a6b3c7": ["Completed", 164.27, "Completed", 164.27]}}, {"Transfer ID": 23316, "Amount": "1,500.00", "Status": "Completed", "Type": "bank", "Method": "mx_banamex_bank", "Rapyd Payout ID": "payout_5d2f69ab137b4ba31e81254db8e94bd4", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "CARLOS MANUEL EDISON VALLE", "Sender User Email": "<EMAIL>", "Recipient Name": "LUIS MIGUEL TAMARIZ VALLE", "All Payouts": {"payout_a1094d47524a6ed211740c570e1723ce": ["Completed", 1458, "Completed", 1458], "payout_5d2f69ab137b4ba31e81254db8e94bd4": ["Completed", 1458, "Completed", 1458]}}, {"Transfer ID": 23315, "Amount": "169.00", "Status": "Completed", "Type": "bank", "Method": "mx_banamex_bank", "Rapyd Payout ID": "payout_0b6d399c3ffc4561c9f183d4032efab3", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "DANIEL VELAZQUEZ VICTORIANO", "Sender User Email": "victoria<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "Recipient Name": "<PERSON>", "All Payouts": {"payout_a2f440672fb40dd7be9588971bbf328b": ["Completed", 164.27, "Completed", 164.27], "payout_0b6d399c3ffc4561c9f183d4032efab3": ["Completed", 164.27, "Completed", 164.27]}}, {"Transfer ID": 23314, "Amount": "169.00", "Status": "Completed", "Type": "bank", "Method": "mx_hsbc_bank", "Rapyd Payout ID": "payout_f032f9876c87a9743c35c6e382a0849b", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "RICARDO DE JESUS HERNANDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_74be2d4d21d27a79dd7417cca161403c": ["Completed", 164.27, "Completed", 164.27], "payout_f032f9876c87a9743c35c6e382a0849b": ["Completed", 164.27, "Completed", 164.27]}}, {"Transfer ID": 23313, "Amount": "600.00", "Status": "Completed", "Type": "bank", "Method": "mx_caja_pop_mexico_bank", "Rapyd Payout ID": "payout_624a559a5f80b1d4d2664714bf77b2a1", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>", "All Payouts": {"payout_b343ba09f646ac48da4ec1eb8b9f8042": ["Completed", 583.2, "Completed", 583.2], "payout_624a559a5f80b1d4d2664714bf77b2a1": ["Completed", 583.2, "Completed", 583.2]}}, {"Transfer ID": 23312, "Amount": "105.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_3e046b7cd88801f3d694f7cb53a35af8", "Employer": "Huggins Farms - Prefunded", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_36323f143be0e6feb81828bb82eacfc8": ["Completed", 102.06, "Completed", 102.06], "payout_3e046b7cd88801f3d694f7cb53a35af8": ["Completed", 102.06, "Completed", 102.06]}}, {"Transfer ID": 23311, "Amount": "900.00", "Status": "Completed", "Type": "bank", "Method": "mx_banorte_ixi_bank", "Rapyd Payout ID": "payout_e1ceb212270ff26a2b36672e0687e7ac", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "federico medina reyes", "All Payouts": {"payout_1263457cf01121b6ba1ff94c1b1e37b1": ["Completed", 874.8, "Completed", 874.8], "payout_e1ceb212270ff26a2b36672e0687e7ac": ["Completed", 874.8, "Completed", 874.8]}}, {"Transfer ID": 23310, "Amount": "1,250.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_d6da141604c441a54e152fca0d1d0bde", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JORGE LUIS DE LEON SUSTAITA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>", "All Payouts": {"payout_90d19ff194b7282e871261055337b1da": ["Completed", 1215, "Completed", 1215], "payout_d6da141604c441a54e152fca0d1d0bde": ["Completed", 1215, "Completed", 1215]}}, {"Transfer ID": 23309, "Amount": "300.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_b634b46621d1d90d1e189c8bd4426da6", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ARMANDO GRAJEDA ZARATE", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>a fuentes", "All Payouts": {"payout_bdd0c3dfc9dbd4078e5808f701f97439": ["Completed", 291.6, "Completed", 291.6], "payout_b634b46621d1d90d1e189c8bd4426da6": ["Completed", 291.6, "Completed", 291.6]}}, {"Transfer ID": 23308, "Amount": "26.00", "Status": "Completed", "Type": "bank", "Method": "mx_hsbc_bank", "Rapyd Payout ID": "payout_fc617f3830a5830048b8ada8e26566f3", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JESUS EMANUEL DIAZ LOPEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "oscar la<PERSON> lo<PERSON>z", "All Payouts": {"payout_74d971dfbd031b4db11f152cb88fed7c": ["Completed", 25.27, "Completed", 25.27], "payout_fc617f3830a5830048b8ada8e26566f3": ["Completed", 25.27, "Completed", 25.27]}}, {"Transfer ID": 23307, "Amount": "506.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_6c419c97454d742e3eea198ee62dc7a9", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "CESAR ANTONIO AVALOS HERNANDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "perla melgoza", "All Payouts": {"payout_fae4c284559317d442c5fb628b621620": ["Completed", 491.83, "Completed", 491.83], "payout_6c419c97454d742e3eea198ee62dc7a9": ["Completed", 491.83, "Completed", 491.83]}}, {"Transfer ID": 23306, "Amount": "264.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_fa6e510b6de07ad72a5e0beaecd25e45", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "CARLOS ALBERTO MAGANA IBARRA", "Sender User Email": "karloz<PERSON><PERSON>@gmail.com", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_790bc14537ec592bfbc8aa500360d212": ["Completed", 256.61, "Completed", 256.61], "payout_fa6e510b6de07ad72a5e0beaecd25e45": ["Completed", 256.61, "Completed", 256.61]}}, {"Transfer ID": 23305, "Amount": "800.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_6c5d8793b0665c3b9b64e723e6f28829", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_7800b12ac1bfa2a7e6af7a78bb34c891": ["Completed", 777.6, "Completed", 777.6], "payout_6c5d8793b0665c3b9b64e723e6f28829": ["Completed", 777.6, "Completed", 777.6]}}, {"Transfer ID": 23303, "Amount": "300.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_37b479e5cf817b179fe398ac066ba02c", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>", "All Payouts": {"payout_6905432d0e8250fca7123456076ef501": ["Completed", 291.6, "Completed", 291.6], "payout_37b479e5cf817b179fe398ac066ba02c": ["Completed", 291.6, "Completed", 291.6]}}, {"Transfer ID": 23302, "Amount": "80.00", "Status": "Completed", "Type": "bank", "Method": "mx_banrejio_bank", "Rapyd Payout ID": "payout_87850ccad63b77079fa145bcb8ddd7ad", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JAVIER DE LOS SANTOS SALAZAR", "Sender User Email": "delossantossala<PERSON><PERSON><EMAIL>", "Recipient Name": "javier ortiz <PERSON>az ", "All Payouts": {"payout_8104b422cd9beb4e082afb96e92da736": ["Completed", 77.76, "Completed", 77.76], "payout_87850ccad63b77079fa145bcb8ddd7ad": ["Completed", 77.76, "Completed", 77.76]}}, {"Transfer ID": 23301, "Amount": "900.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_79a3e4783328551c7d70bba5a7d23919", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>", "All Payouts": {"payout_5fd2eef827f73b804d14a6c6d5d01526": ["Completed", 874.8, "Completed", 874.8], "payout_79a3e4783328551c7d70bba5a7d23919": ["Completed", 874.8, "Completed", 874.8]}}, {"Transfer ID": 23300, "Amount": "152.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_23e6503c57c1cc68d01d29e031f46f3e", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "FELIPE JUAREZ MARTINEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>", "All Payouts": {"payout_061bce45ebe509992d8bc461be3db02c": ["Completed", 147.74, "Completed", 147.74], "payout_23e6503c57c1cc68d01d29e031f46f3e": ["Completed", 147.74, "Completed", 147.74]}}, {"Transfer ID": 23299, "Amount": "51.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_1b49144f1544e6e03ccf36514bfc7557", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JOSE REFUGIO RANGEL VAZQUEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>", "All Payouts": {"payout_1dd78fa54edb990c984135d6a3353016": ["Completed", 49.57, "Completed", 49.57], "payout_1b49144f1544e6e03ccf36514bfc7557": ["Completed", 49.57, "Completed", 49.57]}}, {"Transfer ID": 23298, "Amount": "397.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_2db73c5fe2dada3f9378115d69fcb762", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_4a584701cdab3be9def6296e60c2e745": ["Completed", 385.88, "Completed", 385.88], "payout_2db73c5fe2dada3f9378115d69fcb762": ["Completed", 385.88, "Completed", 385.88]}}, {"Transfer ID": 23297, "Amount": "1,040.00", "Status": "Completed", "Type": "bank", "Method": "mx_bansefi_bank", "Rapyd Payout ID": "payout_2d949868c638386cfdf3b9c3f52b54c5", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> ", "All Payouts": {"payout_70520a6d0c8efb3b7301a0ed9b0e1fce": ["Completed", 1010.88, "Completed", 1010.88], "payout_2d949868c638386cfdf3b9c3f52b54c5": ["Completed", 1010.88, "Completed", 1010.88]}}, {"Transfer ID": 23296, "Amount": "105.00", "Status": "Completed", "Type": "bank", "Method": "mx_banamex_bank", "Rapyd Payout ID": "payout_d681520660c38caf5ec402e4ca5697cf", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JESUS DANIEL NANDO ZURITA", "Sender User Email": "<EMAIL>", "Recipient Name": "YOMARA ARELLANO MARIN", "All Payouts": {"payout_9b4099faf358432a2522c5374ec57d15": ["Completed", 102.06, "Completed", 102.06], "payout_d681520660c38caf5ec402e4ca5697cf": ["Completed", 102.06, "Completed", 102.06]}}, {"Transfer ID": 23295, "Amount": "167.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_c9b188b941191dfa5349ee70d534fd74", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> antonio amador", "All Payouts": {"payout_fc722b217606204f2eed5d92594fa238": ["Completed", 162.32, "Completed", 162.32], "payout_c9b188b941191dfa5349ee70d534fd74": ["Completed", 162.32, "Completed", 162.32]}}, {"Transfer ID": 23294, "Amount": "710.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_de0c07531ecbda3a4f3349a298273b70", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_7a9ea8dd61ba9831a092ddf6761ea74d": ["Completed", 690.12, "Completed", 690.12], "payout_de0c07531ecbda3a4f3349a298273b70": ["Completed", 690.12, "Completed", 690.12]}}, {"Transfer ID": 23293, "Amount": "410.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_ac68519e4c8e90b89e217a7e4fcfab1e", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_34c180d59609c8537c2916c4c0361a91": ["Completed", 398.52, "Completed", 398.52], "payout_ac68519e4c8e90b89e217a7e4fcfab1e": ["Completed", 398.52, "Completed", 398.52]}}, {"Transfer ID": 23292, "Amount": "102.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_f3da3bb39d13a562c11441f278d75877", "Employer": "Heritage Farms, LLC", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>", "All Payouts": {"payout_df784a502bfd916bdfa245f51905b2fe": ["Completed", 99.14, "Completed", 99.14], "payout_f3da3bb39d13a562c11441f278d75877": ["Completed", 99.14, "Completed", 99.14]}}, {"Transfer ID": 23291, "Amount": "600.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_ce756658db7763a45df6a939239ab62d", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "CRESTINO NAVA NAVA", "Sender User Email": "<EMAIL>", "Recipient Name": "carmen <PERSON>", "All Payouts": {"payout_88314c65540dd01492e2f8250e1d491d": ["Completed", 583.2, "Completed", 583.2], "payout_ce756658db7763a45df6a939239ab62d": ["Completed", 583.2, "Completed", 583.2]}}, {"Transfer ID": 23290, "Amount": "50.00", "Status": "Completed", "Type": "bank", "Method": "mx_hsbc_bank", "Rapyd Payout ID": "payout_d15d04735bde713fe40ddf27fd077843", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "DIEGO GARCIA GARCIA", "Sender User Email": "<EMAIL>", "Recipient Name": "diego  garcia ", "All Payouts": {"payout_84dee11d89a363003bc6f564e2d6a8bf": ["Completed", 48.6, "Completed", 48.6], "payout_d15d04735bde713fe40ddf27fd077843": ["Completed", 48.6, "Completed", 48.6]}}, {"Transfer ID": 23289, "Amount": "90.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_798d0d7b3fc8b418ef5331c27fbecc87", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "Salvador Alejandro Rio<PERSON> Flores", "Sender User Email": "<EMAIL>", "Recipient Name": "marlen sanchez", "All Payouts": {"payout_76c48c3aa5b0f720919317224290f3bd": ["Completed", 87.48, "Completed", 87.48], "payout_798d0d7b3fc8b418ef5331c27fbecc87": ["Completed", 87.48, "Completed", 87.48]}}, {"Transfer ID": 23288, "Amount": "26.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_8f69e3d98ff109face308fb64ea19957", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ERIK ALAN MARTINEZ GAMEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "Alba martinez", "All Payouts": {"payout_ea57ab3e10768fd23ec892e8b3caa1de": ["Completed", 25.27, "Completed", 25.27], "payout_8f69e3d98ff109face308fb64ea19957": ["Completed", 25.27, "Completed", 25.27]}}, {"Transfer ID": 23287, "Amount": "510.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_8664f7565921d5512ba497bebc6e9481", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "DANIEL LUGO BONILLA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>", "All Payouts": {"payout_088c5fd488d52b4aac4d227378452ca0": ["Completed", 495.72, "Completed", 495.72], "payout_8664f7565921d5512ba497bebc6e9481": ["Completed", 495.72, "Completed", 495.72]}}, {"Transfer ID": 23286, "Amount": "500.00", "Status": "Completed", "Type": "bank", "Method": "mx_afirme_bank", "Rapyd Payout ID": "payout_56257def4d59899a7ee5d52f9621d161", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> ", "All Payouts": {"payout_441923a6074d025d8dc881d4ef219fd9": ["Completed", 486, "Completed", 486], "payout_56257def4d59899a7ee5d52f9621d161": ["Completed", 486, "Completed", 486]}}, {"Transfer ID": 23285, "Amount": "50.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_3e32578f2c69dec15d33196e62dc8c10", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "DANIEL GARCIA AGUILAR", "Sender User Email": "<EMAIL>", "Recipient Name": "Baudelia <PERSON>", "All Payouts": {"payout_3f0a651ada26354099e69a768177aabd": ["Completed", 48.6, "Completed", 48.6], "payout_3e32578f2c69dec15d33196e62dc8c10": ["Completed", 48.6, "Completed", 48.6]}}]