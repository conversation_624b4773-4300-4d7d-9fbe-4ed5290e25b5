[{"Transfer ID": 23442, "Amount": "51.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_467e16bea5e7cf6996f1f604828ff8c7", "Employer": "Heritage Farms, LLC", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>"}, {"Transfer ID": 23441, "Amount": "40.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_201062240379bf34e4f279e19c2453c5", "Employer": "Heritage Farms, LLC", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23440, "Amount": "151.61", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_08a1f91e41163574df18a4a1d5d8174b", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ANGEL SIMON RODRIGUEZ MELGAREJO", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23439, "Amount": "150.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_cc56184b34e375afd6321e3561be529e", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON> "}, {"Transfer ID": 23438, "Amount": "69.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_cc5b4b545f4e3d12949cc34f4306fc9f", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "RUTILO MARTINEZ SOTO", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON><PERSON>"}, {"Transfer ID": 23437, "Amount": "50.54", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_8be2ac682098cdf9c7d34200d5133dae", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "MISAEL PADRON RODRIGUEZ", "Sender User Email": "misael<PERSON><EMAIL>", "Recipient Name": "<PERSON><PERSON>"}, {"Transfer ID": 23436, "Amount": "320.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_1f1644390475f8d94eb1668afc23c66e", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "BERTIN CASTRO CARRIZAL", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> "}, {"Transfer ID": 23435, "Amount": "500.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_933be53f351c894fe7ca72281f768fff", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "PABLO NAVA NAVA", "Sender User Email": "<EMAIL>", "Recipient Name": "pablo nava"}, {"Transfer ID": 23434, "Amount": "350.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_a336b9af4adeff3a36ae1ac257c708b0", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "armando lopez"}, {"Transfer ID": 23433, "Amount": "30.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_34e13c06a1ed4ae933503a594aee45af", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>"}, {"Transfer ID": 23432, "Amount": "40.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_129c4f60f149fb5fb756048bebbd9e5f", "Employer": "Jordahl Farms", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "jose<PERSON><PERSON><PERSON><EMAIL>", "Recipient Name": "Cinthya V R Rmz"}, {"Transfer ID": 23431, "Amount": "41.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_5804169f62fa60075fa4e1d9d19fc30a", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JUAN HEDIBERTO PIOQUINTO PEDRO", "Sender User Email": "juan<PERSON><PERSON><EMAIL>", "Recipient Name": "sara <PERSON>"}, {"Transfer ID": 23430, "Amount": "309.00", "Status": "Completed", "Type": "bank", "Method": "mx_hsbc_bank", "Rapyd Payout ID": "payout_3bfa4ceb67b71e6f3e1faf2acb229208", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "jose luis martinez sarabia"}, {"Transfer ID": 23429, "Amount": "40.00", "Status": "Completed", "Type": "bank", "Method": "mx_stp_bank", "Rapyd Payout ID": "payout_0e6fb84a6ab6dc4b9b34da472761c215", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON> "}, {"Transfer ID": 23428, "Amount": "50.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_bbc03e961663b3c5b6f14c0e97cdd391", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "rosa  san <PERSON><PERSON><PERSON> "}, {"Transfer ID": 23427, "Amount": "90.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_53f8359b7a6e152b9c9728786bf0aa4b", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23426, "Amount": "60.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_d54c514093a99eb2eb2d71b79f7d9132", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON> <PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> jesus <PERSON> canales"}, {"Transfer ID": 23425, "Amount": "52.00", "Status": "Completed", "Type": "bank", "Method": "mx_banorte_ixi_bank", "Rapyd Payout ID": "payout_c07c2a8f284618aa58e90c4f9a38f869", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23424, "Amount": "517.81", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_d0171d9e2d79064bc7ba23f21940f97c", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON> "}, {"Transfer ID": 23423, "Amount": "26.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_870240683c1b1eff294affdc049d90f8", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "jose gerardo cruz ramirez"}, {"Transfer ID": 23422, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_6bc784e3a74b85699927600b8510dcaa", "Employer": " Trevino Ag Services", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "lizeldy guadalupe flores Martinez "}, {"Transfer ID": 23421, "Amount": "26.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_133e56ecca3f461dc33d64f331b9f75b", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "eli god<PERSON><PERSON>"}, {"Transfer ID": 23420, "Amount": "30.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_6a2e9944b892b9671fbe76c765cfe7eb", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "EDUARDO CUATRA SANCHEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23419, "Amount": "200.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_eacd884c2b798810fcbdf5be19eb2d59", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "esau felix  lujan "}, {"Transfer ID": 23418, "Amount": "300.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_a0d789d8e32df94e000917623066c481", "Employer": " Trevino Ag Services", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "LIZELDY GUADALUPE FLORES MARTINEZ"}, {"Transfer ID": 23417, "Amount": "300.00", "Status": "Completed", "Type": "bank", "Method": "mx_banamex_bank", "Rapyd Payout ID": "payout_9a89a634cab80e9afb6188cacea70bb7", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON> "}, {"Transfer ID": 23416, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_f4683411d5485fcae563f96bc5a5db5c", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ARMANDO MUNOZ REYES", "Sender User Email": "<EMAIL>", "Recipient Name": "johana itzel gomes cortez"}, {"Transfer ID": 23415, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_a61d048d07f1f9a04843e8f680a11559", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "EFRAIN RAMIREZ MANCINAS", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23414, "Amount": "150.00", "Status": "Completed", "Type": "bank", "Method": "mx_banorte_ixi_bank", "Rapyd Payout ID": "payout_1bf785222e139b521d1cd0c66cb7863c", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23413, "Amount": "400.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_97e77aa864b098223c062d3bd29b4993", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "adilene soto"}, {"Transfer ID": 23412, "Amount": "234.41", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_0822961dcc6c7daafdcbb440aca8a4ef", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "JESUS AARON ATONDO PEREZ", "Sender User Email": "<EMAIL>", "Recipient Name": "jesus atondo perez "}, {"Transfer ID": 23411, "Amount": "39.70", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_ba0bb2d92a44c21bee4aa565b56133a1", "Employer": "Tanimura and Antle Fresh Foods", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "paola morales duarte"}, {"Transfer ID": 23410, "Amount": "500.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_b9c9054f92ee7e1225768bfaafb390c1", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>"}, {"Transfer ID": 23409, "Amount": "300.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_12ae4661626243cfa2a2889b75adb3a3", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JACINTO MENDOZA MENDOZA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON><PERSON> "}, {"Transfer ID": 23408, "Amount": "26.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_3f4b985f10e2383d46204ef58bc84100", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "Dora Luz  Razo de Dios"}, {"Transfer ID": 23407, "Amount": "810.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_a6d46045acf6c982dc62bfd86d2f629d", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "LUIS GERARDO MARTINEZ PRIETO", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23406, "Amount": "825.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_f5a98c11ac88fffea0f1e8cf6aadf1ff", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "FRANCISCO DEL ANGEL TENORIO", "Sender User Email": "<EMAIL>", "Recipient Name": "rufina  de <PERSON> "}, {"Transfer ID": 23405, "Amount": "1,500.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_460d9d12fa98e5360a157df06de0696a", "Employer": "Jordahl Farms", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23404, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_6b4874c5a2d066062d228688c90d20d1", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "PEDRO ZUNIGA ZUNIGA", "Sender User Email": "<EMAIL>", "Recipient Name": "HILDA  Salazar "}, {"Transfer ID": 23403, "Amount": "556.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_e115e9d6187fd026c91933893867e7e9", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "OMAR JIMENEZ PEREZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23402, "Amount": "487.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_9a994a73c1491c0f5fdc5083458eee39", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> "}, {"Transfer ID": 23401, "Amount": "123.00", "Status": "Completed", "Type": "bank", "Method": "mx_banamex_bank", "Rapyd Payout ID": "payout_cde16ece9269c63c787bc127259406c3", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23400, "Amount": "445.00", "Status": "Completed", "Type": "bank", "Method": "mx_banorte_ixi_bank", "Rapyd Payout ID": "payout_cb74f6d616d1f947b4b2aa34130c9599", "Employer": "Heritage Farms, LLC", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23399, "Amount": "515.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_7cc648267a3560b05e3187b988d2ea36", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "APOLINAR HERNANDEZ HERNANDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "guadalupe diaz"}, {"Transfer ID": 23398, "Amount": "203.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_ec92910d10dfc48031e64d883bb11b65", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> "}, {"Transfer ID": 23397, "Amount": "76.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_2b76fd324ae78bd9f566f6946583847d", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> azu<PERSON>na Ortega <PERSON>lva"}, {"Transfer ID": 23396, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_feaaa4000dbf1bb8d84aba33558eb278", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "DANIEL VAZQUEZ GARCIA", "Sender User Email": "<EMAIL>", "Recipient Name": "brise<PERSON>  vazquez "}, {"Transfer ID": 23395, "Amount": "70.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_eafb252ca1e4ac9c8b2fd6b1352c4764", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "GUSTAVO ADOLFO ESQUIVEL PASTRANA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>"}, {"Transfer ID": 23394, "Amount": "80.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_7a19e8aa6b645fb8957036b818dafcb8", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>"}, {"Transfer ID": 23393, "Amount": "613.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_501444a8dabafb2788a2bd616d25b2b8", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "VALENTIN MARTINEZ HERNANDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "valentin  martinez"}, {"Transfer ID": 23392, "Amount": "1,000.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_fccb2a0bb1bbde67f830f58d99184092", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "JOSE CARLOS CANO ROSAS", "Sender User Email": "<EMAIL>", "Recipient Name": "paola guadalupe lugo valenzuela "}, {"Transfer ID": 23391, "Amount": "2,530.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_be10062d5e4908ab3b6d6b64ebfa9b2f", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ROBERTO CARLOS ZARATE RAMIREZ", "Sender User Email": "<EMAIL>", "Recipient Name": "maria sepulveda"}, {"Transfer ID": 23390, "Amount": "100.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_232a235ade8efa6a9dc4598b163b572e", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ANTONIO RUBIO SANTOS", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23389, "Amount": "205.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_e705c9d516181966497b744bb8b71bf2", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "DAVID JONATHAN RUIZ FLORES", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23388, "Amount": "700.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_2ccb64f217fe5390befb655ee67fe01d", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "RAUL CRUZ SALVADOR", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>  cruz salvador"}, {"Transfer ID": 23387, "Amount": "26.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_b6b0983dbaca9e915f1533388647e9fd", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "DANIEL LUGO BONILLA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23386, "Amount": "530.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_0d4edae6f8645042ee1577a6ace5aa83", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "MARIO MARTINEZ VAZQUEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "IDALIA NARANJO"}, {"Transfer ID": 23385, "Amount": "35.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_723e0f5ec01f2ec018e4bfcc1445268f", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> con<PERSON> v<PERSON>dez"}, {"Transfer ID": 23384, "Amount": "400.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_e91a73e4946ba89004b5ac8cbbdd7628", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "RODRIGO GABRIEL ACATITLAN", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23382, "Amount": "520.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_aefe06dc7c2c149713e1ee256322bf13", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "Emeter<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>"}, {"Transfer ID": 23381, "Amount": "41.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_b7b4a8f2fbdf0220483f02f360b1fc08", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON><PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>"}, {"Transfer ID": 23380, "Amount": "260.00", "Status": "Completed", "Type": "bank", "Method": "mx_banorte_ixi_bank", "Rapyd Payout ID": "payout_cd54234eafa8f1cf87054cab12153968", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "JOSE JAVIER ALONZO ZARATE", "Sender User Email": "<EMAIL>", "Recipient Name": "j<PERSON><PERSON> "}, {"Transfer ID": 23379, "Amount": "225.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_13be8c9f06ff23e7381ada2f137f2784", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23378, "Amount": "800.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_78f9fe9918ea3550f0579c561cb31f8f", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "MARTIN GUADALUPE MORALES SANTIAGO", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>"}, {"Transfer ID": 23377, "Amount": "27.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_8afe37854b82e80601a4fd55071190db", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "NEMESIO LOPEZ MENDOZA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>"}, {"Transfer ID": 23376, "Amount": "80.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_0eb3790e9ff16ca1314182f4a3cd1036", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "jessica lizbeth ronda guzman"}, {"Transfer ID": 23375, "Amount": "127.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_e13199ee23fda4435ae7144a9432e42e", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ANTONIO MARTINEZ HERNANDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>  mart<PERSON>"}, {"Transfer ID": 23374, "Amount": "44.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_7ee72a403a5287e00d12bceb9515a386", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "EFRAIN RAMIREZ MANCINAS", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> c<PERSON>z"}, {"Transfer ID": 23373, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_f3f5b0c85edd6b1845a4b973e5a31149", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "JOSE ARMANDO LAZARO GASPAR", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>"}, {"Transfer ID": 23372, "Amount": "428.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_1c943a13edde8353b54455fbe8f077a3", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "LUCIO HERNANDEZ JOSE", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON><PERSON> "}, {"Transfer ID": 23371, "Amount": "50.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_686e77ac6606f658f642f5064e001148", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "RUFINO VAZQUEZ LOPEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23370, "Amount": "50.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_ec55363b24a918dc30faa1cfe000b557", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>"}, {"Transfer ID": 23369, "Amount": "150.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_1bf2ad471a0e34e36b618da6ee973c3c", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "HECTOR AGUSTIN MARTINEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "maria chavez"}, {"Transfer ID": 23368, "Amount": "51.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_ea9eeecec938368cec400f2daf806ad4", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>"}, {"Transfer ID": 23367, "Amount": "550.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_962def407b9ed4e31f07e7d08aa0b34e", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "SALOMON SANCHEZ VELASCO", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>"}, {"Transfer ID": 23366, "Amount": "51.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_54bbfbe4552b9af12ff5c643553f7aa0", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "juan <PERSON>  salas cuevas"}, {"Transfer ID": 23365, "Amount": "500.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_e14c8114a753af991526adee3ce265ed", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON> "}, {"Transfer ID": 23364, "Amount": "34.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_07348e43c02bdf1b453d602d5c207dab", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "FRANCISCO ELIGIO FLORES", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>  t<PERSON>"}, {"Transfer ID": 23363, "Amount": "506.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_4c9171a866927d90936e13cf15b2d7e0", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ARMANDO MUNOZ REYES", "Sender User Email": "<EMAIL>", "Recipient Name": "johana itzel gomes cortez"}, {"Transfer ID": 23362, "Amount": "950.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_3051a32c5bde3c40cbaf8c85bfab4fa7", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "ERIC SAMPAYO BAUTISTA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> morales"}, {"Transfer ID": 23361, "Amount": "600.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_306304486f59305a9ee662bbc46b950a", "Employer": " Trevino Ag Services", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>"}, {"Transfer ID": 23360, "Amount": "3,500.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_3b4a5793a69e54934b7427c7419b4ece", "Employer": " Trevino Ag Services", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>"}, {"Transfer ID": 23359, "Amount": "506.00", "Status": "Completed", "Type": "bank", "Method": "mx_banorte_ixi_bank", "Rapyd Payout ID": "payout_2230ec1d32a1efc03980b847936cf8be", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23358, "Amount": "350.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_f4d8bb9144969634aee4828f5705a448", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "maria a<PERSON>cena  go<PERSON>z castro"}, {"Transfer ID": 23357, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_ac68cd32738a53a15939e9b402eade91", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "GILBERTO LOPEZ ANTONIO", "Sender User Email": "<EMAIL>", "Recipient Name": "juan<PERSON>"}, {"Transfer ID": 23356, "Amount": "40.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_c55552a93347d79a6ef1d76d3a60ed26", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23355, "Amount": "1,183.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_884bd71b7e2db8e6b7759a71f12a7bf1", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23354, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_9bfcf98103117d95e6e6dbcac6cc97a0", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> romano "}, {"Transfer ID": 23353, "Amount": "31.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_053cb90b0a9976d74d987334db2cc40c", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "Santiago Tolentino Prieto", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON> veliz"}, {"Transfer ID": 23352, "Amount": "146.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_f2f73c15a7c724489badde4fe90be772", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "EMANUEL HERNANDEZ HERNANDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> b<PERSON> "}, {"Transfer ID": 23351, "Amount": "520.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_a228e288da9e9bb08f6c39a3e3129aab", "Employer": "Huggins Farms - Prefunded", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23350, "Amount": "479.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_e18160426a6f89604346bec2e11522f9", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JESUS MENDOZA CHAVEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "judith luna azteca"}, {"Transfer ID": 23349, "Amount": "588.29", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_43566bc4110ba565ae117e5acbca9d62", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "martina  lezama "}, {"Transfer ID": 23348, "Amount": "1,195.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_c2d73f0f454e522d74a635aa18edbfc3", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "clara anahi herrera reyes"}, {"Transfer ID": 23347, "Amount": "804.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_83a8c4ada95e99f48799981952c59037", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "RAUL CHAVEZ GARCIA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>"}, {"Transfer ID": 23346, "Amount": "304.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_c64a287ce9962a11d809c44cd8a28bee", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23345, "Amount": "520.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_c7e710bb4945bae2d74adfd9d882ff47", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ABRAHAM ZENON SALDANA", "Sender User Email": "<EMAIL>", "Recipient Name": "Eusebio Zenon Nava"}, {"Transfer ID": 23344, "Amount": "150.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_6b153ef173763c8e5938cd4b6aa8e909", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "SALVADOR GARCIA SALDANA", "Sender User Email": "<EMAIL>", "Recipient Name": "Eusebio  Zenón Nava"}, {"Transfer ID": 23343, "Amount": "506.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_ca86100cc1380fddb8e77b0f99e426a1", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "leticia emilia   mart<PERSON> "}, {"Transfer ID": 23342, "Amount": "253.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_3dff03e4bb4d37013ed2638fec4e111c", "Employer": "Heritage Farms, LLC", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "j<PERSON>o gonza<PERSON>"}, {"Transfer ID": 23341, "Amount": "38.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_9292ea0fc4115f3e7acb91889211089b", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23340, "Amount": "300.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_9f73e18beeb7b9b326f6bf5f93906d6b", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "delfina Espinoza santamaria"}, {"Transfer ID": 23339, "Amount": "53.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_9f80578744b85b2b971941b66ea67ed1", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>"}, {"Transfer ID": 23338, "Amount": "1,050.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_b7af73edb8245bd2094073d83caf548f", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "mayra  garcia"}, {"Transfer ID": 23337, "Amount": "41.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_635ef97bf4e7781ea905efa292044364", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "juancar<PERSON>made<PERSON>resend<PERSON>@gmail.com", "Recipient Name": "benito  navarrete nava"}, {"Transfer ID": 23336, "Amount": "885.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_f09879cbdc2bf6457c7b66fbdc653ad7", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "MIGUEL ANGEL NAVA NAVA", "Sender User Email": "<EMAIL>", "Recipient Name": "guadalupe nava"}, {"Transfer ID": 23335, "Amount": "28.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_39bc3c8c845b35d7bd66b1a702f7f899", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "German <PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "EVELIA  GODINEZ GARCIA"}, {"Transfer ID": 23334, "Amount": "30.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_2b6e742433004954a2b24d11c48583e4", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "SERGIO TENTZOHUA SANCHEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23333, "Amount": "26.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_080d427b143c46e71a5e39988f089ddb", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "MARIO ALBERTO HERNANDEZ LLANAS", "Sender User Email": "hernan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23332, "Amount": "30.00", "Status": "Completed", "Type": "bank", "Method": "mx_banorte_ixi_bank", "Rapyd Payout ID": "payout_beb4d19a720a7f7b4c8b52e73a5d41e3", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "SERGIO ALEXIS LEDESMA MENDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> "}, {"Transfer ID": 23331, "Amount": "1,000.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_510b9f5bdf0f2b4423bf4b88aab10ed3", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ANDRES CARDENAS TORRES", "Sender User Email": "<EMAIL>", "Recipient Name": "andres  cardenas "}, {"Transfer ID": 23330, "Amount": "300.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_2c9ce3fb1c86eaa7545188fc90fb4ebc", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "MARTIN JESUS RODRIGUEZ CHAVEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "jessica  barboza"}, {"Transfer ID": 23329, "Amount": "70.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_53dd869f45055ac7e99cef25c3d07a58", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23328, "Amount": "100.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_85bfe932cfee9f1d9a4ac56566c38c7e", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "GUADALUPE VIELMA ORTIZ", "Sender User Email": "<EMAIL>", "Recipient Name": "Guadalupe vielma ortiz"}, {"Transfer ID": 23327, "Amount": "51.00", "Status": "Completed", "Type": "bank", "Method": "mx_scotiabank_bank", "Rapyd Payout ID": "payout_ad6778c57d799b6b77d02c35e9b39553", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JACINTO YAIR GARCIA MOLINA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> "}, {"Transfer ID": 23326, "Amount": "102.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_cb0081f2d0cb40f084bf310b0605f749", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON> Valenzuel<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "ma<PERSON><PERSON> cu<PERSON> Valenzuela"}, {"Transfer ID": 23325, "Amount": "500.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_84bd299f9ff6a78ab99f09bd8859e530", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "NAZARIO TEMOXTLE PANZO", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON> Temox<PERSON> Panzo"}, {"Transfer ID": 23324, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_a3b6b40cb8ce7fda605076fb8a42c2c3", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "alexia <PERSON>"}, {"Transfer ID": 23323, "Amount": "25.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_f346c97b759fb56b241fab9d2cfcd0d5", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "JOSE ALBERTO BETANZO GARCIA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23322, "Amount": "184.00", "Status": "Completed", "Type": "bank", "Method": "mx_santander_bank", "Rapyd Payout ID": "payout_b6a0bc5c61ede0556922743f3699904f", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "irving  <PERSON> "}, {"Transfer ID": 23321, "Amount": "30.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_753c648d0018183d7d9c22ce693d3ecc", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "RIGOBERTO GUERRA HERNANDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "cristina guadalupe villanueva perez"}, {"Transfer ID": 23320, "Amount": "530.65", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_a2a1bddb9215a1c1d116aaf4a66b0ede", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23319, "Amount": "230.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_098c39a5f4b78bfa5b84255df3bad4f5", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "PEDRO APARICIO GONZALEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>"}, {"Transfer ID": 23318, "Amount": "60.65", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_9392267e59ac5301c8402c23b8dd9c48", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JESUS ALEJANDRO AVALOS RIVERA", "Sender User Email": "jesus<PERSON><EMAIL>", "Recipient Name": "juana baez "}, {"Transfer ID": 23317, "Amount": "169.00", "Status": "Completed", "Type": "bank", "Method": "mx_hsbc_bank", "Rapyd Payout ID": "payout_ef585f5bbe152fdf98d3f3e0e2a6b3c7", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "RICARDO DE JESUS HERNANDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23316, "Amount": "1,500.00", "Status": "Completed", "Type": "bank", "Method": "mx_banamex_bank", "Rapyd Payout ID": "payout_5d2f69ab137b4ba31e81254db8e94bd4", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "CARLOS MANUEL EDISON VALLE", "Sender User Email": "<EMAIL>", "Recipient Name": "LUIS MIGUEL TAMARIZ VALLE"}, {"Transfer ID": 23315, "Amount": "169.00", "Status": "Completed", "Type": "bank", "Method": "mx_banamex_bank", "Rapyd Payout ID": "payout_0b6d399c3ffc4561c9f183d4032efab3", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "DANIEL VELAZQUEZ VICTORIANO", "Sender User Email": "victoria<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23314, "Amount": "169.00", "Status": "Completed", "Type": "bank", "Method": "mx_hsbc_bank", "Rapyd Payout ID": "payout_f032f9876c87a9743c35c6e382a0849b", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "RICARDO DE JESUS HERNANDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23313, "Amount": "600.00", "Status": "Completed", "Type": "bank", "Method": "mx_caja_pop_mexico_bank", "Rapyd Payout ID": "payout_624a559a5f80b1d4d2664714bf77b2a1", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>"}, {"Transfer ID": 23312, "Amount": "105.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_3e046b7cd88801f3d694f7cb53a35af8", "Employer": "Huggins Farms - Prefunded", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23311, "Amount": "900.00", "Status": "Completed", "Type": "bank", "Method": "mx_banorte_ixi_bank", "Rapyd Payout ID": "payout_e1ceb212270ff26a2b36672e0687e7ac", "Employer": "AgEmpleo", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "federico medina reyes"}, {"Transfer ID": 23310, "Amount": "1,250.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_d6da141604c441a54e152fca0d1d0bde", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JORGE LUIS DE LEON SUSTAITA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>"}, {"Transfer ID": 23309, "Amount": "300.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_b634b46621d1d90d1e189c8bd4426da6", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ARMANDO GRAJEDA ZARATE", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>a fuentes"}, {"Transfer ID": 23308, "Amount": "26.00", "Status": "Completed", "Type": "bank", "Method": "mx_hsbc_bank", "Rapyd Payout ID": "payout_fc617f3830a5830048b8ada8e26566f3", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JESUS EMANUEL DIAZ LOPEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "oscar la<PERSON> lo<PERSON>z"}, {"Transfer ID": 23307, "Amount": "506.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_6c419c97454d742e3eea198ee62dc7a9", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "CESAR ANTONIO AVALOS HERNANDEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "perla melgoza"}, {"Transfer ID": 23306, "Amount": "264.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_fa6e510b6de07ad72a5e0beaecd25e45", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "CARLOS ALBERTO MAGANA IBARRA", "Sender User Email": "karloz<PERSON><PERSON>@gmail.com", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23305, "Amount": "800.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_6c5d8793b0665c3b9b64e723e6f28829", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23304, "Amount": "500.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_d3781acd9adbdf5278333c75413c1e67", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "ULISES CRUZ VELASCO", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23303, "Amount": "300.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_37b479e5cf817b179fe398ac066ba02c", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>"}, {"Transfer ID": 23302, "Amount": "80.00", "Status": "Completed", "Type": "bank", "Method": "mx_banrejio_bank", "Rapyd Payout ID": "payout_87850ccad63b77079fa145bcb8ddd7ad", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JAVIER DE LOS SANTOS SALAZAR", "Sender User Email": "delossantossala<PERSON><PERSON><EMAIL>", "Recipient Name": "javier ortiz <PERSON>az "}, {"Transfer ID": 23301, "Amount": "900.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_79a3e4783328551c7d70bba5a7d23919", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>"}, {"Transfer ID": 23300, "Amount": "152.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_23e6503c57c1cc68d01d29e031f46f3e", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "FELIPE JUAREZ MARTINEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>"}, {"Transfer ID": 23299, "Amount": "51.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_1b49144f1544e6e03ccf36514bfc7557", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JOSE REFUGIO RANGEL VAZQUEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON>"}, {"Transfer ID": 23298, "Amount": "397.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_2db73c5fe2dada3f9378115d69fcb762", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23297, "Amount": "1,040.00", "Status": "Completed", "Type": "bank", "Method": "mx_bansefi_bank", "Rapyd Payout ID": "payout_2d949868c638386cfdf3b9c3f52b54c5", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON> "}, {"Transfer ID": 23296, "Amount": "105.00", "Status": "Completed", "Type": "bank", "Method": "mx_banamex_bank", "Rapyd Payout ID": "payout_d681520660c38caf5ec402e4ca5697cf", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "JESUS DANIEL NANDO ZURITA", "Sender User Email": "<EMAIL>", "Recipient Name": "YOMARA ARELLANO MARIN"}, {"Transfer ID": 23295, "Amount": "167.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_c9b188b941191dfa5349ee70d534fd74", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> antonio amador"}, {"Transfer ID": 23294, "Amount": "710.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_de0c07531ecbda3a4f3349a298273b70", "Employer": "Coastal Farm Labor", "Sender User ID": *********, "Sender User Name": "<PERSON><PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23293, "Amount": "410.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_ac68519e4c8e90b89e217a7e4fcfab1e", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23292, "Amount": "102.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_f3da3bb39d13a562c11441f278d75877", "Employer": "Heritage Farms, LLC", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON><PERSON><PERSON>"}, {"Transfer ID": 23291, "Amount": "600.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_ce756658db7763a45df6a939239ab62d", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "CRESTINO NAVA NAVA", "Sender User Email": "<EMAIL>", "Recipient Name": "carmen <PERSON>"}, {"Transfer ID": 23290, "Amount": "50.00", "Status": "Completed", "Type": "bank", "Method": "mx_hsbc_bank", "Rapyd Payout ID": "payout_d15d04735bde713fe40ddf27fd077843", "Employer": "Farm Labor Association for Growers, Inc", "Sender User ID": *********, "Sender User Name": "DIEGO GARCIA GARCIA", "Sender User Email": "<EMAIL>", "Recipient Name": "diego  garcia "}, {"Transfer ID": 23289, "Amount": "90.00", "Status": "Completed", "Type": "bank", "Method": "mx_bbva_bancomer_bank", "Rapyd Payout ID": "payout_798d0d7b3fc8b418ef5331c27fbecc87", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "Salvador Alejandro Rio<PERSON> Flores", "Sender User Email": "<EMAIL>", "Recipient Name": "marlen sanchez"}, {"Transfer ID": 23288, "Amount": "26.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_8f69e3d98ff109face308fb64ea19957", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "ERIK ALAN MARTINEZ GAMEZ", "Sender User Email": "<EMAIL>", "Recipient Name": "Alba martinez"}, {"Transfer ID": 23287, "Amount": "510.00", "Status": "Completed", "Type": "bank", "Method": "mx_azteca_bank", "Rapyd Payout ID": "payout_8664f7565921d5512ba497bebc6e9481", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "DANIEL LUGO BONILLA", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON>"}, {"Transfer ID": 23286, "Amount": "500.00", "Status": "Completed", "Type": "bank", "Method": "mx_afirme_bank", "Rapyd Payout ID": "payout_56257def4d59899a7ee5d52f9621d161", "Employer": " Ashley Furniture Industries", "Sender User ID": *********, "Sender User Name": "<PERSON>", "Sender User Email": "<EMAIL>", "Recipient Name": "<PERSON> "}, {"Transfer ID": 23285, "Amount": "50.00", "Status": "Completed", "Type": "bank", "Method": "mx_bancoppel_bank", "Rapyd Payout ID": "payout_3e32578f2c69dec15d33196e62dc8c10", "Employer": "Fresh Harvest - Prefunded", "Sender User ID": *********, "Sender User Name": "DANIEL GARCIA AGUILAR", "Sender User Email": "<EMAIL>", "Recipient Name": "Baudelia <PERSON>"}, {"Transfer ID": 26452, "Amount": "0.10", "Status": "Completed", "Comment": "By <PERSON> for <PERSON><PERSON>'s idempotency support"}]