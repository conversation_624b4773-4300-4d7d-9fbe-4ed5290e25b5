CREATE TABLE users (id INT AUTO_INCREMENT NOT NULL, username VA<PERSON><PERSON><PERSON>(180) NOT NULL, username_canonical VARCHAR(180) NOT NULL, email VARCHAR(180) NOT NULL, email_canonical VARCHAR(180) NOT NULL, enabled TINYINT(1) NOT NULL, salt VARCHAR(255) DEFAULT NULL, password VARCHAR(255) NOT NULL, last_login DATETIME DEFAULT NULL, confirmation_token VARCHAR(180) DEFAULT NULL, password_requested_at DATETIME DEFAULT NULL, roles LONGTEXT NOT NULL COMMENT '(DC2Type:array)', first_name VA<PERSON>HAR(255) DEFAULT NULL, last_name <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT NULL, profile_picture VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, flagsname <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT NULL, UNIQUE INDEX UNIQ_1483A5E992FC23A8 (username_canonical), UNIQUE INDEX UNIQ_1483A5E9A0D96FBF (email_canonical), UNIQUE INDEX UNIQ_1483A5E9C05FB297 (confirmation_token), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE user_role (user_id INT NOT NULL, role_id INT NOT NULL, INDEX IDX_2DE8C6A3A76ED395 (user_id), INDEX IDX_2DE8C6A3D60322AC (role_id), PRIMARY KEY(user_id, role_id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE api (id INT AUTO_INCREMENT NOT NULL, role_id INT DEFAULT NULL, api_category_name VARCHAR(32) NOT NULL, UNIQUE INDEX UNIQ_AD05D80F7DD5B1CD (api_category_name), INDEX IDX_AD05D80FD60322AC (role_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE brand_partner (id INT AUTO_INCREMENT NOT NULL, contact_id INT DEFAULT NULL, user_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, address LONGTEXT NOT NULL, brand_logo VARCHAR(255) DEFAULT NULL, brand_css VARCHAR(255) DEFAULT NULL, email_from VARCHAR(255) NOT NULL, portal_access TINYINT(1) NOT NULL, UNIQUE INDEX UNIQ_D6109B6DE7A1254A (contact_id), INDEX IDX_D6109B6DA76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE card_program (id INT AUTO_INCREMENT NOT NULL, brand_partner_id INT DEFAULT NULL, marketing_partner_id INT DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, is_co_brand TINYINT(1) NOT NULL, is_portal_use TINYINT(1) NOT NULL, has_cust_address TINYINT(1) NOT NULL, status VARCHAR(255) NOT NULL, UNIQUE INDEX UNIQ_C5F7FDF25E237E06 (name), INDEX IDX_C5F7FDF2A94CDE20 (brand_partner_id), INDEX IDX_C5F7FDF2FE0EBEB2 (marketing_partner_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE card_program_card_type (id INT AUTO_INCREMENT NOT NULL, card_type_id INT DEFAULT NULL, card_program_id INT DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, cust_name VARCHAR(255) NOT NULL, is_additional TINYINT(1) NOT NULL, maxBalance NUMERIC(10, 2) DEFAULT NULL, maxLoad NUMERIC(10, 2) DEFAULT NULL, max_load_freq_per_day LONGTEXT DEFAULT NULL COMMENT '(DC2Type:object)', id_verification_requirements LONGTEXT DEFAULT NULL COMMENT '(DC2Type:simple_array)', minLoad NUMERIC(10, 2) DEFAULT NULL, is_auto_created TINYINT(1) NOT NULL, cards_allowed INT NOT NULL, INDEX IDX_35FA2180925606E5 (card_type_id), INDEX IDX_35FA218052CA9A04 (card_program_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE card_program_country (id INT AUTO_INCREMENT NOT NULL, country_id INT DEFAULT NULL, card_program_id INT DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, INDEX IDX_B6BE2E3FF92F3E70 (country_id), INDEX IDX_B6BE2E3F52CA9A04 (card_program_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE card_program_fee_item (id INT AUTO_INCREMENT NOT NULL, fee_item_id INT DEFAULT NULL, card_program_id INT DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, fee_to_customer_fixed INT DEFAULT NULL, fee_to_customer_ratio NUMERIC(2, 0) DEFAULT NULL, fee_to_bp_fixed INT DEFAULT NULL, fee_to_bp_ratio NUMERIC(2, 0) DEFAULT NULL, INDEX IDX_645F0D51F1D98A02 (fee_item_id), INDEX IDX_645F0D5152CA9A04 (card_program_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE card_program_fee_revenue_share (id INT AUTO_INCREMENT NOT NULL, fee_item_id INT DEFAULT NULL, card_program_id INT DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, INDEX IDX_C30EAC69F1D98A02 (fee_item_id), INDEX IDX_C30EAC6952CA9A04 (card_program_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE card_program_kyc_provider (id INT AUTO_INCREMENT NOT NULL, kyc_provider_id INT DEFAULT NULL, card_program_id INT DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, INDEX IDX_37229BF9322C654 (kyc_provider_id), INDEX IDX_37229BF52CA9A04 (card_program_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE card_program_reshipper (id INT AUTO_INCREMENT NOT NULL, reshipper_id INT DEFAULT NULL, card_program_id INT DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, INDEX IDX_C10A30AA49E8142B (reshipper_id), INDEX IDX_C10A30AA52CA9A04 (card_program_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE card_program_service (id INT AUTO_INCREMENT NOT NULL, service_id INT DEFAULT NULL, card_program_id INT DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, INDEX IDX_4507D8BED5CA9E6 (service_id), INDEX IDX_4507D8B52CA9A04 (card_program_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE card_type (id INT AUTO_INCREMENT NOT NULL, processor_id INT DEFAULT NULL, issuing_bank_id INT DEFAULT NULL, program_manager_id INT DEFAULT NULL, cur_code VARCHAR(3) DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, maxBalance NUMERIC(10, 2) DEFAULT NULL, maxLoad NUMERIC(10, 2) DEFAULT NULL, max_load_freq_per_day LONGTEXT DEFAULT NULL COMMENT '(DC2Type:object)', id_verification_requirements LONGTEXT DEFAULT NULL COMMENT '(DC2Type:simple_array)', minLoad NUMERIC(10, 2) DEFAULT NULL, is_auto_created TINYINT(1) NOT NULL, cards_allowed INT NOT NULL, expiration_date DATETIME NOT NULL, UNIQUE INDEX UNIQ_60ED558B5E237E06 (name), INDEX IDX_60ED558B37BAC19A (processor_id), INDEX IDX_60ED558B1251941D (issuing_bank_id), INDEX IDX_60ED558B6E04C9D7 (program_manager_id), INDEX IDX_60ED558B2FEE9CE9 (cur_code), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE contact (id INT AUTO_INCREMENT NOT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, title VARCHAR(255) NOT NULL, first_name VARCHAR(32) NOT NULL, last_name VARCHAR(32) NOT NULL, email VARCHAR(255) NOT NULL, phone VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE country (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(225) NOT NULL, iso_code VARCHAR(10) DEFAULT NULL, iso3_code VARCHAR(3) NOT NULL, phone_code VARCHAR(6) NOT NULL, in_country_phone_prefix VARCHAR(6) NOT NULL, twilio_call TINYINT(1) NOT NULL, status TINYINT(1) NOT NULL, id_status TINYINT(1) NOT NULL, continent VARCHAR(255) NOT NULL, region VARCHAR(255) NOT NULL, currency VARCHAR(3) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE country_kyc_ids (country_id INT NOT NULL, kyc_provider_id INT NOT NULL, identity_type VARCHAR(255) NOT NULL, image_requirement VARCHAR(255) DEFAULT NULL, enabled TINYINT(1) NOT NULL, INDEX IDX_33DBAFE6F92F3E70 (country_id), INDEX IDX_33DBAFE69322C654 (kyc_provider_id), PRIMARY KEY(country_id, kyc_provider_id, identity_type)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE country_load_partner_method_currency (country_id INT NOT NULL, load_partner_id INT NOT NULL, load_method_id INT NOT NULL, currencies VARCHAR(255) DEFAULT NULL, enabled TINYINT(1) NOT NULL, INDEX IDX_B6795740F92F3E70 (country_id), INDEX IDX_B6795740693F39F9 (load_partner_id), INDEX IDX_B6795740BA745D79 (load_method_id), PRIMARY KEY(country_id, load_partner_id, load_method_id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE currency (cur_code VARCHAR(3) NOT NULL, country VARCHAR(255) NOT NULL, one_f DOUBLE PRECISION NOT NULL, one_usd DOUBLE PRECISION NOT NULL, markup DOUBLE PRECISION NOT NULL, last_update DATETIME NOT NULL, PRIMARY KEY(cur_code)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE fee_category (id INT AUTO_INCREMENT NOT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE fee_global_name (id INT AUTO_INCREMENT NOT NULL, fee_category_id INT DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, INDEX IDX_D6A920049B9BF38C (fee_category_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE fee_item (id INT AUTO_INCREMENT NOT NULL, fee_global_name_id INT DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, fee_name VARCHAR(255) NOT NULL, tran_code VARCHAR(255) NOT NULL, fee_entity_type VARCHAR(255) DEFAULT NULL, fee_entity_id INT DEFAULT NULL, applied_by VARCHAR(255) NOT NULL, measure VARCHAR(255) NOT NULL, max_fee_fixed INT NOT NULL, max_fee_ratio NUMERIC(2, 0) NOT NULL, cost_fixed INT DEFAULT NULL, cost_ratio NUMERIC(2, 0) DEFAULT NULL, INDEX IDX_760D29E65BC652D4 (fee_global_name_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE issuing_bank (id INT AUTO_INCREMENT NOT NULL, user_id INT DEFAULT NULL, contact_id INT DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, name VARCHAR(32) NOT NULL, address LONGTEXT NOT NULL, access_flag TINYINT(1) NOT NULL, INDEX IDX_4C1F97B3A76ED395 (user_id), UNIQUE INDEX UNIQ_4C1F97B3E7A1254A (contact_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE kyc_provider (id INT AUTO_INCREMENT NOT NULL, contact_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, integration_provider_id VARCHAR(255) NOT NULL, UNIQUE INDEX UNIQ_C7A7FF3EE7A1254A (contact_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE load_method (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE load_partner (id INT AUTO_INCREMENT NOT NULL, contact_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, address LONGTEXT NOT NULL, tenant_provider_id VARCHAR(255) NOT NULL, UNIQUE INDEX UNIQ_8A4A3AFCE7A1254A (contact_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE load_partner_method (load_partner_id INT NOT NULL, load_method_id INT NOT NULL, INDEX IDX_31F2BF32693F39F9 (load_partner_id), INDEX IDX_31F2BF32BA745D79 (load_method_id), PRIMARY KEY(load_partner_id, load_method_id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE marketing_partner (id INT AUTO_INCREMENT NOT NULL, contact_id INT DEFAULT NULL, user_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, address LONGTEXT NOT NULL, brand_logo VARCHAR(255) DEFAULT NULL, brand_css VARCHAR(255) DEFAULT NULL, portal_access TINYINT(1) NOT NULL, UNIQUE INDEX UNIQ_9AC66599E7A1254A (contact_id), INDEX IDX_9AC66599A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE notes (id INT AUTO_INCREMENT NOT NULL, toname VARCHAR(32) NOT NULL, fromname VARCHAR(32) NOT NULL, notes VARCHAR(255) NOT NULL, createdtime DATETIME NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE processor (id INT AUTO_INCREMENT NOT NULL, user_id INT DEFAULT NULL, contact_id INT DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, name VARCHAR(32) NOT NULL, address LONGTEXT NOT NULL, access_flag TINYINT(1) NOT NULL, is_program_manager TINYINT(1) NOT NULL, program_manager_id INT DEFAULT NULL, INDEX IDX_29C04650A76ED395 (user_id), UNIQUE INDEX UNIQ_29C04650E7A1254A (contact_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE program_manager (id INT AUTO_INCREMENT NOT NULL, contact_id INT DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, name VARCHAR(32) NOT NULL, address LONGTEXT NOT NULL, is_processor TINYINT(1) NOT NULL, processor_id INT DEFAULT NULL, UNIQUE INDEX UNIQ_71CEDD6DE7A1254A (contact_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE reshipper (id INT AUTO_INCREMENT NOT NULL, contact_id INT DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, name VARCHAR(32) NOT NULL, dba VARCHAR(32) NOT NULL, address VARCHAR(200) NOT NULL, api_integration_id VARCHAR(40) NOT NULL, auto_signup TINYINT(1) NOT NULL, account_validation TINYINT(1) NOT NULL, UNIQUE INDEX UNIQ_941D44A1E7A1254A (contact_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE reshipper_address (id INT AUTO_INCREMENT NOT NULL, reshipper_id INT DEFAULT NULL, country_id INT DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, address_type_name VARCHAR(20) NOT NULL, default_address VARCHAR(200) NOT NULL, second_address VARCHAR(200) NOT NULL, state VARCHAR(255) NOT NULL, city VARCHAR(255) NOT NULL, zip_code VARCHAR(255) NOT NULL, phone VARCHAR(20) NOT NULL, INDEX IDX_346BA81149E8142B (reshipper_id), INDEX IDX_346BA811F92F3E70 (country_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE role (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(32) NOT NULL, authname VARCHAR(2555) NOT NULL, UNIQUE INDEX UNIQ_57698A6A5E237E06 (name), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE section (id INT AUTO_INCREMENT NOT NULL, role_id INT DEFAULT NULL, section_name VARCHAR(32) NOT NULL, UNIQUE INDEX UNIQ_2D737AEFDB9D920D (section_name), INDEX IDX_2D737AEFD60322AC (role_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE service_manager (id INT AUTO_INCREMENT NOT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, UNIQUE INDEX UNIQ_35A077255E237E06 (name), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
ALTER TABLE user_role ADD CONSTRAINT FK_2DE8C6A3A76ED395 FOREIGN KEY (user_id) REFERENCES users (id);
ALTER TABLE user_role ADD CONSTRAINT FK_2DE8C6A3D60322AC FOREIGN KEY (role_id) REFERENCES role (id);
ALTER TABLE api ADD CONSTRAINT FK_AD05D80FD60322AC FOREIGN KEY (role_id) REFERENCES role (id);
ALTER TABLE brand_partner ADD CONSTRAINT FK_D6109B6DE7A1254A FOREIGN KEY (contact_id) REFERENCES contact (id);
ALTER TABLE brand_partner ADD CONSTRAINT FK_D6109B6DA76ED395 FOREIGN KEY (user_id) REFERENCES users (id);
ALTER TABLE card_program ADD CONSTRAINT FK_C5F7FDF2A94CDE20 FOREIGN KEY (brand_partner_id) REFERENCES brand_partner (id);
ALTER TABLE card_program ADD CONSTRAINT FK_C5F7FDF2FE0EBEB2 FOREIGN KEY (marketing_partner_id) REFERENCES marketing_partner (id);
ALTER TABLE card_program_card_type ADD CONSTRAINT FK_35FA2180925606E5 FOREIGN KEY (card_type_id) REFERENCES card_type (id);
ALTER TABLE card_program_card_type ADD CONSTRAINT FK_35FA218052CA9A04 FOREIGN KEY (card_program_id) REFERENCES card_program (id);
ALTER TABLE card_program_country ADD CONSTRAINT FK_B6BE2E3FF92F3E70 FOREIGN KEY (country_id) REFERENCES country (id);
ALTER TABLE card_program_country ADD CONSTRAINT FK_B6BE2E3F52CA9A04 FOREIGN KEY (card_program_id) REFERENCES card_program (id);
ALTER TABLE card_program_fee_item ADD CONSTRAINT FK_645F0D51F1D98A02 FOREIGN KEY (fee_item_id) REFERENCES fee_item (id);
ALTER TABLE card_program_fee_item ADD CONSTRAINT FK_645F0D5152CA9A04 FOREIGN KEY (card_program_id) REFERENCES card_program (id);
ALTER TABLE card_program_fee_revenue_share ADD CONSTRAINT FK_C30EAC69F1D98A02 FOREIGN KEY (fee_item_id) REFERENCES fee_item (id);
ALTER TABLE card_program_fee_revenue_share ADD CONSTRAINT FK_C30EAC6952CA9A04 FOREIGN KEY (card_program_id) REFERENCES card_program (id);
ALTER TABLE card_program_kyc_provider ADD CONSTRAINT FK_37229BF9322C654 FOREIGN KEY (kyc_provider_id) REFERENCES kyc_provider (id);
ALTER TABLE card_program_kyc_provider ADD CONSTRAINT FK_37229BF52CA9A04 FOREIGN KEY (card_program_id) REFERENCES card_program (id);
ALTER TABLE card_program_reshipper ADD CONSTRAINT FK_C10A30AA49E8142B FOREIGN KEY (reshipper_id) REFERENCES reshipper (id);
ALTER TABLE card_program_reshipper ADD CONSTRAINT FK_C10A30AA52CA9A04 FOREIGN KEY (card_program_id) REFERENCES card_program (id);
ALTER TABLE card_program_service ADD CONSTRAINT FK_4507D8BED5CA9E6 FOREIGN KEY (service_id) REFERENCES service_manager (id);
ALTER TABLE card_program_service ADD CONSTRAINT FK_4507D8B52CA9A04 FOREIGN KEY (card_program_id) REFERENCES card_program (id);
ALTER TABLE card_type ADD CONSTRAINT FK_60ED558B37BAC19A FOREIGN KEY (processor_id) REFERENCES processor (id);
ALTER TABLE card_type ADD CONSTRAINT FK_60ED558B1251941D FOREIGN KEY (issuing_bank_id) REFERENCES issuing_bank (id);
ALTER TABLE card_type ADD CONSTRAINT FK_60ED558B6E04C9D7 FOREIGN KEY (program_manager_id) REFERENCES program_manager (id);
ALTER TABLE card_type ADD CONSTRAINT FK_60ED558B2FEE9CE9 FOREIGN KEY (cur_code) REFERENCES currency (cur_code);
ALTER TABLE country_kyc_ids ADD CONSTRAINT FK_33DBAFE6F92F3E70 FOREIGN KEY (country_id) REFERENCES country (id);
ALTER TABLE country_kyc_ids ADD CONSTRAINT FK_33DBAFE69322C654 FOREIGN KEY (kyc_provider_id) REFERENCES kyc_provider (id);
ALTER TABLE country_load_partner_method_currency ADD CONSTRAINT FK_B6795740F92F3E70 FOREIGN KEY (country_id) REFERENCES country (id);
ALTER TABLE country_load_partner_method_currency ADD CONSTRAINT FK_B6795740693F39F9 FOREIGN KEY (load_partner_id) REFERENCES load_partner (id);
ALTER TABLE country_load_partner_method_currency ADD CONSTRAINT FK_B6795740BA745D79 FOREIGN KEY (load_method_id) REFERENCES load_method (id);
ALTER TABLE fee_global_name ADD CONSTRAINT FK_D6A920049B9BF38C FOREIGN KEY (fee_category_id) REFERENCES fee_category (id);
ALTER TABLE fee_item ADD CONSTRAINT FK_760D29E65BC652D4 FOREIGN KEY (fee_global_name_id) REFERENCES fee_global_name (id);
ALTER TABLE issuing_bank ADD CONSTRAINT FK_4C1F97B3A76ED395 FOREIGN KEY (user_id) REFERENCES users (id);
ALTER TABLE issuing_bank ADD CONSTRAINT FK_4C1F97B3E7A1254A FOREIGN KEY (contact_id) REFERENCES contact (id);
ALTER TABLE kyc_provider ADD CONSTRAINT FK_C7A7FF3EE7A1254A FOREIGN KEY (contact_id) REFERENCES contact (id);
ALTER TABLE load_partner ADD CONSTRAINT FK_8A4A3AFCE7A1254A FOREIGN KEY (contact_id) REFERENCES contact (id);
ALTER TABLE load_partner_method ADD CONSTRAINT FK_31F2BF32693F39F9 FOREIGN KEY (load_partner_id) REFERENCES load_partner (id);
ALTER TABLE load_partner_method ADD CONSTRAINT FK_31F2BF32BA745D79 FOREIGN KEY (load_method_id) REFERENCES load_method (id);
ALTER TABLE marketing_partner ADD CONSTRAINT FK_9AC66599E7A1254A FOREIGN KEY (contact_id) REFERENCES contact (id);
ALTER TABLE marketing_partner ADD CONSTRAINT FK_9AC66599A76ED395 FOREIGN KEY (user_id) REFERENCES users (id);
ALTER TABLE processor ADD CONSTRAINT FK_29C04650A76ED395 FOREIGN KEY (user_id) REFERENCES users (id);
ALTER TABLE processor ADD CONSTRAINT FK_29C04650E7A1254A FOREIGN KEY (contact_id) REFERENCES contact (id);
ALTER TABLE program_manager ADD CONSTRAINT FK_71CEDD6DE7A1254A FOREIGN KEY (contact_id) REFERENCES contact (id);
ALTER TABLE reshipper ADD CONSTRAINT FK_941D44A1E7A1254A FOREIGN KEY (contact_id) REFERENCES contact (id);
ALTER TABLE reshipper_address ADD CONSTRAINT FK_346BA81149E8142B FOREIGN KEY (reshipper_id) REFERENCES reshipper (id);
ALTER TABLE reshipper_address ADD CONSTRAINT FK_346BA811F92F3E70 FOREIGN KEY (country_id) REFERENCES country (id);
ALTER TABLE section ADD CONSTRAINT FK_2D737AEFD60322AC FOREIGN KEY (role_id) REFERENCES role (id);
