DROP TABLE IF EXISTS card_program_country;
CREATE TABLE card_program_country (
	card_program_id INT NOT NULL, 
	country_id INT NOT NULL, 
	INDEX IDX_B6BE2E3F52CA9A04 (card_program_id), 
	INDEX IDX_B6BE2E3FF92F3E70 (country_id), 
	<PERSON><PERSON>AR<PERSON> KEY(card_program_id, country_id)) 
DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
ALTER TABLE card_program_country ADD CONSTRAINT FK_B6BE2E3F52CA9A04 FOREIGN KEY (card_program_id) REFERENCES card_program (id);
ALTER TABLE card_program_country ADD CONSTRAINT FK_B6BE2E3FF92F3E70 FOREIGN KEY (country_id) REFERENCES country (id);