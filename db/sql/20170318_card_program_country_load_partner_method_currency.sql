CREATE TABLE card_program_country_load_partner_method_currency (
	card_program_id INT NOT NULL, 
	country_id INT NOT NULL, 
	load_partner_id INT NOT NULL, 
	load_method_id INT NOT NULL, 
	currencies VARCHAR(255) DEFAULT NULL, 
	enabled TINYINT(1) NOT NULL, 
	INDEX IDX_2B75130B52CA9A04 (card_program_id), 
	INDEX IDX_2B75130BF92F3E70 (country_id), 
	INDEX IDX_2B75130B693F39F9 (load_partner_id), 
	INDEX IDX_2B75130BBA745D79 (load_method_id), 
	PRIMARY KEY(card_program_id, country_id, load_partner_id, load_method_id)
) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
ALTER TABLE card_program_country_load_partner_method_currency ADD CONSTRAINT FK_2B75130B52CA9A04 FOREIGN KEY (card_program_id) REFERENCES card_program (id);
ALTER TABLE card_program_country_load_partner_method_currency ADD CONSTRAINT FK_2B75130BF92F3E70 FOREIGN KEY (country_id) REFERENCES country (id);
ALTER TABLE card_program_country_load_partner_method_currency ADD CONSTRAINT FK_2B75130B693F39F9 FOREIGN KEY (load_partner_id) REFERENCES load_partner (id);
ALTER TABLE card_program_country_load_partner_method_currency ADD CONSTRAINT FK_2B75130BBA745D79 FOREIGN KEY (load_method_id) REFERENCES load_method (id);