CREATE TABLE card_program_country_kyc_ids (
	card_program_id INT NOT NULL, 
	country_id INT NOT NULL, 
	kyc_provider_id INT NOT NULL, 
	identity_type VARCHAR(255) NOT NULL, 
	image_requirement VARCHAR(255) DEFAULT NULL, 
	enabled TINYINT(1) NOT NULL, 
	INDEX IDX_F2F79A5752CA9A04 (card_program_id), 
	INDEX IDX_F2F79A57F92F3E70 (country_id), 
	INDEX IDX_F2F79A579322C654 (kyc_provider_id), 
	PRIMARY KEY(card_program_id, country_id, kyc_provider_id, identity_type)
) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
ALTER TABLE card_program_country_kyc_ids ADD CONSTRAINT FK_F2F79A5752CA9A04 FOREIGN KEY (card_program_id) REFERENCES card_program (id);
ALTER TABLE card_program_country_kyc_ids ADD CONSTRAINT FK_F2F79A57F92F3E70 FOREIGN KEY (country_id) REFERENCES country (id);
ALTER TABLE card_program_country_kyc_ids ADD CONSTRAINT FK_F2F79A579322C654 FOREIGN KEY (kyc_provider_id) REFERENCES kyc_provider (id);