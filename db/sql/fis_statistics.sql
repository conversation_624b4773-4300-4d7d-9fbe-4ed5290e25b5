/* Balance Statistics */
SET sql_notes = 0;
CREATE TABLE IF NOT EXISTS fis_statistics_balance (
  id int auto_increment primary key,
  hash_key NVARCHAR(500) NOT NULL COMMENT 'Unique identifier for this entry',
  context NVARCHAR(100) NOT NULL COMMENT 'Context for this specific record',

  file_processor_name NVARCHAR(3) COMMENT 'DPP or PPM',
  work_of_date DATE COMMENT 'Date of Transaction',
  bank_name NVARCHAR(255) COMMENT 'Name of the Bank',
  issuer_bank_id INT COMMENT 'Identity of Bank',
  client_name NVARCHAR(255) COMMENT 'Name of the Client',
  issuer_client_id INT COMMENT 'Identity of Client',
  bin_currency_alpha NVARCHAR(5) COMMENT 'Currency Code',
  bin INT NULL COMMENT 'Bin',
  sub_program_id INT NOT NULL COMMENT 'Subprogram Identifier',

  /* Portfolio Report */
  opening_balance_total DECIMAL(19,4) COMMENT 'Opening Balance Total',
  opening_balance_average DECIMAL(19,4) COMMENT 'Opening Balance Average',
  closing_balance_total DECIMAL(19,4) COMMENT 'Closing Balance Total',
  closing_balance_average DECIMAL(19,4) COMMENT 'Closing Balance Average',
  pan_count INT COMMENT 'Number of Unique PANs',
  record_count INT COMMENT 'Number of Unique Records',

  /* Load Report */
  total_value_load_amount DECIMAL(19,4) COMMENT 'Total Value Load Amount',
  total_value_load_count INT COMMENT 'Total Value Load Count',
  pan_proxy_number_count INT COMMENT 'Pan Proxy Number Count',

  /* Fees Report */
  total_fees_amount DECIMAL(19,4) COMMENT 'Total Amount of Fees',
  total_fees_count INT COMMENT 'Total Number of Fees',

  /* Usage Report */
  total_credit_amount DECIMAL(19,4) COMMENT 'Total Amount Credited',
  total_debit_amount DECIMAL(19,4) COMMENT 'Total Amount Debited'
)

COMMENT 'FIS Reporting Aggregate Values for Balances' COLLATE=utf8_unicode_ci;
SET sql_notes = 1;

CREATE INDEX md5_hash_key_idx ON fis_statistics_balance (hash_key);
CREATE INDEX context_idx ON fis_statistics_balance (context);
CREATE INDEX bin_idx ON fis_statistics_balance (bin);



/* Dispute Statistics */
SET sql_notes = 0;
CREATE TABLE IF NOT EXISTS fis_statistics_dispute (
  id int auto_increment primary key,
  hash_key NVARCHAR(255) COMMENT 'Unique identifier for this entry',
  context NVARCHAR(100) NOT NULL COMMENT 'Context for this specific record',
  file_processor_name NVARCHAR(3) COMMENT 'DPP or PPM',
  work_of_date DATE COMMENT 'Date of Transaction',
  bank_name NVARCHAR(255) COMMENT 'Name of the Bank',
  client_name NVARCHAR(255) COMMENT 'Name of the Client',
  issuer_client_id INT NULL COMMENT 'Identity of Client',
  bin_currency_alpha NVARCHAR(5) COMMENT 'Currency Code',
  bin INT NULL COMMENT 'Bin',
  sub_program_id INT NOT NULL COMMENT 'Subprogram Identifier',
  pan_count INT COMMENT 'Number of Unique PANs',
  record_count INT COMMENT 'Number of Unique Records',
  dispute_status NVARCHAR(255) NULL COMMENT 'Status of Dispute',
  disputed_amount DECIMAL(19,4) COMMENT 'Disputed Amount',
  pan_proxy_number_count INT COMMENT 'Pan Proxy Number Count'
)

COMMENT 'FIS Reporting Aggregate Values for Disputes' COLLATE=utf8_unicode_ci;
SET sql_notes = 1;

CREATE INDEX md5_hash_key_idx ON fis_statistics_dispute (hash_key);
CREATE INDEX context_idx ON fis_statistics_dispute (context);
CREATE INDEX bin_idx ON fis_statistics_dispute (bin);



/* Negative Balance Statistics */
SET sql_notes = 0;
CREATE TABLE IF NOT EXISTS fis_statistics_negative_balance (
  id int auto_increment primary key,
  hash_key NVARCHAR(255) COMMENT 'Unique identifier for this entry',
  context NVARCHAR(100) NOT NULL COMMENT 'Context for this specific record',
  file_processor_name NVARCHAR(3) COMMENT 'DPP or PPM',
  work_of_date DATE COMMENT 'Date of Transaction',
  bank_name NVARCHAR(255) COMMENT 'Name of the Bank',
  issuer_bank_id INT COMMENT 'Identity of Bank',
  client_name NVARCHAR(255) COMMENT 'Name of the Client',
  issuer_client_id INT COMMENT 'Identity of Client',
  bin_currency_alpha NVARCHAR(5) COMMENT 'Currency Code',
  bin INT NULL COMMENT 'Bin',
  sub_program_id INT NOT NULL COMMENT 'Subprogram Identifier',

  /* Portfolio Report */
  opening_balance_total DECIMAL(19,4) COMMENT 'Opening Balance Total',
  opening_balance_average DECIMAL(19,4) COMMENT 'Opening Balance Average',
  closing_balance_total DECIMAL(19,4) COMMENT 'Closing Balance Total',
  closing_balance_average DECIMAL(19,4) COMMENT 'Closing Balance Average',
  pan_count INT COMMENT 'Number of Unique PANs',
  record_count INT COMMENT 'Number of Unique Records',

  /* Load Report */
  total_value_load_amount DECIMAL(19,4) COMMENT 'Total Value Load Amount',
  total_value_load_count INT COMMENT 'Total Value Load Count',
  pan_proxy_number_count INT COMMENT 'Pan Proxy Number Count',

  /* Fees Report */
  total_fees_amount DECIMAL(19,4) COMMENT 'Total Amount of Fees',
  total_fees_count INT COMMENT 'Total Number of Fees',

  /* Usage Report */
  total_credit_amount DECIMAL(19,4) COMMENT 'Total Amount Credited',
  total_debit_amount DECIMAL(19,4) COMMENT 'Total Amount Debited'
)

COMMENT 'FIS Reporting Aggregate Values for Negative Balances' COLLATE=utf8_unicode_ci;
SET sql_notes = 1;

CREATE INDEX md5_hash_key_idx ON fis_statistics_negative_balance (hash_key);
CREATE INDEX context_idx ON fis_statistics_negative_balance (context);
CREATE INDEX bin_idx ON fis_statistics_negative_balance (bin);



/* Monetary Statistics */
SET sql_notes = 0;
CREATE TABLE IF NOT EXISTS fis_statistics_monetary (
  id int auto_increment primary key,
  hash_key NVARCHAR(255) COMMENT 'Unique identifier for this entry',
  context NVARCHAR(100) NOT NULL COMMENT 'Context for this record',
  file_processor_name NVARCHAR(3) COMMENT 'DPP or PPM',
  work_of_date DATE COMMENT 'Date of Transaction',
  bank_name NVARCHAR(255) COMMENT 'Name of the Bank',
  issuer_bank_id INT COMMENT 'Identity of Bank',
  client_name NVARCHAR(255) COMMENT 'Name of the Client',
  issuer_client_id INT COMMENT 'Identity of Client',
  bin_currency_alpha NVARCHAR(5) COMMENT 'Currency Code',
  bin INT NULL COMMENT 'Bin',
  sub_program_id INT NOT NULL COMMENT 'Subprogram Identifier',
  
  /* Totals */
  pan_count INT COMMENT 'Number of Unique PANs',
  record_count INT COMMENT 'Number of Unique Records',
  pan_proxy_number_count INT COMMENT 'Pan Proxy Number Count',

  /* Settle No Auth Report */
  txn_usd_amount DECIMAL(19,4) COMMENT 'Transaction USD Amount',
  txn_type_name NVARCHAR(255) COMMENT 'Transaction Type Name'

  /* ACT Activity Report */
  txn_sign INT COMMENT 'Transaction Sign',
  actual_request_code INT COMMENT 'Actual Request Code'

  /* MCC Report */
  mcc INT COMMENT 'MCC',
  mcc_description INT COMMENT 'MCC Description'
)

COMMENT 'FIS Reporting Aggregate Values for Monetary' COLLATE=utf8_unicode_ci;
SET sql_notes = 1;

CREATE INDEX md5_hash_key_idx ON fis_statistics_monetary (hash_key);
CREATE INDEX context_idx ON fis_statistics_monetary (context);
CREATE INDEX bin_idx ON fis_statistics_monetary (bin);
