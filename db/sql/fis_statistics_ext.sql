/* Balance Statistics */
SET sql_notes = 0;
CREATE TABLE IF NOT EXISTS fis_statistics_transactions (

  id int auto_increment primary key,
  hash_key NVARCHAR(500) NOT NULL,
  context NVARCHAR(100) NOT NULL,

  file_processor_name <PERSON>VARCHAR(3),
  work_of_date DATE,
  bank_name <PERSON><PERSON><PERSON><PERSON><PERSON>(255),
  issuer_bank_id INT,
  client_name NVA<PERSON>HAR(255),
  issuer_client_id INT,
  bin_currency_alpha NVARCHAR(5),
  bin INT NULL,
  sub_program_id INT NOT NULL,
  pan_proxy_number_count INT NOT NULL,

  tran_type NVARCHAR(100),
  total_amount DECIMAL(19,4),
  total_count INT

)

COMMENT 'FIS Reporting Aggregate Values for Transactions' COLLATE=utf8_unicode_ci;
SET sql_notes = 1;

CREATE INDEX md5_hash_key_idx ON fis_statistics_transactions (hash_key);
CREATE INDEX context_idx ON fis_statistics_transactions (context);
CREATE INDEX bin_idx ON fis_statistics_transactions (bin);
CREATE INDEX type_idx ON fis_statistics_transactions (tran_type);