### TransferMex transactions
select distinct distinct uct.txn_time, uc.user_id,
                         (select ug.name from user_group_users ugu inner join user_group ug on ugu.group_id = ug.id where ugu.user_id = u.id) employer,
                         c.name country, s.name state, uct.tran_code, uct.actual_tran_code, uct.user_field_1 as type,
                         uct.outstanding_auths description, uct.user_field_2 message,
                         txn_amount, mt.mcc, m.merchant_cust_name
from user_card_transaction uct
         inner join user_card uc on uct.user_card_id = uc.id
         inner join users u on uc.user_id = u.id
         inner join country c on u.country_id = c.id
         inner join base_state s on u.state_id = s.id
         inner join user_role ur on u.id = ur.user_id
         left join merchant m on uct.merchant_id = m.id
         left join merchant_type mt on m.merchant_type_id = mt.id
where uct.txn_time >= '2023-01-01 00:00:00'
  and uct.tran_id not like 'transfer_%'
  and uc.card_id = 73
  and ur.role_id = 31
  and account_status = 'Executed'
order by uct.txn_time;

### TransferMex transfers
select distinct t.send_at, t.sender_id as user_id,
                (select ug.name from user_group_users ugu inner join user_group ug on ugu.group_id = ug.id where ugu.user_id = u.id) employer,
                c.name country, s.name state, t.send_amount, t.partner, t.payout_type, t.payout_method_type,
                t.transfer_fee, t.cost, t.revenue shared_revenue, t.platform_revenue fx_revenue
from transfer t
         inner join users u on t.sender_id = u.id
         inner join country c on u.country_id = c.id
         inner join base_state s on u.state_id = s.id
         inner join user_card uc on u.id = uc.user_id
         inner join user_role ur on u.id = ur.user_id
where t.send_at >= '2023-01-01 00:00:00'
  and t.status = 'Completed'
  and uc.card_id = 73
  and ur.role_id = 31
order by t.send_at;
