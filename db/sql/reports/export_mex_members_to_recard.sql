select ug.name,
       u.first_name,
       u.last_name,
       u.email,
       COALESCE(ug.botm_business_id, 28) business_id,
       u.birthday                        dob,
       u.address                         address_1,
       u.addressline                     address_2,
       u.city,
       bs.name                           province,
       u.zip                             postal_code,
       c.name                            country,
       u.mobilephone                     mobile_phone,
       u.id                              external_id
from users u
         inner join user_role ur on u.id = ur.user_id
         inner join role r on ur.role_id = r.id and r.name = 'TransferMex Member'
         inner join user_group_users ugu on ugu.user_id = u.id
         inner join user_group ug on ugu.group_id = ug.id
         inner join country c on u.country_id = c.id
         inner join base_state bs on u.state_id = bs.id
where u.register_step = 'Active'
  and ug.id not in (1, 2, 3, 4, 267, 4789, 7457, 9859, 38187, 38188);