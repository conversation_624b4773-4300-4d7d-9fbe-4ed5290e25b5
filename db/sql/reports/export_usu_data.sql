### USU loads
select distinct ucl.load_at, uc.user_id, c.name country, ucl.type, ucl.load_amount, lm.name load_method
from user_card_load ucl
         inner join user_card uc on uc.id = ucl.user_card_id
         inner join users u on uc.user_id = u.id
         inner join country c on u.country_id = c.id
         inner join load_method lm on ucl.method_id = lm.id
         inner join user_role ur on u.id = ur.user_id
where ucl.load_status = 'loaded'
  and ucl.status = 'completed'
  and ucl.load_at >= '2023-01-01 00:00:00'
  and uc.card_id = 71
  and ur.role_id = 9
order by ucl.load_at;

### USU fees
select distinct ufh.time, ufh.user_id, c.name country, ufh.fee_name, ufh.amount
from user_fee_history ufh
         inner join users u on ufh.user_id = u.id
         inner join country c on u.country_id = c.id
         inner join user_card uc on u.id = uc.user_id
         inner join user_role ur on u.id = ur.user_id
where ufh.time >= '2023-01-01 00:00:00'
  and uc.card_id = 71
  and ur.role_id = 9
order by ufh.time;

### USU transactions
select distinct uct.txn_time, uc.user_id, c.name country, uct.user_field_1 as type, txn_amount, mt.mcc, m.merchant_cust_name
from user_card_transaction uct
         inner join user_card uc on uct.user_card_id = uc.id
         inner join users u on uc.user_id = u.id
         inner join country c on u.country_id = c.id
         inner join user_role ur on u.id = ur.user_id
         left join merchant m on uct.merchant_id = m.id
         left join merchant_type mt on m.merchant_type_id = mt.id
where uct.txn_time >= '2023-01-01 00:00:00'
  and uc.card_id = 71
  and ur.role_id = 9
  and uct.account_status in ('PENDING', 'SETTLING', 'SETTLED')
order by uct.txn_time;

### USU users with positive balance
select distinct u.id, u.first_name, u.last_name, u.email, u.status, uc.balance * 0.01 as balance, c.name country
from users u
         inner join user_role ur on u.id = ur.user_id and ur.role_id = 9 # consumer
         inner join user_card uc on u.id = uc.user_id and uc.card_id = 71 # US Unlocked
         inner join country c on u.country_id = c.id
where uc.type = 'DUMMY'
  and uc.balance > 0
  and u.status <> 'closed'
#   and u.id in (select uc.user_id from user_card uc where uc.card_id = 71 and uc.type <> 'DUMMY' and uc.native_status <> 'CLOSED' and issued = 1)
order by u.id;

