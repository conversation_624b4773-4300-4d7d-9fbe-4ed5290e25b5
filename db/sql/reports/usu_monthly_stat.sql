### USU monthly loads
select distinct date_format(ucl.load_at, '%Y-%m') month,
                sum(ucl.load_amount_usd) / 100    amount,
                count(distinct ucl.id)            count
from user_card_load ucl
         inner join user_card uc on uc.id = ucl.user_card_id
         inner join users u on uc.user_id = u.id
         inner join user_role ur on u.id = ur.user_id
where ucl.load_status = 'loaded'
  and ucl.status = 'completed'
  and uc.card_id = 71
  and ur.role_id = 9
group by month
order by month desc;

### Average POS transaction size
select distinct date_format(uct.txn_time, '%Y-%m') month,
                sum(uct.txn_amount_usd) / 100      amount,
                count(distinct uct.id)             count
from user_card_transaction uct
         inner join user_card uc on uc.id = uct.user_card_id
         inner join users u on uc.user_id = u.id
         inner join user_role ur on u.id = ur.user_id
where uct.actual_tran_code = 'APPROVED'
  and uct.tran_id like '%-%'
  and uct.account_status in ('PENDING', 'SETTLING', 'SETTLED')
  and (uct.user_field_1 in ('AUTHORIZATION', 'FINANCIAL_AUTHORIZATION') or uct.user_field_1 is null)
  and uc.card_id = 71
  and ur.role_id = 9
group by month
order by month desc;
