CREATE TABLE email (id INT AUTO_INCREMENT NOT NULL, subject VA<PERSON>HA<PERSON>(255) DEFAULT NULL, body LONGTEXT DEFAULT NULL, content_type VARCHAR(255) NOT NULL, sender VA<PERSON><PERSON><PERSON>(255) NOT NULL, recipients LONGTEXT NOT NULL COMMENT 'Also saved in email_recipient table', time DATETIME NOT NULL, template VARCHAR(255) DEFAULT NULL COMMENT 'Email template id in postmarkapp.com', template_data LONGTEXT DEFAULT NULL COMMENT 'Data sent to postmark used to replace placeholders in template', message_id VARCHAR(128) DEFAULT NULL COMMENT 'Message ID returned from postmark, used to fetch email content.', purpose VARCHAR(255) NOT NULL, error VARCHAR(1024) DEFAULT NULL COMMENT 'The system will retry sending if failed for some errors.', status VARCHAR(255) DEFAULT 'sent' NOT NULL COMMENT 'disabled, pending, send or error', INDEX status_idx (status), INDEX error_idx (error), INDEX subject_idx (subject), INDEX time_idx (time), INDEX message_id_idx (message_id), INDEX content_idx (status, subject, time), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB COMMENT = 'Email history, also the email queue.' ;
CREATE TABLE user_card_snap (id INT AUTO_INCREMENT NOT NULL, user_card_id INT DEFAULT NULL, time VARCHAR(255) NOT NULL COMMENT 'YYYY-MM-DD', balance BIGINT DEFAULT NULL, activity_type VARCHAR(1023) DEFAULT NULL COMMENT 'inactive, active, skeleton, closed, etc.', balance_changed_at DATETIME DEFAULT NULL, status VARCHAR(255) NOT NULL COMMENT 'Card''s status when snap is captured', INDEX IDX_DAB792D812C1842A (user_card_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB COMMENT = 'User card snap create from cron tab to generate reports' ;
CREATE TABLE email_recipient (id INT AUTO_INCREMENT NOT NULL, email_id INT DEFAULT NULL, address VARCHAR(255) NOT NULL, name VARCHAR(255) DEFAULT NULL, INDEX IDX_670F6462A832C1C9 (email_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE error (id INT AUTO_INCREMENT NOT NULL, user_id INT DEFAULT NULL, title LONGTEXT DEFAULT NULL COMMENT 'Error message', body LONGTEXT DEFAULT NULL COMMENT 'Request details like url, cookie, query string', host VARCHAR(255) DEFAULT NULL, query LONGTEXT DEFAULT NULL, request LONGTEXT DEFAULT NULL, headers LONGTEXT DEFAULT NULL, cookie LONGTEXT DEFAULT NULL, server LONGTEXT DEFAULT NULL COMMENT 'PHP SERVER global variable', input LONGTEXT DEFAULT NULL, time DATETIME DEFAULT NULL, message LONGTEXT DEFAULT NULL, call_stack LONGTEXT DEFAULT NULL, critical TINYINT(1) DEFAULT '0' NOT NULL COMMENT 'Email will be sent to admins if it is critical', INDEX IDX_5DDDBC71A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB COMMENT = 'All important errors happened' ;
CREATE TABLE ip_usage (id INT AUTO_INCREMENT NOT NULL, users_id INT DEFAULT NULL, type VARCHAR(255) DEFAULT NULL, login_ip VARCHAR(255) NOT NULL, login_time DATETIME NOT NULL, login_infromation VARCHAR(255) NOT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, INDEX IDX_ADFFA79D67B3B43D (users_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB COMMENT = 'All IPs tracked when user logged in' ;
CREATE TABLE external_invoke (id INT AUTO_INCREMENT NOT NULL, type VARCHAR(255) NOT NULL, request LONGTEXT NOT NULL COMMENT 'Request details', request_key VARCHAR(255) DEFAULT NULL COMMENT 'ID of each request. Depends on request type.', response LONGTEXT DEFAULT NULL, respond_at DATETIME DEFAULT NULL, meta LONGTEXT DEFAULT NULL COMMENT '(DC2Type:json_array)', entity VARCHAR(255) DEFAULT NULL COMMENT 'Connected entity(table)', foreign_key VARCHAR(255) DEFAULT NULL COMMENT 'ID of connected table', status VARCHAR(255) DEFAULT NULL COMMENT 'unknown(as null), success or failed', created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, INDEX type_idx (type), INDEX request_key_idx (request_key), INDEX type_request_key_idx (type, request_key), INDEX create_by_idx (create_by), INDEX type_entity_idx (type, entity, foreign_key), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB COMMENT = 'History of all important 3rdy party API callings.' ;
CREATE TABLE user_velocity (id INT AUTO_INCREMENT NOT NULL, user_id INT DEFAULT NULL, user_card_id INT DEFAULT NULL, velocity_id INT DEFAULT NULL, account_balance_id INT DEFAULT NULL, authorization_id INT DEFAULT NULL, monetary_id INT DEFAULT NULL, dispute_id INT DEFAULT NULL, non_monetary_id INT DEFAULT NULL, trigger_data LONGTEXT DEFAULT NULL COMMENT 'Details of data that triggered the velocity(DC2Type:json_array)', details LONGTEXT DEFAULT NULL COMMENT 'Related all data that triggered the velocity(DC2Type:json_array)', status VARCHAR(255) DEFAULT NULL COMMENT 'null or reviewed', reset TINYINT(1) DEFAULT NULL COMMENT 'Reset statistics or not', reset_at DATETIME DEFAULT NULL, pan VARCHAR(30) DEFAULT NULL, work_of_date DATE DEFAULT NULL, count INT DEFAULT NULL, amount INT DEFAULT NULL, meta LONGTEXT DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, INDEX IDX_5E5532A912C1842A (user_card_id), INDEX IDX_5E5532A9520A5179 (account_balance_id), INDEX IDX_5E5532A92F8B0EB2 (authorization_id), INDEX IDX_5E5532A9B2BA99B (monetary_id), INDEX IDX_5E5532A9C7B47CB5 (dispute_id), INDEX IDX_5E5532A978816E4A (non_monetary_id), INDEX velocity_id_idx (velocity_id), INDEX user_id_idx (user_id), INDEX work_of_date_idx (work_of_date), INDEX pan_idx (pan), INDEX status_idx (status), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB COMMENT = 'History of users that triggered any velocity' ;
CREATE TABLE login_attempt (id INT AUTO_INCREMENT NOT NULL, user VARCHAR(255) NOT NULL, success TINYINT(1) NOT NULL, memo VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB COMMENT = 'Used to deny requests if user try to login after several failed attempts.' ;
CREATE TABLE fis_non_monetary (id INT AUTO_INCREMENT NOT NULL, file_name VARCHAR(255) NOT NULL, file_processor_name VARCHAR(11) DEFAULT NULL, file_report_feed_name VARCHAR(9) DEFAULT NULL, file_date DATE NOT NULL, work_of_date DATE NOT NULL, top_client_id VARCHAR(10) DEFAULT NULL, top_client_name VARCHAR(30) DEFAULT NULL, issuer_client_id VARCHAR(10) DEFAULT NULL, client_name VARCHAR(30) DEFAULT NULL, program_id VARCHAR(10) DEFAULT NULL, program_name VARCHAR(40) DEFAULT NULL, sub_program_id VARCHAR(10) DEFAULT NULL, sub_program_name VARCHAR(80) DEFAULT NULL, bin VARCHAR(6) DEFAULT NULL, bin_currency_alpha VARCHAR(3) DEFAULT NULL, bin_currency_code VARCHAR(3) DEFAULT NULL, package_id VARCHAR(10) DEFAULT NULL, package_name VARCHAR(80) DEFAULT NULL, pan VARCHAR(19) DEFAULT NULL, card_number VARCHAR(19) DEFAULT NULL, activate_date DATE DEFAULT NULL, card_status VARCHAR(10) DEFAULT NULL, cardholder_first_name VARCHAR(50) DEFAULT NULL, cardholder_last_name VARCHAR(50) DEFAULT NULL, cardholder_middle_name VARCHAR(1) DEFAULT NULL, cardholder_mailing_address_line1 VARCHAR(50) DEFAULT NULL, cardholder_mailing_address_line2 VARCHAR(50) DEFAULT NULL, cardholder_mailing_city VARCHAR(35) DEFAULT NULL, cardholder_mailing_state VARCHAR(25) DEFAULT NULL, cardholder_mailing_zip VARCHAR(30) DEFAULT NULL, cardholder_mailing_country VARCHAR(45) DEFAULT NULL, cardholder_country_code VARCHAR(3) DEFAULT NULL, cardholder_telephone_number VARCHAR(23) DEFAULT NULL, cardholder_telephone_number_ext VARCHAR(8) DEFAULT NULL, card_number_loc_creation_date DATE DEFAULT NULL, card_number_loc_expiration_date DATE DEFAULT NULL, market_segment_id VARCHAR(10) DEFAULT NULL, market_segment_description VARCHAR(40) DEFAULT NULL, request_code VARCHAR(10) DEFAULT NULL, source_code VARCHAR(10) DEFAULT NULL, wcs_utc_inserted DATETIME DEFAULT NULL, cardholder_business_number VARCHAR(23) DEFAULT NULL, cardholder_business_number_ext VARCHAR(8) DEFAULT NULL, card_number_loc_status_effective_date DATE DEFAULT NULL, cardholder_country VARCHAR(25) DEFAULT NULL, cardholder_date_of_birth DATE DEFAULT NULL, cardholder_driver_license_number VARCHAR(20) DEFAULT NULL, cardholder_driver_license_state VARCHAR(2) DEFAULT NULL, cardholder_email VARCHAR(80) DEFAULT NULL, cardholder_emergency_phone VARCHAR(23) DEFAULT NULL, cardholder_emergency_phone_extension VARCHAR(8) DEFAULT NULL, cardholder_fax VARCHAR(23) DEFAULT NULL, cardholder_fax_extension VARCHAR(8) DEFAULT NULL, cardholder_ssn VARCHAR(9) DEFAULT NULL, cardholder_suffix VARCHAR(3) DEFAULT NULL, card_status_description VARCHAR(30) DEFAULT NULL, client_level_number VARCHAR(10) DEFAULT NULL, account_loc_creation_date DATE DEFAULT NULL, account_loc_expiration_date DATE DEFAULT NULL, card_number_proxy VARCHAR(30) DEFAULT NULL, comment VARCHAR(512) DEFAULT NULL, privacy_opt_out VARCHAR(3) DEFAULT NULL, other_person_addr1 VARCHAR(50) DEFAULT NULL, other_person_addr2 VARCHAR(50) DEFAULT NULL, other_person_business_number VARCHAR(23) DEFAULT NULL, other_person_business_number_ext VARCHAR(8) DEFAULT NULL, other_person_city VARCHAR(35) DEFAULT NULL, other_person_country VARCHAR(45) DEFAULT NULL, other_person_country_code VARCHAR(3) DEFAULT NULL, other_person_country2 VARCHAR(25) DEFAULT NULL, other_person_dob DATE DEFAULT NULL, other_person_driver_license_number VARCHAR(20) DEFAULT NULL, other_person_driver_license_state VARCHAR(2) DEFAULT NULL, other_person_email VARCHAR(80) DEFAULT NULL, other_person_emergency_phone VARCHAR(23) DEFAULT NULL, other_person_emergency_phone_extension VARCHAR(8) DEFAULT NULL, other_person_fax VARCHAR(23) DEFAULT NULL, other_person_relationship VARCHAR(12) DEFAULT NULL, other_person_first_name VARCHAR(50) DEFAULT NULL, other_person_last_name VARCHAR(50) DEFAULT NULL, other_person_middle_name VARCHAR(1) DEFAULT NULL, other_person_state VARCHAR(25) DEFAULT NULL, other_person_telephone_number VARCHAR(23) DEFAULT NULL, other_person_telephone_number_extension VARCHAR(8) DEFAULT NULL, other_person_zip VARCHAR(30) DEFAULT NULL, primary_relationship VARCHAR(12) DEFAULT NULL, request_code_description VARCHAR(80) DEFAULT NULL, source_code_description VARCHAR(80) DEFAULT NULL, true_anonymous VARCHAR(1) DEFAULT NULL, user_id VARCHAR(30) DEFAULT NULL, user_first_name VARCHAR(15) DEFAULT NULL, user_last_name VARCHAR(15) DEFAULT NULL, person_id VARCHAR(10) DEFAULT NULL, cardholder_client_unique_id VARCHAR(30) DEFAULT NULL, cardholder_other_info VARCHAR(60) DEFAULT NULL, client_val VARCHAR(60) DEFAULT NULL, discretionary_data1 VARCHAR(50) DEFAULT NULL, discretionary_data2 VARCHAR(50) DEFAULT NULL, discretionary_data3 VARCHAR(50) DEFAULT NULL, client_specific_id VARCHAR(36) DEFAULT NULL, cardholder_alerts_email VARCHAR(80) DEFAULT NULL, direct_access_number VARCHAR(13) DEFAULT NULL, cardholder_residential_address_line1 VARCHAR(50) DEFAULT NULL, cardholder_residential_address_line2 VARCHAR(50) DEFAULT NULL, cardholder_residential_city VARCHAR(35) DEFAULT NULL, cardholder_residential_state VARCHAR(25) DEFAULT NULL, cardholder_residential_zip VARCHAR(30) DEFAULT NULL, cardholder_residential_country VARCHAR(45) DEFAULT NULL, card_holder_sms_mobile_number VARCHAR(23) DEFAULT NULL, card_holder_mobile_number VARCHAR(50) DEFAULT NULL, pan_proxy_number VARCHAR(30) DEFAULT NULL, cardholder_mailing_address_line3 VARCHAR(50) DEFAULT NULL, cardholder_residential_address_line3 VARCHAR(50) DEFAULT NULL, order_id VARCHAR(18) DEFAULT NULL, sms_language VARCHAR(80) DEFAULT NULL, email_language VARCHAR(80) DEFAULT NULL, title VARCHAR(25) DEFAULT NULL, initial_value_load_date DATE DEFAULT NULL, date_person_created DATE DEFAULT NULL, id_type VARCHAR(20) DEFAULT NULL, id_number VARCHAR(50) DEFAULT NULL, passport_country_of_issuance VARCHAR(50) DEFAULT NULL, name1 VARCHAR(100) DEFAULT NULL, value1 LONGTEXT DEFAULT NULL, name2 VARCHAR(100) DEFAULT NULL, value2 LONGTEXT DEFAULT NULL, name3 VARCHAR(100) DEFAULT NULL, value3 LONGTEXT DEFAULT NULL, name4 VARCHAR(100) DEFAULT NULL, value4 LONGTEXT DEFAULT NULL, name5 VARCHAR(100) DEFAULT NULL, value5 LONGTEXT DEFAULT NULL, name6 VARCHAR(100) DEFAULT NULL, value6 LONGTEXT DEFAULT NULL, name7 VARCHAR(100) DEFAULT NULL, value7 LONGTEXT DEFAULT NULL, name8 VARCHAR(100) DEFAULT NULL, value8 LONGTEXT DEFAULT NULL, name9 VARCHAR(100) DEFAULT NULL, value9 LONGTEXT DEFAULT NULL, name10 VARCHAR(100) DEFAULT NULL, value10 LONGTEXT DEFAULT NULL, risk_wise_codes VARCHAR(100) DEFAULT NULL, risk_wise_result VARCHAR(10) DEFAULT NULL, idv_codes VARCHAR(100) DEFAULT NULL, idv_result VARCHAR(10) DEFAULT NULL, idology_codes VARCHAR(100) DEFAULT NULL, idology_result VARCHAR(10) DEFAULT NULL, cardholder_federal_tax_id VARCHAR(16) DEFAULT NULL, cardholder_company_name VARCHAR(50) DEFAULT NULL, cardholder_identification_question LONGTEXT DEFAULT NULL, aps_codes LONGTEXT DEFAULT NULL, aps_result VARCHAR(100) DEFAULT NULL, reason_id VARCHAR(10) DEFAULT NULL, reason_description VARCHAR(255) DEFAULT NULL, INDEX file_name_idx (file_name), INDEX work_of_date_idx (work_of_date), INDEX bin_currency_alpha_idx (bin_currency_alpha), INDEX client_name_idx (client_name), INDEX issuer_client_id_idx (issuer_client_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE fis_dispute (id INT AUTO_INCREMENT NOT NULL, file_name VARCHAR(255) NOT NULL, file_processor_name VARCHAR(11) DEFAULT NULL, file_report_feed_name VARCHAR(9) DEFAULT NULL, file_date DATE NOT NULL, work_of_date DATE NOT NULL, issuer_client_id VARCHAR(10) DEFAULT NULL, client_name VARCHAR(30) DEFAULT NULL, sub_program_id VARCHAR(10) DEFAULT NULL, sub_program_name VARCHAR(80) DEFAULT NULL, bin VARCHAR(6) DEFAULT NULL, bin_currency_alpha VARCHAR(3) DEFAULT NULL, bin_currency_code VARCHAR(3) DEFAULT NULL, bank_name VARCHAR(23) DEFAULT NULL, pan VARCHAR(19) DEFAULT NULL, pan_proxy_number VARCHAR(30) DEFAULT NULL, card_number VARCHAR(19) DEFAULT NULL, card_number_proxy VARCHAR(30) DEFAULT NULL, card_holder_name VARCHAR(120) DEFAULT NULL, ssn VARCHAR(9) DEFAULT NULL, cardholder_client_unique_id VARCHAR(30) DEFAULT NULL, direct_access_number VARCHAR(13) DEFAULT NULL, reg_e VARCHAR(1) DEFAULT NULL, claim_open_date DATE DEFAULT NULL, date_written_confirmation_was_requested DATE DEFAULT NULL, date_dispute_form_received DATE DEFAULT NULL, ageing_bus_days DOUBLE PRECISION DEFAULT NULL, txn_uid VARCHAR(36) DEFAULT NULL, original_transaction_date DATETIME DEFAULT NULL, original_request_code VARCHAR(10) DEFAULT NULL, original_request_description VARCHAR(80) DEFAULT NULL, original_transaction_amount DOUBLE PRECISION DEFAULT NULL, original_txn_type_code VARCHAR(5) DEFAULT NULL, original_txn_type_name VARCHAR(50) DEFAULT NULL, merchant_name VARCHAR(40) DEFAULT NULL, merchant_state VARCHAR(2) DEFAULT NULL, merchant_country_alpha VARCHAR(3) DEFAULT NULL, disputed_amount DOUBLE PRECISION DEFAULT NULL, dispute_type VARCHAR(100) DEFAULT NULL, dispute_state VARCHAR(100) DEFAULT NULL, dispute_status VARCHAR(50) DEFAULT NULL, dispute_reason VARCHAR(100) DEFAULT NULL, provisional_credit DOUBLE PRECISION DEFAULT NULL, provisional_credit_date DATE DEFAULT NULL, claim_close_date DATE DEFAULT NULL, final_resolution_date DATE DEFAULT NULL, final_credit DOUBLE PRECISION DEFAULT NULL, final_credit_date DATE DEFAULT NULL, letters_and_dates VARCHAR(1000) DEFAULT NULL, auto_charge_back_indicator VARCHAR(1) DEFAULT NULL, negative_date DATE DEFAULT NULL, negative_balance_amount DOUBLE PRECISION DEFAULT NULL, charge_back_file_date DATE DEFAULT NULL, charge_back_reason VARCHAR(100) DEFAULT NULL, charge_back_amount DOUBLE PRECISION DEFAULT NULL, representment_amount DOUBLE PRECISION DEFAULT NULL, representment_date DATE DEFAULT NULL, provisional_credit_reversal_amount DOUBLE PRECISION DEFAULT NULL, provisional_credit_reversal_date DATE DEFAULT NULL, liability VARCHAR(50) DEFAULT NULL, write_off_amount DOUBLE PRECISION DEFAULT NULL, write_off_date DATE DEFAULT NULL, write_off_request_code VARCHAR(10) DEFAULT NULL, atm VARCHAR(1) DEFAULT NULL, denial_date DATE DEFAULT NULL, denial_reason VARCHAR(100) DEFAULT NULL, claim_identifier VARCHAR(14) DEFAULT NULL, INDEX file_name_idx (file_name), INDEX work_of_date_idx (work_of_date), INDEX bin_currency_alpha_idx (bin_currency_alpha), INDEX client_name_idx (client_name), INDEX issuer_client_id_idx (issuer_client_id), INDEX bank_name_idx (bank_name), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE fis_cashback_program (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE fis_program_type (id INT AUTO_INCREMENT NOT NULL, parent_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, bins LONGTEXT DEFAULT NULL, programs LONGTEXT DEFAULT NULL, INDEX IDX_F03E4D21727ACA70 (parent_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE fis_authorization (id INT AUTO_INCREMENT NOT NULL, file_name VARCHAR(255) NOT NULL, file_processor_name VARCHAR(11) DEFAULT NULL, file_report_feed_name VARCHAR(9) DEFAULT NULL, file_date DATE NOT NULL, work_of_date DATE NOT NULL, issuer_client_id VARCHAR(10) DEFAULT NULL, client_name VARCHAR(30) DEFAULT NULL, sub_program_id VARCHAR(10) DEFAULT NULL, sub_program_name VARCHAR(80) DEFAULT NULL, bin VARCHAR(6) DEFAULT NULL, bin_currency_alpha VARCHAR(3) DEFAULT NULL, bin_currency_code VARCHAR(3) DEFAULT NULL, bank_name VARCHAR(23) DEFAULT NULL, pan VARCHAR(19) DEFAULT NULL, card_number VARCHAR(19) DEFAULT NULL, txn_uid VARCHAR(36) DEFAULT NULL, txn_type_code VARCHAR(5) DEFAULT NULL, txn_type_name VARCHAR(50) DEFAULT NULL, purse_no VARCHAR(6) DEFAULT NULL, purse_name VARCHAR(40) DEFAULT NULL, transaction_date_time DATETIME DEFAULT NULL, authorization_code VARCHAR(6) DEFAULT NULL, actual_request_code VARCHAR(10) DEFAULT NULL, actual_request_code_description VARCHAR(80) DEFAULT NULL, response_code VARCHAR(10) DEFAULT NULL, response_description VARCHAR(80) DEFAULT NULL, reason_code VARCHAR(10) DEFAULT NULL, reason_code_description VARCHAR(80) DEFAULT NULL, source_code VARCHAR(10) DEFAULT NULL, source_description VARCHAR(80) DEFAULT NULL, authorization_amount DOUBLE PRECISION DEFAULT NULL, txn_local_amount DOUBLE PRECISION DEFAULT NULL, txn_usd_amount INT DEFAULT NULL, txn_sign VARCHAR(2) DEFAULT NULL, transaction_currency_code VARCHAR(5) DEFAULT NULL, transaction_currency_alpha VARCHAR(3) DEFAULT NULL, retrieval_ref_no VARCHAR(23) DEFAULT NULL, reversed VARCHAR(1) DEFAULT NULL, avs_information VARCHAR(80) DEFAULT NULL, avs_response_code VARCHAR(1) DEFAULT NULL, pin VARCHAR(1) DEFAULT NULL, pos_data VARCHAR(26) DEFAULT NULL, pos_entry_code VARCHAR(10) DEFAULT NULL, pos_entry_description VARCHAR(80) DEFAULT NULL, mcc VARCHAR(10) DEFAULT NULL, mcc_description VARCHAR(80) DEFAULT NULL, merchant_currency_alpha VARCHAR(3) DEFAULT NULL, merchant_currency_code VARCHAR(3) DEFAULT NULL, merchant_name VARCHAR(40) DEFAULT NULL, merchant_number VARCHAR(16) DEFAULT NULL, merchant_street VARCHAR(80) DEFAULT NULL, merchant_city VARCHAR(25) DEFAULT NULL, merchant_province VARCHAR(30) DEFAULT NULL, merchant_state VARCHAR(2) DEFAULT NULL, merchant_zip VARCHAR(9) DEFAULT NULL, merchant_country_code VARCHAR(3) DEFAULT NULL, merchant_country_name VARCHAR(45) DEFAULT NULL, terminal_number VARCHAR(8) DEFAULT NULL, acquirer_id VARCHAR(18) DEFAULT NULL, card_number_proxy VARCHAR(30) DEFAULT NULL, client_specific_id VARCHAR(36) DEFAULT NULL, authorization_balance DOUBLE PRECISION DEFAULT NULL, settle_balance DOUBLE PRECISION DEFAULT NULL, tolerance_amount DOUBLE PRECISION DEFAULT NULL, card_verification_method VARCHAR(2) DEFAULT NULL, cvc2_response VARCHAR(1) DEFAULT NULL, pan_proxy_number VARCHAR(30) DEFAULT NULL, process_code VARCHAR(6) DEFAULT NULL, token_unique_reference_id VARCHAR(64) DEFAULT NULL, pan_unique_reference_id VARCHAR(64) DEFAULT NULL, token_transaction_id VARCHAR(64) DEFAULT NULL, token_status VARCHAR(1) DEFAULT NULL, token_status_description VARCHAR(50) DEFAULT NULL, network_reference_id VARCHAR(50) DEFAULT NULL, auth_expiry_date DATE DEFAULT NULL, wcs_local_inserted DATETIME DEFAULT NULL, wcs_utc_inserted DATETIME DEFAULT NULL, wcs_utc_updated DATETIME DEFAULT NULL, INDEX file_name_idx (file_name), INDEX work_of_date_idx (work_of_date), INDEX bin_currency_alpha_idx (bin_currency_alpha), INDEX client_name_idx (client_name), INDEX issuer_client_id_idx (issuer_client_id), INDEX bank_name_idx (bank_name), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE fis_account_balance_cache (id INT AUTO_INCREMENT NOT NULL, work_of_date DATE NOT NULL, bin_currency_alpha VARCHAR(3) DEFAULT NULL, bank_name VARCHAR(23) DEFAULT NULL, bin VARCHAR(6) DEFAULT NULL, issuer_client_id VARCHAR(10) DEFAULT NULL, opening_balance DOUBLE PRECISION DEFAULT NULL, opening_card_count INT DEFAULT NULL, total_value_load_amount DOUBLE PRECISION DEFAULT NULL, total_value_load_count INT DEFAULT NULL, total_purchase_amount DOUBLE PRECISION DEFAULT NULL, total_purchase_count INT DEFAULT NULL, total_otc_amount DOUBLE PRECISION DEFAULT NULL, total_otc_count INT DEFAULT NULL, total_atm_withdrawal_amount DOUBLE PRECISION DEFAULT NULL, total_atm_withdrawal_count INT DEFAULT NULL, total_return_amount DOUBLE PRECISION DEFAULT NULL, total_return_count INT DEFAULT NULL, total_adjustment_amount DOUBLE PRECISION DEFAULT NULL, total_adjustment_count INT DEFAULT NULL, total_fees DOUBLE PRECISION DEFAULT NULL, total_fees_count INT DEFAULT NULL, other_credit_amount DOUBLE PRECISION DEFAULT NULL, other_credit_count INT DEFAULT NULL, other_debit_amount DOUBLE PRECISION DEFAULT NULL, other_debit_count INT DEFAULT NULL, total_credit_amount DOUBLE PRECISION DEFAULT NULL, total_credit_count INT DEFAULT NULL, total_debit_amount DOUBLE PRECISION DEFAULT NULL, total_debit_count INT DEFAULT NULL, total_transaction_amount DOUBLE PRECISION DEFAULT NULL, total_transaction_count INT DEFAULT NULL, closing_balance DOUBLE PRECISION DEFAULT NULL, closing_card_count INT DEFAULT NULL, INDEX work_of_date_idx (work_of_date), INDEX bin_currency_alpha_idx (bin_currency_alpha), INDEX issuer_client_id_idx (issuer_client_id), INDEX bank_name_idx (bank_name), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE fis_monetary (id INT AUTO_INCREMENT NOT NULL, file_name VARCHAR(255) NOT NULL, file_processor_name VARCHAR(11) DEFAULT NULL, file_report_feed_name VARCHAR(9) DEFAULT NULL, file_date DATE NOT NULL, work_of_date DATE NOT NULL, issuer_client_id VARCHAR(10) DEFAULT NULL, client_name VARCHAR(30) DEFAULT NULL, sub_program_id VARCHAR(10) DEFAULT NULL, sub_program_name VARCHAR(80) DEFAULT NULL, bin VARCHAR(6) DEFAULT NULL, bin_currency_alpha VARCHAR(3) DEFAULT NULL, bin_currency_code VARCHAR(3) DEFAULT NULL, bank_name VARCHAR(23) DEFAULT NULL, pan VARCHAR(19) DEFAULT NULL, card_number VARCHAR(19) DEFAULT NULL, authorization_amount DOUBLE PRECISION DEFAULT NULL, authorization_code VARCHAR(6) DEFAULT NULL, txn_local_amount DOUBLE PRECISION DEFAULT NULL, txn_usd_amount INT DEFAULT NULL, txn_loc_date_time DATETIME DEFAULT NULL, txn_sign VARCHAR(2) DEFAULT NULL, transaction_currency_code VARCHAR(5) DEFAULT NULL, transaction_currency_alpha VARCHAR(3) DEFAULT NULL, txn_type_code VARCHAR(5) DEFAULT NULL, reason_code VARCHAR(10) DEFAULT NULL, derived_request_code VARCHAR(10) DEFAULT NULL, response_code VARCHAR(10) DEFAULT NULL, match_status_code VARCHAR(10) DEFAULT NULL, match_type_code VARCHAR(10) DEFAULT NULL, initial_load_date_flag DATE DEFAULT NULL, mcc VARCHAR(10) DEFAULT NULL, merchant_currency_alpha VARCHAR(3) DEFAULT NULL, merchant_currency_code VARCHAR(3) DEFAULT NULL, merchant_name VARCHAR(40) DEFAULT NULL, merchant_number VARCHAR(16) DEFAULT NULL, reference_number VARCHAR(17) DEFAULT NULL, payment_method_id VARCHAR(10) DEFAULT NULL, settle_amount DOUBLE PRECISION DEFAULT NULL, wcs_utc_post_date DATETIME DEFAULT NULL, source_code VARCHAR(10) DEFAULT NULL, acquirer_reference_number VARCHAR(23) DEFAULT NULL, acquirer_id VARCHAR(18) DEFAULT NULL, address_verification_response VARCHAR(1) DEFAULT NULL, adjust_amount DOUBLE PRECISION DEFAULT NULL, authorization_response VARCHAR(35) DEFAULT NULL, avs_information VARCHAR(80) DEFAULT NULL, denomination DOUBLE PRECISION DEFAULT NULL, direct_access_number VARCHAR(13) DEFAULT NULL, card_number_proxy VARCHAR(30) DEFAULT NULL, fudge_amt DOUBLE PRECISION DEFAULT NULL, match_status_description VARCHAR(80) DEFAULT NULL, match_type_description VARCHAR(80) DEFAULT NULL, mcc_description VARCHAR(80) DEFAULT NULL, merchant_zip VARCHAR(9) DEFAULT NULL, merchant_city VARCHAR(25) DEFAULT NULL, merchant_country_code VARCHAR(3) DEFAULT NULL, merchant_country_name VARCHAR(45) DEFAULT NULL, merchant_province VARCHAR(30) DEFAULT NULL, merchant_state VARCHAR(2) DEFAULT NULL, merchant_street VARCHAR(80) DEFAULT NULL, pin VARCHAR(1) DEFAULT NULL, pos_data VARCHAR(26) DEFAULT NULL, pos_entry_code VARCHAR(10) DEFAULT NULL, pos_entry_description VARCHAR(80) DEFAULT NULL, purse_no VARCHAR(10) DEFAULT NULL, reason_code_description VARCHAR(80) DEFAULT NULL, derived_request_code_description VARCHAR(80) DEFAULT NULL, response_description VARCHAR(80) DEFAULT NULL, retrieval_ref_no VARCHAR(23) DEFAULT NULL, reversed VARCHAR(1) DEFAULT NULL, source_description VARCHAR(80) DEFAULT NULL, terminal_number VARCHAR(8) DEFAULT NULL, txn_type_name VARCHAR(50) DEFAULT NULL, user_id VARCHAR(30) DEFAULT NULL, user_first_name VARCHAR(15) DEFAULT NULL, user_last_name VARCHAR(15) DEFAULT NULL, wcs_local_post_date DATETIME DEFAULT NULL, comment VARCHAR(512) DEFAULT NULL, client_reference_number VARCHAR(40) DEFAULT NULL, client_specific_id VARCHAR(36) DEFAULT NULL, actual_request_code VARCHAR(10) DEFAULT NULL, actual_request_code_description VARCHAR(80) DEFAULT NULL, cardholder_client_unique_id VARCHAR(30) DEFAULT NULL, pan_proxy_number VARCHAR(30) DEFAULT NULL, txn_uid VARCHAR(36) DEFAULT NULL, purse_name VARCHAR(40) DEFAULT NULL, purse_status VARCHAR(10) DEFAULT NULL, purse_creation_date DATE DEFAULT NULL, purse_effective_date DATE DEFAULT NULL, purse_expiration_date DATE DEFAULT NULL, purse_status_date DATE DEFAULT NULL, association_source VARCHAR(3) DEFAULT NULL, reason_id VARCHAR(5) DEFAULT NULL, reason_description VARCHAR(40) DEFAULT NULL, variance DOUBLE PRECISION DEFAULT NULL, process_code VARCHAR(6) DEFAULT NULL, token_unique_reference_id VARCHAR(64) DEFAULT NULL, pan_unique_reference_id VARCHAR(64) DEFAULT NULL, token_transaction_id VARCHAR(64) DEFAULT NULL, token_status VARCHAR(1) DEFAULT NULL, token_status_description VARCHAR(50) DEFAULT NULL, network_reference_id VARCHAR(50) DEFAULT NULL, multi_clearing_indication VARCHAR(4) DEFAULT NULL, authorization_balance DOUBLE PRECISION DEFAULT NULL, settle_balance DOUBLE PRECISION DEFAULT NULL, wcs_local_inserted DATETIME DEFAULT NULL, wcs_utc_inserted DATETIME DEFAULT NULL, wcs_utc_updated DATETIME DEFAULT NULL, discount_amount DOUBLE PRECISION DEFAULT NULL, reward_amount INT DEFAULT NULL, reward_status VARCHAR(20) DEFAULT NULL, INDEX file_name_idx (file_name), INDEX work_of_date_idx (work_of_date), INDEX bin_currency_alpha_idx (bin_currency_alpha), INDEX client_name_idx (client_name), INDEX issuer_client_id_idx (issuer_client_id), INDEX bank_name_idx (bank_name), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE fis_account_balance_base (id INT AUTO_INCREMENT NOT NULL, pan_proxy_number VARCHAR(30) DEFAULT NULL, bin_currency_alpha VARCHAR(3) DEFAULT NULL, bin_currency_code VARCHAR(3) DEFAULT NULL, closing_balance DOUBLE PRECISION DEFAULT NULL, bin VARCHAR(6) DEFAULT NULL, pan VARCHAR(19) DEFAULT NULL, bank_name VARCHAR(23) DEFAULT NULL, issuer_client_id VARCHAR(10) DEFAULT NULL, client_name VARCHAR(30) DEFAULT NULL, person_id VARCHAR(10) DEFAULT NULL, client_unique_id VARCHAR(30) DEFAULT NULL, update_date DATE DEFAULT NULL, opt_in_at DATE DEFAULT NULL, cashback_status VARCHAR(255) DEFAULT NULL, UNIQUE INDEX UNIQ_32C0247E9E7A74E (pan_proxy_number), INDEX pan_proxy_number_idx (pan_proxy_number), INDEX bin_currency_alpha_idx (bin_currency_alpha), INDEX client_name_idx (client_name), INDEX issuer_client_id_idx (issuer_client_id), INDEX issuer_client_id_client_name_idx (issuer_client_id, client_name), INDEX bank_name_idx (bank_name), INDEX opt_in_at_idx (opt_in_at), INDEX cashback_status_idx (cashback_status), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
CREATE TABLE fis_account_balance (id INT AUTO_INCREMENT NOT NULL, file_name VARCHAR(255) NOT NULL, file_processor_name VARCHAR(11) DEFAULT NULL, file_report_feed_name VARCHAR(9) DEFAULT NULL, file_date DATE NOT NULL, work_of_date DATE NOT NULL, issuer_client_id VARCHAR(10) DEFAULT NULL, client_name VARCHAR(30) DEFAULT NULL, bin VARCHAR(6) DEFAULT NULL, bank_name VARCHAR(23) DEFAULT NULL, pan VARCHAR(19) DEFAULT NULL, opening_balance DOUBLE PRECISION DEFAULT NULL, total_value_load_amount DOUBLE PRECISION DEFAULT NULL, total_value_load_count INT DEFAULT NULL, total_purchase_amount DOUBLE PRECISION DEFAULT NULL, total_purchase_count INT DEFAULT NULL, total_otc_amount DOUBLE PRECISION DEFAULT NULL, total_otc_count INT DEFAULT NULL, total_atm_withdrawal_amount DOUBLE PRECISION DEFAULT NULL, total_atm_withdrawal_count INT DEFAULT NULL, total_return_amount DOUBLE PRECISION DEFAULT NULL, total_return_count INT DEFAULT NULL, total_adjustment_amount DOUBLE PRECISION DEFAULT NULL, total_adjustment_count INT DEFAULT NULL, total_fees DOUBLE PRECISION DEFAULT NULL, total_fees_count INT DEFAULT NULL, other_credit_amount DOUBLE PRECISION DEFAULT NULL, other_credit_count INT DEFAULT NULL, other_debit_amount DOUBLE PRECISION DEFAULT NULL, other_debit_count INT DEFAULT NULL, total_credit_amount DOUBLE PRECISION DEFAULT NULL, total_credit_count INT DEFAULT NULL, total_debit_amount DOUBLE PRECISION DEFAULT NULL, total_debit_count INT DEFAULT NULL, total_transaction_amount DOUBLE PRECISION DEFAULT NULL, total_transaction_count INT DEFAULT NULL, closing_balance DOUBLE PRECISION DEFAULT NULL, bin_currency_alpha VARCHAR(3) DEFAULT NULL, bin_currency_code VARCHAR(3) DEFAULT NULL, pan_proxy_number VARCHAR(30) DEFAULT NULL, person_id VARCHAR(10) DEFAULT NULL, client_unique_id VARCHAR(30) DEFAULT NULL, INDEX file_name_idx (file_name), INDEX work_of_date_idx (work_of_date), INDEX bin_currency_alpha_idx (bin_currency_alpha), INDEX closing_balance_idx (closing_balance), INDEX bin_currency_alpha_work_of_date_idx (bin_currency_alpha, work_of_date), INDEX client_name_idx (client_name), INDEX issuer_client_id_idx (issuer_client_id), INDEX issuer_client_id_client_name_idx (issuer_client_id, client_name), INDEX bank_name_idx (bank_name), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
ALTER TABLE user_card_snap ADD CONSTRAINT FK_DAB792D812C1842A FOREIGN KEY (user_card_id) REFERENCES user_card (id);
ALTER TABLE email_recipient ADD CONSTRAINT FK_670F6462A832C1C9 FOREIGN KEY (email_id) REFERENCES email (id);
ALTER TABLE error ADD CONSTRAINT FK_5DDDBC71A76ED395 FOREIGN KEY (user_id) REFERENCES users (id);
ALTER TABLE ip_usage ADD CONSTRAINT FK_ADFFA79D67B3B43D FOREIGN KEY (users_id) REFERENCES users (id) ON DELETE CASCADE;
ALTER TABLE user_velocity ADD CONSTRAINT FK_5E5532A9A76ED395 FOREIGN KEY (user_id) REFERENCES users (id);
ALTER TABLE user_velocity ADD CONSTRAINT FK_5E5532A912C1842A FOREIGN KEY (user_card_id) REFERENCES user_card (id);
ALTER TABLE user_velocity ADD CONSTRAINT FK_5E5532A9EF829B1E FOREIGN KEY (velocity_id) REFERENCES velocity (id);
ALTER TABLE user_velocity ADD CONSTRAINT FK_5E5532A9520A5179 FOREIGN KEY (account_balance_id) REFERENCES fis_account_balance (id);
ALTER TABLE user_velocity ADD CONSTRAINT FK_5E5532A92F8B0EB2 FOREIGN KEY (authorization_id) REFERENCES fis_authorization (id);
ALTER TABLE user_velocity ADD CONSTRAINT FK_5E5532A9B2BA99B FOREIGN KEY (monetary_id) REFERENCES fis_monetary (id);
ALTER TABLE user_velocity ADD CONSTRAINT FK_5E5532A9C7B47CB5 FOREIGN KEY (dispute_id) REFERENCES fis_dispute (id);
ALTER TABLE user_velocity ADD CONSTRAINT FK_5E5532A978816E4A FOREIGN KEY (non_monetary_id) REFERENCES fis_non_monetary (id);
ALTER TABLE fis_program_type ADD CONSTRAINT FK_F03E4D21727ACA70 FOREIGN KEY (parent_id) REFERENCES fis_program_type (id);
ALTER TABLE alert_job CHANGE total_sent total_sent INT NOT NULL, CHANGE total_viewed total_viewed INT NOT NULL;
# ALTER TABLE notes ADD CONSTRAINT FK_11BA68CDE12AB56 FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE SET NULL;
# ALTER TABLE webhook_event ADD ach_transaction VARCHAR(255) DEFAULT NULL, CHANGE bank_account_id bank_account_id VARCHAR(255) DEFAULT NULL, CHANGE institution_name institution_name VARCHAR(255) DEFAULT NULL, CHANGE account_last_4 account_last_4 INT DEFAULT NULL, CHANGE onboarding_status onboarding_status VARCHAR(255) DEFAULT NULL, CHANGE is_manually_linked is_manually_linked TINYINT(1) DEFAULT NULL;
CREATE TABLE fis_statistics_balance (id INT AUTO_INCREMENT NOT NULL, hash_key VARCHAR(500) DEFAULT NULL, context VARCHAR(100) DEFAULT NULL, file_processor_name VARCHAR(3) DEFAULT NULL, work_of_date DATE NOT NULL, bank_name VARCHAR(255) DEFAULT NULL, issuer_client_id INT NOT NULL, client_name VARCHAR(255) NOT NULL, bin_currency_alpha VARCHAR(5) NOT NULL, bin INT DEFAULT NULL, opening_balance_total NUMERIC(19, 4) NOT NULL, opening_balance_average NUMERIC(19, 4) NOT NULL, closing_balance_total NUMERIC(19, 4) NOT NULL, closing_balance_average NUMERIC(19, 4) NOT NULL, pan_count INT NOT NULL, record_count INT NOT NULL, total_value_load_amount NUMERIC(19, 4) NOT NULL, total_value_load_count INT NOT NULL, pan_proxy_number_count INT NOT NULL, total_fees_amount NUMERIC(19, 4) NOT NULL, total_fees_count INT NOT NULL, total_credit_amount NUMERIC(19, 4) NOT NULL, total_credit_count INT NOT NULL, total_debit_amount NUMERIC(19, 4) NOT NULL, total_debit_count INT NOT NULL, sub_program_id INT NOT NULL, INDEX date_idx (work_of_date), INDEX currency_processor_idx (file_processor_name, bin_currency_alpha), INDEX date_currency_processor_idx (file_processor_name, bin_currency_alpha, work_of_date), INDEX bank_name_idx (bank_name), INDEX client_name_idx (client_name), INDEX issuer_client_id_idx (issuer_client_id), INDEX md5_hash_key_idx (hash_key), INDEX context_idx (context), INDEX md5_hash_key_context_idx (hash_key, context), INDEX bin_idx (bin), INDEX sub_program_id_idx (sub_program_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;
CREATE TABLE fis_statistics_monetary (id INT AUTO_INCREMENT NOT NULL, hash_key VARCHAR(500) NOT NULL, context VARCHAR(100) NOT NULL, file_processor_name VARCHAR(3) DEFAULT NULL, work_of_date DATE NOT NULL, bank_name VARCHAR(255) DEFAULT NULL, issuer_client_id INT NOT NULL, client_name VARCHAR(255) NOT NULL, bin_currency_alpha VARCHAR(5) NOT NULL, bin INT DEFAULT NULL, pan_count INT NOT NULL, record_count INT NOT NULL, pan_proxy_number_count INT NOT NULL, txn_usd_amount NUMERIC(19, 4) NOT NULL, txn_type_name VARCHAR(255) NOT NULL, txn_sign INT DEFAULT NULL, actual_request_code INT DEFAULT NULL, mcc INT DEFAULT NULL, mcc_description VARCHAR(255) DEFAULT NULL, sub_program_id INT NOT NULL, INDEX date_idx (work_of_date), INDEX currency_processor_idx (file_processor_name, bin_currency_alpha), INDEX date_currency_processor_idx (file_processor_name, bin_currency_alpha, work_of_date), INDEX bank_name_idx (bank_name), INDEX client_name_idx (client_name), INDEX issuer_client_id_idx (issuer_client_id), INDEX md5_hash_key_idx (hash_key), INDEX context_idx (context), INDEX bin_idx (bin), INDEX txn_type_name_idx (txn_type_name), INDEX sub_program_id_idx (sub_program_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;
CREATE TABLE fis_statistics_cards (id INT AUTO_INCREMENT NOT NULL, file_processor_name VARCHAR(11) DEFAULT NULL, bin VARCHAR(6) DEFAULT NULL, bank_name VARCHAR(23) DEFAULT NULL, sub_program_id VARCHAR(10) DEFAULT NULL, issuer_client_id VARCHAR(10) DEFAULT NULL, pan_proxy_number VARCHAR(30) DEFAULT NULL, INDEX bank_name_idx (bank_name), INDEX issuer_client_id_idx (issuer_client_id), INDEX sub_program_id_idx (sub_program_id), INDEX bin_idx (bin), INDEX pan_proxy_number_idx (pan_proxy_number), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;
CREATE TABLE fis_statistics_card_metrics (id INT AUTO_INCREMENT NOT NULL, work_of_date DATE NOT NULL, has_monetary INT NOT NULL, has_credit INT NOT NULL, has_spend INT NOT NULL, pan_proxy_number VARCHAR(255) NOT NULL, bin VARCHAR(255) DEFAULT NULL, sub_program_id VARCHAR(255) DEFAULT NULL, issuer_client_id VARCHAR(255) DEFAULT NULL, INDEX date_idx (work_of_date), INDEX has_monetary_idx (has_monetary), INDEX has_credit_idx (has_credit), INDEX has_spend_idx (has_spend), INDEX bin (bin), INDEX issuer_client_id (issuer_client_id), INDEX sub_program_id (sub_program_id), INDEX pan_proxy_number_idx (pan_proxy_number), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;
CREATE TABLE fis_statistics_transactions (id INT AUTO_INCREMENT NOT NULL, hash_key VARCHAR(500) NOT NULL, context VARCHAR(100) NOT NULL, file_processor_name VARCHAR(3) NOT NULL, work_of_date DATE NOT NULL, bank_name VARCHAR(255) NOT NULL, issuer_bank_id INT DEFAULT NULL, client_name VARCHAR(255) NOT NULL, issuer_client_id INT NOT NULL, bin_currency_alpha VARCHAR(5) NOT NULL, bin INT NOT NULL, pan_proxy_number_count INT NOT NULL, sub_program_id INT NOT NULL, tran_type VARCHAR(100) NOT NULL, total_amount NUMERIC(19, 4) NOT NULL, total_count INT NOT NULL, INDEX date_idx (work_of_date), INDEX currency_processor_idx (file_processor_name, bin_currency_alpha), INDEX date_currency_processor_idx (file_processor_name, bin_currency_alpha, work_of_date), INDEX bankName_idx (bank_name), INDEX clientName_idx (client_name), INDEX issuerClientId_idx (issuer_client_id), INDEX md5hashKey_idx (hash_key), INDEX context_idx (context), INDEX bin_idx (bin), INDEX tran_type_idx (tran_type), INDEX sub_program_id_idx (sub_program_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;
CREATE TABLE fis_statistics_negative_balance (id INT AUTO_INCREMENT NOT NULL, hash_key VARCHAR(500) DEFAULT NULL, context VARCHAR(100) DEFAULT NULL, file_processor_name VARCHAR(3) DEFAULT NULL, work_of_date DATE NOT NULL, bank_name VARCHAR(255) DEFAULT NULL, issuer_client_id INT NOT NULL, client_name VARCHAR(255) NOT NULL, bin_currency_alpha VARCHAR(5) NOT NULL, bin INT DEFAULT NULL, opening_balance_total NUMERIC(19, 4) NOT NULL, opening_balance_average NUMERIC(19, 4) NOT NULL, closing_balance_total NUMERIC(19, 4) NOT NULL, closing_balance_average NUMERIC(19, 4) NOT NULL, pan_count INT NOT NULL, record_count INT NOT NULL, total_value_load_amount NUMERIC(19, 4) NOT NULL, total_value_load_count INT NOT NULL, pan_proxy_number_count INT NOT NULL, total_fees_amount NUMERIC(19, 4) NOT NULL, total_fees_count INT NOT NULL, total_credit_amount NUMERIC(19, 4) NOT NULL, total_debit_amount NUMERIC(19, 4) NOT NULL, sub_program_id INT NOT NULL, INDEX date_idx (work_of_date), INDEX currency_processor_idx (file_processor_name, bin_currency_alpha), INDEX date_currency_processor_idx (file_processor_name, bin_currency_alpha, work_of_date), INDEX bank_name_idx (bank_name), INDEX client_name_idx (client_name), INDEX issuer_client_id_idx (issuer_client_id), INDEX md5_hash_key_idx (hash_key), INDEX context_idx (context), INDEX bin_idx (bin), INDEX sub_program_id_idx (sub_program_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;
CREATE TABLE fis_statistics_dispute (id INT AUTO_INCREMENT NOT NULL, hash_key VARCHAR(500) DEFAULT NULL, context VARCHAR(100) DEFAULT NULL, file_processor_name VARCHAR(3) DEFAULT NULL, work_of_date DATE NOT NULL, bank_name VARCHAR(255) DEFAULT NULL, issuer_client_id INT DEFAULT NULL, client_name VARCHAR(255) DEFAULT NULL, bin_currency_alpha VARCHAR(5) NOT NULL, bin INT DEFAULT NULL, dispute_status VARCHAR(255) DEFAULT NULL, disputed_amount NUMERIC(19, 4) NOT NULL, pan_count INT NOT NULL, record_count INT NOT NULL, pan_proxy_number_count INT NOT NULL, sub_program_id INT NOT NULL, INDEX date_idx (work_of_date), INDEX currency_processor_idx (file_processor_name, bin_currency_alpha), INDEX date_currency_processor_idx (file_processor_name, bin_currency_alpha, work_of_date), INDEX bank_name_idx (bank_name), INDEX client_name_idx (client_name), INDEX issuer_client_id_idx (issuer_client_id), INDEX md5_hash_key_idx (hash_key), INDEX context_idx (context), INDEX bin_idx (bin), INDEX sub_program_id_idx (sub_program_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;
