# Notes to contribute

## Run in Docker

Refer to https://github.com/dunglas/symfony-docker

> docker compose build

OR

> docker compose build --pull --no-cache

Then:

> docker compose up

When it's not needed:

> docker compose down --remove-orphans

If your database have a different data directory, you can start by:

> DATABASE_DATA_DIR=/Your/path/to/Docker/mariadb/data docker-compose up -d

For instance, my startup command:

```shell
DATABASE_DATA_DIR=/Users/<USER>/Projects/Docker/mariadb/data \
SPAN_UPLOAD_DIR=/Users/<USER>/Projects/TCSPAN/var/upload \
SPAN_SECURE_DIR=/Users/<USER>/Projects/TCSPAN/var/secure \
SPAN_LOG_DIR=/Users/<USER>/Projects/TCSPAN/var/log \
SERVER_NAME="span.local usu.span.local mex.span.local spendr.span.local leaflink.span.local wilen.span.local pto.span.local fis.span.local essolo.span.local cow.span.local skux.span.local" \
docker-compose up -d
```

## Run local dev environment

> symfony server:start

## Check security vulnerabilities

> symfony check:security

## How to make new entity

> php bin/console make:entity

#### Regenerate an entity to add new get/set methods
> php bin/console make:entity App\\Entity\\Subscription --regenerate
> php bin/console make:entity CoreBundle\\Entity\\EntityName --regenerate

## Start the queue processor

> php bin/console enqueue:consume --setup-broker --message-limit=50 --time-limit="now + 10 minutes" --client=privacy --env=prod --no-debug

## How to make new migration

1. Run `php bin/console d:m:diff --filter-expression=~your_changed_tables~ -vv` to automatically generate the migrations according to the `doctrine:schedule:update --dump-sql` command, but only with the tables you specified with the filter.
2. Adjust the generated migration code as you need if necessary. Read it carefully to ensure that all the SQLs are expected and correct.
3. Run `php bin/console d:m:migrate --no-interaction --query-time -vv` locally to test before committing your code. This command will be automatically executed when deploying changes to the servers using DeployBot.
4. (Optional) If the database change is not what you want, you can run `php bin/console d:m:migrate prev -vv` to revert the changes.
5. Commit and push to `dev` branch and test on the staging server.
6. Review the code by another developer.
7. Merge to `master` branch and deploy on the production server.

## Setup custom domains with SSL support

1. Add DNS record: domain -> our server's IP
2. Configure the platform branding to add the custom domain. https://staging.virtualcards.us/admin#/a/system/platform-branding, `Customize Apperance` of your platform.
3. Enter the custom domain, and save. More custom domains can be entered in the `Alternative domains`.
4. SSH to server, `cd /etc/nginx/sites-available`
5. `cp virtualcards.conf your_platform.conf` and `vim your_platform.conf`
6. Change `listen` to `80`, without `ssl`. change `server_name` to the custom domains. comment the `ssl_certificate` and `ssl_certificate_key`. save and quit.
7. `cd ../sites-enabled` and `ln -s  ../sites-available/your_platform.conf your_platform.conf`
8. Run `sudo certbot --nginx`, and choose your new domain, waiting for the execution
9. Add the new domain to Google captcha console.

## Fix your code before committing changes

> php bin/fixer fix src/

## Set up the prod web server

https://symfony.com/doc/current/setup/web_server_configuration.html

## How to upgrade the patch or minor versions

https://symfony.com/doc/current/setup/upgrade_patch.html
https://symfony.com/doc/current/setup/upgrade_minor.html

## List a package's configuration

> php bin/console config:dump-reference fos_rest

## Code static scan

```shell
vendor/bin/phpstan
vendor/bin/phpstan analyse --generate-baseline phpstan-baseline.php
php vendor/bin/phpstan clear-result-cache
```

## USU

### Affiliate Apply Script

```html
<script>
    const script = document.createElement('script');
    script.src = 'https://account.usunlocked.com/static/affiliate-apply.js?t=' + Date.now();
    script.type = 'text/javascript';
    script.async = true;
    document.head.appendChild(script);
</script>
```

## Tests

##### Unit test

> vendor/bin/codecept generate:test Unit TransferMex/Module/NameTest
> vendor/bin/codecept run --steps Unit --html
> vendor/bin/codecept run Unit Core/Security/SslTest::testDecryptionCache --steps --debug
