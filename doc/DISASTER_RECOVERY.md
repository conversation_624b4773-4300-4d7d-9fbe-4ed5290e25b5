# Disaster Recovery Handbook

## System backups

### EC2
* Automatic daily backup
* Manually cross regions backup monthly

### RDS
* Automatic recovery point
* Automatic daily backup in AWS
* Automatic daily backup from SPAN with core data
* Builtin cross regions backup, but still suggest to copy the snapshot to another region manually monthly

### ElastiCache
* Automatic daily backup
* Backup to S3 monthly

## Possible disasters

### EC2 instance terminated or crashed
* We'll have to recreate a new instance from the latest AMI, with the same Elastic IP.

### Database crashed or data erased
* We'll have to recreate a new instance from the latest snapshot. 

### Redis cache crashed or data flushed
* We'll have to recreate a new instance from the latest backup or even from the rdb files exported to S3 before.

## How to recover from backups

### Recover EC2
1. Find the latest AMI in the `N. Virginia` region, if not, find in the `Ohio` region.
   1. https://us-east-1.console.aws.amazon.com/ec2/home?region=us-east-1#Images:visibility=owned-by-me;sort=desc:creationDate
1. Create a new instance from it. The required configurations:
   1. Use the Elastic IP (`************`).
   2. Lowest instance type: `c5.4xlarge`
   3. `Ubuntu 22.04`+
   4. Security group name: `launch-wizard-1`
   5. VPC `vpc-bb7587c6` and subnet `subnet-9a9d31bb`

### Recover database
1. Find the latest snapshots from the below link. If it's not in the Virginia region, look for in other regions like Ohio.
   1. https://us-east-1.console.aws.amazon.com/rds/home?region=us-east-1#snapshots-list:tab=automated
2. Create a new instance from it. The required configurations:
   1. Lowest instance type: `db.r6g.2xlarge`
   2. Multi-AZ
   3. MariaDB 10.4.32
   4. Option group: `default:mariadb-10-4`
   5. Parameter group: `custom-maria`
3. Change the host names in SPAN to the new instance.
   1. .env.local
   2. parameters_xxx.conf
4. Make a deployment

### Recover Redis
1. Find the latest backup or from the S3 bucket `tern-spendr-shared`.
   1. https://us-east-1.console.aws.amazon.com/elasticache/home?region=us-east-1#/snapshots
2. Create a new Redis OSS cache from the backup.
3. Change the Redis DSN:
    1. .env.local
    2. parameters_xxx.conf
4. Make a deployment
