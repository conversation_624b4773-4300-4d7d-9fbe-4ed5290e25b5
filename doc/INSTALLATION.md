# Installation

### Basic

1. nginx
2. php7.3-fpm
3. mariadb 10.1.48
4. memcached, redis-server
5. supervisor

### PHP Extensions

* pdo, mysqli
* xml, dom, simplexml, xmlreader, xmlwriter, xsl, wddx
* bcmath, curl, soap
* gd, imagick
* igbinary, mbstring, msgpack, shmop
* memcache, memcached
* redis, xdebug, zip

### Others

* npm: secure-spreadsheet

### DataDog

* Agent: https://app.datadoghq.com/account/settings#ubuntu
* Other integrations: https://app.datadoghq.com/account/settings#integrations
  * Cloudflare
  * Memcached, redis
  * MySQL
  * nginx
  * php-fpm: ddtrace, ddtrace-custom
  * Slack

### Setup custom domains with SSL support

1. Add DNS record: domain -> our server's IP
2. Configure the platform branding to add the custom domain. https://staging.virtualcards.us/admin#/a/system/platform-branding, `Customize Apperance` of your platform.
3. Enter the custom domain, and save. More custom domains can be entered in the `Alternative domains`.
4. SSH to server, `cd /etc/nginx/sites-available`
5. `cp virtualcards.conf your_platform.conf` and `vim your_platform.conf`
6. Change `listen` to `80`, without `ssl`. change `server_name` to the custom domains. comment the `ssl_certificate` and `ssl_certificate_key`. save and quit.
7. `cd ../sites-enabled` and `ln -s  ../sites-available/your_platform.conf your_platform.conf`
8. Run `sudo certbot --nginx`, and choose your new domain, waiting for the execution
9. Add the new domain to Google captcha console. 
10. Done

### Database migrations

1. Run `php bin/console d:m:diff --filter-expression=~your_changed_tables~ -vv` to automatically generate the migrations according to the `doctrine:schedule:update --dump-sql` command, but only with the tables you specified with the filter.
2. Adjust the generated migration code as you need if necessary. Read it carefully to ensure that all the SQLs are expected and correct.
3. Run `php bin/console d:m:migrate --no-interaction --query-time -vv` locally to test before committing your code. This command will be automatically executed when deploying changes to the servers using DeployBot.
4. (Optional) If the database change is not what you want, you can run `php bin/console d:m:migrate prev -vv` to revert the changes.
5. Commit and push to `dev` branch and test on the staging server.
6. Review the code by another developer.
7. Merge to `master` branch and deploy on the production server.
