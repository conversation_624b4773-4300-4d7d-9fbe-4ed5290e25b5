# Installation Instructions

## Primary Softwares

- Install Homebrew
- Install Apache
  - `brew install httpd`
- Install PHP7.3
  - `brew install php@7.3`
- Install Redis
  - Install php-redis `pecl install redis`
  - Restart php `brew services restart php@7.3`
  - Restart httpd `brew services restart httpd`
- Install Mariadb
  - Create Database
    - `CREATE DATABASE tern_span_live;`
    - `USE tern_span_live;`
  - Add User
    - `CREATE USER 'you'@'localhost' IDENTIFIED BY 'password';`
  - Configure User Grant
    - `GRANT ALL PRIVILEGES ON *.* to 'you'@'localhost';`
  - Flush Privileges - `FLUSH PRIVILEGES;`
- Updte Config File
  - `parameters.yml`
    - `database_user: you`
    - `database_password: password`
- Install NVM
  - `brew install nvm`
- Install Node Version `12.6`
  - `nvm install 12.6`
  - `nvm use 12.6`

## Create Necessary Directories

```
sudo mkdir /var/cache
sudo mkdir /var/cache/span
sudo mkdir /var/cache/span/dev
sudo mkdir /var/log
sudo mkdir /var/upload
sudo mkdir /var/upload/span
sudo mkdir /var/secure
sudo mkdir /var/secure/span
```

### Grant Permissions to Directories

```
sudo chown you:staff /var/cache/span
sudo chown you:staff /var/cache/span/dev
sudo chown you:staff /var/upload/span
sudo chown you:staff /var/secure/span
```

## Composer Memory Issue

> NOTE: If you get the warning "PHP Fatal error: Allowed memory size ..." you will need to override the memory settings of `composer` using the command `COMPOSER_MEMORY_LIMIT=-1 composer ...` at the beginning of your command.


## Running on Local
There are some nuances to running the application locally, namely the need to recompile from time-to-time if your environment settings prevent automated methods from working. In particular, there are times on Macintosh computers using Apple CPUs (e.g.: M1, M1X, etc.) that you will need to recompile `node-sass` or perform other operations to ensure that the application works as expected.

### Recompile Application

#### Backend
cd /path/to/your/install/of/tern/span/span-web
COMPOSER_ALLOW_SUPERUSER=1 composer install --no-scripts --apcu-autoloader
COMPOSER_ALLOW_SUPERUSER=1 composer run-script build-bootstrap
COMPOSER_ALLOW_SUPERUSER=1 composer dump-autoload --optimize --no-dev --classmap-authoritative
php bin/fix_vendor
php bin/console cache:clear --env=prod --no-debug
php bin/console d:s:u --dump-sql --force

#### Frontend
cd /path/to/your/install/of/tern/span/frontend/
npm install
npm run build
sleep 5
rm -rf ../span-web/web/mobile
mv dist ../span-web/web/mobile
cd ../span-web
php -d memory_limit=1200M bin/console cache:clear --env=dev

#### Magic Users

For local development, note that the API_KEY value in the USERS table must be updated to prevent the "API Token Expired" warning from showing when running the app. The IDs for these users are 500016626 (Staging) and 500018861 (Prod)

```
UPDATE users SET api_token = '<JWT_VALUE>' WHERE id = 500016626;
```