# Upgrading to Symfony 6.3 & PHP 8.2

## The `span-web` directory

* `app`: To be removed. Need to move the configurations and code to the new directories.
* `assets`: Keep. Find out where they are required.
* `bin`: Keep but replace the old files.
* `node_modules`: Keep.
* `src`: Keep but replace the old files.
* `tests`: Keep but replace the old files.
* `var`: Keep.
* `vendor`: Keep but replace the old files.
* `web`: Keep but use the new `index.php`.
* All other files: Merge and replace.

## Note

* Remember to move the migration records (use command `span:db:change-migrate`) before executing the migrate command, to avoid executing the migrations again.

## Steps

1. 
