# Development Environment Setup

## Step 1: Installation

### Homebrew (Mac OS only)
Many of the required dependencies are most easily installed via Homebrew on Mac OS. If using Linux, these packages are likely available for your distribution of choice.

Before installing Homebrew please verify you have xcode tools installed.
To do so, run this command: `xcode-select --install`

To install Homebrew via the terminal, please use the following command:

`/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/master/install.sh)"`

###Dependencies
* PHP 7.3 
    * command: `brew install php@7.3`
* Apache Web Server
    * command: ``
    
    
## When configuring new environments, the new url must be added to:
`usr/local/etc/httpd/extra/httpd.vhosts.conf`
`etc/hosts`
