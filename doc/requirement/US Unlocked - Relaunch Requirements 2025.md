# US Unlocked - Relaunch Requirements 2025

## 📋 Project Overview

**Project Name:** Operation Relaunch US Unlocked 

**GTM Business Owner(s):** <PERSON><PERSON> & <PERSON>

**Target Launch Date:** June 1, 2025 

**Document Version:** 1.0

[Breakdown Development Tasks](https://www.notion.so/1deed8a5a9dc80428fe9f68f7d68964e?pvs=21)

---

**TABLE OF CONTENTS:**

### 🚀 TL;DR Executive Summary

**Launch Goal:** 

Relaunch US Unlocked by June 1, 2025 with new infra and global onboarding model.

**Target User (ICP):**

Crypto-native individuals outside the U.S., particularly expats and global citizens needing access to U.S.-based merchants.

**Vendors**:

- PayPal: Subscription Setup
    - ⚠️ Will need to know the pricing for this for net revenue reporting and logic. @Brion <PERSON>kowski
- Sumsub: KYC/AML
- Coinflow: Crypto (and card) payment processing
    - Crypto Processing
    - 🆕 International Card Processing for Loads to USU
        - Rates Sheet Coming Soon @<PERSON><PERSON>
- 2000 Charge>Brale>🌧️
    - Allows EU customers to load using their bank accounts.
        - Need Rates @Brion <PERSON>
- Rain (Signify): Card issuing

**Core Revenue Engine:** 

Subscription fees + crypto load margins + interchange + float.

**Core Risk Mitigation:** 

Exclude U.S. residents, strict KYC retry loops, inline fraud benchmarks.

**Top 3 Risk Areas:**

1. Negative balances from prepaid usage

2. Failed KYC with no retry fallback

3. User churn at paywall due to friction

---

# 🎯 Business Objectives

- Relaunch US Unlocked with new card issuing through Rain (Signify)
- Implement KYC verification via Sumsub
    - ❌ **US residents are not eligible.**
    - ✅ **US citizens living abroad are welcome.**
    - ✅ **Residents of all non-sanctioned countries, excluding the United States, are welcome.**
- Process payments through Coinflow
    - **Credit/Debit Card Processing 5% fee**
        - Converted to USDC and loaded to wallet/smart contract
    - **Stablecoins: (cost .5% - 1% fee)**
        - USD Coin (USDC)
        - Tether (USDT)
        - Dai (DAI)
    - **Major Cryptocurrencies: (cost 1% - 2% fee)**
        - Bitcoin (BTC) 👑
        - Ethereum (ETH)
        - Polygon (MATIC)
        - Optimism (OP)
        - Arbitrum (ARB)
        - Avalanche (AVAX)
        - Uniswap (UNI)
        - The Sandbox (SAND)
    
- Card Functions (pricings of the below is still TBD+)
    - ~~Merchant Locked Cards (10 Free overall, $1 a card after)~~
    - ~~One-time Use Cards (3 Free a month, $1 a piece after)~~
    - MVP = One Card and see metrics and transactions.
    - @Hans Zhang We need to decide if we have the data on historical transactions to see if we can limit to a list of approved merchants… if not, use the prior scheme to let the transaction in and permission after fact. We are aiming to keep the implementation as slim as we can.
    - Pause/Un-Pause
    - Set Limits
    - Spend
- Improve Overall Messaging and UX as needed for clean product launch
- Generate revenue through subscription fees, transaction fees, FX, fiat<>crypto, and interchange
- Offer optional freight forwarding services to international customers
- Make sure the money moves correctly for fully automated settlements
    - Crypto will make this part a dream. **sigh*
- Net Revenue Reporting <!-- TODO -->
- Reconciliation Tools & Processes <!-- TODO -->
- Customer Support Training & Documentation Refresh
    - ⚠️ TO DO: Need to architect a refund process and policy.
        - If Subscription Fee(s) are being refunded, this will be done thru PayPal.
        - If a user has an outstanding balance in crypto and we need to issue a refund.
            - First off, best practice is tell them to spend off their balance.
                - TBD process - User provides a payment address for USDC and verify address on blockchain as valid address, user confirms that they have verified this address is accurate.
                    - Payment can be sent from Coinflow or a corp crypto exchange account (Coinbase, Gemini)
                        - Not required for MVP

- GO LIVE 🚀

## 🏗️  MVP vs. Phase 2 Scope Matrix

| **Feature** | **MVP?** | **Phase** |
| --- | --- | --- |
| Virtual Card Issuance | Yes | MVP |
| PayPal Subscriptions | Yes | MVP |
| 2000 Charge/Brale - EU bank loads | Yes | MVP |
| Apple Pay / Google Pay Support | Yes | MVP |
| Freight Forwarding Service | Yes | MVP |
| Visa/MC Load Methods | No | MVP |
| USDC/USDT/DAI Loads via Coinflow | Yes | MVP |
| One-time Use & Merchant-Locked Cards | Yes | MVP |
| Yield Staking on Stablecoin Revenue | No | Phase 2 |
| Device Fingerprinting & Session Revocation | Nice to have | Phase 2 |

---

# 💰 Pricing Model

## 💳 Subscription Options

- **Monthly Plan:** $4.95/month
- **Annual Plan:** $39.95/year

## 👥 About Users & Subscriptions

Users will be required to pay the first month and then pay with paypal for the subscruiption.

If the user cannot pass KYC, we can let them retry, or they can ask for credit.

## 🧾 Subscription Billing Logic

- **Credit Card or PayPal:**
    - Traditional recurring subscription model
    - Automatic billing on subscription date
    - Option to pause subscription
        - Pause card on expiry date
    - Pays $4.95 (monthly) or $39.95 (annual) membership fee

## 💸 Load Fees

- Coinflow
    - Major token loads: 2% ( BITCOIN 👏)
        - We will make 1% profit.
    - Stablecoin loads: 2% (STABLECOINS)
        - We will make 1.5% profit.
    - IF COINFLOW cannot  support us breaking out fees, make 2% for all crypto’s.
- **2000 Charge** > **Brale** > 🌧️
    - Allows EU customers to load using banks
        - @Brion Bonkowski What do we charge today, and what’s it cost?
- Visa/MC/Apple Pay/Google Pay loads: $1 + 5%

## 🧮 Costs & Revenue Matrix

| **Revenue Stream** | **Rate/Assumption** |
| --- | --- |
| Subscription (Annual) | $39.95/year |
| Subscription (Monthly) | $4.95/month |
| Stablecoin Load Fee | 2% (Coinflow cost: 0.5%) |
| Major Token Load Fee | 2% (Coinflow cost: 1%) |
| Interchange Revenue | $0.25 avg per $100 spent |
| Card Creation Cost | $0.20 per card (Rain) |
| KYC Cost (Sumsub) | $1.50 per user avg (est.) |
| PayPal Fees (Subscriptions) | TBD |
| 2000 Charge Fees | TBD |
| Brale Fees | TBD |
| Refund Processing | TBD- Manual until automated |

---

# 👤 User Journey

## 1️⃣ Registration & Initial Payment

- User creates account
- Pays $4.95 (monthly) or $39.95 (annual) membership fee
- Becomes a member

## 2️⃣ KYC Verification

- Member completes KYC through Sumsub
- Verification status is tracked and communicated

## 3️⃣ Card Access & Functionality

- Upon KYC approval, member gains access to:
    - Creating virtual cards
    - Loading funds via cryptocurrency, EU bank accounts, or credit/debit cards
    - Making payments
    - Freight Forwarding Options

---

# 💳 About Card Types

## ⚡ One-Time Use  ****

*$2 per Use (after payment card is made visible, if card unused MFA shows card details)*

### **⚡ One-Time Use Card**

> Perfect for one-time purchases on sites you do not fully trust. Automatically disables after a single use.
> 

## 💸  **Everyday Spend Cards (2 Types)**

*First 10 cards Everyday Spend Cards included with your membership*

*Need more? Add extra cards for $2 each.*

### **🔒 Store Locked Card (Merchant Restricted Spend)**

> Locks to the first merchant that successfully charges it, or to any merchant you manually approve after an initial decline. Best for subscriptions and recurring payments with trusted merchants.
> 

### 🇺🇸 US Unlocked **Card (Works at Approved Online Merchants)**

> A reusable, unrestricted card for general online spending and trusted sites.
> 

---

# 🔧 Technical Requirements

## 1️⃣ Vendor Integrations

### 💳 Rain Integration

- Implement virtual card issuance ($0.20 per card)
- Configure interchange revenue tracking
- ~~Set up digital wallet enablement for Apple Pay/Google Pay~~
- Implement customer migration for existing 2k customers

### 🆔 Sumsub Integration

- Implement KYC flow after payment
- Configure verification status tracking
- Set up notifications for approval/rejection
- Implement ongoing monitoring for compliance

### 🪙 Coinflow Integration

- Configure payment processing for subscription fees
- Set up cryptocurrency payment options (stablecoins, major tokens)
- Implement credit/debit card processing
- Configure chargeback protection

### 💰➡️ 🪙 ➡️ 🌧️  2000 Charge/Brale/Rain

- 2000 Charge - EU Bank Deposit Loads
- Brale - Converts bank deposit to USDC > Rain Prefunding

### 🧾 PayPal Subscriptions

- Create a PayPal subscription
- pay with Credit card for subscription

## 2️⃣ User Interface Requirements

### 🖥️  Account Dashboard

- Subscription status indicator
- Payment method management
- KYC status indicator
- Virtual card management
- Transaction history
- Freight forwarding service opt-in and management

### 🔔 Notification System
<!-- TODO: low priority -->
- Opt-in email alerts for upcoming subscription payments
- Configurable notification preferences for transaction alerts
- In-app payment reminders

## 3️⃣ Billing System
<!-- TODO: low priority -->
### 🧾 Flexible Billing Logic

- Activity tracking for crypto users
- Define "service usage" parameters
- Billing exemption logic for inactive months
- Payment confirmation system

### 🧑‍💼 Subscription Management

- Payment method switching
- Pause subscription option
- Billing history

---

# 📊 Revenue Tracking
<!-- TODO: low priority -->
## 📉 Key Metrics to Track

- Number of active subscribers (monthly vs. annual)
- Subscription revenue
- Transaction fee revenue
- Load fee revenue by payment method
- Interchange revenue by volume tier
- Comprehensive negative balance reporting and tracking.
    - Alerts to slack
    - Alerts to dashboard
    - Email to user to upload funds and mention their account is on hold until they load more funds.
- Freight forwarding service adoption and revenue
- Costs (KYC, card issuance, processing fees, freight forwarding)
- Net revenue reporting

## 📋 Reporting Requirements

- Comprehensive dashboard for all revenue streams
- Cost breakdown by category
- Net profit calculation - Net Revenue Reporting

---

# 🔐 Security & Compliance

## 🛡️ KYC/AML Requirements

- Customer identity verification
- Ongoing monitoring
- Compliance with regulatory requirements
- Secure data handling

## 🌍 Jurisdictional Customization

- KYC requirements may vary by country depending on regulatory risk appetite.
- Users from sanctioned or high-risk jurisdictions will be automatically excluded at Sumsub’s level using global PEP/sanctions lists.
- Localization Support
    - Offer identity docs tailored to region (e.g., EU national ID cards, LATAM passport + selfie combos).Translate KYC error prompts into native languages for top 5 geographies (e.g., Spanish, Portuguese, German). Using Localize.JS.

## 🔐 Data Protection

- PCI DSS compliance for card data
    - Privacy policy updates
- Secure storage of user information
- Data retention policies

### 👮 Global Data Protection Requirements

- All user data will be stored and processed in compliance with applicable local data laws:
- GDPR (EU): Right to access, right to erasure (right to be forgotten), consent-based data use, breach notification within 72 hours.
- UK DPA 2018: Treated similar to GDPR for UK-based users.
- LGPD (Brazil): Consent for data processing, clear privacy policy, user rights on data portability and deletion.
- User data may be stored in U.S.-based infrastructure (e.g., AWS, Google Cloud) with geo-fencing and encryption at rest.

---

# 📱 Mobile Considerations

- Responsive design for all interfaces
- Streamlined mobile KYC flow
- Push notification support
- Mobile-optimized virtual card management

## 📍 Localized UX and Messaging
<!-- TODO: low priority -->
### 🌐 Automatic Locale Detection

- Implement automatic detection of browser locale to tailor onboarding experiences based on user region.
- Default the onboarding experience to the user's detected local language when available.

### 📖 Multilingual Support

- Ensure availability of fee disclosures, KYC prompts, and privacy policies in native languages for the following regions:
    - **EU**: English, Spanish, German, French
    - **LATAM**: Spanish, Portuguese
    - **Asia**: Major regional languages including Chinese, Japanese, Korean, Hindi
    - **Middle East & North Africa (MENA)**: Arabic
    - **Africa**: English, French, Portuguese, Swahili

### 🚦 Regionalized Error Handling

- Present region-specific error messages during critical user journeys, including:
    - Identity verification (KYC)
    - Transaction loading
    - Payment or transaction errors
- Localized error messages should provide clear, culturally appropriate guidance to reduce user abandonment rates.

### 📜 Localized Legal Compliance

- Ensure all legal documents (Terms of Service, Privacy Policy, User Agreements) are translated and presented in a legally compliant format tailored to each supported region.
- Implement explicit, region-specific consent collection methods to adhere to local data protection regulations (e.g., GDPR in EU, LGPD in Brazil, PDPA in Asia).

---

# 📬 Customer Communication

## ✉️ Onboarding Messaging

- Clear explanation of subscription model
- KYC process guidance
- Card usage instructions
- Fee transparency

## 🔄 Transition Communications (Existing Customers)

- Migration instructions for current customers
- Timeline for transition
- New features and benefits
- KYC requirements explanation

## 📬 Payment Notifications

- Upcoming payment reminders
- Payment confirmation receipts
- Failed payment alerts
- Inactive account notifications

---

# 🥼 Testing Requirements

## 🧪 User Acceptance Testing

- Complete end-to-end user journey
- Payment processing
- KYC flow
- Card issuance and usage

## 🧫 Integration Testing

- PayPal Subscription Setup
- Rain (Signify) API integration
- Sumsub verification flow
- Coinflow payment processing
- Cross-vendor data flow

---

# 📈 Marketing & Content

## 🧠 Content Creation

- TBD

### 🗓️ Meaningful Crypto Holidays for Marketing

| **Date** | **Holiday** | **Asset Token** |
| --- | --- | --- |
| **~~April 20~~** | ~~DOGE Day (est. 2014)~~ | ~~DOGE~~ |
|  **May 2** | **Bitcoin Pizza Day (est. 2010)** | 👑 **BTC**  |
| **May 31** | Polygon Genesis Day (est. 2020) | MATIC |
| **June 23** | Optimism Genesis Block (est. 2021) | OP |
| **July 30** | Ethereum Creation Day (est. 2015) | ETH |
| **August 31** | Arbitrum Genesis Block (est. 2021) | ARB |
| **September 16** | Uniswap (UNI) Genesis Block (est. 2020) | UNI |
| **September 21** | Avalanche Genesis Block (est. 2020) | AVAX |
| **September 26** | USDC Creation Day (est. 2018) | USDC |
| **October 4** | USDT Genesis Block (est. 2014) | USDT |
| **October 28** | SAND Genesis Block (est. 2019) | SAND |
| **October 31** | **Bitcoin White Paper Day (est. 2008)** | 👑 **BTC**  |
| **December 18** | DAI Genesis Block (est. 2017) | DAI |
| **January 3** | **Bitcoin Genesis Block Day (est. 2009)** | 👑 **BTC**  |

**Stablecoins:**

- **USDC (USD Coin):**
Strong presence globally, particularly across Asia, Europe, and Latin America, driven by DeFi, payments, and remittance use cases. Adoption significantly growing in Asia (Hong Kong, Singapore, and Korea).
- **USDT (Tether):**
The most dominant stablecoin worldwide, especially prevalent in Asia (China, Southeast Asia), Middle East, Eastern Europe, Africa, and Latin America. It remains the leading trading and remittance stablecoin globally.
- **DAI (DAI):**
Less mainstream adoption compared to USDC and USDT, but notably used in decentralized finance (DeFi) hubs like Europe (Germany, Netherlands), Southeast Asia, and Latin America.

**Major Cryptocurrencies:**

- **Bitcoin (BTC):**
Universally adopted; substantial growth in emerging markets like Latin America (El Salvador, Argentina), Africa (Nigeria, Kenya), Southeast Asia, Europe, and Middle East as a store-of-value, remittance, and hedge against inflation.
- **Ethereum (ETH):**
Broad adoption globally, particularly in technology-forward regions (Europe, Asia-Pacific), extensively used in DeFi, NFTs, and infrastructure projects worldwide.
- **Polygon (MATIC):**
Growing strongly in India (home market), Southeast Asia, and Europe due to lower transaction fees and active developer communities. Increasing use in enterprise and gaming sectors internationally.
- **Optimism (OP) & Arbitrum (ARB):**
Primarily adopted by advanced DeFi communities and tech-focused crypto users in Europe, Asia (Singapore, Korea, Hong Kong), and Australia, though adoption outside the crypto-native sector remains limited.
- **Avalanche (AVAX):**
Popular among crypto traders and DeFi developers, especially in Europe (Germany, France, Switzerland), Turkey, and Latin America, though global adoption is still moderate compared to Ethereum.
- **Uniswap (UNI):**
Widely used internationally among crypto traders and DeFi enthusiasts, particularly prominent in Europe, Asia, and Latin American crypto communities.
- **The Sandbox (SAND):**
Moderate adoption globally, primarily within gaming, metaverse, and NFT communities across Asia (Korea, Japan, Singapore), Europe, and parts of Latin America.

**Summary of Global Adoption:**

- High (global mainstream): BTC, ETH, USDT, USDC
- Moderate (strong niche communities globally): MATIC, UNI, DAI, AVAX
- Emerging (primarily niche, community-driven):  OP, ARB, SAND

These tokens have robust global reach overall, but stablecoins (especially USDT) and major cryptos like BTC and ETH have by far the most established and universal global adoption outside America.

## 🎯 Marketing Recommendations:

- **Thematic Promotions:** Plan targeted promotions or announcements aligned with these crypto holidays, highlighting tokens supported by **CoinFlow**.
- **Token-Specific Campaigns:** Leverage each holiday’s asset token (BTC, ETH, USDC, etc.) to precisely engage relevant crypto communities, driving brand affinity and platform adoption.
- **Community Engagement:** Utilize these key dates to deepen community interaction through token-specific content, fostering loyalty and advocacy within dedicated crypto ecosystems.
- **Educational & Promotional Opportunities:** Develop engaging educational content, launch special token-focused features or products, and offer promotional discounts that correspond to each crypto holiday, enhancing user acquisition and retention.
- **Free Token Loads:** Offer **free loads** specifically with the featured token on its commemorative day. For example, no-fee BTC loads on Bitcoin Pizza Day or ETH loads on Ethereum Creation Day. This targeted incentive boosts adoption and showcases CoinFlow’s commitment to the crypto community.

### 🔎 Messaging Focus

- Easier to use than before
- Load with Bitcoin!
- Global USDC loading for FREE
- Usable at any US merchant
- Multiple loading options (wire, crypto, cards)
- Optional freight forwarding services for international purchases

---

# 🧭 Onboarding Experience — US Unlocked Relaunch

A seamless onboarding experience is critical to drive conversions, reduce abandonment, and set the tone for how crypto users experience US Unlocked. This is not just about UX polish—onboarding *is* our retention engine.

---

## 🔑 Key Principles

- **Trust First**: Users are technical, unforgiving, and privacy-focused. They expect security, clarity, and efficiency.
- **No Surprises**: Fees, policies, and eligibility must be transparent upfront. Clarity eliminates churn.
- **Self-Serve > Support Tickets**: Build for self-resolution and zero-friction. Every support ticket represents a failed UX.
- **Crypto Speed Expectations**: From KYC to card issuance, if it's slower than a blockchain confirmation, it's too slow.

---

## 🧩 Onboarding Flow Structure
<!-- TODO: low priority -->
### 1️⃣ Registration

- Account Creation
    - Email + Password or OAuth (Google/Apple login optional)
    - Email verification (must be completed to proceed)
- Phone Verification
    - Collect mobile number
    - Verify via OTP
- 2FA Setup (Required for Login Security)
    - User selects preferred method: SMS or Authenticator App (TOTP)
        - Nice to have one day: Support for YubiKey hardware keys as an advanced security option
- Country of Extended-Stay or Residence
    - Dropdown selector
        - Tooltip: “U.S. citizens temporarily living abroad are eligible if residing in a supported country.”
        - Inline validation: “Unfortunately, we cannot serve users currently residing in the U.S.”
            - Exception Flow: Allow users with U.S. identification to upload proof of long-term stay abroad (e.g., utility bill, lease, visa). These documents will be manually reviewed alongside their Sumsub KYC submission.
        - If proof is sufficient and KYC is approved, the user may proceed and transact while flagged for follow-up. Manual intervention will be applied only if discrepancies arise.
            - Make a modal asking to provide further evidence of their stay and duration of stay so we can approve if document evidence is insufficient. Do virus scan on any documents uploaded prior to letting the rep see the file.
    - Logic allows users with a U.S. ID as long as their selected stay country is eligible
- Terms of Service Acknowledgment
    - Checkbox confirmation required
    - Link to Terms of Service and Privacy Policy
- Pre-KYC Privacy Assurance
    - Explain early: “We’ll request ID after payment to comply with global regulations.”
    - Reassure: “Your data is encrypted, never sold, and only used for identity verification.”
- Session Persistence
    - Auto-save user progress if they abandon mid-registration
    - Store email, country, and phone for re-engagement and drip campaigns

### 2️⃣ Membership Payment

- Display splash page prompting $4.95 membership fee *(Design TBD – Corey)*
- Accept payment via Coinflow (crypto or card)
- **Payment is required before any personally identifiable information (PII) is collected or KYC is triggered**
- When payment settles, user enters KYC flow to make sure we don’t spend unnecessary money on uncommitted users.

### 3️⃣ KYC Verification (via Sumsub)

- Trigger Sumsub only **after payment confirmation**
- No PII collected prior
- Real-time webhook listener to track `In Progress`, `Verified`, or `Rejected`
- Retry flow with explanations and friendly prompts

### 4️⃣ Welcome Dashboard

- Show clear status indicators:
    - Membership: Active
    - KYC: ✅ Verified / ❌ Rejected / ⏳ In Progress
- Prompt: “Load Your Account Using Crypto!”
- Tips panel: load options, card safety, spend controls

### 5️⃣ First Card Creation

- One-click to create Merchant Locked or One-Time Use card
- Contextual microcopy for use cases
- Optional Apple/Google Pay enablement (if MVP-ready)

### 6️⃣ Load Funds Coinflow Widget)

- Choose asset (BTC, ETH, USDC, etc.)
- Show real-time fees and network costs
- Optional: conversion preview and receipt

---

## ❗ KYC Retry Policy & Compliance Messaging

**Header (In-App / Modal):**

**We’d love to serve you—but you must verify your identity in order to proceed.**

**Body Copy:**

To use US Unlocked, you must successfully complete identity verification with our compliance partner, [Sumsub](https://sumsub.com/). This is a strict regulatory requirement—we can only support verified users that reside outside the United States.

Your last attempt didn’t go through, but don’t worry. You can retry verification anytime.

---

**Failure Loop Logic (Flow Requirements):**

1. On KYC failure, show a clear rejection message with context (if available).
2. Provide a **“Retry Verification”** button that reopens the Sumsub flow.
3. On **every login**, re-check KYC status.
    - If still unverified, **automatically re-prompt** the user to complete verification.
4. The user **remains ineligible** to access the product until Sumsub returns a “Verified” status.
5. Do **not** route to customer support—directly loop the user back to Sumsub until successful.

---

**Login Re-Prompt Copy:**

**Still need to verify your identity.**

To move forward, you’ll need to complete identity verification. This is required for regulatory compliance.

[ Retry Verification with Sumsub ]

---

## 🔄 User Journey Map (with Status Logging)

Each user can be in **only one active status** at a time. Every transition logs timestamp and channel (`web`, `mobile`). This enables targeted re-engagement campaigns and clear performance tracking.

### 📍 User Statuses

1. **Created** – Registered; awaiting email verification
2. **Verified_Email** – Email verified; awaiting mobile verification
3. **Verified_Mobile** – Mobile verified; awaiting 2FA setup
4. **TwoFA_Completed** – 2FA completed; awaiting subscription payment
5. **Subscription_Required** – Subscription payment required via PayPal
6. **Subscription_Payment_Paid** – Subscription payment successfully completed
7. **Subscription_Payment_Failed** – Subscription payment attempt failed
8. **Subscription_Active** – Subscription active; user ready for KYC verification
9. **KYC_Started** – Sumsub KYC flow initiated
10. **KYC_In_Review** – KYC information submitted; pending approval
11. **KYC_Verified** – KYC approved; full account access granted
12. **KYC_Rejected** – KYC rejected; user can retry
13. **Funds_Loaded** – First successful wallet funding
14. **FirstOneTimeUseCard_Seen** – Created first one-time use card
15. **FirstMerchantLockedCard_Seen** – Created first merchant-locked card
16. **FirstOneTimeUseCard_Used** – Successfully transacted using a one-time use card
17. **FirstMerchantLockedCard_Used** – Successfully transacted using a merchant-locked card
18. **Dormant** – No activity for 30+ days post-registration

### 📊 Logged Data per Status Change

- `status_updated_at`
- `channel`
- Optional: `utm_source`, `referrer_id`, `payment_txn_id`

---

## 📩 Drip Campaign Triggers

| From Status | If Stuck After | Message |
| --- | --- | --- |
| **Created → Verified_Email** | 12h | “Still waiting to verify your email? Click the link we sent to activate your account.” |
| **Verified_Email → Verified_Mobile** | 24h | “Secure your account—confirm your mobile number to proceed.” |
| **Verified_Mobile → TwoFA_Completed** | 6h | “Almost secure! Set up Two-Factor Authentication to protect your account.” |
| **TwoFA_Completed → Subscription_Required** | 1h | “Activate your subscription now to unlock full account access.” |
| **Subscription_Required → Subscription_Payment_Paid** | 6h | “Your subscription awaits! Complete your PayPal payment now.” |
| **Subscription_Payment_Failed → Subscription_Payment_Paid** | Immediate | “Oops, your payment didn’t go through. Please retry to continue.” |
| **Subscription_Payment_Paid → Subscription_Active** | Immediate | “Subscription active! Proceed to identity verification to fully enable your account.” |
| **Subscription_Active → KYC_Started** | 1h | “Let’s confirm your identity—start the verification process now.” |
| **KYC_Started → KYC_In_Review** | 30m | “Almost there! Complete your identity verification to activate your account.” |
| **KYC_In_Review → KYC_Verified** | 2h | “We’re reviewing your information—hold tight, approval is coming soon.” |
| **KYC_Rejected → KYC_Started** | Immediate | “We had trouble verifying your identity. Let’s try again.” |
| **KYC_Verified → Funds_Loaded** | 12h | “You’re verified! Load funds now to start transacting.” |
| **Funds_Loaded → FirstOneTimeUseCard_Seen** | 12h | “Funds loaded! Create your first one-time use card today.” |
| **Funds_Loaded → FirstMerchantLockedCard_Seen** | 12h | “Ready to spend? Create your first merchant-locked card now.” |
| **FirstOneTimeUseCard_Seen → FirstOneTimeUseCard_Used** | 24h | “Your one-time use card is ready—make your first purchase now!” |
| **FirstMerchantLockedCard_Seen → FirstMerchantLockedCard_Used** | 24h | “Merchant-locked card ready! Complete your first secure transaction today.” |
| **Any → Dormant** | 30 days | “It’s been a while! Log in now to reconnect and continue your US Unlocked experience.” |
| **Dormant → Any Active Status** | Immediate | “Welcome back! Ready to pick up right where you left off?” |

---

## ⚙️ Tools & Interfaces

- **Progress Tracker** (top nav): Account > Payment > KYC > Cards > Load
- **In-App Help Bot**: Onboarding-specific answers
    - We will use AI LLM chatbot to train customers and act as first line support.
- **FAQs**: Dedicated onboarding section
    - Knowledge base and help articles
- **Login History UI**: Show login events by IP/device/location with ability to revoke session access
- **~~New Device Detection**: Alert users on login from unrecognized device, require 2FA approval~~
    - ~~Coinbase does this amazingly UX wise as an example, btw.~~
        - Not in scope for MVP

---

## ⚠️ Risk Points to Eliminate

- Manual refund logic — must be preemptive and policy-driven
- Failed KYC with no retry path
- Unexpected billing for inactive months
- Delays or failures in card provisioning or fund loading
- Unauthorized access due to unmonitored sessions or devices

---

## ✅ Success Metrics

- % completing KYC within 24 hrs
- % loading funds within 48 hrs of sign-up
- Upgrade rate: monthly ➝ annual
- Onboarding step drop-off %
- Support ticket volume tagged "onboarding"
- **% of flagged device logins resolved within 24 hours**

---

## 🧠 Notes for Engineering

- Sumsub triggered only after payment
- Use webhook listener for real-time KYC state
- Billing engine tracks user activity before charging
- Maintain status log history for campaign automation and analytics
- Payment status (`initiated`, `pending`, `settled`, `failed`) must be logged with timestamps and user ID
- Implement login history with device fingerprinting + geo-IP logs
- Trigger alerts + require 2FA for new device logins, with ability to view and revoke sessions

---

### 🔐 Login Authentication & Device Management
<!-- TODO: low priority -->
- **Device Fingerprinting**: Track browser, device type, OS, location, and IP per login event
- **Login History**: Present users with full session history via dashboard UI
- **Session Management**: Users can view and revoke active sessions at any time
- **New Device Detection**:
    - Alert user when login is from a previously unseen device or IP
    - Require step-up 2FA for unrecognized devices
    - Optionally approve device for future use
- **Admin Tools**: Internal team visibility into last login, device profile, IP, and geolocation
- **Security Alerts**: Email and/or in-app notifications for logins from unusual locations or new devices

---

# 🌟 Post-Launch Evaluation

### 📈 Success Metrics

- User migration rate - speed to onboard, load, and purchase.
- New customer acquisition
- ARPU - Average Revenue per user
- Subscription conversion
    - Abandoned Members
        - Did not pay to get started - got spooked at the paywall.
    - Ineligible Members (US Resident or sanctioned country)
    - Monthly Members
    - Annual Members
    - Dormant Members
- Transaction volume & fees
- Interchange revenue growth
- Negative Balances Reporting
    - Let’s benchmark Rain’s negative balance performance against Lithic’s, where we consistently hit Gold Standard thresholds. Lithic gave us a strong baseline to compare.
    - **🚥 Key Benchmarks to Know 🚥**
        - 🟢 < 0.05% (Gold Standard): Top-tier programs with strong controls, device fingerprinting, and velocity checks.
        - 🟡 0.05%–0.10% (Industry Standard): Normal range for open-loop prepaid cards that allow online or international transactions.
        - 🔴 > 0.10% (Warning Zone): Indicates gaps in onboarding, identity verification, or behavioral monitoring.
- Customer satisfaction
    - Anything above 94% rating is good

---

# 🚦 Launch Readiness Checklist

- [x]  Contracting Completed
- [x]  Vendor integrations plans finalized
- [x]  Requirements Documentation Drafted
- [x]  Load Payment processing configured
- [x]  KYC flow determined
- [x]  KYC flow implemented and verified
- [ ]  Card Usage and functions tested
    - [ ]  Test to spend too much on a US Unlocked Card
        - [ ]  Test on/off decline
        - [ ]  Test > per charge limit decline
        - [ ]  Test happy path
    - [ ]  Test one time use card
        - [ ]  Test on/off decline
        - [ ]  Test > monthly limit decline
        - [ ]  Test happy path approval
    - [ ]  Test Merchant locked card
        - [ ]  Add more than one merchant
            - [ ]  Test with Temporary Unlock
            - [ ]  Test with decline and permission
        - [ ]  Test on/off decline
        - [ ]  Test > max limit decline
        - [ ]  Test happy path approval

- [x]  Subscription management messaging
- [x]  Freight Forwarding decoupling enhancements
- [ ]  User interface updates  - ([🚀 USU  - Final Scrubs](https://www.notion.so/USU-Final-Scrubs-1e6ed8a5a9dc80238e42fc5a4bf55eef?pvs=21) )
    - [x]  Documented @Corey Glaze
    - [ ]  Development @Hans Zhang  <!-- TODO -->
- [x]  Customer migration plan finalized -([🚀 USU  - Final Scrubs](https://www.notion.so/USU-Final-Scrubs-1e6ed8a5a9dc80238e42fc5a4bf55eef?pvs=21))
- [x]  Communication templates prepared - ([🚀 USU  - Final Scrubs](https://www.notion.so/USU-Final-Scrubs-1e6ed8a5a9dc80238e42fc5a4bf55eef?pvs=21))
- [ ]  PayPal Subscription Slack Alerts
    - [ ]  When a new subscription happens for first time
    - [ ]  When a subscription has cancelled by user
    - [ ]  When a subscription payment failed
    - [ ]  When a subscription payment renewed
- [ ]  Negative balance tracking & slack alerts
- [ ]  Load tracking & slack alerts
- [ ]  Chargeback process defined (TBD) - (@Corey Glaze to figure out - hand to ian and george)
    - [ ]  Corey to Document against Rain Disputes API
        - [ ]  Customer Interface
            - [ ]  ZenDesk
            - [ ]  FAQs
        - [ ]  Admin Interface
            - [ ]  Workflow
- [ ]  Customer Service Tools and Help Desk - (@Corey Glaze & devs to do - hand to ian)
- [ ]  Day 1 - Refund Process  - (@Brion Bonkowski  & @Corey Glaze to figure out)
- [ ]  Revenue tracking system implemented in Net Revenue Report (@Corey Glaze to audit after go live and get in shape w/ @Hans Zhang)  <!-- TODO -->
    - [ ]  Costs Audited
    - [ ]  Income Audited
- [ ]  End-to-end User acceptance testing passed ( @Corey Glaze = Product - Sign Off - before Launch)
    - [ ]  Security audit & review completed ( @Hans Zhang = Development - Sign Off - before Launch) <!-- TODO -->
- [ ]  Marketing materials - (@Brion Bonkowski & @Corey Glaze to figure out)

# 🔮 Final Thoughts on Product

> **First impressions are forever.**  
Crypto users don’t tolerate broken flows, unclear policies, or any necessary friction. If we win them early, they become loyal evangelists forever. If we fumble, just like with the underbanked/underserved users in prepaid, they will leave—and they will tell others we suck. Build our onboarding like all our potential growth depends on it. Because it actually does.
> 

## *“The onboarding is the product.” - 🧙🏻‍♂️*

---

# 🤑 Final Thoughts on Money

### 💰 How to Earn More - Yield Staking Stablecoin for Profits

We should put .125% - .25% of the revenue we accumulate from stablecoin loads into a staking contract at a reputable exchange that offers staking (COINBASE, GEMINI…) we can make 4-5% APY% staking our USDC en masse just sitting there. 

### 🚨 Stablecoins and ALTs are Cool—But Only Bitcoin Really Matters…

**From day one**, we must accumulate Bitcoin in our corporate treasury.  @Joost van Schijndel, watch what happens over the next five years. By dollar-cost averaging a small amount of Bitcoin from every transaction, we position ourselves for massive long-term growth. 💪🏾💥

Historically, Bitcoin yields a **25–35% CAGR** over five years when consistently dollar-cost averaging.

To fully understand the rationale and context, please watch the video below:

[https://www.youtube.com/watch?v=O9KnBcWMkpw](https://www.youtube.com/watch?v=O9KnBcWMkpw)

**TABLE OF CONTENTS:**
