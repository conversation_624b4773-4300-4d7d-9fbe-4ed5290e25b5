# VirtualCards App

For both admin portal and mobile app (iOS and Android).

#### Tips

* Run `yarn --ignore-engines`
* Android Keystore password and key password: `Clf12345`

#### Build for admin portal

1. set `window.deviceMode` to `web` in `const.js`
2. Uncomment `customize-__SUB_DOMAIN__` rows in `index.template.html`
3. Run `yarn build`

#### Build for CLF (Universal Transaction Compliance) mobile app

1. set `window.deviceMode` to `clf` in `const.js`
2. Comment `customize-__SUB_DOMAIN__` rows in `index.template.html`
3. Run `yarn build-ios` or `yarn build-android`
4. (iOS only) Update `*-Info.plist` file:

```
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>need location access to find dispensaries nearby</string>
    <key>NSLocationAlwaysUsageDescription</key>
    <string>need location access to find dispensaries nearby</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>need photo library access to select and update your avatar photo</string>
    <key>NSCameraUsageDescription</key>
    <string>need camera access to scan QR codes</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>need microphone access to help you to input text by translating speech</string>
    <key>NSSpeechRecognitionUsageDescription</key>
    <string>need speech recognition access to help you to input text</string>
```

5. (iOS only) Use "Legacy Build System". Choose `File` -> `Workspace Settings`.
