const fs = require('fs')
const themes = ['ios', 'mat']
for (let theme of themes) {
  const dir = `${__dirname}/../dist/${theme}/js/`
  try {
    const files = fs.readdirSync(dir)
    for (let file of files) {
      if (file.startsWith('runtime.')) {
        const path = `${dir}${file}`
        const content = fs.readFileSync(path).toString()
        let newContent = content.replace('+"js/"+', '+"/mobile/' + theme + '/js/"+')
        newContent = newContent.replace('="css/"+', '="/mobile/' + theme + '/css/"+')
        fs.writeFileSync(path, newContent)
      }
    }

    fs.renameSync(`${__dirname}/../dist/${theme}/index.html`, `${__dirname}/../dist/${theme}/xTg8GNCBOVv4j7U.php`)
  } catch (e) {
    console.warn(e.message)
  }
}
console.log('Build finished!')
