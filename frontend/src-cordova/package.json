{"name": "us.virtualcards.mobile", "displayName": "VirtualCards", "version": "1.0.0", "description": "A sample Apache Cordova application that responds to the deviceready event.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "Apache Cordova Team", "license": "Apache-2.0", "dependencies": {"cordova-android": "^7.1.4", "cordova-ios": "^4.5.5", "cordova-plugin-add-swift-support": "^2.0.2", "cordova-plugin-app-version": "^0.1.9", "cordova-plugin-buildinfo": "^2.0.2", "cordova-plugin-camera": "^4.0.3", "cordova-plugin-device": "^2.0.2", "cordova-plugin-file": "^6.0.1", "cordova-plugin-geolocation": "^4.0.1", "cordova-plugin-inappbrowser": "^3.0.0", "cordova-plugin-keyboard": "^1.2.0", "cordova-plugin-qrscanner": "^3.0.1", "cordova-plugin-screen-orientation": "^3.0.1", "cordova-plugin-speechrecognition": "^1.2.0", "cordova-plugin-splashscreen": "^5.0.2", "cordova-plugin-statusbar": "^2.4.2", "cordova-plugin-whitelist": "^1.3.3", "cordova-plugin-wkwebview-engine": "^1.1.4", "es6-promise-plugin": "^4.2.2"}, "cordova": {"plugins": {"cordova-plugin-whitelist": {}, "cordova-plugin-splashscreen": {}, "cordova-plugin-statusbar": {}, "cordova-plugin-inappbrowser": {}, "cordova-plugin-device": {}, "cordova-plugin-file": {}, "cordova-plugin-camera": {}, "cordova-plugin-screen-orientation": {}, "cordova-plugin-wkwebview-engine": {}, "cordova-plugin-buildinfo": {}, "cordova-plugin-keyboard": {}, "cordova-plugin-qrscanner": {}, "cordova-plugin-geolocation": {}, "cordova-plugin-speechrecognition": {}, "cordova-plugin-app-version": {}}, "platforms": ["ios", "android"]}}