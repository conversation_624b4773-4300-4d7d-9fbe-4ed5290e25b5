<template>
  <div id="q-app">
    <router-view />
    <q-ajax-bar size="2px" :speed="50" v-show="globalAjaxBar"/>
  </div>
</template>

<script>
import $ from 'jquery'
import { getScrollParent } from './common'
import eventBus from './eventBus'

export default {
  name: 'App',
  computed: {
    globalAjaxBar () {
      return this.$store.state.Config.globalAjaxBar
    }
  },
  methods: {
    cordovaInit () {
      if (!window.cordova || !window.device) {
        return
      }
      if (window.device.platform === 'iOS') {
        window.Keyboard.shrinkView(true)
        window.Keyboard.disableScrollingInShrinkView(false)
        window.Keyboard.hideFormAccessoryBar(false)
        window.Keyboard.automaticScrollToTopOnHiding = true

        window.addEventListener('keyboardDidShow', function () {
          window.document.activeElement.scrollIntoViewIfNeeded(true)
          window.document.body.classList.add('keyboard-visible')
        })

        window.addEventListener('keyboardWillHide', function () {
          window.document.body.classList.remove('keyboard-visible')
        })
      } else {
        // Fix Android text field focus/scroll issue
        $('body').off('focus', 'input').on('focus', 'input', () => {
          const element = window.document.activeElement
          const parent = getScrollParent(element)
          const before = parent.scrollTop
          element.scrollIntoView({
            behavior: 'instant',
            block: 'start',
            inline: 'nearest'
          })
          const after = parent.scrollTop
          if (before + 50 < after) {
            parent.scrollTop = after - 50
          } else {
            setTimeout(() => {
              const before = parent.scrollTop
              element.scrollIntoView({
                behavior: 'instant',
                block: 'start',
                inline: 'nearest'
              })
              const after = parent.scrollTop
              if (before + 50 < after) {
                parent.scrollTop = after - 50
              }
            }, 300)
          }
        })
      }
    },

    installWebListener () {
      if (window.deviceMode !== 'web') {
        return
      }

      $('body').off('mouseleave', '.sidebar-popover').on('mouseleave', '.sidebar-popover', () => {
        this.$root.$emit('admin-hide-all-nav')
      })

      $('.q-table-container').each(function () {
        const $t = $(this)
        var $ctrl = $t.find('.q-table-top .q-table-control')
        if ($ctrl.length) {
          if ($ctrl.find('.q-table-handler').length <= 0) {
            $ctrl.last().append('<button tabindex="0" type="button" class="q-btn inline relative-position q-btn-item non-selectable q-btn-round q-btn-flat q-focusable q-hoverable text-faded q-btn-dense q-table-handler ml-10">' +
              '<div class="q-focus-helper"></div>' +
              '<div class="q-btn-inner row col items-center q-popup--skip justify-center">' +
              '<i aria-hidden="true" class="mdi mdi-arrow-expand"></i></div></button>')
          }
        } else {
          if ($t.children().filter('.q-table-handler').length <= 0) {
            $t.append('<div class="q-table-handler"><i class="mdi mdi-arrow-expand"></i></div>')
          }
        }
      })

      $('.q-table-handler').off('click').on('click', function () {
        var $t = $(this)
        var $parent = $t.parents('.q-table-container').first()
        var $body = $('body')
        if ($t.find('.mdi-arrow-expand').length > 0) {
          $t.find('.mdi-arrow-expand').removeClass('mdi-arrow-expand').addClass('mdi-arrow-collapse')
          $parent.addClass('maximized')
          $body.css('overflow', 'hidden')
        } else {
          $t.find('.mdi-arrow-collapse').removeClass('mdi-arrow-collapse').addClass('mdi-arrow-expand')
          $parent.removeClass('maximized')
          $body.css('overflow', 'visible')
        }
      })
    }
  },
  mounted () {
    this.eventBus = eventBus
    window.$ = $
    window.mainApp = this
    window.document.body.classList.add(`device-mode-${window.deviceMode}`)

    localStorage.removeItem('__u')
    localStorage.removeItem('__p')

    this.cordovaInit()
    this.installWebListener()
  }
}
</script>

<style lang="scss">
  @import '../node_modules/currency-flags/dist/currency-flags.min.css';
  @import '../node_modules/@mdi/font/css/materialdesignicons.min.css';
  @import './css/q.scss';
  @import './css/helper.scss';
  @import './css/app.scss';
</style>
