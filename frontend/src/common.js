import { origin, deviceMode, transferMex } from './const'
import { Dialog, Platform, Notify, Loading } from 'quasar'
import cf from 'currency-formatter'
import axios from 'axios'
import to from 'await-to-js'
import qs from 'qs'
import store from './store'
import moment from 'moment'
import _ from 'lodash'
import $ from 'jquery'
import copy from 'copy-to-clipboard'
import { required, email, helpers } from 'vuelidate/lib/validators'

const phoneNumberValidator = helpers.regex('Must be digits', /^[0-9+\s-]*$/)
const pinValidator = helpers.regex('Must be 4 digits', /^[0-9]{4}$/)
const barcodeNumberValidator = value => {
  return isRapidAccountNumber(value) || isBotmAccountNumber(value)
}

function isRapidAccountNumber (value) {
  value = (value || '').toString()
  return value.match(/^[0-9]{11}$/) !== null
}

function isBotmAccountNumber (value) {
  value = (value || '').toString()
  return value.match(/^[0-9A-Z]{4}-[0-9A-Z]{4}-[0-9A-Z]{4}$/) !== null
}

let widgetToken = null
const pos = location.href.indexOf('x-widget-token')
if (pos > 0) {
  const search = location.href.substring(pos)
  const params = new URLSearchParams(search)
  widgetToken = params.get('x-widget-token').split('#')[0]
}

async function request (url, method = 'get', data = {}, silent = false, responseType = 'json') {
  let xWidgetToken = null
  if (data && data['x-widget-token']) {
    xWidgetToken = data['x-widget-token']
    delete data['x-widget-token']
  }

  let params = null
  let contentType = 'application/json'
  method = method.toLowerCase()
  if (method === 'get' || method === 'delete') {
    params = data
    params['_t'] = +new Date()
  } else if (method === 'form') {
    method = 'post'
    contentType = 'application/x-www-form-urlencoded'
    data = qs.stringify(data)
  } else if (method === 'file') {
    method = 'post'
    contentType = 'multipart/form-data'
    const oldData = data
    data = new FormData()
    _.forEach(oldData, (v, k) => {
      data.append(k, v)
    })
  }
  if (!_.startsWith(url, '/')) {
    url = `/${url}`
  }

  // if (window.cordova) {
  if (deviceMode !== 'web') {
    if (!_.startsWith(url, 'http')) {
      url = `${origin}${url}`
    }
  }

  const headers = {
    'X-Requested-With': 'XMLHttpRequest',
    'Content-type': contentType,
    'x-api-key': store.state.Config.token,
    'x-token': store.state.Config.token,
    'x-i18n': store.state.Config.locale,
    'x-timezone': store.state.Config.timezone
  }
  if (widgetToken || xWidgetToken) {
    headers['x-widget-token'] = xWidgetToken || widgetToken
  }

  let [err, resp] = await to(axios.request({
    url,
    method,
    data,
    responseType,
    headers,
    params: params
  }))

  // Retry for random failures of the Axios request
  if ((err || !resp || !resp.data) && method.toLowerCase() === 'get') {
    [err, resp] = await to(axios.request({
      url,
      method,
      data,
      responseType,
      headers,
      params: params
    }))
  }

  if (err && err.response && err.response.status === 401) {
    notifyResponse(err.response.data.message || 'Token is timeout! Please login again.', () => {
      window.location.href = '/login'
    })
    return
  }

  if (err || !resp || !resp.data) {
    const originalError = err

    if (err && err.response) {
      err = err.response.data
    }

    // Debugging
    console.error({
      url,
      method,
      silent,
      err,
      resp
    })

    if (!err && resp && resp.request && resp.request.responseURL) {
      // let url = resp.request.responseURL
      // url = url.replace('/user/api/token', '')
      // window.location.href = url
    }
    if (resp === undefined && !err) {
      console.error('Undefined response. This could be a CORS issue or a dropped internet connection.')
    }

    if (!resp) {
      resp = {}
    }
    let message = 'Failed to request data. Please try again later.'
    let logError = false

    if (err && err.message) {
      message = err.message
    } else if (originalError) {
      logError = true
      if (originalError.message) {
        message += ` (${originalError.message})`
      } else {
        const xhr = originalError.response || originalError.request
        if (xhr) {
          if (xhr.statusText) {
            message += ` (${xhr.statusText})`
          } else if (xhr.status) {
            message += ` (${xhr.status})`
          }
        }
      }
    }

    resp.data = {
      success: false,
      message,
      data: null
    }

    if (logError) {
      axios.request({
        url: `${origin}/t/log-error`,
        method: 'post',
        data: {
          error: {
            url,
            message
          }
        }
      })
    }
  }

  if (window.mainApp && window.mainApp.installWebListener) {
    window.mainApp.installWebListener()
  }

  if (resp.data.success === undefined) {
    console.log(resp.data)
    return resp.data
  }

  if (!silent) {
    notifyResponse(resp.data)
  }

  return resp.data
}

let responseQueue = []

function notifyResponse (resp, cb = null) {
  if (!resp || _.isString(resp)) {
    resp = {
      success: false,
      message: resp
    }
  }
  if (resp.success === false && resp.message) {
    if (responseQueue.indexOf(resp.message) >= 0) {
      return
    }
    responseQueue.push(resp.message)
    _notifyDialog(responseQueue[0], cb)
  }
}

function _notifyDialog (message, cb = null) {
  if (message === 'Session is timeout! Please login again.') {
    window.mainApp.$root.$emit('show-session-detect-dialog')
    responseQueue.shift()
    return
  }

  const config = {
    title: 'Error',
    message: message,
    color: 'negative'
  }
  const promise = Dialog.create(config)
  promise.then(() => {
    responseQueue.shift()

    if (cb) {
      cb()
    }

    if (message === 'Invalid user!') {
      if (window.deviceMode === 'web') {
        window.location.href = '/logout'
      }
    } else if (responseQueue.length) {
      _notifyDialog(responseQueue[0])
    }
  }).catch(() => {
    responseQueue.shift()
  })
}

function notifySuccess (resp, title = 'Message', color = 'positive') {
  if (!resp || _.isString(resp)) {
    resp = {
      success: true,
      message: resp
    }
  }
  if (resp.success) {
    Dialog.create({
      title,
      message: resp.message || 'Operation finished successfully!',
      color
    })
  }
}

function notifyOptional (message) {
  if (message === true) {
    notify(null)
  } else if (message) {
    notifySuccess('' + message)
  }
}

function notify (message = null, type = 'positive', timeout = 2000) {
  if (message && _.isObject(message) && !_.isString(message)) {
    message = message.message
  }
  Notify.create({
    type,
    message: message || 'Operation finished successfully!',
    timeout
  })
}

function notifyForm (message = 'Please complete the form before submitting!') {
  notify(message, 'negative')
}

async function uploadAttachment (file, category = null) {
  const params = {
    file
  }
  if (category) {
    params.category = category
  }
  const resp = await request(`/attachments`, 'file', params)
  if (resp.success) {
    return resp.data.file
  }
  return resp.message
}

function isNumeric (x) {
  return ((typeof x === 'number' || typeof x === 'string') && !isNaN(Number(x)))
}

function moneyFormat (amount, currency = 'USD', withCurrency = false, convertToMajor = true, precision = undefined) {
  if (convertToMajor) {
    amount = moneyMajorAmount(amount, currency)
  }
  let format = cf.format(amount, { code: currency, precision })
  if (withCurrency) {
    format = `${currency} ${format}`
  }
  return format
}

function moneyDecimalDigits (currency) {
  const config = cf.findCurrency(currency)
  if (config) {
    return config.decimalDigits
  }
  Dialog.create({
    title: 'Error',
    message: 'Unsupported currency ' + currency,
    color: 'negative'
  })
  return null
}

function moneyMajorAmount (amount, currency = 'USD', fixed = false) {
  const digit = moneyDecimalDigits(currency)
  if (digit === null) {
    return amount
  }
  let value = amount * Math.pow(10, -digit)
  if (fixed) {
    value = value.toFixed(digit)
  }
  return value
}

function moneyMinorAmount (amount, currency = 'USD') {
  const digit = moneyDecimalDigits(currency)
  if (digit === null) {
    return amount
  }
  return Math.round(amount * Math.pow(10, digit))
}

function moneyFormatHtml (amount, currency = 'USD', formatCb = null) {
  const minus = amount < 0
  amount = Math.abs(amount)
  let format = moneyFormat(amount, currency)
  if (formatCb) {
    format = formatCb(format, amount, currency)
  }
  const matched = format.match(/(.*?)([\d., ]+)(.*?)/)
  return `<sup>${matched[1]}</sup>${minus ? '-' : ''}${matched[2]}<sup>${matched[3]}</sup>`
}

function callNative (message, arg = null) {
  try {
    if (Platform.is.ios) {
      window.webkit.messageHandlers[message].postMessage(arg)
    }
  } catch (e) {
    console.log(e)
  }
}

function isLive () {
  return [
    'www.virtualcards.us',
    'account.usunlocked.com',
    'fis.virtualcards.us',
    'prepaidprogrammanagement.com',
    'www.prepaidprogrammanagement.com',
    'admin.transfermex.com',
    'admin.cashonweb.net',
    'secureaccounts.leaflink.com',
    'admin.spendr.com'
  ].includes(window.location.hostname)
}

function isStaging () {
  return window.location.hostname.indexOf('staging.virtualcards.us') > -1 ||
  window.location.hostname.indexOf('test.virtualcards.us') > -1 ||
  window.location.hostname.indexOf('test.spendr.com') > -1 ||
  window.location.hostname.indexOf('stage.spendr.com') > -1
}

function isLocal () {
  return _.includes(['localhost', 'span.hans', 'span.local'], window.location.hostname)
}

function isConsumer (u = null) {
  u = u || store.state.User
  if (!u.teams) {
    return false
  }
  return u.teams.indexOf('Consumer') >= 0
}

function isSuperAdmin (u = null) {
  u = u || store.state.User
  if (!u.teams) {
    return false
  }
  return u.teams.indexOf('MasterAdmin') >= 0
}

function redirectRoot (url) {
  window.location.href = url
}

function deleteConfirm (url, cb = null, msg = null) {
  Dialog.create({
    title: 'Confirm',
    message: msg || 'Are you sure that you want to delete this item?',
    color: 'negative',
    cancel: true
  }).then(async () => {
    const resp = await request(url, 'post')
    notifySuccess(resp)
    cb && cb()
  }).catch(() => { })
}

function pages ({ pagination, pagesNumber }) {
  if (!Number.isFinite(pagesNumber)) {
    pagesNumber = 1
  }

  const page = parseInt(pagination.page)
  const items = []
  let step = 1
  if (pagesNumber > 100) {
    step = 10
  }
  if (pagesNumber > 10000) {
    step = 100
  }
  if (pagesNumber > 100000) {
    step = 1000
  }
  if (pagesNumber > 1000000) {
    step = 10000
  }
  let hasLast = false
  for (let i = 1; i <= pagesNumber; i += (i === 1 && step > 1 ? (step - 1) : step)) {
    items.push({
      label: i.toString(),
      value: i
    })
    if (i < page && page < (i + (i === 1 && step > 1 ? (step - 1) : step))) {
      items.push({
        label: page.toString(),
        value: page
      })
    }
    if (i === pagesNumber) {
      hasLast = true
    }
  }
  if (!hasLast) {
    items.push({
      label: pagesNumber.toString(),
      value: pagesNumber
    })
  }
  return items
}

function concatInPlace (arr, news) {
  for (let n of news) {
    arr.push(n)
  }
}

function toSelectOptionsFromObject (obj) {
  const all = []
  _.forEach(obj, (v, k) => {
    all.push({
      label: k,
      value: v
    })
  })
  return all
}

function toSelectOptions (arr, humanize = false) {
  return _.map(arr, a => {
    if (_.isObject(a)) {
      return {
        label: a.name,
        value: a.id,
        stamp: a.stamp,
        disable: a.disable
      }
    }
    return {
      label: humanize ? _.startCase(a) : a + '',
      value: a === 'Sent sms, email, or called' ? 'Sent' : a
    }
  })
}

function fixSelectOptions (items) {
  return _.map(items, item => {
    if (item.label === '________') {
      item.label = ''
      item.className = 'select-separator'
    }
    return item
  })
}

function providerByAccountNumber (an) {
  if (isBotmAccountNumber(an)) {
    return 'BOTM'
  }
  if (isRapidAccountNumber(an)) {
    return 'Rapid'
  }
  return null
}

function cardStatuses (provider) {
  if (!provider || !transferMex[provider]) {
    return []
  }
  return transferMex[provider].cardStatuses
}

function cardStatusesByAccountNumber (an) {
  return cardStatuses(
    providerByAccountNumber(an)
  )
}

function allCardStatuses () {
  const all = []
  _.forEach(transferMex, (v, k) => {
    for (const s of v.cardStatuses) {
      let label = `${k} - ${s}`
      if (k === 'BOTM' && s === 'Inactive') {
        label += `(Closed)`
      }
      all.push({
        label,
        value: s
      })
    }
  })
  return all
}

function EventHandlerMixin (eventName, method = 'reload') {
  return {
    mounted () {
      return this.$root.$on(eventName, this[method])
    },
    beforeDestroy () {
      return this.$root.$off(eventName, this[method])
    }
  }
}

function trendIcon (value) {
  if (value < 0) {
    return 'mdi-arrow-down'
  } else if (value > 0) {
    return 'mdi-arrow-up'
  }
  return ''
}

function trendColor (value) {
  if (value < 0) {
    return 'text-red-8'
  } else if (value > 0) {
    return 'green'
  }
  return ''
}

function isDebugging () {
  return !window.cordova || (window.BuildInfo && window.BuildInfo.debug)
}

function bindState (module, field, newField = null) {
  newField = newField || field
  const o = {}
  o[newField] = {
    get () {
      return store.state[module][field]
    },
    set (value) {
      const n = {}
      n[field] = value
      store.commit(`${module}/update`, n)
    }
  }
  return o
}

function bindStateReadonly (module, field, newField = null) {
  newField = newField || field
  const o = {}
  o[newField] = {
    get () {
      return store.state[module][field]
    }
  }
  return o
}

function bindStateChild (module, child, field = null, newField = null) {
  const o = {}
  const fields = field ? [field] : Object.keys(store.state[module][child])
  _.forEach(fields, field => {
    o[newField || field] = {
      get () {
        return store.state[module][child][field]
      },
      set (value) {
        const n = {}
        n[field] = value
        store.commit(`${module}/updateChild`, {
          key: child,
          value: n
        })
      }
    }
  })
  return o
}

function bindStateSelect (module, field, newField = null, nullable = true) {
  newField = newField || field

  const o = {}
  o[newField] = {
    get () {
      let values = []
      if (module) {
        values = store.state[module][field]
      } else {
        values = this[field]
      }
      if (nullable) {
        values.unshift({
          label: 'None',
          value: null
        })
      }
      return values
    }
  }
  return o
}

async function loginAs (userId) {
  const newWin = window.open('/static/loading.html', '_blank')
  const from = window.encodeURIComponent(window.location.href)
  Loading.show()
  const resp = await request(`/login_as/data?user_id=${userId}&from=${from}`)
  Loading.hide()
  if (resp.success) {
    if (resp.data.window) {
      newWin.location = resp.data.url
    } else {
      newWin.close()
      redirectRoot(resp.data.url)
    }
  } else {
    newWin.close()
  }
}

function isTouchable () {
  const prefixes = ' -webkit- -moz- -o- -ms- '.split(' ')
  const mq = function (query) {
    return window.matchMedia(query).matches
  }

  if (('ontouchstart' in window) || (window.DocumentTouch && document instanceof window.DocumentTouch)) {
    return true
  }

  // include the 'heartz' as a way to have a non matching MQ to help terminate the join
  // https://git.io/vznFH
  const query = ['(', prefixes.join('touch-enabled),('), 'heartz', ')'].join('')
  return mq(query)
}

let touchableDevice = isTouchable()

function click (fn) {
  if (touchableDevice) {
    return
  }
  fn()
}

function touch (fn) {
  if (!touchableDevice) {
    return
  }
  setTimeout(fn, 150)
}

const clf = {
  orgType () {
    return store.getters['User/organizationType']
  },
  orgName () {
    return store.getters['User/organizationName']
  },
  isDispensary () {
    return clf.orgType() === 'Dispensary'
  },
  isVendor () {
    return clf.orgType() === 'Vendor'
  },
  isBank () {
    return clf.orgType() === 'Bank'
  },
  isMerchant () {
    return clf.isDispensary() || clf.isVendor()
  },
  apiPrefix () {
    return store.getters['User/apiPrefix']
  },
  is () {
    return store.state.User.cpKey === 'cp_clf'
  },
  isAdminRoute ($route) {
    return clf.is() &&
      isSuperAdmin() &&
      $route.path.startsWith('/a/clf/sa')
  },
  isSaRoute ($route) {
    return clf.is() &&
      isSuperAdmin() &&
      $route.path.startsWith('/a') &&
      !$route.path.startsWith('/a/clf/sa')
  },
  updateStatusBar () {
    if (window.StatusBar) {
      window.StatusBar.styleLightContent()

      if (window.device.platform === 'Android') {
        window.StatusBar.backgroundColorByHexString('#388E3C')
      }
    }
  },
  resetStatusBar () {
    if (window.StatusBar) {
      window.StatusBar.styleDefault()

      if (window.device.platform === 'Android') {
        window.StatusBar.backgroundColorByHexString('#F4F4F4')
      }
    }
  }
}

function clfDevice () {
  return deviceMode === 'clf'
}

function noop () { }

function getScrollParent (node) {
  const isElement = node instanceof HTMLElement
  const overflowY = isElement && window.getComputedStyle(node).overflowY
  const isScrollable = overflowY !== 'visible' && overflowY !== 'hidden'

  if (!node) {
    return null
  } else if (isScrollable && node.scrollHeight >= node.clientHeight) {
    return node
  }

  return getScrollParent(node.parentNode) || document.body
}

function showPlatforms () {
  const u = store.state.User
  if (!u || !u.platforms) {
    return true
  }
  return u.platforms > 1
}

function generateColumns (names, rights = [], sort = [], others = {}) {
  return names.map(n => {
    const o = {
      field: n,
      label: n,
      align: rights.includes(n) ? 'right' : 'left',
      sortLabel: typeof sort === 'object' && sort[n] ? sort[n] : null
    }
    if (others[n]) {
      _.assignIn(o, others[n])
    }
    return o
  })
}

function isEmpty (v) {
  return v === null || v === undefined || v === ''
}

function adminLayoutRoot (route) {
  const p = route.fullPath
  if (!p) {
    return '/a'
  }
  const ps = p.split('/')
  if (ps.length <= 1) {
    return p
  }
  return '/' + [ps[0], ps[1]].join('')
}

function toggleModalClass (visible) {
  let delta = visible
  if (typeof (visible) === 'boolean') {
    delta = visible ? 1 : -1
  }
  if ($('body > .modal').length + delta > 0) {
    $('body').addClass('modal-opened')
  } else {
    $('body').removeClass('modal-opened')
  }
}

function ref (r) {
  if (_.isArray(r) && r.length) {
    return r[0]
  }
  return r
}

function isImage (url) {
  if (!url) {
    return false
  }
  url = url.toLowerCase()
  for (const ext of [
    '.jpeg', '.jpg', 'gif', '.png'
  ]) {
    if (url.endsWith(ext)) {
      return true
    }
  }
  return false
}

function fullName (r) {
  return (r['First Name'] || r['firstName'] || '') + ' ' + (r['Last Name'] || r['lastName'] || '')
}

function avg (amount, count) {
  if (count === 0) {
    return 0
  }
  return (amount || 0) / count
}

function percent (value, total, fixed = 2) {
  const a = parseFloat(avg(value, total)) || 0
  return (a * 100).toFixed(fixed) + '%'
}

function fullUTC (s) {
  if (!s) {
    return s
  }
  if (s.indexOf('T') < 0) {
    s += 'T00:00:00+00:00'
  }
  return s
}

function dateRangePickerText (data) {
  if (!data.start || !data.end) {
    return 'All'
  }
  const start = moment(data.start).format('MMM DD, YYYY')
  const end = moment(data.end).format('MMM DD, YYYY')
  if (start === end) {
    return start
  }
  return `${start} ~ ${end}`
}

function copyToClipboard (s) {
  copy(s)
  notify('Copied to the clipboard')
}

// function phoneNumberValidator () {
//   return helpers.regex('Must be digits', /^[0-9+\s-]*$/)
// }

// function pinValidator () {
//   return helpers.regex('Must be 4 digits', /^[0-9]{4}$/)
// }

function commonUserEntityValidator (fields, isUser = true) {
  const validations = {
    entity: {}
  }
  for (const field of fields) {
    validations.entity[field] = { required }
  }
  if (isUser) {
    validations.entity['Email'] = { required, email }
    validations.entity['Phone'] = { required, phoneNumberValidator }
  }
  if (fields.indexOf('Pin') !== -1) {
    validations.entity['Pin'] = { pinValidator }
  }

  if (fields.indexOf('Barcode Number') !== -1) {
    validations.entity['Barcode Number'] = { barcodeNumberValidator }
  }

  return validations
}

export {
  allCardStatuses,
  cardStatusesByAccountNumber,
  commonUserEntityValidator,
  request,
  uploadAttachment,
  notifyResponse,
  notifySuccess,
  notify,
  notifyForm,
  notifyOptional,
  isNumeric,
  moneyFormat,
  moneyMajorAmount,
  moneyMinorAmount,
  moneyFormatHtml,
  callNative,
  isLive,
  isStaging,
  isLocal,
  isConsumer,
  isSuperAdmin,
  isRapidAccountNumber,
  isBotmAccountNumber,
  redirectRoot,
  deleteConfirm,
  pages,
  phoneNumberValidator,
  providerByAccountNumber,
  barcodeNumberValidator,
  concatInPlace,
  toSelectOptions,
  toSelectOptionsFromObject,
  fixSelectOptions,
  EventHandlerMixin,
  trendIcon,
  trendColor,
  bindState,
  bindStateReadonly,
  bindStateChild,
  bindStateSelect,
  cardStatuses,
  clf,
  clfDevice,
  copyToClipboard,
  dateRangePickerText,
  isDebugging,
  touchableDevice,
  isTouchable,
  isImage,
  loginAs,
  click,
  touch,
  noop,
  getScrollParent,
  showPlatforms,
  generateColumns,
  isEmpty,
  adminLayoutRoot,
  toggleModalClass,
  ref,
  fullName,
  fullUTC,
  avg,
  percent
}
