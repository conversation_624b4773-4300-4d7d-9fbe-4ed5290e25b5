<template>
  <q-dialog class="batch-export-dialog"
            v-model="visible"
            :preventClose="true">
    <span slot="title">Export</span>
    <div slot="body"
         class="pv-20">
      <div v-if="status === 'waiting'">
        <q-icon name="mdi-information-outline"
                size="20px"></q-icon>
        Press "Start" button to start exporting.
      </div>

      <div v-else-if="status === 'querying'">
        <q-spinner size="20px"></q-spinner>
        Querying...
      </div>

      <q-list v-else-if="status === 'exporting'"
              no-border
              separator
              highlight
              class="mt--20 w-400">
        <q-item v-for="(b, i) in batches"
                :key="i">
          <q-item-side>
            <q-item-tile>
              <q-icon v-if="b.status === 'Waiting'"
                      color="gray"
                      name="mdi-clock"
                      size="30px"></q-icon>
              <q-spinner v-else-if="b.status === 'Querying'"
                         size="30px"
                         color="primary"></q-spinner>
              <q-icon v-else
                      color="positive"
                      name="mdi-check-circle"
                      size="30px"></q-icon>
            </q-item-tile>
          </q-item-side>
          <q-item-main>
            <q-item-tile label
                         class="bold">{{ `${b.from} ~ ${b.from + b.limit}` }}</q-item-tile>
            <q-item-tile sublabel>{{ b.status }}</q-item-tile>
          </q-item-main>
        </q-item>
      </q-list>

      <div v-show="status === 'finished'">
        <q-icon name="mdi-check-circle"
                color="positive"
                size="20px"></q-icon>
        Downloading...

        <form ref="form"
              method="post"
              action="/admin/download">
          <input type="hidden"
                 name="path"
                 v-model="url">
        </form>
      </div>
    </div>
    <template slot="buttons">
      <div class="flex-center text-right">
        <q-btn flat
               label="Cancel"
               color="negative"
               @click="cancel" />
        <q-btn v-if="status === 'waiting'"
               color="primary"
               @click="start"
               label="Start"></q-btn>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import { request, notifySuccess } from '../common'
import Singleton from '../mixins/Singleton'

export default {
  name: 'BatchExportDialog',
  mixins: [
    Singleton
  ],
  props: {
    max: {
      type: Number,
      default: 5000
    },
    showAll: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      status: 'waiting',
      timestamp: null,
      action: null,
      params: null,
      batches: [],
      index: 0,
      url: ''
    }
  },
  methods: {
    show (action, params) {
      this.visible = true
      this.action = action
      this.params = params
      this.timestamp = +new Date()
      this.status = 'waiting'
      this.index = 0
      this.url = ''
    },
    cancel () {
      if (this.status === 'waiting') {
        this.visible = false
        return
      }
      this.$q.dialog({
        title: 'Cancel',
        message: 'Are you sure that you want to cancel?',
        color: 'negative',
        cancel: true
      }).then(() => {
        this.visible = false
      }).catch(() => {})
    },
    async start () {
      this.status = 'querying'

      const data = this._.cloneDeep(this.params)
      data.query_count = true

      const resp = await request(this.action, 'form', data)

      this.status = 'exporting'
      this.batches = []

      const max = this.showAll ? resp.data : this.max
      const count = resp.data
      if (count <= 0) {
        this.visible = false
        setTimeout(() => {
          notifySuccess('No data found!')
        }, 200)
        return
      }

      for (let i = 0; i < count; i += max) {
        this.batches.push({
          from: i,
          limit: (i + max) >= count ? (count - i) : max,
          status: 'Waiting'
        })
      }

      this.export()
    },
    async export () {
      if (!this.visible) {
        return
      }
      this.batches[this.index].status = 'Querying'

      const data = this._.cloneDeep(this.params)
      data.query_from = this.batches[this.index].from
      data.query_limit = this.batches[this.index].limit
      data.query_timestamp = this.timestamp
      data.query_report = true

      const resp = await request(this.action, 'form', data)
      if (!this.visible) {
        return
      }

      this.batches[this.index].status = 'Finished'
      this.index++

      if (this.index >= this.batches.length) {
        this.download(resp.data)
      } else {
        this.export()
      }
    },
    download (url) {
      if (!this.visible || !url) {
        return
      }
      this.status = 'finished'
      this.url = url

      this.$nextTick(() => {
        this.$refs.form.submit()

        setTimeout(() => {
          this.visible = false
        }, 1000)
      })
    }
  }
}
</script>

<style lang="scss">
</style>
