<template>
  <q-btn-dropdown flat dense
                  icon="edit"
                  @hide="sync">
    <q-list link>
      <q-item
          v-for="(item, index) in validColumns"
          :key="`a-${index}`"
          @click.native="toggle(item)"
      >
        <q-item-side icon="check"  v-if="!item.hidden"/>
        <q-item-side v-else/>
        <q-item-main :label="item.label" :class="{'text-grey': item.hidden}"/>
      </q-item>
    </q-list>
  </q-btn-dropdown>
</template>

<script>
import _ from 'lodash'
import { request } from '../common'

export default {
  name: 'ColumnFilter',
  props: [
    'columns'
  ],
  computed: {
    validColumns () {
      return _.filter(this.columns, c => c.field)
    }
  },
  methods: {
    toggle (item) {
      this.$set(item, 'hidden', !item.hidden)
    },

    async sync () {
      const id = this.$parent.$parent.$el.id
      if (!id) {
        return
      }
      const data = {}
      data[`${id}_visible_columns`] = _.uniq(this.validColumns
        .filter(c => !c.hidden)
        .map(c => c.field))
      const resp = await request(`/admin/user/preference`, 'post', data)
      if (resp.success) {
        this.$store.commit('User/setPreference', data)
      }
    }
  }
}
</script>
