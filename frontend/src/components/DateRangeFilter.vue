<template>
  <q-field :label="stackLabel ? undefined : label"
           class="date-range-filter"
           :class="type === 'button' ? 'date-range-filter-btn' : ''">
    <q-select v-model="range"
              v-if="type === 'select'"
              :stack-label="stackLabel ? label : undefined"
              :options="ranges"
              @input="val => detectType(val,false, true)"></q-select>

    <q-btn icon="mdi-calendar-text"
           v-else-if="type === 'button'"
           color="primary"
           class="rounded-box"
           round
           flat>
      <q-popover anchor="bottom right"
                 self="top right">
        <q-list separator
                link>
          <q-item v-for="(item, i) in ranges"
                  :key="i"
                  v-close-overlay
                  @click.native="range = item.value; detectType(range, false, true)">
            <q-item-main>
              <q-item-tile label>{{ item.label }}</q-item-tile>
            </q-item-main>
          </q-item>
        </q-list>
      </q-popover>
    </q-btn>

    <q-dialog class="date-range-filter-dialog"
              v-model="visible"
              stack-buttons
              @cancel="onCancel">
      <template slot="title">
        <span>Custom range</span>
        <q-btn class="close"
               flat
               @click="onCancel"
               icon="close" />
      </template>
      <div slot="body">
        <div class="row justify-center">
          <q-field label="Start time:"
                   class="w-240 mv-5 mh-15">
            <q-datetime v-model="startDate"
                        :max="max"
                        type="date" />
          </q-field>
          <q-field label="End time:"
                   class="w-240 mv-5 mh-15">
            <q-datetime v-model="endDate"
                        :max="max"
                        type="date" />
          </q-field>
        </div>
      </div>
      <template slot="buttons"
                slot-scope="props">
        <div class="text-center wp-100">
          <q-btn flat
                 label="cancel"
                 @click="props.cancel" />
          <q-btn label="Set"
                 class="mt-0 ml-10 min-w-150"
                 color="positive"
                 @click="setCustomRange" />
        </div>
      </template>
    </q-dialog>
  </q-field>
</template>

<script>
import _ from 'lodash'
import moment from 'moment'
import { extendMoment } from 'moment-range'

export default {
  name: 'DateRangeFilter',
  props: {
    type: {
      type: String,
      default: 'select'
    },
    label: {
      type: String,
      default: 'Date Range'
    },
    default: {
      type: String
    },
    stackLabel: {
      type: Boolean,
      default: false
    },
    isoWeek: {
      type: Boolean,
      default: false
    },
    ranges: {
      type: Array,
      default () {
        return [
          {
            label: 'All',
            value: 'all'
          },
          {
            label: 'This week',
            value: 'week'
          },
          {
            label: 'This month',
            value: 'month'
          },
          {
            label: 'This quarter',
            value: 'quarter'
          },
          {
            label: 'This year',
            value: 'year'
          },
          {
            label: 'Last 30 days',
            value: 'last_30_days'
          },
          {
            label: 'Last month',
            value: 'last_month'
          },
          {
            label: 'Last quarter',
            value: 'last_quarter'
          },
          {
            label: 'Last year',
            value: 'last_year'
          },
          {
            label: 'Custom Range',
            value: 'custom_range'
          }
        ]
      }
    }
  },
  data () {
    return {
      visible: false,
      range: 'month',
      startDate: new Date(),
      endDate: new Date(),
      max: new Date(),
      startTime: moment().startOf('month'),
      endTime: moment().endOf('month'),
      compareStartTime: moment().month(moment().month() - 1).startOf('month').valueOf(),
      compareEndTime: moment().month(moment().month() - 1).endOf('month').valueOf()
    }
  },
  watch: {
    default () {
      if (this.default) {
        this.range = this.default
        this.detectType(this.range, true)
      }
    }
  },
  methods: {
    getText () {
      const def = _.find(this.ranges, {
        value: this.range
      })
      if (def && this.range !== 'custom_range') {
        return def.label
      }
      return moment(this.startTime).format('L') + ' ~ ' + moment(this.endTime).format('L')
    },
    onCancel () {
      this.visible = false
    },
    setCustomRange () {
      const Moment = extendMoment(moment)
      this.startTime = this.startDate
      this.endTime = this.endDate
      const range = Moment.range(this.startTime, this.endTime)
      range.snapTo('day')
      let days = range.diff('days')
      this.compareStartTime = moment(this.startTime).subtract(1, 'days')
      this.compareEndTime = moment(this.compareStartTime).subtract(days, 'days')
      _.last(this.ranges).label = moment(this.startDate).format('L') + '~' + moment(this.endDate).format('L')
      this.visible = false

      this.$nextTick(() => {
        setTimeout(() => {
          this.$emit('change', this.reloadParams())
        }, 300)
      })
    },
    detectType (val, silent = false, click = false) {
      if (val === 'custom_range' || (click && val.indexOf('custom_range') !== -1)) {
        this.visible = true
      } else {
        switch (val) {
          case 'all':
            this.startTime = null
            this.endTime = null
            this.compareStartTime = null
            this.compareEndTime = null
            break
          case 'week':
            if (this.isoWeek) {
              this.startTime = moment().startOf('isoWeek')
              this.endTime = moment().endOf('isoWeek')
              this.compareStartTime = moment().isoWeek(moment().isoWeek() - 1).startOf('isoWeek').valueOf()
              this.compareEndTime = moment().isoWeek(moment().isoWeek() - 1).endOf('isoWeek').valueOf()
            } else {
              this.startTime = moment().startOf('week')
              this.endTime = moment().endOf('week')
              this.compareStartTime = moment().week(moment().week() - 1).startOf('week').valueOf()
              this.compareEndTime = moment().week(moment().week() - 1).endOf('week').valueOf()
            }
            break
          case 'month':
            this.startTime = moment().startOf('month')
            this.endTime = moment().endOf('month')
            this.compareStartTime = moment().month(moment().month() - 1).startOf('month').valueOf()
            this.compareEndTime = moment().month(moment().month() - 1).endOf('month').valueOf()
            break
          case 'last_30_days':
            this.startTime = moment().subtract(29, 'days')
            this.endTime = moment()
            this.compareStartTime = moment().subtract(1, 'days')
            this.compareEndTime = moment().subtract(59, 'days')
            break
          case 'quarter':
            this.startTime = moment().startOf('quarter')
            this.endTime = moment().endOf('quarter')
            this.compareStartTime = moment().quarter(moment().quarter() - 1).startOf('quarter').valueOf()
            this.compareEndTime = moment().quarter(moment().quarter() - 1).endOf('quarter').valueOf()
            break
          case 'year':
            this.startTime = moment().startOf('year')
            this.endTime = moment().endOf('year')
            this.compareStartTime = moment().year(moment().year() - 1).startOf('year').valueOf()
            this.compareEndTime = moment().year(moment().year() - 1).endOf('year').valueOf()
            break
          case 'today':
            this.startTime = this.startDate
            this.endTime = this.endDate
            this.compareStartTime = moment(this.startTime).subtract(1, 'days')
            this.compareEndTime = moment(this.compareStartTime).subtract(1, 'days')
            break
          case 'last_week':
            this.startTime = moment().subtract(1, 'week').startOf('week')
            this.endTime = moment().subtract(1, 'week').endOf('week')
            this.compareStartTime = moment().week(moment().week() - 2).startOf('week').valueOf()
            this.compareEndTime = moment().week(moment().week() - 2).endOf('week').valueOf()
            break
          case 'last_month':
            this.startTime = moment().subtract(1, 'month').startOf('month')
            this.endTime = moment().subtract(1, 'month').endOf('month')
            this.compareStartTime = moment().month(moment().month() - 2).startOf('month').valueOf()
            this.compareEndTime = moment().month(moment().month() - 2).endOf('month').valueOf()
            break
          case 'last_quarter':
            this.startTime = moment().subtract(1, 'quarter').startOf('quarter')
            this.endTime = moment().subtract(1, 'quarter').endOf('quarter')
            this.compareStartTime = moment().quarter(moment().quarter() - 2).startOf('quarter').valueOf()
            this.compareEndTime = moment().quarter(moment().quarter() - 2).endOf('quarter').valueOf()
            break
          case 'last_year':
            this.startTime = moment().subtract(1, 'year').startOf('year')
            this.endTime = moment().subtract(1, 'year').endOf('year')
            this.compareStartTime = moment().year(moment().year() - 2).startOf('year').valueOf()
            this.compareEndTime = moment().year(moment().year() - 2).endOf('year').valueOf()
            break
        }

        if (!silent) {
          this.$nextTick(() => {
            setTimeout(() => {
              this.$emit('change', this.reloadParams())
            }, 300)
          })
        }
      }
    },
    params () {
      const data = {}
      data.start = this.startTime ? moment(this.startTime).format('L') : null
      data.end = this.endTime ? moment(this.endTime).format('L') : null
      data.period = this.range
      return data
    },
    reloadParams () {
      const data = {}
      data.range = this.range
      data.start = this.startTime ? moment(this.startTime).format('L') : null
      data.end = this.endTime ? moment(this.endTime).format('L') : null
      data.compareStart = this.compareStartTime ? moment(this.compareStartTime).format('L') : null
      data.compareEnd = this.compareEndTime ? moment(this.compareEndTime).format('L') : null
      return data
    }
  },
  mounted () {
    if (this.default) {
      this.range = this.default
      this.detectType(this.range, true)
    }
  }
}
</script>

<style lang="scss">
@import "../css/variable";

.date-range-filter {
  width: 300px;
  margin-bottom: 0 !important;

  &.date-range-filter-btn {
    width: auto;
    min-width: 0 !important;
  }

  .q-field-label {
    flex-basis: 85px !important;
  }

  .q-field-content {
    flex-grow: 1;
    flex-basis: 0;
  }
}

@media (max-width: 434px) {
  .date-range-filter {
    width: 200px;
  }
}

#admin.cp_clf .clf_page .date-range-filter {
  .q-field-label-inner {
    height: 44px;
  }
}

.date-range-filter-dialog {
  .close {
    position: absolute;
    right: 10px;
    top: 10px;
  }

  .q-field-label {
    font-size: 12px;
    font-weight: bold;
    .q-field-label-inner {
      min-height: 30px;
      color: rgba(0, 0, 0, 0.87);
    }
  }
  .q-if:before,
  .q-if:after {
    background: none;
  }
  .q-datetime-input {
    background: white;
    border-radius: 3px;
    padding: 4px 8px;
    border: 1px solid #e0e0e0;
    font-size: 13px;
  }
  .q-if {
    border-radius: 3px;

    &.q-if-focused {
      border-color: transparent;
      box-shadow: 0 0 0 2px var(--q-color-positive);

      .q-if-control {
        color: var(--q-color-positive) !important;
      }
    }
  }
}
</style>
