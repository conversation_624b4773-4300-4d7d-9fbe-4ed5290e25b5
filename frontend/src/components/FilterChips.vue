<template>
  <div class="table-filter-chips">
    <q-chip v-for="(f, i) in validFilters"
            :key="i"
            :class="{readonly: f.readonly}">
      {{ describe(f) }}

      <q-btn flat round
             size="xs"
             v-if="!f.readonly"
             @click="remove(f)"
             icon="mdi-close"></q-btn>
    </q-chip>
  </div>
</template>

<script>
import _ from 'lodash'
import moment from 'moment'
import { predicates } from '../const'
import { isEmpty } from '../common'

export default {
  name: 'FilterChips',
  props: [
    'filters',
    'options'
  ],
  computed: {
    validFilters () {
      return this.filters.filter(f => this.valid(f))
    }
  },
  methods: {
    valid (filter) {
      return filter.field && (!isEmpty(filter.value) || !isEmpty(filter.value_0) || !isEmpty(filter.value_1))
    },
    describe (filter) {
      const option = _.find(this.options, { value: filter.field })
      const subject = option ? option.label : filter.field

      const predicateDefine = _.find(predicates, { value: filter.predicate || '=' })
      let predicate = predicateDefine ? predicateDefine.label.toLowerCase() : filter.predicate
      console.log(filter)
      console.log(option)
      let object = filter.value
      if (option.range) {
        const parts = []
        for (let i = 0; i < 2; i++) {
          let value = filter[`value_${i}`]
          if (!isEmpty(value)) {
            const type = option.range[i].type
            value = this.formatItem(type, value)
          }
          parts.push(value)
        }

        if (!isEmpty(parts[0]) && isEmpty(parts[1])) {
          predicate += ' >='
          object = parts[0]
        } else if (isEmpty(parts[0]) && !isEmpty(parts[1])) {
          predicate += ' <='
          object = parts[1]
        } else {
          predicate += ' in'
          object = `${parts[0]} ~ ${parts[1]}`
        }
      } else if (option.options) {
        const selected = _.find(option.options, { value: filter.value })
        if (selected) {
          object = selected.label
        }
      } else if (option.type) {
        object = this.formatItem(option.type, object)
      }

      return `${subject} ${predicate} ${object} ${this.describeSuffix(option, filter)}`
    },
    formatItem (type, value) {
      if (type === 'date') {
        value = moment(value).format('L')
      } else if (type === 'datetime') {
        value = moment(value).format('L LT')
      } else if (type === 'time') {
        value = moment(value).format('LT')
      } else if (type === 'amount') {
        value = `$${value}`
      }
      return value
    },
    describeSuffix (option, filter) {
      if (option.and && filter.value_and) {
        return 'AND ' + this.describe({
          field: option.and,
          predicate: option.predicate,
          value: filter.value_and
        })
      }
      return ''
    },
    remove (filter) {
      const pos = this.filters.indexOf(filter)
      this.filters.splice(pos, 1)

      this.$emit('reload')
    }
  }
}
</script>

<style lang="scss">
  .table-filter-chips {
    margin: 0 20px;
    padding-left: 20px;
    border-left: 1px solid #ddd;
    max-width: 92%;

    .q-chip {
      border-radius: 4px;
      background: #DBDBDB;
      padding: 0 2px 0 8px;
      min-height: 28px;
      margin: 3px 10px 3px 0;

      &.readonly {
        padding-right: 8px;
      }

      .q-btn.q-btn-round {
        padding: 0 !important;
        width: 22px;
        height: 22px;

        .q-icon {
          font-size: 18px;
        }
      }
    }
  }

  .q-table-control > .table-filter-chips:first-child {
    padding-left: 0;
    margin-left: 0;
    border-left: none;
  }
</style>
