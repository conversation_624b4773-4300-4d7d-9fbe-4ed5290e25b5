<template>
  <div class="font-picker">
    <div class="col preview" :style="{fontFamily: value}">
      <div>
        <strong>{{ value }}</strong>

        <q-btn label="Select"
               size="sm"
               class="btn-select"
               icon-right="mdi-chevron-down">
          <q-popover>
            <q-list separator link>
              <q-item v-for="(item, i) in items"
                      :key="i"
                      v-close-overlay
                      @click.native="select(item)">
                <q-item-main>
                  <q-item-tile label :style="fontStyle(item)">{{ item.family }}</q-item-tile>
                </q-item-main>
              </q-item>
              <q-item v-if="items.length <= 0">
                <q-item-main label="Loading..."></q-item-main>
              </q-item>
            </q-list>
          </q-popover>
        </q-btn>
      </div>
      <div>The quick brown fox jumps over the lazy dog.</div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'FontPicker',
  props: {
    value: {
      type: String
    }
  },
  data () {
    return {
      items: []
    }
  },
  methods: {
    async init () {
      const resp = await axios.get(`https://www.googleapis.com/webfonts/v1/webfonts?key=${this.$c.keys.GOOGLE_WEBSITE}&sort=popularity`)
      this.items = resp.data.items.slice(0, 50)

      this.items.forEach(item => {
        let url = item.files.regular
        if (!url) {
          return
        }

        if (url.startsWith('http://')) {
          url = url.replace('http://', 'https://')
        }

        const fontFace = new FontFace(item.family, `url('${url}')`)
        document.fonts.add(fontFace)
      })
    },

    select (item) {
      this.$emit('change', item)
    },

    fontStyle (item) {
      return {
        fontFamily: item.family
      }
    }
  },
  mounted () {
    this.init()
  }
}
</script>

<style lang="scss">
.font-picker {
  > .preview {
    font-size: 14px;
    line-height: 25px;
  }

  .btn-select {
    float: right;
    padding: 4px 8px !important;
  }
}
</style>
