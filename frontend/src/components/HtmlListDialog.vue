<template>
  <q-dialog class="html-list-dialog"
            v-model="visible">
    <span slot="title">{{ entity.title || 'Message' }}</span>
    <div slot="body">
      <p v-if="entity.message"
         class="mb-30">{{ entity.message }}</p>
      <div v-if="html"
           v-html="html"
           class="text-left"></div>
      <q-table :data="entity.list.data"
               :columns="entity.list.columns"
               v-if="entity.list">
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <template v-for="c in entity.list.columns">
            <q-td v-if="entity.list.htmlColumns && entity.list.htmlColumns.includes(c.field)"
                  :key="c.field"
                  v-html="sanitize(props.row[c.field])">
            </q-td>
            <q-td v-else
                  :key="c.field"
                  v-text="props.row[c.field]"></q-td>
          </template>
        </q-tr>
      </q-table>
    </div>
    <template slot="buttons">
      <div class="flex-center text-right">
        <q-btn flat
               label="OK"
               color="primary"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../mixins/Singleton'

export default {
  name: 'html-list-dialog',
  mixins: [
    Singleton
  ],
  computed: {
    html () {
      if (this.entity && this.entity.html) {
        return this.sanitize(this.entity.html)
      }
      return null
    }
  }
}
</script>

<style lang="scss">
.html-list-dialog {
  .q-table td {
    text-align: left;

    b {
      font-weight: 600;
    }
  }
}
</style>
