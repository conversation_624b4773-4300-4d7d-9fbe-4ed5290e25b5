<template>
  <q-layout class="json-record">
    <q-input
      v-model="recordId"
      @keyup.enter="loadRecordDetail"
      :placeholder="placeholder"
      outlined
      dense
      class="mb-20"
    ></q-input>
    <json-viewer
      v-if="_.values(recordData).length > 0"
      :value="recordData"
      :expand-depth="5"
      :expanded="true"
      :show-double-quotes="false"
      copyable
      boxed
      sort
    ></json-viewer>
    <span v-else>No data</span>
  </q-layout>
</template>

<script>
import JsonViewer from 'vue-json-viewer'
export default {
  name: 'JsonRecord',
  data () {
    return {
      recordId: null
    }
  },
  props: {
    record: {
      type: Object,
      required: true
    },
    placeholder: {
      type: String,
      required: true
    }
  },
  components: {
    JsonViewer
  },
  computed: {
    recordData () {
      return Object.assign({}, this.record)
    }
  },
  methods: {
    loadRecordDetail () {
      this.$emit('load-record-detail', this.recordId)
    }
  }
}
</script>
<style lang="scss">
.json-record {
}
</style>
