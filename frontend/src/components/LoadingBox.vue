<template>
  <div class="common-loading-comp-box">
    <slot></slot>

    <q-inner-loading :visible="loading">
      <q-spinner :size="30"></q-spinner>
    </q-inner-loading>
  </div>
</template>

<script>
export default {
  name: 'Loading<PERSON><PERSON>',
  props: [
    'loading'
  ]
}
</script>

<style lang="scss">
  .common-loading-comp-box {
    position: relative;
    width: 100%;
    min-height: 80px;
  }
</style>
