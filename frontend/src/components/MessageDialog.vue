<template>
  <q-dialog
    class="message-dialog"
    v-model="visible"
    :preventClose="true"
    @cancel="text = null; _hide()"
  >
    <template slot="title">
      <div class="mb-5">
        <div class="radius-box" :class="entity.type === 'success' ? 'positive' : 'negative'">
          <q-icon v-if="entity.type === 'success'" name="mdi-check" class="font-30 positive"></q-icon>
          <q-icon v-else-if="entity.type === 'error'" name="mdi-close" class="font-30 negative"></q-icon>
        </div>
      </div>
      <span class="bold" v-if="entity" v-html="entity.title || ' '"></span>
      <q-btn class="close" round flat @click="text = null; _hide()" icon="close"/>
    </template>
    <div slot="body">
      <p v-if="entity && entity.message">{{ entity.message }}</p>
    </div>
    <template slot="buttons">
      <div class="">
        <q-btn :label="entity.okLabel || 'OK'"
               color="primary"
               @click="text = true; _hide()"/>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../mixins/Singleton'

export default {
  name: 'message-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      text: null
    }
  },
  methods: {
    show () {
      this.text = null
    },
    hide () {
      if (this.entity.callback) {
        this.entity.callback(this.text)
      }
    }
  }
}
</script>

<style lang="scss">
  .message-dialog {
    .modal-content {
      width: 360px;
    }

    .positive {
      color: var(--q-color-positive);
      background: rgba($color: var(--q-color-positive), $alpha: 0.1) !important;
    }

    .negative {
      background-color: rgba($color: var(--q-color-negative), $alpha: 0.1);
      color: var(--q-color-negative) !important;
    }

    .radius-box {
      .q-icon {
        /*color: var(--span-color-primary);*/
      }
    }

    .modal-buttons {
      display: block;

      button {
        width: 100%;
        margin-left: 0 !important;

        &.btn-introduce {
          background: var(--span-color-primary) !important;
        }
      }
    }
  }
</style>
