<template>
  <div class="row flex-center q-py-sm span-pagination">
    <div class="q-mr-sm">
      {{ (pagination.page - 1) * rowsPerPage + 1 }}-{{ _.min([pagination.page * rowsPerPage, pagination.rowsNumber]) }}
      of {{ pagination.rowsNumber }}
    </div>
    <q-btn
        round dense flat
        icon="mdi-chevron-double-left"
        class="q-mr-sm"
        :disable="props.isFirstPage"
        @click="pagination.page = 1; reload()"
    />
    <q-btn
        round dense flat
        icon="mdi-chevron-left"
        class="q-mr-sm"
        :disable="props.isFirstPage"
        @click="props.prevPage"
    />
    <q-btn
        round dense flat
        icon="mdi-chevron-right"
        class="q-mr-sm"
        :disable="props.isLastPage"
        @click="props.nextPage"
    />
    <q-btn
        round dense flat
        icon="mdi-chevron-double-right"
        class="q-mr-sm"
        :disable="props.isLastPage"
        @click="pagination.page = props.pagesNumber; reload()"
    />
    <div class="q-mr-sm">
      Page:
    </div>
    <q-select v-model="pagination.page"
              filter
              @input="pagination.page = $event; reload()"
              :options="c.pages(props)"></q-select>

    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'Pagination',
  props: [
    'props',
    'pagination'
  ],
  computed: {
    rowsPerPage () {
      if (this.pagination.rowsPerPage <= 0) {
        return Number.MAX_SAFE_INTEGER
      }
      return this.pagination.rowsPerPage
    }
  },
  methods: {
    reload () {
      this.$emit('reload')
    }
  }
}
</script>
