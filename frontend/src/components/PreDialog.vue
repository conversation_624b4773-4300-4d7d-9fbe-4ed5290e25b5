<template>
  <q-dialog
    class="pre-dialog"
    v-model="visible"
    :preventClose="true">
    <span slot="title" v-if="entity" v-html="entity.title"></span>
    <div slot="body" class="pre-dialog-body">
      <pre v-if="entity" v-html="entity.content"></pre>
    </div>
  </q-dialog>
</template>

<script>
import Singleton from '../mixins/Singleton'

export default {
  name: 'pre-dialog',
  mixins: [
    <PERSON>ton
  ]
}
</script>

<style lang="scss">
.pre-dialog-body {
  text-align: left;
  overflow: auto;
  padding: 10px 0;

  > pre {
    text-wrap: wrap;
  }
}
</style>
