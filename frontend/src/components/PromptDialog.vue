<template>
  <q-dialog
    class="prompt-dialog"
    v-model="visible"
    :preventClose="true">
    <span slot="title" v-if="entity" v-html="entity.title || 'Prompt'"></span>
    <div slot="body" class="pv-20">
      <p v-if="entity && entity.message"
         v-html="entity.message"></p>
      <q-input v-model="text" class="mv-10"></q-input>
    </div>
    <template slot="buttons">
      <div class="flex-center text-right">
        <q-btn flat label="Cancel"
               color="gray"
               class="mr-10"
               @click="text = null; _hide()" />
        <q-btn label="Continue"
               color="primary"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../mixins/Singleton'

export default {
  name: 'prompt-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      text: null
    }
  },
  methods: {
    show () {
      this.text = null
    },
    hide () {
      if (this.entity.callback) {
        this.entity.callback(this.text)
      }
    }
  }
}
</script>
