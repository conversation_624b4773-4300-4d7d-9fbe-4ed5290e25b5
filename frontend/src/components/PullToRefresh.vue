<template>
  <div class="tc_pull_refresh"
       :class="compClass"
       :style="style">
    <div class="tc_pull_refresh__action">
      <q-spinner :size="20" v-if="loading"></q-spinner>
      <q-icon :name="icon" v-else></q-icon>
      {{ text }}
    </div>
    <div class="tc_pull_refresh__scroll"
         @touchstart="touchStart"
         @touchmove="touchMove"
         @touchend="touchEnd">
      <slot></slot>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'

export default {
  name: 'PullToRefresh',
  data () {
    return {
      startY: -1,
      currentY: -1,
      backing: true,
      done: false
    }
  },
  props: {
    loading: {
      type: Boolean,
      required: true
    },
    check: {
      type: Function,
      required: false
    }
  },
  watch: {
    loading (newVal, oldVal) {
      if (!newVal && oldVal) {
        this.done = true
        setTimeout(() => {
          this.done = false
        }, 500)
      }
    }
  },
  computed: {
    compClass () {
      return [
        (this.loading || this.done) ? 'loading' : '',
        this.backing ? 'back' : '',
        this.ready ? 'ready' : ''
      ]
    },
    icon () {
      return this.done ? 'mdi-check-circle-outline' : 'mdi-arrow-down'
    },
    pullDelta () {
      let delta = Math.max(this.currentY - this.startY, 0)
      return Math.pow(Math.log2(delta + 1), Math.LN10)
    },
    style () {
      return {
        transform: `translateY(${this.pullDelta}px)`
      }
    },
    text () {
      const config = this.cc.pullConfig
      if (this.loading) {
        return config.loadingText
      }
      if (this.done) {
        return config.doneText
      }
      return this.ready ? config.triggerText : config.pullText
    },
    ready () {
      return this.pullDelta > 80
    }
  },
  methods: {
    reload () {
      this.$emit('reload')
    },
    touchCheck ($event) {
      if (typeof (this.check) === 'function') {
        return this.check($event, this)
      }
      if ($($event.target).parents('.q-infinite-scroll').length) {
        if ($event.touches[0].pageY >= this.currentY) {
          const is = this.$el.querySelector('.q-infinite-scroll')
          if (is && is.scrollTop > 0) {
            return false
          }
        } else {
          if ($event.cancelable) {
            $event.preventDefault()
          }
        }
      }
      return true
    },
    touchStart ($event) {
      if (!this.touchCheck($event)) {
        return
      }
      this.done = false
      this.backing = false
      this.startY = $event.touches[0].pageY
    },
    touchMove ($event) {
      if (!this.touchCheck($event)) {
        return
      }
      if (this.startY < 0) {
        this.startY = $event.touches[0].pageY
      }
      this.currentY = $event.touches[0].pageY
    },
    touchEnd () {
      this.backing = true
      if (this.ready) {
        this.reload()
      }
      this.startY = this.currentY = -1
    }
  }
}
</script>

<style lang="scss">
  .tc_pull_refresh {
    height: calc(100vh - 51px - var(--safe-area-inset-top));
    display: flex;
    flex-direction: column;
    overflow: visible;

    &.auto {
      height: auto;

      .tc_pull_refresh__scroll {
        height: auto;
      }
    }

    &.loading {
      transform: translateY(calc(51px + var(--safe-area-inset-top))) !important;
    }

    &.back {
      transition: transform 0.3s;
    }

    &.ready {
      .tc_pull_refresh__action {
        .q-icon {
          transform: rotate(180deg);
        }
      }
    }

    .tc_pull_refresh__action {
      margin-top: calc(-51px - var(--safe-area-inset-top));
      height: calc(51px + var(--safe-area-inset-top));
      min-height: calc(51px + var(--safe-area-inset-top));
      flex-basis: calc(51px + var(--safe-area-inset-top));
      display: flex;
      justify-content: center;
      align-items: center;
      color: var(--q-color-primary);

      .q-icon {
        font-size: 20px;
        margin-right: 5px;
        transition: transform 0.3s;
      }

      .q-spinner {
        margin-right: 5px;
      }
    }

    .tc_pull_refresh__scroll {
      height: calc(100vh - 51px - var(--safe-area-inset-top));
    }

    .list-msg {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      flex-direction: column;
      color: #aaa;
      margin-top: 30px;

      .q-icon {
        font-size: 40px;
        display: block;
        margin-top: -10px;
        margin-bottom: 6px;
        color: #ccc;
      }
    }
  }
</style>
