<template>
  <q-dialog
      class="session-detect-dialog"
      v-model="visible"
      :preventClose="true">
    <span slot="title">Session timeout</span>
    <div slot="body" class="pv-20">
      Your session is timeout. Would you like to resume the session?
    </div>
    <template slot="buttons">
      <div class="flex-center text-right">
        <q-btn color="primary"
               @click="resumeSession"
               :loading="resuming"
               :label="resumeText"></q-btn>
        <q-btn label="End Now"
               :disable="resuming"
               color="negative"
               @click="endSession" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import { request } from '../common'
import Singleton from '../mixins/Singleton'

export default {
  name: 'session-detect-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      left: 30,
      resuming: false,
      interval: null
    }
  },
  computed: {
    resumeText () {
      return `Resume (${this.left})`
    }
  },
  methods: {
    async resumeSession () {
      this.stopCounting()
      this.resuming = true
      const resp = await request('/resume-session', 'post', {}, true)
      this.resuming = false
      if (resp.success) {
        this._hide()
      } else {
        this.endSession()
      }
    },
    endSession () {
      this.stopCounting()
      this._hide()
      location.href = '/logout'
    },
    async detect () {
      if (this.$store.state.User.env === 'dev') {
        return
      }

      const resp = await request('/detect-session', 'get', {}, true)
      if (!resp.success && !this.visible) {
        this.countDown()
      } else if (resp.success && this.visible) {
        this.stopCounting()
        this._hide()
      }
    },
    countDown () {
      this.visible = true
      this.left = 30
      this.interval = setInterval(() => {
        this.left--
        if (this.left <= 0) {
          this.endSession()
        }
      }, 1000)
    },
    stopCounting () {
      if (this.interval) {
        clearInterval(this.interval)
        this.interval = null
      }
    },
    show () {
      this.countDown()
      this.detect()
    }
  },
  mounted () {
    setInterval(this.detect, 5 * 60 * 1000)
  }
}
</script>

<style lang="scss">
</style>
