<template>
  <div id="top_chart_page">
    <div class="chart" :id="chartId + '_chart_graph'" v-show="!isSummary"></div>
    <div v-if="chartId === chartName.LOAD_CHART">
      <load-activity-summary :data="data"  :chart-type="chartType" :is-amount-u-s-d="true" v-if="isSummary"></load-activity-summary>
    </div>
    <div v-if="chartId === chartName.ACTIVE_CHART">
      <common-activity-summary :types="data.dates" :items="data.data" v-if="isSummary"></common-activity-summary>
    </div>
    <div v-if="chartId === chartName.USAGE_CHART">
      <common-activity-summary :types="data.usageType" :items="data.data" :is-amount-u-s-d="true" v-if="isSummary"></common-activity-summary>
    </div>
    <div v-if="chartId === chartName.CARD_FEES_CHART">
      <common-activity-summary :types="data.types" :items="data.data" v-if="isSummary"></common-activity-summary>
    </div>
    <div v-if="chartId === chartName.REVENUE_CHART">
      <common-activity-summary :types="data.dates" :items="data.data" v-if="isSummary"></common-activity-summary>
    </div>
    <div v-if="chartId === chartName.USER_CHART">
      <common-activity-summary :types="data.types" :items="data.data" v-if="isSummary"></common-activity-summary>
    </div>
  </div>
</template>

<script>
import loadActivitySummary from '../../src/pages/dashboard/analytics/summary/loadActivitySummary'
import commonActivitySummary from '../../src/pages/dashboard/analytics/summary/CommonActivitySummary'
import TopChartMixin from '../mixins/TopChartMixin'
import { chartName } from '../const'
import eventBus from '../eventBus'
import $ from 'jquery'
import _ from 'lodash'

export default {
  name: 'TopChart',
  mixins: [
    TopChartMixin
  ],
  data () {
    return {
      chartName: chartName
    }
  },
  components: {
    loadActivitySummary,
    commonActivitySummary
  },
  methods: {
  },
  mounted () {
    if (!_.isEmpty(this.$route.query)) {
      $('.chart').css('height', 200)
      this.getChartSetting()
    }
    eventBus.$on('reload-top-chart', arg => {
      this.reloadChart(arg)
    })
  }
}
</script>

<style lang="scss">
#top_chart_page {
  background: white;
  border-radius: 6px;
  margin: 20px 0;
  .summary-data {
    padding: 0 50px;
  }
  .chart {
    background: white;
    border-radius: 6px;
  }
}
</style>
