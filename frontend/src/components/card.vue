<template>
  <div class="card-box">
    <q-inner-loading :visible="!image">
      <q-spinner :size="20"></q-spinner>
    </q-inner-loading>
    <img :src="image" alt="">
    <div class="number" v-text="number" :style="style"></div>
    <div class="holder" v-text="holder" :style="style"></div>
  </div>
</template>

<script>
import $ from 'jquery'

export default {
  props: [
    'image',
    'number',
    'holder'
  ],
  data () {
    return {
      scale: 1
    }
  },
  computed: {
    pan () {
      return this.number || '4000 1234 5678 9010'
    },
    style () {
      if (this.scale > 1) {
        return {
          fontSize: `${this.scale * 12}px`
        }
      }
      return {
        transform: `scale(${this.scale}) translateX(${(this.scale - 1) / 2 * 100}%)`
      }
    }
  },
  methods: {
    resize () {
      const width = $(this.$el).width()
      this.scale = Math.max(width / 160, 0.7)
    }
  },
  mounted () {
    $(window).on('resize', this.resize)
    this.resize()
  },
  beforeDestroy () {
    $(window).off('resize', this.resize)
  }
}
</script>

<style lang="scss">
  .card-box {
    position: relative;
    color: white;
    font-size: 12px;

    .q-inner-loading {
      color: black;
    }

    > img {
      width: 100%;
      min-height: 80px;
      display: inline-block;
    }

    .number {
      position: absolute;
      text-shadow: 0 2px 2px black;
      left: 5%;
      top: 45%;
    }

    .holder {
      position: absolute;
      text-shadow: 0 2px 2px black;
      left: 5%;
      bottom: 12%;
    }
  }
</style>
