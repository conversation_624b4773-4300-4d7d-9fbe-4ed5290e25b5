<template>
  <div class="number-span">
    <span class="prefix" v-if="prefix">{{ prefix }}</span>
    <span>{{ value }}</span>
    <span class="suffix" v-if="suffix">{{ suffix }}</span>
  </div>
</template>

<script>
export default {
  props: [
    'value',
    'prefix',
    'suffix'
  ]
}
</script>

<style lang="scss">
  .number-span {
    display: inline-block;

    .prefix, .suffix {
      transform: scale(0.6) translate(50%, -10%);
      opacity: 0.7;
      display: inline-block;
    }
  }
</style>
