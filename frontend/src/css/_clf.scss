.btn-gray-light {
  background: #EBEBEB !important;
  border-color: black !important;
  color: black !important;

  &:hover {
    background: black !important;
    border-color: black !important;
  }
}

.btn-red-light {
  background: #F9E0E3 !important;
  border-color: #D0031B !important;
  color: #D0031B !important;

  &:hover {
    background: #D0031B !important;
    border-color: #D0031B !important;
  }
}

.btn-green-light {
  background: #f2fff8 !important;
  border-color: #388E3C !important;
  color: #388E3C !important;

  &:hover {
    background: #388E3C !important;
    border-color: #388E3C !important;
  }
}

.btn-gray-light,
.btn-red-light,
.btn-green-light {
  &:hover {
    color: white !important;
  }

  &.disabled {
    opacity: 0.3 !important;
  }
}

.form-table, .table-form {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 2px;
  }

  th {
    font-weight: bold;
    text-align: right;
    padding-right: 10px;
  }

  &.table-form-light, &.form-table-light {
    th {
      font-weight: normal;
    }

    td {
      font-weight: 300;
    }
  }

  &.form-table-lg, &.table-form-lg {
    th, td {
      padding: 6px;
      font-size: 14px;
    }

    th {
      color: #333;
    }
  }

  &.form-table-xlg, &.table-form-xlg {
    th, td {
      padding: 4px;
      font-size: 16px;
    }

    th {
      color: #333;
    }
  }

  &.form-table-left, &.table-form-left {
    th {
      text-align: left;
    }
  }
}

.line-table {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 4px;
    border-bottom: 1px solid #eee;
  }

  tr:last-of-type {
    th, td {
      border-bottom-color: transparent;
    }
  }
}
