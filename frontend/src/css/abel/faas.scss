.cp_faas,
body > .modal[class*=" faas-"],
body > .modal[class*=" spendr-"],
.message-dialog {
  .radius-box {
    border-radius: 50%;
    display: inline-flex;
    width: 44px;
    height: 44px;
    padding: 0;
    justify-content: center;
    align-items: center;
    color: var(--span-color-primary);
    border: none;
    position: relative;

    &:before {
      content: ' ';
      width: 100%;
      height: 100%;
      background-color: currentColor;
      border-radius: 50%;
      opacity: 0.2;
      position: absolute;
    }
  }
}

body.within-iframe {
  .faas-widget-dialog {
    .modal-header {
      .q-btn.close {
        display: none;
      }
    }

    .modal-content {
      max-width: 100vw;
      max-height: 100vh;
      width: 100vw;
      height: 100vh;
      border-radius: 0;
      border: none;
      box-shadow: none;

      .q-list.rounded-list {
        max-height: none;
      }
    }

    .modal-scroll {
      max-height: none;
    }
  }

  .hide-in-iframe {
    display: none !important;
  }
}
