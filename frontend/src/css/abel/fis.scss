.common-dashboard-card[class*="fis-dashboard-"] {
  .row.programs {
    width: 100%;
    height: 75px;
    flex-direction: column;
    flex-wrap: wrap;

    .col-6 {
      padding: 3px 0 0 6px;
      transform: scale(0.9);
    }

    > div {
      &.active {
        font-weight: 900;
      }
    }

    .circle {
      width: 14px;
      height: 14px;
      border-radius: 50%;
      box-shadow: 0 2px 5px #999;
      display: inline-block;
      margin-right: 5px;
    }

    .program-label {
      display: inline-block;
      font-size: 12px;
      vertical-align: top;
      max-width: calc(100% - 19px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .day-selector {
    display: flex;
    padding: 0 0 5px;
    align-items: center;

    &.initialized {
      background-color: #F1F1FF;
    }

    .btn-arrow {
      padding: 4px !important;
      color: #C384FE;

      .q-btn-inner {
        font-size: 16px;
      }
    }

    .dates-container {
      position: relative;
      overflow: hidden;

      > .dates {
        display: flex;
        justify-content: space-evenly;

        > .q-btn {
          color: #C384FE;
          opacity: 0.7;

          .q-btn-inner {
            font-size: 12px;
            line-height: 1.2;
            text-transform: capitalize;
            transform: scale(0.9);
          }

          &.active {
            font-weight: 900;
            color: #9A47FF;
            opacity: 1;
          }
        }
      }
    }
  }

  &.fis-dashboard-data-zoom {
    .q-card-main {
      &.q-card-container {
        overflow: hidden !important;
      }

      .header {
        padding: 0 28px 0 14px;

        .title {
          .value {
            font-size: 26px;
            color: #333;
          }

          .label {
            font-size: 12px;
            color: #999;
          }
        }

        .right {
          margin-left: auto;
          text-align: right;

          .label {
            font-size: 12px;
            color: silver;
          }

          .value {
            font-size: 16px;
            color: #555;
          }
        }
      }
    }

    .day-selector {
      .btn-arrow {
        color: #666;
      }

      .dates-container {
        > .dates > .q-btn {
          color: #666;

          &.active {
            color: #333;
          }
        }
      }
    }

    .chart-container {
      margin-top: auto !important;
      height: calc(100% - 50px) !important;
    }
  }
}

.fis_report_page, .fis__dashboard__index_page.graph__dashboard__index_page {
  .fun-group {
    .q-field {
      min-width: 140px !important;
      margin: 6px 20px 6px 0 !important;

      .q-field-label {
        flex-basis: 40px;

        .q-field-label-inner {
          min-height: 34px;
        }
      }

      .q-field-content {
        flex-grow: 1;
        flex-basis: 0;
      }

      &.currency-field {
        .q-field-label {
          flex-basis: 70px;
        }
      }

      &.w70-field {
        .q-field-label {
          flex-basis: 70px;
        }
      }

      &.w55-field {
        .q-field-label {
          flex-basis: 55px;
        }
      }

      &.w100-field {
        .q-field-label {
          flex-basis: 100px;
        }
      }

      &.w120-field {
        .q-field-label {
          flex-basis: 120px;
        }
      }

      &.w130-field {
        .q-field-label {
          flex-basis: 130px;
        }
      }

      &.proxy-field {
        .q-field-label {
          flex-basis: 137px;
        }
      }

      &.range-dates-field {
        .q-field-label-inner {
          line-height: 32px;
        }

        .q-field-content {
          display: flex;

          .q-if + .q-if {
            margin-left: 6px;
          }

          .q-datetime-input {
            display: none;
            min-width: 122px;

            &.show {
              display: flex;
            }
          }

          .separator {
            display: none;
            margin: 10px 3px;
            font-size: 14px;

            &.show {
              display: inline-block;
            }
          }
        }
      }

      .q-toggle .q-option-label {
        font-size: 13px;
        color: rgba(0, 0, 0, 0.87);
      }
    }

    > .q-btn {
      margin-left: 15px;
      max-height: 42px;
      align-self: center;
    }
  }

  .main-table.sticky-table {
    padding-top: 15px;

    > .q-table-top {
      margin-top: -15px;
    }
  }
}

.fis_report_page {
  .page-header .title {
    margin-bottom: 15px;
  }

  .main-table {
    .text-negative {
      color: #be031a !important;
    }

    .text-positive {
      color: #00902E !important;
    }
  }
}

.cp_fis {
  .page-content > .col.q-field {
    margin-bottom: 15px;
  }

  &#jake_admin .q-layout-drawer-left .q-item.router-link-active .menu-icon,
  &#jake_admin .q-layout-drawer-left .q-item:active .menu-icon,
  &#jake_admin .q-layout-drawer-left .q-item:focus .menu-icon {
    filter: invert(0.9) sepia(1) saturate(5) hue-rotate(50deg);
    opacity: 0.8 !important;
  }
}
