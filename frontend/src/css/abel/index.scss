$text_color: #171726;
$blue_color: #0064ff;

body {
  font-family: "Poppins", "Roboto", "-apple-system", "Helvetica Neue", Helvetica,
    Arial, sans-serif;

  #q-app {
    //transition: filter 0.3s;
  }

  &.modal-opened {
    #q-app {
      filter: blur(4px);
    }
  }
}

#abel_admin {
  background-color: #f2f5fe;
  .q-layout-drawer {
    background-color: var(--span-color-primary);
  }
  .drawer-header,
  .mex-drawer-header {
    height: 110px;
    margin-top: -8px;
    // margin-bottom: 20px;
    // border-bottom: 1px solid #e2e2eb;

    > .col {
      height: 100%;

      > .logo:first-child {
        display: inline-block !important;
        height: auto !important;
        max-width: 75%;
        max-height: 60px;
      }

      .logo + img {
        position: absolute;
        z-index: 1;
      }
    }

    .mex-logo {
      max-width: 80%;
      max-height: 46px;
    }

    .portal {
      border-left: 1px solid #e2e2eb;
      margin: 13px 0;
      color: #92929e;
      height: auto;
      flex-direction: row !important;
      flex-wrap: nowrap !important;

      .active {
        color: var(--span-color-primary);
        margin-left: -10px;
        font-weight: 500;
      }
    }
  }
  .mex-drawer-header {
    .logo {
      display: none !important;
      height: 0 !important;
    }
  }

  .q-layout-drawer-left {
    box-shadow: none;
    border-right: 1px solid #e2e2eb;
    z-index: 3000;
    .employer-menu-list .q-item {
      &.router-link-active,
      &:active,
      &:focus {
        .menu-icon {
          filter: invert(0.5) sepia(1) saturate(5) hue-rotate(85deg);
        }
      }
      &:hover {
        background: #01c348;
        &.router-link-active,
        &:active,
        &:focus {
          background: white;
        }
      }
    }
    .america-voice-menu-list {
      .q-item {
        &.router-link-active,
        &:active,
        &:focus {
          .menu-icon {
            filter: none;
          }
          background: #f47735;
          a {
            color: #fff !important;
          }
        }
        &:hover {
          background: #f47735;
          &.router-link-active,
          &:active,
          &:focus {
            background: white;
            .menu-icon {
              filter: invert(0.5) sepia(1) saturate(5) hue-rotate(85deg);
            }
            a {
              color: var(--span-color-primary) !important;
            }
            .q-item-main,
            .q-item-side {
              color: var(--span-color-primary) !important;
            }
          }
        }
        .q-item-main,
        .q-item-side {
          color: #fff !important;
        }
      }
    }
    .q-item {
      padding: 15px 8px 15px 18px;
      margin: 0 5px;
      border-radius: 4px;
      .q-icon {
        color: #a0a0a0;
      }

      .q-item-main {
        font-weight: 500;
        color: white;

        + .q-item-section {
          margin-left: 4px;
        }
      }

      a, .q-item-main, .q-item-side > .q-icon {
        color: #ffffff !important;
        text-decoration: none;
      }
      &.router-link-active,
      &:active,
      &:focus {
        background: white;
        color: var(--span-color-primary) !important;

        a,
        .q-icon {
          color: var(--span-color-primary) !important;
        }

        .menu-icon {
          filter: invert(0.7) sepia(1) saturate(5) hue-rotate(155deg);
        }

        .q-item-main {
          color: var(--span-color-primary) !important;
        }

        // &:before {
        //   position: absolute;
        //   left: 0;
        //   content: '';
        //   width: 4px;
        //   height: 30px;
        //   border-top-right-radius: 4px;
        //   border-bottom-right-radius: 4px;
        //   background-color: var(--span-color-primary);
        // }
      }

      &:hover {
        background: #084363;
        &.router-link-active,
        &:active,
        &:focus {
          background: white;
        }
      }
    }
  }

  .q-layout-header {
    box-shadow: none;
    background-color: #f2f5fe;
    // border-bottom: 1px solid #e2e2eb;
    &.shadow {
      box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
    }

    .admin-top-toolbar {
      // height: 110px;
      background-color: #f2f5fe !important;
      padding-left: 20px;
      padding-top: 20px;
      padding-bottom: 10px;
      padding-right: 24px;
      > .q-if {
        box-shadow: none;
      }
      .avatar {
        height: 60px;
        width: 60px;
      }

      .group-name {
        margin: 0;
        font-size: 15px;
      }
      .notice-btn {
        margin-left: 0;
      }
      .global-quick-search-item {
        margin-left: auto !important;
      }
    }
    .employer-header-margin {
      padding-left: 30px;
    }
    .account-box {
      margin: 0 15px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      flex-shrink: 0;
      .q-btn-round {
        width: 60px;
        height: 60px;
      }
      .name-desc {
        line-height: 42px;
      }
      .role-desc {
        color: #5a5a89;
      }
    }
  }

  #global-quick-search {
    background-color: #fff;
    max-width: 500px;
    width: calc(100% - 10px);
    border: none;
    border-radius: 30px;
    padding: 12px !important;
  }

  .q-layout-page-container {
    background: #f2f5fe;
    padding-bottom: 0 !important;

    > .q-layout-page {
      min-height: calc(100vh - 110px) !important;
      padding: 20px 40px 0;
    }
  }

  .page-header {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    flex-wrap: wrap;

    > .title {
      font-size: 28px;
      font-weight: 500;
      color: #171726;
      // margin-top: 12px;
      margin-bottom: 5px;
    }

    > .fun-group {
      margin-left: auto;
      display: flex;
      flex-direction: row;

      > .q-if,
      > .q-field {
        margin-right: 20px;
        margin-top: 25px;

        &:last-of-type {
          margin-right: 0 !important;
        }
      }
    }
  }

  .page-content {
    margin: 20px 0;
  }

  > .q-layout-footer {
    left: auto;
  }

  .date-range-filter {
    width: auto;
  }

  .top-statics {
    height: 100%;

    .row {
      align-items: start;
      flex-wrap: nowrap;

      > .q-icon {
        margin-top: 2px;
        flex-shrink: 0;
      }
    }

    .column {
      margin-left: 12px;
    }

    .row > .q-icon {
      width: 25px;
      height: 25px;
      background-color: #eee;
      border-radius: 5px;
      font-size: 18px;

      &.text-positive {
        background-color: #e5fbf4;
      }

      &.text-negative {
        background-color: #feeeee;
      }

      &.text-warning {
        background-color: #fff8e5;
      }

      &.text-orange {
        background-color: #faf3e1;
      }

      &.text-purple {
        background-color: #c179b91d;
      }
    }

    .value {
      font-size: 20px;
      font-weight: 700;
    }

    .description {
      color: #666;
      margin-top: -3px;
      font-size: 12px;
    }

    &.columns {
      .values {
        margin-top: -3px;
      }

      .super {
        color: #888;
        font-size: 10px;
      }

      .description {
        margin-top: 5px;

        &:before {
          content: "";
          width: 100%;
          height: 1px;
          display: block;
          background: linear-gradient(to right, #ddd, white);
          margin-bottom: 5px;
        }
      }
    }

    > .q-card-main.q-card-container {
      font-size: 18px;
    }
  }
}
@media (max-width: 768px) {
  #abel_admin .q-layout-header .account-box .name-desc {
    font-size: 28px !important;
  }
  #abel_admin .q-layout-header .employer-header-margin {
    padding-left: 10px;
  }
}
@media (max-width: 575px) {
  #abel_admin .q-layout-page-container > .q-layout-page {
    padding: 0 20px;
  }
  #abel_admin .q-layout-header .account-box .name-desc {
    font-size: 22px !important;
  }
  #abel_admin .q-layout-header .admin-top-toolbar .avatar {
    width: 40px;
    height: 40px;
  }
  #abel_admin .q-layout-header .account-box {
    margin: 0;
  }
  .global-quick-search-item {
    display: none;
  }
}
#localize-widget {
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
}

.user-menu-popover {
  min-width: 200px;
}

a.q-link.q-item,
a.q-item-link.q-item {
  color: #171726 !important;
  text-decoration: none;
}

.q-field.dense {
  .q-field-content > .q-if {
    padding: 4px 8px !important;
  }
}

.q-if {
  border-radius: 8px;
  border: 1px solid #e7e7e7;
  background: white;
  padding: 8px 12px !important;
  align-items: center;
  box-shadow: 0 1px 0 rgba(20, 29, 38, 0.05);

  &.dense {
    padding: 4px 8px !important;

    .q-if-label-above {
      transform: translate(-5px, -135%) !important;
    }
  }

  &:before {
    border-bottom: none;
  }

  &:after {
    display: none;
  }

  &.q-if-focused {
    border-color: transparent;
    border-radius: 8px;
    box-shadow: 0 0 0 2px var(--span-color-primary) !important;

    &.q-if-error {
      border-color: transparent !important;
      box-shadow: 0 0 0 2px var(--q-color-negative) !important;
    }

    .q-if-control {
      color: var(--span-color-primary) !important;
    }
  }

  > .q-if-control {
    color: #bbb;
    align-self: center;
  }

  .q-if-label {
    font-size: 13px !important;
  }

  .q-if-label-above {
    color: #333;
    font-weight: normal;
    font-size: 12px !important;
    transform: translate(-12px, -155%) !important;
  }

  &:before,
  &:after {
    background: none;
  }

  &.q-if-error {
    border: 1px solid var(--q-color-negative) !important;
  }

  &.q-if-readonly {
    background-color: #f7f7f7;
    cursor: not-allowed;

    .q-input-target {
      cursor: not-allowed;
    }
  }
}

.date-range-filter {
  min-width: 131px;

  .q-if .q-if-label-above {
    transform: translate(-6px, -155%) !important;
  }
}

.common-fields-table {
  th, td {
    border-bottom: 1px solid #eee;
  }

  tr:last-child {
    th, td {
      border-bottom: none;
    }
  }

  th {
    color: #666;
    font-weight: normal;
    text-align: left;
    padding: 12px 0 !important;
    vertical-align: top;

    .q-icon {
      color: #333;
      font-size: 18px;
      margin-right: 6px;
    }
  }

  td {
    color: #333;
    text-align: right;
    font-weight: 500;
    word-break: break-all;
  }
}

.q-card {
  border-radius: 16px;
  box-shadow: 0 0 15px rgba(28, 20, 70, 0.1);
  background-color: white;

  > .q-card-primary.q-card-container {
    padding: 12px 16px 8px;
  }

  .q-card-title {
    font-size: 14px;
    font-weight: 500;

    .q-chip {
      margin-left: 6px;
      padding-left: 5px;
      padding-right: 5px;
    }

    > .font-12.normal.text-dark {
      line-height: 1.2rem;
      margin-top: 5px;
    }
  }

  &.q-card-lg {
    > .q-card-primary.q-card-container {
      padding: 18px 24px 10px;
    }

    > .q-card-main.q-card-container {
      padding: 10px 24px 24px;
    }
  }
}

.q-table-container {
  border-radius: 16px;
  box-shadow: 0 0 15px rgba(28, 20, 70, 0.1);
  background-color: white;

  &.inner-table {
    border-radius: 0;
    box-shadow: none;
  }

  .q-table-middle {
    margin: 0 15px;
  }

  .q-table > thead {
    tr {
      height: auto;
      background-color: #f8f8f8;
    }

    th {
      color: #6f6f6f;
      text-transform: uppercase;
      font-weight: 600;
    }

    tr:first-child th:first-child {
      border-top-left-radius: 10px;
    }
    tr:first-child th:last-child {
      border-top-right-radius: 10px;
    }
    tr:last-child th:first-child {
      border-bottom-left-radius: 10px;
    }
    tr:last-child th:last-child {
      border-bottom-right-radius: 10px;
    }
  }

  .q-table > tbody {
    tr:nth-of-type(even) {
      background-color: #fafafa;
    }
  }

  .q-table-top {
    .q-table-control {
      > *:first-child {
        margin-right: 15px;
      }

      .q-btn-inner > div {
        font-size: 14px !important;
      }
    }
  }

  .q-table-bottom {
    &.q-table-nodata {
      border-top: none;
      justify-content: center;

      .material-icons {
        color: #999;
      }
    }

    .q-if.q-select {
      padding: 4px 8px !important;
    }

    .q-table-bottom-item {
      margin-right: 12px;
    }

    .q-table-separator {
      flex-grow: 0;

      + .q-table-control {
        margin-right: auto;

        + .q-table-control {
          flex-grow: 1;

          > div {
            width: 100%;
          }

          div.q-mr-sm {
            margin-left: 10px;

            &:first-of-type {
              margin-right: auto;
            }
          }

          .btn-export {
            margin-left: 15px;
          }
        }
      }
    }
  }

  &.clean {
    box-shadow: none;

    .q-table-middle {
      margin: 0;
    }

    .q-table-bottom {
      .q-table-control:last-of-type {
        justify-content: flex-end;
      }
    }
  }
}

.q-chip {
  border-radius: 10px;

  &.positive {
    background-color: #ebfaf4;
    color: var(--q-color-positive);
  }

  &.negative {
    background-color: #feeeee;
    color: var(--q-color-negative);
  }

  &.orange {
    background-color: #faf3e1;
    color: #aaaa00 !important;
  }

  &.warning {
    background-color: #fef4ec;
    color: #ff9549 !important;
  }

  &.blue {
    background-color: #e5f7ff;
    color: #37bdff !important;
  }

  &.dark {
    background-color: #eee;
    color: #555 !important;
  }

  &.magenta {
    color: #f300f7;
    background-color: #f300f70a;
  }

  &.pansy {
    color: #861657;
    background-color: #8616570d;
  }

  &.purple {
    color: #c179b9;
    background-color: #c179b90d;
  }
}

.line {
  border-bottom: 1px solid #ddd;
}

td > .q-chip {
  font-weight: 500;
}

a.link {
  text-decoration: none;
  color: $blue_color !important;
}

.bg-blue {
  background-color: $blue_color !important;
}

.text-blue {
  color: $blue_color !important;
}

.text-gray {
  color: #888 !important;
}

.q-btn {
  box-shadow: none;

  &.disabled {
    opacity: 0.6 !important;
  }

  &:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  &.bg-dark:hover, &.bg-dark:focus {
    color: white !important;
  }

  &.btn-sm {
    min-height: auto;
    padding: 8px 12px !important;

    &.square {
      padding: 8px !important;
    }

    .q-btn-inner {
      font-size: 12px;

      .q-icon {
        margin-right: 0;

        + div {
          margin-left: 8px;
        }
      }
    }
  }

  &.btn-mini {
    min-height: auto;
    padding: 2px 4px !important;
    border-color: #ddd;
    border-radius: 7px !important;
    color: #666;
    height: 26.8px;

    .q-btn-inner {
      font-size: 12px;

      .q-icon {
        margin-right: 0;

        + div {
          margin-left: 8px;
        }
      }
    }
  }

  &.q-btn-rectangle {
    border-radius: 10px;
  }

  &.q-btn-dropdown {
    .q-btn-inner {
      flex-wrap: nowrap;
    }

    .q-icon.on-right {
      margin-left: 4px;
    }

    .q-icon.on-right + .q-icon.on-right {
      display: none;
    }
  }

  &.xl-icon {
    border: 1px solid #e2e2e2;
    border-radius: 14px;
    padding: 35px 15px;
    width: 100%;

    .q-btn-inner {
      font-weight: normal;
      flex-direction: column;

      > .q-icon {
        font-size: 45px;
        color: #92929d;
      }
    }
  }
}

.q-btn-group {
  box-shadow: none;

  .q-btn + .q-btn {
    margin-left: 0 !important;
    flex-grow: 0 !important;
  }
}

.modal-content {
  border-radius: 16px;

  .modal-header {
    text-align: center;
    font-size: 18px;
    font-weight: 500;

    .close {
      position: absolute;
      right: 10px;
      top: 10px;
    }
    .back {
      position: absolute;
      left: 10px;
      top: 10px;
    }
  }

  .modal-body {
    text-align: center;
    color: $text_color;

    &.modal-message {
      min-width: 400px;
    }
  }

  .modal-buttons {
    padding: 10px 24px 18px;
    justify-content: center;

    &.row {
      .q-btn.q-btn-flat.text-negative + .q-btn.bg-positive {
        flex-grow: 2;
      }
    }

    .q-btn {
      flex-grow: 1;
      flex-basis: 1%;

      &.main {
        flex-grow: 2;
      }
    }

    > .stacks {
      display: flex;
      flex-direction: column;
      width: 100%;

      > .row {
        justify-content: space-between;

        + * {
          margin-top: 10px;
        }
      }

      &.single {
        align-items: center;

        .q-btn {
          margin-left: 0;
          width: 100%;

          + .q-btn {
            margin-top: 8px;
          }
        }
      }
    }
  }
}

.html-list-dialog {
  .modal-buttons {
    > .flex-center {
      width: 100%;
      text-align: center !important;

      > .q-btn {
        width: 100%;
      }
    }
  }
}

.q-popover {
  border-radius: 8px;

  > .q-if.q-search:first-child {
    border-radius: 0;
  }
}

.gutter-form {
  margin-top: -28px;
  margin-left: -16px;

  > div {
    padding-left: 16px;
    padding-top: 28px;
  }
}

.q-option-inner + .q-option-label {
  margin-left: 3px !important;
  font-size: 13px;
  font-weight: 500;
  padding-top: 1px;
}

.q-alert {
  border-radius: 12px;
  overflow: hidden;
}

.q-chip.dot {
  width: 8px;
  height: 8px;
  background: red;
  top: 0;
  right: 0;
}

.radius-box {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 4px 8px;
  display: inline-block;
}

.upload-area {
  border: 2px dashed #e2e2e2;
  border-radius: 14px;
  padding: 25px 15px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  position: relative;

  &.dropping {
    border: 2px dashed #aaa;
    background-color: #e9fbe5;
  }

  &.selected {
    cursor: default;

    &:hover {
      background: transparent;
      border-color: #e2e2e2;
    }
  }

  > .q-icon {
    font-size: 45px;
    color: #92929d;
  }

  &:hover {
    background: #eee;
    border-color: #ccc;
  }

  a img {
    max-height: 300px;
    margin: -15px -5px;
  }

  .delete-btn {
    position: absolute;
    right: 8px;
    bottom: 8px;
    z-index: 1;
    text-decoration: none;
  }
}

.form-field-title {
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
  padding-top: 24px !important;
  margin-bottom: -5px;
  color: #555;
}

.sidebar-popover {
  background: white;
  z-index: 9999;

  &.wide {
    .q-list {
      max-width: 480px;

      .q-item {
        display: inline-flex;
        width: 240px;
      }
    }
  }

  .q-item {
    min-height: 43px;
    padding: 4px 16px;
    position: relative;

    .q-item-section + .q-item-section {
      margin-left: 6px;
    }

    .q-item-side-left {
      text-align: center;

      .q-icon {
        font-size: 12px;

        &.mdi-circle {
          display: none;
        }
      }
    }

    &:hover,
    &.active {
      background: var(--q-color-primary);

      a,
      .q-icon,
      .q-item-main a,
      a .q-icon,
      a:hover .q-icon,
      .open-in-new a .q-icon {
        color: white !important;
      }

      .q-item-side-left {
        .q-icon {
          &.mdi-circle {
            display: inline-flex;
          }

          &.mdi-circle-outline {
            display: none;
          }
        }
      }
    }

    .q-item-main {
      a {
        color: #333 !important;
        text-decoration: none;
        line-height: 35px;
        width: 100%;
        display: inline-block;
        white-space: nowrap;
      }
    }
  }
}

.quick-result {
  margin-top: 0 !important;
  margin-bottom: 15px;
  border-radius: 16px;
  box-shadow: 0 0 15px rgba(28, 20, 70, 0.1);
}

.import-info {
  text-align: right;
  margin: 15px !important;
  margin-bottom: -15px !important;
  a {
    color: var(--q-color-primary);
    cursor: pointer;
  }
  .failed {
    color: red;
  }
  .success {
    color: green;
  }
  span {
    margin: 0 5px;
  }
}
