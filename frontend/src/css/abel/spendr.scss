.cp_spendr {
  .click-text {
    color: var(--q-color-primary);
    cursor: pointer;
    text-decoration: underline;
  }
  .top-statics-style {
    .top-statics {
      .description {
        color: #8e8e93 !important;
        font-size: 14px !important;
      }
      .value {
        font-size: 28px !important;
      }
      .row > .q-icon {
        width: 44px !important;
        height: 44px !important;
        font-size: 25px !important;
        border-radius: 10px !important;
        color: #fff !important;
        &.text-positive {
          background-color: #34c759 !important;
        }

        &.text-negative {
          //background-color: #be031a !important;
          background-color: #ff6501!important;
        }

        &.text-purple {
          background-color: #5856d6 !important;
        }

        &.text-orange {
          background-color: #ff7a00 !important;
        }
        &.text-fuschia {
          background-color: #ef5da8 !important;
        }
      }
    }
    .main-table {
      strong {
        font-size: 24px;
      }
    }
  }
  .q-card {
    &.high-card {
      height: 473px !important;
    }
  }
}

hr.light {
  margin-top: 40px;
  margin-bottom: 40px;
  border: none;
  height: 1px;
  background-color: #ddd;
}
