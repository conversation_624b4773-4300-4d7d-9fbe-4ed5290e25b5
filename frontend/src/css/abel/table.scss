.q-table-container.sticky-table {
  .q-table-middle {
    overflow: auto;
  }

  thead {
    tr {
      border: none !important;
    }

    th {
      position: sticky;
      top: 0;
      background: #F5F5F5;
      z-index: 1;

      .col-option {
        position: absolute;
        right: 0;
        margin-top: -3px;
        padding: 4px;
        width: 24px;
        height: 24px;
        opacity: 0;
        transition: opacity .3s;
        font-size: 14px !important;

        .q-btn-inner {
          color: #666;
        }

        .q-btn-dropdown-arrow {
          display: none;
        }
      }

      &:hover{
        .col-option {
          opacity: 1;
        }
      }

      &.freeze {
        left: 0;
        z-index: 2;

        &.last-freeze {
          &:after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            background: #DCDCDC;
            width: 1px;
          }
        }
      }

      &.freeze-r {
        left: auto;
        right: 0;
        z-index: 2;

        &.last-freeze-r {
          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            background: #DCDCDC;
            width: 1px;
          }
        }
      }
    }

    tr.q-table-progress > td {
      position: sticky;
      top: 32px;
    }
  }

  tbody {
    tr {
      td.freeze {
        position: sticky;
        left: 0;
        background-color: white;
        z-index: 1;

        &.last-freeze {
          &:after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            background: #DCDCDC;
            width: 1px;
          }
        }
      }

      td.freeze-r {
        position: sticky;
        right: 0;
        background-color: white;
        z-index: 1;

        &.last-freeze-r {
          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            background: #DCDCDC;
            width: 1px;
          }
        }
      }

      &.even {
        td.freeze, td.freeze-r {
          background-color: #fafafa;
        }
      }
    }
  }

  tr.q-table-progress.animate-fade td {
    z-index: 3;
  }
}
