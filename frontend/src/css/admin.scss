@import "./variable";

#admin, .admin-root, #mobile-clf-layout {
  font-family: 'La<PERSON>', 'Microsoft YaHei', "PingFang SC", sans-serif !important;
  font-size: 12px;
  margin: 0;
  padding: 0;
  color: $text;
  background: $bgcolor !important;

  &.modal {
    background: rgba(0, 0, 0, 0.4) !important;
  }

  .q-layout-page-container {
    padding-bottom: 0 !important;
  }

  .q-layout-header {
    box-shadow: none;
    background: #F5F5F5 !important;

    &.shadow {
      box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2);
    }

    .q-toolbar {
      background: transparent !important;
      color: $text !important;
      padding-left: 10px;
      min-height: 62px;

      .avatar {
        box-shadow: 0 0 5px #aaa;
      }
    }
  }

  .q-layout-page {
    padding: 10px 20px 0;
    display: flex;
    flex-direction: column;

    .page-header {
      display: flex;
      flex-wrap: wrap;
      width: 100%;

      .title {
        font-size: 24px;
        color: $text-highlight;
      }

      .subtitle {
        font-size: 18px;
        line-height: 24px;
        color: #8C8C8C;
        margin-left: 16px;
        align-self: flex-end;

        &:before {
          content: ' - ';
          margin: 0 10px 0 2px;
        }
      }

      .fun-group {
        margin-left: auto;
        display: flex;
        flex-wrap: wrap;
        align-items: center;

        .q-field {
          min-width: 160px;
          margin-bottom: 0;
          margin-right: 10px;

          .q-field-label {
            margin-top: 0;
            margin-right: 8px;

            .q-field-label-inner {
              white-space: nowrap;
            }
          }

          &.auto-field {
            .q-field-label {
              width: auto;
            }
          }
        }

        .q-if-inner {
          overflow-x: hidden;
          text-overflow: ellipsis;
        }
      }

      .q-btn-round {
        color: $text-label;
        width: 40px;
        height: 40px;
      }
    }
  }

  .text-highlight {
    color: $text-highlight;
  }

  @import "./admin/table.scss";

  .on-right {
    margin-left: 4px;
  }

  .page-content {
    padding-bottom: 20px;

    > .form {
      .q-field {
        margin-bottom: 10px;
      }
    }
  }

  .group-header {
    font-size: 16px;
    font-weight: normal;
    margin: 30px 0 20px;
    padding-bottom: 10px;
    color: var(--q-color-primary);
    border-bottom: 1px solid #eee;
  }
}

#admin, .admin-root {
  .q-if {
    border-radius: 3px;
    border: 1px solid #e0e0e0;
    background: white;
    padding: 4px 8px !important;
    align-items: center;

    &:before {
      border-bottom: none;
    }

    &:after {
      display: none;
    }

    &.q-if-focused {
      border-color: transparent;
      border-radius: 1px;
      box-shadow: 0 0 0 2px var(--q-color-positive);

      &.q-if-error {
        box-shadow: 0 0 0 1px red;
      }

      .q-if-control {
        color: var(--q-color-positive) !important;
      }
    }

    > .q-if-control {
      align-self: center;
    }
  }

  .q-if:before,
  .q-if:after {
    background: none;
  }
  .q-if-error{
    border: 1px solid red !important;
    margin-bottom: 0 !important;
  }

  .q-search {
    border: 1px solid #e0e0e0;
    padding: 4px 12px;
    color: black;
    font-size: 12px;
    width: 400px;
    background: white !important;

    .q-if-control {
      font-size: 20px;
      line-height: 22px;
    }
  }

  .input-button-group {
    min-width: 300px;

    .row.no-wrap {
      position: relative;

      .q-if {
        flex-grow: 1;
        height: 38px;
        padding-right: 92px;
      }

      .q-btn {
        position: absolute;
        right: 1px;
        top: 1px;
      }
    }
  }

  span.search_match {
    background-color: yellow;
    border-radius: 3px;
  }

  .dr-picker {
    border: 1px solid #eee;
    padding: 0 12px;
    .q-icon {
      font-size:20px;
    }
  }

  .drawer-color {
    color: $drawer-label !important;
  }
  a:focus {
    text-decoration: none;
  }
  input:not(.no-style):hover {
    border-bottom: none;
  }
  .hover {
    padding-bottom: 10px;
    border-bottom: solid 2px var(--q-color-primary);
    font-weight: bold;
  }

  .q-btn.dark {
    .q-icon {
      color: #888;
    }
  }

  a.btn-wrapper {
    text-decoration: none;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    transition: background-color .2s;

    &:hover {
      background-color: rgba(255, 255, 255, .54);
    }
  }

  .q-btn {
    padding: 4px 16px;

    &.q-btn-dense {
      padding: 0.285em;
    }

    .q-btn-inner {
      .q-icon {
        margin-top: -1px;
      }
    }
  }

  .v-border-right-line {
    border-right: 1px solid $line;
  }

  .divider {
    border-right: 1px solid $line;
    margin: 3px 10px;
    display: inline;
  }

  .q-field-label {
    font-weight: bold;
    min-height: 30px;
    color: rgba(0, 0, 0, .87);

    .q-field-label-inner {
      min-height: 30px;
      color: rgba(0, 0, 0, .87);
      font-size: 13px;
      font-weight: normal;
    }
  }

  .q-select {
    background: white;
    border-radius: 3px;
    padding: 4px 8px;
    border: 1px solid #e0e0e0;
  }

  .q-icon.mdi.mdi-blank[class*="flaticon-"] {
    margin: 0 5px auto 3px;

    &:before {
      visibility: visible;
      margin-left: 0;
      color: #333;
      font-size: 18px;
    }
  }

  @import "./admin/parts";
}

.device-mode-web {
  a.q-item {
    color: #0c0c0c !important;
  }
}

.q-input-target test:after, .q-popover .q-item-label test:after {
  content: '<TEST>';
}

@media (max-width: 699px) {
  #admin, .admin-root, #mobile-clf-layout {
    .q-layout-page {
      .page-header {
        flex-direction: column;

        .fun-group {
          margin-top: 10px;
        }
      }
    }
  }
}

@media (max-width: 434px) {
  #admin, .admin-root, #mobile-clf-layout {
    .q-layout-page {
      .page-header {
        .fun-group {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-left: 0;
          width: 100% !important;

          > * {
            width: 100% !important;
            margin-bottom: 10px !important;
            margin-left: 0 !important;
            margin-right: 0 !important;

            &:last-child {
              margin-bottom: 0 !important;
            }
          }
        }
      }
    }
  }

  #global-quick-search {
    display: none;
  }
}

@import './admin/customize';
@import './admin/sidebar';
@import './admin/clf';
@import './admin/button';
@import './admin/dashboard';
@import './admin/dialog';
@import './admin/other';
