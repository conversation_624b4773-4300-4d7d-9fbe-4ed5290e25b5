#admin.cp_clf, .admin-root.clf-dialog {
  .q-layout-drawer-left .main-list .q-item-section + .q-item-section {
    margin-left: 10px;
  }

  .clf_page, .modal-body {
    .q-if {
      min-height: 44px;
    }

    .q-field {
      margin-bottom: 15px;

      .q-field-content {
        padding-top: 0;
      }

      .q-field-label {
        font-size: 14px;
        min-height: 30px;
        color: rgba(0, 0, 0, 0.87);
      }
    }

    .form-section {
      h4 {
        font-size: 20px;
        color: #333;
        font-weight: bold;
        margin: 0 auto 10px 0;
      }
    }
  }

  .q-layout-page {
    .page-header {
      .fun-group {
        .q-field-label-inner {
          height: 44px;
        }
      }
    }
  }

  input.q-input-target, .q-input-target {
    height: 24px;
    font-size: 14px;
  }

  .flaticon-dashboard:before {
    font-size: 18px;
  }

  .q-btn-rectangle {
    border-radius: 4px;
  }

  .page-header .title .q-btn {
    width: 28px;
    height: 28px;
    margin-top: -4px;
    padding: 4px;
    margin-left: 15px;
  }

  @import "../clf";

  .q-if-addon-left {
    margin-right: 3px;
  }

  .q-if-addon-right {
    margin-left: 3px;
  }

  .text-light {
    font-weight: 300;
  }

  .q-table {
    .q-table-sort-icon {
      vertical-align: top;
    }
  }
}

.modal.clf-dialog {
  .modal-content {
    border-radius: 12px;
  }

  .modal-header {
    position: relative;
    text-align: center;
    padding: 20px;
    font-size: 20px;
    font-weight: normal;
    color: #444;

    .btn-close {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 32px;
      height: 32px;
      padding: 0;

      &:hover {
        background: #f0a6ad !important;
      }
    }
  }

  .modal-body {
    color: #555;
    font-size: 13px;
  }

  .modal-buttons {
    justify-content: center;
    padding: 10px 8px 25px;

    .q-btn {
      min-width: 160px;
    }
  }

  &.clf-dialog-right-buttons {
    .modal-buttons {
      justify-content: flex-end !important;
      padding-left: 30px;
      padding-right: 30px;

      .q-btn {
        min-width: 100px !important;
      }
    }
  }
}

body.device-mode-web {
  .center-box-modal {
    .modal-content {
      background: rgba(0, 0, 0, 0.2);
    }

    .q-modal-layout-content {
      display: flex;
      justify-content: center;
      align-items: center;

      > .center-box {
        background: white;
        width: 100%;
        border-radius: 12px;
        position: relative;
        padding: 15px 10px;

        .btn-close {
          width: 34px;
          height: 34px;
          padding: 0;
          position: absolute;
          right: 8px;
          top: 8px;
        }
      }
    }
  }

  %q-svg-icon {
    background-repeat: no-repeat;
    background-position: center;
    background-size: 80%;

    &:before {
      content: '';
    }

    .q-icon:before {
      content: '';
    }
  }

  %q-svg-icon-size {
    @extend %q-svg-icon;

    position: absolute;
    width: 32px;
    height: 32px;
    top: 6px;
  }
}
