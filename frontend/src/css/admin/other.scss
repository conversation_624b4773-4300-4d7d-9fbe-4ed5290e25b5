@import "../variable";

.sidebar-popover {
  background: #ECECEC;
  z-index: 9999;

  &.wide {

    .q-list {
      max-width: 480px;

      .q-item {
        display: inline-flex;
        width: 240px;
      }
    }
  }

  .q-item {
    min-height: 43px;
    padding: 4px 16px;
    position: relative;

    .q-item-section + .q-item-section {
      margin-left: 6px;
    }

    .q-item-side-left {
      text-align: center;

      .q-icon {
        font-size: 12px;

        &.mdi-circle {
          display: none;
        }
      }
    }

    &:hover, &.active {
      background: var(--q-color-primary);

      a,
      .q-icon,
      .q-item-main a,
      a .q-icon,
      a:hover .q-icon,
      .open-in-new a .q-icon {
        color: white !important;
      }

      .q-item-side-left {
        .q-icon {
          &.mdi-circle {
            display: inline-flex;
          }

          &.mdi-circle-outline {
            display: none;
          }
        }
      }
    }

    .q-item-main {
      a {
        color: $drawer-label !important;
        text-decoration: none;
        line-height: 35px;
        width: 100%;
        display: inline-block;
        white-space: nowrap;
      }
    }

    .open-in-new {
      a {
        text-decoration: none;
        padding: 8px;
        position: absolute;
        top: 5px;
        right: 7px;
        border-radius: 50%;
        transition: background .2s;

        .q-icon {
          color: $drawer-label !important;
        }

        &:hover {
          background: #ddd;

          .q-icon {
            color: black !important;
          }
        }
      }
    }
  }
}

.filter-class {
  .modal-content {
    width: 621px;
    border-radius: 0;
  }
  .close {
    position: absolute;
    right: 20px;
    top: 10px;
    padding: 0;
  }
  .add-filter {
    color: var(--q-color-positive);
    padding-left: 0;

    .q-icon {
      color: var(--q-color-positive);
      margin-right: 5px;
    }
  }
  .modal-buttons:not(.modal-buttons-top) {
    border-top: none;
    padding-bottom: 20px;

    .q-btn {
      padding: 8px 10px;
      border-radius: 3px;
    }

    .clear-btn {
      color: $text;
    }

    .filter-btn {
      background: var(--q-color-positive);
      color: white;
      margin-right: 20px;
    }
  }
  .modal-buttons:not(.modal-buttons-top).column .q-btn + .q-btn{
    border-top: none;
  }
}
.modal-buttons.column .q-btn + .q-btn {
  margin-top: 0 !important;
}

.popover-class {
  .q-item {
    font-size: 14px;
  }

  .check-status {
    font-weight: 500;
    color: $text-highlight !important;
    .q-icon {
      color: $text-highlight !important;
    }
  }
}

.q-popover {
  .q-item {
    &.indent {
      .q-item-label {
        margin-left: 25px;
        margin-right: 10px;

        &:before {
          content: ' - ';
          margin-right: 10px;
        }
      }
    }
  }
}

// for drpicker
.ranges li {
  color: var(--q-color-primary);
  &:hover {
    background-color: var(--q-color-primary);
    border: 1px solid var(--q-color-primary);
  }
  &.active {
    background-color: var(--q-color-primary);
    border: 1px solid var(--q-color-primary);
  }
}
.daterangepicker {
  td {
    &.active {
      background-color: var(--q-color-primary);
      &:hover {
        background-color: var(--q-color-primary);
      }
    }
    &.in-range {
      background-color: #d7cddc;
    }
  }
  .input-mini.active {
    border: 1px solid var(--q-color-primary);
  }
  .range_inputs {
    .applyBtn {
      background-color: var(--q-color-primary);
      color: white;
    }
  }
}

body.desktop {}

body.with-layout-drawer-opened {
  .sidebar-popover {
    z-index: 3000;
  }
}

body .swal2-container {
  z-index: 10600;
}

.span-pagination {
  .btn-export {
    margin-left: 12px;
    min-height: 34px;
  }
}
