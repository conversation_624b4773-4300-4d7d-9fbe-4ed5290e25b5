@import "../variable";

.tr_inactive, .tr_inactive td {
  color: #aaa;
}

.form-page {
  .page-content {
    > .row {
      margin-top: -20px;
      margin-bottom: 15px;

      .col-6, .col-4 {
        padding-top: 15px;
      }

      .q-field.required {
        .q-field-label-inner {
          &:after {
            content: '*';
            color: red;
            margin-left: 3px;
          }
        }
      }
    }
  }
}

.form-table, .table-form {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 2px;
  }

  th {
    font-weight: bold;
    text-align: right;
    padding-right: 10px;
  }

  &.table-form-light, &.form-table-light {
    th {
      font-weight: normal;
    }

    td {
      font-weight: 300;
    }
  }

  &.form-table-lg, &.table-form-lg {
    th, td {
      padding: 6px;
      font-size: 14px;
    }

    th {
      color: #333;
    }
  }

  &.form-table-xlg, &.table-form-xlg {
    th, td {
      padding: 4px;
      font-size: 16px;
    }

    th {
      color: #333;
    }
  }

  &.form-table-left, &.table-form-left {
    th {
      text-align: left;
    }
  }
}

.line-table {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 4px;
    border-bottom: 1px solid #eee;
  }

  tr:last-of-type {
    th, td {
      border-bottom-color: transparent;
    }
  }
}
