#admin {
  .q-drawer-container {
    position: fixed;
    z-index: 3000;
  }

  .q-layout-drawer-left {
    width: 240px;
    box-shadow: none;
    background-color: #F3F3F3;

    .drawer-header {
      line-height: 22px;
      font-size: 21px;
      padding: 20px 18px;
      text-align: center;

      > img {
        max-height: 100px;
        max-width: 100%;
      }

      .column {
        margin-left: 10px;
      }
    }

    .main-list {
      border: none;

      > .q-item {
        border-bottom: none;
        font-weight: normal;
        padding-bottom: 8px;
      }

      .q-item {
        min-height: 55px;
        text-decoration: none;
        cursor: pointer;
        border-left: 4px solid transparent;
        padding-left: 12px;

        .q-item-main {
          a {
            text-decoration: none;
            color: $drawer-label !important;
            line-height: 1.7em;
            display: inline-block;
            width: 100%;
          }
        }

        &.menu-upper-line {
          border-top: 1px solid #ddd;
        }

        &:hover, &.hover {
          background: #ECECEC;
          border-left-color: var(--q-color-primary);

          .q-item-icon {
            color: var(--q-color-primary);
          }
        }

        &.router-link-active {
          background: var(--q-color-primary);

          .q-item-icon {
            color: white;
          }

          .q-item-label {
            color: white !important;
          }

          .q-item-main {
            color: white;

            a {
              color: white !important;
            }
          }

          .q-item-side-right {
            &.open-in-new {
              .q-icon {
                color: white !important;
              }
            }
          }
        }

        .q-item-side {
          min-width: 20px;

          .btn-wrapper {
            opacity: 0;
          }
        }

        .q-item-side-right {
          .q-item-icon {
            font-size: 18px;
          }

          &.open-in-new {
            position: absolute;
            right: 10px;

            .q-icon {
              color: $drawer-label !important;
            }
          }
        }

        &:hover {
          .q-item-side {
            .btn-wrapper {
              opacity: 1;
            }
          }
        }
      }

      .q-collapsible-sub-item {
        .q-item {
          min-height: 40px;
        }
      }

      .q-item-side-left {
        .q-item-icon {
          color: $drawer-icon;
          font-size: 20px;
          margin-top: 0;

          &:before {
            margin-left: 0;
          }
        }
      }

      .q-item-side {
        img {
          width: 18px;
          height: 18px;
          vertical-align: bottom;
        }
      }

      .q-item-label {
        font-size: 13px;
        color: $drawer-label !important;
      }

      .q-item-icon {
        color: $drawer-icon;
      }

      .q-layout-drawer-left {
        width: 240px;
      }

      .q-item-section + .q-item-section {
        margin-left: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .q-collapsible-sub-item {
        background: #f9f9f9;
        padding: 0;

        .q-list {
          border-left: none;
          border-right: none;
        }
      }
    }
  }

  &.drawer-opened {
    .q-layout-drawer-left {
      box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2);
    }
  }
}