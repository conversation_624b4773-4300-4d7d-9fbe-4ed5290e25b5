@import "../variable";

.q-table-container {
  box-shadow: none;

  .q-table-top {
    padding: 8px 0;

    .q-btn {
      color: $text-label;

      &.q-btn-round.q-btn-dense {
        width: 38px;
        height: 38px;
      }
    }
  }

  .q-table-middle {
    margin: 0 -20px;
    width: calc(100% + 40px);
    max-width: calc(100% + 40px);

    thead {
      tr:first-of-type {
        border-top: 1px solid $line-light;
        border-bottom: 1px solid $line-light;
      }

      tr:not(:nth-last-of-type(2)) {
        th {
          height: 57px;
        }
      }
    }

    th {
      font-weight: bold;
      font-size: 14px;
      color: $text-highlight;
    }

    tbody {
      tr {
        border-color: transparent;
        height: 45px;

        &:nth-of-type(odd) {
          background-color: white;

          &:hover {
            background-color: #f9f9f9;
          }
        }

        &.status_inactive,
        &.status_closed,
        &.status_banned {
          color: $line;
        }

        td {
          .q-btn {
            font-size: 12px !important;
            text-transform: none;
            padding: 4px 10px;

            .q-btn-inner {
              flex-wrap: nowrap;
            }
          }

          a {
            color: var(--q-color-primary);
            text-decoration: none;
          }
        }
      }
    }
  }

  .q-table-bottom {
    margin: 0 -20px;
    width: calc(100% + 40px);
    max-width: calc(100% + 40px);

    &.q-table-nodata {
      .q-icon {
        display: none;
      }
    }
  }

  &.no-pagination {
    .q-table-middle {
      border-bottom: 1px solid #DCDCDC;
    }

    .q-table-bottom {
      display: none;
    }
  }

  &.sticky-table {
    .q-table-middle {
      overflow: auto;
    }

    thead {
      tr {
        border: none !important;
        height: 57px;
      }

      th {
        position: sticky;
        top: 0;
        background: #F5F5F5;
        z-index: 1;

        &:before, &:after {
          content: '';
          position: absolute;
          left: 0;
          right: 0;
          height: 1px;
          background-color: #DCDCDC;
          z-index: -1;
        }

        &:before {
          top: 0;
        }

        &:after {
          bottom: 0;
        }

        &.freeze {
          left: 0;
          z-index: 2;

          &.last-freeze {
            &:before, &:after {
              height: auto;
            }

            &:before {
              top: 0;
              bottom: 0;
            }

            &:after {
              top: 1px;
              bottom: 1px;
              right: 1px;
              background: #F5F5F5;
            }
          }
        }

        .col-option {
          position: absolute;
          right: 0;
          margin-top: -3px;
          padding: 4px;
          width: 24px;
          height: 24px;
          opacity: 0;
          transition: opacity .3s;
          font-size: 14px !important;

          .q-btn-inner {
            color: #666;
          }

          .q-btn-dropdown-arrow {
            display: none;
          }
        }

        &:hover{
          .col-option {
            opacity: 1;
          }
        }
      }

      tr.q-table-progress > td {
        position: sticky;
        top: 55px;
      }
    }

    tbody {
      tr {
        td.freeze {
          position: sticky;
          left: 0;
          background-color: white;
          z-index: 1;

          &.last-freeze {
            &:after {
              content: '';
              position: absolute;
              right: 0;
              top: 0;
              bottom: 0;
              background: #DCDCDC;
              width: 1px;
            }
          }
        }

        &.even {
          td.freeze {
            background-color: #F5F5F5;
          }
        }
      }
    }
  }
}
