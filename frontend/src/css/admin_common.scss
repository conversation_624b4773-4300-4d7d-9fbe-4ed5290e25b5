.admin_layout {
  .q-layout-header {
    .q-toolbar {
      .avatar {
        width: 40px;
        height: 40px;
        background-size: cover;
        background-position: center center;
        background-repeat: no-repeat;
        border-radius: 50%;
      }
    }
  }

  .q-layout-page {
    padding: 10px 20px 0;
    display: flex;
    flex-direction: column;
  }

  > .q-layout-footer {
    box-shadow: none;

    .bottom-power {
      text-align: right;
      padding: 0 8px 8px 0;

      a {
        cursor: pointer;
        text-decoration: none;
        color: #666 !important;
      }

      img {
        height: 15px;
        margin-left: 2px;
        margin-bottom: -2px;
        // vertical-align: bottom;
      }
    }
  }

  .quick-result {
    display: flex;
    flex-direction: row;
    background: transparent;
    justify-content: left;
    margin-top: 15px;
    overflow-x: auto;

    > div {
      flex: 1;
      text-align: left;
      padding: 10px 10px 10px 25px;
      background: white;
      margin-right: 2px;
      min-width: 220px;

      &:first-of-type {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
      }

      &:last-of-type {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
        margin-right: 0;
      }

      > h5 {
        margin: 0;
        font-weight: bold;
        font-size: 20px;
        color: black;
      }

      > .description {
        font-size: 15px;
      }
    }
  }

  .auto-expand-row {
    flex-wrap: nowrap;

    > div {
      width: max-content;
      margin-right: 10px;
      margin-left: auto;

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.checkbox-expander {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.q-table-container {
  transition: 0.3s, background 0s;

  &.maximized {
    position: fixed;
    width: 100vw;
    height: 100vh;
    left: 0;
    top: 0;
    background: white;
    z-index: 6000;
    margin: 0 !important;

    > .q-table-middle {
      overflow: auto;
      max-height: calc(100% - 125px) !important;

      thead th {
        position: sticky;
        top: 0;
        background-color: #f8f8f8;

        &:after {
          content: ' ';
          position: absolute;
          display: block;
          width: 100%;
          border-bottom: 1px solid #ddd;
          bottom: 0;
          left: 0;
        }
      }
    }
  }

  > .q-table-handler {
    font-size: 22px;
    color: var(--span-color-primary);
    position: absolute;
    right: 0;
    top: 0;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s;

    &:hover {
      opacity: 1;
    }
  }
}

.admin_layout[id=admin] {
  .q-table-container.maximized {
    padding: 0 15px;

    .q-table-middle {
      background: #f6f6f6;
    }
  }
}

.common-profile-page {
  .q-card {
    &.high-card {
      height: 530px;
    }

    &.wide-card {
      min-height: 300px;

      > .q-card-main {
        position: relative;
        height: 100%;
      }
    }

    overflow: auto;

    > .q-card-primary {
      padding-top: 16px;
    }

    > .q-card-main {
      position: relative;
      height: calc(100% - 67px);
    }
  }

  .q-card-title .font-18.bold {
    height: 33px;
    padding-top: 3px;
  }

  .fields-table {
    width: 100%;

    th {
      padding: 5px 5px 5px 0;
      font-weight: normal;
      font-size: 13px;
      color: #888;
      text-align: left;
      min-width: 100px;
      vertical-align: top;
    }

    td {
      padding: 5px 0 5px 5px;
      text-align: right;
      word-break: break-word;
    }
  }

  .empty-tip {
    height: 100%;
    color: #666;

    .q-icon {
      margin-bottom: 5px;
      font-size: 20px;
    }
  }
}

code.outlined {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 3px 6px;
}

.tr_inactive, .tr_closed {
  color: #bbbbbb;
}

.tr_inactive td, .tr_closed td {
  color: #bbbbbb;
}

.common-profile-notes {
  > .q-card-main {
    overflow: auto;
  }

  .note-card {
    background-color: #feffc4;
    box-shadow: 0 0 8px rgba(28, 20, 70, 0.15);
    border-radius: 8px;
    margin: 5px 0 15px;
    padding: 15px;

    &:last-of-type {
      margin-bottom: 0;
    }

    .title {
      font-size: 14px;
      font-weight: 500;
    }

    .close {
      color: #888 !important;
      text-decoration: none;

      > .q-icon {
        font-size: 18px;
      }

      &:hover {
        color: var(--q-color-negative) !important;
      }
    }

    .body {
      margin: 10px 0 0;
      font-size: 12px;

      ul {
        margin: 0;
        padding-left: 20px;
      }
    }
  }
}
