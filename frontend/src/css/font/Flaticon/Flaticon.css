	/*
  	Flaticon icon font: Flaticon
  	Creation date: 26/08/2019 08:49
  	*/

@font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff2") format("woff2"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {
  font-family: Flaticon;
        font-size: 20px;
font-style: normal;
margin-left: 20px;
visibility: visible;
}

.flaticon-calendar:before { content: "\f100"; }
.flaticon-placeholder:before { content: "\f101"; }
.flaticon-health:before { content: "\f102"; }
.flaticon-pay:before { content: "\f103"; }
.flaticon-employees:before { content: "\f104"; }
.flaticon-bank:before { content: "\f105"; }
.flaticon-dashboard:before { content: "\f106"; }
.flaticon-layout:before { content: "\f107"; }
.flaticon-settings:before { content: "\f108"; }
.flaticon-bill:before { content: "\f109"; }
.flaticon-phone-symbol-of-an-auricular-inside-a-circle:before { content: "\f10a"; }
.flaticon-sports-car:before { content: "\f10b"; }
.flaticon-avatar:before { content: "\f10c"; }
.flaticon-envelope:before { content: "\f10d"; }
.flaticon-placeholder-1:before { content: "\f10e"; }
.flaticon-padlock:before { content: "\f10f"; }
.flaticon-telephone:before { content: "\f110"; }
.flaticon-target:before { content: "\f111"; }
.flaticon-skyline:before { content: "\f112"; }
.flaticon-bank-1:before { content: "\f113"; }
.flaticon-attention:before { content: "\f114"; }
.flaticon-statement:before { content: "\f115"; }
.flaticon-education:before { content: "\f116"; }
.flaticon-plus:before { content: "\f117"; }
.flaticon-pharmacy-1:before { content: "\f118"; }
.flaticon-pharmacy:before { content: "\f119"; }
.flaticon-growth:before { content: "\f11a"; }
.flaticon-task:before { content: "\f11b"; }