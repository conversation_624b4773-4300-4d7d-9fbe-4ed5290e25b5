.common-dashboard-card[class*="fis-dashboard-"] {
  .row.programs {
    width: 100%;
    height: 75px;
    flex-direction: column;
    flex-wrap: wrap;
    z-index: 2;

    .col-6 {
      padding: 3px 0 0 6px;
      transform: scale(0.9);
    }

    > div {
      &.active {
        font-weight: 900;

        .program-label {
          font-weight: 900;
        }
      }
    }

    .circle {
      width: 14px;
      height: 14px;
      border-radius: 50%;
      box-shadow: 0 2px 5px #999;
      display: inline-block;
      margin-right: 5px;
    }

    .program-label {
      display: inline-block;
      font-size: 12px;
      vertical-align: top;
      max-width: calc(100% - 19px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .day-selector {
    display: flex;
    padding: 0 0 5px;
    align-items: center;

    &.initialized {
      background-color: #f1f1ff;
    }

    .btn-arrow {
      padding: 4px !important;
      color: #c384fe;

      .q-btn-inner {
        font-size: 16px;
      }
    }

    .dates-container {
      position: relative;
      overflow: hidden;

      > .dates {
        display: flex;
        justify-content: space-evenly;

        > .q-btn {
          color: #c384fe;
          opacity: 0.7;

          .q-btn-inner {
            font-size: 12px;
            line-height: 1.2;
            text-transform: capitalize;
            transform: scale(0.9);
          }

          &.active {
            font-weight: 900;
            color: #9a47ff;
            opacity: 1;
          }
        }
      }
    }
  }

  &.fis-dashboard-data-zoom {
    .q-card-main {
      .header {
        padding: 0 28px 0 14px;

        .title {
          .value {
            font-size: 26px;
            color: #333;
          }

          .label {
            font-size: 12px;
            color: #999;
          }
        }

        .right {
          margin-left: auto;
          text-align: right;

          .label {
            font-size: 12px;
            color: silver;
          }

          .value {
            font-size: 16px;
            color: #555;
          }
        }
      }

      .sub-header {
        padding: 0 28px 0 14px;
        margin-top: -3px;
        min-height: 18px;

        .label {
          font-size: 12px;
          color: #999;
        }
      }
    }

    .day-selector {
      .btn-arrow {
        color: #666;
      }

      .dates-container {
        > .dates > .q-btn {
          color: #666;

          &.active {
            color: #333;
          }
        }
      }
    }

    .chart-container {
      margin-top: auto !important;
      height: calc(100% - 50px) !important;
    }
  }

  &.fis-dashboard-card-mixed {
    .q-card-main {
      padding: 0 10px 15px !important;

      .text-gray {
        font-size: 12px;
        color: #999 !important;
      }

      .row.legend {
        padding: 0 10px 0 38px;
        margin-bottom: 5px;

        .q-chip {
          color: white;
          font-size: 13px;
          min-height: 24px;
          border-radius: 5px;
          box-shadow: 0 2px 3px rgba(0, 0, 0, 0.4);
          padding: 4px 2px;
          width: 100%;
          text-align: center;
        }
      }

      .row.programs {
        max-width: 340px;
        margin: 7px auto 0 20px !important;
        height: 60px;

        .program-label {
          font-size: 13px;
          margin-top: -2px;
        }
      }

      .top-range-label {
        font-size: 12px;
        position: absolute;
        right: 10px;
        top: 105px;
        transform: scale(0.85);
        color: #999;

        &.top-range-label-left {
          left: 10px;
          right: auto !important;
          top: 99px !important;
        }
      }
    }
  }
}

.fis_report_page,
.fis__dashboard__index_page.graph__dashboard__index_page {
  .fun-group {
    .q-field {
      min-width: 140px !important;
      margin: 6px 20px 6px 0 !important;

      .q-field-label {
        flex-basis: 40px;

        .q-field-label-inner {
          min-height: 34px;
        }
      }

      .q-field-content {
        flex-grow: 1;
        flex-basis: 0;
      }

      &.currency-field {
        .q-field-label {
          flex-basis: 70px;
        }
      }
      &.subprogram-field {
        .q-field-label {
          flex-basis: 90px;
        }
      }
      &.group-type-field {
        .q-field-label {
          flex-basis: 50px;
        }
      }

      &.w70-field {
        .q-field-label {
          flex-basis: 70px;
        }
      }

      &.w55-field {
        .q-field-label {
          flex-basis: 55px;
        }
      }

      &.w100-field {
        .q-field-label {
          flex-basis: 100px;
        }
      }

      &.w120-field {
        .q-field-label {
          flex-basis: 120px;
        }
      }

      &.w130-field {
        .q-field-label {
          flex-basis: 130px;
        }
      }

      &.proxy-field {
        .q-field-label {
          flex-basis: 137px;
        }
      }

      &.range-dates-field {
        .q-field-label-inner {
          line-height: 32px;
        }

        .q-field-content {
          display: flex;

          .q-if + .q-if {
            margin-left: 6px;
          }

          .q-datetime-input {
            display: none;
            min-width: 122px;

            &.show {
              display: flex;
            }
          }

          .separator {
            display: none;
            margin: 10px 3px;
            font-size: 14px;

            &.show {
              display: inline-block;
            }
          }
        }
      }

      .q-toggle .q-option-label {
        font-size: 13px;
        color: rgba(0, 0, 0, 0.87);
      }
    }

    > .q-btn {
      margin-left: 15px;
      max-height: 42px;
      align-self: center;
    }
  }

  .main-table.sticky-table {
    padding-top: 15px;

    > .q-table-top {
      margin-top: -15px;
    }
  }
}

.fis_report_page {
  .page-header .title {
    margin-bottom: 15px;
  }

  .main-table {
    .text-negative {
      color: #be031a !important;
    }

    .text-positive {
      color: #00902e !important;
    }
  }
}

.cp_fis {
  .page-content > .col.q-field {
    margin-bottom: 15px;
  }

  &#jake_admin .q-layout-drawer-left .q-item.router-link-active .menu-icon,
  &#jake_admin .q-layout-drawer-left .q-item:active .menu-icon,
  &#jake_admin .q-layout-drawer-left .q-item:focus .menu-icon {
    filter: invert(0.9) sepia(1) saturate(200) hue-rotate(140deg) opacity(0.7);
    opacity: 0.8 !important;
  }
}
