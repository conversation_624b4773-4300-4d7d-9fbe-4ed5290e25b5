.file-section.both-side {
  .drop-file-area {
    .tip {
      font-size: 13px;
    }
  }

  + .file-section.both-side {
    padding-left: 15px;
  }
}

.file-section {
  .hide {
    display: none !important;
  }

  a {
    text-decoration: none;
  }

  .q-icon {
    font-size: 16px;
  }
}

.drop-file-area {
  border: 2px dashed #ddd;
  text-align: center;
  padding: 20px;
  margin: 15px auto;
  max-width: 470px;
  font-size: 16px;
  border-radius: 10px;
  cursor: pointer;
  height: 229px;
  overflow: hidden;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}

.drop-file-area:hover {
  background-color: rgba(0, 255, 0, 0.06);
}

.drop-file-area.dropping {
  border: 2px dashed var(--span-color-primary);
  background-color: rgba(0, 255, 0, 0.06);
}

.drop-file-area .q-icon {
  font-size: 60px;
  margin-bottom: 12px;
  color: #aaa;
}

.drop-file-area .tip {
  font-size: 15px;
  color: #909090;
}

.drop-file-area .tip strong {
  color: black;
  font-weight: bolder;
}

.selected-file-area {
  position: relative;
  border: 2px dashed #cccccc;
  text-align: center;
  padding: 0;
  margin: 15px auto !important;
  max-width: 470px;
  font-size: 16px;
  border-radius: 10px;
  height: 229px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  overflow: hidden;
}

.selected-file-area img.preview {
  max-width: 100%;
  max-height: 100%;
}

.selected-file-area .elements {
  position: absolute;
  bottom: 0;
  margin-bottom: 0;
  background: rgba(0,0,0,0.5);
  color: white;
  width: 100%;
  padding: 3px 11px;
  font-size: 14px;
  z-index: 1;
  display: flex;
  justify-content: space-between;
}

.selected-file-area .elements a {
  color: white !important;
}

.selected-file-area .elements a.red {
  color: var(--q-color-negative) !important;
}

.selected-file-area .elements a i.fa {
  line-height: 20px;
}

.selected-file-area.success {
  border-color: var(--q-color-positive);
  background-color: #f2fff2;
}

.selected-file-area.failed {
  border-color: var(--q-color-negative);
  background-color: #fff2f2;
}
