@import '../css/font/SourceSansPro.css';

body {
  &.ios {
    font-family: 'Source Sans Pro', '-apple-system', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  }

  .animated {
    animation-duration: .3s;

    &.slow {
      animation-duration: .6s;
    }
  }
}

.info-box {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  text-align: center;
  padding: 10px 0 5px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  &.text-positive {
    border-color: #21BA45;

    .info-box-title {
      color: #21BA45;
    }
  }

  .info-box-title {
    text-transform: uppercase;
    color: #aaa;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 5px;
  }

  .info-box-body {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: -5px;
    min-height: 40px;
    font-size: 16px;

    > .number-span {
      margin-left: -7px;
    }
  }
}

.method-amount {
  border: 1px solid #e0e0e0;
  margin-top: 8px;
  padding-left: 8px;
  align-items: center;
  position: relative;
  height: 36px;
}

.method-number {
  width: calc(100% - 68px);
}

.method-currency {
  display: flex;
  align-items: center;
  text-align: right;
  justify-content: center;
  background: #84888C;
  color: white;
  height: 36px;
  width: 66px;
  position: absolute;
  right: 0;

  .q-btn-dropdown {
    padding: 0 0 0 2px;

    .q-icon.on-right {
      margin-left: -4px;
      margin-right: -6px;
    }
  }

  .currency-flag {
    width: 21px;
    height: 14px;
  }

  > span {
    margin-left: 5px;
  }
}
