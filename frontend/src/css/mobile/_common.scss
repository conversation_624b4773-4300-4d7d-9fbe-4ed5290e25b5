body {
  font-family: 'Source Sans Pro', '-apple-system', 'Helvetica Neue', Helvetica, Arial, sans-serif;

  &.cordova.platform-ios {
    .q-layout-header > .q-toolbar:first-child,
    .q-layout-header > .q-tabs:first-child .q-tabs-head,
    .q-layout-drawer.top-padding {
      padding-top: 0;
      min-height: auto;
    }
  }

  .animated {
    animation-duration: .3s;

    &.slow {
      animation-duration: .6s;
    }
  }

  .q-layout-header {
    [class^="flaticon-"]:before, [class*=" flaticon-"]:before,
    [class^="flaticon-"]:after, [class*=" flaticon-"]:after {
      margin-left: 0 !important;
    }
  }

  .mobile-fixed-top-layout, .modal-page .q-modal-layout {
    .q-layout-header {
      .q-toolbar {
        padding-top: var(--safe-area-inset-top) !important;
        padding-bottom: 0;
        min-height: calc(50px + var(--safe-area-inset-top)) !important;
      }
    }

    .q-layout-page-container {
      padding-top: calc(51px + var(--safe-area-inset-top)) !important;

      > .q-layout-page {
        min-height: calc(100vh - 51px - var(--safe-area-inset-top)) !important;
      }
    }
  }

  .q-infinite-scroll {
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: none;

    .q-infinite-scroll-message {
      text-align: center;
      padding: 15px;
    }
  }

  .pull-to-refresh {
    width: 100%;
    max-height: calc(100vh - 51px - var(--safe-area-inset-top));
  }

  .q-toolbar-title {
    font-size: 20px;
    font-weight: 500;
    line-height: 1.19;
    letter-spacing: 0.02em;
  }

  .q-icon.q-icon-square[class*="flaticon-"] {
    width: 20px;
    height: 20px;
    color: #333;
    fill: #333;
    margin: 2px 5px 0 2px;

    &:before {
      margin-left: 0;
      font-size: 18px;
    }
  }

  .q-datetime-input {
    .q-icon.mdi.mdi-blank[class*="flaticon-"] {
      margin-left: 0;
    }
  }

  .q-icon.mdi[class*="flaticon-"] {
    color: #333;
  }

  .q-field-label {
    min-height: 28px;
  }
}

body.transparent {
  background: transparent !important;

  &.dev {
    background: red !important;
  }

  #q-app {
    display: none !important;
  }

  #vueg-background {
    background: transparent !important;
  }

  .q-layout {
    background: transparent;

    .q-layout-page-container {
      background: transparent;

      .q-layout-page {
        background: transparent;
      }
    }
  }

  .modal-transparent {
    background-color: transparent !important;

    .modal-backdrop {
      background: transparent;
    }

    .modal-content {
      background-color: transparent !important;

      .q-modal-layout {
        background-color: transparent !important;

        .q-layout-header {
          box-shadow: none !important;
          border-bottom: none;

          .q-toolbar {
            background-color: transparent !important;
          }
        }

        .q-modal-layout-content {
          background-color: transparent !important;
        }
      }
    }
  }
}

.modal-transparent {
  .modal-content {
    background-color: rgba(0, 0, 0, 0.3) !important;

    .q-layout-header {
      box-shadow: none !important;
    }
  }
}

%q-svg-icon {
  background-repeat: no-repeat;
  background-position: center;
  background-size: 80%;

  &:before {
    content: '';
  }

  .q-icon:before {
    content: '';
  }
}

%q-svg-icon-size {
  @extend %q-svg-icon;

  position: absolute;
  width: 32px;
  height: 32px;
  top: 6px;
}
