@import '_common.scss';

:root {
  --span-color-primary: #388E3C !important;
  --q-color-primary: #388E3C !important;
  --q-color-positive: #388E3C !important;
  --q-color-negative: #D0031B !important;
}

body {
  @import "../clf";

  #mobile-clf-layout {
    .q-layout-header {
      .q-toolbar {
        justify-content: space-between;
        background-color: #388E3C !important;
        color: white !important;

        &.bg-red-8 {
          background-color: #388E3C !important;
          padding-bottom: 0;
        }
      }
    }

    .q-layout-page {
      background: white;

      .page-header {
        .title {
          font-size: 20px;
          text-align: center;
          margin: 5px auto 10px;
        }
      }
    }
  }

  .q-loading-bar {
    background-color: rgba(255, 255, 255, 0.5) !important;
  }

  .mobile_clf_login_page {
    background: #F4F4F4;
    padding: 6vw;
    padding-top: calc(6vw + var(--safe-area-inset-top));

    .form {
      background: white;
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 5vw;
      position: relative;

      > .logo {
        height: 85px;
        margin: 10px 30px;
      }

      > h2, > h3 {
        color: #4D7F16;
        font-size: 30px;
        margin: 0 auto 15px;
      }

      > h3 {
        font-size: 24px;
      }

      > .tip {
        text-align: center;
        line-height: 1.5em;
        padding: 0 20px;
        margin-bottom: 25px;
      }

      > .q-field {
        margin-bottom: 20px;
        width: 100%;

        .link {
          color: #0E49A2 !important;
          text-decoration: none;
        }
      }

      > .q-btn {
        margin-top: 15px;
        width: 100%;
        border-radius: 8px;
        font-size: 18px;
        background: #1C262A !important;

        + .q-btn {
          margin-top: 10px;
        }

        &.q-btn-flat {
          background: transparent !important;
        }

        &.btn-back {
          background: transparent !important;
          width: auto;
          margin-top: 0;
          position: absolute;
          left: 0;
          top: 0;
          border-radius: 50%;
        }
      }
    }
  }

  .modal-page {
    .q-modal-layout-content {
      .center-box {
        background: white;
        width: 100%;
        border-radius: 12px;
        position: relative;
        padding: 15px 10px;

        .btn-close {
          width: 34px;
          height: 34px;
          padding: 0;
          position: absolute;
          right: 8px;
          top: 8px;
        }
      }
    }

    &.center-box-modal-page {
      .q-modal-layout-content {
        background: #E5E5E5;
        padding: 18px;
        padding-bottom: calc(18px + var(--safe-area-inset-bottom));
        display: flex;
        align-items: center;
        justify-content: center;

        .center-box {
          padding: 0 18px 15px;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          overflow: auto;
        }
      }
    }
  }

  #mobile-clf-layout .q-layout-page.center-box-page {
    background: #E5E5E5;
    padding: 18px;
    padding-bottom: calc(18px + var(--safe-area-inset-bottom));
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .center-box {
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      overflow: auto;
      background: white;
      width: 100%;
      flex: 1;
      border-radius: 12px;
      position: relative;
      padding: 10px;

      .btn-close {
        width: 34px;
        height: 34px;
        padding: 0;
        position: absolute;
        right: 8px;
        top: 8px;
      }
    }
  }

  .center-box-modal-page,
  #mobile-clf-layout .q-layout-page .center-box-page {
    .center-box {
      position: relative;

      .center-box-inner {
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: space-evenly;
        min-height: 400px;
        margin-bottom: 20px;
        position: absolute;
        top: 0;
        left: 15px;
        right: 15px;
        bottom: 0;

        &:after {
          content: '';
          clear: both;
        }

        > div {
          width: 100%;
        }
      }
    }
  }

  .modal-transparent {
    .q-modal-layout-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
    }
  }

  .modal-page {
    .q-toolbar {
      .mdi-chevron-left {
        font-size: 26px;
      }

      .q-toolbar-title {
        text-align: center;
      }
    }
  }

  #mobile-clf-layout {
    .page-header {
      .title {
        display: none;
      }

      .search_field {
        .q-field-label {
          flex-basis: 85px;
        }

        .q-field-content {
          flex: 1;
        }
      }
    }
  }

  .form-table, .table-form {
    td, th {
      vertical-align: top;
    }
  }

  .table-form-column {
    td, th {
      display: block;
      text-align: left;
    }

    th {
      padding-bottom: 0 !important;
    }

    td {
      padding-top: 4px;
      margin-bottom: 8px;
    }
  }

  .q-btn.btn-md {
    border-radius: 8px;
    font-size: 16px;
  }

  .q-btn.btn-green-light, .q-btn.btn-red-light, .q-btn.btn-gray-light {
    border-radius: 8px;
    font-size: 16px;
    min-height: 40px;

    .q-btn-inner {
      line-height: 1em;
    }
  }

  .btn-load-unload {
    padding: 0;

    .q-icon {
      font-size: 27px;
      border-radius: 50%;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
      transition: box-shadow 0.3s;

      &:before {
        transform: scale(1.3);
      }

      &:active {
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.6);
      }
    }
  }

  .q-radio {
    .q-option-inner {
      &:after {
        content: '';
      }
    }
  }

  .mobile_clf_wallet_index_page,
  .mobile_clf_funds_index_page {
    padding: 0 !important;

    .balance-row {
      padding: 15px 0;

      .col-3 {
        display: flex;
        align-items: center;

        &.text-right {
          justify-content: flex-end;
        }
      }

      .balance {
        font-size: 36px;
        text-align: center;
        white-space: nowrap;

        sup {
          font-size: 18px;
          margin-right: 3px;
        }
      }
    }
  }

  .clf_mobile_list_page_index,
  .mobile_clf_funds_list_card,
  .mobile_clf_wallet_index_page .transaction-card {
    box-shadow: none;
    border-radius: 0;
    margin-top: 15px;

    .q-card-container {
      padding: 0;

      .q-card-title {
        text-transform: uppercase;
        text-align: center;
        background: #E3E3E3;
        font-size: 15px;
        letter-spacing: 3px;
      }
    }

    .q-list {
      padding: 0;
    }

    .q-item {
      padding-right: 8px;
    }

    .q-item + .q-item {
      border-top-color: #f0f0f0;
    }

    .q-item-side-left {
      .q-icon {
        font-size: 30px;
        position: static;
        background-size: cover;
        width: 38px;
        height: 38px;
      }
    }

    .q-item-side-right {
      color: #ccc;
      margin-left: 6px;
    }

    .q-item-main {
      display: flex;
      justify-content: space-between;

      .item-left, .item-right {
        .title {
          font-size: 16px;
          font-weight: 600;
        }

        .desc {
          font-size: 12px;
          font-weight: 300;
        }
      }

      .item-right {
        text-align: right;

        .title {
          font-weight: normal;
        }

        .amount {
          font-size: 17px;
          font-weight: 600;
        }

        .balance {
          font-size: 13px;
          color: #333;
        }

        .status {
          font-size: smaller;
          text-transform: uppercase;
          color: red;
        }
      }
    }
  }

  .clf_mobile_list_page {
    padding: 0 !important;
    overflow: hidden;
    position: relative;
    margin-top: 0 !important;

    .page-header {
      background: white;
      z-index: 1;
      box-shadow: 0 0 15px #aaa;
      padding: 10px 15px 15px;
      position: absolute;
      top: -180px;
      transition: top 0.3s;

      &.visible {
        top: 0;
      }
    }
  }

  #clf__settings__index_page {
    height: calc(100vh - 51px - var(--safe-area-inset-top)) !important;
    padding: 0 !important;

    .page-content {
      overflow: auto;
      padding: 0 20px 40px;
    }
  }
}

@media (max-width: 320px) {
  .mobile_clf_wallet_index_page,
  .mobile_clf_funds_index_page {
    .row.balance-row {
      > .col-3 {
        width: 20%;
      }
    }
  }
}

@import 'clf_android';
