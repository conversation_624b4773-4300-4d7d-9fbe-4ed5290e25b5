.q-btn-inner {
  line-height: 1.45;
}

.q-item-side {
  min-width: 26px;

  .currency-flag {
    float: right;
  }
}

.q-item-disabled {
  pointer-events: none;
  opacity: 0.4;
}

.q-btn {
  padding: 10px 16px;

  &.q-btn-dense {
    padding: 6px 12px;
  }

  &.rounded-box {
    border-radius: 35%;
    box-shadow: none;
    font-size: 17px;
    padding: 0;
    width: 2.2em;
    height: 2.2em;

    .q-focus-helper {
      background: currentColor;
    }
  }
}

.q-btn-toggle-sm {
  .q-btn.q-btn-dense {
    font-size: 12px;
    padding: 3px 6px;

    .q-btn-inner {
      line-height: 1;
    }
  }
}

.q-btn-round.q-btn-flat + .q-btn.q-btn-dense.q-btn-dropdown-arrow {
  padding: 6px 2px;
}

.q-card {
  &.shadow-lg {
    box-shadow: 0 0 20px 5px rgba(0, 0, 0, 0.18);
  }
}

.q-card-subtitle, .q-card-title-extra {
  font-size: 13px;
}

.animated {
  animation-duration: 0.3s;
}

.q-subtitle {
  font-size: 18px;
  font-weight: 500;
}

.modal-scroll {
  max-height: 75vh;
}

.modal-buttons ul.notes {
  margin-top: 10px;
  font-size: 13px;
  color: var(--q-color-faded);

  i {
    font-style: normal;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 0 5px;
  }
}

.modal.minimized .modal-content {
  max-width: 95vw;
  max-height: 95vh;
}

.q-table-top {
  .q-table-control {
    flex-grow: 10;
  }

  .q-table-separator {
    flex-grow: 0;
    min-width: 8px !important;

    + .q-table-control {
      margin-left: auto;
      flex-grow: 0;
    }
  }
}

.q-table-middle {
  overflow-y: hidden;

  tr.q-table-progress > td {
    position: relative;

    .q-progress {
      bottom: auto;
      top: 0;
      height: 1px !important;
    }
  }
}

.q-list {
  .q-item.q-item-split {
    padding: 0 16px 0 0;

    &:hover {
      background: transparent !important;
    }

    > .q-item-main {
      padding: 8px 6px 8px 16px;

      &:hover {
        background: rgba(189,189,189,0.5);
      }
    }

    > .q-item-side-right {
      margin-left: 4px;
    }

    > .q-item-side:hover {
      color: var(--span-color-primary);
    }
  }

  .q-item.select-separator {
    min-height: 1px;
    pointer-events: none;
    padding-top: 0;
    padding-bottom: 0;
    background: #e6e6e6 !important;
    margin: 10px 0;
  }
}

.q-if-inner > .row:first-of-type {
  max-width: 100%;
}

.q-field-helper {
  text-align: left;
}

.q-field.q-field-label-right .q-field-label-inner {
  margin-right: 8px;
  float: right;
}

.q-tooltip {
  max-width: none !important;

  code {
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 0 3px;
  }
}

.q-field-no-input .q-field-bottom {
  margin-top: 3px;
  border-top-color: transparent;
}

@media (min-width: 576px) {
  .row > .col-sm-2-5 {
    height: auto;
    width: 20%;
  }

  .row > .col-sm-1-5 {
    height: auto;
    width: 15%;
  }
}
