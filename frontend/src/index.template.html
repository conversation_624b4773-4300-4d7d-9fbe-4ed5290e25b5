<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="description" content="<%= htmlWebpackPlugin.options.productDescription %>">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width<% if (htmlWebpackPlugin.options.ctx.mode.cordova) { %>, viewport-fit=cover<% } %>">
    <title>Loading...</title>

    <link rel="icon" href="statics/icons/icon-256x256.png" type="image/x-icon">
    <link rel="icon" type="image/png" sizes="32x32" href="statics/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="statics/icons/favicon-16x16.png">

    <script>window.originalXhrSend = window.XMLHttpRequest.prototype.send</script>
    <script type='text/javascript' src='https://cdn.yodlee.com/fastlink/v3/initialize.js'></script>

    <link rel="stylesheet" href="/files/customize-__SUB_DOMAIN__.css?v=__CUSTOMIZE_VER__">
    <script src="/files/customize-__SUB_DOMAIN__.js?v=__CUSTOMIZE_VER__"></script>
    <style>
      body {
        --safe-area-inset-top: 0px;
        --safe-area-inset-top: constant(safe-area-inset-top);
        --safe-area-inset-top: env(safe-area-inset-top);
        --safe-area-inset-bottom: 0px;
        --safe-area-inset-bottom: constant(safe-area-inset-bottom);
        --safe-area-inset-bottom: env(safe-area-inset-bottom);
        --safe-area-inset-left: 0px;
        --safe-area-inset-left: constant(safe-area-inset-left);
        --safe-area-inset-left: env(safe-area-inset-left);
        --safe-area-inset-right: 0px;
        --safe-area-inset-right: constant(safe-area-inset-right);
        --safe-area-inset-right: env(safe-area-inset-right);
      }

      body.cordova.platform-mat {
        --safe-area-inset-top: 0px;
        --safe-area-inset-bottom: 0px;
        --safe-area-inset-left: 0px;
        --safe-area-inset-right: 0px;
      }

      .browserupgrade {
        display: none;
        position: fixed;
        margin: 8px;
        padding: 8px;
        background: #FFEEEE;
        border: 1px solid #FFAAAA;
      }

      @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
        .browserupgrade {
          display: block !important;
        }
      }
    </style>
    <!--[if IE]>
    <style>
      .browserupgrade {
        display: block !important;
      }
    </style>
    <![endif]-->
  </head>
  <body>
    <p class="browserupgrade">Your current browser is unsupported. Please use a <a href="https://browsehappy.com/">supported browser</a>.</p>

    <!-- DO NOT touch the following DIV -->
    <div id="q-app"></div>
  </body>
</html>

<%= htmlWebpackPlugin.options.env_suffix %>
