<template>
  <q-layout id="abel_admin"
            class="admin_layout bg-white"
            view="lHh Lpr lFf">
    <q-page-container class="bg-white">
      <transition enter-active-class="animated fadeIn"
                  leave-active-class="animated fadeOut"
                  mode="out-in">
        <router-view />
      </transition>
    </q-page-container>
  </q-layout>
</template>

<script>
import 'fontawesome'

export default {
  name: 'JakeAdminEmptyLayout',
  mounted () {
    if (this.$store.state.User.cpKey === 'cp_spendr' && window.adminLayoutMountedCb) {
      window.adminLayoutMountedCb()
    }
  }
}
</script>

<style lang="scss">
@import url("https://fonts.googleapis.com/css?family=Poppins:100,100i,300,300i,400,500,500i,600,700&display=swap");
@import "../css/admin_common";
@import "../css/admin/dashboard";
@import "../css/abel/fis";
@import "../css/abel/index";
@import "../css/abel/upload";
@import "../css/abel/signaturePad";
@import "../css/abel/table";
@import "../css/abel/faas";
</style>
