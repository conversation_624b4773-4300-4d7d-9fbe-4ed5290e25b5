<template>
  <q-layout>

    <q-layout-header reveal>
      <q-toolbar color="red-8" :style="toolbarStyle" class="shadow-2">
        <q-btn
          flat dense
          icon="mdi-arrow-left"
          @click="back"
          label="Back"
        />
      </q-toolbar>
    </q-layout-header>

    <q-page-container>
      <router-view />
    </q-page-container>

  </q-layout>
</template>

<script>
import LayoutMixin from '../mixins/LayoutMixin'
import { callNative } from '../common'
import { NATIVE_CALL } from '../const'

export default {
  name: 'DefaultLayout',
  mixins: [
    LayoutMixin
  ],
  methods: {
    back () {
      callNative(NATIVE_CALL.CLOSE_LOAD_CARD_PAGE)
    }
  }
}
</script>

<style lang="scss">
  @import '../css/mobile';
</style>
