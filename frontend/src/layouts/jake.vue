<template>
  <q-layout id="jake_admin"
            class="admin_layout"
            view="lHh Lpr lFf"
            :class="layoutClass">
    <q-layout-drawer side="left"
                     v-model="left"
                     :width="240">
      <q-scroll-area class="fit">
        <q-list class="main-list"
                no-border>
          <div class="drawer-header row">
            <div class="col flex flex-center">
              <img class="logo"
                   src="../statics/logo.png"
                   alt="">
            </div>
            <div class="col portal flex flex-center">
              <span class="active">admin</span>portal
            </div>
          </div>

          <template v-if="!hideMenu">
            <q-select v-if="cpKey === 'cp_fis'"
                      class="mh-16 mb-15"
                      :options="fisPlatforms"
                      v-model="fisPlatform"></q-select>

            <template v-for="m in menus">
              <q-item v-if="m.children.length <= 0"
                      :key="m.name"
                      v-ripple
                      :class="m.cls"
                      @click.native="ensureNav(m.route, m.mdRoute)"
                      :to="convertRoute(m.route, m.mdRoute)">
                <q-item-side v-if="m.svgIcon === false">
                  <q-icon :name="m.mdIcon"
                          class="q-item-icon"
                          v-if="m.mdIcon.startsWith('mdi-')"></q-icon>
                  <i class="q-item-icon"
                     :class="m.mdIcon"
                     v-else-if="m.mdIcon.startsWith('flaticon-')"></i>
                </q-item-side>
                <q-item-side v-else
                             class="flex flex-center">
                  <img class="menu-icon"
                       :src="`/static/jake/icons/${m.id}.svg`"
                       alt="">
                </q-item-side>
                <q-item-main>
                  <a :href="'/admin#' + convertRoute(m.route, m.mdRoute)">{{ m.name }}</a>
                </q-item-main>
                <q-item-side right
                             class="open-in-new"
                             v-if="m.route && m.route !== '/noop' && m.mdRoute">
                  <a :href="'/admin#' + convertRoute(m.route)"
                     target="_blank"
                     class="btn-wrapper">
                    <q-icon name="mdi-open-in-new"
                            color="white"></q-icon>
                  </a>
                </q-item-side>
              </q-item>
              <q-item v-else
                      v-ripple
                      :class="{
                      'router-link-active': collapsible[m.id],
                      'hover': hovers[m.id]
                    }"
                      @mouseenter.native="showSubNav(m)"
                      :key="m.name">
                <q-item-side v-if="m.id == 'cow_members' || m.id == 'cow_settings' || m.id == 'cow_reports' || m.id == 'developer'"
                             class="flex flex-center">
                  <img class="menu-icon"
                       :src="`/static/jake/icons/${m.id}.svg`"
                       alt="">
                </q-item-side>
                <q-item-side v-else-if="m.svgIcon"
                             class="flex flex-center">
                  <img class="menu-icon"
                       :src="`/static/jake/icons/${m.svgIcon}.svg`"
                       alt="">
                </q-item-side>
                <q-item-side v-else>
                  <q-icon :name="m.mdIcon"
                          class="q-item-icon"
                          v-if="m.mdIcon.startsWith('mdi-')"></q-icon>
                  <i class="q-item-icon"
                     :class="m.mdIcon"
                     v-else-if="m.mdIcon.startsWith('flaticon-')"></i>
                </q-item-side>
                <q-item-main notranslate="">{{ m.name }}</q-item-main>
                <q-item-side icon="mdi-chevron-right"></q-item-side>

                <q-popover anchor="top right"
                           self="top left"
                           :ref="`subNav_${m.id}`"
                           :offset="[0, 8]"
                           @hide="subNavHide(m)"
                           class="sidebar-popover"
                           :class="{wide: m.children.length > 4 && m.direction !== 'column'}">
                  <q-list>
                    <q-item v-for="c in m.children"
                            :key="c.name"
                            v-ripple
                            @click.native="ensureNav(c.route, c.mdRoute)"
                            :class="{active: isSameRoute($route.fullPath, convertRoute(c.route, c.mdRoute))}"
                            :to="convertRoute(c.route, c.mdRoute)">
                      <q-item-side>
                        <q-icon name="mdi-circle-outline"></q-icon>
                        <q-icon name="mdi-circle"></q-icon>
                      </q-item-side>
                      <q-item-main>
                        <a :href="'/admin#' + convertRoute(c.route, c.mdRoute)">{{ c.name }}</a>
                      </q-item-main>
                      <q-item-side right
                                   class="open-in-new"
                                   v-if="c.route && m.route !== '/noop' && c.mdRoute && isSuperAdmin(user)">
                        <a :href="'/admin#' + convertRoute(c.route)"
                           onclick="event.stopPropagation();"
                           target="_blank"
                           class="btn-wrapper">
                          <q-icon name="mdi-open-in-new"
                                  color="white"></q-icon>
                        </a>
                      </q-item-side>
                    </q-item>
                  </q-list>
                </q-popover>
              </q-item>
            </template>
          </template>
        </q-list>
      </q-scroll-area>
    </q-layout-drawer>
    <q-layout-header v-if="user && user.id">
      <q-toolbar class="admin-top-toolbar"
                 color="white"
                 text-color="dark">
        <q-btn flat
               round
               icon="mdi-menu"
               color="primary"
               class="btn-top font-16 ml-5"
               @click="toggleDrawer"></q-btn>

        <q-btn flat
               round
               color="primary"
               class="btn-top font-16 ml-5"
               @click="$router.go(-1)"
               icon="mdi-chevron-left"></q-btn>

        <q-search v-if="cpKey !== 'cp_fis'"
                  icon="search"
                  v-model="keyword"
                  type="text"
                  class="m-10"
                  id="global-quick-search"
                  @keydown.13="search"
                  @clear="search"
                  clearable
                  hide-underline
                  placeholder="Search...">
        </q-search>
        <q-btn v-if="cpKey !== 'cp_fis'"
               flat
               round
               icon="mdi-bell-outline"
               color="grey-7"
               class="ml-auto font-16">
          <q-popover>
            <q-list separator
                    link>
              <q-item v-close-overlay>No notifications</q-item>
            </q-list>
          </q-popover>
        </q-btn>

        <div class="account-box"
             :style="cpKey === 'cp_fis' ? 'margin-left:auto' : ''">
          <q-btn flat
                 round
                 class="p-0">
            <div class="avatar"
                 :style="{backgroundImage: `url(${user.avatar || '/static/img/avatar2.png'})`}"></div>
          </q-btn>
          <div class="column ml-7 mr-10">
            <div class="text-weight-bold"
                 notranslate="">
              {{ user.fullName }}
            </div>
            <div class="text-grey-7 font-12">{{ user.currentRole }}</div>
          </div>
          <i class="q-icon mdi mdi-menu-down font-24 text-grey-7"></i>

          <q-popover>
            <q-list separator
                    link
                    class="user-menu-popover">
              <q-list-header>{{ user.email }}</q-list-header>
              <q-item v-if="user.impersonating"
                      @click.native="c.redirectRoot('/login_as/exit?url=/admin')">
                <q-item-side icon="mdi-arrow-left"></q-item-side>
                <q-item-main>Login back</q-item-main>
              </q-item>
              <q-item v-close-overlay
                      v-if="user.switchedCp"
                      @click.native="c.redirectRoot('/admin/card-program/switch/back')">
                <q-item-side icon="mdi-sitemap"></q-item-side>
                <q-item-main>Switch to main system</q-item-main>
              </q-item>
              <q-item v-close-overlay
                      :to="'/j/i/admin__user_modify__profile'">
                <q-item-side icon="mdi-account"></q-item-side>
                <q-item-main>Profile</q-item-main>
              </q-item>
              <q-item v-close-overlay
                      @click.native="logout">
                <q-item-side icon="mdi-logout-variant"></q-item-side>
                <q-item-main>Logout</q-item-main>
              </q-item>
            </q-list>
          </q-popover>
        </div>
      </q-toolbar>
    </q-layout-header>
    <q-page-container v-if="user && user.id">
      <transition enter-active-class="animated fadeIn"
                  leave-active-class="animated fadeOut"
                  mode="out-in">
        <router-view />
      </transition>

      <SessionDetectDialog></SessionDetectDialog>
    </q-page-container>
    <q-layout-footer>
      <div class="bottom-power">
        <a href="https://www.ternitup.com"
           target="_blank">
          Powered by
          <img src="/static/img/tern_logo_new.svg"
               alt="Tern - FinTech Banking and Payment Leaders" />
        </a>
      </div>
    </q-layout-footer>
  </q-layout>
</template>

<script>
import 'fontawesome'
import AdminLayoutMixin from '../mixins/AdminLayoutMixin'
import eventBus from '../eventBus'
import { request, notifySuccess } from '../common'

export default {
  name: 'JakeAdminLayout',
  mixins: [
    AdminLayoutMixin
  ],
  data () {
    return {
      root: '/j',
      keyword: ''
    }
  },
  computed: {
    fisPlatforms () {
      const all = []
      if (!this.user || !this.user.fisPlatforms) {
        return all
      }
      const names = this.user.fisAllPlatforms || {}
      for (const key of this.user.fisPlatforms) {
        all.push({
          label: key.toUpperCase() === 'DPP' ? 'Cashback' : key.toUpperCase(),
          sublabel: names[key],
          value: key
        })
      }
      return all
    },
    fisPlatform: {
      get () {
        return this.user.fisPlatform
      },
      set (v) {
        this.$store.commit('User/update', {
          fisPlatform: v
        })
      }
    },
    hideMenu () {
      if (this.$route.path === '/j/fis/choose') {
        return true
      }
      return this.cpKey === 'cp_fis' && !this.fisPlatform
    }
  },
  watch: {
    fisPlatform (n, o) {
      if (o && n !== o) {
        this.updateFisPlatform()
      }
    }
  },
  methods: {
    search () {
      this.$nextTick(() => {
        setTimeout(() => {
          if (this.keyword) {
            const route = this.$route.path
            if (this.user && this.user.cpKey === 'cp_cow') {
              if (route === '/j/cow/members') {
                this.$root.$emit('reload-cow-members-search', this.keyword)
              } else {
                localStorage.setItem('quickSearch', this.keyword)
                this.$router.push(`/j/cow/members`)
              // setTimeout(() => {
              //   this.$root.$emit('reload-cow-members-search', this.keyword)
              // }, 1500)
              }
            } else if (this.user && this.user.cpKey === 'cp_mex') {
              this.searchMex()
            }
          }
        }, 300)
      })
    },
    async searchMex () {
      const route = this.$route.path
      this.$q.loading.show()
      const resp = await request(`/admin/mex/members/list/1/10?keyword=${this.keyword}&period=all&sortType=asc`, 'get')
      this.$q.loading.hide()
      if (resp.success) {
        if (resp.data.count > 1 || route === '/j/mex/members') {
          if (route === '/j/mex/members') {
            this.$root.$emit('reload-mex-members-search', this.keyword)
          } else {
            localStorage.setItem('mexQuickSearch', this.keyword)
            this.$router.push(`/j/mex/members`)
          }
        } else if (resp.data.count === 1) {
          window.open(`/admin#/j/mex/members/${resp.data.data[0]['Member ID']}`, '_blank')
        } else {
          notifySuccess('No data found', 'Message', 'negative')
        }
      }
    },
    logout () {
      window.location.href = '/logout'
    },
    initFisPlatform () {
      if (this.user && this.user.id) {
        if (this.user.fisCurrentPlatform) {
          this.fisPlatform = this.user.fisCurrentPlatform
        } else if (this.user.fisDefaultPlatform) {
          this.fisPlatform = this.user.fisDefaultPlatform
        } else if (this.fisPlatforms.length) {
          this.fisPlatform = this.fisPlatforms[0].value
        }
        // else if (this.fisPlatforms.length > 1) {
        //   this.$router.replace('/j/fis/choose')
        // } else if (this.fisPlatforms.length === 1) {
        //   this.fisPlatform = this.fisPlatforms[0].value
        // } else {
        //   this.$q.dialog({
        //     title: 'Error',
        //     message: 'Access denied to any platforms. Please contact admin.',
        //     color: 'negative'
        //   }).then(async () => {
        //     this.logout()
        //   }).catch(() => {
        //     this.logout()
        //   })
        // }
      }
    },
    async updateFisPlatform () {
      this.$q.loading.show()
      const resp = await request(`/admin/fis/update-platform/${this.fisPlatform}`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        this.$root.$emit('reload-fis-dash-filters')
        this.$root.$emit('reload-fis-list-filters')
        const route = this.$route.path
        console.log(route)
        const hideList = [
          '/j/fis/report/monetary',
          '/j/fis/report/auth',
          '/j/fis/report/spend',
          '/j/fis/report/dispute',
          '/j/fis/report/non-monetary',
          '/j/fis/report/negative'
        ]
        if (hideList.indexOf(route) >= 0) {
          this.$root.$emit('reload-list-filters')
        } else {
          this.$root.$emit('reload-page-data')
        }
      }
    }
  },
  mounted () {
    this.initFisPlatform()
    eventBus.$on('loaded-user-profile', this.initFisPlatform)
  },
  beforeDestroy () {
    eventBus.$off('loaded-user-profile', this.initFisPlatform)
  }
}
</script>

<style lang="scss">
@import url("https://fonts.googleapis.com/css?family=Poppins:300,300i,400,500,500i,600,700&display=swap");
@import "../css/admin_common";
@import "../css/admin/dashboard";
@import "../css/jake/fis";
@import "../css/jake/index";
@import "../css/jake/upload";
@import "../css/jake/signaturePad";
@import "../css/jake/table";
</style>
