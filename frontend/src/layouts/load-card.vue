<template>
  <q-layout id="load-card-layout">

    <q-layout-header reveal>
      <q-toolbar color="red-8" :style="toolbarStyle" class="shadow-2">
        <q-btn
          flat round dense
          icon="mdi-arrow-left"
          @click="back"
        />
        {{ title }}
      </q-toolbar>
    </q-layout-header>

    <q-page-container class="relative-position">
      <transition
        :enter-active-class="'animated ' + enterClass"
        :leave-active-class="'animated ' + leaveClass">
        <router-view />
      </transition>
    </q-page-container>

  </q-layout>
</template>

<script>
import { callNative } from '../common'
import { NATIVE_CALL } from '../const'
import LayoutMixin from '../mixins/LayoutMixin'

export default {
  name: 'LoadCardLayout',
  mixins: [
    LayoutMixin
  ],
  data () {
    return {
      enterClass: 'fadeInRight',
      leaveClass: 'fadeOutLeft'
    }
  },
  computed: {
    title () {
      return this.$route.meta.title || 'Load Card'
    }
  },
  methods: {
    back () {
      const step = this.$route.meta.step
      if (step <= 1) {
        callNative(NATIVE_CALL.CLOSE_LOAD_CARD_PAGE)
        return
      }
      this.enterClass = 'fadeInLeft'
      this.leaveClass = 'fadeOutRight'
      this.$router.go(step === 4 ? -2 : -1)
      setTimeout(() => {
        this.enterClass = 'fadeInRight'
        this.leaveClass = 'fadeOutLeft'
      }, 300)
    }
  }
}
</script>

<style lang="scss">
  @import '../css/mobile';

  #load-card-layout {
    .q-layout-header {
      .q-toolbar.bg-red-8 {
        background-color: rgb(225, 46, 47) !important;
      }
    }

    .q-layout-page-container {
      > .q-layout-page {
        position: absolute;
        width: 100%;
        top: 51px;
      }
    }
  }
</style>
