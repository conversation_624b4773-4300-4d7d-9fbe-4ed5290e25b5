<template>
  <q-layout view="lHr LpR lFr"
            id="mobile-clf-layout"
            class="mobile-fixed-top-layout">

    <q-layout-header>
      <q-toolbar color="red-8" :style="toolbarStyle" class="shadow-2">
        <q-btn v-if="['/clf/dispensary/dashboard/transactions', '/clf/merchant/dashboard/fees'].includes($route.path)"
               flat round
               class="btn-back"
               @click="$router.back()"></q-btn>
        <q-btn flat round
               v-else
               class="btn-menu"
               @click="leftDrawer = true"></q-btn>
        <template v-if="['/clf/merchant/employees', '/clf/merchant/invoice'].includes($route.path) && showLeftFakeButton">
          <q-btn flat round disable></q-btn>
        </template>

        <div class="spacer"></div>
        <div class='q-title' :style="titleStyle">{{ viewTitle }}</div>
        <div class="spacer"></div>

        <template v-if="$route.path === '/clf/merchant/employees'">
          <q-btn flat round
                 @click="$root.$emit('show-dispensary-employee-detail-dialog')"
                 icon="mdi-blank flaticon-plus"></q-btn>
          <q-btn flat round
                 @click="$root.$emit('show-mobile-clf-merchant-employees-filter')"
                 icon="mdi-blank flaticon-education"></q-btn>
        </template>
        <template v-else-if="$route.path === '/clf/merchant/invoice'">
          <q-btn flat round
                 @click="$root.$emit('show-dispensary-bill-invoice-dialog', {})"
                 icon="mdi-blank flaticon-plus"></q-btn>
          <q-btn flat round
                 @click="$root.$emit('show-mobile-clf-merchant-invoices-filter')"
                 icon="mdi-blank flaticon-education"></q-btn>
        </template>
        <template v-else-if="filterPage[$route.path]">
          <q-btn flat round
                 @click="$root.$emit(filterPage[$route.path])"
                 icon="mdi-blank flaticon-education"></q-btn>
        </template>
        <q-btn v-else flat round disable></q-btn>
      </q-toolbar>
    </q-layout-header>

    <q-layout-drawer side="left" v-model="leftDrawer" v-if="user">
      <div class="meta-box-container">
        <div class="meta-box">
          <q-btn flat round
                 color="white"
                 class="btn-close-drawer"
                 @click="leftDrawer = false"
                 icon="mdi-close"></q-btn>
          <div class="avatar-container" @click="menuGo({route: '/clf/settings'})">
            <div class="avatar" v-if="user.avatar"
                 :style="{backgroundImage: `url(${user.avatar})`}"></div>
            <div class="avatar avatar-default" v-else></div>
          </div>
          <div class="username" @click="menuGo({route: '/clf/settings'})">{{ user.fullName }}</div>
          <div class="number" v-if="user.account">
            <template v-if="type === 'Patient'">
              Patient: <strong>{{ user.account.number }}</strong>
            </template>
            <template v-else-if="type === 'Dispensary'">
              Dispensary: <strong>{{ user.organization.name }}</strong>
            </template>
            <template v-else-if="type === 'Vendor'">
              Vendor: <strong>{{ user.organization.name }}</strong>
            </template>
          </div>
          <div class="balance">{{ user.account.balance | moneyFormat }}</div>
        </div>
      </div>
      <q-list no-border link class="menu-list">
        <q-item v-for="(m, i) in menus"
                :key="i"
                @click.native="menuGo(m)"
                :class="{active: menuActive(m)}">
          <q-item-side :icon="m.icon"
                       :class="{reverse: m.route === '/clf/merchant/invoice'}"></q-item-side>
          <q-item-main :label="m.name"></q-item-main>
        </q-item>
      </q-list>
      <q-list no-border link class="fixed-list">
        <q-item @click.native="logoutConfirmDialog = true">
          <q-item-side icon="mdi-logout"></q-item-side>
          <q-item-main label="Log Out"></q-item-main>
        </q-item>
      </q-list>
    </q-layout-drawer>

    <q-page-container class="relative-position">
      <router-view ref="routerView"></router-view>
    </q-page-container>

    <q-dialog
      v-model="logoutConfirmDialog"
      @ok="onLogoutOk"
    >
      <span slot="title">Confirm</span>
      <span slot="message">Are you sure that you want to log out?</span>

      <template slot="buttons" slot-scope="props">
        <q-btn flat color="negative"
               label="No"
               @click="$c.click(props.cancel)"
               @touchend.native="props.cancel"/>
        <q-btn flat color="negative"
               label="Yes"
               @click="$c.click(props.ok)"
               @touchend.native="props.ok" />
      </template>
    </q-dialog>

    <BankSelectModal></BankSelectModal>
    <BankAddModal></BankAddModal>
    <BankConnectModal></BankConnectModal>
    <BankVerifyModal></BankVerifyModal>

    <WalletLoadModal></WalletLoadModal>
    <WalletDetailModal></WalletDetailModal>
  </q-layout>
</template>

<script>
import LayoutMixin from '../../../mixins/LayoutMixin'
import BankSelectModal from '../../../pages/mobile/clf/banks/select'
import BankAddModal from '../../../pages/mobile/clf/banks/add'
import BankConnectModal from '../../../pages/mobile/clf/banks/connect'
import BankVerifyModal from '../../../pages/mobile/clf/banks/verify'
import WalletLoadModal from '../../../pages/mobile/clf/wallet/load'
import WalletDetailModal from '../../../pages/mobile/clf/wallet/detail'
import { clf } from '../../../common'

export default {
  name: 'MobileClfLayout',
  mixins: [
    LayoutMixin
  ],
  components: {
    BankSelectModal,
    BankAddModal,
    BankConnectModal,
    BankVerifyModal,
    WalletLoadModal,
    WalletDetailModal
  },
  data () {
    return {
      leftDrawer: false,
      logoutConfirmDialog: false,
      filterPage: {
        '/clf/dispensary/patients': 'show-mobile-clf-dispensary-patients-filter',
        '/clf/merchant/due': 'show-mobile-clf-merchant-dues-filter',
        '/clf/merchant/dashboard/transactions': 'show-mobile-clf-merchant-transaction-report-filter',
        '/clf/merchant/dashboard/fees': 'show-mobile-clf-merchant-fee-report-filter'
      }
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    },
    type () {
      if (this.user && this.user.account) {
        return this.user.account.type
      }
      return null
    },
    menus () {
      // ---------------------------- Patient ----------------------------
      if (this.type === 'Patient') {
        return [
          {
            icon: 'mdi-wallet-outline',
            name: 'My Wallet',
            route: '/clf/wallet'
          },
          {
            icon: 'mdi-map-marker-outline',
            name: 'Store Locator',
            route: '/clf/dispensary-locator'
          },
          {
            icon: 'mdi-tumblr-reblog',
            name: 'Funds Transfers',
            route: '/clf/funds'
          },
          {
            icon: 'mdi-bank',
            name: 'Banking Setup',
            route: '/clf/banks'
          },
          {
            icon: 'mdi-settings-outline',
            name: 'My Settings',
            route: '/clf/settings'
          }
        ]
      }

      // ---------------------------- Dispensary ----------------------------
      if (this.type === 'Dispensary') {
        return [
          {
            icon: 'mdi-dashboard',
            name: 'Dashboard',
            route: '/clf/dispensary/dashboard'
          },
          {
            icon: 'mdi-wallet-outline',
            name: 'My Wallet',
            route: '/clf/wallet'
          },
          {
            icon: 'mdi-account-multiple-outline',
            name: 'Patients',
            route: '/clf/dispensary/patients'
          },
          {
            icon: 'mdi-account-group-outline',
            name: 'Employees',
            route: '/clf/merchant/employees'
          },
          {
            icon: 'mdi-cash',
            name: 'Incoming Bills',
            route: '/clf/merchant/due'
          },
          {
            icon: 'mdi-cash',
            name: 'Outgoing Invoices',
            route: '/clf/merchant/invoice'
          },
          {
            icon: 'mdi-tumblr-reblog',
            name: 'Funds Transfers',
            route: '/clf/funds'
          },
          {
            icon: 'mdi-bank',
            name: 'Banking Setup',
            route: '/clf/banks'
          },
          {
            icon: 'mdi-settings-outline',
            name: 'Dispensary Settings',
            route: '/clf/admin/settings'
          }
        ]
      }

      // ---------------------------- Vendor ----------------------------
      if (this.type === 'Vendor') {
        return [
          {
            icon: 'mdi-dashboard',
            name: 'Dashboard',
            route: '/clf/vendor/dashboard'
          },
          {
            icon: 'mdi-wallet-outline',
            name: 'My Wallet',
            route: '/clf/wallet'
          },
          {
            icon: 'mdi-cash',
            name: 'Incoming Bills',
            route: '/clf/merchant/due'
          },
          {
            icon: 'mdi-cash',
            name: 'Outgoing Invoices',
            route: '/clf/merchant/invoice'
          },
          {
            icon: 'mdi-tumblr-reblog',
            name: 'Funds Transfers',
            route: '/clf/funds'
          },
          {
            icon: 'mdi-bank',
            name: 'Banking Setup',
            route: '/clf/banks'
          },
          {
            icon: 'mdi-settings-outline',
            name: 'Vendor Settings',
            route: '/clf/admin/settings'
          }
        ]
      }
      return []
    },
    viewTitle () {
      const path = this.$route.path
      for (let m of this.menus) {
        if (m.route === path) {
          return m.name
        }
      }
      if (this.$route.meta.name) {
        return this.$route.meta.name
      }
      return ''
    },
    showLeftFakeButton () {
      return !(this.$route.path === '/clf/merchant/invoice' && window.document.body.clientWidth <= 360)
    },
    titleStyle () {
      const s = {}
      if (this.$route.path === '/clf/merchant/invoice' && window.document.body.clientWidth <= 360) {
        s.fontSize = '19px'
      }
      return s
    }
  },
  methods: {
    menuActive (menu) {
      return this.$route.path.startsWith(menu.route)
    },
    menuGo (menu) {
      this.leftDrawer = false
      this.$router.push(menu.route)
    },
    onLogoutOk () {
      this.$store.dispatch('Bank/clear')
      this.$store.dispatch('DashboardFee/clear')
      this.$store.dispatch('DashboardTransaction/clear')
      this.$store.dispatch('Due/clear')
      this.$store.dispatch('Employee/clear')
      this.$store.dispatch('Fund/clear')
      this.$store.dispatch('Invoice/clear')
      this.$store.dispatch('Patient/clear')
      this.$store.dispatch('Transaction/clear')
      this.$store.dispatch('User/clear')

      this.$router.replace('/clf/login')
    }
  },
  mounted () {
    clf.updateStatusBar()

    if (this.$c.isDebugging() && !window.cordova) {
      const body = document.querySelector('body')
      body.classList.add('dev')
    }
  }
}
</script>

<style lang="scss">
  @import '../../../css/font/SourceSansPro.css';
  @import "../../../css/font/Flaticon/Flaticon.css";
  @import '../../../css/mobile/clf';
  @import '../../../css/admin';

  #mobile-clf-layout {
    .q-layout-header {
      .q-toolbar {
        .btn-menu, .btn-back {
          width: 50px;
          height: 50px;
          margin-left: -10px;

          @extend %q-svg-icon;
          background-size: 36px 36px;
        }

        .btn-menu {
          background-image: url('../../../statics/mobile/clf/top-menu-hamburger.svg');
        }

        .btn-back {
          background-image: url('../../../statics/mobile/clf/top-menu-go-back.svg');
        }

        .flaticon-education, .flaticon-plus {
          margin: 0;

          &:before {
            transform: scale(1.2);
            -webkit-text-fill-color: white;
          }
        }
      }
    }

    .q-layout-drawer-left {
      width: 280px !important;
      transform: translateX(-280px) !important;

      &.q-layout-drawer-delimiter {
        transform: translateX(0px) !important;
      }

      .meta-box-container {
        height: calc(200px + var(--safe-area-inset-top));
        background-image: url('../../../statics/mobile/clf/menu-bg-image.png');
        background-size: 103% 103%;
        background-position: -3px -3px;
        position: relative;

        .meta-box {
          position: relative;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          padding: 10px 20px;
          padding-top: calc(10px + var(--safe-area-inset-top));

          .btn-close-drawer {
            position: absolute;
            right: 0;
            top: var(--safe-area-inset-top);
            font-size: 18px;

            @extend %q-svg-icon;
            background-image: url('../../../statics/mobile/clf/menu-close.svg');
            background-size: 50%;
            opacity: 0.7;
          }

          .avatar-container {
            margin: 15px auto 8px 0;
            width: 70px;
            height: 70px;
            background-color: white;
            border-radius: 50%;
            padding: 1px;

            .avatar {
              width: 100%;
              height: 100%;
              background-size: cover;
              background-position: 50%;
              background-repeat: no-repeat;
              border-radius: 50%;

              &.avatar-default {
                background-image: url('../../../statics/mobile/avatar.png');
              }
            }
          }

          .username {
            color: white;
            font-size: 24px;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }

          .number {
            color: #81C784;
            font-size: 12px;
            margin-top: 3px;

            strong {
              letter-spacing: 1px;
            }
          }

          .balance {
            margin-top: 7px;
            font-size: 18px;
            color: #F8E71C;
            letter-spacing: 2px;
          }
        }
      }

      .menu-list {
        padding: 0 0 10px 0;
        height: calc(100vh - 57px - 200px - var(--safe-area-inset-top) - var(--safe-area-inset-bottom));
        overflow: auto;

        .q-item-side.reverse {
          .q-icon {
            transform: rotateY(180deg);
          }
        }

        .mdi-dashboard {
          @extend %q-svg-icon-size;
          background-size: 72%;
          background-image: url('../../../statics/mobile/clf/admin/005-dashboard.svg');
        }

        .mdi-wallet-outline {
          @extend %q-svg-icon-size;
          background-image: url('../../../statics/mobile/clf/menu-wallet_2.svg');
        }

        .mdi-account-multiple-outline {
          @extend %q-svg-icon-size;
          background-image: url('../../../statics/mobile/clf/admin/001-health.svg');
        }

        .mdi-account-group-outline {
          @extend %q-svg-icon-size;
          background-image: url('../../../statics/mobile/clf/admin/003-employees.svg');
        }

        .mdi-cash {
          @extend %q-svg-icon-size;
          background-image: url('../../../statics/mobile/clf/admin/002-pay.svg');
        }

        .mdi-map-marker-outline {
          @extend %q-svg-icon-size;
          background-image: url('../../../statics/mobile/clf/menu-locator_2.svg');
        }

        .mdi-tumblr-reblog {
          @extend %q-svg-icon-size;
          background-image: url('../../../statics/mobile/clf/menu-transfer_2.svg');
        }

        .mdi-bank {
          @extend %q-svg-icon-size;
          background-image: url('../../../statics/mobile/clf/menu-bank_2.svg');
        }

        .mdi-settings-outline {
          @extend %q-svg-icon-size;
          background-image: url('../../../statics/mobile/clf/menu-settings_2.svg');
        }

        .q-item.active {
          background-color: #388E3C;
          color: white;

          .q-icon {
            color: white;
          }

          .mdi-dashboard {
            background-image: url('../../../statics/mobile/clf/admin/005-dashboard_white.svg');
          }

          .mdi-wallet-outline {
            background-image: url('../../../statics/mobile/clf/menu-wallet.svg');
          }

          .mdi-account-multiple-outline {
            @extend %q-svg-icon-size;
            background-image: url('../../../statics/mobile/clf/admin/001-health_white.svg');
          }

          .mdi-account-group-outline {
            @extend %q-svg-icon-size;
            background-image: url('../../../statics/mobile/clf/admin/003-employees_white.svg');
          }

          .mdi-cash {
            @extend %q-svg-icon-size;
            background-image: url('../../../statics/mobile/clf/admin/002-pay_white.svg');
          }

          .mdi-map-marker-outline {
            background-image: url('../../../statics/mobile/clf/menu-locator.svg');
          }

          .mdi-tumblr-reblog {
            background-image: url('../../../statics/mobile/clf/menu-transfer.svg');
          }

          .mdi-bank {
            background-image: url('../../../statics/mobile/clf/menu-bank.svg');
          }

          .mdi-settings-outline {
            background-image: url('../../../statics/mobile/clf/menu-settings.svg');
          }
        }
      }

      .fixed-list {
        position: absolute;
        left: 0;
        bottom: var(--safe-area-inset-bottom);
        width: 100%;
        border-top: 1px solid #ddd !important;
        background: white;

        .mdi-logout {
          @extend %q-svg-icon-size;
          background-image: url('../../../statics/mobile/clf/menu-logout.svg');
        }
      }

      .q-list {
        .q-item {
          padding: 14px 16px;
        }

        .q-item-label {
          text-transform: uppercase;
        }

        .q-item-main {
          margin-left: 15px;
        }
      }
    }

    .q-layout-page-container {
      display: flex;
      flex-direction: column;
    }
  }

  body.device-mode-clf {
    .replaced-icon.mdi-alert-outline {
      @extend %q-svg-icon-size;
      background-image: url("../../../statics/mobile/clf/tran-pending-load.svg");
    }

    .replaced-icon.mdi-arrow-right-circle-outline {
      @extend %q-svg-icon-size;
      background-image: url("../../../statics/mobile/clf/tran-credit.svg");
    }

    .replaced-icon.mdi-arrow-left-circle-outline {
      @extend %q-svg-icon-size;
      background-image: url("../../../statics/mobile/clf/tran-debit.svg");
    }

    .modal-page {
      .q-layout-header {
        .btn-back {
          @extend %q-svg-icon;
          margin-top: 4px;
          background-image: url("../../../statics/mobile/clf/top-menu-go-back.svg");
        }
      }
    }
  }

  @media (min-width: 360px) {
    #mobile-clf-layout {
      .q-layout-drawer-left {
        width: 310px !important;
        transform: translateX(-310px) !important;
      }
    }
  }

  @media (min-width: 400px) {
    #mobile-clf-layout {
      .q-layout-drawer-left {
        width: 320px !important;
        transform: translateX(-320px) !important;
      }
    }
  }
</style>
