<template>
  <q-layout id="mobile-empty-layout">

    <q-page-container class="relative-position">
      <transition
        :enter-active-class="'animated ' + enterClass"
        :leave-active-class="'animated ' + leaveClass">
        <router-view />
      </transition>
    </q-page-container>

  </q-layout>
</template>

<script>
import LayoutMixin from '../../mixins/LayoutMixin'
import { clf } from '../../common'

export default {
  name: 'MobileEmptyLayout',
  mixins: [
    LayoutMixin
  ],
  data () {
    return {
      enterClass: 'fadeInRight',
      leaveClass: 'fadeOutLeft'
    }
  },
  mounted () {
    clf.resetStatusBar()
  }
}
</script>

<style lang="scss">
  @import '../../css/font/SourceSansPro.css';
  @import '../../css/mobile/common';
</style>
