import { EventHandlerMixin, isSuperAdmin, request } from '../common'
import SessionDetectDialog from '../components/SessionDetectDialog'
import _ from 'lodash'
import $ from 'jquery'
import eventBus from '../eventBus'

export default {
  mixins: [
    EventHandlerMixin('admin-hide-all-nav', 'hideAllNav'),
    EventHandlerMixin('admin-show-drawer', 'showDrawer'),
    EventHandlerMixin('admin-hide-drawer', 'hideDrawer')
  ],
  components: {
    SessionDetectDialog
  },
  data () {
    return {
      root: '/a',
      left: true,
      keyword: '',
      collapsible: {},
      hovers: {}
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    },
    menus () {
      let all = this.user.menus || []
      if (all.length <= 0) {
        all = [
          {
            children: [],
            icon: 'fa fa-fw fa-user',
            id: 'profile',
            mdIcon: 'mdi-account',
            name: 'Profile',
            route: '/admin/user_modify/profile'
          },
          {
            children: [],
            icon: 'fa fa-fw fa-sign-out',
            id: 'logout',
            mdIcon: 'mdi-logout',
            name: 'Logout',
            route: '/admin/logout'
          }
        ]
      }
      return all
    },
    title () {
      return this.$route.meta.title || 'Admin Portal'
    },
    cpKey () {
      return this.user ? this.user.cpKey : ''
    },
    layoutClass () {
      const all = []
      if (this.user && this.user.cpKey) {
        all.push(this.user.cpKey)
      }
      if (this.left) {
        all.push('drawer-opened')
      }
      return all
    }
  },
  watch: {
    menus () {
      this.updateActiveMenu()
    },
    left () {
      this.$store.commit('Config/update', {
        sidebar: this.left
      })
      setTimeout(() => {
        $(window).resize()
      }, 300)
    },
    '$route': {
      deep: true,
      handler () {
        this.updateActiveMenu()
      }
    }
  },
  methods: {
    isSameRoute (a, b) {
      let p = a.indexOf('?')
      if (p >= 0) {
        a = a.substring(0, p)
      }
      p = b.indexOf('?')
      if (p >= 0) {
        b = b.substring(0, p)
      }
      return a === b
    },
    updateActiveMenu () {
      const route = this.$route.fullPath
      let found = false
      for (let m of this.menus) {
        this.$set(this.collapsible, m.id, false)
        if (!found) {
          for (let c of m.children) {
            const r = this.convertRoute(c.route, c.mdRoute)
            if (this.isSameRoute(r, route)) {
              this.$set(this.collapsible, m.id, true)
              found = true
              break
            }
          }
        }
      }
    },
    showSubNav (menu) {
      for (let m of this.menus) {
        this.$set(this.hovers, m.id, false)
        const ref = this.$refs[`subNav_${m.id}`]
        if (ref && ref[0]) {
          ref[0].hide().catch(() => {})
        }
      }
      this.$set(this.hovers, menu.id, true)
      const ref = this.$refs[`subNav_${menu.id}`]
      if (ref && ref[0]) {
        ref[0].show().catch(() => {})
      }
    },
    subNavHide (menu) {
      this.$set(this.hovers, menu.id, false)
    },
    hideAllNav () {
      _.forEach(this.hovers, (h, k) => {
        this.$set(this.hovers, k, false)

        const ref = this.$refs[`subNav_${k}`]
        if (ref && ref[0]) {
          ref[0].hide().catch(() => {})
        }
      })
    },
    toggleDrawer () {
      this.left = !this.left
    },
    showDrawer () {
      this.left = true
    },
    hideDrawer () {
      this.left = false
    },
    convertRoute (r, newRoute) {
      if (newRoute) {
        if (newRoute === true) {
          return this.root
        }
        return `${this.root}/${newRoute}`
      }

      if (!r) {
        return null
      }
      r = r.toLowerCase()
      if (r.startsWith(window.location.origin)) {
        r = r.replace(window.location.origin, '')
      }
      if (r.startsWith('http://') || r.startsWith('https://')) {
        window.location.href = r
        return r
      }
      if (r.startsWith('/')) {
        r = r.substr(1)
      }
      const p = r.indexOf('#/')
      if (p >= 0) {
        return r.substr(p + 1)
      }
      return `${this.root}/i/${r.replace(/\//g, '__')}`
    },
    ensureNav (r, newRoute) {
      const route = this.convertRoute(r, newRoute)
      if (this.isSameRoute(this.$route.fullPath, route)) {
        this.$root.$emit('frame-navigate-reload')
      }
    },
    goDefaultUrl () {
      if (this.$route.fullPath === this.root && this.user.defaultUrl) {
        if (this.user.defaultUrl === '/admin') {
          let m = _.first(this.menus)
          if (m.hasOwnProperty('children')) {
            let children = m.children
            if (children.length > 0) {
              m = _.first(children)
            }
          }
          this.$router.replace(this.convertRoute(m.route, m.mdRoute))
        } else {
          this.$router.replace(this.convertRoute(this.user.defaultUrl))
        }
      }
    },
    async storageHandler () {
      const resp = await request('/admin/user/profile')
      if (resp.success) {
        this.$store.commit('User/update', resp.data)
        this.$forceUpdate()
        localStorage.setItem('updatePic', false)
      }
    },
    isSuperAdmin (user) {
      return isSuperAdmin(user)
    }
  },
  mounted () {
    if (window.adminLayoutMountedCb) {
      window.adminLayoutMountedCb()
    }

    this.left = this.$store.state.Config.sidebar

    if (this.$route.path === `${this.root}/i/admin__user_index` && this.$route.query) {
      this.keyword = this.$route.query.keyword
    }

    for (let m of this.menus) {
      this.$set(this.collapsible, m.id, false)
      this.$set(this.hovers, m.id, false)
    }
    this.updateActiveMenu()

    eventBus.$on('loaded-user-profile', this.goDefaultUrl)

    window.document.addEventListener('scroll', () => {
      const header = window.document.querySelector('.q-layout-header')
      if (window.document.documentElement.scrollTop > 10) {
        header.classList.add('shadow')
      } else {
        header.classList.remove('shadow')
      }
    })

    window.addEventListener('storage', function () {
      if (localStorage.getItem('updatePic') === 'true') {
        setTimeout(() => {
          this.storageHandler()
        }, 2000)
      }
    }.bind(this), false)
  },
  beforeDestroy () {
    eventBus.$off('loaded-user-profile', this.goDefaultUrl)
  }
}
