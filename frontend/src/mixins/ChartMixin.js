import echarts from 'echarts'
import { request, moneyFormat, moneyMajorAmount } from '../common'
import _ from 'lodash'
import $ from 'jquery'

const ChartMixin = {
  props: [
    'chartId',
    'title',
    'params'
  ],
  data () {
    return {
      titleShow: true,
      data: {},
      sumData: '',
      chartType: null,
      chart: null
    }
  },
  methods: {
    initChart () {
      this.chart = echarts.init(document.getElementById(this.chart_id), 'primary')
      let series = null
      if (this.chartId === 'load') {
        series = [{
          name: 'All',
          type: 'line',
          smooth: true,
          data: []
        }, {
          name: 'First',
          type: 'line',
          smooth: true,
          data: []
        }, {
          name: 'Reload',
          type: 'line',
          smooth: true,
          data: []
        }]
      } else if (this.chartId === 'usage') {
        series = [{
          name: 'Usage',
          type: 'line',
          smooth: true,
          data: []
        }]
      } else if (this.chartId === 'revenue') {
        series = [{
          name: 'Revenue',
          type: 'line',
          smooth: true,
          data: []
        }]
      } else if (this.chartId === 'active') {
        series = [{
          name: 'Active cards',
          type: 'line',
          smooth: true,
          data: []
        }]
      } else if (this.chartId === 'total_account_balance') {
        series = [
          {
            name: 'Total issued cards',
            type: 'line',
            data: []
          },
          {
            name: 'Total account balance',
            type: 'line',
            data: []
          },
          {
            name: 'Usable spending limit',
            type: 'line',
            data: []
          }
        ]
      }
      let option = {
        legend: {
          data: []
        },
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          top: 20,
          left: 40
        },
        xAxis: {
          data: []
        },
        yAxis: {
          scale: true,
          padding: [0, 0, 0, 30]
        },
        series: series
      }
      this.chart.setOption(option)
    },
    async load (data) {
      if (!this.chart) {
        return
      }
      this.$q.loading.show()
      let url = '/admin/analytics/load-activity'
      if (this.chartId === 'load') {
        url = '/admin/analytics/load-activity'
      } else if (this.chartId === 'usage') {
        url = '/admin/analytics/usage-activity'
      } else if (this.chartId === 'user') {
        url = '/admin/analytics/users'
      } else if (this.chartId === 'revenue') {
        url = '/admin/analytics/revenue'
      } else if (this.chartId === 'active') {
        url = '/admin/analytics/active-cards'
      } else if (this.chartId === 'card_fees_collected') {
        url = '/admin/analytics/card-fees'
      } else if (this.chartId === 'total_account_balance') {
        url = '/admin/analytics/total-account-balance'
      }
      const resp = await request(url, 'get', data)
      this.$q.loading.hide()
      if (resp) {
        this.data = resp.data
        let series = null
        let xAxis = null
        let yAxis = null
        if (this.chartId === 'load') {
          xAxis = {
            data: resp.data.dates
          }
          series = [{
            data: this.transformData(resp.data.all, 'all')
          }, {
            data: this.transformData(resp.data.first, 'first')
          }, {
            data: this.transformData(resp.data.reload, 'reload')
          }]
        } else if (this.chartId === 'usage') {
          xAxis = {
            axisLabel: {
              interval: 0,
              formatter: function (params) {
                var newParamsName = ''
                var paramsNameNumber = params.length
                var provideNumber = 10
                var rowNumber = Math.ceil(paramsNameNumber / provideNumber)
                /**
                 * 判断标签的个数是否大于规定的个数， 如果大于，则进行换行处理 如果不大于，即等于或小于，就返回原标签
                 */
                // 条件等同于rowNumber>1
                if (paramsNameNumber > provideNumber) {
                  /** 循环每一行,p表示行 */
                  for (var p = 0; p < rowNumber; p++) {
                    var tempStr = ''
                    var start = p * provideNumber
                    var end = start + provideNumber
                    // 此处特殊处理最后一行的索引值
                    if (p === rowNumber - 1) {
                      // 最后一次不换行
                      tempStr = params.substring(start, paramsNameNumber)
                    } else {
                      // 每一次拼接字符串并换行
                      tempStr = params.substring(start, end) + '\n'
                    }
                    newParamsName += tempStr
                  }
                } else {
                  // 将旧标签的值赋给新标签
                  newParamsName = params
                }
                // 将最终的字符串返回
                return newParamsName
              }
            },
            data: resp.data.usageType
          }
          series = [{
            type: 'bar',
            data: this.transformData(resp.data.data)
          }]
        } else if (this.chartId === 'user') {
          xAxis = {
            axisLabel: {
              interval: 0,
              rotate: 45
            },
            data: resp.data.types
          }
          series = [{
            type: 'bar',
            data: resp.data.data
          }]
        } else if (this.chartId === 'revenue') {
          xAxis = {
            data: resp.data.dates
          }
          series = [{
            data: this.transformData(resp.data.data)
          }]
        } else if (this.chartId === 'active') {
          xAxis = {
            data: resp.data.dates
          }
          series = [{
            data: resp.data.data
          }]
        } else if (this.chartId === 'card_fees_collected') {
          xAxis = {
            axisLabel: {
              interval: 0,
              rotate: 30
            },
            data: resp.data.types
          }
          series = [{
            type: 'bar',
            data: resp.data.data
          }]
        } else if (this.chartId === 'total_account_balance') {
          series = [
            {
              name: 'Total issued cards',
              type: 'line',
              data: Object.values(resp.data.chartData.totalIssuedCards)
            },
            {
              name: 'Total account balance',
              type: 'line',
              data: Object.values(resp.data.chartData.totalAccountBalance)
            },
            {
              name: 'Usable spending limit',
              type: 'line',
              data: Object.values(resp.data.chartData.usableSpendingLimit)
            }
          ]

          xAxis = {
            type: 'category',
            data: resp.data.dates
          }
          // series = seriesLists
          yAxis = {
            type: 'value',
            splitLine: {
              show: false
            }
          }
        }

        if (!this.isSummary) {
          if (this.chartId === 'total_account_balance') {
            this.chart.setOption({
              xAxis: xAxis,
              series: series,
              grid: {
                left: '20%',
                top: '20%'
              },
              yAxis: yAxis,
              legend: {
                data: ['Total issued cards', 'Total account balance', 'Usable spending limit']
              },
              axisLabel: {
                color: '#999',
                fontSize: 10,
                formatter: v => {
                  if (v >= 1000 || v < -1000) {
                    return (v / 1000) + 'k'
                  }
                  return v
                }
              }
            })
          } else {
            this.chart.setOption({
              xAxis: xAxis,
              series: series,
              grid: {
                left: '20%'
              }
            })
          }
        }

        if (this.chartId === 'load') {
          let sumData = 0
          sumData = this.data.allLoad
          if (this.chartType) {
            if (this.chartType.y_axis === '0') {
              sumData = this.data.allLoad
            } else if (this.chartType.y_axis === '1') {
              sumData = this.data.firstLoad
            } else if (this.chartType.y_axis === '2') {
              sumData = this.data.reloadLoad
            }
          }
          this.sumData = moneyFormat(sumData, 'USD')
        }
        if (this.chartId === 'revenue') {
          this.sumData = moneyFormat(this.data.totalFee, 'USD')
        }
        if (this.chartId === 'card_fees_collected') {
          this.sumData = moneyFormat(this.data.totalFee, 'USD')
        }
      }
    },
    reloadType (data) {
      if (!data) {
        this.noDataInitType()
        return
      }
      if (data.widget_name === '0') {
        this.titleShow = false
      } else {
        this.titleShow = true
      }
      if (data.chart_type === '3') {
        if (data.chart_type === '3') {
          this.isSummary = true
          let id = '#' + this.chart_id
          $(id).css('display', 'none')
        }
      } else {
        let type = ''
        if (data.chart_type === '1') {
          type = 'line'
        } else if (data.chart_type === '2') {
          type = 'bar'
        }
        if (this.chartId === 'load') {
          this.chart.setOption({
            legend: {
              selected: {
                'All': data.y_axis === '0',
                'First': data.y_axis === '1',
                'Reload': data.y_axis === '2'
              }
            },
            series: [{
              type: type
            }, {
              type: type
            }]
          })
        } else {
          this.chart.setOption({
            series: [{
              smooth: true,
              type: type
            }]
          })
        }
      }
    },
    noDataInitType () {
      let type = 'line'
      if (this.chartId === 'load') {
        this.chart.setOption({
          legend: {
            selected: {
              'All': true,
              'First': false,
              'Reload': false
            }
          },
          series: [{
            type: type
          }, {
            type: type
          }, {
            type: type
          }]
        })
      } else {
        this.chart.setOption({
          series: [{
            smooth: true,
            type: type
          }]
        })
      }
    },
    transformData (items, key) {
      let data = _.transform(items, function (result, n) {
        result.push(moneyMajorAmount(n, 'USD', true))
      }, [])
      return data
    },

    reload () {
      this.$root.$emit('dashboard-reload-chart', this.chartId)
    }
  }
}
export default ChartMixin
