import { notify } from '../common'
import $ from 'jquery'

export default {
  data () {
    return {
      file: null,
      acceptFileTypes: [
        '.xlsx'
      ]
    }
  },
  computed: {
    fileAccept () {
      return this.acceptFileTypes.join(', ')
    }
  },
  methods: {
    selectFile () {
      if (this.file || this.isFileUploaded()) {
        return
      }
      $(this.$refs.file).click()
    },
    isFileUploaded () {
      return false
    },
    selectedFile () {
      const input = this.$refs.file
      if (input.files.length) {
        this.file = input.files[0]
      }
      input.type = 'text'
      input.type = 'file'
    },
    getValidDragFile (e) {
      if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length) {
        const file = e.dataTransfer.files[0]
        console.log(file)
        if (file && file.name) {
          for (const ext of this.acceptFileTypes) {
            if (file.name.toLowerCase().endsWith(ext)) {
              return file
            }
          }
        }
      }
      return null
    },
    setupDnd () {
      const area = $(this.$el).find('.upload-area'),
        dom = area.get(0)
      if (!dom) {
        return
      }

      dom.addEventListener('dragover', e => {
        e.stopPropagation()
        e.preventDefault()
        area.addClass('dropping')
      }, false)

      dom.addEventListener('dragleave', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')
      }, false)

      dom.addEventListener('drop', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')

        const file = this.getValidDragFile(e)
        if (file) {
          this.file = file
        } else {
          notify(`Please select a ${this.acceptFileTypes.join(', ')} file.`, 'negative')
        }
      }, false)
    }
  },
  mounted () {
    this.setupDnd()
  }
}
