import { notify } from '../common'

export default {
  data () {
    return {
      force: false,
      forceSync: false,
      requestCb: () => {
        if (this.force && !this.forceSync) {
          notify('Submitted update request. Please refresh the list again several minutes later.', 'positive', 3000)
        }
        this.force = false
      }
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        force: this.force
      }
    },
    async forceReload () {
      this.force = true
      return this.reload()
    }
  }
}
