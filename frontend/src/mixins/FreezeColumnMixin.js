import $ from 'jquery'

export default {
  data () {
    return {
      freezeColumn: 3,
      freezeColumnRight: -1,
      freezeNarrow: false,
      columnWidths: []
    }
  },
  watch: {
    data: {
      deep: true,
      handler () {
        for (const s of [0, 500, 1000, 2000, 5000]) {
          setTimeout(() => {
            this.updateColumnWidths()
          }, s)
        }
      }
    }
  },
  computed: {
    finalFreezeColumn () {
      return this.freezeNarrow ? -1 : this.freezeColumn
    },
    finalFreezeColumnRight () {
      return this.freezeNarrow ? 1 : this.freezeColumnRight
    },
    filteredVisibleColumns () {
      return this.visibleColumns || this.columns
    },
    columnStyles () {
      const all = []
      for (let i = 0; i < this.filteredVisibleColumns.length; i++) {
        all.push(this.columnStyle(i))
      }
      return all
    },
    columnClasses () {
      const all = []
      for (let i = 0; i < this.filteredVisibleColumns.length; i++) {
        all.push(this.columnClass(i))
      }
      return all
    }
  },
  methods: {
    updateColumnWidths () {
      this.$nextTick(() => {
        this.columnWidths = []
        $(this.$el).find('.sticky-table thead tr:first th').each((i, el) => {
          this.columnWidths.push($(el).outerWidth())
        })
      })
    },
    columnStyle (i) {
      const style = {}
      const length = this.columnWidths.length
      if (i <= this.finalFreezeColumn) {
        let left = 0
        for (let j = 0; j < length && j < i; j++) {
          left += this.columnWidths[j]
        }
        style.left = `${left}px`
      } else if (length - i <= this.finalFreezeColumnRight) {
        let right = -1
        for (let j = length - 1; j >= 0 && j > i; j--) {
          right += this.columnWidths[j]
        }
        style.right = `${right}px`
      }
      return style
    },
    columnClass (i) {
      const length = this.columnWidths.length
      return {
        relative: true,
        freeze: i <= this.finalFreezeColumn,
        'freeze-r': i >= length - this.finalFreezeColumnRight,
        'last-freeze': i === this.finalFreezeColumn,
        'last-freeze-r': i === length - this.finalFreezeColumnRight
      }
    },
    rowClass (row) {
      let all = { even: row.__index % 2, watch: row.watch }
      if (this.alterRowClassForMultiSel) {
        all = this.alterRowClassForMultiSel(all, row)
      }
      return all
    }
  },
  mounted () {
    if (window.innerWidth <= 768) {
      this.freezeNarrow = true
    }
  }
}
