export default {
  data () {
    return {
      hackedSend: null
    }
  },
  methods: {
    restoreXhrSend () {
      window.XMLHttpRequest.prototype.send = window.originalXhrSend
    },
    hackXhrSend () {
      window.XMLHttpRequest.prototype.send = this.hackedSend
    }
  },
  mounted () {
    this.hackedSend = window.XMLHttpRequest.prototype.send
  },
  beforeDestroy () {
    this.hackXhrSend()
  }
}
