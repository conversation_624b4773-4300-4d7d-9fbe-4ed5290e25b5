import { notifyResponse } from '../common'
import $ from 'jquery'
export default {
  methods: {
    validateForm (form) {
      if (!$(form).find('.file-attachment-id:eq(0)').val()) {
        if ($(form).find('.drop-file-area-input:eq(0)').val()) {
          notifyResponse('Please finish uploading your documents. Click the upload button in the bottom left of each photo before proceeding.')
        } else {
          notifyResponse('Please select the front image of your ID document to upload.')
        }
        return false
      }
      if ($('.file-section:visible:eq(1)').length && !$(form).find('.file-attachment-id:eq(1)').val()) {
        if ($(form).find('.drop-file-area-input:eq(1)').val()) {
          notifyResponse('Please finish uploading your documents. Click the upload button in the bottom left of each photo before proceeding.')
        } else {
          notifyResponse('Please select the back image of your ID document to upload.')
        }
        return false
      }
      return true
    },
    hide () {
      const self = this
      $('.file-section').each(function () {
        self.removeFile($(this).find('.selected-file-area a'))
      })
    },
    chooseFile ($a) {
      var $parent = $($a).parent()
      var $file = $parent.find('.drop-file-area-input')
      $file.click()
    },
    resetFile ($file) {
      $file.type = 'text'
      $file.type = 'file'
    },
    selectedFile ($file, file) {
      if (!file && $file.files.length <= 0) {
        return
      }
      if (!file) {
        file = $file.files[0]
      }

      var name = file.name.toLowerCase()
      if (!name.endsWith('.jpg') && !name.endsWith('.jpeg') && !name.endsWith('.png')) {
        this.resetFile($file)
        notifyResponse('Only JPG and PNG formats are supported!')
        return
      }
      if (file.size > 5242880) { // 5MB = 5 * 1024 * 1024
        this.resetFile($file)
        notifyResponse('The file is too large. Please compress it to under 5MB first!')
        return
      }

      var $parent = $($file).parent()
      var $selected = $parent.find('.selected-file-area')
      $selected.removeClass('hide failed')
      $parent.find('.drop-file-area').addClass('hide')

      this.displayPicture($selected.find('img.preview')[0], file)
    },

    doUploadFile ($a, file) {
      let widgetToken = null
      const pos = location.href.indexOf('x-widget-token')
      if (pos > 0) {
        const search = location.href.substring(pos)
        const params = new URLSearchParams(search)
        widgetToken = params.get('x-widget-token').split('#')[0]
      } else {
        widgetToken = this.$store.state.Faas.widgetToken
      }

      var $parent = $($a).parents('.file-section')
      var $selected = $parent.find('.selected-file-area')

      var $tip = $selected.find('.tip')
      $tip.text('Uploading file...')

      var formData = new FormData()
      formData.append('category', 'idology_file')
      formData.append('file', file)

      var oReq = new XMLHttpRequest()
      oReq.open('POST', '/faas/widget/api/attachments', true)
      oReq.setRequestHeader('X-Requested-With', 'XMLHttpRequest')
      // console.log('asdfgtyrewqert')
      // console.log(widgetToken)
      oReq.setRequestHeader('x-widget-token', widgetToken)
      oReq.onreadystatechange = function () {
        if (oReq.readyState === 4 && oReq.status === 200) {
          var resp = JSON.parse(oReq.responseText)
          if (!resp.success) {
            notifyResponse(resp.message)
            this.removeFile($selected.find('a'))
            return
          }
          $tip.text('Uploaded. Ready to verify.')
          $selected.addClass('success')
          $parent.find('.file-attachment-id').val(resp.data.file.id)

          this.resetFile($parent.find('input[type=file]')[0])
        }
      }.bind(this)
      oReq.upload.onprogress = function (e) {
        $tip.text('Uploading file... ')
      }
      oReq.send(formData)

      $parent.data('req', oReq)
    },
    setupDnd () {
      const self = this
      $('.drop-file-area').each(function () {
        var ifForm = $(this),
          dom = ifForm.get(0)
        ifForm.off('dragover dragleave drop')

        dom.addEventListener('dragover', function (e) {
          e.stopPropagation()
          e.preventDefault()

          ifForm.addClass('dropping')
        }, false)
        dom.addEventListener('dragleave', function (e) {
          e.stopPropagation()
          e.preventDefault()

          ifForm.removeClass('dropping')
        }, false)
        dom.addEventListener('drop', function (e) {
          e.stopPropagation()
          e.preventDefault()

          ifForm.removeClass('dropping')

          if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length) {
            self.selectedFile($(this).siblings('.drop-file-area-input'), e.dataTransfer.files[0])
          }
        }, false)
      })
    },
    updatePictures () {
      var $sections = $('.file-section')
      var type = this.docType
      if (type && type !== 'PSP') {
        $sections.show()
      } else {
        $sections.eq(1).hide()
        this.removeFile($sections.eq(1).find('.selected-file-area a'))
      }
    },
    displayPicture (img, file) {
      var $img = $(img)
      $img.data('file', file)

      var cache = new Image()
      cache.onload = function () {
        $img.data('width', cache.width)
        $img.data('height', cache.height)
      }

      var fileReader = new FileReader()
      fileReader.onload = function (e) {
        img.src = e.target.result
        cache.src = e.target.result
      }
      fileReader.readAsDataURL(file)
    },
    rotatePicture ($a, degrees) {
      var $parent = $($a).parents('.file-section')
      var img = $parent.find('img.preview')[0]
      var $img = $(img)
      var width = $img.data('width')
      var height = $img.data('height')

      var canvas = document.createElement('canvas')
      canvas.width = height
      canvas.height = width

      var context = canvas.getContext('2d')
      context.translate(canvas.width / 2, canvas.height / 2)
      context.rotate(degrees * Math.PI / 180)
      context.drawImage(img, -width / 2, -height / 2)

      canvas.toBlob(function (blob) {
        this.displayPicture(img, blob)
      }.bind(this), 'image/jpeg', 0.93)
    },
    removeFile ($a) {
      var $parent = $($a).parents('.file-section').eq(0)
      var req = $parent.data('req')
      if (req) {
        req.abort()
      }

      var $selected = $parent.find('.selected-file-area')
      $selected.addClass('hide').removeClass('success failed')
      $selected.find('.elements a').not('.red').show()
      $selected.find('.tip').text('')
      $selected.find('img.preview').attr('src', '')

      $parent.find('.drop-file-area').removeClass('hide')

      var $file = $parent.find('.drop-file-area-input')
      if ($file.length) {
        $file[0].type = 'text'
        $file[0].type = 'file'
      }

      $parent.find('.file-attachment-id').val('')
    },
    uploadFile ($a) {
      var $parent = $($a).parents('.file-section')
      var img = $parent.find('img.preview')[0]
      var $img = $(img)
      var width = $img.data('width')
      var height = $img.data('height')

      var $selected = $parent.find('.selected-file-area')
      $selected.find('.elements a').not('.red').hide()

      var $tip = $selected.find('.tip')
      $tip.text('Processing...')

      var maxWidth = 4288
      var maxHeight = 3216
      var nWidth = width
      var nHeight = height

      // Resize the image if needed
      if (width > maxWidth || height > maxHeight) {
        var ratio = width / height
        var maxRatio = maxWidth / maxHeight
        if (ratio >= maxRatio) {
          nWidth = Math.round(maxWidth * 0.8)
          nHeight = Math.round(nWidth / ratio)
        } else {
          nHeight = Math.round(maxHeight * 0.8)
          nWidth = Math.round(ratio * nHeight)
        }
      } else {
        return this.doUploadFile($a, $img.data('file'))
      }

      var canvas = document.createElement('canvas')
      canvas.width = nWidth
      canvas.height = nHeight

      var context = canvas.getContext('2d')
      context.drawImage(img, 0, 0, width, height, 0, 0, nWidth, nHeight)

      canvas.toBlob(function (blob) {
        this.doUploadFile($a, blob)
      }.bind(this), 'image/jpeg', 0.93)
    }
  }
}
