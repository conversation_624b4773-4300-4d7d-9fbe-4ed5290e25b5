import { mapState } from 'vuex'
import queryString from 'query-string'

export default {
  computed: {
    ...mapState({
      userCard: state => state.UserCard
    }),
    cardProgram () {
      if (!this.userCard || !this.userCard.card) {
        return null
      }
      return this.$store.getters['Config/cardProgram'](this.userCard.card.id)
    },
    toolbarStyle () {
      const def = {}
      const cp = this.cardProgram
      if (cp && cp.customize) {
        def['background-color'] = `${cp.customize.linkActiveColor} !important`
        // TODO: add more
      }
      const search = queryString.parse(window.location.search)
      if (search && search.mainColor) {
        def['background-color'] = `#${search.mainColor} !important`
      }
      return def
    }
  }
}
