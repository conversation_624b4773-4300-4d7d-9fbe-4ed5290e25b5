import _ from 'lodash'

export default {
  data () {
    return {
      multiSel: {
        enabled: false,
        map: {},
        selected: {},
        ids: {}
      },
      multiSelIdKey: 'id'
    }
  },
  watch: {
    data: {
      deep: true,
      handler () {
        const key = this.multiSelIdKey
        const map = {}
        for (const item of this.data) {
          const id = item[key]
          map[id] = item
          if (!(id in this.multiSel.ids)) {
            this.$set(this.multiSel.ids, id, false)
          }
        }
        this.multiSel.map = map
      }
    },
    'multiSel.ids': {
      deep: true,
      handler () {
        const sel = this.multiSel
        _.forEach(sel.ids, (on, id) => {
          if (on) {
            this.$set(sel.selected, id, sel.selected[id] || sel.map[id])
          } else {
            this.$delete(sel.selected, id)
          }
        })
      }
    }
  },
  computed: {
    multiSelActionLabel () {
      let label = null
      const selected = Object.keys(this.multiSel.selected).length
      if (selected) {
        label = `Selected ${selected} rows`
      }
      return label
    }
  },
  methods: {
    onHeaderMultiSelectAll (selected) {
      const key = this.multiSelIdKey
      for (const item of this.data) {
        const id = item[key]
        this.$set(this.multiSel.ids, id, selected)
      }
    },
    alterRowClassForMultiSel (obj, row) {
      obj = obj || {}
      const id = row[this.multiSelIdKey]
      if (this.multiSel.ids[id]) {
        obj['multi-selected'] = true
      }
      return obj
    },
    toggleMultiSelRow (row) {
      const id = row[this.multiSelIdKey]
      this.$set(this.multiSel.ids, id, !this.multiSel.ids[id])
    },
    resetMultiSel () {
      const sel = this.multiSel
      _.forEach(sel.ids, (on, id) => {
        this.$set(sel.ids, id, false)
      })
    },
    getMultiSelKeys () {
      const result = []
      _.forEach(this.multiSel.ids, (on, id) => {
        if (on) {
          result.push(id)
        }
      })
      return result
    },
    setMultiSelKeys (ids) {
      _.forEach(this.multiSel.ids, (v, k) => {
        this.multiSel.ids[k] = _.includes(ids, k)
      })
      _.forEach(ids, (v, k) => {
        if (!this.multiSel.ids[v]) {
          this.$set(this.multiSel.ids, v, true)
        }
      })
    }
  }
}
