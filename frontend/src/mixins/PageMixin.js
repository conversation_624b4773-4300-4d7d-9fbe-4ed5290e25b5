import { deviceMode } from '../const'
import $ from 'jquery'
import { EventHandlerMixin } from '../common'

export default {
  mixins: [
    EventHandlerMixin('reload-page-data')
  ],
  data () {
    let title = 'VirtualCards'
    const host = location.hostname
    if (host.startsWith('utc.')) {
      title = 'Universal Transaction Compliance'
    } else if (host.startsWith('fis.') || host.indexOf('prepaidprogrammanagement') >= 0) {
      title = 'FIS'
    } else if (window.ternCustomizes && window.ternCustomizes._cp_name) {
      title = window.ternCustomizes._cp_name
    }
    return {
      title,
      hideAjaxBarWhenLoading: false,
      loading: false,
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 10,
        page: 1
      }
    }
  },
  computed: {
    adminPortalName () {
      if (deviceMode === 'clf') {
        return 'Universal Transaction Compliance Mobile'
      }

      const user = this.$store.state.User
      if (user && user.organization && user.organization.type) {
        return `${user.organization.type} Portal`
      }
      return 'Admin Portal'
    }
  },
  watch: {
    loading () {
      if (this.hideAjaxBarWhenLoading) {
        if (this.loading) {
          this.$store.commit('Config/update', {
            globalAjaxBar: false
          })
        } else {
          setTimeout(() => {
            this.$store.commit('Config/update', {
              globalAjaxBar: true
            })
          }, 2000)
        }
      }
    },
    title: {
      immediate: true,
      handler () {
        window.document.title = `${this.title} - ${this.adminPortalName}`
      }
    }
  },
  methods: {
    async reload () {},
    async refresher (loaded) {
      await this.reload()
      loaded('done')
    }
  },
  mounted () {
    const $stick = $('.sticky-table')
    if ($stick.length) {
      $(window).on('resize', () => {
        const height = window.innerHeight -
          $('.q-layout-header').height() -
          $('.q-layout-page .page-header').height() -
          ($('.sticky-table .q-table-bottom:visible').height() || 0) - 70
        $stick.find('.q-table-middle').css('max-height', `${height}px`)
      }).resize()
    }
  }
}
