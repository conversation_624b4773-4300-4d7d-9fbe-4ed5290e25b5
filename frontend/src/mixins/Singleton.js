import moment from 'moment'
import _ from 'lodash'
import { toggleModalClass } from '../common'

const time = moment.unix()
const name = `singleton-component-${time}`

export default {
  name,
  data () {
    return {
      visible: false,
      origin: null,
      entity: {},
      defaultEntity: {},
      loading: false,
      addOldCardToLegacy: true
    }
  },
  watch: {
    visible (v) {
      toggleModalClass(v)
    }
  },
  methods: {
    show () { },
    _preShow () { },
    async _show (arg = {}) {
      if (this._asyncShow) {
        await this._asyncShow(arg)
      }
      this._preShow(arg)
      this.visible = true
      this.origin = arg
      this.entity = _.defaults(_.cloneDeep(arg), _.cloneDeep(this.defaultEntity))
      this.show(arg)

      if (this.$v && this.$v.$reset) {
        this.$v.$reset()
      }
    },
    hide () { },
    _preHide () { },
    _hide (arg = {}) {
      this._preHide(arg)
      this.visible = false
      this.origin = null
      setTimeout(() => {
        this.entity = _.cloneDeep(this.defaultEntity)
      }, 300)
      this.hide(arg)
    },
    _hideAndEmit (evt, args) {
      setTimeout(() => {
        this.$root.$emit(evt, args)
      }, 200)

      this._hide()
    },
    _hideAndCall (callback) {
      setTimeout(() => {
        callback && callback()
      }, 200)

      this._hide()
    }
  },
  mounted () {
    this.$root.$on(`show-${this.$options.name}`, this._show)
    this.$root.$on(`hide-${this.$options.name}`, this._hide)
  },
  beforeDestroy () {
    this.$root.$off(`show-${this.$options.name}`, this._show)
    this.$root.$off(`hide-${this.$options.name}`, this._hide)
  },
  created () {
    this.entity = this.defaultEntity
  }
}
