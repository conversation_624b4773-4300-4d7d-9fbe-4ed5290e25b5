import { request, toSelectOptions } from '../common'

window.statesCache = []

export default {
  methods: {
    async loadStates (countryId) {
      if (!countryId) {
        return []
      }

      if (window.statesCache[countryId]) {
        return window.statesCache[countryId]
      }

      this.loading = true
      const resp = await request(`/admin/country/${countryId}/states`)
      this.loading = false
      if (resp.success) {
        window.statesCache[countryId] = toSelectOptions(resp.data)
        return window.statesCache[countryId]
      }
      return []
    }
  }
}
