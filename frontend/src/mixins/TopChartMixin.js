import echarts from 'echarts'
import { request, moneyMajorAmount } from '../common'
import _ from 'lodash'
import $ from 'jquery'
import { chartName, chartType } from '../const'

const TopChartMixin = {
  props: [
    'chartId',
    'loadUrl'
  ],
  data () {
    return {
      data: {},
      isSummary: false,
      sumData: '',
      chartType: null,
      chart: null
    }
  },
  computed: {
    chart_id () {
      return this.chartId + '_chart_graph'
    }
  },
  methods: {
    initData (data) {
      this.chartType = data
      this.reloadType(data)
      if (this.chart) {
        $(window).on('resize', this.chart.resize)
      }
    },
    initChart () {
      if (_.isEmpty(this.$route.query)) {
        let id = '#' + this.chart_id
        $(id).css('display', 'none')
        return
      }
      this.chart = echarts.init(document.getElementById(this.chart_id), 'primary')
      let series = []
      if (this.chartId === chartName.LOAD_CHART) {
        series = [{
          name: 'All',
          type: 'line',
          smooth: true,
          data: []
        }, {
          name: 'First',
          type: 'line',
          smooth: true,
          data: []
        }, {
          name: 'Reload',
          type: 'line',
          smooth: true,
          data: []
        }]
      } else if (this.chartId === chartName.USAGE_CHART) {
        series = [{
          name: 'Usage',
          type: 'line',
          smooth: true,
          data: []
        }]
      } else if (this.chartId === chartName.REVENUE_CHART) {
        series = [{
          name: 'Revenue',
          type: 'line',
          smooth: true,
          data: []
        }]
      } else if (this.chartId === chartName.ACTIVE_CARDS_CHART) {
        series = [{
          name: 'Active cards',
          type: 'line',
          smooth: true,
          data: []
        }]
      } else if (this.chartId === chartName.USER_CHART) {
        series = [{
          name: 'User',
          type: 'bar',
          smooth: true,
          data: []
        }]
      }
      let option = {
        legend: {
          data: []
        },
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          top: 40,
          left: 80
        },
        xAxis: {
          data: []
        },
        yAxis: {
          scale: true,
          padding: [0, 0, 0, 30]
        },
        series: series
      }
      this.chart.setOption(option)
    },
    async reloadChart (data) {
      if (!this.chart) {
        return
      }
      this.data = data
      if (data) {
        let series = null
        let xAxis = null
        if (this.chartId === chartName.LOAD_CHART) {
          xAxis = {
            data: data.dates
          }
          series = [{
            data: this.transformData(data.all, 'all')
          }, {
            data: this.transformData(data.first, 'first')
          }, {
            data: this.transformData(data.reload, 'reload')
          }]
        } else if (this.chartId === chartName.USAGE_CHART) {
          xAxis = {
            data: data.usageType
          }
          series = [{
            data: this.transformData(data.data)
          }]
        } else if (this.chartId === chartName.USER_CHART) {
          xAxis = {
            data: data.types
          }
          series = [{
            name: 'User',
            type: 'bar',
            data: data.data
          }]
        } else if (this.chartId === chartName.REVENUE_CHART) {
          xAxis = {
            data: data.dates
          }
          series = [{
            data: this.transformData(data.data)
          }]
        } else if (this.chartId === chartName.ACTIVE_CARDS_CHART) {
          xAxis = {
            data: data.dates
          }
          series = [{
            data: data.data
          }]
        } else if (this.chartId === chartName.CARD_FEES_CHART) {
          xAxis = {
            data: data.types
          }
          series = [{
            data: data.data
          }]
        }
        if (!this.isSummary) {
          this.chart.setOption({
            xAxis: xAxis,
            series: series
          })
        }
      }
    },
    async getChartSetting () {
      this.initChart()
      this.$q.loading.show()
      const resp = await request('/admin/analytics/card-program/chart-setting')
      this.$q.loading.hide()
      if (resp) {
        if (this.chartId === chartName.USER_CHART) {
          this.initData(resp.data.New_Sign_Ups)
        }
        if (this.chartId === chartName.LOAD_CHART) {
          this.initData(resp.data.Load_Activity)
        }
        if (this.chartId === chartName.ACTIVE_CARDS_CHART) {
          this.initData(resp.data.Active_cards)
        }
        if (this.chartId === chartName.REVENUE_CHART) {
          this.initData(resp.data.Revenue)
        }
        if (this.chartId === chartName.USAGE_CHART) {
          this.initData(resp.data.Card_spend)
        }
        if (this.chartId === chartName.CARD_FEES_CHART) {
          this.initData(resp.data.Card_fees_collected)
        }
      }
    },
    reloadType (data) {
      if (!data) {
        this.noDataInitType()
        return
      }
      if (!this.chart) {
        return
      }
      let id = '#' + this.chart_id
      if (data.chart_type === chartType.SUMMARY_CHART) {
        this.isSummary = true
        $(id).css('display', 'none')
      } else {
        $(id).css('height', '326px')
        this.chart.resize()
        let type = ''
        if (data.chart_type === chartType.BAR_CHART) {
          type = 'bar'
        } else {
          type = 'line'
        }
        if (this.chartId === chartName.LOAD_CHART) {
          this.chart.setOption({
            legend: {
              selected: {
                'All': data.y_axis === '0',
                'First': data.y_axis === '1',
                'Reload': data.y_axis === '2'
              }
            },
            series: [{
              type: type
            }, {
              type: type
            }]
          })
        } else {
          this.chart.setOption({
            series: [{
              smooth: true,
              type: type
            }]
          })
        }
      }
    },
    noDataInitType () {
      let type = 'line'
      if (this.chartId === chartName.LOAD_CHART) {
        this.chart.setOption({
          legend: {
            selected: {
              'All': true,
              'First': false,
              'Reload': false
            }
          },
          series: [{
            type: type
          }, {
            type: type
          }, {
            type: type
          }]
        })
      } else {
        this.chart.setOption({
          series: [{
            smooth: true,
            type: type
          }]
        })
      }
    },
    transformData (items, key) {
      let data = _.transform(items, function (result, n) {
        result.push(moneyMajorAmount(n, 'USD', true))
      }, [])
      return data
    }
  }
}
export default TopChartMixin
