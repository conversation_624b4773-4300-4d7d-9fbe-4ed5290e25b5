import { request } from '../../common'

export default {
  methods: {
    async request ({ pagination }) {
      const everLoaded = this.everLoaded

      this.beforeRequest({ pagination })
      if (pagination.page <= 0) {
        return
      }

      const params = this.mergeQuerySortParams(pagination)
      this.loading = true
      const resp = await request(`${this.c.clf.apiPrefix()}/${this.apiEndpointName}/${pagination.page}/${pagination.rowsPerPage}`, 'get', params)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.count

        if (!everLoaded && this.status === 'unpaid' && resp.data.count <= 0) {
          this.everLoaded = true
          this.status = 'all'
          this.reload()
        }
      }
    },

    getQueryParams () {
      const params = this.$refs.dateRangeFilter.params()
      params.status = this.status
      return params
    }
  }
}
