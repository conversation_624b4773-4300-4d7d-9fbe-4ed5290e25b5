import echarts from 'echarts'
import $ from 'jquery'
import { moneyFormat } from '../../common'

const ChartMixin = {
  data () {
    return {
      chart: [],
      option: null,
      title: null,
      str: ''
    }
  },
  methods: {
    resize () {
      for (const key in this.chart) {
        if (Object.hasOwnProperty.call(this.chart, key)) {
          const element = this.chart[key]
          element.resize()
        }
      }
    },
    updateChart (data) {
      this.str = data.type === 'CreditsChart' ? data.difference : 0
      this.initChart(data, this.str)
      if (data.type === 'CreditsChart') {
        this.option.series.data = [
          {
            value: data.credits,
            name: 'Credits'
          },
          {
            value: data.debits,
            name: 'Debits'
          }
        ]
      } else {
        this.option.series.data = Object.values(data.chart)
      }
      this.chart[data.type].setOption(this.option)
      $(window).on('resize', this.resize)
    },
    initChart (data, str) {
      let chartId = data.type
      this.chart[chartId] = echarts.init(document.getElementById(chartId), 'primary')
      let series = null
      let grid = null
      let legend = null
      let tooltip = null
      let count = []
      let avg = []
      if (chartId === 'CommissionsChart') {
        // build a color map as your need.
        let colorList = ['#9c85ca', '#5190b7', '#ff9031', '#0062ff']
        let labelList = ['Membership Fee', 'Monthly Fee', 'Transaction Fee', 'Unload Fee']
        let count = Object.values(data.chartCount)
        series = {
          data: [0, 0, 0, 0, 0, 0],
          type: 'bar',
          itemStyle: {
            barBorderRadius: [10, 10, 0, 0],
            color: function (params) {
              return colorList[params.dataIndex]
            }
          },
          label: {
            show: true,
            position: 'bottom',
            color: '#92929e',
            formatter: function (params) {
              // build a color map as your need.
              return labelList[params.dataIndex]
            }
          }
        }
        grid = {
          left: '8%',
          right: '20px',
          top: '5px',
          bottom: '20px'
        }
        tooltip = {
          trigger: 'item',
          backgroundColor: '#ffffff',
          padding: 0,
          formatter: function (params) {
            return '<div style="box-shadow: 0 5px 14px 0 rgba(68, 68, 80, 0.2);border-radius:4px;padding:30px;padding-top:16px;"><p style="text-align:center;color:' + colorList[params.dataIndex] + ';margin:0px;">' + labelList[params.dataIndex] + '</p>' +
              '<div style="line-height:30px;min-width:215px;display:flex;justify-content:space-between;color:#231f20;">#of TXNs<span>Total($)</span></div>' +
              '<div style="line-height:30px;min-width:215px;display:flex;justify-content:space-between;color:#231f20;">' + count[params.dataIndex] + '<span>' + moneyFormat(params.value * 100) + '</span></div>' +
              '</div>'
          }
        }
      }

      if (chartId === 'SpendChart') {
        count = Object.values(data.chartTotal)
        avg = Object.values(data.chartAvg)
        let labelList = ['Computers \n& Digital', 'Entertainment \n& Shopping', 'Food & \nBeverage', 'Home', 'Health & \nBeauty', 'Other']
        let colorList = ['#0062ff', '#ff974a', '#3dd598', '#ffc542', '#fc5a5a', '#9c85ca']

        series = {
          data: [0, 0, 0, 0, 0, 0],
          type: 'bar',
          itemStyle: {
            barBorderRadius: [10, 10, 0, 0],
            color: function (params) {
              return colorList[params.dataIndex]
            }
          },
          label: {
            show: true,
            position: 'bottom',
            color: '#92929e',
            formatter: function (params) {
              // build a color map as your need.
              return labelList[params.dataIndex]
            }
          }
        }
        grid = {
          left: '8%',
          right: '20px',
          top: '5px',
          bottom: '35px'
        }
        tooltip = {
          trigger: 'item',
          backgroundColor: '#ffffff',
          padding: 0,
          formatter: function (params) {
            return '<div style="box-shadow: 0 5px 14px 0 rgba(68, 68, 80, 0.2);border-radius:4px;padding:30px;padding-top:16px;"><p style="text-align:center;color:' + colorList[params.dataIndex] + ';margin:0px;">' + labelList[params.dataIndex] + '</p>' +
              '<div style="line-height:30px;min-width:215px;display:flex;justify-content:space-between;color:#231f20;">#of TXNs<span>' + count[params.dataIndex] + '</span></div>' +
              '<div style="line-height:30px;min-width:215px;display:flex;justify-content:space-between;color:#231f20;">Total ($)<span>' + moneyFormat(params.value * 100) + '</span></div>' +
              '<div style="line-height:30px;min-width:215px;display:flex;justify-content:space-between;color:#231f20;">Avg. ($)<span>' + moneyFormat(avg[params.dataIndex] * 100) + '</span></div>' +
              '</div>'
          }
        }
      }

      if (chartId === 'CreditsChart') {
        let colorList = ['#00d993', '#fc5a5a']
        series = {
          name: 'Amount',
          type: 'pie',
          radius: ['50%', '70%'],
          label: {
            show: true,
            position: 'center',
            formatter: [
              `{a| Difference}`,
              `{b|${moneyFormat(str)}}`
            ].join('\n'),
            rich: {
              a: {
                color: '#ffc541',
                fontSize: 18
              },
              b: {
                color: '#171725',
                fontSize: 18,
                lineHeight: 25
              }
            }
          },
          itemStyle: {
            color: function (params) {
              return colorList[params.dataIndex]
            }
          },
          data: []
        }
        grid = {
          left: '8%',
          right: '20px',
          top: '5px',
          bottom: '35px'
        }
        legend = {
          // orient: 'vertical',
          // left: 10,
          bottom: 2,
          data: ['Credits', 'Debits']
        }
        tooltip = {
          trigger: 'item',
          backgroundColor: '#ffffff',
          padding: 0,
          formatter: function (params) {
            return '<div style="box-shadow: 0 5px 14px 0 rgba(68, 68, 80, 0.2);border-radius:4px;padding:30px;padding-top:16px;"><p style="text-align:center;color:' + colorList[params.dataIndex] + ';margin:0px;">' + params.name + '</p>' +
              '<div style="line-height:30px;min-width:215px;display:flex;justify-content:space-between;color:#231f20;">#of TXNs<span>' + (params.name === 'Debits' ? data.debitsCount : data.creditsCount) + '</span></div>' +
              '<div style="line-height:30px;min-width:215px;display:flex;justify-content:space-between;color:#231f20;">Total ($)<span>' + moneyFormat(params.value) + '</span></div>' +
              '<div style="line-height:30px;min-width:215px;display:flex;justify-content:space-between;color:#231f20;">Avg. ($)<span>' + (params.name === 'Debits' ? moneyFormat(data.debitsAvg) : moneyFormat(data.creditsAvg)) + '</span></div>' +
              '</div>'
          }
        }
      }

      this.option = {
        legend: legend,
        tooltip: tooltip,
        xAxis: {
          type: 'category',
          show: false
        },
        grid: grid,
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          }
        },
        series: series
      }
    }
  }
}
export default ChartMixin
