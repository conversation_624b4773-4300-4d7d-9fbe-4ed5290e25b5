import PageMixin from '../PageMixin'
import { bindState, bindStateReadonly } from '../../common'
import DateRangeFilter from '../../components/DateRangeFilter'

export default {
  mixins: [
    PageMixin
  ],
  components: {
    DateRangeFilter
  },
  data () {
    return {
      dateRanges: [
        {
          label: 'All',
          value: 'all'
        },
        {
          label: 'Daily',
          value: 'today'
        },
        {
          label: 'Weekly',
          value: 'week'
        },
        {
          label: 'Monthly',
          value: 'month'
        },
        {
          label: 'Quarterly',
          value: 'quarter'
        },
        {
          label: 'Yearly',
          value: 'year'
        },
        {
          label: 'Custom Range',
          value: 'custom_range'
        }
      ],
      dateRange: 'month'
    }
  },
  computed: {
    ...bindStateReadonly('Config', 'cardPrograms'),
    ...bindState('Config', 'selectedCp')
  }
}
