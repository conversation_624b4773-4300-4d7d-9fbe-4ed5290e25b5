import CardLoadUnloadDialog from '../../pages/card-management/load-unload/dialog'

export default {
  components: {
    CardLoadUnloadDialog
  },
  computed: {
    esSoloMenu () {
      if (this.user.cpKey === 'cp_es_solo') {
        if (['Platform Owner', 'Program Owner'].includes(this.user.currentRole)) {
          return true
        }
      }
      return false
    }
  },
  methods: {
    showLoadUnloadDialog (user) {
      this.$root.$emit('show-card-load-unload-dialog')
    },
    esSoloLoginAs (user) {
      if (this.user.cpKey !== 'cp_es_solo') {
        return true
      }
      if (!user.teams || !user.teams.length) {
        return false
      }
      for (const team of user.teams) {
        if ([
          'Platform Owner',
          'Program Owner'
        ].includes(team)) {
          return true
        }
      }
      return false
    }
  }
}
