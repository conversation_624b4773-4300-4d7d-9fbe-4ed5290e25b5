import PageMixin from '../PageMixin'
import { bindState, bindStateReadonly } from '../../common'
import DateRangeFilter from '../../components/DateRangeFilter'

export default {
  mixins: [
    PageMixin
  ],
  components: {
    DateRangeFilter
  },
  data () {
    return {
      dateRanges: [
        {
          label: 'Daily',
          value: 'today'
        },
        {
          label: 'Weekly',
          value: 'week'
        },
        {
          label: 'Monthly',
          value: 'month'
        },
        {
          label: 'Quarterly',
          value: 'quarter'
        },
        {
          label: 'Yearly',
          value: 'year'
        },
        {
          label: 'All',
          value: 'all'
        },
        {
          label: 'Custom Range',
          value: 'custom_range'
        }
      ],
      dateRange: 'month',
      spendingBreakdownIcons: {
        computer: 'mdi-laptop',
        entertainment: 'mdi-controller-classic-outline',
        finance: 'mdi-finance',
        food: 'mdi-silverware-fork',
        health: 'mdi-stethoscope',
        transportation: 'mdi-car',
        house: 'mdi-home-outline',
        other: 'mdi-shopping'
      }
    }
  },
  computed: {
    ...bindStateReadonly('Config', 'cardPrograms'),
    ...bindState('Config', 'selectedCp'),
    user () {
      return this.$store.state.User
    },
    masterAdmin () {
      const user = this.user
      return user && user.currentRole === 'MasterAdmin'
    },
    agentAdmin () {
      const user = this.user
      return user && user.currentRole === 'Faas Admin'
    },
    masterAgentAdmin () {
      return this.masterAdmin || this.agentAdmin
    }
  }
}
