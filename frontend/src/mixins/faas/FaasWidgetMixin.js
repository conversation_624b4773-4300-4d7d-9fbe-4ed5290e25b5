import { fullName, request, toSelectOptions } from '../../common'
export default {
  data () {
    return {
      user: {},
      xWidgetToken: null
    }
  },
  computed: {
    fullName () {
      return fullName(this.user)
    },
    countries () {
      const cp = this.$store.getters['Config/current']
      if (!cp) {
        return []
      }
      return toSelectOptions(cp.countries)
    }
  },
  methods: {
    async _asyncShow (arg) {
      this.$store.commit('Faas/update', {
        widgetToken: null
      })
      if (arg.xWidgetToken || arg.widgetToken) {
        this.xWidgetToken = arg.xWidgetToken || arg.widgetToken
        return
      }
      const userId = arg['User ID'] || arg['Member ID'] || arg['Employee ID'] || arg['userId']
      if (userId) {
        this.$q.loading.show()
        const resp = await request(`/admin/faas/base/members/${userId}/widget-token`)
        this.$q.loading.hide()
        if (resp.success) {
          this.xWidgetToken = resp.data
          this.$store.commit('Faas/update', {
            widgetToken: this.xWidgetToken
          })
        }
      }
    },
    buildRequestParams (params) {
      params['x-widget-token'] = this.xWidgetToken
      return params
    },
    async request (url, method = 'get', params = {}, silent = false) {
      return request(url, method, this.buildRequestParams(params), silent)
    },
    async loadUser () {
      this.$q.loading.show()
      const resp = await this.request(`/faas/widget/api/user`)
      this.$q.loading.hide()
      if (resp.success) {
        this.user = resp.data
      }
    }
  }
}
