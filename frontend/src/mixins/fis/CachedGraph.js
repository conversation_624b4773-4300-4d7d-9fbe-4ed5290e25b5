import moment from 'moment'
import _ from 'lodash'
import md5 from 'js-md5'
import Dexie from 'dexie/import-wrapper-prod.mjs'

export default {
  data () {
    return {
      graphName: 'graph',
      graphData: null
    }
  },
  computed: {
    isCustomRange () {
      return this.params.range.startsWith('custom_range__')
    },
    reportUrl () {
      let prefix = `#/a/fis/report/${this.graphName}?1=1` + this.commonReportUrlSuffix()
      if (this.isCustomRange) {
        const parts = this.params.range.split('__')
        const start = moment.utc(parts[1]).format('YYYY-MM-DD')
        const end = moment.utc(parts[2]).format('YYYY-MM-DD')
        prefix += `&format=${this.graphName === 'balance' ? 'Summary' : 'Detail'}&start=${start}&end=${end}`
      }
      return prefix
    }
  },
  methods: {
    async loadWithDbCache (p) {
      if (!p || p.mode !== 'force') {
        const data = await this.hasDbCacheForParams()
        if (data) {
          return new Promise(resolve => {
            setTimeout(() => {
              this.onResponseData(data)
              if (p && p.cb) {
                p.cb({
                  success: true,
                  message: '',
                  data
                })
              }
              resolve(true)
            }, 100)
          })
        }
      }
      return false
    },
    async hasDbCacheForParams () {
      const db = await this.prepareIndexedDb()
      if (!db) {
        return null
      }
      const params = this.getParamsForDbKey()
      const key = md5(this.graphName + '_' + JSON.stringify(params))
      const data = await db.cards.get(key)
      if (!data || !data.when) {
        await this.deleteOutdatedDbCache(key)
        return null
      }
      const yesterday = moment.utc().subtract(1, 'd').startOf('day').add(10, 'h')
      const when = moment(data.when)
      if (when.isBefore(yesterday)) {
        await this.deleteOutdatedDbCache(key)
        return null
      }
      const min = moment.utc().startOf('day').add(10, 'h')
      if (min.isBefore() && when.isBefore(min)) {
        await this.deleteOutdatedDbCache(key)
        return null
      }
      return data.data
    },
    async deleteOutdatedDbCache (key) {
      const db = await this.prepareIndexedDb()
      if (!db) {
        return 0
      }
      const where = {
        graph: this.graphName
      }
      if (key) {
        where.key = key
      }
      return db.cards.where(where).delete()
    },
    saveCache (data) {
      this.graphData = data
      this.$store.commit('Fis/updateGraph', {
        name: this.graphName,
        value: data
      })
    },
    getParamsForDbKey () {
      const all = _.cloneDeep(this.params)
      delete all._t
      return all
    },
    async saveDbCache (data) {
      if (this.params) {
        const db = await this.prepareIndexedDb()
        if (db) {
          const params = this.getParamsForDbKey()
          const when = moment.utc().format()
          const graph = this.graphName
          const key = md5(graph + '_' + JSON.stringify(params))
          const saveData = {
            key,
            graph,
            when,
            params,
            data
          }

          try {
            await db.cards.put(saveData, key)
            console.log(`Saved the cache for ${graph} with key ${key}`)
          } catch (e) {
            console.log(`DB save error: ${e}`)
          }
        }
      }
    },
    async prepareIndexedDb () {
      const app = window.mainApp
      return new Promise((resolve) => {
        if (app.fisDashDb !== undefined) {
          return resolve(app.fisDashDb)
        }

        try {
          app.fisDashDb = new Dexie('fis_dashboard')
          app.fisDashDb.version(2).stores({
            cards: 'key, graph'
          })
          return resolve(app.fisDashDb)
        } catch (e) {
          console.log('Failed to open db', e)
          app.fisDashDb = null
          return resolve(null)
        }
      })
    },
    onResponseData (data) {
      this.saveCache(data)
    },
    commonReportUrlSuffix () {
      let filters = {}
      _.forEach(this.params, (v, k) => {
        if ([
          'currency', 'bank', 'type', 'client'
        ].includes(k) && v && v !== 'All' && v !== 'All (roll-up)' && v !== 'USD') {
          filters[k] = v
        }
      })
      if (Object.keys(filters).length) {
        filters = JSON.stringify(filters)
        return `&initialFilters=${encodeURIComponent(filters)}`
      }
      return ''
    }
  },
  mounted () {
    if (this.$store.state.Fis.graphs[this.graphName]) {
      this.onResponseData(this.$store.state.Fis.graphs[this.graphName])
    }
  }
}
