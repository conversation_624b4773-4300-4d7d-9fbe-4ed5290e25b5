import DateRangeFilter from '../../components/DateRangeFilter'
import _ from 'lodash'

export default {
  components: {
    DateRangeFilter
  },
  methods: {
    rangeFilterChanged (drf) {
      this.filter.range = drf.range
      if (drf.range === 'custom_range' || drf.range.indexOf('custom_range') !== -1) {
        this.filter.range = 'custom_range' + `__${drf.start}__${drf.end}`
        _.forEach(this.filters.ranges, (item, index) => {
          if (item.value === 'custom_range' || item.value.indexOf('custom_range') !== -1) {
            this.filters.ranges[index].value = this.filter.range
          }
        })
      }
      // this.reload()
    }
  }
}
