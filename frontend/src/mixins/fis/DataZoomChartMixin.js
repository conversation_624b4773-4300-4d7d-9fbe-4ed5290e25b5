import moment from 'moment'
import Card from '../../pages/fis/dashboard/card'
import _ from 'lodash'
import echarts from 'echarts'
import { request } from '../../common'
import CachedGraph from './CachedGraph'
import $ from 'jquery'

export default {
  mixins: [
    CachedGraph
  ],
  components: {
    Card
  },
  props: {
    params: {
      type: Object
    }
  },
  data () {
    return {
      totals: [],
      data: [],
      axis: [],
      requestUrl: `/admin/fis/dashboard/load/data`,
      color: '#7BCA0C',
      handleColor: '#4d9a06',
      gradientColor: '#F6FBF0',
      startIndex: null,
      endIndex: null,
      initialLabel: null,
      initialRange: [],
      negativeAmount: false,
      chartSelector: '.chart-container',
      reportName: null,
      reportUrlBase: '#/a/fis/report/',
      start: null,
      end: null,
      chart: null
    }
  },
  computed: {
    seriesData () {
      return this.data.map(v => v.amount)
    },
    items () {
      if (this.startIndex === null || this.endIndex === null) {
        return []
      }
      if (this.data && Array.isArray(this.data)) {
        return this.data.slice(this.startIndex, this.endIndex + 1)
      } else if (this.data) {
        // Data returned as an object, but should be array
        const newArray = Object.keys(this.data).map(function (i) {
          return this.data[i]
        })
        return newArray.slice(this.startIndex, this.endIndex + 1)
      }
    },
    total () {
      if (!this.isCustomRange && this.initialLabel && this.totals) {
        return this.totals.count
      }
      const totalValue = this.items.reduce((sum, item) => sum + item.count, 0)
      return totalValue
    },
    amount () {
      if (!this.isCustomRange && this.initialLabel && this.totals) {
        return this.totals.amount
      }
      const totalAmount = this.items.reduce((sum, item) => sum + item.amount, 0)
      return totalAmount
    },
    avg () {
      if (!this.total && this.totals) {
        return this.totals.amount / this.totals.count
      } else if (this.amount && this.total) {
        return this.amount / this.total
      } else {
        return 0
      }
    },
    pcount () {
      if ((!this.isCustomRange && this.initialLabel && this.totals) || this.totals) {
        return this.totals.pcount
      } else if (this.data && this.data.pcount) {
        return this.data.pcount
      }
      const totalPanCount = this.items.reduce((sum, item) => sum + item.pcount, 0)
      return totalPanCount
    },
    pans () {
      if (this.items.pans) {
        if (!this.isCustomRange && this.initialLabel && this.items.pans) {
          return this.items.length ? this.items[this.items.length - 1].pans.length : 0
        }
        const all = []
        for (const item of this.items) {
          if (item.pans) {
            for (const p of (item.pans || [])) {
              all.push(p)
            }
          }
        }
        return _.uniq(all).length
      } else {
        return 0
      }
    },
    startDate () {
      if (!this.data[this.startIndex]) {
        return null
      }
      return this.c.fullUTC(this.data[this.startIndex].date)
    },
    endDate () {
      if (!this.data[this.endIndex]) {
        return null
      }
      return this.c.fullUTC(this.data[this.endIndex].date)
    },
    reportUrl () {
      let start = moment(this.startDate).format('YYYY-MM-DD')
      let end = moment(this.endDate).format('YYYY-MM-DD')
      if (this.initialLabel && this.initialRange) {
        start = this.initialRange[0]
        end = this.initialRange[1]
      }
      return `${this.reportUrlBase}${this.reportName !== null ? this.reportName : this.graphName}?format=Detail&start=${start || ''}&end=${end || ''}` + this.commonReportUrlSuffix()
    }
  },
  methods: {
    async reload (p) {
      if (await this.loadWithDbCache(p)) {
        return
      }
      const resp = await request(this.requestUrl, 'get', this.params)
      if (p && p.cb) {
        p.cb(resp)
      }
      if (resp.success) {
        this.onResponseData(resp.data)
        await this.saveDbCache(resp.data)
      }
    },
    onResponseData (data) {
      this.saveCache(data)

      this.initialLabel = data.initialLabel
      this.initialRange = data.initialRange
      this.totals = data.totals
      this.data = data.data
      this.axis = data.axis
      this.start = data.start
      this.end = data.end

      if (this.onResponseDataCallback) {
        this.onResponseDataCallback()
      }

      this.$nextTick(() => {
        this.initChart()
      })
    },
    resize () {
      this.chart.resize()
    },
    initChart () {
      this.initLineChart()
    },
    initLineChart () {
      const length = this.axis.length
      this.startIndex = 0
      this.endIndex = length - 1

      let startValue = null
      let endValue = this.axis[this.endIndex] || null

      if (this.isCustomRange) {
        let endIndex = _.findIndex(this.data, { date: this.initialRange[1] + 'T00:00:00+00:00' })
        if (endIndex < 0) {
          endIndex = _.findIndex(this.data, { date: this.end })
        }
        if (endIndex >= 0) {
          this.endIndex = endIndex
          endValue = this.axis[this.endIndex] || null
        }
        let startIndex = _.findIndex(this.data, { date: this.initialRange[0] + 'T00:00:00+00:00' })
        if (startIndex < 0) {
          startIndex = _.findIndex(this.data, { date: this.start })
        }
        if (startIndex >= 0) {
          this.startIndex = startIndex
          startValue = this.axis[this.startIndex] || null
        }
      }

      if (!startValue) {
        // TODO: The ranges are changing to be smaller windows
        // so this is no longer necessary
        let maxSpan = 7
        if (this.params.range === 'Monthly') {
          maxSpan = 30
        } else if (this.params.range === 'Quarterly') {
          maxSpan = 90
        } else if (this.params.range.indexOf('custom_range') !== false) {
          maxSpan = this.data.length
        }

        if (length >= maxSpan) {
          startValue = this.axis[length - maxSpan]
          this.startIndex = length - maxSpan
        } else if (length) {
          startValue = this.axis[0]
        }
      }

      const color = this.color
      const chart = echarts.init(this.$el.querySelector(this.chartSelector), 'fis')
      this.chart = chart
      const options = _.defaultsDeep({
        grid: {
          left: 60,
          top: 10,
          right: 20,
          bottom: 70
        },
        xAxis: {
          type: 'category',
          data: this.axis,
          boundaryGap: false,
          axisPointer: {
            show: true,
            lineStyle: {
              type: 'dash'
            },
            label: {
              show: false
            }
          },
          axisLabel: {
            color: '#999',
            fontSize: 10,
            formatter: v => {
              if (v >= 1000) {
                return (v / 1000) + 'k'
              }
              return v
            }
          },
          axisLine: {
            lineStyle: {
              color: '#e6e6e6'
            }
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#e6e6e6'
            }
          }
        },
        yAxis: {
          scale: true,
          type: 'value',
          axisLabel: {
            color: '#999',
            fontSize: 10,
            formatter: v => {
              if (v >= 1000) {
                return '$' + (v / 1000) + 'k'
              }
              return '$' + v
            }
          },
          axisLine: {
            lineStyle: {
              color: '#e6e6e6'
            }
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#e6e6e6'
            }
          }
        },
        dataZoom: [
          {
            show: true,
            realtime: true,
            rangeMode: 'value',
            startValue,
            endValue,
            fillerColor: color,
            handleColor: this.handleColor,
            minValueSpan: 1
          }
        ],
        series: [
          {
            type: 'line',
            smooth: false,
            symbol: 'emptyCircle',
            itemStyle: {
              color
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color
              }, {
                offset: 1,
                color: this.gradientColor
              }])
            },
            data: this.seriesData
          }
        ],
        tooltip: {
          enterable: true,
          formatter: (params) => {
            if (!params && !params.length) {
              return ''
            }
            const d = this.data[params[0].dataIndex]
            if (!d) {
              return ''
            }
            const n = this.negativeAmount
            return `<div class="mb-5"><div class="chart-tooltip-circle" style="background: ${color}"></div> ${moment.utc(this.c.fullUTC(d.date)).format('DD MMM YYYY')}</div>
              <div class="field-row"><div class="field-label">PANs:</div><div class="field-value">${this.$options.filters.number(d.pcount)}</div></div>
              <div class="field-row boldest"><div class="field-label">Amount:</div><div class="field-value ${n ? 'red' : ''}">${n ? '(' : ''}${this.c.moneyFormat(d.amount, this.params.currency, false, false)}${n ? ')' : ''}</div></div>
              <div class="field-row"><div class="field-label">Count:</div><div class="field-value">${this.$options.filters.number(d.count)}</div></div>
              <div class="field-row"><div class="field-label">Avg:</div><div class="field-value ${n ? 'red' : ''}">${n ? '(' : ''}${d.count > 0 ? this.c.moneyFormat(d.avg, this.params.currency, false, false) : 0}${n ? ')' : ''}</div></div>`
          }
        }
      }, this.cc.fis.chart)
      chart.clear()
      chart.setOption(options)
      chart.resize()
      chart.on('datazoom', p => {
        if (p.startValue) {
          return
        }
        this.initialLabel = null
        this.startIndex = Math.round(p.start * 0.01 * (this.data.length - 1))
        this.endIndex = Math.round(p.end * 0.01 * (this.data.length - 1))
        chart.dispatchAction({
          type: 'showTip',
          dataIndex: this.startIndex,
          seriesIndex: 0
        })

        if (this.onDataZoom) {
          this.onDataZoom()
        }
      })

      setTimeout(() => {
        chart.resize()
      }, 200)
      $(window).on('resize', this.resize)
    }
  }
}
