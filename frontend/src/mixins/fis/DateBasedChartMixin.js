import { request } from '../../common'
import $ from 'jquery'
import moment from 'moment'
import CachedGraph from './CachedGraph'

export default {
  mixins: [
    CachedGraph
  ],
  data () {
    return {
      date: null,
      dates: [],
      offset: null,
      width: 350,
      cellWidth: 50,
      canScrollLeft: true,
      canScrollRight: true,
      axis: [],
      oldRange: null,
      requestUrl: ``,
      requestCb: null
    }
  },
  computed: {
    datesWidth () {
      return Math.max(this.dates.length * this.cellWidth, this.width)
    },
    axisCurrent () {
      if (!this.date) {
        return ''
      }
      return moment.utc(this.date).format('DD MMM')
    }
  },
  watch: {
    'params.range' () {
      this.date = null
      this.offset = null
    }
  },
  methods: {
    async reload (cb) {
      return new Promise(resolve => {
        this.$nextTick(async () => {
          await this.reloadNow(cb)
          resolve()
        })
      })
    },
    async reloadNow (p) {
      if (await this.loadWithDbCache(p)) {
        return
      }
      const resp = await request(this.requestUrl, 'get', {
        date: this.date,
        offset: this.offset,
        ...this.params
      })
      if (p && p.cb) {
        p.cb(resp)
      }
      if (resp.success) {
        this.onResponseData(resp.data)
        await this.saveDbCache(resp.data)
      }
    },
    onResponseData (data) {
      this.saveCache(data)

      if (this.requestCb) {
        this.requestCb(data)
      }
      this.axis = data.axis
      this.dates = data.dates
      if (
        this.dates.length &&
        (this.date === null || !this.dates.includes(this.date) || this.params.range !== this.oldRange)
      ) {
        this.date = this.dates[this.dates.length - 1]
        this.oldRange = this.params.range
        this.$nextTick(() => {
          setTimeout(() => {
            const el = this.$refs.dates
            if (!el) {
              return
            }
            el.scrollLeft = el.scrollWidth
            this.updateArrows()
            $(el).on('scroll', this.updateArrows)
          }, 100)
        })
      }
      this.$nextTick(() => {
        this.initChart()
      })
    },
    scrollLeft () {
      const el = this.$refs.dates
      $(el).animate({
        scrollLeft: el.scrollLeft - this.width
      }, 300)
    },
    scrollRight () {
      const el = this.$refs.dates
      $(el).animate({
        scrollLeft: el.scrollLeft + this.width
      }, 300)
    },
    selectDate (date, evt) {
      if (this.date === date) {
        return
      }
      if (evt) {
        let $t = $(evt.target)
        if (!$t.is('.btn-date')) {
          $t = $t.parents('.btn-date').first()
        }
        this.offset = Math.floor(($t[0].offsetLeft + 8 - this.$refs.dates.scrollLeft) / this.cellWidth)
      }
      this.date = date
      this.params.direct = false
      this.params.offset = this.offset
      this.$refs.card.reload()
    },
    updateArrows () {
      const el = this.$refs.dates
      this.canScrollLeft = el.scrollLeft > 0
      this.canScrollRight = el.scrollLeft < el.scrollWidth - el.clientWidth
    }
  },
  mounted () {
    $(window).on('resize', () => {
      this.cellWidth = this.$el.clientWidth / 8
      this.width = this.cellWidth * 7
    }).trigger('resize')
  }
}
