import _ from 'lodash'

export default {
  data () {
    return {
      reportProxyField: 'PAN Proxy Number',
      reportActions: {
        'Show Balances Details': 'portfolio',
        'Show Monetary Details': 'monetary',
        'Show Authorization Details': 'auth',
        'Show Non-Monetary Details': 'non-monetary',
        'Show Disputes': 'dispute'
      }
    }
  },
  methods: {
    takeReportAction (action, row) {
      const proxy = row[this.reportProxyField]
      let filters = ''
      if (this.filters && this.filters.banks) {
        filters = JSON.stringify(this.filter)
      } else {
        const onlyFilters = this.filters.filter(f => {
          return [
            'currency', 'bank', 'type',
            'client', 'date_range'
          ].includes(f.field)
        })
        filters = JSON.stringify(onlyFilters)
      }
      window.open(`#/a/fis/report/${action}?cardNumberProxy=${proxy}&initialFilters=${encodeURIComponent(filters)}`, '_blank')
    },
    fromDashboardEx (query) {
      if (query.initialFilters) {
        const initials = JSON.parse(query.initialFilters)
        if (_.isArray(initials)) {
          _.forEach(initials, v => {
            this.filters.push(v)
          })
        } else if (initials && _.isObject(initials)) {
          _.forEach(initials, (v, k) => {
            if (![
              'currency', 'bank', 'type', 'client'
            ].includes(k)) {
              return
            }
            if (!v || [
              'All', 'All (roll-up)', 'USD'
            ].includes(v)) {
              return
            }
            this.filters.push({
              field: k,
              value: v,
              predicate: '='
            })
          })
        }
      }
      if (query && query['from-chart'] === 'card_spend') {
        for (let i = this.filters.length - 1; i >= 0; i--) {
          const f = this.filters[i]
          if (f.field === 'filter[a.txnTypeName=]') {
            this.filters.splice(i, 1)
          }
        }
        for (const n of [
          'Purchase Approved Settled',
          'Purchase Approved Settled NoAuth',
          'ATM Approved Settled',
          'ATM Approved Settled NoAuth'
        ]) {
          const filter = {}
          filter.field = 'filter[a.txnTypeName=]'
          filter.value = n
          filter.predicate = '='
          this.filters.push(filter)
        }
      }
      if (query && query['from-chart'] === 'ach') {
        for (const n of [
          '1921', '1922', '1923',
          '1401', '1920'
        ]) {
          const filter = {}
          filter.field = 'filter[a.actualRequestCode=]'
          filter.value = n
          filter.predicate = '='
          this.filters.push(filter)
        }
      }
      if (query && query['from-chart'] === 'people') {
        for (const n of [
          '94106',
          '60309'
        ]) {
          const filter = {}
          filter.field = 'filter[a.actualRequestCode=]'
          filter.value = n
          filter.predicate = '='
          this.filters.push(filter)
        }
      }
      if (query.txnTypeName) {
        let filter = {}
        filter.field = 'filter[a.txnTypeName=]'
        filter.value = query.txnTypeName
        filter.predicate = '='
        this.filters.push(filter)
      }
    }
  }
}
