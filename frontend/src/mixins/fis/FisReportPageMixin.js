import { request, EventHandlerMixin } from '../../common'
import moment from 'moment'
import _ from 'lodash'
import FreezeColumnMixin from '../FreezeColumnMixin'

export default {
  mixins: [
    FreezeColumnMixin,
    EventHandlerMixin('reload-fis-list-filters', 'initTopFilters')
  ],
  data () {
    return {
      filter: {
        bank: 'All (roll-up)',
        client: 'All (roll-up)',
        range: 'Daily',
        type: '',
        currency: 'USD',
        format: 'Summary',
        balance: 'All',
        changed: true,
        startDate: null,
        endDate: null
      },
      filters: {
        banks: [],
        clients: [],
        ranges: [],
        types: [],
        currencies: [],
        formats: [],
        balances: [],
        endDate: null
      },
      tradFilter: null,
      reportKey: ''
    }
  },
  watch: {
    'filter.range': {
      handler () {
        if (!this.filter.endDate) {
          return
        }
        const map = {
          Weekly: 'weeks',
          Monthly: 'months',
          Quarterly: 'quarters',
          Yearly: 'years'
        }
        if (map[this.filter.range]) {
          this.filter.startDate = moment.utc(this.filter.endDate)
            .subtract(1, map[this.filter.range])
            .add(1, 'd')
            .format()
        }
      }
    }
  },
  computed: {
    filteredVisibleColumns () {
      const columns = this.visibleColumns || this.columns
      if (this.filter.format === 'Summary') {
        return columns.filter(v => !(['PAN Proxy Number', 'BIN', 'Type'].includes(v.field)))
      }
      return columns
    }
  },
  methods: {
    cellClass (value) {
      if (value > 0) {
        return 'text-positive'
      }
      if (value < 0) {
        return 'text-negative'
      }
      return ''
    },
    getQueryParams () {
      if (this.filtersUrl) {
        const data = this.$refs.filtersDialog.getFilters()
        data.keyword = this.keyword
        return data
      }

      const params = _.assignIn({}, this.filter)

      if (this.$refs && this.$refs.dateRangeFilter) {
        _.assignIn(params, this.$refs.dateRangeFilter.params())
      }

      if (this.tradFilter) {
        params.filter = this.tradFilter
      }

      return params
    },
    async init () {
      if (this.filterOptions) {
        for (const fo of this.filterOptions) {
          fo.predicate = '='
        }
      }
      await this.initTopFilters()
      this.reload()
    },
    async initTopFilters () {
      if (!this.reportKey) {
        return
      }
      this.loading = true
      const resp = await request(`/admin/fis/report/${this.reportKey}/filters`)
      this.loading = false
      if (resp.success) {
        this.filters = resp.data
        this.filters.ranges.push({
          label: 'Custom Range',
          value: 'custom_range'
        })
        if (!this.filter.endDate) {
          this.filter.endDate = resp.data.latestDate
        }
      }
    },
    fromDashboard () {
      const query = this.$route.query
      if (_.isEmpty(query)) {
        return
      }
      if (query.format) {
        this.filter.format = query.format
      }
      if (query.start) {
        this.filter.range = 'custom_range'
        this.filter.startDate = query.start + 'T00:00:00+00:00'
      }
      if (query.end) {
        this.filter.endDate = query.end + 'T00:00:00+00:00'
      }

      if (query.initialFilters) {
        const initials = JSON.parse(query.initialFilters)
        if (_.isArray(initials)) {
          _.forEach(initials, v => {
            if (![
              'currency', 'bank', 'type', 'client'
            ].includes(v.field) || !v.value) {
              return
            }
            this.filter[v.field] = v.value
          })
        } else if (initials && _.isObject(initials)) {
          _.forEach(initials, (v, k) => {
            if (!['bank', 'client', 'range', 'type', 'currency', 'format'].includes(k)) {
              return
            }
            this.filter[k] = v
          })
        }
      }
    },
    resetFilter () {
      this.filter = {
        bank: 'All (roll-up)',
        client: 'All (roll-up)',
        range: 'Daily',
        type: '',
        currency: 'USD',
        format: 'Summary',
        balance: 'All',
        changed: true,
        startDate: null,
        endDate: null
      }
      this.reload()
    }
  },
  mounted () {
    this.fromDashboard()
  }
}
