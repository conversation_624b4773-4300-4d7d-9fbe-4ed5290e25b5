import moment from 'moment'
import Card from '../../pages/fis/dashboard/card'
import _ from 'lodash'
import echarts from 'echarts'
import { avg, request } from '../../common'
import CachedGraph from './CachedGraph'
import $ from 'jquery'
// import { data } from 'jquery'

export default {
  mixins: [
    CachedGraph
  ],
  components: {
    Card
  },
  props: {
    params: {
      type: Object
    }
  },
  data () {
    return {
      requestUrl: `/admin/fis/dashboard/ach/data`,
      mode: 'line',
      defaultMode: 'line',
      handleColor: '#DADACE',
      handleDarkColor: '#979797',
      graphName: 'ach',
      reportName: 'monetary',
      reportUrlBase: '#/a/fis/report/',
      series: {
        'ACH IN': {
          name: 'ACH IN',
          amount: 0,
          count: 0,
          pcount: 0,
          avg: 0,
          percent: 0,
          color: '#35BC3F',
          subColor: '#9CE1A2'
        },
        'ACH OUT': {
          name: 'ACH OUT',
          amount: 0,
          count: 0,
          pcount: 0,
          avg: 0,
          percent: 0,
          color: '#F00523',
          subColor: '#FBBAC1',
          subColorReverse: true
        }
      },
      seriesNames: [
        'ACH IN', 'ACH OUT'
      ],
      lineData: null,
      startDate: null,
      endDate: null,
      active: null,
      withTotalLine: false,
      start: null,
      end: null,
      chart: null
    }
  },
  computed: {
    amount () {
      let sum = 0
      _.forEach(this.series, (v, k) => {
        sum += v.amount
      })
      return sum
    },
    pan () {
      let sum = 0
      _.forEach(this.series, (v, k) => {
        sum += v.pan
      })
      return sum
    },
    pcount () {
      let sum = 0
      _.forEach(this.series, (v, k) => {
        sum += v.pcount
      })
      return sum
    },
    count () {
      let sum = 0
      _.forEach(this.series, (v, k) => {
        sum += v.count
      })
      return sum
    },
    names () {
      let names = []
      _.forEach(this.series, (v, k) => {
        names.push(v.name)
      })
      return names
    },
    percentBoxStyle () {
      return {
        width: `${100 / this.seriesNames.length}%`
      }
    },
    reportUrl () {
      let path = this.graphName || this.reportName || 'monetary'
      let url = `#/a/fis/report/${path}?from-chart=` + this.graphName + this.commonReportUrlSuffix()
      if (this.startDate) {
        url += '&start=' + moment.utc(this.startDate).format('YYYY-MM-DD')
      }
      if (this.endDate) {
        url += '&end=' + moment.utc(this.endDate).format('YYYY-MM-DD')
      }
      return url
    },
    amountFormat () {
      return this.$options.filters.moneyFormat(this.amount, 'USD', false, true, this.amount < 100000000 ? (this.amount < 10000000 ? 2 : 1) : 0)
    },
    allSeries () {
      if (!this.withTotalLine || this.mode !== 'line') {
        return this.series
      }
      const all = _.cloneDeep(this.series)
      all[this.withTotalLine] = {
        name: this.withTotalLine,
        color: '#FBB72B'
      }
      return all
    }
  },
  methods: {
    async reload (p) {
      if (await this.loadWithDbCache(p)) {
        return
      }
      const resp = await request(this.requestUrl, 'get', this.params)
      if (p && p.cb) {
        p.cb(resp)
      }
      if (resp.success) {
        this.onResponseData(resp.data)
        await this.saveDbCache(resp.data)
      }
    },
    onResponseData (data) {
      this.end = data.end
      this.start = data.start
      this.saveCache(data)
      this.lineData = data
      this.setMode(this.mode, true)
    },
    setMode (mode, initLine = false) {
      this.mode = mode
      if (mode === 'line') {
        this.$nextTick(() => {
          this.initLineChart()

          if (this.defaultMode === 'bar') {
            this.defaultMode = null
            this.setMode('bar')
          }
        })
      } else {
        this.$nextTick(() => {
          if (initLine) {
            this.initLineChart()
          }
          this.initChart()
        })
      }
    },
    resize () {
      this.chart.resize()
    },
    initChart () {
      const counts = []
      _.forEach(this.series, d => {
        counts.push({
          name: d.name,
          value: Math.abs(d.amount),
          itemStyle: {
            color: d.color,
            shadowBlur: 5,
            shadowOffsetY: 2,
            shadowColor: '#999'
          }
        })
      })

      const chart = echarts.init(this.$el.querySelector('.bar-chart-container'), 'fis')
      this.chart = chart
      const options = _.defaultsDeep({
        grid: {
          left: '10%',
          top: 5,
          right: 10,
          bottom: 10
        },
        xAxis: {
          show: false,
          data: this.names,
          axisPointer: {
            show: true,
            lineStyle: {
              type: 'dash'
            },
            label: {
              show: false
            }
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          axisLine: {
            show: false
          },
          axisLabel: {
            color: '#999',
            fontSize: 10,
            formatter: v => {
              if (v >= 100000) {
                return '$' + (v / 100000) + 'k'
              }
              return '$' + v / 100
            }
          },
          splitLine: {
            lineStyle: {
              color: '#e6e6e6'
            }
          }
        },
        series: [
          {
            type: 'bar',
            data: counts,
            barWidth: '60%',
            barMinHeight: 0
          }
        ],
        tooltip: {
          // enterable: true,
          formatter: (params) => {
            if (!params && !params.length) {
              return ''
            }
            if (params[0].name === '') {
              return ''
            }
            this.active = params[0].name
            return this.buildTooltipHtml({
              rs: this.series
            })
          }
        }
      }, this.cc.fis.chart)
      chart.setOption(options)
      chart.resize()
      chart.off('mouseover')
      chart.on('mouseover', p => {
        this.active = p.name
      })
      chart.off('mouseout')
      chart.on('mouseout', () => {
        this.active = null
      })
      $(window).on('resize', this.resize)
    },
    initLineChart () {
      const ld = this.lineData
      const length = ld.axis.length
      let initializing = false
      if (ld.startIndex === undefined) {
        initializing = true
        ld.startIndex = 0
      }
      if (ld.endIndex === undefined) {
        ld.endIndex = length - 1
      }

      let startValue = initializing ? null : (ld.axis[ld.startIndex] || null)
      let endValue = ld.axis[ld.endIndex] || null

      if (this.isCustomRange) {
        const endIndex = _.findIndex(ld.data, { date: ld.initialRange[1] + 'T00:00:00+00:00' })
        if (endIndex >= 0) {
          ld.endIndex = endIndex
          endValue = ld.axis[ld.endIndex] || null
        }
        const startIndex = _.findIndex(ld.data, { date: ld.initialRange[0] + 'T00:00:00+00:00' })
        if (startIndex >= 0) {
          ld.startIndex = startIndex
          startValue = ld.axis[ld.startIndex] || null
        }
      }

      if (!startValue) {
        let maxSpan = 7
        if (this.params.range === 'Monthly') {
          maxSpan = 30
        } else if (this.params.range === 'Quarterly') {
          maxSpan = 90
        } else if (this.params.range.indexOf('custom_range') !== false) {
          maxSpan = ld.data.length
        }

        if (length >= maxSpan) {
          startValue = ld.axis[length - maxSpan]
          ld.startIndex = length - maxSpan
        } else if (length) {
          startValue = ld.axis[0]
        }
      }
      this.updateLineDateRange()

      const series = []
      _.forEach(this.series, (p, name) => {
        let colors
        if (p.subColorReverse) {
          colors = [{
            offset: 0,
            color: '#FFFFFF'
          }, {
            offset: 1,
            color: p.subColor
          }]
        } else {
          colors = [{
            offset: 0,
            color: p.subColor
          }, {
            offset: 1,
            color: '#FFFFFF'
          }]
        }

        series.push({
          type: 'line',
          smooth: false,
          symbol: 'none',
          itemStyle: {
            color: p.color
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, colors)
          },
          data: ld.data.map(l => {
            let vv = 0
            _.forEach(l.rs, v => {
              if (v.name === name) {
                vv = v.amount
                return false
              }
            })
            return vv
          })
        })
      })

      if (this.withTotalLine) {
        series.push({
          type: 'line',
          smooth: false,
          symbol: 'none',
          itemStyle: {
            color: '#FBB72B'
          },
          lineStyle: {
            type: 'dotted'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: '#fcce8c'
            }, {
              offset: 1,
              color: '#FFFFFF'
            }])
          },
          data: ld.data.map(l => {
            let vv = 0
            _.forEach(l.rs, v => {
              vv += v.amount
            })
            return vv
          })
        })
      }

      const chart = echarts.init(this.$el.querySelector('.line-chart-container'), 'fis')
      this.chart = chart
      const options = _.defaultsDeep({
        grid: {
          left: 50,
          top: 0,
          right: 15,
          bottom: 65
        },
        xAxis: {
          type: 'category',
          data: ld.axis,
          boundaryGap: false,
          axisPointer: {
            show: true,
            lineStyle: {
              type: 'dash'
            },
            label: {
              show: false
            }
          },
          axisLabel: {
            color: '#999',
            fontSize: 10
          },
          axisLine: {
            lineStyle: {
              color: '#e6e6e6'
            }
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#e6e6e6'
            }
          }
        },
        yAxis: {
          scale: true,
          type: 'value',
          axisLabel: {
            color: '#999',
            fontSize: 10,
            formatter: v => {
              if (v >= 100000) {
                return '$' + (v / 100000) + 'k'
              }
              return '$' + v / 100
            }
          },
          axisLine: {
            lineStyle: {
              color: '#e6e6e6'
            }
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#e6e6e6'
            }
          }
        },
        dataZoom: [
          {
            show: true,
            realtime: true,
            rangeMode: 'value',
            startValue,
            endValue,
            fillerColor: this.handleColor,
            handleColor: this.handleDarkColor,
            minValueSpan: 1
          }
        ],
        series,
        tooltip: {
          // enterable: true,
          formatter: (params) => {
            if (!params && !params.length) {
              return ''
            }
            const d = ld.data[params[0].dataIndex]
            if (!d) {
              return ''
            }
            return this.buildTooltipHtml(d)
          }
        }
      }, this.cc.fis.chart)
      chart.getDom().echart = chart
      chart.clear()
      chart.setOption(options)
      chart.resize()
      chart.off('datazoom')
      chart.on('datazoom', p => {
        if (p.startValue) {
          return
        }
        ld.startIndex = Math.round(p.start * 0.01 * (ld.data.length - 1))
        ld.endIndex = Math.round(p.end * 0.01 * (ld.data.length - 1))
        this.updateLineDateRange()
      })

      setTimeout(() => {
        chart.resize()
      }, 200)
      $(window).on('resize', this.resize)
    },
    updateLineDateRange () {
      const ld = this.lineData
      if (!ld) {
        return
      }
      this.startDate = this.endDate = null
      if (ld.data[ld.startIndex]) {
        this.startDate = this.c.fullUTC(ld.data[ld.startIndex].date)
      }
      if (ld.data[ld.endIndex]) {
        this.endDate = this.c.fullUTC(ld.data[ld.endIndex].date)
      }

      _.forEach(this.series, s => {
        s.amount = s.count = s.avg = s.percent = s.pcount = 0
      })

      if (ld.data[ld.startIndex] && ld.data[ld.endIndex]) {
        for (let i = ld.startIndex; i <= ld.endIndex; i++) {
          for (const d of ld.data[i].rs) {
            const s = _.find(this.series, v => v.name === d.name)
            if (!s) {
              continue
            }
            s.amount += d.amount
            s.count += d.count
            s.pcount += d.pcount
          }
          let total = 0
          _.forEach(this.series, s => {
            total += Math.abs(s.amount)
          })
          _.forEach(this.series, s => {
            s.avg = this.c.avg(s.amount, s.count)
            s.percent = this.c.avg(Math.abs(s.amount), total)
          })
        }
      }
    },
    buildTooltipHtml (d) {
      const detail = _.cloneDeep(this.series)
      let html = ``
      if (d.date) {
        html += `<div class="chart-tooltip-title">${this.$options.filters.date(this.c.fullUTC(d.date), 'DD MMM YYYY')}</div>`
      }
      html += `<table class="table chart-tooltip-table chart-tooltip-table-dense">
        <thead>
        <tr>
          <th></th>`
      _.forEach(this.series, (v, k) => {
        html += `<th class="${v.name === this.active ? 'bold' : ''}"><div class="chart-tooltip-circle" style="background: ${v.color}"></div> ${v.name}</th>`
      })
      html += `</tr>
        </thead>
        <tbody>`

      let amount = 0
      let total = 0
      _.forEach(d.rs, (v, k) => {
        amount += Math.abs(parseInt(v.amount))
        total += parseInt(v.amount)
      })

      _.forEach(detail, (p, name) => {
        p.count = p.amount = p.avg = p.percent = p.pcount = 0
        _.forEach(d.rs, (v, k) => {
          if (v.name === name) {
            p.count = parseInt(v.count)
            p.amount = parseInt(v.amount)
            p.pcount = v.pcount ? parseInt(v.pcount) : 0
            p.avg = avg(p.amount, p.count)
            p.percent = avg(Math.abs(p.amount), amount)
            return false
          }
        })
      })

      html += `
        <tr><th class="text-right">#</th>${_.map(detail, d => `<td class="${d.name === this.active ? 'bold' : ''}">${d.count}</td>`).join('')}</tr>
        <tr><th class="text-right">$</th>${_.map(detail, d => `<td class="${d.name === this.active ? 'bold' : ''}">${d.amount >= 0 ? this.c.moneyFormat(d.amount) : `<span class="red">(${this.c.moneyFormat(-d.amount)})</span>`}</td>`).join('')}</tr>
        <tr><th class="text-right">PANS</th>${_.map(detail, d => `<td class="${d.name === this.active ? 'bold' : ''}">${this.$options.filters.number(d.pcount)}</td>`).join('')}</tr>
        <tr><th class="text-right">AVG</th>${_.map(detail, d => `<td class="${d.name === this.active ? 'bold' : ''}">${d.avg >= 0 ? this.c.moneyFormat(d.avg) : `<span class="red">(${this.c.moneyFormat(-d.avg)})</span>`}</td>`).join('')}</tr>
        <tr><th class="text-right">%</th>${_.map(detail, d => `<td class="${d.name === this.active ? 'bold' : ''}">${this.$options.filters.percent(d.percent, 1)}</td>`).join('')}</tr>
        </tbody></table>
        <div class="chart-tooltip-title mt-8 font-18 black">${this.c.moneyFormat(total)}</div>
        `

      return html
    }
  },
  mounted () {
    _.forEach(this.series, (v, k) => {
      if (this.seriesNames.indexOf(k) < 0) {
        delete this.series[k]
      }
    })
    this.$el.addEventListener('mouseleave', () => {
      this.active = null
    })
  }
}
