export default {
  methods: {
    statusClass (status) {
      return {
        'Initial': 'dark',
        'Onboarded': 'blue',
        'Pending': 'blue',
        'On Hold': 'warning',
        'Active': 'positive',
        'Approved': 'positive',
        'Inactive': 'purple',
        'Denied': 'negative',
        'Archived': 'gray',
        'Closed': 'negative',
        'KY<PERSON> Failed (OFAC)': 'orange',
        'KYC (Scan Pending)': 'purple',
        'KYC Failed (Scan)': 'pansy',
        'KY<PERSON> Failed (OFAC & Scan)': 'magenta'
      }[status] || status
    }
  }
}
