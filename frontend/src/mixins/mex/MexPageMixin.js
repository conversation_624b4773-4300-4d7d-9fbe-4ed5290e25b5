import PageMixin from '../PageMixin'
import { bindState, bindStateReadonly } from '../../common'
import DateRangeFilter from '../../components/DateRangeFilter'

export default {
  mixins: [
    PageMixin
  ],
  components: {
    DateRangeFilter
  },
  data () {
    return {
      dateRanges: [
        {
          label: 'Weekly',
          value: 'week'
        },
        {
          label: 'Monthly',
          value: 'month'
        },
        {
          label: 'Quarterly',
          value: 'quarter'
        },
        {
          label: 'Yearly',
          value: 'year'
        },
        {
          label: 'All',
          value: 'all'
        },
        {
          label: 'Custom Range',
          value: 'custom_range'
        }
      ],
      dateRange: 'all'
    }
  },
  computed: {
    ...bindStateReadonly('Config', 'cardPrograms'),
    ...bindState('Config', 'selectedCp'),
    user () {
      return this.$store.state.User
    },
    masterAdmin () {
      const user = this.user
      return user && user.currentRole === 'MasterAdmin'
    },
    agentAdmin () {
      const user = this.user
      return user && user.currentRole === 'TransferMex Admin'
    },
    agentAgent () {
      const user = this.user
      return user && user.currentRole === 'TransferMex Agent'
    },
    employerAdmin () {
      const user = this.user
      return user && user.currentRole === 'TransferMex Employer'
    },
    masterAgentAdmin () {
      return this.masterAdmin || this.agentAdmin
    },
    canLoginAsMembers () {
      if (this.masterAdmin) {
        return true
      }
      if (!this.agentAdmin && !this.agentAgent) {
        return false
      }
      const user = this.user
      return user && user.uPermissions && user.uPermissions.includes('login_as')
    },
    canLoginAsEmployers () {
      if (this.masterAdmin) {
        return true
      }
      if (!this.agentAdmin && !this.agentAgent) {
        return false
      }
      const user = this.user
      return user && user.uPermissions && user.uPermissions.includes('login_as_employers')
    },
    textEmployer () {
      return this.user.cpKey === 'cp_mex' ? 'Employer' : 'Client'
    },
    textEmployers () {
      return this.user.cpKey === 'cp_mex' ? 'Employers' : 'Clients'
    },
    textEmployee () {
      return this.user.cpKey === 'cp_mex' ? 'Employee' : 'Member'
    },
    textEmployees () {
      return this.user.cpKey === 'cp_mex' ? 'Employees' : 'Members'
    }
  },
  methods: {
    isMasterAdmin () {
      const user = this.$store.state.User
      return user && user.currentRole === 'MasterAdmin'
    }
  }
}
