import { notify, request } from '../../common'
import _ from 'lodash'

export default {
  methods: {
    async refreshRapid () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/transfer-fundings/refresh-rapid`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        _.assignIn(this.quick, resp.data)
        notify(resp)
      }
    },
    async refreshBotm () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/transfer-fundings/refresh-botm`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        _.assignIn(this.quick, resp.data)
        notify(resp)
      }
    },
    async refreshTransfer (partner) {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/transfer-fundings/refresh-transfer`, 'post', { partner: partner })
      this.$q.loading.hide()
      if (resp.success) {
        _.assignIn(this.quick, resp.data)
        console.log(this.quick)
        notify(resp)
      }
    }
  }
}
