import StateListMixin from '../StateListMixin'
import { toSelectOptions } from '../../common'

export default {
  mixins: [
    StateListMixin
  ],
  data () {
    return {
      loadingStates: false,
      states: []
    }
  },
  computed: {
    countries () {
      const cp = this.$store.getters['Config/current']
      if (!cp) {
        return []
      }
      return toSelectOptions(cp.countries)
    },
    stateBefore () {
      if (this.loadingStates) {
        return [
          {
            icon: 'mdi-loading mdi-spin',
            handler () {}
          }
        ]
      }
      return null
    }
  },
  watch: {
    'entity.CountryId': {
      immediate: true,
      handler () {
        this.loadStatesAuto()
      }
    }
  },
  methods: {
    async loadStatesAuto () {
      this.loadingStates = true
      this.states = []
      this.states = await this.loadStates(this.entity.CountryId)
      this.loadingStates = false
    }
  }
}
