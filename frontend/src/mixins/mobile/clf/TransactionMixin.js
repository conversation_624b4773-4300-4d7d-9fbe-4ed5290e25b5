export default {
  methods: {
    transactionIcon (t) {
      if (t.status !== 'Completed') {
        return 'mdi-alert-outline'
      }
      if (t.credit) {
        return this.reverse ? 'mdi-arrow-left-circle-outline' : 'mdi-arrow-right-circle-outline'
      }
      if (t.debit) {
        return this.reverse ? 'mdi-arrow-right-circle-outline' : 'mdi-arrow-left-circle-outline'
      }
      return 'mdi-help-circle-outline'
    },
    transactionAmount (t) {
      return t.amount || t.credit || t.debit
    },
    transactionClass (t) {
      if (t.status !== 'Completed') {
        return 'text-dark'
      }
      if (t.credit) {
        return this.reverse ? 'text-negative' : 'text-positive'
      }
      if (t.debit) {
        return this.reverse ? 'text-positive' : 'text-negative'
      }
      return 'text-dark'
    }
  }
}
