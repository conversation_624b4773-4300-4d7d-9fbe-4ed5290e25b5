import echarts from 'echarts'
import { request, moneyFormat } from '../../common'
const SpendrChartMixin = {
  props: [
    'chartId',
    'title',
    'value'
  ],
  data () {
    return {
      chart: null,
      delta: 0,
      visible: false,
      series: [],
      option: null,
      positionStr: '',
      xAxis: [],
      graphSetting: [],
      totalField: ''
    }
  },
  methods: {
    async reloadData () {
      let resp = null
      if (this.chartId === 'spendrTopMerchant') {
        resp = await request(`/admin/spendr/dashboard/top-five-merchants`, 'get', this.value)
      } else if (this.chartId === 'spendrTopLocation') {
        resp = await request(`/admin/spendr/dashboard/top-five-locations`, 'get', this.value)
      }
      if (resp && resp.success) {
        // if (!this.chartSetting) {
        //   this.graphSetting = resp.data.chartSetting
        // } else {
        //   this.graphSetting = this.chartSetting
        // }
        // colorList = this.graphSetting.colorList
        // labelList = this.graphSetting.itemList
        // labelList.forEach((element, index) => {

        // })
        // this.graphSetting.fieldList.forEach((e, index) => {
        //   this.series.data = Object.values(resp.data)
        // })
        // this.series[this.graphSetting.fieldList.length].data = Object.values(resp.data.chartData[this.graphSetting.totalField])
        // this.xAxis = resp.data.key
        const colorList = ['#9c85ca', '#5190b7', '#ff9031', '#0062ff', '#4ACC3D']
        const count = resp.data.count
        const labelList = resp.data.key
        // this.option.xAxis.data = resp.data.key
        this.option.series.data = resp.data.data
        this.option.series.itemStyle = {
          barBorderRadius: [10, 10, 0, 0],
          color: function (params) {
            return colorList[params.dataIndex]
          }
        }
        this.option.series.label = {
          show: true,
          position: 'bottom',
          color: '#92929e',
          formatter: function (params) {
            // build a color map as your need.
            return labelList[params.dataIndex]
          }
        }
        this.option.tooltip = {
          trigger: 'item',
          backgroundColor: '#ffffff',
          padding: 0,
          formatter: function (params) {
            return '<div style="box-shadow: 0 5px 14px 0 rgba(68, 68, 80, 0.2);border-radius:4px;padding:30px;padding-top:16px;"><p style="text-align:center;color:' + colorList[params.dataIndex] + ';margin:0px;">' + resp.data.key[params.dataIndex] + '</p>' +
              '<div style="line-height:30px;min-width:215px;display:flex;justify-content:space-between;color:#231f20;">#of TXNs<span>Total($)</span></div>' +
              '<div style="line-height:30px;min-width:215px;display:flex;justify-content:space-between;color:#231f20;">' + count[params.dataIndex] + '<span>' + moneyFormat(params.value * 100) + '</span></div>' +
              '</div>'
          }
        }
        this.chart.setOption(this.option)
      }
    },
    initChart () {
      this.chart = echarts.init(document.getElementById(this.chartId), 'primary')
      // let that = this
      this.option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '15%',
          right: '30px',
          top: '5px',
          bottom: '8%'
        },
        xAxis:
        {
          type: 'category',
          splitLine: {
            show: false
          },
          data: []
        },
        yAxis:
        {
          type: 'value',
          splitLine: {
            show: false
          }
        },
        label: {},
        series: {
          data: [],
          type: 'bar',
          itemStyle: {}
        }
      }
    }
  }
}
export default SpendrChartMixin
