<template>
  <q-page id="user_management__user_page">
    <div class="page-header">
      <div class="title">Create Alert Job</div>
      <div class="fun-group">
        <div class="divider"></div>
        <q-btn flat
               @click="this.submitFilters"
               icon="mdi-plus"
               label="Use Current Filters"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div class="max-w-300">
          <h5>{{ quick.count | number }}</h5>
          <div class="description">Total # of users</div>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat round dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true"/>
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props" :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field" :class="`text-${col.align || 'left'}`">
            <template v-if="col.field === 'status'">
              {{ col.value | capitalize }}
            </template>
            <template v-else-if="col.field === 'username' && props.row.emailInactive">
              <span class="text-negative">{{ col.value }}</span>
            </template>
            <template v-else-if="col.field === 'lastLoginAt'">
              {{ col.value | date('L LT') }}
            </template>
            <template v-else-if="['accountBalance', 'legacyBalance'].includes(col.field)">
              <span v-if="col.value">{{ col.value | moneyFormat }}</span>
            </template>
            <template v-else-if="col.field === 'teams'">
              {{ col.value | join(', ') }}
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog" :max="1000"></BatchExportDialog>

    <template v-if="esSoloMenu">
      <CardLoadUnloadDialog></CardLoadUnloadDialog>
    </template>
  </q-page>
</template>

<script>
import { request, EventHandlerMixin, notify, redirectRoot } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import UserMenuMixin from '../../../mixins/esSolo/UserMenuMixin'
import { chartName } from '../../../const'
import eventBus from '../../../eventBus'

export default {
  mixins: [
    ListPageMixin,
    UserMenuMixin,
    EventHandlerMixin('admin_user_quick_search', 'search')
  ],
  data () {
    return {
      title: 'User Management',
      url: '/a/user-management/user',
      filtersUrl: '/admin/user_search/filters',
      downloadUrl: '/admin/user_search/export',
      timeField: 'ucCreated',
      cardProgramField: 'cardprogramselect',
      chartId: chartName.USER_CHART,
      autoReloadWhenUrlChanges: true,
      columns: [
        {
          field: 'id',
          label: 'ID',
          align: 'left'
        }, {
          field: 'username',
          label: 'Username',
          align: 'left'
        }, {
          field: 'firstName',
          label: 'First Name',
          align: 'left'
        }, {
          field: 'lastName',
          label: 'Last Name',
          align: 'left'
        }, {
          field: 'country',
          label: 'Country',
          align: 'left'
        }, {
          field: 'lastLoginAt',
          label: 'Last Login Time',
          align: 'left',
          hidden: true
        }, {
          field: 'legacyBalance',
          label: 'Legacy Balance',
          align: 'right'
        }, {
          field: 'accountBalance',
          label: 'Account Balance',
          align: 'right'
        }, {
          field: 'teams',
          label: 'Account Type',
          align: 'left'
        }, {
          field: 'status',
          label: 'Status',
          align: 'left'
        }, {
          field: 'legacy',
          label: 'Legacy User',
          align: 'left'
        }
      ],
      quick: {
        count: 0
      },
      filterOptions: [
        {
          value: 'useridx',
          label: 'User ID'
        }, {
          value: 'firstname',
          label: 'First Name'
        }, {
          value: 'lastname',
          label: 'Last Name'
        }, {
          value: 'email',
          label: 'Email'
        }, {
          value: 'address',
          label: 'Address'
        }, {
          value: 'phone',
          label: 'Phone'
        }, {
          value: 'accountnumber',
          label: 'Card Account Number'
        }, {
          value: 'iddocumentnumber',
          label: 'ID Doc Number'
        }, {
          value: 'processorselect',
          label: 'Processor',
          options: [],
          source: 'processor'
        }, {
          value: 'programmanagerselect',
          label: 'Program Manager',
          options: [],
          source: 'programmanager'
        }, {
          value: 'countryselect',
          label: 'Country',
          search: true,
          options: [],
          source: 'countrys'
        }, {
          value: 'stateselect',
          label: 'State',
          options: [],
          search: true,
          source: 'states',
          map: {
            country: 'countryselect'
          }
        }, {
          value: 'city',
          label: 'City'
        }, {
          value: 'cardtypeselect',
          label: 'Card Type',
          options: [],
          source: 'cardtype'
        }, {
          value: 'accountselect',
          label: 'Role',
          options: [],
          source: 'roles'
        }, {
          value: 'cardprogramselect',
          label: 'Card Program',
          options: [],
          source: 'cardprograms'
        }, {
          value: 'accountstatusselect',
          label: 'Account Status',
          options: [],
          source: 'accountStatus'
        }, {
          value: 'flagsselect',
          label: 'Flags',
          options: [],
          source: 'flags'
        }, {
          value: 'cardissuedselect',
          label: 'Card issued',
          options: [
            {
              label: 'Yes',
              value: 'y'
            }, {
              label: 'No',
              value: 'n'
            }
          ]
        }, {
          value: 'cardstatusselect',
          label: 'Card Status',
          options: [
            {
              label: 'Active',
              value: 'active'
            }, {
              label: 'Inactive',
              value: 'inactive'
            }
          ]
        }, {
          value: 'billingstatusselect',
          label: 'Billing Address Status',
          options: [
            {
              label: 'Valid',
              value: 'valid'
            }, {
              label: 'Invalid',
              value: 'invalid'
            }
          ]
        }, {
          value: 'billingtypeselect',
          label: 'Billing Address Type',
          options: [],
          source: 'reshipperaddre'
        }, {
          value: 'genderselect',
          label: 'Gender',
          options: [
            {
              label: 'Male',
              value: 'Male'
            }, {
              label: 'Female',
              value: 'Female'
            }, {
              label: 'Not Specified',
              value: 'not_specified'
            }
          ]
        }, {
          value: 'regionselect',
          label: 'Region',
          options: [],
          source: 'regions'
        }, {
          value: 'kycProvider',
          label: 'KYC Provider',
          options: [],
          source: 'kycProviders'
        }, {
          value: 'idstatusselect',
          label: 'ID verification status',
          options: [],
          source: 'idStatus'
        }, {
          value: 'networktype',
          label: 'Network type',
          options: [],
          source: 'networks'
        }, {
          value: 'age',
          label: 'Age',
          options: [
            {
              label: '0-10',
              value: '1_10'
            }, {
              label: '11-20',
              value: '11_20'
            }, {
              label: '21-30',
              value: '21_30'
            }, {
              label: '31-40',
              value: '31_40'
            }, {
              label: '41-50',
              value: '41_50'
            }, {
              label: '51-60',
              value: '51_60'
            }, {
              label: '61-70',
              value: '61_70'
            }, {
              label: '71-80',
              value: '71_80'
            }, {
              label: '81-90',
              value: '81_90'
            }, {
              label: '91-100',
              value: '91_100'
            }, {
              label: '100+',
              value: '101_200'
            }
          ]
        }, {
          value: 'IDissuedate',
          label: 'ID issue date',
          options: [
            {
              label: 'Valid',
              value: 'valid'
            }, {
              label: 'Invalid',
              value: 'invalid'
            }
          ]
        }, {
          value: 'IDexpirationdate',
          label: 'ID expiration date',
          options: [
            {
              label: 'Valid',
              value: 'valid'
            }, {
              label: 'Invalid',
              value: 'invalid'
            }
          ]
        }, {
          value: 'affiliate_tenant_type',
          label: 'Affiliate Type',
          options: [],
          source: 'affiliateTypes'
        }, {
          value: 'affiliate_select',
          label: 'Affiliate',
          options: [],
          search: true,
          source: 'affiliates',
          map: {
            type: 'affiliate_tenant_type'
          }
        }, {
          value: 'merchant_custom_name',
          label: 'Merchant custom name'
        }, {
          value: 'email_status',
          label: 'Email status',
          options: [
            {
              label: 'Active',
              value: 'active'
            }, {
              label: 'Inactive',
              value: 'inactive'
            }
          ]
        }, {
          value: 'Registrationdates',
          label: 'Registration date',
          range: [{
            value: 'RegistrationdateA',
            type: 'date'
          }, {
            value: 'RegistrationdateB',
            type: 'date'
          }]
        }, {
          value: 'ucCreated',
          label: 'Card Create Date',
          range: [{
            value: 'ucCreatedFrom',
            type: 'date'
          }, {
            value: 'ucCreatedTo',
            type: 'date'
          }]
        }, {
          value: 'LoadPayments',
          label: 'Load Payment Date',
          range: [{
            value: 'LoadPaymentsA',
            type: 'date'
          }, {
            value: 'LoadPaymentsB',
            type: 'date'
          }]
        }, {
          value: 'LoadPaymentTimes',
          label: 'Load Payment Times',
          range: [{
            value: 'LoadPaymentsTimesA',
            type: 'number'
          }, {
            value: 'LoadPaymentsTimesB',
            type: 'number'
          }]
        }, {
          value: 'LoadPaymentAmount',
          label: 'Load Payment Amount',
          range: [{
            value: 'LoadPaymentsAmountA',
            type: 'amount'
          }, {
            value: 'LoadPaymentsAmountB',
            type: 'amount'
          }]
        }, {
          value: 'CardTransactions',
          label: 'Card Transaction Date',
          helper: 'POS transaction',
          range: [{
            value: 'CardTransactionsA',
            type: 'date'
          }, {
            value: 'CardTransactionsB',
            type: 'date'
          }]
        }, {
          value: 'CardTransactionsTimes',
          label: 'Card Transaction Times',
          range: [{
            value: 'CardTransactionsTimesA',
            type: 'number'
          }, {
            value: 'CardTransactionsTimesB',
            type: 'number'
          }]
        }, {
          value: 'CardTransactionAmount',
          label: 'Card Transaction Amount',
          range: [{
            value: 'CardTransactionsAmountA',
            type: 'amount'
          }, {
            value: 'CardTransactionsAmountB',
            type: 'amount'
          }]
        }, {
          value: 'MCC',
          label: 'MCC',
          helper: 'EG:A,B'
        }, {
          value: 'localbalance',
          label: 'Local balance',
          range: [{
            value: 'localbalancemin',
            type: 'amount'
          }, {
            value: 'localbalancemax',
            type: 'amount'
          }]
        }, {
          value: 'legacyBalance',
          label: 'Legacy balance',
          range: [{
            value: 'legacyBalanceFrom',
            type: 'amount'
          }, {
            value: 'legacyBalanceTo',
            type: 'amount'
          }]
        }, {
          value: 'accountBalance',
          label: 'Account balance',
          range: [{
            value: 'accountBalanceFrom',
            type: 'amount'
          }, {
            value: 'accountBalanceTo',
            type: 'amount'
          }]
        }, {
          value: 'vip',
          label: 'VIP',
          options: [
            {
              label: 'Yes',
              value: 'yes'
            }, {
              label: 'No',
              value: 'no'
            }
          ]
        }
      ]
    }
  },
  computed: {
    isGranted () {
      return this.$store.state.User.isGranted
    },
    user () {
      return this.$store.state.User
    }
  },
  methods: {
    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.getQueryParams()
      const resp = await request(`/admin/user_search/search/${pagination.page}/${pagination.rowsPerPage}`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.quick.count = resp.data.total
        this.pagination.rowsNumber = resp.data.total
        this.chartData = resp.data.chartData
        if (this.chartData) {
          eventBus.$emit('reload-top-chart', this.chartData)
        }
      }
    },

    async submitFilters () {
      const filters = this.getQueryParams()
      this.loading = true
      const response = await request(`/admin/alert_job_submit/${this.$route.query.target}`, 'post', filters)
      console.log(response)
      if (response.success) {
        this.loading = false
        redirectRoot(`#/a/alert/search`)
      }
    },

    async updateGtpPhone (u) {
      this.loading = true
      const resp = await request(`/admin/user/essolo/update-gtp-phone-number`, 'get', {
        userId: u.id
      })
      this.loading = false
      if (resp.success) {
        this.$q.dialog({
          title: 'Update GTP Phone Number',
          message: 'The phone number we use to communicate with GTP\'s API:',
          prompt: {
            model: resp.data
          },
          cancel: true
        }).then(async phone => {
          this.loading = true
          const resp = await request(`/admin/user/essolo/update-gtp-phone-number`, 'post', {
            userId: u.id,
            phone
          })
          this.loading = false
          if (resp.success) {
            notify(resp.message)
          }
        }).catch(() => {})
      }
    }
  },
  mounted () {
    if (this.user.cpKey === 'cp_usu') {
      this.filterOptions.push({
        value: 'register_step',
        label: 'Onboard Status',
        options: [
          {
            value: 'register_consumer_created',
            label: 'Onboard (email)'
          },
          {
            value: 'register_consumer_email_confirmed',
            label: 'Onboard (home)'
          },
          {
            value: 'pending',
            label: 'Pending Member'
          },
          {
            value: 'active_no_kyc',
            label: 'Active Member (no KYC)'
          },
          {
            value: 'active',
            label: 'Active Member (KYC)'
          },
          {
            value: 'negative_balance',
            label: 'Negative Balance'
          },
          {
            value: 'inactive',
            label: 'Inactive'
          }
        ]
      })
    }
  }
}
</script>

<style lang="scss">
@import '../../../css/variable';

#user_management__user_page {
}
</style>
