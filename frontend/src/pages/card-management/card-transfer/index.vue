<template>
  <q-page id="card_management__card_transfer_page" class="form-page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="group-header">Select the sending user & card</div>
      <div class="row gutter-lg">
        <div class="col-4">
          <q-field label="Keyword" label-width="3" class="required">
            <q-input v-model="fromKeyword"
                     @blur="$v.fromKeyword.$touch"
                     :error="$v.fromKeyword.$error"
                     placeholder="Sending user Id or email"></q-input>
          </q-field>
        </div>
        <div class="col-4">
          <q-btn label="Search"
                 color="primary"
                 @click="search('from')"
                 icon="mdi-magnify"></q-btn>
        </div>
      </div>

      <template v-if="fromUser">
        <div class="row gutter-lg">
          <div class="col-4">
            <q-field label="Id" label-width="3">
              <q-input v-model="fromUser.id" disable></q-input>
            </q-field>
          </div>
          <div class="col-4">
            <q-field label="Email" label-width="3">
              <q-input v-model="fromUser.username" disable></q-input>
            </q-field>
          </div>
          <div class="col-4">
            <q-field label="Full Name" label-width="3">
              <q-input :value="fromUser.firstName + ' ' + fromUser.lastName" disable></q-input>
            </q-field>
          </div>
        </div>
        <div class="row gutter-lg">
          <div class="col-4">
            <q-field label="Card" label-width="3" class="required">
              <q-select v-model="fromUser.uc"
                        @blur="$v.fromUser.uc.$touch"
                        :error="$v.fromUser.uc.$error"
                        :options="fromCards"></q-select>
            </q-field>
          </div>
          <div class="col-4" v-if="fromCard">
            <q-field label="Card Id" label-width="3">
              <q-input v-model="fromCard.id" disable></q-input>
            </q-field>
          </div>
          <div class="col-4" v-if="fromCard">
            <q-field label="Card Name" label-width="3">
              <q-input v-model="fromCard.card.name" disable></q-input>
            </q-field>
          </div>
        </div>
        <div class="row gutter-lg" v-if="fromCard">
          <div class="col-4">
            <q-field label="Balance" label-width="3">
              <q-input v-model="fromCard.coin.formatted" disable></q-input>
            </q-field>
          </div>
          <div class="col-4">
            <q-field label="Status" label-width="3">
              <q-input v-model="fromCard.status" disable></q-input>
            </q-field>
          </div>
          <div class="col-4">
            <q-field label="Max Balance" label-width="3">
              <q-input :value="$c.moneyFormat(fromCard.card.maxBalance)" disable></q-input>
            </q-field>
          </div>
        </div>

        <template v-if="fromCard">
          <div class="group-header">Select the receiving user & card</div>
          <div class="row gutter-lg">
            <div class="col-4">
              <q-field label="Keyword" label-width="3" class="required">
                <q-input v-model="toKeyword"
                         @blur="$v.toKeyword.$touch"
                         :error="$v.toKeyword.$error"
                         placeholder="Receiving user Id or email"></q-input>
              </q-field>
            </div>
            <div class="col-4">
              <q-btn label="Search"
                     color="primary"
                     @click="search('to')"
                     icon="mdi-magnify"></q-btn>
            </div>
          </div>

          <template v-if="toUser">
            <div class="row gutter-lg">
              <div class="col-4">
                <q-field label="Id" label-width="3">
                  <q-input v-model="toUser.id" disable></q-input>
                </q-field>
              </div>
              <div class="col-4">
                <q-field label="Email" label-width="3">
                  <q-input v-model="toUser.username" disable></q-input>
                </q-field>
              </div>
              <div class="col-4">
                <q-field label="Full Name" label-width="3">
                  <q-input :value="toUser.firstName + ' ' + toUser.lastName" disable></q-input>
                </q-field>
              </div>
            </div>
            <div class="row gutter-lg">
              <div class="col-4">
                <q-field label="Card" label-width="3" class="required">
                  <q-select v-model="toUser.uc"
                            @blur="$v.toUser.uc.$touch"
                            :error="$v.toUser.uc.$error"
                            :options="toCards"></q-select>
                </q-field>
              </div>
              <div class="col-4" v-if="toCard">
                <q-field label="Card Id" label-width="3">
                  <q-input v-model="toCard.id" disable></q-input>
                </q-field>
              </div>
              <div class="col-4" v-if="toCard">
                <q-field label="Card Name" label-width="3">
                  <q-input v-model="toCard.card.name" disable></q-input>
                </q-field>
              </div>
            </div>
            <div class="row gutter-lg" v-if="toCard">
              <div class="col-4">
                <q-field label="Balance" label-width="3">
                  <q-input v-model="toCard.coin.formatted" disable></q-input>
                </q-field>
              </div>
              <div class="col-4">
                <q-field label="Status" label-width="3">
                  <q-input v-model="toCard.status" disable></q-input>
                </q-field>
              </div>
              <div class="col-4">
                <q-field label="Max Balance" label-width="3">
                  <q-input :value="$c.moneyFormat(toCard.card.maxBalance)" disable></q-input>
                </q-field>
              </div>
            </div>
          </template>

          <template v-if="toCard">
            <div class="group-header">Specify other parameters</div>
            <div class="row gutter-lg">
              <div class="col-6">
                <q-field label="Amount" label-width="3" class="required">
                  <q-input type="number"
                           prefix="$"
                           v-model="amount"
                           @blur="$v.amount.$touch"
                           :error="$v.amount.$error"></q-input>
                </q-field>
              </div>
            </div>

            <div class="row mt-30">
              <q-btn label="Submit"
                     color="primary"
                     @click="submit"
                     icon="mdi-send"></q-btn>
            </div>
          </template>
        </template>
      </template>
    </div>

    <q-inner-loading :visible="loading">
      <q-spinner :size="50"></q-spinner>
    </q-inner-loading>
  </q-page>
</template>

<script>
import PageMixin from '../../../mixins/PageMixin'
import { notifySuccess, request } from '../../../common'
import { required } from 'vuelidate/lib/validators'
import _ from 'lodash'

export default {
  name: 'CardTransferIndex',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'Card to Card Transfer',
      fromUser: null,
      toUser: null,
      fromKeyword: null,
      toKeyword: null,
      amount: null
    }
  },
  computed: {
    fromCards () {
      if (!this.fromUser) {
        return []
      }
      return this.fromUser.cards.map(uc => {
        return {
          value: uc.id,
          label: uc.accountNumber
        }
      })
    },
    fromCard () {
      if (!this.fromUser || !this.fromUser.uc) {
        return null
      }
      return _.find(this.fromUser.cards, { id: this.fromUser.uc })
    },
    toCards () {
      if (!this.toUser) {
        return []
      }
      return this.toUser.cards.map(uc => {
        return {
          value: uc.id,
          label: uc.accountNumber
        }
      })
    },
    toCard () {
      if (!this.toUser || !this.toUser.uc) {
        return null
      }
      return _.find(this.toUser.cards, { id: this.toUser.uc })
    }
  },
  validations: {
    fromUser: {
      id: { required },
      uc: { required }
    },
    toUser: {
      id: { required },
      uc: { required }
    },
    fromKeyword: { required },
    toKeyword: { required },
    amount: { required }
  },
  methods: {
    async search (type) {
      if (this.$v[`${type}Keyword`].$invalid) {
        this.$v[`${type}Keyword`].$touch()
        return
      }

      this.loading = true
      const resp = await request(`/admin/user/search-in-program`, 'get', {
        keyword: this[`${type}Keyword`]
      })
      this.loading = false
      if (resp.success) {
        this[`${type}User`] = resp.data
        if (resp.data.cards.length === 1) {
          this[`${type}User`].uc = resp.data.cards[0].id
        }
      }
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return
      }
      this.loading = true
      const resp = await request(`/admin/card/card-transfer/submit`, 'post', {
        fromUserCardId: this.fromUser.uc,
        toUserCardId: this.toUser.uc,
        amount: this.amount
      })
      this.loading = false
      if (resp.success) {
        notifySuccess(resp)
        this.resetForm()
      }
    },
    resetForm () {
      this.fromUser = null
      this.fromKeyword = null
      this.toUser = null
      this.toKeyword = null

      this.$v.$reset()
    }
  }
}
</script>

<style lang="scss">
  #card_management__card_transfer_page {
  }
</style>
