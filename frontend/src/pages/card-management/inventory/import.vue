<template>
  <q-page id="card_management__inventory_import_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
    </div>
    <div class="page-content mt-20">
      <div class="form">
        <q-field label="Platform"
                 :error="$v.entity.platform.$error"
                 v-show="$c.showPlatforms()"
                 label-width="12">
          <q-select :options="platforms"
                    :disable="submitting"
                    filter
                    @blur="$v.entity.platform.$touch"
                    v-model="entity.platform"></q-select>
        </q-field>
        <q-field label="Card Program"
                 :error="$v.entity.cardProgram.$error"
                 label-width="12">
          <q-select :options="filteredCardPrograms"
                    :disable="submitting"
                    :placeholder="entity.platform ? '' : 'Please select a `Platform` first.'"
                    filter
                    @blur="$v.entity.cardProgram.$touch"
                    v-model="entity.cardProgram"></q-select>
        </q-field>
        <q-field label="Distribution Center"
                 :error="$v.entity.distributionCenter.$error"
                 v-if="cardProgram && cardProgram.deliveryMethod === 'mail'"
                 label-width="12">
          <q-select :options="distributionCenters"
                    :disable="submitting"
                    filter
                    @blur="$v.entity.distributionCenter.$touch"
                    v-model="entity.distributionCenter"></q-select>
        </q-field>
        <q-field label="CSV File"
                 label-width="12"
                 helper="Click on '+' button to select the file to upload. Please ensure that the file has been uploaded before submitting the form.">
          <q-uploader url="/attachments"
                      :disable="submitting"
                      name="file"
                      auto-expand
                      ref="uploader"
                      @add="$refs.uploader && $refs.uploader.upload()"
                      @uploaded="(file, xhr) => uploadedAttachment(xhr)"
                      :additional-fields="[{name: 'category', value: 'card_inventory'}]"
                      extensions=".csv"
                      clearable></q-uploader>
          <div v-if="file" class="mt-10">
            Uploaded: <a :href="file.url" target="_blank">{{ file.name }}</a>
          </div>
        </q-field>

        <q-card class="notice-card">
          <q-card-title>Notice</q-card-title>
          <q-card-separator></q-card-separator>
          <q-card-main>
            <div class="font-14">
              <span class="black">Sample for the CSV file:</span>
              <a href="/statics/admin/card_inventory_sample.csv"
                 class="ml-10"
                 target="_blank">Download</a>
            </div>
            <div class="mt-10">
              <table class="normal-table">
                <tr>
                  <th>BIN_Number</th>
                  <th>Card_Processor_Unique_ID</th>
                </tr>
                <tr>
                  <td>123456</td>
                  <td>00100023</td>
                </tr>
                <tr>
                  <td>123456</td>
                  <td>00100024</td>
                </tr>
              </table>
            </div>
            <ul class="font-14 mt-10">
              <li>The header is optional.</li>
              <li>Please ensure that the leading zeros of the `Card_Processor_Unique_ID` are kept.</li>
            </ul>
          </q-card-main>
        </q-card>

        <div class="mt-20">
          <q-btn color="primary"
                 icon="mdi-send"
                 :disable="$v.$invalid"
                 :loading="submitting"
                 :percentage="percentage"
                 @click="submit"
                 label="Import"></q-btn>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script>
import PageMixin from '../../../mixins/PageMixin'
import { required, requiredIf } from 'vuelidate/lib/validators'
import { notifySuccess, request } from '../../../common'
import _ from 'lodash'

export default {
  name: 'CardInventoryImport',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'Import Card Inventory',
      platforms: [],
      cardPrograms: [],
      distributionCenters: [],
      entity: {
        platform: null,
        cardProgram: null,
        distributionCenter: null,
        file: null
      },
      file: null,
      submitting: false,
      percentage: 0
    }
  },
  computed: {
    filteredCardPrograms () {
      if (!this.entity.platform) {
        return []
      }
      const platform = _.find(this.platforms, { value: this.entity.platform })
      if (platform.label === 'Tern Commerce') {
        return this.cardPrograms.filter(cp => !cp.platform || cp.platform === platform.value)
      }
      return this.cardPrograms.filter(cp => cp.platform === platform.value)
    },
    cardProgram () {
      if (!this.entity.cardProgram) {
        return null
      }
      return _.find(this.cardPrograms, { value: this.entity.cardProgram })
    }
  },
  validations: {
    entity: {
      platform: {
        required
      },
      cardProgram: {
        required
      },
      distributionCenter: {
        required: requiredIf(function () {
          return this.cardProgram && this.cardProgram.deliveryMethod === 'mail'
        })
      },
      file: {
        required
      }
    }
  },
  methods: {
    async submit () {
      this.submitting = true
      this.percentage = 0
      const itv = setInterval(() => {
        this.percentage += 1
      }, 100)
      const resp = await request(`/admin/card_management/inventory/import`, 'post', this.entity)
      clearInterval(itv)
      this.submitting = false
      this.percentage = 0
      if (resp.success) {
        notifySuccess(resp)
        this.$router.replace('/a/card-management/inventory')
      }
    },

    async reload () {
      this.$q.loading.show()
      const resp = await request(`/admin/card_management/inventory/filters`)
      this.$q.loading.hide()
      if (resp.success) {
        this.platforms = resp.data.platforms
        this.cardPrograms = resp.data.cardPrograms
        this.distributionCenters = resp.data.distributionCenters

        if (!this.$c.showPlatforms()) {
          this.entity.platform = this.platforms[0].value
        }
      }
    },

    uploadedAttachment (xhr) {
      const resp = JSON.parse(xhr.response)
      this.file = resp.data.file
      this.entity.file = this.file.id
      this.$refs.uploader.reset()
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #card_management__inventory_import_page {
    .notice-card {
      background: white;
      margin-top: 20px;

      .q-card-container {
        padding: 12px;
      }

      .q-card-title {
        font-size: 16px;
      }

      ul {
        margin: 20px 0;
        padding-left: 30px;
      }

      li {
        margin-bottom: 8px;
      }
    }

    .normal-table {
      font-size: 14px;
      border: 1px solid;
      border-collapse: collapse;

      th, td {
        padding: 5px;
        border: 1px solid;
        background: white;
      }
    }
  }
</style>
