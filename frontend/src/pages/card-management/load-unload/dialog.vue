<template>
  <q-dialog
    class="card_management__load_unload_dialog big-dialog admin-root"
    v-model="visible"
    :preventClose="true">
    <span slot="title">
      Load/Unload Card

      <q-btn round
             icon="mdi-close"
             class="btn-close"
             text-color="white"
             @click="_hide"
             color="red-2"></q-btn>
    </span>
    <div slot="body">
      <FormComponent></FormComponent>
    </div>
    <template slot="buttons">
      <div class="flex-center text-right"></div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import FormComponent from './index'

export default {
  name: 'card-load-unload-dialog',
  mixins: [
    Singleton
  ],
  components: {
    FormComponent
  }
}
</script>

<style lang="scss">

</style>
