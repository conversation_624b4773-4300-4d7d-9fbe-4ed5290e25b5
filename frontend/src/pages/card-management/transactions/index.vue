<template>
  <q-page id="card_management__transactions_page" class="form-page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="group-header">Search the user</div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Keyword" label-width="3" class="required">
            <q-input v-model="keyword"
                     @blur="$v.keyword.$touch"
                     :error="$v.keyword.$error"
                     placeholder="User Id or email"></q-input>
          </q-field>
        </div>
        <div class="col-6">
          <q-btn label="Search"
                 color="primary"
                 @click="search"
                 icon="mdi-magnify"></q-btn>
        </div>
      </div>

      <template v-if="entity">
        <div class="row gutter-lg">
          <div class="col-6">
            <q-field label="Id" label-width="3">
              <q-input v-model="entity.id" disable></q-input>
            </q-field>
          </div>
          <div class="col-6">
            <q-field label="Email" label-width="3">
              <q-input v-model="entity.username" disable></q-input>
            </q-field>
          </div>
        </div>
        <div class="row gutter-lg">
          <div class="col-6">
            <q-field label="First Name" label-width="3">
              <q-input v-model="entity.firstName" disable></q-input>
            </q-field>
          </div>
          <div class="col-6">
            <q-field label="Last Name" label-width="3">
              <q-input v-model="entity.lastName" disable></q-input>
            </q-field>
          </div>
        </div>

        <div class="group-header">Choose the card to get the transaction details</div>
        <div class="row gutter-lg">
          <div class="col-6">
            <q-field label="Account Number" label-width="3" class="required">
              <q-select v-model="entity.uc"
                        @blur="$v.entity.uc.$touch"
                        :error="$v.entity.uc.$error"
                        :options="cards"></q-select>
            </q-field>
          </div>
        </div>

        <template v-if="card">
          <div class="row gutter-lg">
            <div class="col-6">
              <q-field label="Card Id" label-width="3">
                <q-input v-model="card.id" disable></q-input>
              </q-field>
            </div>
            <div class="col-6">
              <q-field label="Holder" label-width="3">
                <q-input v-model="card.holder" disable></q-input>
              </q-field>
            </div>
          </div>
          <div class="row gutter-lg">
            <div class="col-6">
              <q-field label="Balance" label-width="3">
                <q-input v-model="card.coin.formatted" disable></q-input>
              </q-field>
            </div>
            <div class="col-6">
              <q-field label="Status" label-width="3">
                <q-input v-model="cardStatus" disable></q-input>
              </q-field>
            </div>
          </div>

          <template v-if="card.card">
            <div class="row gutter-lg">
              <div class="col-6">
                <q-field label="Card Name" label-width="3">
                  <q-input v-model="card.card.name" disable></q-input>
                </q-field>
              </div>
              <div class="col-6">
                <q-field label="Max Balance" label-width="3">
                  <q-input :value="$c.moneyFormat(card.card.maxBalance)" disable></q-input>
                </q-field>
              </div>
            </div>
            <div class="row gutter-lg">
              <div class="col-6">
                <q-field label="Max Load Amount" label-width="3">
                  <q-input :value="$c.moneyFormat(card.card.maxLoadAmount)" disable></q-input>
                </q-field>
              </div>
              <div class="col-6">
                <q-field label="Min Load Amount" label-width="3">
                  <q-input :value="$c.moneyFormat(card.card.minLoadAmount)" disable></q-input>
                </q-field>
              </div>
            </div>
          </template>

          <div class="group-header">Specify other parameters</div>
          <div class="row gutter-lg">
            <div class="col-6">
              <q-field label="Start Date" label-width="3" class="required">
                <q-datetime type="date"
                            v-model="entity.startAt"
                            @blur="$v.entity.startAt.$touch"
                            :error="$v.entity.startAt.$error">
                </q-datetime>
              </q-field>
            </div>
            <div class="col-6">
              <q-field label="End Date" label-width="3" class="required">
                <q-datetime type="date"
                            v-model="entity.endAt"
                            @blur="$v.entity.endAt.$touch"
                            :error="$v.entity.endAt.$error">
                </q-datetime>
              </q-field>
            </div>
            <div class="col-6">
              <q-field label="Number" label-width="3" helper="Max transactions to return. Defaults to 100">
                <q-input type="number"
                         min="1"
                         v-model="entity.number"></q-input>
              </q-field>
            </div>
          </div>

          <div class="row mt-30">
            <q-btn label="Query"
                   color="primary"
                   @click="submit"
                   icon="mdi-send"></q-btn>
          </div>

          <template v-if="items">
            <div class="group-header">Transaction details</div>
            <q-table :data="items"
                     :columns="columns"
                     separator="none"
                     row-key="id">
              <q-tr slot="body" slot-scope="props" :props="props">
                <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
                  <template v-if="['time'].includes(col.field)">
                    {{ props.row[col.field] | date('L LT') }}
                  </template>
                  <template v-else>
                    {{ _.get(props.row, col.field) }}
                  </template>
                </q-td>
              </q-tr>
            </q-table>
          </template>
        </template>
      </template>
    </div>

    <q-inner-loading :visible="loading">
      <q-spinner :size="50"></q-spinner>
    </q-inner-loading>
  </q-page>
</template>

<script>
import PageMixin from '../../../mixins/PageMixin'
import { notify, request } from '../../../common'
import { required } from 'vuelidate/lib/validators'
import _ from 'lodash'

export default {
  name: 'CardTransactionsIndex',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'Card Transaction Details',
      entity: null,
      keyword: null,
      items: null,
      columns: [
        {
          field: 'id',
          label: 'ID',
          align: 'left'
        }, {
          field: 'tranId',
          label: 'Transaction Id',
          align: 'left'
        }, {
          field: 'type',
          label: 'Type',
          align: 'left'
        }, {
          field: 'time',
          label: 'Time',
          align: 'left'
        }, {
          field: 'coin.formatted',
          label: 'Amount',
          align: 'right'
        }, {
          field: 'balance.formatted',
          label: 'Balance',
          align: 'right'
        }, {
          field: 'description',
          label: 'Description',
          align: 'left'
        }, {
          field: 'merchant.name',
          label: 'Merchant',
          align: 'left'
        }
      ]
    }
  },
  computed: {
    cards () {
      if (!this.entity) {
        return []
      }
      return this.entity.cards.map(uc => {
        return {
          value: uc.id,
          label: uc.accountNumber
        }
      })
    },
    card () {
      if (!this.entity || !this.entity.uc) {
        return null
      }
      return _.find(this.entity.cards, { id: this.entity.uc })
    },
    cardStatus () {
      if (!this.card) {
        return null
      }
      return `${this.card.status.toUpperCase()} (${this.card.nativeStatus})`
    }
  },
  validations: {
    entity: {
      id: { required },
      uc: { required },
      startAt: { required },
      endAt: { required }
    },
    keyword: { required }
  },
  methods: {
    async search () {
      if (this.$v.keyword.$invalid) {
        this.$v.keyword.$touch()
        return
      }

      this.loading = true
      const resp = await request(`/admin/user/search-in-program`, 'get', {
        keyword: this.keyword
      })
      this.loading = false
      if (resp.success) {
        this.entity = resp.data
        if (resp.data.cards.length === 1) {
          this.entity.uc = resp.data.cards[0].id
        }
      }
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return
      }
      this.loading = true
      const resp = await request(`/admin/card/transactions/submit`, 'post', this.entity)
      this.loading = false
      if (resp.success) {
        notify()
        this.items = resp.data || []
      }
    },
    resetForm () {
      this.entity = null
      this.keyword = null

      this.$v.$reset()
    }
  }
}
</script>

<style lang="scss">
  #card_management__transactions_page {
  }
</style>
