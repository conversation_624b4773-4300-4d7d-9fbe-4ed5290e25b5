<template>
  <q-page id="card_program__load_methods_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="subtitle">{{ subtitle }}</div>
      <div class="fun-group">
        <q-btn color="primary"
               to="/a/card-program/owners/add"
               icon="mdi-plus"
               label="Add"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat round dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true"/>
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body" slot-scope="props" :props="props">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="['id'].includes(col.field)">
              {{ _.get(props.row, col.field) }}
            </template>
            <template v-else-if="col.field === ''">
              <q-btn size="xs"
                     color="grey-3"
                     text-color="black"
                     :to="`/a/card-program/owners/edit/${props.row.id}`"
                     label="Edit"></q-btn>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'

export default {
  name: 'CardProgramLoadMethodsIndex',
  mixins: [
    ListPageMixin
  ],
  data () {
    return {
      title: 'Load methods',
      subtitle: '',
      requestUrl: '/admin/card_program/load_methods/list',
      autoLoad: true,
      columns: [
        {
          field: 'county_name',
          label: 'Country Name',
          align: 'left'
        }, {
          field: '',
          label: 'Action',
          align: 'left'
        }
      ],
      quick: {
        count: 0
      },
      filterOptions: [],
      requestCb: (data) => {}
    }
  },
  methods: {
    getOtherQueryParams () {
      if (!this.$route.query) {
        return {}
      }
      return {
        'cp': this.$route.query.cp
      }
    }
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #card_program__load_methods_page {
  }
</style>
