<template>
  <q-page id="card_program__owners_detail_page" class="form-page">
    <div class="page-header column">
      <div class="title mb-15">{{ title }}</div>
      <div class="subtitle-line font-14">{{ entity.firstName || '&nbsp;' }}</div>
    </div>
    <div class="page-content">
      <div class="group-header">Basic info</div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Name" label-width="3" class="required">
            <q-input v-model="entity.firstName"
                     @blur="$v.entity.firstName.$touch"
                     :error="$v.entity.firstName.$error"></q-input>
          </q-field>
        </div>
      </div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Email" label-width="3" class="required">
            <q-input v-model="entity.email"
                     :disable="!!userId"
                     @blur="$v.entity.email.$touch"
                     :error="$v.entity.email.$error"></q-input>
          </q-field>
        </div>
        <div class="col-6" v-if="!userId">
          <q-field label="Initial Password" label-width="3" class="required">
            <q-input v-model="entity.password"
                     type="password"
                     @blur="$v.entity.password.$touch"
                     :error="$v.entity.password.$error"></q-input>
          </q-field>
        </div>
      </div>
      <div class="row gutter-lg">
      </div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Status" label-width="3" class="required">
            <q-select v-model="entity.status" :options="statusOptions"></q-select>
          </q-field>
        </div>
        <div class="col-6" v-if="userId">
          <q-field label="Lock Status" label-width="3" class="required" helper="`Locked` means user cannot log in.">
            <q-option-group
              v-model="entity.lockStatus"
              :options="lockOptions"
              @blur="$v.entity.lockStatus.$touch"
              :error="$v.entity.lockStatus.$error"
              color="primary"
              inline
              dense
            />
          </q-field>
        </div>
      </div>

      <div class="group-header">Accessible Card Program</div>
      <div>
        <q-option-group
          type="checkbox"
          v-model="entity.cardPrograms"
          :options="cardPrograms"
          color="primary"
        />
      </div>

      <div class="group-header">Developer Access</div>
      <div class="mb-20">
        <a href="/developer" target="_blank">API Documentation <q-icon name="mdi-open-in-new"></q-icon></a>
      </div>
      <template v-if="userId">
        <q-field label="API key" label-width="12" class="mt--5">
          <q-input v-model="entity.apiToken" readonly></q-input>
        </q-field>
        <div class="row gutter-lg">
          <div class="col-6">
            <q-btn label="Copy" @click="copyApiToken"></q-btn>
            <q-btn label="Regenerate" class="ml-10"
                   @click="regenerateApiToken"
                   color="negative"></q-btn>
          </div>
        </div>
      </template>
      <p v-if="!userId">Save the form first to access the API key.</p>

      <div class="row mt-40">
        <q-btn label="Submit"
               color="primary"
               @click="submit"
               icon="mdi-send"></q-btn>
      </div>
    </div>

    <q-inner-loading :visible="loading">
      <q-spinner :size="50"></q-spinner>
    </q-inner-loading>
  </q-page>
</template>

<script>
import PageMixin from '../../../mixins/PageMixin'
import { required, requiredIf } from 'vuelidate/lib/validators'
import { notifySuccess, notify, request } from '../../../common'
import copy from 'copy-to-clipboard'

export default {
  name: 'CardProgramOwnersDetailIndex',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'Add Program Owner',
      entity: {
        cardPrograms: [],
        status: 'active',
        lockStatus: 'Unlock'
      },
      statusOptions: [
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' },
        { label: 'Closed', value: 'closed' },
        { label: 'Banned', value: 'banned' }
      ],
      lockOptions: [
        { label: 'Locked', value: 'Lock' },
        { label: 'Unlocked', value: 'Unlock' }
      ],
      cardPrograms: []
    }
  },
  validations: {
    entity: {
      email: { required },
      password: {
        required: requiredIf(function () {
          return !this.userId
        })
      },
      firstName: { required },
      status: { required },
      lockStatus: {
        required: requiredIf(function () {
          return this.userId
        })
      }
    }
  },
  computed: {
    userId () {
      return this.$route.params.id
    }
  },
  methods: {
    async reload () {
      this.loading = true
      const resp = await request(`/admin/card_program/owners/detail?userId=${this.userId || ''}`)
      this.loading = false
      if (resp.success) {
        this.cardPrograms = resp.data.cardPrograms
        if (resp.data.user) {
          this.entity = resp.data.user
        }
      }
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return
      }
      this.loading = true
      const resp = await request(`/admin/card_program/owners/submit`, 'post', this.entity)
      this.loading = false
      if (resp.success) {
        notifySuccess()
        this.$router.replace('/a/card-program/owners')
        if (!this.userId) {
          this.$nextTick(() => {
            setTimeout(() => {
              this.$router.replace(`/a/card-program/owners/edit/${resp.data.id}`)
            }, 500)
          })
        }
      }
    },
    async copyApiToken () {
      copy(this.entity.apiToken)
      notify('Copied to the clipboard')
    },
    async regenerateApiToken () {
      this.$q.dialog({
        title: `Regenerate API key`,
        message: 'Are you sure that you want to regenerate the API key? ' +
          'The old key will be invalid after regeneration.',
        cancel: true,
        color: 'negative'
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/user/generate-api-token/${this.entity.id}`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          notifySuccess(resp)
          this.entity.apiToken = resp.data
        }
      }).catch(() => {})
    }
  },
  mounted () {
    if (this.userId) {
      this.title = 'Edit Program Owner'
    }

    this.reload()
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #card_program__owners_detail_page {
  }
</style>
