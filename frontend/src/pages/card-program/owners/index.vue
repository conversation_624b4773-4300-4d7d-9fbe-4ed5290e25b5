<template>
  <q-page id="card_program__owners_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-btn color="primary"
               v-if="editable"
               to="/a/card-program/owners/add"
               icon="mdi-plus"
               label="Add"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat round dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true"/>
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body" slot-scope="props" :props="props">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="['id', 'firstName', 'lastName', 'email'].includes(col.field)">
              <span v-html="$options.filters.searchMatch(_.get(props.row, col.field), keyword)"></span>
            </template>
            <template v-else-if="col.field === 'cardPrograms'">
              <div class="mb-5" v-for="v in col.value" :key="v">{{ v }}</div>
            </template>
            <template v-else-if="col.field === ''">
              <q-btn size="xs"
                     v-if="editable"
                     color="grey-3"
                     text-color="black"
                     :to="`/a/card-program/owners/edit/${props.row.id}`"
                     label="Edit"></q-btn>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin } from '../../../common'

export default {
  name: 'CardProgramOwnersIndex',
  mixins: [
    ListPageMixin,
    EventHandlerMixin('admin_card_program_owners_search', 'search')
  ],
  data () {
    return {
      title: 'Program Owners',
      requestUrl: '/admin/card_program/owners/list',
      filtersUrl: '/admin/card_program/owners/filters',
      autoLoad: true,
      autoReloadWhenUrlChanges: true,
      editable: false,
      columns: [
        {
          field: 'id',
          label: 'ID',
          align: 'left'
        }, {
          field: 'firstName',
          label: 'Name',
          align: 'left'
        }, {
          field: 'email',
          label: 'Email Address',
          align: 'left'
        }, {
          field: 'cardPrograms',
          label: 'Card Programs',
          align: 'left'
        }, {
          field: 'status',
          label: 'Status',
          align: 'left'
        }, {
          field: '',
          label: 'Action',
          align: 'left'
        }
      ],
      quick: {
        count: 0
      },
      filterOptions: [
        {
          value: 'filter[cp.id=]',
          label: 'Card program',
          options: [],
          source: 'cardPrograms'
        }
      ],
      requestCb: (data) => {
        this.editable = !!data.editable
      }
    }
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #card_program__owners_page {
  }
</style>
