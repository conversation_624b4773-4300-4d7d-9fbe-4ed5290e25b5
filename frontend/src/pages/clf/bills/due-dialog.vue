<template>
  <q-dialog
    class="dispensary-bill-due-dialog clf-dialog clf-dialog-right-buttons admin-root"
    v-model="visible"
    :preventClose="true">
    <span slot="title">
      <template v-if="entity.status === 'paid'">Paid Invoice</template>
      <template v-else>{{ entity.invoiceView ? 'Outgoing' : 'Incoming' }} Invoice</template>

      <q-btn round
             icon="mdi-close"
             class="btn-close"
             text-color="white"
             @click="_hide"
             color="red-2"></q-btn>
    </span>
    <div slot="body">
      <div class="merchant-icon-box">
        <vue-load-image>
          <img slot="image" :src="other.icon || '/static/img/clf/dispensary.png'" alt="">
          <q-spinner slot="preloader" size="30"></q-spinner>
        </vue-load-image>
      </div>
      <table class="form-table form-table-lg form-table-left pl-20">
        <tr>
          <th width="110">Invoice:</th>
          <td>{{ entity.id }}</td>
        </tr>
        <tr>
          <th>{{ other.type }} ID:</th>
          <td>{{ other.id }}</td>
        </tr>
      </table>
      <table class="form-table form-table-lg form-table-left mt-10 pl-20">
        <tr>
          <th width="50%">{{ entity.invoiceView ? 'Recipient' : 'Biller' }}:</th>
          <th>PO Number: <span class="optional">(OPTIONAL)</span></th>
        </tr>
        <tr>
          <td class="pt-0">{{ other.name }}</td>
          <td class="pt-0">{{ entity.poNumber }}</td>
        </tr>
        <tr>
          <th class="pt-20" colspan="2">Due Date:</th>
        </tr>
        <tr>
          <td class="pt-0" colspan="2">{{ entity.dueAt | date }}</td>
        </tr>
        <tr>
          <th class="pt-20">Item Description:</th>
          <th class="pt-20">Item Cost:</th>
        </tr>
        <tr v-for="item in entity.items" :key="item.id">
          <td class="pt-0">{{ item.description }}</td>
          <td class="pt-0">{{ item.amount | moneyFormat }}</td>
        </tr>
      </table>

      <div class="summary-box">
        <div class="font-16 mb-10">{{ entity.pristineAmount | moneyFormat }}</div>
        <div class="font-12 text-light">TRAN FEE: <span class="ml-10">{{ entity.tranFee | percent }}</span></div>
        <div class="font-12 text-light">TAX RATE: <span class="ml-10">{{ entity.taxRate | percent }}</span></div>
        <div class="font-22 mt-10 text-black heavy">
          {{ entity.status === 'paid' ? 'PAID' : 'DUE' }}:
          {{ entity.amount | moneyFormat }}
        </div>
      </div>
    </div>
    <template slot="buttons">
      <q-btn outline
             v-if="entity.invoiceView || entity.status === 'paid'"
             class="btn-gray-light"
             @click="_hide"
             label="Close"></q-btn>
      <q-btn outline
             v-else
             class="btn-red-light"
             @click="submit"
             label="PAY NOW"></q-btn>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import VueLoadImage from 'vue-load-image'

export default {
  name: 'dispensary-bill-due-dialog',
  mixins: [
    Singleton
  ],
  components: {
    VueLoadImage
  },
  data () {
    return {
      defaultEntity: {
        sender: {},
        recipient: {},
        items: []
      }
    }
  },
  computed: {
    other () {
      return (this.entity.invoiceView ? this.entity.recipient : this.entity.sender) || {}
    }
  },
  methods: {
    async submit () {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to pay the bill?',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await this.c.request(`${this.c.clf.apiPrefix()}/bills/pay`, 'post', this.entity)
        this.$q.loading.hide()
        if (resp.success) {
          this._hide()
          this.$root.$emit('reload-dispensary-bills-due')
          this.$root.$emit('reload-merchant-dues')
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
  .dispensary-bill-due-dialog {
    .modal-content {
      width: 650px;
    }

    .merchant-icon-box {
      text-align: center;
      margin: 5px auto 25px;

      img {
        height: 100px;
      }
    }

    .form-table {
      .optional {
        font-weight: 300;
        font-size: 12px;
        transform: scale(0.85);
        margin-left: 5px;
      }
    }

    .summary-box {
      width: 230px;
      margin: 20px 0 10px auto;
      text-align: center;
    }

    .modal-buttons {
      padding-right: 70px !important;
      padding-bottom: 40px !important;

      .q-btn {
        width: 140px;
      }
    }
  }

  @media (max-width: 414px) {
    .dispensary-bill-due-dialog {
      .form-table {
        .optional {
          display: none;
        }
      }

      .modal-buttons {
        padding-right: 20px !important;
        padding-bottom: 20px !important;
      }
    }
  }
</style>
