<template>
  <div>
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20"
                         default="all"
                         ref="dateRangeFilter"
                         @change="reload"></DateRangeFilter>

        <q-field label="Status">
          <q-select v-model="status"
                    @input="reload"
                    :options="statuses"></q-select>
        </q-field>

        <q-btn flat round
               @click="reload"
               class="mr-15"
               icon="mdi-refresh"></q-btn>
        <q-btn color="dark"
               @click="download"
               label="Export"/>
      </div>
    </div>
    <div class="page-content mt-20">
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               @request="request"
               separator="none"
               row-key="id">

        <q-tr slot="body" slot-scope="props" :props="props"
              :class="`tr_${props.row.status}`"
              @click.native="rowClick(props.row)" class="cursor-pointer">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="['sendAt'].includes(col.field)">
              {{ _.get(props.row, col.field) | date }}
            </template>
            <template v-else-if="['status'].includes(col.field)">
              <template v-if="props.row.overdue">
                <div class="text-red-8">
                  <q-icon name="warning" class="mr-3"></q-icon> Overdue
                </div>
              </template>
              <template v-else-if="props.row.status === 'paid'">
                <div class="font-14">{{ props.row.statusText }}</div>
                <div class="font-12 scale-85">{{ props.row.payAt | date('L LT') }}</div>
              </template>
              <template v-else>
                {{ props.row.statusText }}
              </template>
            </template>
            <template v-else-if="['amount'].includes(col.field)">
              <span class="text-red-8">-{{ _.get(props.row, col.field) | moneyFormat }}</span>
            </template>
            <template v-else-if="['dueAt'].includes(col.field)">
              <span :class="{'text-red-8': props.row.overdue}">
                {{ _.get(props.row, col.field) | date }}
              </span>
            </template>
            <template v-else-if="!col.field">
              <q-btn outline
                     v-if="props.row.status !== 'paid'"
                     @click="rowClick(props.row)"
                     class="btn-red-light mr-10 w-70"
                     label="PAY"></q-btn>
              <q-btn outline
                     @click="rowClick(props.row)"
                     class="btn-gray-light w-70"
                     label="VIEW"></q-btn>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <DueDialog></DueDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </div>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import BillListMixin from '../../../mixins/clf/BillListMixin'
import DateRangeFilter from '../../../components/DateRangeFilter'
import { EventHandlerMixin } from '../../../common'
import DueDialog from './due-dialog'

export default {
  name: 'due',
  mixins: [
    ListPageMixin,
    BillListMixin,
    EventHandlerMixin('reload-dispensary-bills-due')
  ],
  components: {
    DateRangeFilter,
    DueDialog
  },
  data () {
    return {
      title: 'Incoming Bills',
      downloadUrl: `${this.c.clf.apiPrefix()}/bills/export`,
      autoLoad: true,
      columns: [
        {
          name: 'p.sendAt',
          field: 'sendAt',
          label: 'Received',
          align: 'center',
          sortable: true
        }, {
          name: 'p.id',
          field: 'id',
          label: 'Invoice',
          align: 'center',
          sortable: true
        }, {
          name: 's.name',
          field: 'sender.name',
          label: 'Biller',
          align: 'center',
          sortable: true
        }, {
          name: 'p.status',
          field: 'status',
          label: 'Status',
          align: 'center',
          sortable: true
        }, {
          name: 'p.amount',
          field: 'amount',
          label: 'Amount',
          align: 'center',
          sortable: true
        }, {
          name: 'p.dueAt',
          field: 'dueAt',
          label: 'Due Date',
          align: 'center',
          sortable: true
        }, {
          field: '',
          label: '',
          align: 'right'
        }
      ],
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 3,
        page: 1
      },
      status: 'unpaid',
      apiEndpointName: 'bills'
    }
  },
  computed: {
    statuses () {
      return this.cc.clf.billStatuses.filter(v => {
        return v.label !== 'Draft'
      })
    }
  },
  methods: {
    rowClick (item) {
      this.$root.$emit('show-dispensary-bill-due-dialog', item)
    }
  }
}
</script>

<style lang="scss">

</style>
