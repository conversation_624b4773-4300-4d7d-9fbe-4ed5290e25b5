<template>
  <q-page id="clf__bills__index_page" class="clf_page">
    <Due></Due>
    <Invoice></Invoice>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import Due from './due'
import Invoice from './invoice'

export default {
  name: 'ClfPatientIndex',
  mixins: [
    ListPageMixin
  ],
  components: {
    Due,
    Invoice
  },
  data () {
    return {
      title: 'Bill Pay'
    }
  },
  mounted () {
    window.document.title = `${this.title} - ${this.adminPortalName}`
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #clf__bills__index_page {
  }
</style>
