<template>
  <q-dialog
    class="dispensary-bill-invoice-dialog clf-dialog clf-dialog-right-buttons admin-root"
    v-model="visible"
    :preventClose="true">
    <span slot="title">
      {{ entity.id ? 'Edit Invoice' : 'Add Invoice' }}

      <q-btn round
             icon="mdi-close"
             class="btn-close"
             text-color="white"
             @click="_hide"
             color="red-2"></q-btn>
    </span>
    <div slot="body">
      <table class="form-table form-table-lg form-table-left" v-if="entity.id">
        <tr>
          <th width="80">Invoice:</th>
          <td>{{ entity.id }}</td>
        </tr>
      </table>
      <table class="form-table form-table-lg form-table-left mt-10">
        <tr>
          <th width="50%">Recipient:</th>
          <th class="pl-20">PO Number: <span class="text-light font-12 scale-85 ml-5">(OPTIONAL)</span></th>
        </tr>
        <tr>
          <td class="pt-0">
            <q-select v-model="entity.recipient"
                      @blur="$v.entity.recipient.$touch"
                      :error="$v.entity.recipient.$error"
                      :options="vendors"></q-select>
          </td>
          <td class="pt-0 pl-20">
            <q-input v-model="entity.poNumber"></q-input>
          </td>
        </tr>
        <tr>
          <th class="pt-10" colspan="2">Due Date:</th>
        </tr>
        <tr>
          <td class="pt-0">
            <q-datetime v-model="entity.dueAt"
                        @blur="$v.entity.dueAt.$touch"
                        :error="$v.entity.dueAt.$error"
                        :format="cc.format.date"></q-datetime>
          </td>
          <td></td>
        </tr>
        <tr>
          <td colspan="2" class="text-center pv-15">
            <q-btn flat @click="addItem">
              <q-icon name="mdi-plus-circle"
                      class="font-30"
                      color="positive"></q-icon>
              <span class="ml-10 tt-none font-16 heavy">Add Item</span>
            </q-btn>
          </td>
        </tr>
        <tr class="title-row">
          <th>Item Description:</th>
          <th class="pl-20">Item Cost:</th>
        </tr>
        <tr v-for="(item, i) in entity.items"
            :key="i" class="item-row">
          <td>
            <q-input v-model="item.description"
                     :error="$v.entity.items.$each[i].description.$error"
                     @blur="$v.entity.items.$each[i].description.$touch">
              <q-autocomplete :debounce="0"
                              separator
                              :static-data="descriptionData"></q-autocomplete>
            </q-input>
          </td>
          <td class="pl-20 flex">
            <q-input v-model="item.amount"
                     type="number"
                     :min="0.01"
                     :error="$v.entity.items.$each[i].amount.$error"
                     @blur="$v.entity.items.$each[i].amount.$touch"
                     prefix="$"></q-input>

            <q-btn round
                   @click="removeItem(item)"
                   flat
                   class="ml-10"
                   color="red-8"
                   icon="mdi-minus-circle"></q-btn>
          </td>
        </tr>
      </table>

      <div class="summary-box">
        <div class="font-16 mb-10">
          <template v-if="entity.status">{{ entity.pristineAmount | moneyFormat }}</template>
          <template v-else>{{ pristineAmount | moneyFormat }}</template>
        </div>
        <div class="font-12 text-light">TRAN FEE: <span class="ml-10">{{ entity.tranFee | percent }}</span></div>
        <div class="font-12 text-light">TAX RATE: <span class="ml-10">{{ entity.taxRate | percent }}</span></div>
        <div class="font-22 mt-10 text-black heavy">
          DUE:
          <template v-if="entity.status">{{ entity.amount | moneyFormat }}</template>
          <template v-else>{{ amount | moneyFormat }}</template>
        </div>
      </div>
    </div>
    <template slot="buttons">
      <q-btn outline
             :disable="!removable"
             :class="{'hidden-only': !entity.id}"
             class="btn-red-light"
             @click="remove"
             label="DELETE">
        <q-tooltip v-if="!removable">Please delete all items first</q-tooltip>
      </q-btn>

      <q-btn outline
             class="btn-gray-light"
             @click="submit"
             label="SAVE"></q-btn>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { required, minValue } from 'vuelidate/lib/validators'
import { moneyMinorAmount, moneyMajorAmount, request, toSelectOptions } from '../../../common'
import _ from 'lodash'

export default {
  name: 'dispensary-bill-invoice-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        sender: {},
        items: [],
        tranFee: 0.01,
        taxRate: 0.0575
      },
      vendors: [],
      descriptions: []
    }
  },
  computed: {
    pristineAmount () {
      return this.entity.items.reduce((sum, item) => {
        return sum + moneyMinorAmount(item.amount || 0)
      }, 0)
    },
    amount () {
      return this.pristineAmount *
        (1 + this.entity.tranFee) *
        (1 + this.entity.taxRate)
    },
    descriptionData () {
      return {
        field: 'value',
        list: this.descriptions
      }
    },
    removable () {
      return !this.entity.status && this.entity.items.length <= 0
    }
  },
  validations: {
    entity: {
      recipient: {
        required
      },
      dueAt: {
        required
      },
      items: {
        $each: {
          description: {
            required
          },
          amount: {
            required,
            minValue: minValue(0.01)
          }
        }
      }
    }
  },
  methods: {
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return
      }
      this.$q.loading.show()
      const resp = await this.c.request(`${this.c.clf.apiPrefix()}/invoices/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this._hide()
        this.$root.$emit('reload-dispensary-bills-invoice')
        this.$root.$emit('reload-merchant-invoices')
      }
    },

    async remove () {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to delete this invoice?',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await this.c.request(`${this.c.clf.apiPrefix()}/invoices/delete`, 'delete', {
          id: this.entity.id
        })
        this.$q.loading.hide()
        if (resp.success) {
          this._hide()
          this.$root.$emit('reload-dispensary-bills-invoice')
          this.$root.$emit('reload-merchant-invoices')
        }
      }).catch(() => {})
    },

    show () {
      this.$v.$reset()
      if (this.entity.recipient && _.isObject(this.entity.recipient)) {
        this.entity.recipient = this.entity.recipient.id
      }
      if (this.entity.items.length <= 0) {
        this.addItem()
      } else {
        _.forEach(this.entity.items, item => {
          item.amount = moneyMajorAmount(item.amount, 'USD', true)
        })
      }
    },

    addItem () {
      this.entity.items.push({})
    },

    removeItem (item) {
      const index = this.entity.items.indexOf(item)
      if (index >= 0) {
        this.entity.items.splice(index, 1)
      }
    },

    async init () {
      const resp = await request(`${this.c.clf.apiPrefix()}/invoices/dialog/init/data`)
      if (resp.success) {
        this.vendors = toSelectOptions(resp.data.vendors)
        this.descriptions = toSelectOptions(resp.data.descriptions)
      }
    }
  },
  mounted () {
    this.init()
  }
}
</script>

<style lang="scss">
  .modal.clf-dialog.clf-dialog-right-buttons.dispensary-bill-invoice-dialog {
    .modal-content {
      width: 650px;
    }

    .summary-box {
      margin: 30px auto 10px auto;
      text-align: center;
    }

    .item-row {
      td {
        background-color: #F5F5F5;
        padding: 10px 15px 0;
      }

      &:last-of-type {
        td {
          padding-bottom: 10px;
        }
      }
    }

    .modal-buttons {
      justify-content: space-between !important;

      .q-btn {
        width: 140px;
      }
    }
  }

  @media (max-width: 414px) {
    .modal.clf-dialog.clf-dialog-right-buttons.dispensary-bill-invoice-dialog {
      .title-row {
        th.pl-20 {
          padding-left: 0 !important;
        }
      }

      .item-row {
        td {
          background-color: #F5F5F5;
          padding: 5px 5px 0 !important;

          &:last-of-type {
            position: relative;
            padding-left: 0 !important;

            .q-btn {
              position: absolute;
              top: 7px;
              right: 3px;
            }
          }
        }

        &:last-of-type {
          td {
            padding-bottom: 5px !important;
          }
        }
      }

      .modal-buttons {
        .q-btn {
          width: calc(50% - 10px);
        }

        .q-btn + .q-btn {
          margin-left: 15px;
        }
      }
    }
  }

  @media (max-width: 320px) {
    .modal.clf-dialog.clf-dialog-right-buttons.dispensary-bill-invoice-dialog {
      .item-row {
        td {
          &:last-of-type {
            .q-btn {
              right: -3px;
            }
          }
        }
      }
    }
  }
</style>
