<template>
  <div class="mt-30">
    <div class="page-header">
      <div class="title">
        {{ title }}

        <q-btn round
               icon="mdi-plus"
               @click="add"
               color="primary"></q-btn>
      </div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20"
                         default="all"
                         ref="dateRangeFilter"
                         @change="reload"></DateRangeFilter>

        <q-field label="Status">
          <q-select v-model="status"
                    @input="reload"
                    :options="cc.clf.billStatuses"></q-select>
        </q-field>

        <q-btn flat round
               @click="reload"
               class="mr-15"
               icon="mdi-refresh"></q-btn>
        <q-btn color="dark"
               @click="download"
               label="Export"/>
      </div>
    </div>
    <div class="page-content mt-20">
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               @request="request"
               separator="none"
               row-key="id">

        <q-tr slot="body" slot-scope="props" :props="props"
              :class="`tr_${props.row.status}`"
              @click.native="rowClick(props.row)" class="cursor-pointer">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="['createdAt'].includes(col.field)">
              {{ _.get(props.row, col.field) | date }}
            </template>
            <template v-else-if="['status'].includes(col.field)">
              <template v-if="props.row.overdue">
                <div class="text-red-8">
                  <q-icon name="warning" class="mr-3"></q-icon> Overdue
                </div>
              </template>
              <template v-else-if="props.row.status === 'paid'">
                <div class="font-14">{{ props.row.statusText }}</div>
                <div class="font-12 scale-85">{{ props.row.payAt | date('L LT') }}</div>
              </template>
              <template v-else>
                {{ props.row.statusText }}
              </template>
            </template>
            <template v-else-if="['amount'].includes(col.field)">
              <span class="text-positive">+{{ _.get(props.row, col.field) | moneyFormat }}</span>
            </template>
            <template v-else-if="['dueAt'].includes(col.field)">
              <span :class="{'text-red-8': props.row.overdue}">
                {{ _.get(props.row, col.field) | date }}
              </span>
            </template>
            <template v-else-if="!col.field">
              <q-btn outline
                     v-if="props.row.status !== 'paid'"
                     @click.stop="send(props.row)"
                     class="mr-10 w-70"
                     :class="props.row.status ? 'btn-gray-light' : 'btn-green-light'"
                     :label="props.row.status ? 'RESEND' : 'SEND'"></q-btn>
              <q-btn outline
                     v-if="!props.row.status"
                     @click="rowClick(props.row)"
                     class="btn-green-light w-70"
                     label="EDIT"></q-btn>
              <q-btn outline
                     v-else
                     @click="rowClick(props.row)"
                     class="btn-gray-light w-70"
                     label="VIEW"></q-btn>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <InvoiceDialog></InvoiceDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </div>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import BillListMixin from '../../../mixins/clf/BillListMixin'
import DateRangeFilter from '../../../components/DateRangeFilter'
import { EventHandlerMixin } from '../../../common'
import InvoiceDialog from './invoice-dialog'

export default {
  name: 'invoice',
  mixins: [
    ListPageMixin,
    BillListMixin,
    EventHandlerMixin('reload-dispensary-bills-invoice')
  ],
  components: {
    DateRangeFilter,
    InvoiceDialog
  },
  data () {
    return {
      title: 'Outgoing Invoices',
      downloadUrl: `${this.c.clf.apiPrefix()}/invoices/export`,
      autoLoad: true,
      columns: [
        {
          name: 'p.createdAt',
          field: 'createdAt',
          label: 'Created',
          align: 'center',
          sortable: true
        }, {
          name: 'p.id',
          field: 'id',
          label: 'Invoice',
          align: 'center',
          sortable: true
        }, {
          name: 'r.name',
          field: 'recipient.name',
          label: 'Recipient',
          align: 'center',
          sortable: true
        }, {
          name: 'p.status, p.dueAt',
          field: 'status',
          label: 'Status',
          align: 'center',
          sortable: true
        }, {
          name: 'p.amount',
          field: 'amount',
          label: 'Amount',
          align: 'center',
          sortable: true
        }, {
          name: 'p.dueAt',
          field: 'dueAt',
          label: 'Due Date',
          align: 'center',
          sortable: true
        }, {
          field: '',
          label: '',
          align: 'right'
        }
      ],
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 10,
        page: 1
      },
      status: 'unpaid',
      apiEndpointName: 'invoices'
    }
  },
  methods: {
    rowClick (item) {
      if (!item.status) {
        return this.$root.$emit('show-dispensary-bill-invoice-dialog', item)
      }
      item.invoiceView = true
      this.$root.$emit('show-dispensary-bill-due-dialog', item)
    },

    send (item) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to send this invoice?',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await this.c.request(`${this.c.clf.apiPrefix()}/invoices/send`, 'post', item)
        this.$q.loading.hide()
        if (resp.success) {
          this.$root.$emit('reload-dispensary-bills-invoice')
        }
      }).catch(() => {})
    },

    add () {
      this.$root.$emit('show-dispensary-bill-invoice-dialog', {})
    }
  }
}
</script>

<style lang="scss">

</style>
