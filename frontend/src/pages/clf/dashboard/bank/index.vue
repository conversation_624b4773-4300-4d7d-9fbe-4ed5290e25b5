<template>
  <Dashboard class="clf__dashboard_bank__index_page">
    <template slot="filters">
      <q-field label="Group" class="mr-20 auto-field">
        <q-select v-model="group" :options="groups"></q-select>
      </q-field>
    </template>

    <template slot-scope="{ params }">
      <div class="row gutter-sm">
        <div class="col-4">
          <LineCard :params="params"
                   title="Dispensaries"
                   api="dispensaries"
                   summary-filter="number"
                   report-url="#/a/clf/merchants/dispensary"
                   :formatter="dispensariesFormatter"></LineCard>
        </div>
        <div class="col-4">
          <LineCard :params="params"
                   :summary="false"
                   title="Vendors"
                   api="vendors"
                   summary-filter="number"
                   report-url="#/a/clf/vendors"
                   :formatter="vendorsFormatter"></LineCard>
        </div>
        <div class="col-4">
          <LineCard :params="params"
                    title="Patients"
                    api="patients"
                    summary-filter="number"
                    report-url="#/a/clf/patients"
                    :formatter="patientsFormatter"></LineCard>
        </div>
        <div class="col-4">
          <LineCard :params="params"
                    title="Sales Volume"
                    api="sales"
                    summary-filter="moneyFormat"
                    report-url="#/a/clf/report/transactions"
                    :formatter="salesFormatter"></LineCard>
        </div>
        <div class="col-4">
          <BarCard :params="params"
                   title="Taxes & Fees"
                   api="fees"
                   summary-filter="moneyFormat"
                   color="#DC4054"
                   report-url="#/a/clf/report/fees"
                   :formatter="feesFormatter"></BarCard>
        </div>
      </div>
    </template>
  </Dashboard>
</template>

<script>
import Dashboard from '../common/index'
import LineCard from '../common/line'
import BarCard from '../common/bar'

export default {
  name: 'ClfBankDashboard',
  components: {
    Dashboard,
    LineCard,
    BarCard
  },
  data () {
    return {
      employeeChartOptions: {
        grid: {
          bottom: 60
        },
        xAxis: {
          axisLabel: {
            rotate: 30,
            interval: 0,
            showMinLabel: true,
            showMaxLabel: true
          }
        }
      },
      group: null,
      groups: [
        {
          label: 'All (Patients, Dispensaries and Vendors)',
          value: null
        }
      ]
    }
  },
  methods: {
    dispensariesFormatter (params) {
      params = params[0]
      return `${params.name}<br/>${this.$options.filters.number(params.value)} Dispensaries`
    },
    vendorsFormatter (params) {
      params = params[0]
      return `${params.name}<br/>${this.$options.filters.number(params.value)} Vendors`
    },
    salesFormatter (params) {
      params = params[0]
      return `${params.name}: ${this.c.moneyFormat(params.value, 'USD', false, false)}`
    },
    patientsFormatter (params) {
      params = params[0]
      return `${params.name}<br/>${this.$options.filters.number(params.value)} Patients`
    },
    feesFormatter (params) {
      params = params[0]
      return `<div class="chart-tooltip-box" style="background: #DC4054"></div> ${params.name}<br/>${this.c.moneyFormat(params.value, 'USD', false, false)}`
    }
  }
}
</script>

<style lang="scss">
</style>
