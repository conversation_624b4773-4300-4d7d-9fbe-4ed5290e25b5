<template>
  <q-page id="clf__report_account_balance__index_page" class="clf_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20"
                         default="all"
                         ref="dateRangeFilter"
                         @change="reload"></DateRangeFilter>

        <q-btn flat round
               @click="reload"
               class="mr-15"
               icon="mdi-refresh"></q-btn>
        <q-btn color="dark"
               @click="download"
               label="Export"/>
      </div>
    </div>
    <div class="page-content mt-20">
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               @request="request"
               separator="none"
               row-key="id">
        <q-tr slot="body" slot-scope="props" :props="props"
              :class="`tr_${props.row.status}`"
              @click.native="rowClick(props.row)" class="cursor-pointer">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="['balance'].includes(col.field)">
              <span v-if="props.row[col.field]">{{ props.row[col.field] | moneyFormat }}</span>
            </template>
            <template v-else-if="['amount'].includes(col.field)">
              <span :class="props.row.amount >= 0 ? 'text-positive' : 'text-red-8'">
                {{ props.row[col.field] > 0 ? '+' : '' }}{{ props.row[col.field] | moneyFormat }}
              </span>
            </template>
            <template v-else-if="['date'].includes(col.field)">
              <span v-if="props.row[col.field]">{{ props.row[col.field] | date }}</span>
            </template>
            <template v-else-if="['description'].includes(col.field)">
              <div class="font-14">{{ props.row[col.field] }}</div>
              <div v-if="props.row.subDescription" class="font-12">{{ props.row.subDescription }}</div>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import DateRangeFilter from '../../../../components/DateRangeFilter'

export default {
  name: 'clf-dashboard-balance-report',
  mixins: [
    ListPageMixin
  ],
  components: {
    DateRangeFilter
  },
  data () {
    return {
      title: 'Account Balance Report',
      downloadUrl: `${this.c.clf.apiPrefix()}/balance-report/export`,
      requestUrl: `${this.c.clf.apiPrefix()}/balance-report`,
      autoLoad: true,
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 20,
        page: 1
      },
      columns: [
        {
          name: 't.dateTime',
          field: 'date',
          label: 'Date',
          align: 'left',
          sortable: true
        }, {
          name: '___description',
          field: 'description',
          label: 'Description',
          align: 'left',
          sortable: true
        }, {
          name: '___type',
          field: 'type',
          label: 'Type',
          align: 'left',
          sortable: true
        }, {
          name: 't.amount',
          field: 'amount',
          label: 'Amount',
          align: 'right',
          sortable: true
        }, {
          name: 't.producerBalanceAfter',
          field: 'balance',
          label: 'Balance',
          align: 'right',
          sortable: true
        }
      ]
    }
  },
  methods: {
    getQueryParams () {
      return this.$refs.dateRangeFilter.params()
    }
  }
}
</script>

<style lang="scss">
</style>
