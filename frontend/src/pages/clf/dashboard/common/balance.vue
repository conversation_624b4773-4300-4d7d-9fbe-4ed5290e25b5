<template>
  <Card class="clf-dashboard-balance"
        :title="title"
        @reload="reload"
        :report-url="reportUrl">
    <div class="row summary">
      <div class="col-8">{{ data.balance | moneyFormat }}</div>
      <div class="col-4" :class="c.trendColor(data.balanceDelta)">
        <q-icon :name="c.trendIcon(data.balanceDelta)"></q-icon>
        {{ data.balanceDelta | percent(1) }}
      </div>
    </div>

    <div class="row mt-10 mb-20 text-center" v-if="loadable">
      <div class="col pr-15">
        <q-btn outline
               @click="load"
               class="btn-green-light full-width"
               label="LOAD"></q-btn>
      </div>
      <div class="col pl-15">
        <q-btn outline
               @click="unload"
               class="btn-red-light full-width"
               label="UNLOAD"></q-btn>
      </div>
    </div>

    <div class="mb-auto" :class="loadable ? '' : 'mt-15'">
      <h4>Key Metrics</h4>
      <table class="line-table">
        <tr v-for="m in data.metrics" :key="m.name">
          <td>{{ m.name }}</td>
          <td class="text-center heavy text-dark font-16">{{ m.value }}</td>
          <td class="text-right" :class="c.trendColor(m.delta)">
            <q-icon :name="c.trendIcon(m.delta)"></q-icon>
            {{ m.delta | percent(1) }}
          </td>
        </tr>
      </table>
    </div>

    <load-funds-dialog v-if="loadable"
                       ref="loadFundsDialog"></load-funds-dialog>
  </Card>
</template>

<script>
import Card from './card'
import LoadFundsDialog from './load-funds-dialog'
import { request } from '../../../../common'

export default {
  name: 'ClfDashboardBalance',
  components: {
    Card,
    LoadFundsDialog
  },
  props: {
    title: {
      type: String,
      default: 'Account Balance'
    },
    api: {
      type: String,
      default: 'balance'
    },
    loadable: {
      type: Boolean,
      default: true
    },
    params: {
      type: Object,
      required: true
    },
    reportUrl: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      data: {
        metrics: []
      }
    }
  },
  methods: {
    async reload (cb) {
      const resp = await request(`${this.c.clf.apiPrefix()}/dashboard/${this.api}`, 'get', this.params)
      cb && cb()
      if (resp.success) {
        this.data = resp.data
      }

      if (this.loadable) {
        this.$refs.loadFundsDialog.init()
      }
    },
    load () {
      if (this.$c.clfDevice()) {
        return this.$root.$emit('show-mobile-clf-wallet-load-modal', { type: 'Load' })
      }
      this.$root.$emit('show-load-funds-dialog')
    },
    unload () {
      if (this.$c.clfDevice()) {
        return this.$root.$emit('show-mobile-clf-wallet-load-modal', { type: 'Unload' })
      }
      this.$root.$emit('show-load-funds-dialog', {
        type: 'unload'
      })
    }
  }
}
</script>

<style lang="scss">
  .clf-dashboard-balance {
    .line-table {
      td.text-center.text-dark {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 200px;
      }
    }
  }
</style>
