<template>
  <q-card class="clf-dispensary-dashboard-card common-dashboard-card">
    <q-card-title>
      <span>{{ title }}</span>
      <a :href="reportUrl">View report</a>
    </q-card-title>
    <q-card-main>
      <slot></slot>

      <q-btn round flat
             size="xs"
             @click="reload"
             class="btn-reload"
             icon="mdi-refresh"></q-btn>

      <q-inner-loading :visible="loading">
        <q-spinner size="30"></q-spinner>
      </q-inner-loading>
    </q-card-main>
  </q-card>
</template>

<script>
import { EventHandlerMixin } from '../../../../common'
import ChartCardMixin from '../../../../mixins/ChartCardMixin'

export default {
  name: 'ClfDispensaryDashboardCard',
  mixins: [
    ChartCardMixin,
    EventHandlerMixin('admin-reload-dashboard'),
    EventHandlerMixin('admin-reload-dashboard-card', 'reloadCard')
  ],
  props: [
    'title',
    'reportUrl'
  ],
  data () {
    return {
      loading: false
    }
  },
  methods: {
    reload () {
      this.loading = true
      this.$emit('reload', () => {
        this.loading = false
      })
    },
    reloadCard (name) {
      if (this.title === name) {
        this.reload()
      }
    }
  }
}
</script>

<style lang="scss">
  .clf-dispensary-dashboard-card {
  }
</style>
