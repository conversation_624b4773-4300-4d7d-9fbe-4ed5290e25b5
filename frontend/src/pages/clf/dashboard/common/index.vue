<template>
  <q-page class="clf__dashboard__index_page graph__dashboard__index_page clf_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <slot name="filters"></slot>

        <DateRangeFilter class="mr-20"
                         default="year"
                         :ranges="ranges"
                         ref="dateRangeFilter"
                         @change="reload"></DateRangeFilter>

        <q-btn @click="reload"
               v-if="!$c.clfDevice()"
               color="black"
               label="RELOAD"></q-btn>
      </div>
    </div>
    <div class="page-content mt-20">
      <slot :params="params"></slot>
    </div>
  </q-page>
</template>

<script>
import DateRangeFilter from '../../../../components/DateRangeFilter'

export default {
  name: 'ClfDashboardIndex',
  components: {
    DateRangeFilter
  },
  data () {
    return {
      isMounted: false,
      ranges: [
        {
          label: 'This week',
          value: 'week'
        },
        {
          label: 'This month',
          value: 'month'
        },
        {
          label: 'This year',
          value: 'year'
        },
        {
          label: 'Last month',
          value: 'last_month'
        },
        {
          label: 'Last year',
          value: 'last_year'
        },
        {
          label: 'Custom Range',
          value: 'custom_range'
        }
      ]
    }
  },
  computed: {
    organization () {
      return this.$store.state.User.organization || {}
    },
    title () {
      let name = this.organization.name
      if (this.sa) {
        name = 'Admin Portal'
      }
      return name ? `${name} - Dashboard` : 'Dashboard'
    },
    params () {
      return this.isMounted ? this.$refs.dateRangeFilter.params() : {}
    },
    sa () {
      return this.$route.fullPath.startsWith('/a/clf/sa')
    }
  },
  methods: {
    reload () {
      this.$root.$emit('admin-reload-dashboard')
    }
  },
  mounted () {
    this.isMounted = true
    this.$nextTick(() => {
      this.reload()
    })
  }
}
</script>

<style lang="scss">
</style>
