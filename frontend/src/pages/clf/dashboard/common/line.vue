<template>
  <Card class="clf-dashboard-line-card"
        :title="title"
        @reload="reload"
        :report-url="reportUrl">
    <div class="row summary">
      <div class="col-8">{{ $options.filters[summaryFilter](data.total) }}</div>
      <div class="col-4" :class="c.trendColor(data.delta)">
        <q-icon :name="c.trendIcon(data.delta)"></q-icon>
        {{ data.delta | percent(1) }}
      </div>
    </div>
    <div class="chart-container"></div>
  </Card>
</template>

<script>
import Card from './card'
import echarts from 'echarts'
import _ from 'lodash'
import { request } from '../../../../common'

export default {
  name: 'ClfDashboardLineCard',
  components: {
    Card
  },
  props: {
    title: {
      type: String,
      required: true
    },
    api: {
      type: String,
      required: true
    },
    params: {
      type: Object,
      required: true
    },
    formatter: {
      type: Function,
      required: true
    },
    summaryFilter: {
      type: String,
      required: true
    },
    reportUrl: {
      type: String,
      required: true
    },
    chartOptions: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      data: {
      }
    }
  },
  methods: {
    async reload (cb) {
      const resp = await request(`${this.c.clf.apiPrefix()}/dashboard/${this.api}`, 'get', this.params)
      cb()
      if (resp.success) {
        this.data = resp.data
        this.initChart()
      }
    },
    initChart () {
      const keys = this.data.parts.keys
      const chart = echarts.init(this.$el.querySelector('.chart-container'), 'clf')
      const options = _.defaultsDeep(_.cloneDeep(this.chartOptions), {
        xAxis: {
          data: keys,
          axisLabel: {
            interval: keys.length >= 8 ? 'auto' : 0
          }
        },
        series: [{
          data: this.data.parts.values,
          type: 'line',
          lineStyle: {
            color: '#ccc'
          }
        }],
        tooltip: {
          formatter: this.formatter
        }
      }, this.cc.clf.chart)
      chart.setOption(options)
    }
  }
}
</script>

<style lang="scss">
  .clf-dashboard-line-card {
  }
</style>
