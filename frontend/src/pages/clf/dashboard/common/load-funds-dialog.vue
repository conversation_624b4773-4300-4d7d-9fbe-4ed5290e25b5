<template>
  <q-dialog
    class="load-funds-dialog clf-dialog admin-root"
    v-model="visible"
    :preventClose="true">
    <span slot="title">
      <template v-if="type === 'load'">Load Funds</template>
      <template v-else>Unload Funds</template>

      <q-btn round
             icon="mdi-close"
             class="btn-close"
             text-color="white"
             @click="_hide"
             color="red-2"></q-btn>
    </span>
    <div slot="body">
      <div class="form" v-show="step === 1">
        <q-field label-width="12"
                 :label="type === 'load' ? 'Deposit Amount:' : 'Withdraw Amount:'">
          <q-input type="number"
                   class="w-150"
                   :prefix="'$'"
                   :step="1"
                   :min="0.01"
                   @input="$v.amount.$touch"
                   @blur="formatAmount"
                   v-model="amount"></q-input>
        </q-field>
        <q-field label-width="12"
                 :label="type === 'load' ? 'From Bank:' : 'To:'">
          <q-select v-model="card"
                    :options="cards"
                    placeholder="[Select bank account]"
                    @input="$v.card.$touch"></q-select>
          <div class="text-right mt-3" v-show="card">
            <a :href="$c.deviceMode === 'clf' ? '#/clf/banks' : '#/a/i/clf__bank'"
               class="blue-link">My Bank Settings</a>
          </div>
        </q-field>
      </div>
      <div v-if="step === 2">
        <h3 v-if="type === 'load'">Are you sure that you want to load funds to your account?</h3>
        <h3 v-else>Are you sure that you want to unload funds from your account?</h3>
        <table class="table form-table form-table-lg">
          <tr>
            <th v-if="type === 'load'">Deposit:</th>
            <th v-else>Withdraw:</th>
            <td v-text="amountFormat"></td>
          </tr>
          <tr>
            <th v-if="type === 'load'">To Account:</th>
            <th v-else>From Account:</th>
            <td v-text="account"></td>
          </tr>
          <tr>
            <th v-if="type === 'load'">From Bank:</th>
            <th v-else>To Bank:</th>
            <td v-text="cardName"></td>
          </tr>
        </table>
        <div class="bottom-tip" v-if="type === 'load'">Allow 3-5 days for funds to deposit from your bank.</div>
        <div class="bottom-tip" v-else>Allow 3-5 days for funds to deposit to your bank.</div>
      </div>
    </div>
    <template slot="buttons">
      <q-btn  label="Load Funds"
              outline
              v-show="step === 1 && type === 'load'"
              :disabled="$v.$invalid"
              class="btn-green-light"
              @click="next"></q-btn>
      <q-btn  label="Unload Funds"
              outline
              v-show="step === 1 && type === 'unload'"
              :disabled="$v.$invalid"
              class="btn-red-light"
              @click="next"></q-btn>

      <q-btn  label="Yes"
              outline
              v-show="step === 2"
              class="btn-green-light"
              @click="next"></q-btn>
      <q-btn  label="No"
              outline
              v-show="step === 2"
              class="btn-red-light"
              @click="_hide"></q-btn>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { required } from 'vuelidate/lib/validators'
import { notifySuccess, request, toSelectOptions } from '../../../../common'
import accounting from 'accounting'
import _ from 'lodash'

export default {
  name: 'load-funds-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      step: 1,
      type: 'load',
      amount: null,
      account: '',
      cards: [],
      card: null
    }
  },
  computed: {
    amountFormat () {
      return accounting.formatMoney(this.amount)
    },
    cardName () {
      if (!this.card) {
        return ''
      }
      const selected = _.find(this.cards, { value: this.card })
      return selected.label
    }
  },
  validations: {
    amount: {
      required
    },
    card: {
      required
    }
  },
  methods: {
    formatAmount () {
      this.amount = accounting.formatMoney(this.amount, '', 2, '')
    },
    async next () {
      if (this.step === 1) {
        this.step += 1
        return
      }
      const data = {
        amount: this.amount
      }
      this.$q.loading.show()
      const resp = await request(`/clf/load/${this.type}/${this.card}`, 'post', data)
      if (resp.success) {
        this.$q.loading.hide()
        notifySuccess()
        this._hide()
        this.$root.$emit('admin-reload-dashboard-card', 'Account Balance')
      }
    },
    show (arg) {
      this.type = arg.type || 'load'
      this.step = 1
      this.amount = null
      this.card = null
    },
    async init () {
      const resp = await request(`/clf/merchant/dashboard/load-funds-data`)
      if (resp.success) {
        this.cards = toSelectOptions(resp.data.cards)
        this.account = `${this.c.clf.orgName()} (${resp.data.accountNumber})`
      }
    }
  },
  mounted () {
    this.init()
  }
}
</script>

<style lang="scss">
  .load-funds-dialog {
    .modal-content {
      width: 400px;

      a.blue-link {
        text-decoration: none;
        color: #295CAC !important;

        &:hover {
          text-decoration: underline;
        }
      }

      h3 {
        margin: -5px auto 20px;
        font-size: 22px;
        line-height: 1.5em;
        font-weight: 300;
        text-align: center;
      }

      .bottom-tip {
        margin: 20px auto;
        font-size: 15px;
        font-weight: 300;
        text-align: center;
      }
    }
  }
</style>
