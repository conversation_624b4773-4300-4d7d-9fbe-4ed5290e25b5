<template>
  <Card class="clf-dashboard-pie-card"
        :title="title"
        @reload="reload"
        :report-url="reportUrl">
    <div class="row summary">
      <div class="col-8 text-red-8">{{ $options.filters[summaryFilter](data.total) }}</div>
    </div>
    <div class="chart-container"></div>
  </Card>
</template>

<script>
import Card from './card'
import echarts from 'echarts'
import _ from 'lodash'
import { request } from '../../../../common'

export default {
  name: 'ClfDashboardPieCard',
  components: {
    Card
  },
  props: {
    title: {
      type: String,
      required: true
    },
    api: {
      type: String,
      required: true
    },
    params: {
      type: Object,
      required: true
    },
    formatter: {
      type: Function,
      required: true
    },
    summaryFilter: {
      type: String,
      required: true
    },
    reportUrl: {
      type: String,
      required: true
    },
    chartOptions: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      data: {
      }
    }
  },
  methods: {
    async reload (cb) {
      const resp = await request(`${this.c.clf.apiPrefix()}/dashboard/${this.api}`, 'get', this.params)
      cb()
      if (resp.success) {
        this.data = resp.data
        this.initChart()
      }
    },
    initChart () {
      const chart = echarts.init(this.$el.querySelector('.chart-container'), 'clf')
      const options = _.defaultsDeep(_.cloneDeep(this.chartOptions), {
        grid: {
          left: 0,
          right: 0,
          top: 0,
          bottom: 0
        },
        tooltip: {
          show: false
        },
        xAxis: {
          show: false
        },
        yAxis: {
          show: false
        },
        series: [
          {
            type: 'pie',
            radius: ['70%', '90%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                backgroundColor: '#fff',
                borderColor: '#666',
                borderRadius: 4,
                shadowColor: 'rgba(0, 0, 0, 0.8)',
                shadowBlur: 5,
                padding: [6, 8],
                lineHeight: 22,
                fontSize: 13,
                formatter: this.formatter,
                rich: {
                  value: {
                    color: '#333'
                  },
                  name: {
                    fontSize: 14
                  }
                }
              }
            },
            labelLine: {
              normal: {
                show: false
              }
            },
            data: this.data.parts
          }
        ]
      }, this.cc.clf.chart)
      chart.setOption(options)
    }
  }
}
</script>

<style lang="scss">
  .clf-dashboard-pie-card {
  }
</style>
