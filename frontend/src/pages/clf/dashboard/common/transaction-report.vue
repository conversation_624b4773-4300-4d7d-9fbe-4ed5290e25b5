<template>
  <q-page id="clf__report_transaction__index_page" class="clf_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20"
                         default="all"
                         ref="dateRangeFilter"
                         @change="reload"></DateRangeFilter>

        <q-btn flat round
               @click="reload"
               class="mr-15"
               icon="mdi-refresh"></q-btn>
        <q-btn color="dark"
               @click="download"
               label="Export"/>

        <q-btn color="primary"
               icon="mdi-plus"
               class="ml-15"
               v-if="!$route.fullPath.startsWith('/a/clf/sa/')"
               @click="$router.push('/a/clf/transactions/new')"
               label="New Payment"></q-btn>
      </div>
    </div>
    <div class="page-content mt-20">
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               @request="request"
               separator="none"
               row-key="id">
        <q-tr slot="body" slot-scope="props" :props="props"
              :class="`tr_${props.row.status}`"
              @click.native="rowClick(props.row)" class="cursor-pointer">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="['balanceBefore', 'balanceAfter'].includes(col.field)">
              <span v-if="props.row[col.field]">{{ props.row[col.field] | moneyFormat }}</span>
            </template>
            <template v-else-if="['amount'].includes(col.field)">
              <span :class="props.row.amount >= 0 ? 'text-positive' : 'text-red-8'">
                {{ props.row[col.field] > 0 ? '+' : '' }}{{ props.row[col.field] | moneyFormat }}
              </span>
            </template>
            <template v-else-if="['date'].includes(col.field)">
              <span v-if="props.row[col.field]">{{ props.row[col.field] | date }}</span>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <DetailDialog class="center-box-modal admin-root clf-dialog"
                  :reverse="true"></DetailDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import DateRangeFilter from '../../../../components/DateRangeFilter'
import DetailDialog from '../../../mobile/clf/wallet/detail'

export default {
  name: 'clf-dashboard-transaction-report',
  mixins: [
    ListPageMixin
  ],
  components: {
    DateRangeFilter,
    DetailDialog
  },
  data () {
    const columns = [
      {
        name: 't.dateTime',
        field: 'date',
        label: 'Date',
        align: 'left',
        sortable: true
      }, {
        name: 't.id',
        field: 'tranId',
        label: 'Tran ID',
        align: 'left',
        sortable: true
      }, {
        name: 'u.firstName, u.lastName',
        field: 'patient',
        label: 'Patient',
        align: 'left',
        sortable: true
      }, {
        name: 'type.name',
        field: 'type',
        label: 'Tran Type',
        align: 'left',
        sortable: true
      }, {
        name: 'me.name',
        field: 'employee',
        label: 'Employee',
        align: 'left',
        sortable: true
      }, {
        name: 't.amount',
        field: 'amount',
        label: 'Amount',
        align: 'right',
        sortable: true
      }, {
        name: 't.availableBalanceBefore',
        field: 'balanceBefore',
        label: 'Balance Before',
        align: 'right',
        sortable: true
      }, {
        name: 't.availableBalanceAfter',
        field: 'balanceAfter',
        label: 'Balance After',
        align: 'right',
        sortable: true
      }
    ]

    if (this.$route.fullPath.startsWith('/a/clf/sa/')) {
      columns.unshift({
        name: 'm.name',
        field: 'merchant',
        label: 'Merchant',
        align: 'left',
        sortable: true
      })
    }

    return {
      title: 'Transactions Report',
      downloadUrl: `${this.c.clf.apiPrefix()}/transaction-report/export`,
      requestUrl: `${this.c.clf.apiPrefix()}/transaction-report`,
      autoLoad: true,
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 20,
        page: 1
      },
      columns
    }
  },
  methods: {
    getQueryParams () {
      return this.$refs.dateRangeFilter.params()
    },
    rowClick (entity) {
      entity.id = entity.tranId
      this.$root.$emit('show-mobile-clf-wallet-detail-modal', entity)
    }
  }
}
</script>

<style lang="scss">
</style>
