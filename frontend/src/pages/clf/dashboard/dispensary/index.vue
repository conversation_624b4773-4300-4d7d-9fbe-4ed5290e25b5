<template>
  <Dashboard class="clf__dashboard_dispensary__index_page">
    <template slot-scope="{ params }">
      <div class="row gutter-sm">
        <div class="col-4">
          <BalanceCard :params="params"
                       :report-url="$c.clfDevice() ? '#/clf/wallet' : '#/a/clf/report/account-balance'"></BalanceCard>
        </div>
        <div class="col-4">
          <LineCard :params="params"
                    title="Sales Volume"
                    api="sales"
                    summary-filter="moneyFormat"
                    :report-url="$c.clfDevice() ? '#/clf/dispensary/dashboard/transactions' : '#/a/clf/report/transactions'"
                    :formatter="salesFormatter"></LineCard>
        </div>
        <div class="col-4">
          <BarCard :params="params"
                   :summary="false"
                   title="Top Employees"
                   api="employees"
                   summary-filter="moneyFormat"
                   :report-url="$c.clfDevice() ? '#/clf/merchant/employees' : '#/a/clf/employees'"
                   :chart-options="employeeChartOptions"
                   :formatter="employeesFormatter"></BarCard>
        </div>
        <div class="col-4">
          <LineCard :params="params"
                    title="Patient Count"
                    api="patients"
                    summary-filter="number"
                    :report-url="$c.clfDevice() ? '#/clf/dispensary/patients' : '#/a/clf/patients'"
                    :formatter="patientsFormatter"></LineCard>
        </div>
        <div class="col-4">
          <PieCard :params="params"
                   title="Outstanding Bills"
                   api="bills"
                   summary-filter="moneyFormat"
                   :report-url="$c.clfDevice() ? '#/clf/merchant/invoice' : '#/a/clf/bills'"
                   :formatter="billsFormatter"></PieCard>
        </div>
        <div class="col-4">
          <BarCard :params="params"
                   title="Taxes & Fees"
                   api="fees"
                   summary-filter="moneyFormat"
                   color="#DC4054"
                   :report-url="$c.clfDevice() ? '#/clf/merchant/dashboard/fees' : '#/a/clf/report/fees'"
                   :formatter="feesFormatter"></BarCard>
        </div>
      </div>
    </template>

    <template v-if="$c.clfDevice()">
      <TransactionReport></TransactionReport>
    </template>
  </Dashboard>
</template>

<script>
import Dashboard from '../common/index'
import BalanceCard from '../common/balance'
import LineCard from '../common/line'
import BarCard from '../common/bar'
import PieCard from '../common/pie'
import TransactionReport from '../../../mobile/clf/dispensary/dashboard/transaction-report'

export default {
  name: 'ClfDispensaryDashboard',
  components: {
    Dashboard,
    BalanceCard,
    LineCard,
    BarCard,
    PieCard,
    TransactionReport
  },
  data () {
    return {
      employeeChartOptions: {
        grid: {
          bottom: 60
        },
        xAxis: {
          axisLabel: {
            rotate: 30
          }
        }
      }
    }
  },
  methods: {
    salesFormatter (params) {
      params = params[0]
      return `${params.name}: ${this.c.moneyFormat(params.value, 'USD', false, false)}`
    },
    patientsFormatter (params) {
      params = params[0]
      return `${params.name}<br/>${this.$options.filters.number(params.value)} Patients`
    },
    employeesFormatter (params) {
      params = params[0]
      return `${params.name}<br/>${this.$options.filters.number(params.value)} Transactions`
    },
    feesFormatter (params) {
      params = params[0]
      return `<div class="chart-tooltip-box" style="background: #DC4054"></div> ${params.name}<br/>${this.c.moneyFormat(params.value, 'USD', false, false)}`
    },
    billsFormatter (params) {
      return `{value|${this.c.moneyFormat(params.data.value, 'USD', false, false)}}\n{name|${params.data.name}}`
    }
  }
}
</script>

<style lang="scss">
</style>
