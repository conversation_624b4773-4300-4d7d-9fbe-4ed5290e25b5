<template>
  <Dashboard class="clf__dashboard_sa__index_page">
    <template slot-scope="{ params }">
      <div class="row gutter-sm">
        <div class="col-4">
          <LineCard :params="params"
                    title="All Dispensaries"
                    api="dispensaries"
                    summary-filter="number"
                    :report-url="'#/a/clf/sa/merchants/dispensary'"
                    :formatter="dispensariesFormatter"></LineCard>
        </div>
        <div class="col-4">
          <LineCard :params="params"
                    title="All Vendors"
                    api="vendors"
                    summary-filter="number"
                    :report-url="'#/a/clf/sa/merchants/vendor'"
                    :formatter="vendorsFormatter"></LineCard>
        </div>
        <div class="col-4">
          <LineCard :params="params"
                    title="All Patients"
                    api="patients"
                    summary-filter="number"
                    :report-url="'#/a/clf/sa/patients'"
                    :formatter="patientsFormatter"></LineCard>
        </div>
        <div class="col-4">
          <PieCard :params="params"
                   title="Merchant Balance"
                   api="balances"
                   summary-filter="moneyFormat"
                   :report-url="'#/a/clf/sa/merchants/dispensary'"
                   :formatter="balanceFormatter"></PieCard>
        </div>
        <div class="col-4">
          <LineCard :params="params"
                   title="Sales Volume"
                   api="sales"
                   summary-filter="moneyFormat"
                   :report-url="'#/a/clf/sa/report/transactions'"
                   :formatter="salesFormatter"></LineCard>
        </div>
        <div class="col-4">
          <BarCard :params="params"
                   title="Taxes & Fees"
                   api="fees"
                   summary-filter="moneyFormat"
                   :report-url="'#/a/clf/sa/report/fees'"
                   :formatter="feesFormatter"></BarCard>
        </div>
      </div>
    </template>
  </Dashboard>
</template>

<script>
import Dashboard from '../common/index'
import LineCard from '../common/line'
import BarCard from '../common/bar'
import PieCard from '../common/pie'

export default {
  name: 'ClfSaDashboard',
  components: {
    Dashboard,
    LineCard,
    BarCard,
    PieCard
  },
  methods: {
    dispensariesFormatter (params) {
      params = params[0]
      return `${params.name}<br/>${this.$options.filters.number(params.value)} Dispensaries`
    },
    vendorsFormatter (params) {
      params = params[0]
      return `${params.name}<br/>${this.$options.filters.number(params.value)} Vendors`
    },
    patientsFormatter (params) {
      params = params[0]
      return `${params.name}<br/>${this.$options.filters.number(params.value)} Patients`
    },
    feesFormatter (params) {
      params = params[0]
      return `<div class="chart-tooltip-box" style="background: #DC4054"></div> ${params.name}<br/>${this.c.moneyFormat(params.value, 'USD', false, false)}`
    },
    salesFormatter (params) {
      params = params[0]
      return `${params.name}: ${this.c.moneyFormat(params.value, 'USD', false, false)}`
    },
    balanceFormatter (params) {
      return `{value|${this.c.moneyFormat(params.data.value, 'USD', false, false)}}\n{name|${params.data.name}}`
    }
  }
}
</script>

<style lang="scss">
</style>
