<template>
  <Dashboard class="clf__dashboard_vendor__index_page">
    <template slot-scope="{ params }">
      <div class="row gutter-sm">
        <div class="col-6">
          <BalanceCard :params="params"
                       :report-url="$c.clfDevice() ? '#/clf/wallet' : '#/a/clf/report/account-balance'"></BalanceCard>
        </div>
        <div class="col-6">
          <LineCard :params="params"
                    title="Paid Invoices"
                    api="paid-bills"
                    summary-filter="moneyFormat"
                    :report-url="$c.clfDevice() ? '#/clf/merchant/due' : '#/a/clf/bills'"
                    :formatter="salesFormatter"></LineCard>
        </div>
        <div class="col-6">
          <PieCard :params="params"
                   title="Outstanding Bills"
                   api="bills"
                   summary-filter="moneyFormat"
                   :report-url="$c.clfDevice() ? '#/clf/merchant/invoice' : '#/a/clf/bills'"
                   :formatter="billsFormatter"></PieCard>
        </div>
        <div class="col-6">
          <BarCard :params="params"
                   title="Taxes & Fees"
                   api="fees"
                   summary-filter="moneyFormat"
                   color="#DC4054"
                   :report-url="$c.clfDevice() ? '#/clf/merchant/dashboard/fees' : '#/a/clf/report/fees'"
                   :formatter="feesFormatter"></BarCard>
        </div>
      </div>
    </template>
  </Dashboard>
</template>

<script>
import Dashboard from '../common/index'
import BalanceCard from '../common/balance'
import LineCard from '../common/line'
import BarCard from '../common/bar'
import PieCard from '../common/pie'

export default {
  name: 'ClfDispensaryDashboard',
  components: {
    Dashboard,
    BalanceCard,
    LineCard,
    BarCard,
    PieCard
  },
  methods: {
    salesFormatter (params) {
      params = params[0]
      return `${params.name}: ${this.c.moneyFormat(params.value, 'USD', false, false)}`
    },
    feesFormatter (params) {
      params = params[0]
      return `<div class="chart-tooltip-box" style="background: #DC4054"></div> Fees ${this.c.moneyFormat(params.value, 'USD', false, false)}`
    },
    billsFormatter (params) {
      return `{value|${this.c.moneyFormat(params.data.value, 'USD', false, false)}}\n{name|${params.data.name}}`
    }
  }
}
</script>

<style lang="scss">
  .clf__dashboard_vendor__index_page {
    .page-content > .row {
      max-width: 932px;

      .clf-dispensary-dashboard-card {
        height: calc(100vw / 2 - 120px);
      }
    }
  }
</style>
