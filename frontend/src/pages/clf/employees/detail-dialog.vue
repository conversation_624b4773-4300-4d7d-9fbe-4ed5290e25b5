<template>
  <q-dialog
    class="dispensary-employee-detail-dialog clf-dialog admin-root"
    v-model="visible"
    :preventClose="true">
    <span slot="title">
      Employee Details

      <q-btn round
             icon="mdi-close"
             class="btn-close"
             text-color="white"
             @click="_hide"
             color="red-2"></q-btn>
    </span>
    <div slot="body">
      <div class="row gutter-sm" v-if="entity.id">
        <div class="col">
          <q-field label="Employee ID:" label-width="2">
            <div class="pt-6">{{ entity.id }}</div>
          </q-field>
        </div>
      </div>
      <div class="row gutter-sm">
        <div class="col">
          <q-field label="Full Name" label-width="12" :error="$v.entity.name.$error">
            <q-input v-model="entity.name" :blur="$v.entity.name.$touch"></q-input>
          </q-field>
        </div>
        <div class="col">
          <q-field label="Job Title" label-width="12">
            <q-input v-model="entity.title"></q-input>
          </q-field>
        </div>
      </div>
      <div class="row gutter-sm">
        <div class="col">
          <q-field label="Mailing Address" label-width="12">
            <q-input v-model="entity.address.address1"></q-input>
          </q-field>
        </div>
      </div>
      <div class="row gutter-sm">
        <div class="col-5">
          <q-field label="City" label-width="12">
            <q-input v-model="entity.address.city"></q-input>
          </q-field>
        </div>
        <div class="col-5">
          <q-field label="State / Province" label-width="12">
            <q-select v-model="entity.address.state"
                      filter
                      :options="$store.state.Config.usStates"></q-select>
          </q-field>
        </div>
        <div class="col-2">
          <q-field label="Postal Code" label-width="12">
            <q-input v-model="entity.address.postalCode"></q-input>
          </q-field>
        </div>
      </div>
      <div class="row gutter-sm">
        <div class="col">
          <q-field label="Phone" label-width="12">
            <q-input v-model="entity.address.phone"></q-input>
          </q-field>
        </div>
        <div class="col">
          <q-field label="Wage" label-width="12">
            <q-input v-model="entity.wage"
                     align="right"
                     type="number"
                     prefix="$" suffix="/month"></q-input>
          </q-field>
        </div>
      </div>
      <div class="row gutter-sm">
        <div class="col">
          <q-field label="Start Date" label-width="12">
            <q-datetime v-model="entity.startAt" :format="cc.format.date"></q-datetime>
          </q-field>
        </div>
        <div class="col">
          <q-field label="Employee Status" label-width="12">
            <q-select v-model="entity.status" :options="statuses"></q-select>
          </q-field>
        </div>
      </div>
    </div>
    <template slot="buttons">
      <div class="flex-center text-right">
        <q-btn color="dark"
               @click="submit"
               label="Save"></q-btn>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { required } from 'vuelidate/lib/validators'

export default {
  name: 'dispensary-employee-detail-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        address: {},
        status: 'active'
      },
      statuses: [
        {
          label: 'Active',
          value: 'active'
        },
        {
          label: 'Inactive',
          value: 'inactive'
        }
      ]
    }
  },
  validations: {
    entity: {
      name: {
        required
      }
    }
  },
  methods: {
    async submit () {
      if (this.$v.$invalid) {
        return this.$v.$touch()
      }
      this.$q.loading.show()
      const resp = await this.c.request(`${this.c.clf.apiPrefix()}/employees/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this._hide()
        this.$root.$emit('reload-dispensary-employees')
      }
    },
    show () {
      if (this.entity.address && this._.isObject(this.entity.address.state)) {
        this.entity.address.state = this.entity.address.state.id
      }
      if (this.entity.wage) {
        this.entity.wage = this.c.moneyMajorAmount(this.entity.wage, 'USD', true)
      }
    }
  }
}
</script>

<style lang="scss">
  .dispensary-employee-detail-dialog {
    .modal-content {
      width: 700px;
    }
  }

  @media (max-width: 767px) {
    .dispensary-employee-detail-dialog {
      .modal-body > div {
        > .row {
          display: block;
          margin-top: 0;

          > .col, > .col-5, > .col-2 {
            width: 100%;
            padding-top: 0 !important;
          }
        }
      }
    }
  }
</style>
