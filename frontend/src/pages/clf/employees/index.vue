<template>
  <q-page id="clf__employees__index_page" class="clf_page">
    <div class="page-header">
      <div class="title">
        {{ title }}

        <q-btn round
               icon="mdi-plus"
               @click="add"
               color="primary"></q-btn>
      </div>
      <div class="fun-group">
        <q-field label="Status" class="status-field">
          <q-select v-model="status"
                    @input="reload"
                    :options="statuses"></q-select>
        </q-field>

        <template v-if="!$c.clfDevice()">
          <q-btn flat round
                 @click="reload"
                 class="mr-15"
                 icon="mdi-refresh"></q-btn>
          <q-btn color="dark"
                 @click="download"
                 label="Export"/>
        </template>
      </div>
    </div>
    <div class="page-content mt-20">
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               @request="request"
               separator="none"
               row-key="id">
        <q-tr slot="body" slot-scope="props" :props="props"
              :class="`tr_${props.row.status}`"
              @click.native="rowClick(props.row)" class="cursor-pointer">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="col.field === 'wage'">
              <span v-if="props.row.wage">{{ props.row.wage | moneyFormat }}/month</span>
            </template>
            <template v-else-if="['name', 'title', 'addressText'].includes(col.field)">
              <span v-html="$options.filters.searchMatch(_.get(props.row, col.field), keyword)"></span>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <EmployeeDetails></EmployeeDetails>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import EmployeeDetails from './detail-dialog'
import { EventHandlerMixin } from '../../../common'

export default {
  name: 'ClfEmployeeIndex',
  mixins: [
    ListPageMixin,
    EventHandlerMixin('reload-dispensary-employees'),
    EventHandlerMixin('admin_dispensary_employees_search', 'search')
  ],
  components: {
    EmployeeDetails
  },
  data () {
    return {
      title: 'Employees',
      requestUrl: `${this.c.clf.apiPrefix()}/employees`,
      downloadUrl: `${this.c.clf.apiPrefix()}/employees/export`,
      autoLoad: true,
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 20,
        page: 1
      },
      columns: [
        {
          name: 'me.name',
          field: 'name',
          label: 'Name',
          align: 'left',
          sortable: true
        }, {
          name: 'me.title',
          field: 'title',
          label: 'Title',
          align: 'left',
          sortable: true
        }, {
          name: 'a.address1, a.address2, a.city, s.stateName',
          field: 'addressText',
          label: 'Address',
          align: 'left',
          sortable: true
        }, {
          name: 'a.phone',
          field: 'address.phone',
          label: 'Phone',
          align: 'left',
          sortable: true
        }, {
          name: 'me.wage',
          field: 'wage',
          label: 'Wage',
          align: 'right',
          sortable: true
        }
      ],
      status: 'active',
      statuses: ['Active', 'Inactive', 'All'].map(v => ({
        label: v,
        value: this._.lowerCase(v)
      }))
    }
  },
  methods: {
    getQueryParams () {
      return {
        keyword: this.keyword,
        status: this.status
      }
    },

    add () {
      this.$root.$emit('show-dispensary-employee-detail-dialog')
    },

    rowClick (item) {
      this.$root.$emit('show-dispensary-employee-detail-dialog', item)
    }
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #clf__employees__index_page {
    .fun-group {
      .status-field {
        .q-field-label {
          flex-basis: 50px;
        }

        .q-field-content {
          flex-grow: 1;
          flex-basis: 0;
        }
      }
    }
  }
</style>
