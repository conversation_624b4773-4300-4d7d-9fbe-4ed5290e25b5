<template>
  <q-page id="clf__merchant_documents__index_page" class="clf_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="subtitle" v-if="entity">{{ entity.name }}</div>
    </div>
    <div class="page-content">
      <div class="row gutter-md">
        <div class="col">
          <div class="form-section">
            <q-field :label="u"
                     v-for="(u, k) in uploads"
                     :key="k">
              <div class="row">
                <div class="col-auto">
                  <div class="empty-tip" v-if="uploading[k]">Uploading "{{ uploading[k].name }}"...</div>
                  <div class="empty-tip" v-else-if="document[k]">
                    <a :href="document[k].url" target="_blank">{{ document[k].name }}</a>
                  </div>
                  <div class="empty-tip" v-else>No document uploaded yet.</div>
                </div>
                <div class="col text-right">
                  <input type="file"
                         class="hidden"
                         @change="fileChanged(k, $event)"
                         :ref="`upload_${k}`">
                  <q-btn no-caps
                         @click="document[k] = null"
                         v-if="document[k]"
                         text-color="dark"
                         label="Delete"></q-btn>
                  <q-btn no-caps
                         v-else
                         @click="selectFile(k)"
                         text-color="dark"
                         :loading="uploading[k] !== null"
                         label="Upload document"></q-btn>
                </div>
              </div>
            </q-field>

            <q-field label="Check License Validation with State">
              <q-option-group type="checkbox"
                              v-model="document.checks"
                              :options="checks"></q-option-group>
            </q-field>

            <q-field label="Last License Verification Date">
              <q-datetime type="date"
                          v-model="document.checkAt"></q-datetime>
            </q-field>
          </div>

          <div class="confirm-section">
            <q-btn color="dark" no-caps
                   class="w-100"
                   :disable="isUploading"
                   @click="submit"
                   label="Save"></q-btn>

            <q-btn flat no-caps
                   class="ml-10"
                   :to="`/a/clf/merchants/${saType}`"
                   label="Back"></q-btn>
          </div>
        </div>
      </div>

      <q-inner-loading :visible="loading">
        <q-spinner size="40"></q-spinner>
      </q-inner-loading>
    </div>
  </q-page>
</template>

<script>
import { request, uploadAttachment, notifyResponse, notifySuccess } from '../../../common'
import PageMixin from '../../../mixins/PageMixin'
import $ from 'jquery'

export default {
  name: 'ClfMerchantDocuments',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: `Verified Documents`,
      loading: false,
      entity: {},
      document: {
        uploads: {},
        checks: [],
        checkAt: null
      },
      uploads: {
        owner_id_1: 'Owner ID 1',
        owner_id_2: 'Owner ID 2',
        owner_id_3: 'Owner ID 3',
        incorporation: 'Articles of Incorporation',
        mbr: 'MRB Business License'
      },
      uploading: {
        owner_id_1: null,
        owner_id_2: null,
        owner_id_3: null,
        incorporation: null,
        mbr: null
      },
      checks: [
        { label: 'Quarterly', value: 'Quarterly' },
        { label: 'Bi-Annually', value: 'Bi-Annually' },
        { label: 'Annually', value: 'Annually' }
      ]
    }
  },
  computed: {
    saType () {
      const match = this.$route.path.match(/\/a\/clf\/merchants\/(.*?)\//)
      if (match && match[1]) {
        return match[1]
      }
      return null
    },
    saTypeId () {
      return this.$route.params.id || ''
    },
    isUploading () {
      for (const key in this.uploading) {
        if (this.uploading.hasOwnProperty(key) && this.uploading[key]) {
          return true
        }
      }
      return false
    }
  },
  methods: {
    async reload () {
      this.loading = true
      const resp = await request(`/clf/merchants/${this.saType}/documents/${this.saTypeId}/data`)
      this.loading = false
      if (resp.success) {
        this.entity = resp.data.entity
        this.document = resp.data.document
      }
    },

    selectFile (key) {
      $(this.$refs[`upload_${key}`]).click()
    },

    async fileChanged (key, $event) {
      if ($event.target.files.length) {
        const file = $event.target.files[0]
        this.uploading[key] = file
        const resp = await uploadAttachment(file, 'public')
        this.uploading[key] = null

        const input = this.$refs[`upload_${key}`]
        input.type = 'text'
        input.type = 'file'

        if (typeof (resp) === 'string') {
          notifyResponse(`Failed to upload ${this.uploads[key]}: ${resp}`)
          return
        }
        this.document[key] = resp
      }
    },

    async submit () {
      this.loading = true
      const resp = await request(`/clf/merchants/${this.saType}/documents/${this.saTypeId}/data`, 'post', this.document)
      this.loading = false
      if (resp.success) {
        notifySuccess(resp)
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #clf__merchant_documents__index_page {
    .page-content {
      margin-top: 30px;
      min-height: 100px;
      background: white;
      border-radius: 12px;
      padding: 20px;
      -webkit-overflow-scrolling: touch;

      .form-section {
        .q-field {
          padding-bottom: 10px;
          border-bottom: 1px solid rgba(0, 0, 0, 0.05);

          .q-field-label {
            width: 250px;
          }

          .q-field-content {
            width: calc(100% - 250px);
          }

          .empty-tip {
            font-size: 1rem;
            line-height: 36px;
            vertical-align: middle;
          }

          .q-datetime-input {
            max-width: 200px;
          }

          .q-checkbox {
            font-size: 1.2em;
            margin-bottom: 3px;
            color: black;
          }
        }
      }
    }
  }
</style>
