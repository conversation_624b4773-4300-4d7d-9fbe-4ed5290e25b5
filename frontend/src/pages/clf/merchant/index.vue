<template>
  <q-page id="clf__merchants__index_page" class="clf_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
      <div class="fun-group">
        <q-field label="Status" class="status-field">
          <q-select v-model="status"
                    @input="reload"
                    :options="statuses"></q-select>
        </q-field>

        <template v-if="!$c.clfDevice()">
          <q-btn flat round
                 @click="reload"
                 class="mr-15"
                 icon="mdi-refresh"></q-btn>
        </template>
      </div>
    </div>
    <div class="page-content mt-20">
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               @request="request"
               separator="none"
               row-key="id">
        <q-tr slot="body" slot-scope="props" :props="props"
              :class="`tr_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="['name', 'licenseNumber', 'addressText', 'address.email', 'address.phone', 'status'].includes(col.field)">
              <span v-html="$options.filters.searchMatch(_.get(props.row, col.field), keyword)"></span>
            </template>
            <template v-else-if="['balance'].includes(col.field)">
              <span v-if="props.row[col.field]">{{ props.row[col.field] | moneyFormat }}</span>
            </template>
            <template v-else-if="col.field === ''">
              <q-btn-dropdown size="xs"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row, 'documents')">
                    <q-item-main>Verified Documents</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="edit(props.row, 'parameters')">
                    <q-item-main>BSA Volume Parameters</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin } from '../../../common'

export default {
  name: 'ClfMerchantIndex',
  mixins: [
    ListPageMixin,
    EventHandlerMixin('reload-clf-merchants'),
    EventHandlerMixin('admin_clf_merchants_search', 'search')
  ],
  data () {
    return {
      title: 'Merchants',
      columns: [
        {
          name: 'm.name',
          field: 'name',
          label: 'Name',
          align: 'left',
          sortable: true
        }, {
          name: 'm.licenseNumber',
          field: 'licenseNumber',
          label: 'License Number',
          align: 'left',
          sortable: true
        }, {
          name: 'a.address1, a.address2, a.city, s.stateName',
          field: 'addressText',
          label: 'Address',
          align: 'left',
          sortable: true
        }, {
          name: 'u.email',
          field: 'adminUser.email',
          label: 'Admin Email',
          align: 'left',
          sortable: true
        }, {
          name: 'u.workPhone',
          field: 'adminUser.workPhone',
          label: 'Admin Phone',
          align: 'left',
          sortable: true
        }, {
          name: 'ac.balance',
          field: 'balance',
          label: 'Account Balance',
          align: 'right',
          sortable: true
        }, {
          name: 'status.name',
          field: 'status',
          label: 'Status',
          align: 'left',
          sortable: true
        }, {
          name: '',
          field: '',
          label: 'Action',
          align: 'left'
        }
      ],
      status: 'active',
      statuses: ['Active', 'Inactive', 'All'].map(v => ({
        label: v,
        value: this._.lowerCase(v)
      }))
    }
  },
  computed: {
    type () {
      return this.$route.params.type
    }
  },
  methods: {
    getQueryParams () {
      return {
        keyword: this.keyword,
        status: this.status
      }
    },

    edit (item, page) {
      this.$router.push(`/a/clf/merchants/${this.type}/${page}/${item.id}`)
    },

    initData (route) {
      const type = route.params.type
      this.title = type === 'dispensary' ? 'Dispensaries' : 'Vendors'
      this.requestUrl = `/clf/merchants/${type}`
      this.downloadUrl = `/clf/merchants/${type}/export`
      this.data = []

      this.reload()
    }
  },
  beforeRouteUpdate (to, from, next) {
    this.initData(to)
    next()
  },
  mounted () {
    this.initData(this.$route)
  }
}
</script>

<style lang="scss">
  #clf__merchants__index_page {
  }
</style>
