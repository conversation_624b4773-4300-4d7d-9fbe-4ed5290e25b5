<template>
  <q-page id="clf__merchant_parameters__index_page" class="clf_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="subtitle" v-if="entity">{{ entity.name }}</div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="columns"
               hide-bottom
               class="mv-20"
               :pagination.sync="pagination"
               :loading="loading">
        <q-tr slot="body" slot-scope="props" :props="props">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="col.field === 'name'">
              <div class="font-14">{{ props.row[col.field] }}</div>
            </template>
            <template v-if="['day', 'week', 'month'].includes(col.field)">
              <q-input type="number"
                       :prefix="props.row.id.endsWith('_amount') ? '$' : ''"
                       v-model="parameters[props.row.id][col.field]"></q-input>
            </template>
          </q-td>
        </q-tr>
      </q-table>

      <div class="confirm-section">
        <q-btn color="dark" no-caps
               class="w-100"
               @click="submit"
               :disable="loading"
               label="Save"></q-btn>

        <q-btn flat no-caps
               class="ml-10"
               :to="`/a/clf/merchants/${saType}`"
               label="Back"></q-btn>
      </div>
    </div>
  </q-page>
</template>

<script>
import { request, notifySuccess } from '../../../common'
import PageMixin from '../../../mixins/PageMixin'

export default {
  name: 'ClfMerchantParameters',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: `BSA Volume Parameters`,
      loading: false,
      entity: {},
      parameters: {},
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 10,
        page: 1,
        sortBy: null,
        descending: false
      },
      columns: [
        {
          field: 'name',
          label: 'Parameter Name',
          align: 'left'
        }, {
          field: 'day',
          label: 'Day',
          align: 'left'
        }, {
          field: 'week',
          label: 'Week',
          align: 'left'
        }, {
          field: 'month',
          label: 'Month',
          align: 'left'
        }, {
          field: '',
          label: '',
          align: 'left'
        }
      ]
    }
  },
  computed: {
    saType () {
      const match = this.$route.path.match(/\/a\/clf\/merchants\/(.*?)\//)
      if (match && match[1]) {
        return match[1]
      }
      return null
    },
    saTypeId () {
      return this.$route.params.id || ''
    },
    data () {
      const all = []
      const names = {
        average_amount: 'Average Amount $',
        average_transactions: 'Average # Transactions',
        high_amount: 'High Amount $',
        high_transactions: 'High # Transactions',
        peer_average_amount: 'Peer Average Amount',
        peer_average_transactions: 'Peer Average # Transactions',
        peer_high_amount: 'Peer High Amount',
        peer_high_transactions: 'Peer High # Transactions'
      }
      for (const key in this.parameters) {
        if (this.parameters.hasOwnProperty(key)) {
          const p = this.parameters[key]
          p.id = key
          p.name = names[key] || key
          all.push(p)
        }
      }
      return all
    }
  },
  methods: {
    async reload () {
      this.loading = true
      const resp = await request(`/clf/merchants/${this.saType}/parameters/${this.saTypeId}/data`)
      this.loading = false
      if (resp.success) {
        this.entity = resp.data.entity
        this.parameters = resp.data.parameters
      }
    },

    async submit () {
      this.$q.loading.show()
      const resp = await request(`/clf/merchants/${this.saType}/parameters/${this.saTypeId}/data`, 'post', this.parameters)
      this.$q.loading.hide()
      if (resp.success) {
        notifySuccess(resp)
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #clf__merchant_parameters__index_page {
  }
</style>
