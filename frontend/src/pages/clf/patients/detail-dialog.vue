<template>
  <q-dialog
    class="dispensary-patient-detail-dialog clf-dialog clf-dialog-right-buttons admin-root"
    v-model="visible"
    :preventClose="true">
    <span slot="title">
      Patient Details

      <q-btn round
             icon="mdi-close"
             class="btn-close"
             text-color="white"
             @click="_hide"
             color="red-2"></q-btn>
    </span>
    <div slot="body">
      <div class="row gutter-sm top-info-container">
        <div class="col-5 avatar-box">
          <vue-load-image>
            <img slot="image" :src="entity.user.avatar || '/static/img/avatar.png'" alt="">
            <div slot="preloader" class="text-center w-120">
              <q-spinner size="30"></q-spinner>
            </div>
          </vue-load-image>
        </div>
        <div class="col-7 basic-info-box">
          <div class="font-24 mb-10 pl-5">{{ entity.user.fullName }}</div>
          <table class="form-table">
            <tr>
              <th width="117">Account Number:</th>
              <td>{{ entity.account.number }}</td>
            </tr>
            <tr>
              <th>Account Status:</th>
              <td>{{ entity.account.status }}</td>
            </tr>
            <tr v-if="$store.getters['User/clfAdmin']">
              <th>Account Balance:</th>
              <td>{{ entity.account.balance | moneyFormat }}</td>
            </tr>
          </table>
        </div>
      </div>

      <table class="form-table form-table-lg">
        <tr>
          <th width="270">Email:</th>
          <td>{{ entity.user.email }}</td>
        </tr>
        <tr>
          <th>DOB:</th>
          <td>{{ entity.user.birthday | date }}</td>
        </tr>
        <tr>
          <th>Address:</th>
          <td>{{ entity.user.address }} {{ entity.user.address2 ? ', ' : '' }} {{ entity.user.address2 }}</td>
        </tr>
        <tr>
          <th>City:</th>
          <td>{{ entity.user.city }}</td>
        </tr>
        <tr>
          <th>State / Province:</th>
          <td>{{ _.get(entity.user, 'state.name') }}</td>
        </tr>
        <tr>
          <th>Postal Code:</th>
          <td>{{ entity.user.zip }}</td>
        </tr>
        <tr>
          <th>Home Phone:</th>
          <td>{{ entity.user.homePhone }}</td>
        </tr>
        <tr>
          <th>Mobile Phone:</th>
          <td>{{ entity.user.mobilePhone }}</td>
        </tr>
        <tr>
          <th>Work Phone:</th>
          <td>{{ entity.user.workPhone }}</td>
        </tr>
      </table>
    </div>
    <template slot="buttons">
      <q-btn outline
             class="btn-gray-light"
             @click="_hide"
             label="Close"></q-btn>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import VueLoadImage from 'vue-load-image'

export default {
  name: 'dispensary-patient-detail-dialog',
  mixins: [
    Singleton
  ],
  components: {
    VueLoadImage
  },
  data () {
    return {
      defaultEntity: {
        address: {},
        user: {},
        account: {}
      }
    }
  }
}
</script>

<style lang="scss">
  .dispensary-patient-detail-dialog {
    .modal-content {
      width: 650px;

      .top-info-container {
        margin: 0 auto 30px;
      }

      .avatar-box {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        img {
          max-height: 110px;
        }
      }

      .basic-info-box {
        min-height: 126px;
      }
    }
  }

  @media (max-width: 414px) {
    .dispensary-patient-detail-dialog {
      .modal-content {
        .modal-body {
          padding: 10px 18px;
        }

        .top-info-container {
          margin-bottom: 20px;

          .avatar-box {
            width: 90px;
            padding-top: 0;
            padding-left: 0;
          }

          .basic-info-box {
            padding-top: 0;
            width: calc(100% - 90px);

            .font-24.pl-5 {
              padding-left: 0 !important;
            }

            .form-table {
              th {
                width: 50px;
              }
            }
          }
        }

        .form-table-lg {
          th {
            width: 98px;
          }

          td {
            word-break: break-all;
          }
        }
      }
    }
  }
</style>
