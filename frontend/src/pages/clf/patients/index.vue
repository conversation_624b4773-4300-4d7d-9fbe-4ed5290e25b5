<template>
  <q-page id="clf__patients__index_page" class="clf_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20"
                         default="all"
                         ref="dateRangeFilter"
                         @change="reload"></DateRangeFilter>

        <template v-if="!$c.clfDevice()">
          <q-btn flat round
                 @click="reload"
                 class="mr-15"
                 icon="mdi-refresh"></q-btn>
          <q-btn color="dark"
                 @click="download"
                 label="Export"/>
        </template>
      </div>
    </div>
    <div class="page-content mt-20">
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               @request="request"
               separator="none"
               row-key="id">
        <q-tr slot="body" slot-scope="props" :props="props"
              :class="`tr_${props.row.status}`"
              @click.native="rowClick(props.row)" class="cursor-pointer">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="['lastSpend', 'totalSpend'].includes(col.field)">
              <span v-if="props.row[col.field]">{{ props.row[col.field] | moneyFormat }}</span>
            </template>
            <template v-else-if="['since', 'lastPurchase'].includes(col.field)">
              <span v-if="props.row[col.field]">{{ props.row[col.field] | date }}</span>
            </template>
            <template v-else-if="['user.fullName', 'user.id', 'addressText'].includes(col.field)">
              <span v-html="$options.filters.searchMatch(_.get(props.row, col.field), keyword)"></span>
            </template>
            <template v-else-if="col.field === ''">
              <q-btn label="VIEW"
                     @click.stop="rowClick(props.row)"
                     class="btn-gray-light w-70"
                     outline></q-btn>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <PatientDetail></PatientDetail>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import DateRangeFilter from '../../../components/DateRangeFilter'
import PatientDetail from './detail-dialog'
import { EventHandlerMixin } from '../../../common'

export default {
  name: 'ClfPatientIndex',
  mixins: [
    ListPageMixin,
    EventHandlerMixin('reload-dispensary-patients'),
    EventHandlerMixin('admin_dispensary_patients_search', 'search')
  ],
  components: {
    DateRangeFilter,
    PatientDetail
  },
  data () {
    return {
      title: 'Patients',
      requestUrl: `${this.c.clf.apiPrefix()}/patients`,
      downloadUrl: `${this.c.clf.apiPrefix()}/patients/export`,
      autoLoad: true,
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 20,
        page: 1
      },
      columns: [
        {
          name: 'u.firstName, u.lastName',
          field: 'user.fullName',
          label: 'Name',
          align: 'left',
          sortable: true
        }, {
          name: 'u.id',
          field: 'user.id',
          label: 'Patient ID',
          align: 'left',
          sortable: true
        }, {
          name: 'u.createdAt',
          field: 'since',
          label: 'Patient Since',
          align: 'left',
          sortable: true
        }, {
          name: 'ad.city, state.stateName',
          field: 'addressText',
          label: 'City/State',
          align: 'left',
          sortable: true
        }, {
          name: 't.dateTime',
          field: 'lastPurchase',
          label: 'Last Purchase',
          align: 'right',
          sortable: true
        }, {
          name: '___lastSpend',
          field: 'lastSpend',
          label: 'Last Spend',
          align: 'right',
          sortable: true
        }, {
          name: '___totalSpend',
          field: 'totalSpend',
          label: 'Total Spend',
          align: 'right',
          sortable: true
        }, {
          field: '',
          label: '',
          align: 'right'
        }
      ]
    }
  },
  methods: {
    getQueryParams () {
      const params = this.$refs.dateRangeFilter.params()
      params.keyword = this.keyword
      return params
    },

    rowClick (item) {
      this.$root.$emit('show-dispensary-patient-detail-dialog', item)
    }
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #clf__patients__index_page {
  }
</style>
