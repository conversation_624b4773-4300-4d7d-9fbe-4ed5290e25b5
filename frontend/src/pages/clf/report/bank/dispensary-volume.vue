<template>
  <q-page id="clf_report_bank_dispensary_volume_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-field label="Report">
          <q-select v-model="type"
                    @input="reload"
                    :options="types"></q-select>
        </q-field>

        <q-btn label="Reload"
               no-caps
               @click="reload"/>
      </div>
    </div>
    <div class="page-content q-mt-md">
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <q-tr slot="body"
              slot-scope="props" :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field">
            <template v-if="col.field === 'actual'">
              <span class="text-negative">{{ col.value }}</span>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
  </q-page>
</template>

<script>
import { request } from '../../../../common'
import PageMixin from '../../../../mixins/PageMixin'
import Pagination from '../../../../components/Pagination'

export default {
  mixins: [
    PageMixin
  ],
  components: {
    Pagination
  },
  data () {
    return {
      title: 'Dispensary Volume Report',
      hideAjaxBarWhenLoading: true,
      type: 'volume',
      types: [
        {
          label: 'exceeding volume expectations list',
          value: 'volume'
        },
        {
          label: 'exceeding peer volume expectations',
          value: 'peer_volume'
        },
        {
          label: 'collecting or sending $10,000 or more/day',
          value: 'sending_10000'
        }
      ],
      columns: [
        {
          field: 'name',
          label: 'Dispensary',
          align: 'left'
        }, {
          field: 'parameter',
          label: 'Volume Parameter',
          align: 'left'
        }, {
          field: 'range',
          label: 'Range',
          align: 'left'
        }, {
          field: 'threshold',
          label: 'Threshold',
          align: 'left'
        }, {
          field: 'actual',
          label: 'Value',
          align: 'left'
        }
      ],
      data: []
    }
  },
  methods: {
    reload () {
      this.request({
        pagination: this.pagination
      })
    },
    async request ({ pagination }) {
      this.loading = true
      const resp = await request(`/clf/report/bank/dispensary-volume/${this.type}/${pagination.page}/${pagination.rowsPerPage}`)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.total
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
  @import '../../../../css/variable';

  #clf_report_bank_dispensary_volume_page {
    .fun-group {
      .q-field-label {
        width: auto;
      }

      .q-field-content {
        > .q-if {
          height: 44px;
        }
      }
    }
  }
</style>
