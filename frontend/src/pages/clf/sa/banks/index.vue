<template>
  <q-page id="clf__sa_banks__index_page" class="clf_page">
    <div class="page-header">
      <div class="title">
        {{ title }}

        <q-btn round
               icon="mdi-plus"
               @click="add"
               color="primary"></q-btn>
      </div>
      <div class="fun-group">
        <template v-if="!$c.clfDevice()">
          <q-btn flat round
                 @click="reload"
                 class="mr-15"
                 icon="mdi-refresh"></q-btn>
          <q-btn color="dark"
                 @click="download"
                 label="Export"/>
        </template>
      </div>
    </div>
    <div class="page-content mt-20">
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               @request="request"
               separator="none"
               row-key="id">
        <q-tr slot="body" slot-scope="props" :props="props"
              :class="`tr_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="['name', 'routingNumber', 'address', 'admin', 'status'].includes(col.field)">
              <span v-html="$options.filters.searchMatch(_.get(props.row, col.field), keyword)"></span>
            </template>
            <template v-else-if="col.field === ''">
              <q-btn-dropdown size="xs"
                              @click.stop="$c.noop"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="remove(props.row)">
                    <q-item-main>Delete</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="p('user_managment__user__login_at') && !isGranted"
                          @click.native="loginAs(props.row)">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, request, notifySuccess } from '../../../../common'

export default {
  name: 'ClfSaBankIndex',
  mixins: [
    ListPageMixin,
    EventHandlerMixin('reload-clf-sa-banks'),
    EventHandlerMixin('admin_clf_sa_banks_search', 'search')
  ],
  data () {
    return {
      title: 'Banks',
      requestUrl: '/clf/sa/banks',
      downloadUrl: '/clf/sa/banks/export',
      autoLoad: true,
      columns: [
        {
          name: 'b.id',
          field: 'id',
          label: 'ID',
          align: 'left',
          sortable: true
        }, {
          name: 'b.name',
          field: 'name',
          label: 'Name',
          align: 'left',
          sortable: true
        }, {
          name: 'b.routingNumber',
          field: 'routingNumber',
          label: 'Routing Number',
          align: 'left',
          sortable: true
        }, {
          name: 'a.address1, a.address2, a.city, state.stateName',
          field: 'address',
          label: 'Address',
          align: 'left',
          sortable: true
        }, {
          name: 'u.firstName, u.lastName',
          field: 'admin',
          label: 'Admin',
          align: 'left',
          sortable: true
        }, {
          name: 's.name',
          field: 'status',
          label: 'Status',
          align: 'left',
          sortable: true
        }, {
          field: '',
          label: 'Action',
          align: 'left'
        }
      ]
    }
  },
  computed: {
    isGranted () {
      return this.$store.state.User.isGranted
    }
  },
  methods: {
    getQueryParams () {
      const params = {}
      params.keyword = this.keyword
      return params
    },

    add () {
      this.$router.push(`/a/clf/sa/banks/add`)
    },

    edit (item) {
      this.$router.push(`/a/clf/sa/banks/edit/${item.id}`)
    },

    loginAs (item) {
      if (item.adminId) {
        this.$c.loginAs(item.adminId)
      }
    },

    remove (item) {
      this.$q.dialog({
        title: `Delete Bank`,
        message: `Are you sure that you want to delete the bank "${item.name}"?`,
        cancel: true,
        color: 'negative'
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/clf/sa/banks/delete/${item.id}`, 'delete')
        this.$q.loading.hide()
        if (resp.success) {
          notifySuccess(resp)
          this.reload()
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
  @import '../../../../css/variable';

  #clf__sa_banks__index_page {
  }
</style>
