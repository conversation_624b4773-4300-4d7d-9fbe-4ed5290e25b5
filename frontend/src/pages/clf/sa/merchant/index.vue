<template>
  <q-page id="clf__sa_merchants__index_page" class="clf_page">
    <div class="page-header">
      <div class="title">
        {{ title }}

        <q-btn round
               icon="mdi-plus"
               @click="add"
               color="primary"></q-btn>
      </div>
      <div class="fun-group">
        <q-field label="Status" class="status-field">
          <q-select v-model="status"
                    @input="reload"
                    :options="statuses"></q-select>
        </q-field>

        <template v-if="!$c.clfDevice()">
          <q-btn flat round
                 @click="reload"
                 class="mr-15"
                 icon="mdi-refresh"></q-btn>
          <q-btn color="dark"
                 @click="download"
                 label="Export"/>
        </template>
      </div>
    </div>
    <div class="page-content mt-20">
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               @request="request"
               separator="none"
               row-key="id">
        <q-tr slot="body" slot-scope="props" :props="props"
              :class="`tr_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="['name', 'licenseNumber', 'addressText', 'address.email', 'address.phone', 'status'].includes(col.field)">
              <span v-html="$options.filters.searchMatch(_.get(props.row, col.field), keyword)"></span>
            </template>
            <template v-else-if="['balance'].includes(col.field)">
              <span v-if="props.row[col.field]">{{ props.row[col.field] | moneyFormat }}</span>
            </template>
            <template v-else-if="col.field === ''">
              <q-btn-dropdown size="xs"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="remove(props.row)">
                    <q-item-main>Delete</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="p('user_managment__user__login_at') && !isGranted"
                          @click.native="loginAs(props.row)">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, request, notifySuccess } from '../../../../common'

export default {
  name: 'ClfSaMerchantIndex',
  mixins: [
    ListPageMixin,
    EventHandlerMixin('reload-clf-sa-merchants'),
    EventHandlerMixin('admin_clf_sa_merchants_search', 'search')
  ],
  data () {
    return {
      title: 'Merchants',
      columns: [
        {
          name: 'm.name',
          field: 'name',
          label: 'Name',
          align: 'left',
          sortable: true
        }, {
          name: 'm.licenseNumber',
          field: 'licenseNumber',
          label: 'License Number',
          align: 'left',
          sortable: true
        }, {
          name: 'a.address1, a.address2, a.city, s.stateName',
          field: 'addressText',
          label: 'Address',
          align: 'left',
          sortable: true
        }, {
          name: 'u.email',
          field: 'adminUser.email',
          label: 'Admin Email',
          align: 'left',
          sortable: true
        }, {
          name: 'u.workPhone',
          field: 'adminUser.workPhone',
          label: 'Admin Phone',
          align: 'left',
          sortable: true
        }, {
          name: 'ac.balance',
          field: 'balance',
          label: 'Account Balance',
          align: 'right',
          sortable: true
        }, {
          name: 'status.name',
          field: 'status',
          label: 'Status',
          align: 'left',
          sortable: true
        }, {
          name: '',
          field: '',
          label: 'Action',
          align: 'left'
        }
      ],
      status: 'active',
      statuses: ['Active', 'Inactive', 'All'].map(v => ({
        label: v,
        value: this._.lowerCase(v)
      }))
    }
  },
  computed: {
    type () {
      return this.$route.params.type
    },
    isGranted () {
      return this.$store.state.User.isGranted
    }
  },
  methods: {
    getQueryParams () {
      return {
        keyword: this.keyword,
        status: this.status
      }
    },

    add () {
      this.$router.push(`/a/clf/sa/merchants/${this.type}/add`)
    },

    edit (item) {
      this.$router.push(`/a/clf/sa/merchants/${this.type}/edit/${item.id}`)
    },

    loginAs (item) {
      if (item.adminUser && item.adminUser.id) {
        this.$c.loginAs(item.adminUser.id)
      }
    },

    remove (item) {
      this.$q.dialog({
        title: `Delete ${this.type}`,
        message: `Are you sure that you want to delete the ${this.type} "${item.name}"?`,
        cancel: true,
        color: 'negative'
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/clf/sa/merchants/${this.type}/delete/${item.id}`, 'delete')
        this.$q.loading.hide()
        if (resp.success) {
          notifySuccess(resp)
          this.reload()
        }
      }).catch(() => {})
    },

    initData (route) {
      const type = route.params.type
      this.title = type === 'dispensary' ? 'Dispensaries' : 'Vendors'
      this.requestUrl = `/clf/sa/merchants/${type}`
      this.downloadUrl = `/clf/sa/merchants/${type}/export`
      this.data = []

      this.reload()
    }
  },
  beforeRouteUpdate (to, from, next) {
    this.initData(to)
    next()
  },
  mounted () {
    this.initData(this.$route)
  }
}
</script>

<style lang="scss">
  @import '../../../../css/variable';

  #clf__sa_merchants__index_page {
  }
</style>
