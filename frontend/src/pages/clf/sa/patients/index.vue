<template>
  <q-page id="clf__sa_patients__index_page" class="clf_page">
    <div class="page-header">
      <div class="title">
        {{ title }}

        <q-btn round
               icon="mdi-plus"
               @click="add"
               color="primary"></q-btn>
      </div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20"
                         default="all"
                         ref="dateRangeFilter"
                         @change="reload"></DateRangeFilter>

        <template v-if="!$c.clfDevice()">
          <q-btn flat round
                 @click="reload"
                 class="mr-15"
                 icon="mdi-refresh"></q-btn>
          <q-btn color="dark"
                 @click="download"
                 label="Export"/>
        </template>
      </div>
    </div>
    <div class="page-content mt-20">
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               @request="request"
               separator="none"
               row-key="id">
        <q-tr slot="body" slot-scope="props" :props="props"
              :class="`tr_${props.row.status}`"
              @click.native="view(props.row)" class="cursor-pointer">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="['lastSpend', 'totalSpend', 'balance'].includes(col.field)">
              <span v-if="props.row[col.field]">{{ props.row[col.field] | moneyFormat }}</span>
            </template>
            <template v-else-if="['since', 'lastPurchase'].includes(col.field)">
              <span v-if="props.row[col.field]">{{ props.row[col.field] | date }}</span>
            </template>
            <template v-else-if="['user.fullName', 'user.id', 'addressText'].includes(col.field)">
              <span v-html="$options.filters.searchMatch(_.get(props.row, col.field), keyword)"></span>
            </template>
            <template v-else-if="col.field === ''">
              <q-btn-dropdown size="xs"
                              @click.stop="$c.noop"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="view(props.row)">
                    <q-item-main>View</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="remove(props.row)">
                    <q-item-main>Delete</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="p('user_managment__user__login_at') && !isGranted"
                          @click.native="loginAs(props.row)">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <PatientDetail></PatientDetail>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import DateRangeFilter from '../../../../components/DateRangeFilter'
import PatientDetail from '../../patients/detail-dialog'
import { EventHandlerMixin, request, notifySuccess } from '../../../../common'

export default {
  name: 'ClfSaPatientIndex',
  mixins: [
    ListPageMixin,
    EventHandlerMixin('reload-clf-sa-patients'),
    EventHandlerMixin('admin_clf_sa_patients_search', 'search')
  ],
  components: {
    DateRangeFilter,
    PatientDetail
  },
  data () {
    return {
      title: 'Patients',
      requestUrl: '/clf/sa/patients',
      downloadUrl: '/clf/sa/patients/export',
      autoLoad: true,
      columns: [
        {
          name: 'u.firstName, u.lastName',
          field: 'user.fullName',
          label: 'Name',
          align: 'left',
          sortable: true
        }, {
          name: 'u.id',
          field: 'user.id',
          label: 'Patient ID',
          align: 'left',
          sortable: true
        }, {
          name: 'u.createdAt',
          field: 'since',
          label: 'Patient Since',
          align: 'left',
          sortable: true
        }, {
          name: 'ad.city, state.stateName',
          field: 'addressText',
          label: 'City/State',
          align: 'left',
          sortable: true
        }, {
          name: 't.dateTime',
          field: 'lastPurchase',
          label: 'Last Purchase',
          align: 'right',
          sortable: true
        }, {
          name: '___lastSpend',
          field: 'lastSpend',
          label: 'Last Spend',
          align: 'right',
          sortable: true
        }, {
          name: '___totalSpend',
          field: 'totalSpend',
          label: 'Total Spend',
          align: 'right',
          sortable: true
        }, {
          name: 'a.balance',
          field: 'balance',
          label: 'Account Balance',
          align: 'right',
          sortable: true
        }, {
          field: '',
          label: 'Action',
          align: 'left'
        }
      ]
    }
  },
  computed: {
    isGranted () {
      return this.$store.state.User.isGranted
    }
  },
  methods: {
    getQueryParams () {
      const params = this.$refs.dateRangeFilter.params()
      params.keyword = this.keyword
      return params
    },

    view (item) {
      this.$root.$emit('show-dispensary-patient-detail-dialog', item)
    },

    add () {
      this.$router.push(`/a/clf/sa/patients/add`)
    },

    edit (item) {
      this.$router.push(`/a/clf/sa/patients/edit/${item.user.id}`)
    },

    loginAs (item) {
      if (item.user && item.user.id) {
        this.$c.loginAs(item.user.id)
      }
    },

    remove (item) {
      this.$q.dialog({
        title: `Delete Patient`,
        message: `Are you sure that you want to delete the patient "${item.user.fullName}"?`,
        cancel: true,
        color: 'negative'
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/clf/sa/patients/delete/${item.id}`, 'delete')
        this.$q.loading.hide()
        if (resp.success) {
          notifySuccess(resp)
          this.reload()
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
  @import '../../../../css/variable';

  #clf__sa_patients__index_page {
  }
</style>
