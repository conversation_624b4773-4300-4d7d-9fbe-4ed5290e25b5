<template>
  <q-page id="clf__sa_settings__index_page" class="clf_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-btn flat round
               @click="reload"
               class="mr-15"
               icon="mdi-refresh"></q-btn>
      </div>
    </div>
    <div class="page-content pt-15">
      <div class="row">
        <div class="col">
          <q-card color="white"
                  text-color="dark"
                  inline>
            <q-card-title>
              Dispensary/Vendor
            </q-card-title>
            <q-card-separator />
            <q-card-main>
              <q-field label="Refund Threshold Value" :label-width="6">
                <q-input type="number"
                         v-model="entity.clf_refund_threshold"
                         :min="0"
                         :max="100"
                         align="right"
                         suffix="%"></q-input>
              </q-field>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-btn color="primary"
             class="mv-15"
             @click="submit"
             icon="mdi-content-save"
             label="Save"></q-btn>

      <q-inner-loading :visible="loading">
        <q-spinner size="40"></q-spinner>
      </q-inner-loading>
    </div>
  </q-page>
</template>

<script>
import _ from 'lodash'
import { notifySuccess, request } from '../../../../common'
import PageMixin from '../../../../mixins/PageMixin'

export default {
  name: 'ClfSaSettingsIndex',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: `UTC Global Settings`,
      loading: false,
      entity: {
        clf_refund_threshold: 10
      }
    }
  },
  methods: {
    async reload () {
      this.loading = true
      const resp = await request(`${this.c.clf.apiPrefix()}/settings`)
      this.loading = false
      if (resp.success) {
        this.entity = _.assignIn(this.entity, resp.data)
      }
    },
    async submit () {
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`${this.c.clf.apiPrefix()}/settings`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notifySuccess(resp)
        this.reload()
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
  #clf__sa_settings__index_page {
  }
</style>
