<template>
  <q-page class="clf_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20"
                         default="all"
                         ref="dateRangeFilter"
                         @change="reload"></DateRangeFilter>

        <q-btn flat round
               @click="reload"
               class="mr-15"
               icon="mdi-refresh"></q-btn>

        <q-btn color="primary"
               class="ml-15"
               @click="$root.$emit('show-clf-sa-testing-transactions-new-dialog')"
               label="Create"></q-btn>
      </div>
    </div>
    <div class="page-content mt-20">
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               @request="request"
               separator="none"
               row-key="id">
        <q-tr slot="body" slot-scope="props" :props="props"
              :class="`tr_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="['balance'].includes(col.field)">
              <span v-if="props.row[col.field]">{{ props.row[col.field] | moneyFormat }}</span>
            </template>
            <template v-else-if="['amount'].includes(col.field)">
              <span :class="props.row.amount >= 0 ? 'text-positive' : 'text-red-8'">
                {{ props.row[col.field] > 0 ? '+' : '' }}{{ props.row[col.field] | moneyFormat }}
              </span>
            </template>
            <template v-else-if="['date'].includes(col.field)">
              <span v-if="props.row[col.field]">{{ props.row[col.field] | date }}</span>
            </template>
            <template v-else-if="['tranId', 'consumer', 'type', 'merchant', 'employee'].includes(col.field)">
              <span v-html="$options.filters.searchMatch(_.get(props.row, col.field), keyword)"></span>
            </template>
            <template v-else-if="col.field === ''">
              <q-btn label="Refund"
                     v-if="props.row.type === 'Purchase' && props.row.status === 'Completed' && !props.row.refundFrom"
                     @click.stop="refund(props.row)"
                     class="btn-red-light w-70"
                     outline></q-btn>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <new-dialog></new-dialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../../mixins/ListPageMixin'
import DateRangeFilter from '../../../../../components/DateRangeFilter'
import { EventHandlerMixin, clf, notify, request } from '../../../../../common'
import NewDialog from './new'

export default {
  name: 'ClfSaTestingTransactionsIndex',
  mixins: [
    ListPageMixin,
    EventHandlerMixin('reload-clf-sa-testing-transactions'),
    EventHandlerMixin('admin_clf_sa_testing_transactions_search', 'search')
  ],
  components: {
    DateRangeFilter,
    NewDialog
  },
  data () {
    return {
      title: 'Purchase/Refund Transactions',
      requestUrl: `${clf.apiPrefix()}/testing/transactions`,
      autoLoad: true,
      columns: [
        {
          name: 't.dateTime',
          field: 'date',
          label: 'Date',
          align: 'left',
          sortable: true
        }, {
          name: 't.id',
          field: 'tranId',
          label: 'Tran ID',
          align: 'left',
          sortable: true
        }, {
          name: 'u.name',
          field: 'consumer',
          label: 'Consumer',
          align: 'left',
          sortable: true
        }, {
          name: 'type.name',
          field: 'type',
          label: 'Tran Type',
          align: 'left',
          sortable: true
        }, {
          name: 'status.name',
          field: 'status',
          label: 'Status',
          align: 'left',
          sortable: true
        }, {
          name: 'm.name',
          field: 'merchant',
          label: 'Merchant',
          align: 'left',
          sortable: true
        }, {
          name: 'me.name',
          field: 'employee',
          label: 'Employee',
          align: 'left',
          sortable: true
        }, {
          name: 't.amount',
          field: 'amount',
          label: 'Amount',
          align: 'right',
          sortable: true
        }, {
          name: 't.refundFrom',
          field: 'refundFrom',
          label: 'Refunded In',
          align: 'left'
        }, {
          field: '',
          label: 'Action',
          align: 'left'
        }
      ]
    }
  },
  methods: {
    getQueryParams () {
      const params = this.$refs.dateRangeFilter.params()
      params.keyword = this.keyword
      return params
    },

    async refund (item) {
      this.$q.dialog({
        title: 'Confirm',
        message: `Please input the amount you want to refund from transaction #${item.tranId}:`,
        color: 'negative',
        cancel: true,
        prompt: {
          model: '',
          type: 'number',
          prefix: '$'
        }
      }).then(async amount => {
        if (!amount) {
          notify('Invalid refund amount!', 'negative')
          return
        }
        this.$q.loading.show()
        const resp = await request(`${clf.apiPrefix()}/testing/transactions/${item.tranId}/refund`, 'post', {
          amount
        })
        this.$q.loading.hide()
        if (resp.success) {
          notify()
          this.reload()
        }
      }).catch(() => {})
    }
  }
}
</script>
