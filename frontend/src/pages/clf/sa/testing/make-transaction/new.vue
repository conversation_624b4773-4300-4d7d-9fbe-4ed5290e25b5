<template>
  <q-dialog
    class="clf-sa-testing-transactions-new-dialog clf-dialog admin-root"
    v-model="visible"
    :preventClose="true">
    <span slot="title">
      Make Transaction

      <q-btn round
             icon="mdi-close"
             class="btn-close"
             text-color="white"
             @click="_hide"
             color="red-2"></q-btn>
    </span>
    <div slot="body">
      <div class="row gutter-sm">
        <div class="col">
          <q-field label="Transaction Type"
                   :error="$v.type.$error"
                   label-width="12">
            <q-select :options="types"
                      filter
                      @blur="$v.type.$touch"
                      v-model="type"></q-select>
          </q-field>
        </div>
        <div class="col">
          <q-field label="Amount"
                   :error="$v.amount.$error"
                   label-width="12">
            <q-input type="number"
                     @blur="$v.amount.$touch"
                     prefix="$"
                     v-model="amount"></q-input>
          </q-field>
        </div>
      </div>
      <div class="row gutter-sm">
        <div class="col">
          <q-field label="Consumer Type"
                   label-width="12">
            <q-radio v-model="consumerType" val="patient" label="Patient" />
            <q-radio v-model="consumerType" val="dispensary" label="Dispensary" />
          </q-field>
        </div>
        <div class="col">
          <q-field :label="consumerType === 'dispensary' ? 'Dispensary Admin' : 'Patient'"
                   :error="$v.consumer.$error"
                   label-width="12">
            <q-select :options="consumers"
                      @blur="$v.consumer.$touch"
                      filter
                      v-model="consumer"></q-select>
          </q-field>
        </div>
      </div>
      <div class="row gutter-sm">
        <div class="col">
          <q-field :label="`Merchant (${consumerType === 'dispensary' ? 'Vendor' : 'Dispensary'})`"
                   :error="$v.merchant.$error"
                   label-width="12">
            <q-select :options="merchants"
                      @blur="$v.merchant.$touch"
                      filter
                      v-model="merchant"></q-select>
          </q-field>
        </div>
        <div class="col">
          <q-field label="Merchant Employee (Optional)"
                   label-width="12">
            <q-select :options="employees"
                      filter
                      v-model="employee"></q-select>
          </q-field>
        </div>
      </div>
      <div class="row gutter-sm">
        <div class="col">
          <q-field label="Merchant Terminal"
                   label-width="12">
            <q-input v-model="terminal" disable></q-input>
          </q-field>
        </div>
        <div class="col">
          <q-field label="Transaction Status"
                   label-width="12">
            <q-select :options="statuses"
                      filter
                      @blur="$v.status.$touch"
                      v-model="status"></q-select>
          </q-field>
        </div>
      </div>
      <div class="row gutter-sm">
        <div class="col">
          <q-field label="Description (Optional)"
                   label-width="12">
            <q-input type="textarea"
                     v-model="description"></q-input>
          </q-field>
        </div>
      </div>
    </div>
    <template slot="buttons">
      <q-btn color="primary"
             icon="mdi-plus"
             :disabled="$v.$invalid"
             @click="submit"
             label="Create"></q-btn>
      <q-btn color="gray"
             class="ml-15"
             @click="_hide"
             label="Cancel"></q-btn>
    </template>

    <q-inner-loading :visible="loading">
      <q-spinner size="40"></q-spinner>
    </q-inner-loading>
  </q-dialog>
</template>

<script>
import { required } from 'vuelidate/lib/validators'
import { request, toSelectOptions, notify } from '../../../../../common'
import Singleton from '../../../../../mixins/Singleton'

export default {
  name: 'clf-sa-testing-transactions-new-dialog',
  mixins: [
    Singleton
  ],
  data () {
    const types = [
      'In-store Purchase',
      'In-store Refund'
    ].map(value => ({
      label: value,
      value
    }))
    const statuses = [
      'Pending',
      'Completed'
    ].map(value => ({
      label: value,
      value
    }))
    return {
      title: 'Create New Payment',
      amount: null,
      employee: null,
      description: null,
      consumer: null,
      consumerType: 'patient',
      merchant: null,
      status: null,
      patients: [],
      dispensaries: [],
      allEmployees: {},
      vendors: [],
      type: 'In-store Purchase',
      types,
      statuses,
      terminal: '1',
      loaded: false
    }
  },
  validations: {
    amount: {
      required
    },
    consumer: {
      required
    },
    merchant: {
      required
    },
    status: {
      required
    },
    type: {
      required
    }
  },
  computed: {
    merchants () {
      return (this.consumerType === 'dispensary' ? this.vendors : this.dispensaries).map(v => ({
        value: v.id,
        label: v.name
      }))
    },
    consumers () {
      return this.consumerType === 'dispensary' ? this.dispensaries.map(v => ({
        value: v.uid,
        label: `${v.firstName} ${v.lastName}`
      })) : this.patients.map(v => ({
        value: v.id,
        label: `${v.firstName} ${v.lastName}`
      }))
    },
    employees () {
      return this.allEmployees[this.merchant] || []
    }
  },
  watch: {
    merchant () {
      this.loadEmployees()
    }
  },
  methods: {
    async show () {
      this.$v.$reset()
      this.consumerType = 'patient'
      this.status = 'completed'
      this.type = 'In-store Purchase'
      this.amount = null
      this.employee = null
      this.description = null
      this.consumer = null
      this.merchant = null
      if (this.loaded || this.loading) {
        return
      }
      this.$q.loading.show()
      const resp = await request(`/clf/sa/testing/transactions/prepare/make/data`)
      this.$q.loading.hide()
      if (resp.success) {
        this.loaded = true
        this.patients = resp.data.patients
        this.dispensaries = resp.data.dispensaries
        this.vendors = resp.data.vendors
      }
    },
    async loadEmployees () {
      if (!this.merchant || (this.merchant in this.allEmployees)) {
        return
      }
      this.$q.loading.show()
      const resp = await request(`/clf/sa/testing/transactions/prepare/employee/data`, 'get', {
        merchant: this.merchant
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.loaded = true
        this.$set(this.allEmployees, this.merchant, toSelectOptions(resp.data))
        if (!this.employee && resp.data.length) {
          this.employee = resp.data[0].id
        }
      }
    },
    async submit () {
      if (this.$v.$invalid) {
        return this.$v.$touch()
      }
      this.$q.loading.show()
      const resp = await request(`/clf/sa/testing/transactions/make/transaction`, 'post', {
        amount: this.amount,
        type: this.type,
        status: this.status,
        consumer: this.consumer,
        merchant: this.merchant,
        employee: this.employee,
        terminal: this.terminal,
        description: this.description
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.$root.$emit('reload-clf-sa-testing-transactions')
        notify()
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
  .clf-sa-testing-transactions-new-dialog {
    .modal-body {
      width: 600px;
    }

    .q-field-content {
      .q-radio {
        line-height: 40px;
        margin-right: 10px;
      }
    }
  }
</style>
