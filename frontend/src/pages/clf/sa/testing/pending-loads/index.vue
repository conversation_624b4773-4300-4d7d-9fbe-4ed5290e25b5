<template>
  <q-page class="clf_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20"
                         default="all"
                         ref="dateRangeFilter"
                         @change="reload"></DateRangeFilter>

        <q-btn flat round
               @click="reload"
               class="mr-15"
               icon="mdi-refresh"></q-btn>
      </div>
    </div>
    <div class="page-content mt-20">
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               @request="request"
               separator="none"
               row-key="id">
        <q-tr slot="body" slot-scope="props" :props="props"
              :class="`tr_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="['amount'].includes(col.field)">
              <span :class="props.row.amount >= 0 ? 'text-positive' : 'text-red-8'">
                {{ props.row[col.field] > 0 ? '+' : '' }}{{ props.row[col.field] | moneyFormat }}
              </span>
            </template>
            <template v-else-if="['date'].includes(col.field)">
              <span v-if="props.row[col.field]">{{ props.row[col.field] | date }}</span>
            </template>
            <template v-else-if="col.field === ''">
              <q-btn label="Approve"
                     @click.stop="rowClick(props.row, 'approve')"
                     class="btn-green-light mr-10"
                     outline></q-btn>
              <q-btn label="Decline"
                     @click.stop="rowClick(props.row, 'decline')"
                     class="btn-red-light"
                     outline></q-btn>
            </template>
            <template v-else-if="['user', 'email', 'tranId', 'type'].includes(col.field)">
              <span v-html="$options.filters.searchMatch(_.get(props.row, col.field), keyword)"></span>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../../mixins/ListPageMixin'
import DateRangeFilter from '../../../../../components/DateRangeFilter'
import { EventHandlerMixin, clf, request, notify } from '../../../../../common'

export default {
  name: 'ClfSaTestingPendingLoadsIndex',
  mixins: [
    ListPageMixin,
    EventHandlerMixin('reload-clf-sa-testing-pending-loads'),
    EventHandlerMixin('admin_clf_sa_testing_pending_loads_search', 'search')
  ],
  components: {
    DateRangeFilter
  },
  data () {
    return {
      title: 'Pending Load/Unload Management',
      requestUrl: `${clf.apiPrefix()}/testing/pending-loads`,
      autoLoad: true,
      columns: [
        {
          name: 't.dateTime',
          field: 'date',
          label: 'Date',
          align: 'left',
          sortable: true
        }, {
          name: 't.id',
          field: 'tranId',
          label: 'Tran ID',
          align: 'left',
          sortable: true
        }, {
          name: 'u.name',
          field: 'user',
          label: 'User',
          align: 'left',
          sortable: true
        }, {
          name: 'u.email',
          field: 'email',
          label: 'User Email',
          align: 'left',
          sortable: true
        }, {
          name: 'org',
          field: 'org',
          label: 'User Type',
          align: 'left'
        }, {
          name: 'type.name',
          field: 'type',
          label: 'Tran Type',
          align: 'left',
          sortable: true
        }, {
          name: 't.amount',
          field: 'amount',
          label: 'Amount',
          align: 'right',
          sortable: true
        }, {
          field: '',
          label: '',
          align: 'right'
        }
      ]
    }
  },
  methods: {
    getQueryParams () {
      const params = this.$refs.dateRangeFilter.params()
      params.keyword = this.keyword
      return params
    },

    async rowClick (item, action = 'approve') {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to *** ${action} *** the pending ${item.type} request "${item.tranId}"?`,
        color: action === 'approve' ? 'positive' : 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`${clf.apiPrefix()}/testing/pending-loads/${item.tranId}/${action}`, 'post')
        this.$q.loading.hide()
        if (resp) {
          notify()
          this.reload()
        }
      }).catch(() => {})
    }
  }
}
</script>
