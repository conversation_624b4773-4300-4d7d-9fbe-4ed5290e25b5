<template>
  <q-page class="clf_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
        <div class="red font-14 mt-5">Please be careful. This tool will be removed once we go live.</div>
      </div>
      <div class="fun-group">
        <q-btn flat round
               @click="reload"
               class="mr-15"
               icon="mdi-refresh"></q-btn>
      </div>
    </div>
    <div class="page-content mt-20">
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               @request="request"
               separator="none"
               row-key="id">

        <q-tr slot="header" slot-scope="props" :props="props">
          <th v-for="col in props.cols" :key="col.field" :align="col.align">
            <div v-if="col.field === 'number'" class="font-12 scale-85 normal">Patient/Merchant</div>
            {{ col.label }}
          </th>
        </q-tr>

        <q-tr slot="body" slot-scope="props" :props="props"
              :class="`tr_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="['balance'].includes(col.field)">
              <span v-if="props.row[col.field]">{{ props.row[col.field] | moneyFormat }}</span>
            </template>
            <template v-else-if="['id', 'userId', 'name', 'email', 'type', 'number'].includes(col.field)">
              <span v-html="$options.filters.searchMatch(_.get(props.row, col.field), keyword)"></span>
            </template>
            <template v-else-if="col.field === ''">
              <q-btn label="RESET"
                     @click.stop="rowClick(props.row)"
                     class="btn-red-light w-70"
                     outline></q-btn>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../../mixins/ListPageMixin'
import { EventHandlerMixin, clf, request, notify } from '../../../../../common'

export default {
  name: 'ClfSaTestingResetBalanceIndex',
  mixins: [
    ListPageMixin,
    EventHandlerMixin('reload-clf-sa-testing-reset-balance'),
    EventHandlerMixin('admin_clf_sa_testing_reset_balance_search', 'search')
  ],
  data () {
    return {
      title: 'Reset Account Balance and Activity',
      requestUrl: `${clf.apiPrefix()}/testing/reset-balance`,
      autoLoad: true,
      columns: [
        {
          name: 't.id',
          field: 'id',
          label: 'ID',
          align: 'left',
          width: 100,
          sortable: true
        }, {
          name: 'u.id',
          field: 'userId',
          label: 'User ID',
          align: 'left',
          sortable: true
        }, {
          name: 't.name',
          field: 'name',
          label: 'Name',
          align: 'left',
          sortable: true
        }, {
          name: 'u.email',
          field: 'email',
          label: 'Email',
          align: 'left',
          sortable: true
        }, {
          name: 'type.name',
          field: 'type',
          label: 'Type',
          align: 'left',
          sortable: true
        }, {
          name: 't.number',
          field: 'number',
          label: 'Number',
          align: 'center',
          sortable: true
        }, {
          name: 't.balance',
          field: 'balance',
          label: 'Balance',
          align: 'right',
          sortable: true
        }, {
          field: '',
          label: '',
          align: 'right'
        }
      ]
    }
  },
  methods: {
    getQueryParams () {
      const params = {}
      params.keyword = this.keyword
      return params
    },

    async rowClick (item) {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to reset balance and all activity of account #${item.id} ${item.name}?`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`${clf.apiPrefix()}/testing/reset-balance/${item.id}/reset`, 'post')
        this.$q.loading.hide()
        if (resp) {
          notify()
          this.reload()
        }
      }).catch(() => {})
    }
  }
}
</script>
