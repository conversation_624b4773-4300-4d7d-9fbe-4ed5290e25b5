<template>
  <q-page id="clf__settings__index_page" class="clf_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="row gutter-md">
        <div class="col-4 left-column">
          <div class="icon-admin">
            <div class="icon-box">
              <img v-if="!entity.id && !saType" src="" alt="">
              <vue-load-image v-else>
                <template slot="image">
                  <img v-if="entity.icon" :src="entity.icon" alt="">
                  <img v-else-if="isBank" src="../../../statics/mobile/clf/bank.png" alt="">
                  <img v-else src="../../../statics/mobile/clf/marijuana.svg" alt="">
                </template>
                <q-spinner slot="preloader" size="30"></q-spinner>
              </vue-load-image>
            </div>
            <div>
              <q-btn flat no-caps
                     class="hover-green btn-change-picture"
                     label="Change Picture">
                <input type="file" class="icon_file_input"
                       @change="selectedFile"
                       accept="image/*">
              </q-btn>
              |
              <q-btn flat no-caps color="gray"
                     class="hover-red"
                     @click="removePicture"
                     label="Delete Picture"></q-btn>
            </div>
          </div>

          <div class="hours-section" v-if="c.clf.isDispensary() || saType === 'dispensary'">
            <h4>Hours of Operation</h4>
            <div class="row gutter-xs"
                 v-for="(h, i) in entity.hours"
                 :key="i">
              <div class="col text">{{ weekdays[i] }}</div>
              <div class="col-4">
                <q-select :options="hours"
                          :error="$v.entity.hours.$each[i].open.$error || $v.entity.hours.$each[i].close.$error"
                          @blur="$v.entity.hours.$each[i].open.$touch(); $v.entity.hours.$each[i].close.$touch()"
                          v-model="h.open"></q-select>
              </div>
              <div class="col-1 text"
                   :class="{silver: h.open < 0}">to</div>
              <div class="col-4">
                <q-select :options="[]"
                          disable
                          v-if="h.open < 0"
                          v-model="h.close"></q-select>
                <q-select :options="hours"
                          v-else
                          :error="$v.entity.hours.$each[i].close.$error"
                          @blur="$v.entity.hours.$each[i].close.$touch"
                          v-model="h.close"></q-select>
              </div>
            </div>
          </div>
        </div>
        <div class="col">
          <div class="form-section" v-if="entity && entity.address">
            <h4 v-if="saType">{{ _.upperFirst(saType) }} Details</h4>
            <h4 v-else>{{ $store.getters['User/organizationType'] }} Details</h4>
            <q-field :label="nameLabel"
                     :error="$v.entity.name.$error"
                     label-width="12">
              <q-input v-model="entity.name"
                       @blur="$v.entity.name.$touch"></q-input>
            </q-field>

            <q-field label="Legal Name"
                     v-if="isBank"
                     :error="$v.entity.legalName.$error"
                     label-width="12">
              <q-input v-model="entity.legalName"
                       @blur="$v.entity.legalName.$touch"></q-input>
            </q-field>
            <q-field label="Routing Number"
                     v-if="isBank"
                     :error="$v.entity.routingNumber.$error"
                     label-width="12">
              <q-input v-model="entity.routingNumber"
                       @blur="$v.entity.routingNumber.$touch"></q-input>
            </q-field>

            <q-field label="State License Number"
                     v-if="isMerchant"
                     :error="$v.entity.licenseNumber.$error"
                     label-width="12">
              <q-input v-model="entity.licenseNumber"
                       @blur="$v.entity.licenseNumber.$touch"></q-input>
            </q-field>

            <q-field label="Address" label-width="12"
                     :error="$v.entity.address.address1.$error">
              <q-input v-model="entity.address.address1"
                       @blur="$v.entity.address.address1.$touch"></q-input>
            </q-field>
            <div class="row gutter-md">
              <div class="col">
                <q-field label="City" label-width="12"
                         :error="$v.entity.address.city.$error">
                  <q-input v-model="entity.address.city"
                           @blur="$v.entity.address.city.$touch"></q-input>
                </q-field>
              </div>
              <div class="col">
                <q-field label="State/Province" label-width="12"
                         :error="$v.entity.address.state.$error">
                  <q-select v-model="entity.address.state"
                            :options="states"
                            @blur="$v.entity.address.state.$touch"></q-select>
                </q-field>
              </div>
              <div class="col">
                <q-field label="Postal Code" label-width="12"
                         :error="$v.entity.address.postalCode.$error">
                  <q-input v-model="entity.address.postalCode"
                           @blur="$v.entity.address.postalCode.$touch"></q-input>
                </q-field>
              </div>
            </div>
          </div>

          <div class="form-section" v-if="entity && entity.adminUser">
            <h4>Account Details</h4>
            <div class="row gutter-md">
              <div class="col">
                <q-field label="First Name" label-width="12"
                         :error="$v.entity.adminUser.firstName.$error">
                  <q-input v-model="entity.adminUser.firstName"
                           @blur="$v.entity.adminUser.firstName.$touch"></q-input>
                </q-field>
              </div>
              <div class="col">
                <q-field label="Last Name" label-width="12"
                         :error="$v.entity.adminUser.lastName.$error">
                  <q-input v-model="entity.adminUser.lastName"
                           @blur="$v.entity.adminUser.lastName.$touch"></q-input>
                </q-field>
              </div>
            </div>
            <div class="row gutter-md">
              <div class="col">
                <q-field label="Email" label-width="12"
                         :error="$v.entity.adminUser.email.$error">
                  <q-input v-model="entity.adminUser.email"
                           @blur="$v.entity.adminUser.email.$touch"></q-input>
                </q-field>
              </div>
              <div class="col">
                <q-field label="Phone Number" label-width="12"
                         :error="$v.entity.adminUser.workPhone.$error">
                  <q-input v-model="entity.adminUser.workPhone"
                           @blur="$v.entity.adminUser.workPhone.$touch"></q-input>
                </q-field>
              </div>
            </div>
            <div class="row gutter-md">
              <div class="col q-field">
                <div class="q-field-label items-center flex">
                  New Password
                  <span class="text-gray thin ml-5">(optional)</span>
                </div>
                <q-input type="password" v-model="entity.adminUser.newPassword"
                         :error="$v.entity.adminUser.newPassword.$error"
                         @blur="$v.entity.adminUser.newPassword.$touch() && $v.entity.adminUser.confirmPassword.$touch()"></q-input>
              </div>
              <div class="col q-field">
                <div class="q-field-label items-center flex">
                  Confirm New Password
                  <span class="text-gray thin ml-5">(optional)</span>
                </div>
                <q-input type="password" v-model="entity.adminUser.confirmPassword"
                         :error="$v.entity.adminUser.confirmPassword.$error"
                         @blur="$v.entity.adminUser.newPassword.$touch() && $v.entity.adminUser.confirmPassword.$touch()"></q-input>
              </div>
            </div>
            <div class="row gutter-md" v-if="$store.getters['Config/current'] && $store.getters['Config/current'].passwordExpiry">
              <div class="col">
                <q-checkbox v-model="entity.adminUser.passwordExpiryDisabled"
                            label="Turn off password 90 days expiry."></q-checkbox>
              </div>
            </div>
          </div>

          <div class="confirm-section" v-if="saType">
            <q-btn color="dark" no-caps
                   @click="submit"
                   label="Submit"></q-btn>
          </div>
          <div class="confirm-section" v-else-if="entity.id">
            <h5>Please Enter Your Current Account Password</h5>
            <q-input type="password"
                     v-model="entity.adminUser.password"
                     :error="$v.entity.adminUser.password.$error"
                     @blur="$v.entity.adminUser.password.$touch"></q-input>
            <q-btn color="dark" no-caps
                   @click="submit"
                   label="Save Account Settings"></q-btn>
          </div>
        </div>
      </div>

      <q-inner-loading :visible="loading">
        <q-spinner size="40"></q-spinner>
      </q-inner-loading>
    </div>
  </q-page>
</template>

<script>
import _ from 'lodash'
import { notifySuccess, notifyResponse, request, uploadAttachment, clfDevice } from '../../../common'
import PageMixin from '../../../mixins/PageMixin'
import { required } from 'vuelidate/lib/validators'
import fixOrientation from 'fix-orientation'
import VueLoadImage from 'vue-load-image'

export default {
  name: 'ClfSettingsIndex',
  mixins: [
    PageMixin
  ],
  components: {
    VueLoadImage
  },
  data () {
    return {
      title: `${this.$store.getters['User/organizationType']} Settings`,
      loading: false,
      weekdays: ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'],
      hours: _.map(_.range(25), v => {
        v--
        let label = ''
        if (v < 0) {
          label = 'CLOSED'
        } else if (v < 12) {
          label = `${v} AM`
        } else {
          label = `${v === 12 ? 12 : (v - 12)} PM`
        }
        return {
          label,
          value: v
        }
      }),
      states: [],
      entity: {
        address: {},
        adminUser: {},
        hours: _.map(_.range(7), () => ({}))
      }
    }
  },
  computed: {
    valid () {
      return !this.$v.$invalid
    },
    type () {
      return this.$store.getters['User/organizationType'].toLowerCase()
    },
    isMerchant () {
      return ['dispensary', 'vendor'].includes(this.type) || ['dispensary', 'vendor'].includes(this.saType)
    },
    isBank () {
      return ['bank'].includes(this.type) || ['bank'].includes(this.saType)
    },
    nameLabel () {
      if (this.isMerchant) {
        return 'Legal Business Name'
      }
      if (this.type === 'bank') {
        return 'Bank Name'
      }
      return 'Name'
    },
    saType () {
      if (this.$route.path.startsWith('/a/clf/sa/banks/')) {
        return 'bank'
      }
      const match = this.$route.path.match(/\/a\/clf\/sa\/merchants\/(.*?)\//)
      if (match && match[1]) {
        return match[1]
      }
      return null
    },
    saTypeId () {
      return this.$route.params.id || ''
    },
    apiPrefix () {
      let prefix = this.c.clf.apiPrefix()
      const type = this.saType
      if (type === 'bank') {
        prefix = `/clf/sa/banks`
      } else if (type) {
        prefix = `/clf/sa/merchants/${type}`
      }
      return prefix
    }
  },
  validations () {
    const all = {
      entity: {
        name: { required },
        address: {
          address1: { required },
          city: { required },
          state: { required },
          postalCode: { required }
        },
        adminUser: {
          firstName: { required },
          lastName: { required },
          email: { required },
          workPhone: { required },
          newPassword: {},
          confirmPassword: {
            confirmPassword: (value, vm) => {
              return !vm ||
                (!vm.newPassword && !value) ||
                vm.newPassword === value
            }
          }
        }
      }
    }
    if (!this.saType) {
      all.entity.adminUser.password = {
        required
      }
    }
    if (this.isMerchant) {
      all.entity.licenseNumber = { required }
    }
    if (this.isBank) {
      all.entity.legalName = { required }
      all.entity.routingNumber = { required }
    }
    if (!this.entity.id) {
      all.entity.adminUser.newPassword = { required }
      all.entity.adminUser.confirmPassword = { required }
    }
    if (this.type === 'dispensary' || this.saType === 'dispensary') {
      all.entity.hours = {
        $each: {
          open: {
            required
          },
          close: {
            close: (value, vm) => {
              if (vm.open < 0) {
                return true
              }
              const open = parseInt(vm.open)
              if (isNaN(open)) {
                return true
              }
              const close = parseInt(value)
              if (isNaN(close)) {
                return false
              }
              return open < value
            }
          }
        }
      }
    }
    return all
  },
  methods: {
    async reload () {
      this.$v.$reset()
      this.loading = true
      const resp = await request(`${this.apiPrefix}/settings/data/${this.saTypeId}`)
      this.loading = false
      if (resp.success) {
        this.entity = resp.data.entity
        this.states = this.c.toSelectOptions(resp.data.states)
      }
    },
    async submit () {
      if (!this.valid) {
        this.$q.notify('Please fix the highlighted fields first')
        return this.$v.$touch()
      }
      const $input = this.$el.querySelector('.icon_file_input')
      if ($input.files.length) {
        this.$q.loading.show({
          message: 'Uploading icon...'
        })
        const file = await uploadAttachment($input.files[0], 'public')
        if (typeof (file) === 'string') {
          this.$q.loading.hide()
          notifyResponse(`Failed to upload icon...${file}`)
          return
        }
        this.entity.newIcon = file
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const data = _.cloneDeep(this.entity)
      const resp = await request(`${this.apiPrefix}/settings/save`, 'post', data)
      this.$q.loading.hide()
      if (resp.success) {
        notifySuccess(resp)

        if (this.saType === 'bank') {
          this.$router.push(`/a/clf/sa/banks`)
        } else if (this.saType) {
          this.$router.push(`/a/clf/sa/merchants/${this.saType}`)
        } else {
          this.reload()

          if (clfDevice()) {
            this.$store.dispatch('User/init')
          }
        }
      }
    },
    removePicture () {
      const $input = this.$el.querySelector('.icon_file_input')
      $input.type = 'text'
      $input.type = 'file'

      this.entity.removeIcon = true
      this.entity.icon = null
      this.$q.notify({
        color: 'positive',
        message: 'Reset to default picture',
        position: 'top',
        timeout: 1500
      })
    },
    async selectedFile () {
      const $input = this.$el.querySelector('.icon_file_input')
      if ($input.files.length) {
        const reader = new FileReader()
        const self = this
        reader.onload = function () {
          fixOrientation(this.result, fixed => {
            self.entity.icon = fixed
          })
        }
        reader.readAsDataURL($input.files[0])
      }
    }
  },
  mounted () {
    if (this.saType) {
      if (this.saTypeId) {
        this.title = `Edit ${_.upperFirst(this.saType)}`
      } else {
        this.title = `Add ${_.upperFirst(this.saType)}`
      }
    }

    this.reload()
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #clf__settings__index_page {
    .page-content {
      margin-top: 30px;
      min-height: 100px;
      background: white;
      border-radius: 12px;
      padding: 20px;
      -webkit-overflow-scrolling: touch;

      .left-column {
        width: 340px;
        max-width: 340px;
        min-width: 340px;

        .icon-admin {
          text-align: center;
          margin-bottom: 50px;

          .icon-box {
            padding: 20px 0;
            min-height: 180px;
            display: flex;
            justify-content: center;
            align-items: center;

            img {
              width: 180px;
              min-width: 180px;
            }
          }

          .btn-change-picture {
            padding: 0;
            width: 120px;
            height: 34px;

            .q-btn-inner {
              position: relative;
            }
          }

          .icon_file_input {
            width: 120px;
            height: 34px;
            position: absolute;
            opacity: 0;
          }
        }

        .hours-section {
          padding: 9px;

          h4 {
            font-size: 20px;
            color: #333;
            font-weight: bold;
            margin: 0 auto 25px 0;
            text-align: center;
          }

          > .row {
            margin-bottom: 8px;

            > .text {
              line-height: 44px;
              font-size: 14px;
              color: #333;

              &:first-of-type {
                font-weight: bold;
                font-size: 16px;
              }
            }

            .col-4 {
              width: 35%;
              max-width: 35%;
              flex: 0 0 35%;
            }
          }
        }
      }

      .confirm-section {
        margin-top: 20px;
        background-color: #E4F4E4;
        border-radius: 6px;
        padding: 20px;
        text-align: center;

        h5 {
          margin: 0 auto 10px;
          color: #333;
          font-size: 14px;
          font-weight: 600;
        }

        .q-if {
          width: 200px;
          margin: 0 auto 15px !important;
        }

        .q-btn {
          width: 200px;
        }
      }
    }
  }

  @media (max-width: 414px) {
    #clf__settings__index_page {
      .page-content {
        padding: 0;
        margin: 0;

        .left-column {
          width: 100%;
          max-width: 100%;
          min-width: 100%;
        }

        .icon-admin {
          margin: 0 !important;
        }

        > .row {
          margin: 0 !important;
          display: block;

          > .col, > .col-4 {
            padding: 0 !important;
          }
        }

        .form-section {
          margin-bottom: 20px;

          .q-field {
            margin-bottom: 10px;
          }

          > .row {
            margin-top: 0;
            display: block;

            > .col {
              padding-top: 0;
            }
          }
        }

        h4 {
          font-size: 20px;
          color: #333;
          font-weight: bold;
          margin: 10px auto 15px 0;
          text-align: center;
        }
      }
    }
  }
</style>
