<template>
  <q-page id="clf__transactions__new_page" class="clf_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
    </div>
    <div class="page-content mt-20">
      <div class="form">
        <q-field label="Amount"
                 :error="$v.amount.$error"
                 label-width="12">
          <q-input type="number"
                   class="w-200"
                   @blur="$v.amount.$touch"
                   prefix="$"
                   v-model="amount"></q-input>
        </q-field>
        <q-field label="Transaction Type"
                 :error="$v.type.$error"
                 label-width="12">
          <q-select :options="types"
                    filter
                    @blur="$v.type.$touch"
                    v-model="type"></q-select>
        </q-field>
        <q-field label="Employee"
                 :error="$v.employee.$error"
                 label-width="12">
          <q-select :options="employees"
                    filter
                    @blur="$v.employee.$touch"
                    v-model="employee"></q-select>
        </q-field>
        <q-field label="Terminal"
                 label-width="12">
          <q-input v-model="terminal" disable></q-input>
        </q-field>
        <q-field label="Description (Optional)"
                 label-width="12">
          <q-input type="textarea"
                   v-model="description"></q-input>
        </q-field>

        <div class="mt-10">
          <q-btn color="primary"
                 icon="mdi-plus"
                 @click="submit"
                 label="Create"></q-btn>
          <q-btn color="gray"
                 class="ml-15"
                 @click="$router.replace('/a/clf/report/transactions')"
                 label="Cancel"></q-btn>
        </div>
      </div>

      <q-inner-loading :visible="loading">
        <q-spinner size="40"></q-spinner>
      </q-inner-loading>
    </div>
  </q-page>
</template>

<script>
import PageMixin from '../../../mixins/PageMixin'
import { required } from 'vuelidate/lib/validators'
import { request, toSelectOptions } from '../../../common'

export default {
  name: 'ClfDispensaryTransactionNew',
  mixins: [
    PageMixin
  ],
  data () {
    const types = [
      'In-store Purchase',
      'In-store Refund'
    ].map(value => ({
      label: value,
      value
    }))
    return {
      title: 'Create New Payment',
      amount: null,
      employee: null,
      description: null,
      type: 'In-store Purchase',
      types,
      terminal: '1'
    }
  },
  validations: {
    amount: {
      required
    },
    employee: {
      required
    },
    type: {
      required
    }
  },
  computed: {
    employees () {
      return toSelectOptions(this.$store.state.Clf.employees)
    }
  },
  methods: {
    async reload () {
      await this.$store.dispatch('Clf/initEmployees')
      if (this.employees.length && !this.employee) {
        this.employee = this.employees[0].value
      }
    },
    async submit () {
      if (this.$v.$invalid) {
        return this.$v.$touch()
      }
      this.loading = true
      const resp = await request(`/clf/dispensary/transactions/create`, 'post', {
        amount: this.amount,
        type: this.type,
        employee: this.employee,
        terminal: this.terminal,
        description: this.description
      })
      this.loading = false
      if (resp.success) {
        this.$router.push(`/a/clf/transactions/start/${resp.data.token}`)
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #clf__transactions__new_page {
    .form {
      max-width: 600px;
    }
  }
</style>
