<template>
  <q-page id="clf__transactions__start_page" class="clf_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
    </div>
    <div class="page-content mt-20">
      <div class="qr-wrap">
        <canvas class="qr" v-show="!done"></canvas>
      </div>

      <div class="actions mt-20">
        <q-btn color="gray"
               @click="$router.replace('/a/clf/report/transactions')"
               label="Cancel"></q-btn>
      </div>

      <q-inner-loading :visible="loading">
        <q-spinner :size="30"></q-spinner>
      </q-inner-loading>
    </div>
  </q-page>
</template>

<script>
import PageMixin from '../../../mixins/PageMixin'
import HideLoadingBarMixin from '../../../mixins/HideLoadingBarMixin'
import qrc from 'qrcode'
import { request } from '../../../common'

export default {
  name: 'ClfDispensaryTransactionStart',
  mixins: [
    PageMixin,
    HideLoadingBarMixin
  ],
  data () {
    return {
      title: 'Waiting to be paid',
      timeout: null,
      done: false
    }
  },
  computed: {
    token () {
      return this.$route.params.token
    }
  },
  methods: {
    async reload () {
      this.stop()
      if (!this.token) {
        return
      }
      this.loading = true
      const resp = await request(`/clf/dispensary/transactions/start/${this.token}`)
      this.loading = false
      if (resp.success) {
        const canvas = this.$el.querySelector('canvas.qr')
        qrc.toCanvas(canvas, this.token, {
          width: 250,
          height: 250
        }, err => {
          err && console.error(err)
        })

        this.updateStatus()
      }
    },
    updateStatus () {
      this.stop()
      this.timeout = setTimeout(async () => {
        if (!this.timeout || !this.token) {
          return
        }
        this.restoreXhrSend()
        const resp = await request(`/clf/dispensary/transactions/status/${this.token}`)
        this.hackXhrSend()
        if (!this.timeout) {
          return
        }
        if (resp.success) {
          if (resp.data.status === 'Pending') {
            this.updateStatus()
            return
          }
          this.done = true
          if (resp.data.status === 'Completed') {
            this.$swal({
              type: 'success',
              text: 'Payment is completed successfully!'
            }).then(() => {
              this.$router.replace('/a/clf/transactions/new')
            })
            return
          }
          if (resp.data.status === 'Cancelled') {
            this.$swal({
              type: 'error',
              text: 'Payment was cancelled!'
            }).then(() => {
              this.$router.replace('/a/clf/transactions/new')
            })
            return
          }
          console.log(resp.data)
        } else {
          this.updateStatus()
        }
      }, 2000)
    },
    stop () {
      if (this.timeout) {
        clearTimeout(this.timeout)
        this.timeout = null
      }
    }
  },
  mounted () {
    this.reload()
  },
  beforeDestroy () {
    this.stop()
  },
  beforeRouteUpdate (to, from, next) {
    next()
    this.reload()
  },
  beforeRouteLeave (to, from, next) {
    this.stop()
    this.hackXhrSend()
    next()
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #clf__transactions__start_page {
    .page-content {
      height: calc(100vh - 140px);

      .qr-wrap {
        width: 100%;
        height: calc(100% - 60px);
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
</style>
