<template>
  <q-page class="error-detail">
    <json-record
      placeholder="Error ID"
      :record="errors"
      @load-record-detail="loadErrorDetail"
    ></json-record>
  </q-page>
</template>

<script>
import { request } from '../../common'
import JsonRecord from '../../components/JsonRecord.vue'

export default {
  name: 'error_detail',
  data () {
    return {
      errors: {},
      loading: false
    }
  },
  components: {
    JsonRecord
  },
  methods: {
    async loadErrorDetail (recordId) {
      const id = this._.isNull(recordId) || this._.isUndefined(recordId)
        ? this.$route.params.id
        : recordId
      this.$q.loading.show()
      try {
        let { data } = await request('/admin/debug/errors/' + id, 'get')
        this.$q.loading.hide()
        this.errors = {}
        for (let key in data) {
          let value = data[key]
          try {
            value = JSON.parse(value)
          } catch (e) {}
          this.errors[key] = value
        }
        this.$forceUpdate()
      } catch (e) {
        this.$q.loading.hide()
        this.$q.notify('Error fetching error details')
      } finally {
        this.loading = false
      }
    }
  },
  mounted () {
    this.loadErrorDetail()
  }
}
</script>

<style lang="scss">
.error-detail {
}
</style>
