<template>
  <q-page class="invoke-detail">
    <json-record
      placeholder="External Invoke ID"
      :record="invokes"
      @load-record-detail="loadInvokeDetail"
    ></json-record>
  </q-page>
</template>

<script>
import { request } from '../../common'
import JsonRecord from '../../components/JsonRecord.vue'

export default {
  name: 'external_invoke',
  data () {
    return {
      invokes: {},
      loading: false
    }
  },
  components: {
    JsonRecord
  },
  methods: {
    async loadInvokeDetail (recordId) {
      const id = this._.isNull(recordId) || this._.isUndefined(recordId)
        ? this.$route.params.id
        : recordId
      this.$q.loading.show()
      try {
        let { data } = await request(
          '/admin/debug/external-invoke/' + id,
          'get'
        )
        this.$q.loading.hide()
        this.invokes = {}
        for (let key in data) {
          let value = data[key]
          try {
            value = JSON.parse(value)
          } catch (e) {}
          this.invokes[key] = value
        }
      } catch (e) {
        this.$q.loading.hide()
        this.$q.notify('Error fetching external invoke details')
      } finally {
        this.loading = false
      }
    }
  },
  mounted () {
    this.loadInvokeDetail()
  }
}
</script>

<style lang="scss">
.invoke_detail {
}
</style>
