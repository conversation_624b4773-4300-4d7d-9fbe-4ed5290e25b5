<template>
  <q-page>
    <h6 class="mt-30 mb-20">Welcome, {{ user.fullName }}!</h6>
    <p class="font-14">Click on a menu item in left side bar to continue.</p>
  </q-page>
</template>

<script>
import PageMixin from '../../mixins/PageMixin'
import _ from 'lodash'
import { adminLayoutRoot } from '../../common'

export default {
  name: 'welcome',
  mixins: [
    PageMixin
  ],
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  mounted () {
    // Enable the welcome page for below platforms
    if ([
      // 'cp_fis'
    ].includes(this.user.cpKey)) {
      return
    }

    const defaultRoute = this.user.defaultRoute
    if (!defaultRoute || ['/dashboard', '/admin', '/', ''].includes(defaultRoute) || defaultRoute.endsWith('/dashboard')) {
      const dashboard = _.find(this.user.menus, { id: 'dashboard' })
      if (dashboard) {
        const root = adminLayoutRoot(this.$route)
        this.$router.push(`${root}/${dashboard.mdRoute}`)
      } else if (this.user.cpKey === 'cp_cow') {
        const root = adminLayoutRoot(this.$route)
        if (this.user.teams.indexOf('CashOnWeb Supervisor') !== -1) {
          this.$router.push(`${root}/cow/agents`)
        } else {
          this.$router.push(`${root}/cow/members`)
        }
      }
    } else {
      this.$router.replace(defaultRoute)
    }
  }
}
</script>
