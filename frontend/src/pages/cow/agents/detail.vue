<template>
  <q-dialog class="cow-agent-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">{{ edit ? 'Edit' : 'Create' }} New Agent</div>
      <div class="font-12 normal text-dark">Please fill in the information below about the agent.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-6">
          <q-input float-label="First Name"
                   autocomplete="no"
                   :error="$v.entity['First Name'].$error"
                   @blur="$v.entity['First Name'].$touch"
                   v-model="entity['First Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Last Name"
                   autocomplete="no"
                   :error="$v.entity['Last Name'].$error"
                   @blur="$v.entity['Last Name'].$touch"
                   v-model="entity['Last Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Email"
                   autocomplete="no"
                   :error="$v.entity['Email'].$error"
                   @blur="$v.entity['Email'].$touch"
                   v-model="entity['Email']"></q-input>
        </div>
        <div class="col-sm-6">
          <div class="row">
            <q-select class="phone-code col-4"
                      autocomplete="no"
                      float-label="Phone"
                      filter
                      autofocus-filter
                      v-model="entity['phoneCode']"
                      :options="countries" />
            <q-input class="phone-number col-8"
                     placeholder="Phone Only digits. No country dial code."
                     :error="$v.entity['phone'].$error"
                     @blur="$v.entity['phone'].$touch"
                     v-model="entity['phone']">
            </q-input>
          </div>
        </div>
        <div class="col-sm-12">
          <q-select v-model="entity['Locator']"
                    autocomplete="no"
                    filter
                    autofocus-filter
                    float-label="Locator"
                    placeholder="Please select locator."
                    :error="$v.entity['Locator'].$error"
                    @change="$v.entity['Locator'].$touch"
                    :options="LoadLocatorOptions" />
        </div>
        <div class="pt-20"
             :class="edit ? 'col-sm-12 text-center' : 'col-sm-12'">
          <div class="bold">Role</div>
          <div class="mt-8">
            <q-radio v-model="entity['User Type']"
                     color="blue"
                     :error="$v.entity['User Type'].$error"
                     :disable="($store.state.User.teams.indexOf('CashOnWeb Supervisor') !== -1)"
                     @change="$v.entity['User Type'].$touch"
                     val="Administrator"
                     label="Administrator"></q-radio>
            <q-radio v-model="entity['User Type']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['User Type'].$error"
                     :disable="($store.state.User.teams.indexOf('CashOnWeb Supervisor') !== -1) && edit"
                     @change="$v.entity['User Type'].$touch"
                     val="Agent"
                     label="Agent"></q-radio>
            <q-radio v-model="entity['User Type']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['User Type'].$error"
                     :disable="($store.state.User.teams.indexOf('CashOnWeb Supervisor') !== -1)"
                     @change="$v.entity['User Type'].$touch"
                     val="Compliance"
                     label="Compliance"></q-radio>
            <q-radio v-model="entity['User Type']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['User Type'].$error"
                     :disable="($store.state.User.teams.indexOf('CashOnWeb Supervisor') !== -1)"
                     @change="$v.entity['User Type'].$touch"
                     val="Supervisor"
                     label="Supervisor"></q-radio>
          </div>
        </div>
        <div class="col-sm-12 text-center pt-20"
             v-show="edit">
          <div class="bold">Status</div>
          <div class="mt-8">
            <q-radio v-model="entity['Status']"
                     color="blue"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Active"
                     label="Active"></q-radio>
            <q-radio v-model="entity['Status']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Inactive"
                     label="Inactive"></q-radio>
          </div>
        </div>
        <div v-if="$store.state.User.teams.indexOf('MasterAdmin') !== -1 || $store.state.User.teams.indexOf('CashOnWeb Platform Partner') !== -1 || $store.state.User.teams.indexOf('CashOnWeb Admin') !== -1"
             class="col-sm-12 text-center pt-20"
             v-show="edit">
          <div class="bold">Locked status </div>
          <div class="mt-8">
            <q-radio v-model="entity['locked']"
                     color="blue"
                     val="Unlock"
                     label="Unlocked"></q-radio>
            <q-radio v-model="entity['locked']"
                     color="blue"
                     class="ml-15"
                     val="Lock"
                     label="Lock"></q-radio>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks"
           v-if="edit">
        <div class="row"
             v-if="origin">
          <q-btn :label="origin['Status'] === 'Archived' ? 'Activate' : 'Archive'"
                 no-caps
                 :color="origin['Status'] === 'Archived' ? 'blue' : 'orange' "
                 @click="toggleStatus" />
          <q-btn label="Save Changes"
                 no-caps
                 color="positive"
                 @click="save" />
        </div>
        <q-btn label="Cancel"
               no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
      </div>
      <template v-else>
        <q-btn label="Cancel"
               no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
        <q-btn label="Create Agent"
               no-caps
               color="positive"
               class="main"
               @click="save" />
      </template>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request, toSelectOptions } from '../../../common'
import { required, email, helpers } from 'vuelidate/lib/validators'
import _ from 'lodash'
const countryList = require('country-data').countries
const alpha = helpers.regex('alpha', /^[0-9+-\s]*$/)

export default {
  name: 'cow-agent-detail-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        'User Type': 'Agent',
        'Status': 'Active',
        'phoneCode': 'CW',
        'phone': '',
        'locked': 'Unlocked'
      },
      LoadLocatorOptions: [],
      countries: []
    }
  },
  computed: {
    edit () {
      if (this.entity['User ID']) {
        return true
      }
      return false
    }
  },
  mounted () {
    countryList.all.map(x => {
      if (x.countryCallingCodes.length) {
        this.countries.push({
          label: x.emoji ? x.emoji + ' ' + x.countryCallingCodes[0] : x.name + ' ' + x.countryCallingCodes,
          value: x.alpha2,
          code: x.countryCallingCodes[0]
        })
      }
    })
    return this.countries
  },
  validations: {
    entity: {
      'First Name': {
        required
      },
      'Last Name': {
        required
      },
      'Email': {
        required,
        email
      },
      'phone': {
        required,
        alpha
      },
      'User Type': {
        required
      },
      'Locator': {
        required
      },
      'Status': {
        required
      }
    }
  },
  methods: {
    async show () {
      this.$q.loading.show()
      if (this.entity['Mobile Phone']) {
        const phoneList = this.entity['Mobile Phone'].split(' ')
        // console.log(phoneList)
        let code = 'CW'
        if (phoneList.length > 1) {
          this.entity['phone'] = this.entity['Mobile Phone'].split(phoneList[0])[1]
          this.countries.forEach(element => {
            // console.log(element)
            if (element.code === phoneList[0]) {
              code = element.value
            }
          })
        } else {
          this.entity['phone'] = phoneList[0]
        }
        this.entity['phoneCode'] = code
      }
      const resp = await request(`/admin/cow/locator/listAll`)
      this.$q.loading.hide()
      if (resp.success) {
        this.LoadLocatorOptions = toSelectOptions(resp.data)
      }
    },
    onSuccess (resp) {
      notify(resp.message)

      if (this.origin && this.origin['User ID']) {
        _.assignIn(this.origin, resp.data)
      }

      this.$root.$emit('reload-cow-agents')
      this._hide()
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      this.entity['Mobile Phone'] = this.entity['phone']
      const resp = await request(`/admin/cow/agents/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    },
    async toggleStatus () {
      if (!this.origin) {
        return
      }
      this.$q.loading.show()
      const resp = await request(`/admin/cow/agents/${this.entity['User ID']}/toggle-status`, 'post', {
        Status: this.origin['Status'] === 'Active' ? 'Archived' : 'Active'
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    }
  }
}
</script>

<style lang="scss">
.cow-agent-detail-dialog {
  .modal-content {
    width: 550px;
  }
  .phone-code {
    // border-right: none;
    padding-right: 0 !important;
    padding-left: 10px !important;
    min-width: 100px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .phone-number {
    border-left: none;
    max-width: calc(100% - 100px);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
</style>
