<template>
  <q-page id="cow__agents__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Partner"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-2">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}
                    <q-chip dense
                            :class="quick.preTotal >= 0 ? 'positive' : 'negative'">{{ quick.preTotal | percent(1, true) }}</q-chip>
                  </div>
                  <div class="description">Total</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalAdmin || 0 }}
                    <q-chip dense
                            :class="quick.preTotalAdmin >= 0 ? 'positive' : 'negative'">{{ quick.preTotalAdmin | percent(1, true) }}</q-chip>
                  </div>
                  <div class="description">Total Administrators</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-2">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalAgent || 0 }}
                    <q-chip dense
                            :class="quick.preTotalAgent >= 0 ? 'positive' : 'negative'">{{ quick.preTotalAgent | percent(1, true) }}</q-chip>
                  </div>
                  <div class="description">Total Agents</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalActive || 0 }}
                    <q-chip dense
                            :class="quick.preTotalActive >= 0 ? 'positive' : 'negative'">{{ quick.preTotalActive | percent(1, true) }}</q-chip>
                  </div>
                  <div class="description">Total Active Users</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-2">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.percentActive | 0 }}
                  </div>
                  <div class="description">%Active Users</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Agents Report</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-account-plus-outline"
                 color="positive"
                 label="Create New Agent"
                 @click="add"
                 class="btn-sm mr-10"
                 no-caps></q-btn>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="props.row['locked'] === 'Lock' ? 'locked' :  rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="props.row['Status'] === 'Active' ? 'positive' : 'negative'">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Create Date'">
              <div class="locked-column">
                <i v-if="props.row.locked === 'Lock'"
                   class="mdi mdi-lock-outline"></i>{{ _.get(props.row, col.field) }}
              </div>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-if="$store.state.User.teams.indexOf('MasterAdmin') !== -1"
                          v-close-overlay
                          @click.native="$c.loginAs(props.row['User ID'])">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

    <DetailDialog></DetailDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import CowPageMixin from '../../../mixins/cow/CowPageMixin'
import DetailDialog from './detail'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'cow-agents',
  mixins: [
    CowPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-cow-agents')
  ],
  components: {
    DetailDialog
  },
  mounted () {
    this.keyword = localStorage.getItem('agentId') ? localStorage.getItem('agentId') : ''
    localStorage.setItem('agentId', '')
  },
  data () {
    return {
      title: 'Agents',
      requestUrl: `/admin/cow/agents/list`,
      downloadUrl: `/admin/cow/agents/export`,
      keyword: '',
      dateRange: 'all',
      columns: generateColumns([
        'Create Date', 'User ID', 'First Name', 'Last Name',
        'User Type', 'Email', 'Mobile Phone', 'Load Locator',
        'Last Login Date/Time', 'Status', 'Actions'
      ], [], {
        'Create Date': 'u.createdAt',
        'User ID': 'u.id',
        'First Name': 'u.firstName',
        'Last Name': 'u.lastName',
        'User Type': 't.name',
        'Email': 'u.email',
        'Mobile Phone': 'u.mobilephone',
        'Load Locator': '',
        'Last Login Date/Time': 'u.lastLogin'
      }),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        }, {
          value: 'filter[u.mobilephone=]',
          label: 'Mobile Phone'
        }, {
          value: 'filter[u.status=]',
          label: 'Status',
          options: [
            { label: 'Active', value: 'active' },
            { label: 'Archived', value: 'closed' }
          ]
        }, {
          value: 'filter[t.name=]',
          label: 'User Type',
          options: [
            { label: 'Administrator', value: 'CashOnWeb Admin' },
            { label: 'Agent', value: 'CashOnWeb Agent' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    add () {
      this.$root.$emit('show-cow-agent-detail-dialog')
    },
    edit (row) {
      this.$root.$emit('show-cow-agent-detail-dialog', row)
    }
  }
}
</script>
<style lang="scss">
#cow__agents__index_page {
  .q-table {
    .locked-column {
      position: relative;
    }
    .locked {
      color: #fc5a5a;
      i.mdi-lock-outline {
        margin-right: 5px;
        position: absolute;
        line-height: 20px;
        left: -20px;
      }
    }
  }
}
</style>
