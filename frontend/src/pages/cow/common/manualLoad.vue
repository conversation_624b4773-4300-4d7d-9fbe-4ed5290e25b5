<template>
  <q-dialog class="cow-manual-load-dialog"
            v-model="visible">
    <template slot="title">
      <template>
        <div class="font-18 mb-2">Manual Load</div>
      </template>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div id="funding">
        <div class="col-sm-12 col-md-4 load-info">
          <div class="load-amount">
            <div class="input-group">
              <span>$</span>
              <input type="number"
                     step="0.1"
                     min="0.1"
                     v-model="loadAmount" />
            </div>
          </div>
          <q-radio dense
                   v-model="type"
                   val="credit"
                   label="Credit" />
          <q-radio dense
                   class="ml-10"
                   v-model="type"
                   val="debit"
                   label="Debit" />
          <div class="text-area">
            <p>Comments:</p>
            <q-input type="textarea"
                     :minlength="3"
                     :maxlength="155"
                     :rows="6"
                     v-model="content" />
            <p>Character Limit: {{content.length}}/155</p>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn label="Done"
               color="positive"
               class="load-btn"
               @click="load()" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { EventHandlerMixin, request, notifySuccess } from '../../../common'

export default {
  name: 'cow-manual-load-dialog',
  mixins: [
    Singleton,
    EventHandlerMixin('cow-manual-load-dialog', 'show')
  ],
  data () {
    return {
      loadAmount: 0,
      type: null,
      content: ''
    }
  },
  created () {
  },
  components: {},
  methods: {
    show () {
      this.loadAmount = 0
      this.type = null
    },
    async load () {
      if (this.loadAmount === 0) {
        notifySuccess(`Please input a non-zero amount!`, 'Message', 'negative')
        return
      }
      if (!this.type) {
        notifySuccess(`Please select the type`, 'Message', 'negative')
        return
      }
      if (this.content.length < 3 || this.content.length > 155) {
        notifySuccess(`Please enter a description content with a length of 3-155`, 'Message', 'negative')
        return
      }
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to ' + (this.type === 'credit' ? 'load' : 'unload') + ' the money $' + this.loadAmount + ' to the partner',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/cow/manualLoad`, 'post', {
          'loadAmount': this.loadAmount,
          'type': this.type,
          'content': this.content
        })
        if (resp.success) {
          this.cancel()
        }
        this.$q.loading.hide()
      }).catch(() => {})
    },
    cancel () {
      this._hide()
      this.$root.$emit('reload-cow-funding')
      this.$root.$emit('reload-partner-balance-chart')
    }
  }
}
</script>

<style lang="scss">
.cow-manual-load-dialog {
  .modal-content {
    width: 450px;
  }
  .single {
    width: 70%;
  }
  .close-btn {
    background-color: #7ac142;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
  }
  .load-amount {
    max-width: 350px;
    margin: 0 auto;
    .input-group {
      color: #231f20;
      font-weight: 600;
      font-size: 22px;
      display: flex;
      justify-content: center;
      width: 150px;
      margin: 20px auto;
      border-radius: 10px;
      border: solid 1px #e2e2ea;
      padding-left: 20px;
      span {
        line-height: 53px;
      }
      input {
        display: inline-block;
        padding: 10px 5px;
        border-radius: 10px;
        text-align: center;
        border: none;
        outline: none;
        max-width: 80%;
        &:active,
        &:hover,
        &:focus {
          border: none;
        }
      }
    }
  }
  .text-area {
    max-width: 80%;
    margin: 0 auto;
    margin-top: 10px;
    position: relative;
    p {
      position: absolute;
      right: 5px;
      bottom: 5px;
      margin: 0;
      opacity: 0.5;
      color: #000;
      font-size: 14px;
    }
  }
}
</style>
