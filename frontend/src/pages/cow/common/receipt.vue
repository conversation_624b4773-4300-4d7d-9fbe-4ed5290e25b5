<template>
  <q-dialog class="cow-receipt-dialog"
            v-model="visible">
    <template slot="title">
      <template>
        <div class="mb-5">
        </div>
        <div class="font-18 mb-2">Transaction Receipt</div>
      </template>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div id="receipt">
        <div class="col-sm-12 col-md-4 transaction-info">
          <img src="/static/jake/icons/cow/logo.svg">
          <q-list bordered
                  separator>
            <template v-if="entity['type'] === 'transaction'">
              <q-item v-ripple>
                <q-item-side>
                  Transaction Details:
                </q-item-side>
                <q-item-main>
                  {{entity['Date & time']}}
                </q-item-main>
              </q-item>
              <q-item v-ripple>
                <q-item-side>
                  Amount:
                </q-item-side>
                <q-item-main>
                  {{entity['AmountStr']}}
                </q-item-main>
              </q-item>
              <q-item v-ripple>
                <q-item-side>
                  Merchant Name:
                </q-item-side>
                <q-item-main>
                  {{entity['Merchant Name']}}
                </q-item-main>
              </q-item>
              <q-item v-ripple>
                <q-item-side>
                  Transaction Type:
                </q-item-side>
                <q-item-main>
                  {{entity['Transaction Type']}}
                </q-item-main>
              </q-item>
            </template>
            <template v-else-if="entity['type'] === 'load'">
              <q-item v-ripple>
                <q-item-side>
                  Load Location:
                </q-item-side>
                <q-item-main>
                  {{entity['Date & time']}}
                </q-item-main>
              </q-item>
              <q-item v-ripple>
                <q-item-side>
                  Load Amount Paid:
                </q-item-side>
                <q-item-main>
                  {{entity['Amount Tx']}}
                </q-item-main>
              </q-item>
              <q-item v-ripple>
                <q-item-side>
                  Loaded Amount to COW Account:
                </q-item-side>
                <q-item-main>
                  {{entity['Loaded']}}
                </q-item-main>
              </q-item>
              <q-item v-ripple>
                <q-item-side>
                  Date & time:
                </q-item-side>
                <q-item-main>
                  {{entity['Date & Time']}}
                </q-item-main>
              </q-item>
            </template>
            <template v-else-if="entity['type'] === 'unload'">
              <q-item v-ripple>
                <q-item-side>
                  Load Location:
                </q-item-side>
                <q-item-main>
                  {{entity['Date & time']}}
                </q-item-main>
              </q-item>
              <q-item v-ripple>
                <q-item-side>
                  UnLoaded Amount Paid:
                </q-item-side>
                <q-item-main>
                  {{entity['PayAmount'] | moneyFormat}}
                </q-item-main>
              </q-item>
              <q-item v-ripple>
                <q-item-side>
                  UnLoaded Amount to COW Account:
                </q-item-side>
                <q-item-main>
                  {{entity['Amount Tx']}}
                </q-item-main>
              </q-item>
              <q-item v-ripple>
                <q-item-side>
                  Date & time:
                </q-item-side>
                <q-item-main>
                  {{entity['Date & Time']}}
                </q-item-main>
              </q-item>
            </template>
          </q-list>
          <p class="mt-10 mb-5">Taxes are included in this transaction</p>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn label="Print"
               color="positive"
               class="print-btn load-btn"
               @click="print" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { EventHandlerMixin } from '../../../common'
import html2canvas from 'html2canvas'

export default {
  name: 'cow-receipt-dialog',
  mixins: [
    Singleton,
    EventHandlerMixin('cow-receipt-dialog', 'show')
  ],
  data () {
    return {
      transaction: {

      }
    }
  },
  created () { },
  components: {},
  methods: {
    show () {
      console.log(this.entity)
    },
    cancel () {
      this._hide()
    },
    print () {
      let target = null
      const name = 'Transaction Receipt'
      target = document.getElementById('receipt')
      // target = document.body
      // console.log(target)
      this.$q.loading.show()
      const targetCss = window.getComputedStyle(target)

      const options = {
        scale: 2,
        width: parseInt(targetCss.width, 10),
        height: parseInt(targetCss.height, 10)
        // y: target.offsetTop
      }
      // console.log(options)
      html2canvas(target, options).then(canvas => {
        // canvas is the final rendered <canvas> element
        const imgData = canvas.toDataURL('image/jpeg')
        console.log(imgData)
        this.fileDownload(imgData, name)
        this.$q.loading.hide()
      })
    },
    fileDownload (downloadUrl, filename) {
      let aLink = document.createElement('a')
      aLink.style.display = 'none'
      aLink.href = downloadUrl
      aLink.download = `${filename}`
      // 触发点击-然后移除
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    }
  }
}
</script>

<style lang="scss">
.cow-receipt-dialog {
  .modal-content {
    width: 450px;
  }
  .single {
    width: 70%;
  }
  .load-btn {
    font-size: 16px;
    margin: 0 auto;
    .q-btn-inner {
      line-height: 28px;
    }
  }
  .close-btn {
    background-color: #7ac142;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
  }
  .print-btn {
    background-color: #9c85ca !important;
  }
  .transaction-info {
    img {
      max-width: 60%;
      margin: 0 auto;
    }
    .q-list {
      border-top: none;
      border-right: none;
      border-left: none;
      // border-color: #e2e2ea;
      font-size: 15px;
      max-width: 325px;
      margin: 0 auto;
    }
    .q-item {
      padding: 10px 0;
    }
    .q-item-side {
      color: #92929e;
    }
    .q-item-division {
      border-color: #e2e2ea;
    }
    .q-item-main {
      text-align: right;
      color: #171725;
      font-size: 16px;
    }
  }
}
</style>
