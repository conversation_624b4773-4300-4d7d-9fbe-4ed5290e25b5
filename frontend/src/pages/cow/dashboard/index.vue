<template>
  <q-page class="cow__dashboard__index_page">
    <div class="page-header">
      <div class="title">{{title}}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Partner"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-xs-6 col-sm-3"
             v-for="(n, i) in numbers"
             :key="i">
          <q-card>
            <q-card-title>
              <span>{{ n.title }}</span>
              <q-chip v-if="n.title == 'Total Members' || n.title == 'Member Spend' "
                      dense
                      :class="n.delta >= 0 ? 'positive' : 'negative'">{{ n.delta | percent(1, true) }}</q-chip>
            </q-card-title>
            <q-card-main>
              <div v-if="n.title != 'Member Spend'"
                   class="font-22 heavy mb-10">{{ n.value }}</div>
              <div v-else
                   class="font-22 heavy mb-10">{{ n.value | moneyFormat}}</div>
              <a :href="n.url"
                 @click="viewReport(n.title)"
                 class="link bold font-12">View Report <i class="mdi mdi-arrow-right"></i></a>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xs-6 col-sm-4"
             v-for="(n, i) in revenue"
             :key="i + numbers.length">
          <q-card>
            <q-card-title>
              <span>{{ n.title }}</span>
              <q-chip dense
                      :class="n.delta >= 0 ? 'positive' : 'negative'">{{ n.delta | percent(1, true) }}</q-chip>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy mb-10">{{ n.value  | moneyFormat}}</div>
              <a :href="n.url"
                 @click="viewReport('commissions')"
                 class="link bold font-12">View Report <i class="mdi mdi-arrow-right"></i></a>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xs-12 col-sm-12">
          <PartnerBalance :viewReportFlag="true"
                          v-model="chartDateRange"></PartnerBalance>
        </div>
        <div class="col-xs-12 col-sm-12">
          <ProgramBalance :viewReportFlag="true"
                          v-model="chartDateRange">
          </ProgramBalance>
        </div>
        <div class="col-xs-12 col-sm-6"
             id="membersChart">
          <q-card class="h-400">
            <q-card-title>
              <div class="row chart-label">
                <div>
                  <p>Members</p>
                  <p class="label-header">{{memberTotal}} Members</p>
                </div>
                <q-btn class="btn-sm ml-auto mr-8 square"
                       color="positive"
                       @click="download('member')"
                       icon="mdi-file-document-outline"></q-btn>
                <q-btn class="btn-sm square"
                       color="blue"
                       @click="viewReport('New Members')"
                       icon="mdi-chart-line"></q-btn>
              </div>
            </q-card-title>
            <div class="member-status row">
              <div class="member-status-item col-xs-6 col-3"
                   v-for="(item,i) in memberNumberList"
                   :key="i">
                <q-knob font-size="12px"
                        size="50px"
                        v-model="memberNumberList[i]['delta']"
                        disabled
                        readonly
                        :class="knobClass(i)"
                        class="q-ma-md">
                  {{ item.delta }}%
                </q-knob>
                <div class="count-desc">
                  <p class="count">{{item.count}}</p>
                  <p class="name">{{i}}</p>
                </div>
              </div>
            </div>
          </q-card>
        </div>
        <div class="col-xs-12 col-sm-6"
             id='commissionsChart'>
          <q-card class="h-400">
            <q-card-title>
              <div class="row chart-label">
                <div>
                  <p>Commissions</p>
                  <p class="label-header">{{commissions ? commissions.total : 0 | moneyFormat}}</p>
                </div>
                <q-btn class="btn-sm ml-auto mr-8 square"
                       color="positive"
                       @click="download('commissions')"
                       icon="mdi-file-document-outline"></q-btn>
                <q-btn class="btn-sm square"
                       color="blue"
                       @click="viewReport('commissions')"
                       icon="mdi-chart-line"></q-btn>
              </div>
              <p class="label-extra">{{commissions ? commissions.count : 0}} Fees</p>
            </q-card-title>
            <div id="CommissionsChart"
                 style="height: 300px"></div>
          </q-card>
        </div>
        <div class="col-xs-12 col-sm-6"
             id='spendingChart'>
          <q-card class="h-400">
            <q-card-title>
              <div class="row chart-label">
                <div>
                  <p>Card Spending <q-chip dense
                            :class="spenDelta >= 0 ? 'positive' : 'negative'">{{ spenDelta | percent(1, true) }}</q-chip>
                  </p>
                  <p class="label-header">{{spend | moneyFormat}}</p>
                </div>
                <q-btn class="btn-sm ml-auto mr-8 square"
                       color="positive"
                       @click="download('spending')"
                       icon="mdi-file-document-outline"></q-btn>
                <q-btn class="btn-sm square"
                       color="blue"
                       @click="viewReport('Member Spend')"
                       icon="mdi-chart-line"></q-btn>
              </div>
            </q-card-title>
            <div id="SpendChart"
                 style="height: 300px"></div>
          </q-card>
        </div>
        <div class="col-xs-12 col-sm-6">
          <q-card class="h-400">
            <q-card-title>
              <div class="row chart-label">
                <div>
                  <p>Credits &amp; Debits <q-chip dense
                            :class="creditsDelta >= 0 ? 'positive' : 'negative'">{{ creditsDelta | percent(1, true) }}</q-chip>
                  </p>
                  <p class="label-header">{{credits ? credits.difference : 0 | moneyFormat}}</p>
                  <p class="label-subtitle"><span class="credits">Credits: {{credits ? credits.credits : 0 | moneyFormat}}</span> - <span class="debits">Debits: {{credits ? credits.debits : 0 | moneyFormat}}</span></p>
                </div>
              </div>
            </q-card-title>
            <div id="CreditsChart"
                 style="height: 300px"></div>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script>
import CowPageMixin from '../../../mixins/cow/CowPageMixin'
import ChartMixin from '../../../mixins/cow/ChartMixin'
import PartnerBalance from '../common/partnerBalance'
import ProgramBalance from '../common/programBalance'
import { request, notify } from '../../../common'
import html2canvas from 'html2canvas'

export default {
  name: 'cow-Dashboard',
  mixins: [
    CowPageMixin,
    ChartMixin
  ],
  components: {
    PartnerBalance,
    ProgramBalance
  },
  data () {
    return {
      loading: false,
      commissions: null,
      quick: {
        balance: 100
      },
      dateRanges: [
        {
          label: 'All',
          value: 'all'
        },
        {
          label: 'Daily',
          value: 'today'
        },
        {
          label: 'Weekly',
          value: 'week'
        },
        {
          label: 'Monthly',
          value: 'month'
        },
        {
          label: 'Quarterly',
          value: 'quarter'
        },
        {
          label: 'Yearly',
          value: 'year'
        },
        {
          label: 'Custom Range',
          value: 'custom_range'
        }
      ],
      dateRange: 'all',
      chartDateRange: 'all',
      title: 'Dashboard Overview',
      numbers: [
        { title: 'Total Members', delta: 0, value: '0', url: 'javascript:' },
        { title: 'Active Members', delta: 0, value: '0', url: 'javascript:' },
        { title: 'Member Spend', delta: 0, value: '0', url: 'javascript:' },
        { title: 'Active Agents', delta: 0, value: '0', url: 'javascript:' }
      ],
      revenue: [
        { title: 'Total Fee Revenue', delta: 0, value: 0, url: '/j/cow/report/commissions' },
        { title: 'Partner Revenue', delta: 0, value: 0, url: 'javascript:' },
        { title: 'Platform Revenue', delta: 0, value: 0, url: 'javascript:' }
      ],
      cost: null,
      spend: 0,
      credits: null,
      creditsDelta: 0,
      spenDelta: 0,
      memberNumberList: [],
      memberTotal: 0,
      knobColor: {
        'Active': 'active-color',
        'Onboard (KYC)': 'kyc-color',
        'Onboard (Address)': 'addrerss-color',
        'KYC Failed': 'kyc-failed-color',
        'Onboard (Email)': 'email-color',
        'Inactive': 'inactive-color',
        'Closed': 'closed-color',
        'On Hold': 'on-hold-color'
      }
    }
  },
  methods: {
    download (type) {
      let userAgent = navigator.userAgent
      if (userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Chrome') === -1) {
        return notify('Please take a screenshot manually or use another browser to open the website to save the picture!', 'negative')
      }
      let target = null
      let name = ''
      if (type === 'member') {
        target = document.getElementById('membersChart')
        name = 'memberChart'
      } else if (type === 'spending') {
        target = document.getElementById('spendingChart')
        name = 'spendingChart'
      } else {
        target = document.getElementById('commissionsChart')
        name = 'commissionsChart'
      }
      // target = document.body
      // console.log(target)
      this.$q.loading.show()
      const targetCss = window.getComputedStyle(target)

      const options = {
        scale: 2,
        width: parseInt(targetCss.width, 10),
        height: parseInt(targetCss.height, 10),
        y: target.offsetTop + 70
      }
      // console.log(options)
      html2canvas(target, options).then(canvas => {
        // canvas is the final rendered <canvas> element
        const imgData = canvas.toDataURL('image/jpeg')
        console.log(imgData)
        this.fileDownload(imgData, name)
        this.$q.loading.hide()
      })
    },
    fileDownload (downloadUrl, filename) {
      let aLink = document.createElement('a')
      aLink.style.display = 'none'
      aLink.href = downloadUrl
      aLink.download = `${filename}`
      // 触发点击-然后移除
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    },
    async reload () {
      this.$q.loading.show()
      let data = {}
      if (this.$refs && this.$refs.dateRangeFilter) {
        data = this.$refs.dateRangeFilter.params()
      }
      this.chartDateRange = data.period
      const resp = await request(`/admin/cow/dashboard/static`, 'get', data)
      if (resp.success) {
        // account
        this.numbers[1]['value'] = resp.data.member.active
        this.numbers[0]['delta'] = resp.data.member.preNew
        this.numbers[0]['value'] = resp.data.member.total
        this.numbers[2]['delta'] = resp.data.spend.preTotal
        this.numbers[2]['value'] = resp.data.spend.total
        this.numbers[3]['value'] = resp.data.agent
        this.memberTotal = resp.data.member.total
        this.memberNumberList = resp.data.member.statusList
        // Spend Chart
        this.spenDelta = resp.data.spend.preTotal
        this.spend = resp.data.spend.total
        resp.data.spend.type = 'SpendChart'
        this.updateChart(resp.data.spend)
        // Commissions Chart
        this.revenue[0]['value'] = resp.data.commissions.total
        this.revenue[0]['delta'] = resp.data.commissions.preTotal
        this.revenue[1]['value'] = resp.data.commissions.partner
        this.revenue[1]['delta'] = resp.data.commissions.prePartner
        this.revenue[2]['value'] = resp.data.commissions.tern
        this.revenue[2]['delta'] = resp.data.commissions.preTern
        this.commissions = {
          'total': resp.data.commissions.total,
          'count': resp.data.commissions.count
        }
        resp.data.commissions.type = 'CommissionsChart'
        this.updateChart(resp.data.commissions)
        // Credits Chart
        this.credits = resp.data.credits
        this.creditsDelta = resp.data.credits.predifference
        resp.data.credits.type = 'CreditsChart'
        this.updateChart(resp.data.credits)
      }
      this.$q.loading.hide()
    },
    viewReport (type) {
      if (type === 'commissions') {
        this.$router.push(`/j/cow/report/commissions`)
      }
      if (type === 'Active Agents') {
        this.$router.push(`/j/cow/agents`)
      }
      if (type === 'Active Members' || type === 'New Members') {
        this.$router.push(`/j/cow/members`)
      }
      if (type === 'Member Spend') {
        this.$router.push(`/j/cow/report/approved`)
      }
    },
    knobClass (i) {
      return this.knobColor[i]
    }
  },
  async mounted () {
    this.reload()
  }
}
</script>
<style lang="scss">
.cow__dashboard__index_page {
  .view-report-big-btn {
    background: #5190b7;
    color: #fff;
    max-height: 38px;
    margin-top: 10px;
  }
  .chart-label {
    display: flex;
    align-items: center;
    p {
      margin: 0;
      line-height: 26px;
      font-size: 16px;
      color: #171725;
    }
    .label-header {
      font-size: 28px;
    }
    .btn-sm {
      max-height: 32px;
    }
  }
  .label-extra {
    margin: 0;
    color: #787393;
    font-size: 14px;
    line-height: 24px;
  }
  .label-subtitle {
    font-size: 14px;
    .credits {
      color: #00d993;
    }
    .debits {
      color: #fc5a5a;
    }
  }
  a.link {
    color: #7ac142 !important;
  }
  .member-status {
    display: flex;
    .member-status-item {
      display: flex;
      //  align-items: center;
      margin-top: 15px;
      .count-desc {
        display: inline-block;
        max-width: calc(100% - 82px);
      }
      p {
        margin: 0;
      }
      .count {
        color: #171726;
        font-size: 16px;
      }
      .name {
        color: #696975;
        font-size: 14px;
      }
      .q-knob-label {
        color: #171726;
        font-weight: 600;
      }
      .q-ma-md {
        margin: 6px;
      }
    }
  }
  .active-color {
    color: #67e8bd;
  }
  .kyc-color {
    color: #ff94d8;
  }
  .addrerss-color {
    color: #ffc200;
  }
  .kyc-failed-color {
    color: rgba($color: #ff974a, $alpha: 0.1);
  }
  .email-color {
    color: #0064ff;
  }
  .inactive-color {
    color: #444450;
  }
  .closed-color {
    color: rgba($color: #fc5a5a, $alpha: 0.1);
  }
  .on-hold-color {
    color: #ff974a;
  }
}
@media screen and (max-width: 475px) {
  #jake_admin .page-header {
    display: block;
    .fun-group {
      display: block;
    }
  }
  #membersChart .h-400 {
    height: auto !important;
  }
}
</style>
