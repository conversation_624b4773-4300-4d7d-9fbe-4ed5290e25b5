<template>
  <q-dialog class="cow-member-kyc-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">View KYC Documentation</div>
      <div class="font-12 normal">Here are the KYC documents provided by the customer. Please review to ensure everything was uploaded correctly.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <template>
        <p>Selfie</p>
        <img v-if="entity.imageId && entity.imageId.selfImageId "
             :src="entity.selfImage">
        <div class="self-item file-section">
          <span class="type hide">self</span>
          <input type="file"
                 class="drop-file-area-input hide"
                 accept="image/*"
                 @change="selectedFile($event.target)"
                 name="file" />
          <div class="selected-file-area mv-20 hide">
            <img class="preview"
                 :src="''"
                 alt="" />
            <div class="elements">
              <a href="javascript:"
                 @click="uploadFile($event.target)"
                 class="bold">
                <q-icon name="mdi-cloud-upload-outline"></q-icon> Click to Upload
              </a>
              <span class="tip"></span>
              <a href="javascript:"
                 @click="rotatePicture($event.target, -90)"
                 class="ml-auto mr-10">
                <q-icon name="mdi-rotate-left"></q-icon>
                <q-tooltip>Rotate left</q-tooltip>
              </a>
              <a href="javascript:"
                 @click="rotatePicture($event.target, 90)"
                 class="mr-10">
                <q-icon name="mdi-rotate-right"></q-icon>
                <q-tooltip>Rotate right</q-tooltip>
              </a>
              <a href="javascript:"
                 @click="removeFile($event.target)"
                 class="red">
                <q-icon name="mdi-close"></q-icon> Remove
              </a>
            </div>
          </div>
        </div>
        <div class="row option-btn">
          <div v-if="entity.kycDeny !== true && entity.isAllKycPassed !== true"
               class="upload-btn"
               @click="chooseFile($event.target, 'self')">Upload New Image</div>
          <q-btn v-if="entity.selfImage && $store.state.User.teams.indexOf('CashOnWeb Compliance') !== -1"
                 label="Download Image"
                 no-caps
                 class="download-btn"
                 @click="downLoad('self')" />
        </div>
      </template>
      <template v-if="entity.kycDeny !== true && entity.isAllKycPassed !== true">
        <p>Government Issued ID</p>
        <div class="row select-item">
          <div class="col-sm-6">
            <q-select autocomplete="no"
                      placeholder="Country"
                      :options="countries"
                      filter
                      autofocus-filter
                      v-model="entity['defaultCountryId']"></q-select>
          </div>
          <div class="col-sm-5">
            <q-select autocomplete="no"
                      placeholder="Select Type"
                      filter
                      :options="typeOptions"
                      autofocus-filter
                      :before="stateBefore"
                      v-model="entity['provider']"></q-select>
          </div>
        </div>
      </template>
      <template>
        <p>Front of ID</p>
        <img v-if="entity.imageId && entity.imageId.frontImageId"
             :src="entity.frontImage">
        <div class="front-item file-section">
          <span class="type hide">front</span>
          <input type="file"
                 class="drop-file-area-input hide"
                 accept="image/*"
                 @change="selectedFile($event.target)"
                 name="file" />
          <div class="selected-file-area mv-20 hide">
            <img class="preview"
                 :src="''"
                 alt="" />
            <div class="elements">
              <a href="javascript:"
                 @click="uploadFile($event.target)"
                 class="bold">
                <q-icon name="mdi-cloud-upload-outline"></q-icon> Click to Upload
              </a>
              <span class="tip"></span>
              <a href="javascript:"
                 @click="rotatePicture($event.target, -90)"
                 class="ml-auto mr-10">
                <q-icon name="mdi-rotate-left"></q-icon>
                <q-tooltip>Rotate left</q-tooltip>
              </a>
              <a href="javascript:"
                 @click="rotatePicture($event.target, 90)"
                 class="mr-10">
                <q-icon name="mdi-rotate-right"></q-icon>
                <q-tooltip>Rotate right</q-tooltip>
              </a>
              <a href="javascript:"
                 @click="removeFile($event.target)"
                 class="red">
                <q-icon name="mdi-close"></q-icon> Remove
              </a>
            </div>
          </div>
        </div>
        <div class="row option-btn">
          <div v-if="entity.kycDeny !== true && entity.isAllKycPassed !== true"
               class="upload-btn"
               @click="chooseFile($event.target, 'front')">Upload New Image</div>
          <q-btn v-if="entity.frontImage && $store.state.User.teams.indexOf('CashOnWeb Compliance') !== -1"
                 label="Download Image"
                 no-caps
                 class="download-btn"
                 @click="downLoad('front')" />
        </div>
      </template>
      <template v-if="entity.providerType !== 'PSP'">
        <p>Back of ID</p>
        <img v-if="entity.imageId && entity.imageId.backImageId"
             :src="entity.backImage">
        <div class="back-item file-section">
          <span class="type hide">back</span>
          <input type="file"
                 class="drop-file-area-input hide"
                 accept="image/*"
                 @change="selectedFile($event.target)"
                 name="file" />
          <div class="selected-file-area mv-20 hide">
            <img class="preview"
                 :src="''"
                 alt="" />
            <div class="elements">
              <a href="javascript:"
                 @click="uploadFile($event.target)"
                 class="bold">
                <q-icon name="mdi-cloud-upload-outline"></q-icon> Click to Upload
              </a>
              <span class="tip"></span>
              <a href="javascript:"
                 @click="rotatePicture($event.target, -90)"
                 class="ml-auto mr-10">
                <q-icon name="mdi-rotate-left"></q-icon>
                <q-tooltip>Rotate left</q-tooltip>
              </a>
              <a href="javascript:"
                 @click="rotatePicture($event.target, 90)"
                 class="mr-10">
                <q-icon name="mdi-rotate-right"></q-icon>
                <q-tooltip>Rotate right</q-tooltip>
              </a>
              <a href="javascript:"
                 @click="removeFile($event.target)"
                 class="red">
                <q-icon name="mdi-close"></q-icon> Remove
              </a>
            </div>
          </div>
        </div>
        <div class="row option-btn">
          <div v-if="entity.kycDeny !== true && entity.isAllKycPassed !== true"
               class="upload-btn"
               @click="chooseFile($event.target, 'back')">Upload New Image</div>
          <q-btn v-if="entity.backImage && $store.state.User.teams.indexOf('CashOnWeb Compliance') !== -1"
                 label="Download Image"
                 no-caps
                 class="download-btn"
                 @click="downLoad('back')" />
        </div>
      </template>
      <template>
        <p>Bill or Bank Statement</p>
        <img v-if="entity.imageId && entity.imageId.billImageId"
             :src="entity.billImage">
        <div class="bill-item file-section">
          <span class="type hide">bill</span>
          <input type="file"
                 class="drop-file-area-input hide"
                 accept="image/*"
                 @change="selectedFile($event.target)"
                 name="file" />
          <div class="selected-file-area mv-20 hide">
            <img class="preview"
                 :src="''"
                 alt="" />
            <div class="elements">
              <a href="javascript:"
                 @click="uploadFile($event.target)"
                 class="bold">
                <q-icon name="mdi-cloud-upload-outline"></q-icon> Click to Upload
              </a>
              <span class="tip"></span>
              <a href="javascript:"
                 @click="rotatePicture($event.target, -90)"
                 class="ml-auto mr-10">
                <q-icon name="mdi-rotate-left"></q-icon>
                <q-tooltip>Rotate left</q-tooltip>
              </a>
              <a href="javascript:"
                 @click="rotatePicture($event.target, 90)"
                 class="mr-10">
                <q-icon name="mdi-rotate-right"></q-icon>
                <q-tooltip>Rotate right</q-tooltip>
              </a>
              <a href="javascript:"
                 @click="removeFile($event.target)"
                 class="red">
                <q-icon name="mdi-close"></q-icon> Remove
              </a>
            </div>
          </div>
        </div>
        <div class="row option-btn">
          <div v-if="entity.kycDeny !== true && entity.isAllKycPassed !== true"
               class="upload-btn"
               @click="chooseFile($event.target,'bill')">Upload New Image</div>
          <q-btn v-if="entity.billImage && $store.state.User.teams.indexOf('CashOnWeb Compliance') !== -1"
                 label="Download Image"
                 no-caps
                 class="download-btn"
                 @click="downLoad('bill')" />
        </div>
      </template>
    </template>
    <template slot="buttons">
      <div v-if="entity['Status']"
           class="stacks">
        <div class="kyc-box">
          <div class="form-field-title">KYC Status</div>
          <q-chip v-if="entity.isAllKycPassed"
                  class="font-12 mv-5 approved">Manually Approved
          </q-chip>
          <q-chip v-if="!entity.isAllKycPassed"
                  class="font-12 mv-5 negative">Failed</q-chip>
          <Statuses :statuses="entity.kycStatuses"></Statuses>
          <div class="mv-10 text-negative"
               v-if="!entity.isAllKycPassed">
            <span v-if="entity['kycStatuses']['ID Scan'] === null">Unable to read image</span>
            <span v-else-if="entity['kycStatuses']['ID Scan'] !== true">{{ entity['kycStatuses']['ID Scan'] }}</span>
          </div>
          <div v-if="entity.ofacSuccessAt"
               class="font-12 text-faded">
            <span>OFAC approved </span>
            <span notranslate=""> @ </span>
            <span>{{ entity.ofacSuccessAt }}</span>
          </div>
          <div v-if="entity.kycApprover"
               class="font-12 text-faded">
            <span>Manually approved by </span>
            <span notranslate="">{{ entity.kycApprover }} @ </span>
            <span>{{ entity.kycApprovedAt }}</span>
          </div>
        </div>
        <div v-if="!entity.isAllKycPassed"
             class="row">
          <q-btn v-if="entity['kycDeny'] !== true"
                 label="Deny"
                 no-caps
                 class="deny-btn"
                 @click="deny" />
          <q-btn v-if="entity['kycDeny'] !== true && entity['kycStatuses']['OFAC'] === true"
                 label="Manually Approve"
                 no-caps
                 class="manually-btn"
                 @click="manually" />
        </div>
      </div>
    </template>
    <IdManualDialog></IdManualDialog>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import Statuses from './id_status'
import IdManualDialog from './id_manual'
import { request, notifyResponse, toSelectOptions } from '../../../common'
import _ from 'lodash'
import $ from 'jquery'
import MexStateListMixin from '../../../mixins/mex/MexStateListMixin'

export default {
  name: 'cow-member-kyc-detail-dialog',
  mixins: [
    Singleton,
    MexStateListMixin
  ],
  components: {
    Statuses,
    IdManualDialog
  },
  data () {
    return {
      typeOptions: [],
      uploading: false
    }
  },
  watch: {
    'entity.defaultCountryId' () {
      if (this.entity['defaultCountryId']) {
        this.getType()
      }
    },
    'entity.provider' () {
      this.typeOptions.forEach(e => {
        if (e.value === this.entity.provider) {
          this.entity.providerType = e.stamp
        }
      })
    }
  },
  methods: {
    async getType () {
      if (this.entity.kycDeny !== true && this.entity.isAllKycPassed !== true) {
        this.$q.loading.show()
        const resp = await request(`/admin/cow/kyc-setting/${this.entity['defaultCountryId']}`)
        if (resp.success && resp.data) {
          this.typeOptions = toSelectOptions(resp.data)
        }
        this.$q.loading.hide()
      }
    },
    hide () {
      const self = this
      $('.file-section').each(function () {
        self.removeFile($(this).find('.selected-file-area a'))
      })
    },
    chooseFile (a, type) {
      console.log(type)
      const parent = $(a).parent().parent()
      let file
      if (type === 'self') {
        file = parent.find('.' + type + '-item .drop-file-area-input')
      }
      if (type === 'front') {
        file = parent.find('.' + type + '-item .drop-file-area-input')
      }
      if (type === 'back') {
        file = parent.find('.' + type + '-item .drop-file-area-input')
      }
      if (type === 'bill') {
        file = parent.find('.' + type + '-item .drop-file-area-input')
      }
      file.click()
    },
    selectedFile ($file, file) {
      if (!file && $file.files.length <= 0) {
        return
      }
      if (!file) {
        file = $file.files[0]
      }

      var name = file.name.toLowerCase()
      if (!name.endsWith('.jpg') && !name.endsWith('.jpeg') && !name.endsWith('.png')) {
        this.resetFile($file)
        notifyResponse('Only JPG and PNG formats are supported!')
        return
      }
      if (file.size > 5242880) { // 5MB = 5 * 1024 * 1024
        this.resetFile($file)
        notifyResponse('The file is too large. Please compress it to under 5MB first!')
        return
      }

      var $parent = $($file).parent()
      var $selected = $parent.find('.selected-file-area')
      var $type = $parent.find('.type').html()
      if ($type === 'self') {
        this.entity.selfImage = null
      }
      if ($type === 'front') {
        this.entity.frontImage = null
      }
      if ($type === 'back') {
        this.entity.backImage = null
      }
      if ($type === 'bill') {
        this.entity.billImage = null
      }
      console.log(this.entity)
      $selected.removeClass('hide failed')
      $parent.find('.drop-file-area').addClass('hide')

      this.displayPicture($selected.find('img.preview')[0], file)
    },
    doUploadFile ($a, file) {
      var $parent = $($a).parents('.file-section')
      var $selected = $parent.find('.selected-file-area')
      var $type = $parent.find('.type').html()
      var $tip = $selected.find('.tip')
      $tip.text('Uploading file...')

      var formData = new FormData()
      formData.append('category', 'idology_file')
      formData.append('file', file)

      var oReq = new XMLHttpRequest()
      oReq.open('POST', '/attachments', true)
      oReq.setRequestHeader('X-Requested-With', 'XMLHttpRequest')
      oReq.onreadystatechange = function () {
        if (oReq.readyState === 4 && oReq.status === 200) {
          var resp = JSON.parse(oReq.responseText)
          if (!resp.success) {
            notifyResponse(resp.message)
            this.removeFile($selected.find('a'))
            return
          }
          console.log($type)
          if ($type === 'self') {
            this.entity.imageId.selfImageId = resp.data.file.id
          }
          if ($type === 'front') {
            this.entity.imageId.frontImageId = resp.data.file.id
          }
          if ($type === 'back') {
            this.entity.imageId.backImageId = resp.data.file.id
          }
          if ($type === 'bill') {
            this.entity.imageId.billImageId = resp.data.file.id
          }
          console.log(this.entity)
          this.uploading = false
          $tip.text('Uploaded. Ready to verify.')
          $selected.addClass('success')
          this.resetFile($parent.find('input[type=file]')[0])
        }
      }.bind(this)
      let that = this
      oReq.upload.onprogress = function (e) {
        $tip.text('Uploading file... ')
        that.uploading = true
      }
      oReq.send(formData)

      $parent.data('req', oReq)
    },
    removeFile ($a) {
      var $parent = $($a).parents('.file-section').eq(0)
      var req = $parent.data('req')
      if (req) {
        req.abort()
      }
      var $type = $parent.find('.type').html()
      var $selected = $parent.find('.selected-file-area')
      $selected.addClass('hide').removeClass('success failed')
      $selected.find('.elements a').not('.red').show()
      $selected.find('.tip').text('')
      $selected.find('img.preview').attr('src', '')

      $parent.find('.drop-file-area').removeClass('hide')

      var $file = $parent.find('.drop-file-area-input')
      if ($file.length) {
        $file[0].type = 'text'
        $file[0].type = 'file'
      }
      if (this.entity.imageId) {
        if ($type === 'self') {
          this.entity.imageId.selfImageId = null
        }
        if ($type === 'front') {
          this.entity.imageId.frontImageId = null
        }
        if ($type === 'back') {
          this.entity.imageId.backImageId = null
        }
        if ($type === 'bill') {
          this.entity.imageId.billImageId = null
        }
      }
    },
    resetFile ($file) {
      $file.type = 'text'
      $file.type = 'file'
    },
    displayPicture (img, file) {
      var $img = $(img)
      $img.data('file', file)

      var cache = new Image()
      cache.onload = function () {
        $img.data('width', cache.width)
        $img.data('height', cache.height)
      }

      var fileReader = new FileReader()
      fileReader.onload = function (e) {
        img.src = e.target.result
        cache.src = e.target.result
      }
      fileReader.readAsDataURL(file)
    },
    rotatePicture ($a, degrees) {
      var $parent = $($a).parents('.file-section')
      var img = $parent.find('img.preview')[0]
      var $img = $(img)
      var width = $img.data('width')
      var height = $img.data('height')

      var canvas = document.createElement('canvas')
      canvas.width = height
      canvas.height = width

      var context = canvas.getContext('2d')
      context.translate(canvas.width / 2, canvas.height / 2)
      context.rotate(degrees * Math.PI / 180)
      context.drawImage(img, -width / 2, -height / 2)

      canvas.toBlob(function (blob) {
        this.displayPicture(img, blob)
      }.bind(this), 'image/jpeg', 0.93)
    },
    uploadFile ($a) {
      var $parent = $($a).parents('.file-section')
      var img = $parent.find('img.preview')[0]
      var $img = $(img)
      var width = $img.data('width')
      var height = $img.data('height')

      var $selected = $parent.find('.selected-file-area')
      $selected.find('.elements a').not('.red').hide()

      var $tip = $selected.find('.tip')
      $tip.text('Processing...')

      var maxWidth = 4288
      var maxHeight = 3216
      var nWidth = width
      var nHeight = height

      // Resize the image if needed
      if (width > maxWidth || height > maxHeight) {
        var ratio = width / height
        var maxRatio = maxWidth / maxHeight
        if (ratio >= maxRatio) {
          nWidth = Math.round(maxWidth * 0.8)
          nHeight = Math.round(nWidth / ratio)
        } else {
          nHeight = Math.round(maxHeight * 0.8)
          nWidth = Math.round(ratio * nHeight)
        }
      } else {
        return this.doUploadFile($a, $img.data('file'))
      }

      var canvas = document.createElement('canvas')
      canvas.width = nWidth
      canvas.height = nHeight

      var context = canvas.getContext('2d')
      context.drawImage(img, 0, 0, width, height, 0, 0, nWidth, nHeight)

      canvas.toBlob(function (blob) {
        this.doUploadFile($a, blob)
      }.bind(this), 'image/jpeg', 0.93)
    },
    async show () {
      this.verifying = false
      this.$q.loading.show()
      const resp = await request(`/admin/cow/members/${this.entity['Member ID']}/detail`)
      this.$q.loading.hide()
      if (resp.success) {
        this.entity = _.assignIn({}, this.entity, resp.data.entity)
        // this.getType()
      }
    },
    async deny () {
      this.$q.loading.show({
        message: 'Deny KYC...'
      })
      await request(`/admin/cow/members/${this.entity['Member ID']}/kyc_deny`, 'post')
      this._hide()
      this.$q.loading.hide()
      this.$root.$emit('reload-cow-kyc-members')
      this.$root.$emit('reload-cow-members')
      this.$root.$emit('reload-cow-members-profile', false)
    },
    async manually () {
      if (this.uploading) {
        notifyResponse('The documents are uploading, please wait.')
        return
      }
      // if (!this.entity.imageId.selfImageId) {
      //   notifyResponse('Please finish uploading your selfie document. And click the upload button in the bottom left of photo to complete upload.')
      //   return
      // }
      if (!this.entity.imageId.frontImageId) {
        notifyResponse('Please finish uploading your front of ID document. And click the upload button in the bottom left of photo to complete upload.')
        return
      }
      if (!this.entity.imageId.billImageId) {
        notifyResponse('Please finish uploading your bill document. And click the upload button in the bottom left of photo to complete upload.')
        return
      }
      if (this.entity.providerType !== 'PSP' && !this.entity.imageId.backImageId) {
        notifyResponse('Please finish uploading your ' + this.entity.providerType + ' document Back image. Click the upload button in the bottom left of photo before Manually Approve.')
        return
      }
      if (!this.entity.providerType || this.entity.providerType === 'PSP') {
        notifyResponse('Please select the documents type.')
        return
      }
      this.$q.loading.show()
      let resp
      if (this.entity['uiv']) {
        resp = await request(`admin/cow/kyc_exceptions/update/${this.entity['uiv']}/image`, 'post', {
          'selfId': this.entity.imageId.selfImageId,
          'frontId': this.entity.imageId.frontImageId,
          'backId': this.entity.imageId.backImageId,
          'billId': this.entity.imageId.billImageId,
          'providerType': this.entity.providerType
        })
      } else {
        resp = await request(`admin/cow/${this.entity['Member ID']}/upload-id-post`, 'post', {
          'selfId': this.entity.imageId.selfImageId,
          'frontId': this.entity.imageId.frontImageId,
          'backId': this.entity.imageId.backImageId,
          'billId': this.entity.imageId.billImageId,
          'providerType': this.entity.providerType,
          'country': this.entity.defaultCountryId
        })
      }
      this.$q.loading.hide()
      if (resp.success) {
        this._hideAndEmit('show-cow-member-id-manual-dialog', this.entity)
      } else {
        notifyResponse('Upload your documents error.')
      }
    },
    async downLoad (id) {
      let data = {
        'fileId': null
      }
      switch (id) {
        case 'self': data.fileId = this.entity.imageId ? this.entity.imageId.selfImageId : null
          break
        case 'front': data.fileId = this.entity.imageId ? this.entity.imageId.frontImageId : null
          break
        case 'back': data.fileId = this.entity.imageId ? this.entity.imageId.backImageId : null
          break
        case 'bill': data.fileId = this.entity.imageId ? this.entity.imageId.billImageId : null
          break
      }
      if (!data.fileId) {
        notifyResponse('There is no image for download.')
        return false
      }
      this.$q.loading.show()
      const resp = await request(`/admin/cow/members/${this.entity['Member ID']}/id-file-download`, 'post', data)
      this.$q.loading.hide()
      if (resp.success && resp.data) {
        location.href = `${resp.data}/download`
      }
    }
  }
}
</script>

<style lang="scss">
.cow-member-kyc-detail-dialog {
  .modal-content {
    max-width: 580px !important;
    .modal-scroll {
      max-height: 100%;
    }
  }
  .selected-file-area .elements a {
    color: #7ac142 !important;
  }
  .select-item {
    max-width: 450px;
    margin: 15px auto;
    justify-content: space-between;
  }
  img {
    max-width: 320px !important;
  }
  .hide {
    display: none !important;
  }
  .option-btn {
    justify-content: center;
    margin-top: 10px;
    margin-bottom: 10px;
    .upload-btn {
      margin-right: 10px;
      line-height: 40px;
      cursor: pointer;
      padding: 0 10px;
      border-radius: 10px;
      background: #f1f1f6;
      color: #696975;
    }
    .download-btn {
      background: #7ac142;
      color: #fafafb;
    }
  }
  .form-field-title {
    font-size: 14px;
    color: #171726;
    width: 100%;
    text-align: center;
  }
  .kyc-box {
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 15px;
    text-align: center;
    color: #333;
    .form-field-title {
      margin: 0 auto;
      padding-top: 0 !important;
    }
    .cow-member-id-status {
      max-width: 320px;
      margin: 10px auto;
    }
  }
  .manually-btn {
    background-color: #9c85ca;
    color: #fff;
  }
  .deny-btn {
    background-color: #fc5a5a;
    color: #fff;
  }
  .q-chip.approved {
    color: #00b8ff;
    background-color: rgba($color: #00b8ff, $alpha: 0.1);
  }

  .q-chip.negative {
    background-color: rgba($color: #fc5a5a, $alpha: 0.1);
    color: #ff4852;
  }
}
</style>
