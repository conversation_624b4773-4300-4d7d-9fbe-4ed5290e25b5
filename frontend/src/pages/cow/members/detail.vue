<template>
  <q-dialog class="cow-member-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">{{ edit ? 'Edit Member' : 'Create New Member' }}</div>
      <div class="font-12 normal text-dark">Please fill in the information below about the member.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12 member-status">
          <div class="text-center form-field-title">Member Status</div>
          <q-select v-if="entity['Status'] !== 'KYC Failed' && entity['Status'] !== 'Closed' "
                    v-model="status"
                    :class="status"
                    :options="statusOptions" />
          <span class="status-info"
                v-if="entity['Status'] === 'KYC Failed'">{{entity['Status']}}</span>
          <span class="status-close"
                v-else-if="entity['Status'] === 'Closed'">{{entity['Status']}}</span>
        </div>
        <div class="col-sm-12 form-field-title">Personal Information</div>
        <div class="col-sm-6">
          <q-input placeholder="First Name"
                   autocomplete="no"
                   :error="$v.entity['First Name'].$error"
                   @input="$v.entity['First Name'].$touch"
                   v-model="entity['First Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input placeholder="Last Name"
                   autocomplete="no"
                   :error="$v.entity['Last Name'].$error"
                   @input="$v.entity['Last Name'].$touch"
                   v-model="entity['Last Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input placeholder="Email"
                   autocomplete="no"
                   :error="$v.entity['Email'].$error"
                   @input="$v.entity['Email'].$touch"
                   v-model="entity['Email']"></q-input>
        </div>
        <div class="col-sm-6">
          <div class="row">
            <q-select class="phone-code col-4"
                      autocomplete="no"
                      filter
                      autofocus-filter
                      v-model="entity['phoneCode']"
                      :options="countryCodeList" />
            <q-input class="phone-number col-8"
                     autocomplete="no"
                     placeholder="Phone Only digits. No country dial code."
                     :error="$v.entity['phone'].$error"
                     @input="$v.entity['phone'].$touch"
                     v-model="entity['phone']"></q-input>
          </div>
        </div>
        <div class="col-sm-12">
          <q-datetime placeholder="Date of Birth"
                      autocomplete="no"
                      type="date"
                      format="MM/DD/YYYY"
                      :error="$v.entity['Date of Birth'].$error"
                      @change="$v.entity['Date of Birth'].$touch"
                      v-model="entity['Date of Birth']"></q-datetime>
        </div>
        <div class="form-field-title">Residential Address</div>
        <div class="col-sm-12">
          <q-input placeholder="Street Address"
                   autocomplete="no"
                   :error="$v.entity['Street Address'].$error"
                   @change="$v.entity['Street Address'].$touch"
                   v-model="entity['Street Address']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input placeholder="City"
                   autocomplete="no"
                   :error="$v.entity['City'].$error"
                   @change="$v.entity['City'].$touch"
                   v-model="entity['City']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input autocomplete="no"
                   placeholder="Postal Code"
                   :error="$v.entity['Postal Code'].$error"
                   @change="$v.entity['Postal Code'].$touch"
                   v-model="entity['Postal Code']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-select autocomplete="no"
                    placeholder="Country"
                    :options="countries"
                    filter
                    autofocus-filter
                    :error="$v.entity['CountryId'].$error"
                    @change="$v.entity['CountryId'].$touch"
                    v-model="entity['CountryId']"></q-select>
        </div>
        <div class="col-sm-6">
          <q-select autocomplete="no"
                    placeholder="State"
                    :options="states"
                    filter
                    autofocus-filter
                    :before="stateBefore"
                    :error="$v.entity['StateId'].$error"
                    @change="$v.entity['StateId'].$touch"
                    v-model="entity['StateId']"></q-select>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <div class="kyc-box"
             v-if="edit && entity.kycStep">
          <div class="form-field-title">KYC Status</div>
          <q-chip class="font-12 mv-5"
                  :class="kycStatusClass">{{ entity.kycStep }}</q-chip>
          <Statuses :statuses="entity.kycStatuses"></Statuses>
          <div class="mv-10 text-negative"
               v-if="kycFailType">
            <strong>{{ kycFailType }}</strong>: {{ kycFailMsg }}
          </div>
          <div v-if="entity.kycApprover"
               class="font-12 text-faded">
            <span>Manually approved by </span>
            <span notranslate="">{{ entity.kycApprover }} @ </span>
            <span>{{ entity.kycApprovedAt }}</span>
          </div>

          <q-btn label="Start KYC Verification"
                 color="blue"
                 no-caps
                 @click="startKyc('ofac')"
                 v-if="entity.kycStep === 'KYC Not Started' && entity.kycDeny !== true" />
          <div class="row"
               v-else-if="['KYC Failed (OFAC)', 'KYC Failed (OFAC & Scan)'].includes(entity.kycStep) && entity.kycDeny !== true">
            <q-btn label="Recheck OFAC"
                   color="orange"
                   no-caps
                   @click="startKyc('ofac')"></q-btn>
            <q-btn label="Manually Approve"
                   color="blue"
                   no-caps
                   class="main"
                   v-if="entity.kycStatuses && entity.kycStatuses['ID Scan'] !== null"
                   @click="manualKyc"></q-btn>
          </div>
          <q-btn label="Start ID Scan"
                 color="blue"
                 no-caps
                 @click="startIdScan"
                 class="ph-50"
                 v-else-if="entity.kycStep === 'KYC (Scan Pending)' && entity.kycDeny !== true" />
          <div class="row"
               v-else-if="entity.kycStep === 'KYC Failed (Scan)' && entity.kycDeny !== true">
            <q-btn label="Rescan ID"
                   color="orange"
                   no-caps
                   @click="startIdScan"></q-btn>
            <q-btn label="Manually Approve"
                   color="blue"
                   no-caps
                   class="main"
                   @click="manualKyc"></q-btn>
          </div>
        </div>
        <div class="row">
          <q-btn :label="edit ? 'Cancel' : 'Back'"
                 no-caps
                 color="grey-3"
                 text-color="tertiary"
                 @click="cancel" />
          <q-btn :label="edit ? 'Save Changes' : 'Next Step'"
                 no-caps
                 color="positive"
                 class="main"
                 @click="save" />
        </div>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request } from '../../../common'
import { required, email, helpers } from 'vuelidate/lib/validators'
import _ from 'lodash'
import MexStateListMixin from '../../../mixins/mex/MexStateListMixin'
import Statuses from './id_status'
const countryList = require('country-data').countries
const alpha = helpers.regex('alpha', /^[0-9+-\s]*$/)

const validations = {
  entity: {}
}
for (const field of [
  'First Name', 'Last Name', 'Email', 'phone',
  'Date of Birth', 'CountryId', 'Street Address', 'City', 'Postal Code', 'StateId'
]) {
  validations.entity[field] = { required }
}

validations.entity['Email'] = { required, email }
validations.entity['phone'] = { required, alpha }
export default {
  name: 'cow-member-detail-dialog',
  mixins: [
    Singleton,
    MexStateListMixin
  ],
  components: {
    Statuses
  },
  data () {
    return {
      defaultEntity: {
        'Member ID': 0,
        'phoneCode': 'CW',
        'phone': ''
      },
      verifying: false,
      statusOptions: [
        {
          'label': 'Active',
          'value': 'active'
        },
        {
          'label': 'Inactive',
          'value': 'inactive'
        }
        // {
        //   'label': 'Closed',
        //   'value': 'closed'
        // }
      ],
      status,
      countryCodeList: []
    }
  },
  mounted () {
    countryList.all.map(x => {
      if (x.countryCallingCodes.length) {
        this.countryCodeList.push({
          label: x.emoji ? x.emoji + ' ' + x.countryCallingCodes[0] : x.name + ' ' + x.countryCallingCodes,
          value: x.alpha2,
          code: x.countryCallingCodes[0]
        })
      }
    })
    return this.countryCodeList
  },
  computed: {
    edit () {
      return this.entity['Member ID']
    },
    kycStatusClass () {
      const step = this.entity.kycStep
      if (['Manually Approved'].includes(step)) {
        return 'approved'
      }
      if (['KYC Passed'].includes(step)) {
        return 'positive'
      }
      return 'negative'
    },
    kycFailType () {
      let type = null
      _.forEach(this.entity.kycStatuses || {}, (v, k) => {
        if (v && v !== true) {
          type = k
          return false
        }
      })
      return type
    },
    kycFailMsg () {
      let msg = null
      _.forEach(this.entity.kycStatuses || {}, v => {
        if (v && v !== true) {
          msg = v
          return false
        }
      })
      return msg
    }
  },
  validations,
  methods: {
    async show () {
      this.verifying = false
      this.$q.loading.show()
      if (this.entity['Phone']) {
        const phoneList = this.entity['Phone'].split(' ')
        // console.log(phoneList)
        let code = 'CW'
        if (phoneList.length > 1) {
          this.entity['phone'] = this.entity['Phone'].split(phoneList[0])[1]
          // console.log(this.entity['phone'])
          this.countryCodeList.forEach(element => {
            // console.log(element)
            if (element.code === phoneList[0]) {
              code = element.value
            }
          })
        } else {
          this.entity['phone'] = phoneList[0]
        }
        this.entity['phoneCode'] = code
      }
      const resp = await request(`/admin/cow/members/${this.entity['Member ID']}/detail`)
      this.$q.loading.hide()
      if (resp.success) {
        this.entity = _.assignIn({}, this.entity, resp.data.entity)
        this.status = this.StatusClass(this.entity.Status)
        const startKyc = this.entity.startKyc
        delete this.entity.startKyc
        if (startKyc) {
          this.startKyc()
        }
      }
    },
    StatusClass (status) {
      if (status === 'Active') {
        return 'active'
      }
      if (status === 'Inactive') {
        return 'inactive'
      }
      if (status === 'Closed') {
        return 'closed'
      }
      return 'active'
    },
    onSuccess (resp) {
      if (this.verifying) {
        return
      }

      notify(resp.message)

      this.$root.$emit('reload-cow-members')
      this.$root.$emit('reload-cow-members-profile')
      this.$root.$emit('reload-cow-members-profile-notes')

      if (!this.edit) {
        this.visible = false

        setTimeout(() => {
          resp.data.startKyc = true
          this._show(resp.data)
        }, 200)
      } else {
        this._hide()
      }
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      this.entity['Phone'] = this.entity['phone']
      this.entity.Status = this.status === 'hold' ? 'inactive' : this.status
      const resp = await request(`/admin/cow/members/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    },
    cancel () {
      this._hide()
    },
    hide () {
      this.$root.$emit('reload-cow-members')
      this.$root.$emit('reload-cow-members-profile', false)
      this.$root.$emit('reload-cow-kyc-members')
    },
    async startKyc (type, param) {
      this.verifying = true
      if (this.$v.$anyDirty) {
        await this.save()
        if (this.$v.$invalid) {
          this.verifying = false
          return
        }
      }
      if (!type) {
        if (this.entity.kycStatuses['OFAC'] !== true) {
          type = 'ofac'
        } else {
          return
        }
      }
      const oldStep = this.entity.kycStep
      this.$q.loading.show({
        message: 'Verifying ' + type.toUpperCase()
      })
      const resp = await request(`/admin/cow/members/${this.entity['Member ID']}/id-${type}`, 'post', {
        param
      })
      this.$q.loading.hide()

      this.entity = _.assignIn({}, this.entity, resp.data)
      delete this.entity.startKyc
      this.verifying = false

      if (oldStep !== this.entity.kycStep) {
        this.$root.$emit('reload-cow-members')
        this.$root.$emit('reload-cow-members-profile')
      }
    },
    async startIdScan () {
      if (this.$v.$anyDirty) {
        await this.save()
      }
      this._hideAndEmit('show-cow-member-kyc-detail-dialog', this.entity)
    },
    async manualKyc () {
      if (this.$v.$anyDirty) {
        await this.save()
      }
      this._hideAndEmit('show-cow-member-kyc-detail-dialog', this.entity)
    }
  }
}
</script>

<style lang="scss">
.cow-member-detail-dialog {
  .modal-content {
    width: 580px;
  }
  .form-field-title {
    font-size: 14px;
    color: #171726;
    width: 100%;
    text-align: center;
  }
  .kyc-box {
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 15px;
    text-align: center;
    color: #333;

    .form-field-title {
      margin: 0 auto;
      padding-top: 0 !important;
    }

    .cow-member-id-status {
      max-width: 320px;
      margin: 10px auto;
    }
  }
  .q-chip {
    padding: 0 26px;
  }
  .q-chip.approved {
    color: #00b8ff;
    background-color: rgba($color: #00b8ff, $alpha: 0.1);
  }

  .q-chip.positive {
    color: #00d993;
    background-color: rgba($color: #00d993, $alpha: 0.1);
  }
  .q-chip.negative {
    background-color: rgba($color: #fc5a5a, $alpha: 0.1);
    color: #ff4852;
  }
  .member-status {
    .form-field-title {
      padding-top: 0 !important;
    }
    .status-info {
      max-width: 104px;
      margin: 0 auto;
      display: block;
      padding: 0 !important;
      margin-top: 10px;
      border-radius: 5px;
      background-color: rgba($color: #ffc542, $alpha: 0.1);
      color: #ffc542;
    }
    .status-close {
      max-width: 104px;
      margin: 0 auto;
      display: block;
      padding: 0 !important;
      margin-top: 10px;
      border-radius: 5px;
      background-color: rgba($color: #fc5a5a, $alpha: 0.1);
      color: #fc5a5a;
    }
    .q-select {
      max-width: 104px;
      margin: 0 auto;
      padding: 0 !important;
      margin-top: 10px;
      border-radius: 5px;
      .q-input-target {
        text-align: center;
        font-size: 14px;
        display: inline-block;
      }
      .q-icon {
        position: absolute;
        right: -25px;
      }
    }
    .q-select.active {
      border-color: rgba($color: #00d993, $alpha: 0.1);
      background: rgba($color: #00d993, $alpha: 0.1);
      .q-input-target {
        color: #00d993 !important;
      }
    }
    .q-select.inactive {
      border-color: rgba($color: #ff974a, $alpha: 0.1);
      background: rgba($color: #ff974a, $alpha: 0.1);
      .q-input-target {
        color: #ff974a !important;
      }
    }
    .q-select.closed {
      border-color: rgba($color: #fc5a5a, $alpha: 0.1);
      background: rgba($color: #fc5a5a, $alpha: 0.1);
      .q-input-target {
        color: #fc5a5a !important;
      }
    }
  }
  .phone-code {
    // border-right: none;
    padding-right: 0 !important;
    padding-left: 10px !important;
    min-width: 100px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .phone-number {
    border-left: none;
    max-width: calc(100% - 100px);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
</style>
