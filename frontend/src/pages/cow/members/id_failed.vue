<template>
  <q-dialog class="cow-member-id-failed-dialog"
            v-model="visible">
    <template slot="title">
      <div class="mb-5">
        <q-icon name="mdi-close-circle"
                class="font-35 error"></q-icon>
      </div>
      <div class="font-18 mb-2">Identity Verification Failed</div>
      <div class="font-14 normal text-center">
        The customer has failed KYC verification. What would you like to do?
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="buttons">
      <div class="row btn-list">
        <div class="col-sm-4">
          <q-btn label="Try Again"
                 no-caps
                 class="cancel-btn col-sm-4"
                 @click="startIdScan" />
        </div>
        <div class="col-sm-1">
        </div>
        <div class="col-sm-7">
          <q-btn label="Manually Approve KYC"
                 no-caps
                 class="load-btn"
                 @click="manualKyc" />
        </div>
        <div class="col-sm-12">
          <q-btn label="Decline Account"
                 no-caps
                 class="ml-0 mt-8 error-btn"
                 @click="_hide" />
        </div>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'

export default {
  name: 'cow-member-id-failed-dialog',
  mixins: [
    Singleton
  ],
  computed: {},
  methods: {
    startIdScan () {
      this._hideAndEmit('show-cow-member-id-upload-dialog', this.entity)
    },
    manualKyc () {
      this._hideAndEmit('show-cow-member-id-manual-dialog', this.entity)
    }
  }
}
</script>

<style lang="scss">
.cow-member-id-failed-dialog {
  .modal-content {
    width: 450px;
  }
  .load-btn {
    background-color: #9c85ca;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
  }
  .close-btn {
    background-color: #7ac142;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
  }
  .error {
    color: #fc5a5a;
  }
  .btn-list .q-btn {
    width: 100%;
    font-size: 16px;
  }
  .cancel-btn {
    background-color: #f1f1f6;
    color: #696975;
  }
  .error-btn {
    background-color: #fc5a5a;
    color: #fafafb;
  }
}
</style>
