<template>
  <q-dialog class="cow-member-id-success-dialog"
            v-model="visible">
    <template slot="title">
      <div class="mb-5">
        <q-icon name="mdi-check-circle"
                color="positive"
                class="font-35"></q-icon>
      </div>
      <div class="font-18 mb-2">Member is Activated</div>
      <div class="font-14 normal">
        The member’s Cash on Web account is now activated.
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn label="Load Member"
               no-caps
               class="load-btn"
               @click="loadAccount" />
        <q-btn label="Done"
               no-caps
               class="ml-0 mt-8 close-btn"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'

export default {
  name: 'cow-member-id-success-dialog',
  mixins: [
    Singleton
  ],
  computed: {},
  methods: {
    loadAccount () {
      this._hide()
      this.$root.$emit('show-cow-load-member-dialog', this.entity)
    },
    hide () {
      this.$root.$emit('reload-cow-members')
      this.$root.$emit('reload-cow-members-profile', false)
      this.$root.$emit('reload-cow-kyc-members')
    }
  }
}
</script>

<style lang="scss">
.cow-member-id-success-dialog {
  .modal-content {
    width: 450px;
  }
  .load-btn {
    background-color: #9c85ca;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
  }
  .close-btn {
    background-color: #7ac142;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
  }
}
</style>
