<template>
  <q-page id="cow__kyc_pool__members__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Partner"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total Members in KYC Pool</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }} Report</strong>
        </template>
        <template slot="top-right">
          <q-btn v-if="$store.state.User.teams.indexOf('CashOnWeb Agent') === -1 && $store.state.User.teams.indexOf('CashOnWeb Compliance') === -1"
                 icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip v-if="props.row['kycDeny']"
                      class="font-13 negative">
                Verification Denied
              </q-chip>
              <q-chip v-else-if="props.row['pending'] && props.row['Status']['OFAC']"
                      class="font-13 pending">
                Verification Pending
              </q-chip>
              <q-chip v-else-if="props.row['Status']['ID Scan'] !== true || props.row['Status']['OFAC'] !== true"
                      @mouseenter.native="showReason(props.row)"
                      @mouseleave.native="hideReason(props.row)"
                      class="font-13 orange">
                KYC Failed
                <q-popover v-if="props.row['Status']['ID Scan'] === null"
                           :ref="`subNav_${props.row['Member ID']}`"
                           class="kyc-reason">
                  Unable to read image
                </q-popover>
                <q-popover v-else-if="props.row['Status']['ID Scan'] !== true"
                           :ref="`subNav_${props.row['Member ID']}`"
                           class="kyc-reason">
                  {{props.row['Status']['ID Scan']}}
                </q-popover>
                <q-popover v-if="props.row['Status']['OFAC'] === null"
                           :ref="`subNav_${props.row['Member ID']}`"
                           class="kyc-reason">
                  OFAC Failed
                </q-popover>
              </q-chip>
              <q-chip v-else
                      class="font-13 positive">
                Manually Approved
              </q-chip>
            </template>
            <template v-else-if="col.field === 'KYC Documentation'">
              <span v-if="props.row['Documentation']"
                    @click="viewKyc(props.row)"
                    class="view-doc">View Documents</span>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="view(props.row)">
                    <q-item-main>View</q-item-main>
                  </q-item>
                  <q-item v-if="$store.state.User.teams.indexOf('CashOnWeb Agent') === -1"
                          v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Status']['ID Scan'] !== true && props.row['kycDeny'] !== true && $store.state.User.teams.indexOf('CashOnWeb Agent') === -1"
                          v-close-overlay
                          @click.native="approveKyc(props.row)">
                    <q-item-main>Approve KYC</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <DetailDialog></DetailDialog>
    <IdUploadDialog></IdUploadDialog>
    <IdInstructionDialog></IdInstructionDialog>
    <IdSuccessDialog></IdSuccessDialog>
    <IdManualDialog></IdManualDialog>
    <IdFailedDialog></IdFailedDialog>
    <KycDetailDialog></KycDetailDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import CowPageMixin from '../../../mixins/cow/CowPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import DetailDialog from './detail'
import IdUploadDialog from './id_upload'
import IdInstructionDialog from './id_instruction'
import IdSuccessDialog from './id_success'
import IdManualDialog from './id_manual'
import IdFailedDialog from './id_failed'
import KycDetailDialog from './KycDetail'
import _ from 'lodash'

export default {
  name: 'cow-kyc-members',
  mixins: [
    CowPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-cow-kyc-members')
  ],
  components: {
    DetailDialog,
    IdUploadDialog,
    IdInstructionDialog,
    IdSuccessDialog,
    IdManualDialog,
    IdFailedDialog,
    KycDetailDialog
  },
  data () {
    return {
      hovers: {},
      title: 'KYC Exception Pool',
      requestUrl: `/admin/cow/members/kyc/list`,
      downloadUrl: `/admin/cow/members/kyc/export`,
      keyword: '',
      dateRange: 'all',
      columns: generateColumns([
        'Member ID', 'First Name', 'Last Name',
        'Email', 'Phone', 'Country', 'City', 'KYC Provider', 'KYC Type',
        'KYC Documentation', 'Status', 'Actions'
      ], [], {
        'Member ID': 'u.id',
        'First Name': 'u.firtsName',
        'Last Name': 'u.lastName',
        'Email': 'u.email',
        'Phone': 'u.mobilephone',
        'Country': 'u.country',
        'City': 'u.city'
      }),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        }, {
          value: 'filter[u.mobilephone=]',
          label: 'Mobile Phone'
        }, {
          value: 'status',
          label: 'Status',
          options: [
            { label: 'Manually Approved', value: 'manually' },
            { label: 'KYC Failed', value: 'failed' },
            { label: 'Verification Denied', value: 'denied' },
            { label: 'Verification Pending', value: 'pending' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  computed: {
    visibleColumns () {
      return _.filter(this.columns, c => {
        // if (this.$store.state.User.teams.indexOf('CashOnWeb Agent') !== -1 && c.label === 'KYC Documentation') {
        //   c.hidden = true
        // }
        return !c.hidden
      })
    }
  },
  methods: {
    view (row) {
      this.$router.push(`/j/cow/members/${row['Member ID']}`)
    },
    edit (row) {
      console.log(row)
      this.$root.$emit('show-cow-member-detail-dialog', row)
    },
    approveKyc (row) {
      this.$root.$emit('show-cow-member-kyc-detail-dialog', row)
    },
    viewKyc (row) {
      this.$root.$emit('show-cow-member-kyc-detail-dialog', row)
    },
    showReason (row) {
      let ref = this.$refs[`subNav_${row['Member ID']}`]
      if (ref && ref[0]) {
        ref[0].hide().catch(() => {})
      }
      this.$set(this.hovers, row['Member ID'], true)
      if (ref && ref[0]) {
        ref[0].show().catch(() => {})
      }
    },
    hideReason (row) {
      const ref = this.$refs[`subNav_${row['Member ID']}`]
      this.$set(this.hovers, row['Member ID'], false)
      if (ref && ref[0]) {
        ref[0].hide().catch(() => {})
      }
    }
  }
}
</script>
<style lang="scss">
#cow__kyc_pool__members__index_page {
  .q-table {
    .q-chip.positive {
      background-color: rgba($color: #00d993, $alpha: 0.1);
      color: #00d993 !important;
    }
    .q-chip.negative {
      background-color: rgba($color: #fc5a5a, $alpha: 0.1);
      color: #fc5a5a !important;
    }
    .q-chip.orange {
      background-color: rgba($color: #ff974a, $alpha: 0.1);
      color: #ff974a !important;
    }
    .q-chip.pending {
      background-color: rgba($color: #0062ff, $alpha: 0.1);
      color: #0062ff !important;
    }
    .view-doc {
      color: #9c85ca;
      cursor: pointer;
    }
  }
}
.kyc-reason {
  padding: 5px 7px !important;
  color: #ffffff;
  font-size: 12px;
  background: #444450 !important;
  max-width: 200px !important;
}
</style>
