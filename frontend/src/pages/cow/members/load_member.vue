<template>
  <q-dialog class="cow-load-member-dialog"
            v-model="visible">
    <template slot="title">
      <template v-if="step == 'amount'">
        <div class="mb-5">
          <div class="avatar">
            <img v-if="entity['avatar']"
                 :src="entity['avatar']">
            <img v-else
                 src="/static/img/avatar2.png">
          </div>
        </div>
        <div class="font-18 mb-2">Load Member</div>
        <div class="font-14 normal">
          Please fill in the information below to load this member’s account.
        </div>
      </template>
      <template v-if="step == 'fee'">
        <div class="confirm-amount">
          <div class="mb-5">
            <img src="/static/jake/icons/cow/member_load.svg">
          </div>
          <div class="font-18 mb-2">Confirm Load</div>
          <p class="header-title">{{form.load.loadAmount.formatted}} {{form.load.loadAmount.currency}}</p>
          <p class="sub-title">({{form.load.payAmount.formatted}} {{form.load.payAmount.currency}})</p>
        </div>
      </template>
      <template v-if="step == 'confirm'">
        <div class="mb-5">
          <q-icon name="mdi-check-circle"
                  color="positive"
                  class="font-35"></q-icon>
        </div>
        <div class="font-18 mb-2">Load Successfull</div>
        <div class="confirm-amount">
          <p class="header-title">{{form.load.loadAmount.formatted}} {{form.load.loadAmount.currency}}</p>
          <p class="sub-title">({{form.load.payAmount.formatted}} {{form.load.payAmount.currency}})</p>
        </div>
      </template>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div v-if="step == 'amount'"
           class="col-sm-12 col-md-4 load-info">
        <q-list bordered
                separator>
          <q-item v-ripple>
            <q-item-side>
              User Balance
            </q-item-side>
            <q-item-main>
              {{ balance | moneyFormat}}
            </q-item-main>
          </q-item>
          <q-item v-ripple>
            <q-item-side>
              User Full Name
            </q-item-side>
            <q-item-main>
              {{$c.fullName(entity)}}
            </q-item-main>
          </q-item>
          <q-item v-ripple>
            <q-item-side>
              User ID
            </q-item-side>
            <q-item-main>
              {{entity['Member ID']}}
            </q-item-main>
          </q-item>
        </q-list>
        <div class="font-18 mb-2 mt-20 amount-header">Load Amount</div>
        <div class="load-amount">
          <div class="input-group">
            <span>$</span>
            <input type="number"
                   step="100"
                   v-model="inputyLoadAmount"
                   @change="updateSliderAndModel"
                   @blur="updateSliderAndModel"
                   :max="amountMax"
                   :min="amountMin" />
          </div>
          <q-slider :value="form.loadAmount"
                    :min="amountMin"
                    :max="amountMax"
                    @change="updateInputAndModel"
                    color="green" />
          <!-- <q-checkbox v-model="form.charge"
                      label="Charge 5% Load fee" /> -->
        </div>
        <div v-if="($store.state.User.teams.indexOf('CashOnWeb Compliance') !== -1 || $store.state.User.teams.indexOf('CashOnWeb Agent') !== -1) && front"
             class="front-image">
          <p class="header-title">Front of ID</p>
          <img v-if="front"
               :src="front">
          <q-checkbox v-model="checkFront"
                      label="I have verified that the customers likeness matches the presented Government Issued ID photo."></q-checkbox>
        </div>
      </div>
      <div class="col-sm-12 col-md-4 load-info"
           v-if="step == 'confirm' || step == 'fee' ">
        <q-list bordered
                separator>
          <q-item v-ripple>
            <q-item-side>
              Load Amount
            </q-item-side>
            <q-item-main>
              {{ form.load.payAmount.formatted }}
            </q-item-main>
          </q-item>
          <q-item v-if="form.load.loadFee"
                  v-ripple>
            <q-item-side>
              Load Fee
            </q-item-side>
            <q-item-main>
              {{ form.load.loadFee | moneyFormat }}
            </q-item-main>
          </q-item>
          <q-item v-if="form.load.membershipFee"
                  v-ripple>
            <q-item-side>
              Member Fee
            </q-item-side>
            <q-item-main>
              {{ form.load.membershipFee | moneyFormat }}
            </q-item-main>
          </q-item>
          <q-item v-if="form.load.membershipFee"
                  v-ripple>
            <q-item-side>
              Member Account Loaded
            </q-item-side>
            <q-item-main>
              {{ form.load.loadAmount.formatted }}
            </q-item-main>
          </q-item>
          <q-item v-ripple>
            <q-item-side>
              After Load Balance
            </q-item-side>
            <q-item-main>
              {{ form.load.balance | moneyFormat }}
            </q-item-main>
          </q-item>
          <q-item v-ripple>
            <q-item-side>
              User Full name
            </q-item-side>
            <q-item-main>
              {{$c.fullName(entity)}}
            </q-item-main>
          </q-item>
          <q-item v-ripple>
            <q-item-side>
              Account ID
            </q-item-side>
            <q-item-main>
              {{entity['Member ID']}}
            </q-item-main>
          </q-item>
          <q-item v-if="step == 'confirm'"
                  v-ripple>
            <q-item-side>
              Transaction ID
            </q-item-side>
            <q-item-main>
              {{form.load.transactionNo}}
            </q-item-main>
          </q-item>
          <q-item v-if="$store.state.User.locator"
                  v-ripple>
            <q-item-side>
              Locator
            </q-item-side>
            <q-item-main>
              {{$store.state.User.locator}}
            </q-item-main>
          </q-item>
          <q-item v-ripple>
            <q-item-side>
              Load By
            </q-item-side>
            <q-item-main>
              {{$store.state.User.id}}
            </q-item-main>
          </q-item>
          <q-item v-if="step == 'confirm'"
                  v-ripple>
            <q-item-side>
              Status
            </q-item-side>
            <q-item-main>
              <span :class="statusClass('complate')">
                Loaded
              </span>
            </q-item-main>
          </q-item>
        </q-list>
      </div>
    </template>
    <template slot="buttons">
      <div v-if="step == 'amount'"
           class="stacks">
        <div class="row">
          <q-btn label="Back"
                 no-caps
                 class="load-btn"
                 color="grey-3"
                 text-color="tertiary"
                 @click="cancel" />
          <q-btn label="Next Step"
                 no-caps
                 color="positive"
                 class="main load-btn"
                 @click="nextConfirm" />
        </div>
      </div>
      <div v-if="step == 'fee'"
           class="stacks single">
        <q-btn label="Continue"
               color="positive"
               class="load-btn"
               @click="next" />
        <q-btn label="Back"
               no-caps
               color="grey-3"
               class="load-btn"
               text-color="tertiary"
               @click="back" />
      </div>
      <div v-if="step == 'confirm'"
           class="stacks single">

        <q-btn label="Print"
               color="primary"
               class="load-btn"
               @click="print" />
        <q-btn label="Done"
               color="positive"
               class="load-btn"
               @click="cancel" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { EventHandlerMixin, request, notifyResponse, notifySuccess } from '../../../common'

export default {
  name: 'cow-load-member-dialog',
  mixins: [
    Singleton,
    EventHandlerMixin('cow-load-member-dialog', 'show')
  ],
  data () {
    return {
      amountMin: 50,
      amountMax: 1000,
      maxBalance: 1000,
      balance: 0,
      inputyLoadAmount: 100,
      form: {
        loadAmount: 100,
        id: null,
        charge: true
      },
      step: 'amount',
      front: '',
      checkFront: false
    }
  },
  created () { },
  components: {},
  computed: {},
  methods: {
    print () {
      window.print()
    },
    updateSliderAndModel () {
      if (parseInt(this.inputyLoadAmount) > this.amountMax) {
        this.inputyLoadAmount = this.amountMax
      }
      if (parseInt(this.inputyLoadAmount) < this.amountMin) {
        this.inputyLoadAmount = this.amountMin
      }
      this.form.loadAmount = parseInt(this.inputyLoadAmount)
      this.inputyLoadAmount = this.form.loadAmount
    },
    updateInputAndModel (val) {
      this.form.loadAmount = val
      this.inputyLoadAmount = val
    },
    async show () {
      this.$q.loading.show()
      this.step = 'amount'
      this.form.id = this.entity['Member ID']
      const resp = await request(`/admin/cow/members/load_init`, 'post', this.form, true)
      if (resp.success) {
        this.form.loadAmount = resp.data.init
        this.inputyLoadAmount = resp.data.init
        this.amountMin = resp.data.min
        this.amountMax = resp.data.max
        this.balance = resp.data.balance
        this.maxBalance = resp.data.maxBalance
        this.front = resp.data.front
        this.selectFlag = false
        this.$q.loading.hide()
      } else {
        this.$q.loading.hide()
        notifyResponse(resp, () => {
          this._hide()
        })
      }
    },
    async nextConfirm (row) {
      // console.log(this.entity)
      if (this.entity['kycStatuses']['ID Scan'] !== true && (this.form.loadAmount > 500 || (this.form.loadAmount + this.balance / 100) > 500)) {
        this.$q.dialog({
          title: 'Error',
          message: 'This user is eligible to load $500.00 without ID verification. Please have the user login and KYC verify to have higher limits. ',
          color: 'negative',
          ok: 'Close',
          cancel: 'Try Again'
        }).then(() => {
          this._hide()
        }).catch(() => {})
        return
      }
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to load $' + this.form.loadAmount + ' for ' + this.entity['First Name'] + ' ' + this.entity['Last Name'] + ' ?',
        color: 'pative',
        cancel: true
      }).then(async () => {
        this.next()
      }).catch(() => {})
    },
    async next () {
      if (this.step === 'amount') {
        if ((this.$store.state.User.teams.indexOf('CashOnWeb Compliance') !== -1 || this.$store.state.User.teams.indexOf('CashOnWeb Agent') !== -1) && !this.checkFront) {
          notifySuccess('Please check the Front image with consumner', 'Message', 'negative')
          return
        }
        if ((this.form.loadAmount + this.balance / 100) > this.maxBalance) {
          notifySuccess('Account Balance Limit Met.' + ' At this time you can load up to $' + (this.maxBalance * 100 - this.balance) / 100 + ' . Please have the member spend down their balance to add more funds.', 'Message', 'negative')
          return
        }
        this.$q.loading.show()
        const resp = await request(`/admin/cow/members/load_fee`, 'post', this.form)
        if (resp.success) {
          if (resp.data.balance <= 0) {
            notifySuccess('The load amount is not enough to deduct the membership fee and load fee. Please load $' + (resp.data.membershipFee + resp.data.loadFee) / 100 + ' at least', 'Message', 'negative')
          } else {
            this.step = 'fee'
            this.form.load = resp.data
          }
        }
        this.$q.loading.hide()
      } else if (this.step === 'fee') {
        this.$q.loading.show()
        const resp = await request(`/admin/cow/members/load_confirm`, 'post', this.form)
        if (resp.success) {
          this.step = 'confirm'
          this.form.load = resp.data
        }
        this.$q.loading.hide()
      }
    },
    back () {
      if (this.step === 'fee') {
        this.step = 'amount'
      }
    },
    cancel () {
      this._hide()
    },
    hide () {
      this.$root.$emit('reload-cow-members')
      this.$root.$emit('reload-cow-members-profile', false)
      this.$root.$emit('reload-cow-kyc-members')
    },
    statusClass (status) {
      if (status === 'complate') {
        return 'load-success'
      }
      return 'load-inprogress'
    }
  }
}
</script>

<style lang="scss">
.cow-load-member-dialog {
  .modal-content {
    width: 450px;
  }
  .load-btn {
    font-size: 16px;
    .q-btn-inner {
      line-height: 28px;
    }
  }
  .close-btn {
    background-color: #7ac142;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
  }
  .load-info {
    .front-image {
      max-width: 325px;
      margin: 0 auto;
      margin-top: 30px;
      .header-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 10px;
      }
      .q-option-label {
        font-size: 12px;
        font-weight: normal;
        color: #171725;
        text-align: left;
      }
      img {
        max-width: 80%;
      }
    }
    .q-list {
      border-top: none;
      border-right: none;
      border-left: none;
      // border-color: #e2e2ea;
      font-size: 15px;
      max-width: 325px;
      margin: 0 auto;
    }
    .q-item {
      padding: 16px 0;
    }
    .q-item-side {
      color: #92929e;
    }
    .q-item-division {
      border-color: #e2e2ea;
    }
    .q-item-main {
      text-align: right;
      color: #171725;
      font-size: 16px;
    }
    .load-amount {
      max-width: 350px;
      margin: 0 auto;
      .input-group {
        color: #231f20;
        font-weight: 600;
        font-size: 22px;
        display: flex;
        justify-content: center;
        width: 190px;
        margin: 10px auto;
        border-radius: 10px;
        border: solid 1px #e2e2ea;
        padding-left: 20px;
        span {
          line-height: 53px;
        }
        input {
          display: inline-block;
          padding: 10px 5px;
          border-radius: 10px;
          min-width: 150px;
          text-align: center;
          border: none;
          outline: none;
          &:active,
          &:hover,
          &:focus {
            border: none;
          }
        }
      }
    }
  }
  .amount-header {
    font-weight: 600;
  }
  .confirm-amount {
    .header-title {
      font-size: 20px;
      font-weight: 600;
      color: #7ac142;
      margin: 0;
    }
    .sub-title {
      font-size: 14px;
      text-align: center;
      color: #92929e;
      margin: 0;
    }
  }
  .load-success {
    color: #00d993;
    background: rgba($color: #00d993, $alpha: 0.1);
    border-radius: 5px;
    padding: 5px;
  }
  .load-inprogress {
    color: #fc5a5a;
    background: rgba($color: #fc5a5a, $alpha: 0.1);
    border-radius: 5px;
    padding: 5px;
  }
}
</style>
