<template>
  <q-card id="cow__members__profile_basic"
          class="high-card">
    <q-card-main>
      <div v-if="$store.state.User.teams.indexOf('MasterAdmin') !== -1 || $store.state.User.teams.indexOf('CashOnWeb Platform Partner') !== -1 || $store.state.User.teams.indexOf('CashOnWeb Admin') !== -1 || $store.state.User.teams.indexOf('CashOnWeb Compliance') !== -1">
        <div @click="updateLock(true)"
             class="radius-box bg-positive"
             v-if="entity['locked'] === 'Unlock'">
          <q-icon name="mdi-lock-open-outline"></q-icon>
        </div>
        <div @click="updateLock(false)"
             class="radius-box bg-negative"
             v-else>
          <q-icon name="mdi-lock-outline"></q-icon>
        </div>
      </div>
      <div class="text-center mb-20">
        <div class="avatar mt-5">
          <img :src="'/static/img/avatar.png'"
               alt="">
        </div>
        <div class="font-20 bold mt-5 full-name">{{ $c.fullName(entity) }}<i v-if="!entity['watch'] && $store.state.User.teams.indexOf('CashOnWeb Agent') === -1"
             @click="updateWatch()"
             class="mdi mdi-eye-outline"></i></div>
        <div class="text-faded">{{ entity['Phone'] }}</div>

        <div class="text-faded mt-15">Balance</div>
        <div class="font-16 heavy text-black mt--3">{{ entity['Balance'] | moneyFormat }}</div>

        <q-chip class="font-13 mt-5"
                :class="statusClass(entity['Status'])">
          {{ entity['Status'] }}
        </q-chip>
        <div class="action-btn">
          <q-btn class="load-btn"
                 v-if="$store.state.User.teams.indexOf('CashOnWeb Compliance') === -1 && entity['Status'] === 'Active'"
                 :disabled="entity['locked'] !== 'Unlock'"
                 @click="loadMember()">
            <i class="mdi mdi-arrow-up"></i>Load
          </q-btn>
          <q-btn v-if="$store.state.User.teams.indexOf('CashOnWeb Compliance') === -1 && $store.state.User.teams.indexOf('CashOnWeb Agent') === -1 && entity['Status'] === 'Active'"
                 class="unload-btn"
                 @click="refundMember()">
            <i class="mdi mdi-arrow-down"></i>Unload
          </q-btn>
        </div>
      </div>
      <table class="fields-table">
        <tr v-for="(r, i) in fields"
            :key="i">
          <th>{{ r }}</th>
          <td>{{ entity[r] }}</td>
        </tr>
      </table>
    </q-card-main>

    <LoadMember></LoadMember>
    <RefundMember></RefundMember>
  </q-card>
</template>

<script>
import { request } from '../../../../common'
import LoadMember from '../load_member'
import RefundMember from '../refund_member'

export default {
  name: 'cow-members-profile-basic',
  components: {
    LoadMember,
    RefundMember
  },
  props: {
    entity: {
      type: Object
    }
  },
  data () {
    return {
      fields: [
        'Email', 'Phone', 'Card Program'
      ]
    }
  },
  methods: {
    statusClass (status) {
      const cls = [{
        'On Hold': 'warning',
        'Active': 'positive',
        'Closed': 'negative'
      }[status] || status]

      if (status && status.startsWith('KYC')) {
        cls.push('orange')
      }
      return cls
    },
    updateLock (type) {
      let message = 'Are you sure that you want to unlock this user?'
      if (type) {
        message = 'Are you sure that you want to lock this user?'
      }
      this.$q.dialog({
        title: 'Confirm',
        message: message,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/cow/members/${this.entity['Member ID']}/lock`, 'post', { 'lock': type })
        if (resp.success) {
          this.$root.$emit('reload-cow-members-profile')
        }
        // this.$q.loading.hide()
      }).catch(() => {})
    },
    updateWatch (type) {
      let message = 'Are you sure that you want to watch this user?'
      this.$q.dialog({
        title: 'Confirm',
        message: message,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/cow/members/${this.entity['Member ID']}/watch`, 'post', { 'watch': true })
        if (resp.success) {
          this.$root.$emit('reload-cow-members-profile')
        }
        // this.$q.loading.hide()
      }).catch(() => {})
    },
    loadMember () {
      // console.log('asdfsadf')
      // this.uploadLock(false)
      this.$root.$emit('show-cow-load-member-dialog', this.entity)
    },
    refundMember () {
      console.log('asdfsadf')
      this.$root.$emit('show-cow-refund-member-dialog', this.entity)
    }
  }
}
</script>

<style lang="scss">
#cow__members__profile_basic {
  .avatar {
    width: 130px;
    height: 130px;
    background-color: #eee;
    border-radius: 20px;
    margin: 0 auto;
    overflow: hidden;
  }

  .radius-box {
    border: none;
    color: white;
    position: absolute;
    right: 16px;
    top: 16px;
    font-size: 18px;
    padding: 4px 5px;
    line-height: 1em;
    cursor: pointer;

    &.bg-positive {
      background-color: #3dd598 !important;
    }
  }

  .fields-table {
    th {
      min-width: 60px !important;
    }
  }
  .q-chip.warning {
    background-color: rgba($color: #ffc300, $alpha: 0.1);
    color: #ffc300 !important;
  }
  .q-chip.positive {
    background-color: rgba($color: #00d993, $alpha: 0.1);
    color: #00d993 !important;
  }
  .q-chip.negative {
    background-color: rgba($color: #fc5a5a, $alpha: 0.1);
    color: #fc5a5a !important;
  }
  .q-chip.orange {
    background-color: rgba($color: #ff974a, $alpha: 0.1);
    color: #ff974a !important;
  }
  .full-name {
    display: flex;
    justify-content: center;
    line-height: 20px;
    i {
      color: #fc5a5a;
      cursor: pointer;
      font-size: 20px;
      margin-left: 5px;
    }
  }
  .action-btn {
    display: flex;
    justify-content: center;
    .load-btn,
    .unload-btn {
      color: #fafafb;
      font-size: 14px;
      height: 39px;
      // line-height: 39px;
      text-transform: none;
      margin-top: 10px;
      display: flex;
      align-items: center;
      i {
        font-size: 20px;
      }
    }
    .load-btn {
      background: #01da33;
    }
    .unload-btn {
      background: #df2f35;
      margin-left: 10px;
    }
  }
}
</style>
