<template>
  <q-card id="mex__members__profile_detail"
          class="high-card">
    <q-card-title>
      <div class="row flex-center">
        <div class="font-18 bold">Account Details</div>
        <q-btn icon="mdi-refresh"
               outline
               @click="$emit('reload')"
               class="btn-mini ml-auto"></q-btn>
        <q-btn v-if="$store.state.User.teams.indexOf('CashOnWeb Agent') === -1"
               icon="mdi-pencil-outline"
               outline
               @click="edit"
               class="btn-mini ml-5"></q-btn>
      </div>
    </q-card-title>
    <q-card-main>
      <table class="fields-table mt-10 mb-auto">
        <tr v-for="(r, i) in fields"
            :key="i">
          <th>{{ r }}</th>
          <td>{{ entity[r] }}</td>
        </tr>
      </table>

      <div class="row flex-center mv-auto">
        <q-btn icon="mdi-file-document-outline"
               no-caps
               class="btn-sm profile-btn"
               v-if="['Initial'].includes(entity['Status']) || (entity['Status'] && entity['Status'].startsWith('KYC Failed')) && $store.state.User.teams.indexOf('CashOnWeb Agent') === -1"
               @click="manual"
               label="Manually Verify KYC"></q-btn>
        <q-btn icon="mdi-file-download-outline"
               no-caps
               class="btn-sm profile-btn"
               v-else-if="entity['KYC Passed'] && $store.state.User.teams.indexOf('CashOnWeb Agent') === -1"
               @click="manual"
               label="KYC Documents"></q-btn>
      </div>
      <div class="row flex-center mv-auto">
        <q-btn icon="mdi-file-download-outline"
               no-caps
               class="btn-sm profile-btn"
               v-if="entity['isManualApprover'] && entity['idManualApprovalSignature']"
               @click="manualDetail"
               label="Manual KYC Details"></q-btn>
      </div>
    </q-card-main>

    <HtmlListDialog></HtmlListDialog>
    <DetailDialog></DetailDialog>

    <IdUploadDialog></IdUploadDialog>
    <IdInstructionDialog></IdInstructionDialog>
    <IdSuccessDialog></IdSuccessDialog>
    <IdManualDialog></IdManualDialog>
  </q-card>
</template>

<script>
import HtmlListDialog from '../../../../components/HtmlListDialog'
import DetailDialog from '../detail'
import IdUploadDialog from '../id_upload'
import IdInstructionDialog from '../id_instruction'
import IdSuccessDialog from '../id_success'
import IdManualDialog from '../id_manual'
import { request } from '../../../../common'
import _ from 'lodash'

export default {
  name: 'cow-members-profile-detail',
  components: {
    HtmlListDialog,
    DetailDialog,
    IdUploadDialog,
    IdInstructionDialog,
    IdSuccessDialog,
    IdManualDialog
  },
  props: {
    entity: {
      type: Object
    }
  },
  data () {
    return {
      fields: [
        'First Name', 'Last Name', 'Date of Birth', 'Mailing Address',
        'City', 'Country', 'State / Province', 'Postal Code'
      ]
    }
  },
  methods: {
    edit () {
      this.$root.$emit('show-cow-member-detail-dialog', this.entity)
    },
    manual () {
      this.$root.$emit('show-cow-member-kyc-detail-dialog', this.entity)
    },
    manualDetail () {
      this.$root.$emit('show-cow-member-manual-detail-dialog', this.entity)
    },
    async download () {
      this.$q.loading.show()
      const resp = await request(`/admin/cow/members/${this.entity['Member ID']}/id-download`)
      this.$q.loading.hide()
      if (resp.success && resp.data) {
        if (resp.data.length > 1) {
          const links = []
          _.forEach(resp.data, (v, k) => {
            links.push(`<a href="${v}/download">File ${k + 1}</a>`)
          })
          this.$root.$emit('show-html-list-dialog', {
            title: 'Download KYC Documents',
            html: `<p>Click on the below links to download the files:</p><ul><li>${links.join('</li><li>')}</li></ul>`
          })
        } else if (resp.data.length) {
          location.href = `${resp.data[0]}/download`
        }
      }
    }
  }
}
</script>

<style lang="scss">
#mex__members__profile_detail {
  .q-card-main {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }
}
</style>
