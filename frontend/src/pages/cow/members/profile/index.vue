<template>
  <q-page id="cow__members__profile_page">
    <div class="row gutter-sm mt-0">
      <div class="col-sm-12 col-md-4">
        <BasicCard :entity="entity"></BasicCard>
      </div>
      <div class="col-sm-12 col-md-4">
        <DetailCard :entity="entity"
                    @reload="reload"></DetailCard>
      </div>
      <div class="col-sm-12 col-md-4">
        <NotesCard :uid="uid"></NotesCard>
      </div>
      <div v-if="$store.state.User.teams.indexOf('CashOnWeb Agent') === -1 && $store.state.User.teams.indexOf('CashOnWeb Compliance') === -1"
           class="col-12">
        <TransactionsCard :uid="uid"></TransactionsCard>
      </div>
      <div v-if="$store.state.User.teams.indexOf('CashOnWeb Agent') === -1"
           class="col-12 mb-30">
        <LoadHistory :uid="uid"></LoadHistory>
      </div>
    </div>

    <KycDetailDialog></KycDetailDialog>
    <ManualDetail></ManualDetail>
  </q-page>
</template>

<script>
import CowPageMixin from '../../../../mixins/cow/CowPageMixin'
import BasicCard from './basic'
import DetailCard from './detail'
import NotesCard from './notes'
import TransactionsCard from './transactions'
import LoadHistory from './load'
import { EventHandlerMixin, request } from '../../../../common'
import KycDetailDialog from '../KycDetail'
import ManualDetail from './manualDetail'

export default {
  name: 'cow-members-profile',
  mixins: [
    CowPageMixin,
    EventHandlerMixin('reload-cow-members-profile')
  ],
  components: {
    BasicCard,
    DetailCard,
    NotesCard,
    TransactionsCard,
    LoadHistory,
    KycDetailDialog,
    ManualDetail
  },
  data () {
    return {
      title: 'Member Profile',
      entity: {}
    }
  },
  computed: {
    uid () {
      return this.$route.params.id
    }
  },
  watch: {
    uid () {
      this.reload()
    }
  },
  methods: {
    async reload (indicator = true) {
      indicator && this.$q.loading.show()
      const resp = await request(`/admin/cow/members/${this.uid}/profile`)
      indicator && this.$q.loading.hide()
      if (resp.success) {
        this.entity = resp.data
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
#cow__members__profile_page {
  .q-card {
    &.high-card {
      height: 473px;
    }

    &.wide-card {
      min-height: 300px;

      > .q-card-main {
        position: relative;
        height: 100%;
      }
    }

    overflow: auto;

    > .q-card-primary {
      padding-top: 16px;
    }

    > .q-card-main {
      position: relative;
      height: calc(100% - 67px);
    }
  }

  .q-card-title .font-18.bold {
    height: 33px;
    padding-top: 3px;
  }

  .fields-table {
    width: 100%;

    th {
      padding: 5px 5px 5px 0;
      font-weight: normal;
      font-size: 13px;
      color: #888;
      text-align: left;
      min-width: 100px;
      vertical-align: top;
    }

    td {
      padding: 5px 0 5px 5px;
      text-align: right;
      word-break: break-word;
    }
  }

  .empty-tip {
    height: 100%;
    color: #666;

    .q-icon {
      margin-bottom: 5px;
      font-size: 20px;
    }
  }
  .profile-btn {
    background: #9c85ca;
    color: #ffffff;
  }
}
</style>
