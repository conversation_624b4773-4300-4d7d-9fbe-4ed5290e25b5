<template>
  <q-card class="wide-card">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :loading="loading"
               class="inner-table m--15"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">Member Load History</div>
        </template>
        <template slot="top-right">
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12 status-item"
                      :class="props.row['Status'] === 'loaded' ? 'positive' : 'negative'">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Account ID'">
              <span class="account-id">{{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else-if="col.field === 'Transaction'">
              <span class="load-icon"
                    v-if="props.row['Transaction'] =='Account Load'"><i class="mdi mdi-arrow-up"></i></span>
              <span class="load-icon unload-item"
                    v-if="props.row['Transaction'] =='Account Unload'"><i class="mdi mdi-arrow-down"></i></span>
              {{ _.get(props.row, col.field) }}
            </template>
            <template v-else-if="col.field === 'Amount'">
              <span class="amount-item">{{ _.get(props.row, col.field) | moneyFormat }}</span>
            </template>
            <template v-else-if="col.field === 'Loaded By'">
              <span @click="goToAgent(props.row)"
                    class="account-id">{{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>
      </q-table>
      <span v-if="data.length && $store.state.User.teams.indexOf('CashOnWeb Agent') === -1"
            @click="$c.loginAs(uid)"
            class="auto-login">login as user to view all loads</span>
    </q-card-main>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-card>
</template>

<script>
import CowPageMixin from '../../../../mixins/cow/CowPageMixin'
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns } from '../../../../common'

export default {
  name: 'cow-members-profile-load',
  mixins: [
    CowPageMixin,
    ListPageMixin
  ],
  props: {
    uid: {
      type: String
    }
  },
  data () {
    return {
      title: 'Member Profile',
      requestUrl: `/admin/cow/members/load/list`,
      columns: generateColumns([
        'Transaction', 'Date & Time', 'Transaction ID',
        'Email', 'Account ID', 'Merchant Name', 'Amount', 'Loaded By', 'Status'
      ]),
      keyword: '',
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 5,
        page: 1
      },
      filterOptions: [
        {
          value: 'filter[ucl.id=]',
          label: 'Transaction ID'
        },
        {
          value: 'filter[ucl.type=]',
          label: 'Type',
          options: [
            { label: 'Load', value: 'load_card' },
            { label: 'Refund', value: 'unload' }
          ]
        },
        {
          value: 'date_range',
          label: 'Date Time',
          range: [{
            value: 'range[ucl.createdAt][start]',
            type: 'date'
          }, {
            value: 'range[ucl.createdAt][end]',
            type: 'date'
          }]
        }
      ],
      autoLoad: true
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        member: this.uid
      }
    },
    goToAgent (row) {
      const { href } = this.$router.resolve({
        path: `/j/cow/agents`
      })
      localStorage.setItem('agentId', row['Loaded By'])
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss">
.account-id {
  color: #7ac142;
  text-decoration-color: #7ac142;
  text-decoration-line: underline;
}
.amount-item {
  color: #01da33;
}
.auto-login {
  display: block;
  margin-top: 30px;
  cursor: pointer;
  text-transform: uppercase;
  color: #7ac142;
  text-align: center;
  &:hover {
    color: #42880c;
  }
}
.load-icon {
  color: #00de00;
  font-size: 20px;
  background: rgba($color: #00de00, $alpha: 0.2);
  padding: 6px 20px;
  border-radius: 20px;
  margin-right: 10px;
  position: relative;
  i {
    position: absolute;
    top: 9px;
    left: 10px;
  }
}
.unload-item {
  color: #dc3545;
  background: rgba($color: #dc3545, $alpha: 0.2);
}
.status-item {
  text-transform: capitalize;
}
</style>
