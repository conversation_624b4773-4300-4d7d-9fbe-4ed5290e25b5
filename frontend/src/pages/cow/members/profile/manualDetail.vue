<template>
  <q-dialog class="cow-member-manual-detail-dialog"
            v-model="visible">
    <template slot="title">
      <div class="font-18 mb-2">Manual KYC Details</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <img v-if="entity.idManualApprovalSignature && entity"
           :src="entity.idManualApprovalSignature">
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'

export default {
  name: 'cow-member-manual-detail-dialog',
  mixins: [
    <PERSON>ton
  ],
  computed: {},
  methods: {
  }
}
</script>

<style lang="scss">
.cow-member-manual-detail-dialog {
  .modal-content {
    width: 450px;
  }
  .load-btn {
    background-color: #9c85ca;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
  }
  .close-btn {
    background-color: #7ac142;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
  }
}
</style>
