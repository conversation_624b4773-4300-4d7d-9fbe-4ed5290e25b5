<template>
  <q-card class="wide-card">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :loading="loading"
               class="inner-table m--15"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">Transaction History</div>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field === 'Amount'">
              <span v-if="props.row['Amount'] >= 0"
                    class="amount-item">{{ _.get(props.row, col.field) | moneyFormat}}</span>
              <span v-else
                    class="reduce-amount-item">{{ _.get(props.row, col.field) | moneyFormat}}</span>

            </template>
            <template v-else-if="col.field === 'Transaction'">
              <span class="load-icon"
                    :class="props.row['iconClass']"
                    :style="{background: props.row['iconBgColor']}">
                <img v-if="props.row['iconStr']"
                     :src="props.row['icon']">
                <i v-else-if="props.row['category']"
                   class="mc-icon"
                   :class="`mc-icon-${props.row['icon']}`"></i>
                <i v-else
                   :class="props.row['icon']"></i>
              </span>
              {{ _.get(props.row, col.field) }}

            </template>
            <template v-else-if="col.field === 'Account ID'">
              <span class="account-id">{{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>
      </q-table>
      <span v-if="data.length && $store.state.User.teams.indexOf('CashOnWeb Compliance') === -1"
            @click="$c.loginAs(uid)"
            class="auto-login">login as user to view all loads</span>
    </q-card-main>
  </q-card>
</template>

<script>
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns } from '../../../../common'

export default {
  name: 'cow-members-profile-transactions',
  mixins: [
    MexPageMixin,
    ListPageMixin
  ],
  props: {
    uid: {
      type: String
    }
  },
  data () {
    return {
      title: 'Member Profile',
      requestUrl: `/admin/cow/members/transactions/list`,
      columns: generateColumns([
        'Transaction', 'Date & Time', 'Transaction ID',
        'Email', 'Account ID', 'Merchant Name', 'Amount', 'Running Balance'
      ]),
      keyword: '',
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 5,
        page: 1
      },
      autoLoad: true
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        member: this.uid
      }
    }
  }
}
</script>
<style lang="scss">
/* Privacy global css */
.mc-icon-computer,
.mc-icon-entertainment,
.mc-icon-finance,
.mc-icon-food,
.mc-icon-health,
.mc-icon-house,
.mc-icon-other,
.mc-icon-transportation {
  max-width: 100%;
  background-size: 100%;
  background-image: url("/static/usu/img/category/all.png");
  width: 70%;
  height: 70%;
  top: 6px !important;
  left: 6px !important;
}
.mc-icon-computer {
  background-position: 0 0;
  background-size: 100%;
}
.mc-icon-entertainment {
  background-position: 0 14.285714%;
  background-size: 100%;
}
.mc-icon-finance {
  background-position: 0 28.571429%;
  background-size: 100%;
}
.mc-icon-food {
  background-position: 0 42.857143%;
  background-size: 100%;
}
.mc-icon-health {
  background-position: 0 57.142857%;
  background-size: 100%;
}
.mc-icon-house {
  background-position: 0 71.428571%;
  background-size: 100%;
}
.mc-icon-other {
  background-position: 0 85.714286%;
  background-size: 100%;
}
.mc-icon-transportation {
  background-position: 0 100%;
  background-size: 100%;
}

.auto-login {
  display: block;
  margin-top: 30px;
  cursor: pointer;
  text-transform: uppercase;
  color: #7ac142;
  text-align: center;
  &:hover {
    color: #42880c;
  }
}
.reduce-amount-item {
  color: #fc5a5a;
}
.load-icon {
  img {
    width: 40px;
    position: absolute;
    top: 0;
    right: 0;
  }
}
</style>
