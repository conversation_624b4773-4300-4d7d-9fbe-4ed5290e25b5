<template>
  <q-dialog class="cow-refund-member-dialog"
            v-model="visible">
    <template slot="title">
      <template v-if="step == 'amount'">
        <div class="mb-5">
          <div class="avatar">
            <img v-if="entity['avatar']"
                 :src="entity['avatar']">
            <img v-else
                 src="/static/img/avatar2.png">
          </div>
        </div>
        <div class="font-18 mb-2">Refund Member</div>
        <div class="font-14 normal">
          Please fill in the information below to refund this member.
        </div>
      </template>
      <template v-if="step == 'confirm'">
        <div class="confirm-amount">
          <div class="mb-5">
            <img src="/static/jake/icons/cow/member_refund.svg">
          </div>
          <div class="font-18 mb-2">Confirm Refund</div>
          <p class="header-title">{{form.unload.payAmount.formatted}} {{form.unload.payAmount.currency}}</p>
          <p class="sub-title">({{form.unload.unLoadAmount.formatted}} {{form.unload.unLoadAmount.currency}})</p>
        </div>
      </template>
      <template v-if="step == 'status'">
        <div class="mb-5">
          <q-icon name="mdi-check-circle"
                  color="positive"
                  class="font-35"></q-icon>
        </div>
        <div class="font-18 mb-2">Refund Successfully</div>
        <div class="confirm-amount">
          <p class="header-title">{{form.unload.payAmount.formatted}} {{form.unload.payAmount.currency}}</p>
          <p class="sub-title">({{form.unload.unLoadAmount.formatted}} {{form.unload.unLoadAmount.currency}})</p>
        </div>
      </template>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div v-if="step == 'amount'"
           class="col-sm-12 col-md-4 refund-info">
        <q-list bordered
                separator>
          <q-item v-ripple>
            <q-item-side>
              User Balance
            </q-item-side>
            <q-item-main>
              {{ balance | moneyFormat }}
            </q-item-main>
          </q-item>
          <q-item v-ripple>
            <q-item-side>
              User Full Name
            </q-item-side>
            <q-item-main>
              {{$c.fullName(entity)}}
            </q-item-main>
          </q-item>
          <q-item v-ripple>
            <q-item-side>
              User ID
            </q-item-side>
            <q-item-main>
              {{entity['Member ID']}}
            </q-item-main>
          </q-item>
        </q-list>
        <div class="font-18 mb-2 mt-20 amount-header">Refund Amount</div>
        <div class="refund-amount">
          <div class="input-group">
            <span>$</span>
            <input type="number"
                   step="100"
                   v-model="inputyLoadAmount"
                   @change="updateSliderAndModel"
                   @blur="updateSliderAndModel"
                   :max="amountMax"
                   :min="amountMin" />
          </div>
          <q-slider :value="form.unloadAmount"
                    :min="amountMin"
                    :max="amountMax"
                    @change="updateInputAndModel"
                    color="green" />
        </div>
        <q-checkbox v-model="form.waive"
                    label="Waive Refund Fee" />
      </div>
      <div class="col-sm-12 col-md-4 refund-info"
           v-if="step == 'confirm' || step == 'status' ">
        <q-list bordered
                separator>
          <q-item v-ripple>
            <q-item-side>
              Refund Amount
            </q-item-side>
            <q-item-main>
              {{ form.unload.unLoadAmount.formatted }}
            </q-item-main>
          </q-item>
          <q-item v-ripple>
            <q-item-side>
              Unload Fees
            </q-item-side>
            <q-item-main>
              {{form.unload.unloadFee | moneyFormat}}
            </q-item-main>
          </q-item>
          <q-item v-ripple>
            <q-item-side>
              Refund Total
            </q-item-side>
            <q-item-main>
              {{form.unload.payAmount.formatted}}
            </q-item-main>
          </q-item>
          <q-item v-ripple>
            <q-item-side>
              User Full name
            </q-item-side>
            <q-item-main>
              {{$c.fullName(entity)}}
            </q-item-main>
          </q-item>
          <q-item v-ripple>
            <q-item-side>
              Account ID
            </q-item-side>
            <q-item-main>
              {{entity['Member ID']}}
            </q-item-main>
          </q-item>
          <q-item v-if="step == 'status'"
                  v-ripple>
            <q-item-side>
              Transaction ID
            </q-item-side>
            <q-item-main>

            </q-item-main>
          </q-item>
          <q-item v-if="step == 'status'"
                  v-ripple>
            <q-item-side>
              Status
            </q-item-side>
            <q-item-main>
              <span class="refund-status">
                Completed
              </span>
            </q-item-main>
          </q-item>
        </q-list>
      </div>
    </template>
    <template slot="buttons">
      <div v-if="step == 'amount'"
           class="stacks">
        <div class="row">
          <q-btn label="Back"
                 no-caps
                 class="refund-btn"
                 color="grey-3"
                 text-color="tertiary"
                 @click="cancel" />
          <q-btn label="Next Step"
                 no-caps
                 color="positive"
                 class="main refund-btn"
                 @click="next" />
        </div>
      </div>
      <div v-if="step == 'confirm'"
           class="stacks single">
        <q-btn label="Continue"
               color="positive"
               class="refund-btn"
               @click="next" />
        <q-btn label="Back"
               no-caps
               color="grey-3"
               class="refund-btn"
               text-color="tertiary"
               @click="back" />
      </div>
      <div v-if="step == 'status'"
           class="stacks single">
        <q-btn label="Done"
               color="positive"
               class="refund-btn"
               @click="cancel" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { EventHandlerMixin, request, notifyResponse } from '../../../common'

export default {
  name: 'cow-refund-member-dialog',
  mixins: [
    Singleton,
    EventHandlerMixin('cow-refund-member-dialog', 'show')
  ],
  data () {
    return {
      amountMin: 50,
      amountMax: 1000,
      balance: 0,
      inputyLoadAmount: 100,
      form: {
        unloadAmount: 100,
        id: null,
        waive: false
      },
      step: 'amount'
    }
  },
  created () { },
  components: {},
  computed: {},
  methods: {
    updateSliderAndModel () {
      if (parseInt(this.inputyLoadAmount) > this.amountMax) {
        this.inputyLoadAmount = this.amountMax
      }
      if (parseInt(this.inputyLoadAmount) < this.amountMin) {
        this.inputyLoadAmount = this.amountMin
      }
      this.form.unloadAmount = parseInt(this.inputyLoadAmount)
    },
    updateInputAndModel (val) {
      this.form.unloadAmount = val
      this.inputyLoadAmount = val
    },
    async show () {
      this.$q.loading.show()
      this.step = 'amount'
      this.form.waive = false
      this.form.id = this.entity['Member ID']
      const resp = await request(`/admin/cow/members/unload_init`, 'post', this.form, true)
      if (resp.success) {
        this.form.unloadAmount = resp.data.init
        this.inputyLoadAmount = resp.data.init
        this.amountMin = resp.data.min
        this.amountMax = resp.data.max
        this.balance = resp.data.balance
        this.$q.loading.hide()
      } else {
        this.$q.loading.hide()
        notifyResponse(resp, () => {
          this._hide()
        })
      }
    },
    async next () {
      if (this.step === 'amount') {
        this.$q.loading.show()
        const resp = await request(`/admin/cow/members/unload_fee`, 'post', this.form)
        if (resp.success) {
          this.step = 'confirm'
          this.form.unload = resp.data
        }
        this.$q.loading.hide()
      } else if (this.step === 'confirm') {
        this.$q.loading.show()
        const resp = await request(`/admin/cow/members/unload_confirm`, 'post', this.form)
        if (resp.success) {
          this.step = 'status'
          this.form.unload = resp.data
        }
        this.$q.loading.hide()
      }
    },
    back () {
      if (this.step === 'confirm') {
        this.step = 'amount'
      }
    },
    cancel () {
      this._hide()
    },
    hide () {
      this.$root.$emit('reload-cow-members')
      this.$root.$emit('reload-cow-members-profile', false)
      this.$root.$emit('reload-cow-kyc-members')
    }
  }
}
</script>

<style lang="scss">
.cow-refund-member-dialog {
  .modal-content {
    width: 450px;
  }
  .refund-btn {
    font-size: 16px;
    .q-btn-inner {
      line-height: 28px;
    }
  }
  .close-btn {
    background-color: #7ac142;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
  }
  .refund-info {
    .q-list {
      border-top: none;
      border-right: none;
      border-left: none;
      // border-color: #e2e2ea;
      font-size: 15px;
      max-width: 325px;
      margin: 0 auto;
    }
    .q-item {
      padding: 20px 0;
    }
    .q-item-side {
      color: #92929e;
    }
    .q-item-division {
      border-color: #e2e2ea;
    }
    .q-item-main {
      text-align: right;
      color: #171725;
      font-size: 16px;
    }
    .refund-amount {
      max-width: 350px;
      margin: 0 auto;
      .input-group {
        color: #231f20;
        font-weight: 600;
        font-size: 22px;
        display: flex;
        justify-content: center;
        width: 150px;
        margin: 20px auto;
        border-radius: 10px;
        border: solid 1px #e2e2ea;
        padding-left: 20px;
        span {
          line-height: 53px;
        }
        input {
          display: inline-block;
          padding: 10px 5px;
          border-radius: 10px;
          text-align: center;
          border: none;
          outline: none;
          &:active,
          &:hover,
          &:focus {
            border: none;
          }
        }
      }
    }
  }
  .amount-header {
    font-weight: 600;
  }
  .confirm-amount {
    .header-title {
      font-size: 20px;
      font-weight: 600;
      color: #7ac142;
      margin: 0;
    }
    .sub-title {
      font-size: 14px;
      text-align: center;
      color: #92929e;
      margin: 0;
    }
  }
  .refund-status {
    color: #00d993;
    background: rgba($color: #00d993, $alpha: 0.1);
    border-radius: 5px;
    padding: 5px;
  }
}
</style>
