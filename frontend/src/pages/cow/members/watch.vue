<template>
  <q-page id="cow__watch__members__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Partner"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div v-if="$store.state.User.teams.indexOf('CashOnWeb Agent') === -1"
           class="row gutter-sm">
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-eye-outline"
                        color="negative"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total Members on Watch List</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-subtitles-outline"
                        color="warning"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalBalance || 0 | moneyFormat }}</div>
                  <div class="description">Total Balances</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-equalizer-outline"
                        class="avg-balance"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.avgBalance || 0 | moneyFormat }}</div>
                  <div class="description">Average Balances</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Member Watch List Report</strong>
        </template>
        <template slot="top-right">
          <q-btn v-if="$store.state.User.teams.indexOf('CashOnWeb Agent') === -1 && $store.state.User.teams.indexOf('CashOnWeb Compliance') === -1"
                 icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <!-- <q-chip v-if="props.row['kycDeny']"
                      class="font-13 negative">
                KYC Verification Denied
              </q-chip>
              <q-chip v-else-if="props.row['pending'] && props.row['kycStatuses']['OFAC']"
                      class="font-13 blue">
                KYC Verification Pending
              </q-chip> -->
              <q-chip class="font-13"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Create Date'">
              <div class="watch-column">
                <i v-if="props.row.watch"
                   class="mdi mdi-eye-outline"></i>{{ _.get(props.row, col.field) }}
              </div>
            </template>
            <template v-else-if="col.field === 'Balance'">
              {{props.row['Balance'] | moneyFormat}}
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="view(props.row)">
                    <q-item-main>View</q-item-main>
                  </q-item>
                  <q-item v-if="$store.state.User.teams.indexOf('CashOnWeb Agent') === -1"
                          v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Status'] === 'Active' && $store.state.User.teams.indexOf('CashOnWeb Agent') === -1"
                          v-close-overlay
                          @click.native="$c.loginAs(props.row['Member ID'])">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                  <q-item v-if="$store.state.User.teams.indexOf('CashOnWeb Agent') === -1"
                          v-close-overlay
                          @click.native="unWatch(props.row)">
                    <q-item-main>Unwatch</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, request } from '../../../common'
import CowPageMixin from '../../../mixins/cow/CowPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'cow-watch-members',
  mixins: [
    CowPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-cow-watch-members')
  ],
  components: {
  },
  data () {
    return {
      title: 'Member Watch List',
      requestUrl: `/admin/cow/members/watch/list`,
      downloadUrl: `/admin/cow/members/watch/export`,
      keyword: '',
      dateRange: 'all',
      columns: generateColumns([
        'Create Date', 'Member ID', 'First Name', 'Last Name',
        'Email', 'Phone', 'Program', 'Address',
        'Balance', 'Last Login Date/Time', 'Status', 'Actions'
      ], [], {
        'Create Date': 'u.createdAt',
        'Member ID': 'u.id',
        'First Name': 'u.firstName',
        'Last Name': 'u.lastName',
        'Email': 'u.email',
        'Phone': 'u.mobilephone',
        'Address': 'u.address',
        'Last Login Date/Time': 'u.lastLogin'
      }),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        }, {
          value: 'filter[u.mobilephone=]',
          label: 'Mobile Phone'
        }, {
          value: 'filter[u.status=]',
          label: 'Status',
          options: [
            { label: 'Active', value: 'active' },
            { label: 'Pending', value: 'pending' },
            { label: 'KYC Failed', value: 'kyc_failed' },
            { label: 'Inactive', value: 'inactive' },
            { label: 'Closed', value: 'closed' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    searchMember (data) {
      this.keyword = data
      this.reload()
    },
    statusClass (status) {
      const cls = []
      cls.push({
        'Pending': 'blue',
        'On Hold': 'warning',
        'Active': 'positive',
        'Closed': 'negative'
      }[status] || status)

      if (status && status.startsWith('KYC ')) {
        cls.push('orange')
      }

      return cls
    },
    view (row) {
      this.$router.push(`/j/cow/members/${row['Member ID']}`)
    },
    edit (row) {
      this.$root.$emit('show-cow-member-detail-dialog', row)
    },
    unWatch (row) {
      let message = 'Are you sure that you want to unwatch this user?'
      this.$q.dialog({
        title: 'Confirm',
        message: message,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/cow/members/${row['Member ID']}/watch`, 'post', { 'watch': false })
        if (resp.success) {
          this.reload()
        }
        this.$q.loading.hide()
      }).catch(() => {})
    }
  }
}
</script>
<style lang="scss">
#cow__watch__members__index_page {
  .q-table {
    .watch-column {
      position: relative;
    }
    .watch {
      i.mdi-eye-outline {
        color: #fc5a5a;
        margin-right: 5px;
        position: absolute;
        line-height: 20px;
        left: -20px;
      }
    }
    .q-chip.warning {
      background-color: rgba($color: #ffc300, $alpha: 0.1);
      color: #ffc300 !important;
    }
    .q-chip.positive {
      background-color: rgba($color: #00d993, $alpha: 0.1);
      color: #00d993 !important;
    }
    .q-chip.negative {
      background-color: rgba($color: #fc5a5a, $alpha: 0.1);
      color: #fc5a5a !important;
    }
    .q-chip.orange {
      background-color: rgba($color: #ff974a, $alpha: 0.1);
      color: #ff974a !important;
    }
  }
  .total-balance {
    color: #f16501;
  }
  .avg-balance {
    color: #f16501;
  }
}
</style>
