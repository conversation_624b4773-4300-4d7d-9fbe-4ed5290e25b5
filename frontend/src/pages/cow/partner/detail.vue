<template>
  <q-dialog class="cow-funding-account-dialog"
            v-model="visible">
    <template v-if="load"
              slot="title">
      <template>
        <div class="mb-5">
          <q-icon name="mdi-check-circle"
                  color="positive"
                  class="font-35"></q-icon>
        </div>
        <div class="font-18 mb-2">Load Successfull</div>
        <div class="confirm-amount">
          <p class="header-title">{{load.totalPay.formatted}} USD</p>
          <p class="sub-title">({{load.totalAmount.formatted}} USD)</p>
        </div>
      </template>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template v-if="load"
              slot="body">
      <div id="funding">
        <div class="col-sm-12 col-md-4 load-info">
          <q-list bordered
                  separator>
            <q-item v-ripple>
              <q-item-side>
                Transfer Method
              </q-item-side>
              <q-item-main>
                <div class="method-item">
                  <img class="method-img"
                       src="/static/cow/img/bank_login.svg">{{ load.method}}
                </div>
              </q-item-main>
            </q-item>
            <q-item v-ripple>
              <q-item-side>
                Load Amount
              </q-item-side>
              <q-item-main>
                {{ load.totalPay.formatted }}
              </q-item-main>
            </q-item>
            <template v-if="load.confirm.instruction">
              <q-item v-for="(item,k) in load.confirm.instruction"
                      :key="k"
                      v-ripple>
                <q-item-side>
                  {{item.key}}
                </q-item-side>
                <q-item-main>
                  {{item.value}}
                </q-item-main>
              </q-item>
            </template>
            <template v-if="load.confirm.result">
              <q-item v-for="(item,k) in load.confirm.result"
                      :key="k"
                      v-ripple>
                <q-item-side>
                  {{k}}
                </q-item-side>
                <q-item-main>
                  {{item}}
                </q-item-main>
              </q-item>
            </template>
            <q-item v-ripple>
              <q-item-side>
                Transaction ID
              </q-item-side>
              <q-item-main>
                {{load.transactionNo}}
              </q-item-main>
            </q-item>
            <q-item v-ripple>
              <q-item-side>
                Status
              </q-item-side>
              <q-item-main>
                <q-chip class="font-12 status-item blue pending-item">
                  {{load.status}}
                </q-chip>
              </q-item-main>
            </q-item>
          </q-list>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn label="Print Load Details"
               color="positive"
               class="print-btn load-btn"
               @click="print" />
        <q-btn label="Done"
               color="positive"
               class="load-btn"
               @click="cancel" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { EventHandlerMixin, request, notifyResponse } from '../../../common'
import html2canvas from 'html2canvas'

export default {
  name: 'cow-funding-detail-dialog',
  mixins: [
    Singleton,
    EventHandlerMixin('cow-funding-detail-dialog', 'show')
  ],
  data () {
    return {
      load: null
    }
  },
  created () { },
  components: {},
  methods: {
    async show () {
      this.$q.loading.show()
      const resp = await request(`/admin/cow/funding/load/${this.entity['loadId']}/detail`, 'get', {}, true)
      if (resp.success) {
        this.load = resp.data
        this.$q.loading.hide()
      } else {
        this.$q.loading.hide()
        notifyResponse(resp, () => {
          this._hide()
        })
      }
    },
    cancel () {
      this._hide()
      this.$root.$emit('reload-cow-funding')
      this.$root.$emit('reload-partner-balance-chart')
    },
    print () {
      let target = null
      const name = 'Load detail'
      target = document.getElementById('funding')
      // target = document.body
      // console.log(target)
      this.$q.loading.show()
      const targetCss = window.getComputedStyle(target)

      const options = {
        scale: 2,
        width: parseInt(targetCss.width, 10),
        height: parseInt(targetCss.height, 10)
        // y: target.offsetTop
      }
      // console.log(options)
      html2canvas(target, options).then(canvas => {
        // canvas is the final rendered <canvas> element
        const imgData = canvas.toDataURL('image/jpeg')
        console.log(imgData)
        this.fileDownload(imgData, name)
        this.$q.loading.hide()
      })
    },
    fileDownload (downloadUrl, filename) {
      let aLink = document.createElement('a')
      aLink.style.display = 'none'
      aLink.href = downloadUrl
      aLink.download = `${filename}`
      // 触发点击-然后移除
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    }
  }
}
</script>

<style lang="scss">
.cow-funding-account-dialog {
  .modal-content {
    width: 450px;
  }
  .single {
    width: 70%;
  }
  .load-btn {
    font-size: 16px;
    margin: 0 auto;
    .q-btn-inner {
      line-height: 28px;
    }
  }
  .close-btn {
    background-color: #7ac142;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
  }
  .print-btn {
    background-color: #9c85ca !important;
  }
  .load-info {
    .q-list {
      border-top: none;
      border-right: none;
      border-left: none;
      // border-color: #e2e2ea;
      font-size: 15px;
      max-width: 325px;
      margin: 0 auto;
    }
    .q-item {
      padding: 10px 0;
    }
    .q-item-side {
      color: #92929e;
    }
    .q-item-division {
      border-color: #e2e2ea;
    }
    .q-item-main {
      text-align: right;
      color: #171725;
      font-size: 16px;
    }
    .load-amount {
      max-width: 350px;
      margin: 0 auto;
      .input-group {
        color: #231f20;
        font-weight: 600;
        font-size: 22px;
        display: flex;
        justify-content: center;
        width: 190px;
        margin: 20px auto;
        border-radius: 10px;
        border: solid 1px #e2e2ea;
        padding-left: 20px;
        span {
          line-height: 53px;
        }
        input {
          display: inline-block;
          padding: 10px 5px;
          border-radius: 10px;
          text-align: center;
          min-width: 150px;
          border: none;
          outline: none;
          &:active,
          &:hover,
          &:focus {
            border: none;
          }
        }
      }
    }
  }
  .amount-header {
    font-weight: 600;
  }
  .confirm-amount {
    .header-icon {
      width: 50px;
      height: 50px;
      margin: 0 auto;
      background: rgba($color: #61be1a, $alpha: 0.2);
      border-radius: 25px;
      display: flex;
      img {
        max-width: 23px;
        margin: 0 auto;
      }
    }
    .header-title {
      font-size: 20px;
      font-weight: 600;
      color: #7ac142;
      margin: 0;
    }
    .sub-title {
      font-size: 14px;
      text-align: center;
      color: #92929e;
      margin: 0;
    }
  }
  .load-status {
    color: #00d993;
    background: rgba($color: #00d993, $alpha: 0.1);
    border-radius: 5px;
    padding: 5px;
  }
  .method-item {
    display: flex;
    align-items: center;
    text-align: right;
    justify-content: flex-end;
    .method-img {
      max-width: 30px;
      margin-right: 5px;
    }
  }
}
</style>
