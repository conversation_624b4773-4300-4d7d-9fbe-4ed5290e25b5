<template>
  <q-dialog class="cow-funding-account-dialog"
            v-model="visible">
    <template slot="title">
      <template v-if="step == 'amount'">
        <div class="font-18 mb-2">Fund Member</div>
        <div class="font-14 normal">
          To load your account please enter the amount you would like to load.
        </div>
      </template>
      <template v-if="step == 'selectMethod'">
        <div class="confirm-amount">
          <div class="font-18 mb-2">Fund Amount</div>
          <div class="font-14 normal">
            Select your desired load method from the options below.
          </div>
        </div>
      </template>
      <template v-if="step == 'confirm'">
        <div class="confirm-amount">
          <div class="mb-5 header-icon">
            <img src="/static/cow/img/bank_login.svg">
          </div>
          <div class="font-18 mb-2">Confirm Funding</div>
          <p class="header-title">${{form.loadAmount}} USD</p>
          <p class="sub-title">(${{form.loadAmount}} USD)</p>
        </div>
      </template>
      <template v-if="step == 'status'">
        <div class="mb-5">
          <q-icon name="mdi-check-circle"
                  color="positive"
                  class="font-35"></q-icon>
        </div>
        <div class="font-18 mb-2">Load Successfull</div>
        <div class="confirm-amount">
          <p class="header-title">${{form.loadAmount}} USD</p>
          <p class="sub-title">(${{form.loadAmount}} USD)</p>
        </div>
      </template>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div id="funding">
        <div v-if="step == 'amount'"
             class="col-sm-12 col-md-4 load-info">
          <div class="load-amount">
            <div class="input-group">
              <span>$</span>
              <input type="number"
                     step="100"
                     v-model="inputyLoadAmount"
                     @change="updateSliderAndModel"
                     @blur="updateSliderAndModel"
                     :max="amountMax"
                     :min="amountMin" />
            </div>
            <q-slider :value="form.loadAmount"
                      :min="amountMin"
                      :max="amountMax"
                      @change="updateInputAndModel"
                      color="green" />
          </div>
        </div>
        <div v-if="step == 'selectMethod'"
             class="col-sm-12 col-md-4 load-info">
          <div v-for="(item,i) in methodList"
               :key="i"
               class="load-method-wrapper">
            <div class="load-method"
                 @click="form.method = item"
                 :class="{active: form.method == item}">
              <img src="/static/cow/img/bank_login.svg">
              <p>{{item.name}}</p>
            </div>
            <q-icon v-if="form.method == item"
                    name="mdi-check"
                    class="load-method-check"></q-icon>
          </div>
        </div>
        <div class="col-sm-12 col-md-4 load-info"
             v-if="step == 'confirm' || step == 'status' ">
          <q-list bordered
                  separator>
            <q-item v-ripple>
              <q-item-side>
                Transfer Method
              </q-item-side>
              <q-item-main>
                <div class="method-item">
                  <img class="method-img"
                       src="/static/cow/img/bank_login.svg">{{ form.method.name}}
                </div>
              </q-item-main>
            </q-item>
            <q-item v-ripple>
              <q-item-side>
                Load Amount
              </q-item-side>
              <q-item-main>
                ${{ form.loadAmount }}
              </q-item-main>
            </q-item>
            <template v-if="confirm.instruction">
              <q-item v-for="(item,k) in confirm.instruction"
                      :key="k"
                      v-ripple>
                <q-item-side>
                  {{item.key}}
                </q-item-side>
                <q-item-main>
                  {{item.value}}
                </q-item-main>
              </q-item>
            </template>
            <template v-if="confirm.result">
              <q-item v-for="(item,k) in confirm.result"
                      :key="k"
                      v-ripple>
                <q-item-side>
                  {{k}}
                </q-item-side>
                <q-item-main>
                  {{item}}
                </q-item-main>
              </q-item>
            </template>
            <q-item v-if="step == 'status'"
                    v-ripple>
              <q-item-side>
                Transaction ID
              </q-item-side>
              <q-item-main>
                {{transactionNo}}
              </q-item-main>
            </q-item>
            <q-item v-if="step == 'status'"
                    v-ripple>
              <q-item-side>
                Status
              </q-item-side>
              <q-item-main>
                <span>
                  {{loadStatus}}
                </span>
              </q-item-main>
            </q-item>
          </q-list>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn v-if="step != 'status'"
               label="Continue"
               color="positive"
               class="load-btn"
               :disable="!canNext"
               @click="next" />
        <q-btn v-if="step == 'status'"
               label="Print Load Details"
               color="positive"
               class="print-btn load-btn"
               @click="print" />
        <q-btn v-if="step == 'status'"
               label="Done"
               color="positive"
               class="load-btn"
               @click="cancel" />
        <q-btn v-if="step == 'amount'"
               label="Cancel"
               no-caps
               class="load-btn back-btn"
               text-color="tertiary"
               @click="cancel" />
        <q-btn v-if="step == 'selectMethod'"
               label="Back"
               no-caps
               class="load-btn back-btn"
               text-color="tertiary"
               @click="back" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { EventHandlerMixin, request, notifyResponse } from '../../../common'
import html2canvas from 'html2canvas'

export default {
  name: 'cow-funding-account-dialog',
  mixins: [
    Singleton,
    EventHandlerMixin('cow-funding-account-dialog', 'show')
  ],
  data () {
    return {
      amountMin: 100,
      amountMax: 3500,
      methodList: [],
      inputyLoadAmount: 100,
      form: {
        loadAmount: 100,
        load: {
        },
        method: null
      },
      step: 'amount',
      confirm: [],
      transactionNo: null,
      status: null
    }
  },
  created () { },
  components: {},
  computed: {
    canNext () {
      if (this.step === 'selectMethod' && !this.form.method) {
        return false
      }
      return true
    }
  },
  methods: {
    updateSliderAndModel () {
      if (parseInt(this.inputyLoadAmount) > this.amountMax) {
        this.inputyLoadAmount = this.amountMax
      }
      if (parseInt(this.inputyLoadAmount) < this.amountMin) {
        this.inputyLoadAmount = this.amountMin
      }
      this.form.loadAmount = parseInt(this.inputyLoadAmount)
    },
    updateInputAndModel (val) {
      this.inputyLoadAmount = val
      this.form.loadAmount = val
    },
    async show () {
      this.$q.loading.show()
      this.step = 'amount'
      const resp = await request(`/admin/cow/load/init`, 'get', {}, true)
      if (resp.success) {
        this.form.loadAmount = resp.data.init
        this.inputyLoadAmount = resp.data.init
        this.amountMin = resp.data.min
        this.amountMax = resp.data.max
        this.form.method = null
        this.$q.loading.hide()
      } else {
        this.$q.loading.hide()
        notifyResponse(resp, () => {
          this._hide()
        })
      }
    },
    async next () {
      if (this.step === 'amount') {
        this.$q.loading.show()
        const resp = await request(`/admin/cow/load/method`, 'post', this.form)
        if (resp.success) {
          this.form.load = resp.data.id
          this.step = 'selectMethod'
          this.form.method = null
          this.methodList = resp.data.methods
        }
        this.$q.loading.hide()
      } else if (this.step === 'selectMethod' && this.form.method) {
        this.$q.loading.show()
        const resp = await request(`/admin/cow/load/confirm`, 'post', this.form)
        if (resp.success) {
          this.step = 'confirm'
          this.confirm = resp.data.confirm
          this.loadTransactionNo = resp.data.transactionNo
          this.loadStatus = resp.data.status
        }
        this.$q.loading.hide()
      } else if (this.step === 'confirm') {
        this.step = 'status'
      }
    },
    back () {
      if (this.step === 'selectMethod') {
        this.step = 'amount'
        this.form.method = null
      }
    },
    cancel () {
      this._hide()
      this.$root.$emit('reload-cow-funding')
      this.$root.$emit('reload-partner-balance-chart')
    },
    print () {
      let target = null
      const name = 'Load detail'
      target = document.getElementById('funding')
      // target = document.body
      // console.log(target)
      this.$q.loading.show()
      const targetCss = window.getComputedStyle(target)

      const options = {
        scale: 2,
        width: parseInt(targetCss.width, 10),
        height: parseInt(targetCss.height, 10)
        // y: target.offsetTop
      }
      // console.log(options)
      html2canvas(target, options).then(canvas => {
        // canvas is the final rendered <canvas> element
        const imgData = canvas.toDataURL('image/jpeg')
        console.log(imgData)
        this.fileDownload(imgData, name)
        this.$q.loading.hide()
      })
    },
    fileDownload (downloadUrl, filename) {
      let aLink = document.createElement('a')
      aLink.style.display = 'none'
      aLink.href = downloadUrl
      aLink.download = `${filename}`
      // 触发点击-然后移除
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    }
  }
}
</script>

<style lang="scss">
.cow-funding-account-dialog {
  .modal-content {
    width: 450px;
  }
  .single {
    width: 70%;
  }
  .load-btn {
    font-size: 16px;
    margin: 0 auto;
    .q-btn-inner {
      line-height: 28px;
    }
  }
  .close-btn {
    background-color: #7ac142;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
  }
  .back-btn {
    background-color: #92929d;
    font-size: 16px;
    line-height: 48px;
    .q-btn-inner {
      color: #fff;
    }
  }
  .print-btn {
    background-color: #9c85ca !important;
  }
  .load-info {
    .q-list {
      border-top: none;
      border-right: none;
      border-left: none;
      // border-color: #e2e2ea;
      font-size: 15px;
      max-width: 325px;
      margin: 0 auto;
    }
    .q-item {
      padding: 10px 0;
    }
    .q-item-side {
      color: #92929e;
    }
    .q-item-division {
      border-color: #e2e2ea;
    }
    .q-item-main {
      text-align: right;
      color: #171725;
      font-size: 16px;
    }
    .load-amount {
      max-width: 350px;
      margin: 0 auto;
      .input-group {
        color: #231f20;
        font-weight: 600;
        font-size: 22px;
        display: flex;
        justify-content: center;
        width: 190px;
        margin: 20px auto;
        border-radius: 10px;
        border: solid 1px #e2e2ea;
        padding-left: 20px;
        span {
          line-height: 53px;
        }
        input {
          display: inline-block;
          padding: 10px 5px;
          border-radius: 10px;
          min-width: 150px;
          text-align: center;
          border: none;
          outline: none;
          &:active,
          &:hover,
          &:focus {
            border: none;
          }
        }
      }
    }
  }
  .load-method {
    border: 1px solid #ddd;
    border-radius: 10px;
    overflow: hidden;
    max-width: 156px;
    // max-height: 92px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-bottom: 15px;
    position: relative;
    background: white;
    padding: 26px 20px;
    > img {
      max-height: 90px;
      margin: auto;
    }
    > p {
      margin-top: 18px;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 0;
    }
    &:hover,
    &.active {
      border-color: #0091d6;
      box-shadow: 0 3px 5px rgba(0, 0, 0, 0.4);
    }

    .currency {
      position: absolute;
      right: 0;
      bottom: 0;
      background: #aaa;
      color: white;
      padding: 0 6px;
      border-top-left-radius: 8px;
      font-size: 12px;
    }
  }

  .load-method-wrapper {
    display: inline-block;
    position: relative;
  }

  .load-method-check {
    position: absolute;
    right: -9px;
    top: -7px;
    color: #fff;
    font-size: 22px;
    background: var(--q-color-positive) !important;
    border-radius: 50%;
  }
  .amount-header {
    font-weight: 600;
  }
  .confirm-amount {
    .header-icon {
      width: 50px;
      height: 50px;
      margin: 0 auto;
      background: rgba($color: #61be1a, $alpha: 0.2);
      border-radius: 25px;
      display: flex;
      img {
        max-width: 23px;
        margin: 0 auto;
      }
    }
    .header-title {
      font-size: 20px;
      font-weight: 600;
      color: #7ac142;
      margin: 0;
    }
    .sub-title {
      font-size: 14px;
      text-align: center;
      color: #92929e;
      margin: 0;
    }
  }
  .load-status {
    color: #00d993;
    background: rgba($color: #00d993, $alpha: 0.1);
    border-radius: 5px;
    padding: 5px;
  }
  .method-item {
    display: flex;
    align-items: center;
    text-align: right;
    justify-content: flex-end;
    .method-img {
      max-width: 30px;
      margin-right: 5px;
    }
  }
}
</style>
