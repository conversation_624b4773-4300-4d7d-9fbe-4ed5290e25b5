<template>
  <q-page id="cow__report__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="funding-item">
        <q-btn label="Fund Account"
               @click="fundAccount"
               class="btn-sm mr-8 funding-btn"
               no-caps>
          <img src="/static/jake/icons/cash_funding_white.svg">
        </q-btn>

      </div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Partner"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row">
        <div class="col-sm-12">
          <PartnerBalance :viewReportFlag="false"
                          v-model="chartDateRange"></PartnerBalance>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Account History</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align"
                :class="bankItem(props.row)"
                @click.native="showPendingDetail(props.row)">
            <template v-if="col.field === 'Amount'">
              <span v-if="props.row['Amount'] >= 0"
                    class="amount-item">{{ _.get(props.row, col.field) | moneyFormat}}</span>
              <span v-else
                    class="reduce-amount-item">{{ _.get(props.row, col.field) | moneyFormat}}</span>
            </template>
            <template v-else-if="col.field === 'Running Balance'">
              <span v-if="props.row['Status'] === 'Loaded'">{{ _.get(props.row, col.field) | moneyFormat}}</span>
              <span v-else
                    class="waite-item"> {{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else-if="col.field === 'Transaction Type'">
              <p class="fee-type-item">
                <img v-if="props.row['Transaction Type'] == 'Monthly Fee'"
                     src="/static/jake/icons/cow/monthly_fee.svg">
                <img v-if="props.row['Transaction Type'] == 'Platform Adjustment'"
                     src="/static/jake/icons/cow/member_refund.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Approval Fee'"
                     src="/static/jake/icons/cow/approval_fee.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Member Fee'"
                     src="/static/jake/icons/cow/member_fee.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Unload Fee'"
                     src="/static/jake/icons/cow/unload_fee.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Bank Transfer'"
                     src="/static/jake/icons/cow/bank_transfer.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Member Load'"
                     src="/static/jake/icons/cow/member_load.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Member Refund'"
                     src="/static/jake/icons/cow/member_refund.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Load Fee'"
                     src="/static/jake/icons/cow/member_refund.svg">
                {{ _.get(props.row, col.field)}}
              </p>
            </template>
            <template v-else-if="col.field === 'Transaction ID'">
              <span v-if="props.row['Transaction Type'] === 'Monthly Fee' || props.row['Transaction Type'] === 'Unload Fee' || props.row['Transaction Type'] === 'Member Fee' || props.row['Transaction Type'] === 'Approval Fee'"
                    @click="goToFee(props.row)"
                    class="account-id">{{ _.get(props.row, col.field) }}</span>
              <span v-else-if="props.row['Transaction Type'] === 'Member Load' || props.row['Transaction Type'] === 'Member Refund'  || props.row['Transaction Type'] === 'Load Fee'"
                    @click="goToLoad(props.row)"
                    class="account-id">{{ _.get(props.row, col.field) }}</span>
              <span v-else-if="props.row['Transaction Type'] === 'Platform Adjustment'">{{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else-if="col.field === 'Status'">
              <q-chip v-if="props.row['Status'] === 'Loaded'"
                      class="font-12 status-item positive">
                {{ props.row['Status'] }}
              </q-chip>
              <q-chip v-else
                      class="font-12 status-item blue pending-item">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <FundingAccount></FundingAccount>
    <FundingDetail></FundingDetail>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import CowPageMixin from '../../../mixins/cow/CowPageMixin'
import FundingAccount from './funding'
import PartnerBalance from '../common/partnerBalance'
import FundingDetail from './detail'

export default {
  name: 'cow-funding',
  mixins: [
    CowPageMixin,
    ListPageMixin,
    EventHandlerMixin('reload-cow-funding')
  ],
  components: {
    FundingAccount,
    PartnerBalance,
    FundingDetail
  },
  data () {
    return {
      title: 'Partner',
      requestUrl: `/admin/cow/funding/list`,
      downloadUrl: `/admin/cow/funding/export`,
      keyword: '',
      dateRange: 'all',
      chartDateRange: 'all',
      columns: generateColumns([
        'Transaction Type', 'Date & time', 'First Name', 'Last Name', 'Member ID', 'Transaction ID', 'Amount', 'Running Balance', 'Status'
      ]),
      filterOptions: [
        {
          value: 'filter[ucb.type=]',
          label: 'Transaction Type',
          options: [
            { label: 'Monthly Fee', value: 'monthly_fee' },
            { label: 'Unload Fee', value: 'unload_fee' },
            { label: 'Member Fee', value: 'member_ship_fee' },
            { label: 'Member Load', value: 'member_load' },
            // { label: 'Member Refund', value: 'member_unload' },
            { label: 'Approval Fee', value: 'transaction' },
            { label: 'Bank Transfer', value: 'load_card' }
          ]
        }
      ],
      autoLoad: true
    }
  },
  methods: {
    async reload () {
      let data = {}
      if (this.$refs && this.$refs.dateRangeFilter) {
        data = this.$refs.dateRangeFilter.params()
      }
      this.chartDateRange = data.period
      await this.request({
        pagination: this.pagination
      })
      this.everLoaded = true
    },
    fundAccount () {
      this.$root.$emit('show-cow-funding-account-dialog')
    },
    showPendingDetail (row) {
      if (row['Transaction Type'] === 'Bank Transfer' && row['Status'] !== 'Loaded') {
        this.$root.$emit('show-cow-funding-detail-dialog', row)
      }
    },
    goToLoad (row) {
      const { href } = this.$router.resolve({
        path: `/j/cow/report/member_loads`
      })
      localStorage.setItem('openLoadId', row['Transaction ID'])
      window.open(href, '_blank')
    },
    goToFee (row) {
      const { href } = this.$router.resolve({
        path: `/j/cow/report/program_fee`
      })
      localStorage.setItem('openFeeId', row['Transaction ID'])
      localStorage.setItem('openFeeType', row['Transaction Type'])
      window.open(href, '_blank')
    },
    bankItem (row) {
      if (row['Transaction Type'] === 'Bank Transfer' && row['Status'] !== 'Loaded') {
        return 'pending-item'
      }
      return ''
    }
  }
}
</script>
<style lang="scss">
.funding-item {
  margin-left: 10px;
}
.funding-btn {
  background: #9c85ca;
  color: #fff;
  position: relative;
  img {
    float: left;
    position: absolute;
    left: 10px;
  }
  .q-btn-inner {
    font-size: 14px !important;
    padding-left: 30px;
  }
}
.reduce-amount-item {
  color: #fc5a5a;
}
.waite-item {
  color: #f3522b;
}
.fee-type-item {
  display: flex;
  line-height: 40px;
  margin: 0;
  img {
    width: 40px;
    margin-right: 10px;
  }
}
.pending-item {
  cursor: pointer;
}
.account-id {
  color: #7ac142;
  text-decoration-color: #7ac142;
  text-decoration-line: underline;
  cursor: pointer;
}
</style>
