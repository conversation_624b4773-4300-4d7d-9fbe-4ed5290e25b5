<template>
  <q-page id="cow__report__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Partner"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row">
        <div class="col-sm-12">
          <ProgramBalance :viewReportFlag="false"
                          v-model="chartDateRange"></ProgramBalance>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Program History</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field === 'Amount'">
              <span v-if="props.row['Amount'] >= 0"
                    class="amount-item">{{ _.get(props.row, col.field) | moneyFormat}}</span>
              <span v-else
                    class="reduce-amount-item">{{ _.get(props.row, col.field) | moneyFormat}}</span>
            </template>
            <template v-else-if="col.field === 'Running Balance'">
              <span>{{ _.get(props.row, col.field) | moneyFormat}}</span>
            </template>
            <template v-else-if="col.field === 'Member Id'">
              <span @click="goToMember(props.row)"
                    class="account-id">{{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else-if="col.field === 'Transaction Type'">
              <p class="fee-type-item">
                <img v-if="props.row['Transaction Type'] == 'Monthly Fee'"
                     src="/static/jake/icons/cow/monthly_fee.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Transaction Fee'"
                     src="/static/jake/icons/cow/approval_fee.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Member Load'"
                     src="/static/jake/icons/cow/member_load.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Member Refund'"
                     src="/static/jake/icons/cow/member_refund.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Unload Fee'"
                     src="/static/jake/icons/cow/unload_fee.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Membership Fee'"
                     src="/static/jake/icons/cow/member_fee.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Load Fee'"
                     src="/static/jake/icons/cow/member_refund.svg">
                <span class="load-icon"
                      :style="{background: props.row['iconBgColor']}"
                      v-else-if="props.row['iconStr']">
                  <i class="mc-icon"
                     :class="`mc-icon-${props.row['icon']}`"></i>
                </span>
                {{ _.get(props.row, col.field)}}
              </p>
            </template>
            <template v-else-if="col.field === 'Transaction ID'">
              <span v-if="props.row['Transaction Type'] === 'Monthly Fee' || props.row['Transaction Type'] === 'Unload Fee' || props.row['Transaction Type'] === 'Membership Fee' || props.row['Transaction Type'] === 'Transaction Fee'"
                    @click="goToFee(props.row)"
                    class="account-id">{{ _.get(props.row, col.field) }}</span>
              <span v-else-if="props.row['Transaction Type'] === 'Member Load' || props.row['Transaction Type'] === 'Member Refund'"
                    @click="goToLoad(props.row)"
                    class="account-id">{{ _.get(props.row, col.field) }}</span>
              <span v-else
                    @click="goToTransaction(props.row)"
                    class="account-id">{{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import CowPageMixin from '../../../mixins/cow/CowPageMixin'
import ProgramBalance from '../common/programBalance'

export default {
  name: 'cow-funding',
  mixins: [
    CowPageMixin,
    ListPageMixin,
    EventHandlerMixin('reload-cow-funding')
  ],
  components: {
    ProgramBalance
  },
  data () {
    return {
      title: 'Program',
      requestUrl: `/admin/cow/program/list`,
      downloadUrl: `/admin/cow/program/export`,
      keyword: '',
      dateRange: 'all',
      chartDateRange: 'all',
      columns: generateColumns([
        'Transaction Type', 'Date & time', 'First Name', 'Last Name', 'Member Id', 'Transaction ID',
        'Amount', 'Running Balance'
      ]),
      filterOptions: [
        {
          value: 'filter[cpb.type=]',
          label: 'Transaction Type',
          options: [
            { label: 'Monthly Fee', value: 'monthly_fee' },
            { label: 'Unload Fee', value: 'unload_fee' },
            { label: 'Member Load', value: 'load_card' },
            { label: 'Member Refund', value: 'unload_card' },
            { label: 'Transaction Fee', value: 'transaction' },
            { label: 'Transaction Return', value: 'privacy_return' },
            { label: 'Transaction', value: 'transaction_record' }
          ]
        },
        {
          value: 'filter[m.firstName=]',
          label: 'First Name'
        },
        {
          value: 'filter[m.lastName=]',
          label: 'Last Name'
        },
        {
          value: 'filter[m.id=]',
          label: 'Memeber ID'
        }
      ],
      autoLoad: true
    }
  },
  methods: {
    async reload () {
      let data = {}
      if (this.$refs && this.$refs.dateRangeFilter) {
        data = this.$refs.dateRangeFilter.params()
      }
      this.chartDateRange = data.period
      await this.request({
        pagination: this.pagination
      })
      this.everLoaded = true
    },
    goToMember (row) {
      const { href } = this.$router.resolve({
        path: `/j/cow/members/${row['Member Id']}`
      })
      window.open(href, '_blank')
    },
    goToLoad (row) {
      const { href } = this.$router.resolve({
        path: `/j/cow/report/member_loads`
      })
      localStorage.setItem('openLoadId', row['Transaction ID'])
      window.open(href, '_blank')
    },
    goToTransaction (row) {
      const { href } = this.$router.resolve({
        path: `/j/cow/report/approved`
      })
      localStorage.setItem('approvedTransactionId', row['Transaction ID'])
      window.open(href, '_blank')
    },
    goToFee (row) {
      const { href } = this.$router.resolve({
        path: `/j/cow/report/program_fee`
      })
      localStorage.setItem('openFeeId', row['Transaction ID'])
      localStorage.setItem('openFeeType', row['Transaction Type'])
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss">
.reduce-amount-item {
  color: #fc5a5a;
}
.waite-item {
  color: #f3522b;
}
.fee-type-item {
  display: flex;
  line-height: 40px;
  margin: 0;
  img {
    width: 40px;
    margin-right: 10px;
  }
}
.account-id {
  color: #7ac142;
  text-decoration-color: #7ac142;
  text-decoration-line: underline;
  cursor: pointer;
}
/* Privacy global css */
.mc-icon-computer,
.mc-icon-entertainment,
.mc-icon-finance,
.mc-icon-food,
.mc-icon-health,
.mc-icon-house,
.mc-icon-other,
.mc-icon-transportation {
  max-width: 100%;
  background-size: 100%;
  background-image: url("/static/usu/img/category/all.png");
  width: 70%;
  height: 70%;
  top: 6px !important;
  left: 6px !important;
}
.mc-icon-computer {
  background-position: 0 0;
  background-size: 100%;
}
.mc-icon-entertainment {
  background-position: 0 14.285714%;
  background-size: 100%;
}
.mc-icon-finance {
  background-position: 0 28.571429%;
  background-size: 100%;
}
.mc-icon-food {
  background-position: 0 42.857143%;
  background-size: 100%;
}
.mc-icon-health {
  background-position: 0 57.142857%;
  background-size: 100%;
}
.mc-icon-house {
  background-position: 0 71.428571%;
  background-size: 100%;
}
.mc-icon-other {
  background-position: 0 85.714286%;
  background-size: 100%;
}
.mc-icon-transportation {
  background-position: 0 100%;
  background-size: 100%;
}
.load-icon {
  width: 40px;
  margin-right: 10px;
  border-radius: 20px;
  position: relative;
  i {
    position: absolute;
  }
}
</style>
