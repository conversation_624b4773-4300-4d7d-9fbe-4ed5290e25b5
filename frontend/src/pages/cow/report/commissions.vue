<template>
  <q-page id="cow__commissions_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Partner"
                  :options="cardPrograms"></q-select>
        <!-- <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter> -->
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img icon-yellow"><img src="/static/jake/icons/cash_funding.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.totalNumber || 0 }}</div>
                  <div class="description">Number of Month</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img icon-green"><img src="/static/jake/icons/cash_funding.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.totalFee || 0 | moneyFormat}}</div>
                  <div class="description">Total Fee Amount</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img icon-green"><img src="/static/jake/icons/cash_funding.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.partner || 0 | moneyFormat }}</div>
                  <div class="description">Partner Revenue</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img icon-green"><img src="/static/jake/icons/cash_funding.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.tern || 0 | moneyFormat }}</div>
                  <div class="description">Platform Revenue</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Commissions Report</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field === 'Date & Time'">
              {{ _.get(props.row, col.field)}}
            </template>
            <template v-else>
              <div class="text-center"
                   notranslate="">{{ _.get(props.row, col.field) | moneyFormat}}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import CowPageMixin from '../../../mixins/cow/CowPageMixin'

export default {
  name: 'cow-commissionss',
  mixins: [
    CowPageMixin,
    ListPageMixin,
    EventHandlerMixin('reload-cow-commissions')
  ],
  components: {
  },
  data () {
    return {
      title: 'Commissions',
      keyword: '',
      requestUrl: `/admin/cow/commissions/list`,
      downloadUrl: `/admin/cow/commissions/export`,
      columns: generateColumns([
        'Date & Time', 'MemberShip Fee', 'Load Fee', 'Transaction Fee', 'Monthly Fee', 'Unload Fee', 'Total Fee', 'Partner Revenue', 'Platform Revenue'
      ]),
      autoLoad: true
    }
  },
  methods: {
  }
}
</script>
<style lang="scss">
.q-table {
  .q-chip.warning {
    background-color: rgba($color: #ffc300, $alpha: 0.1);
    color: #ffc300 !important;
  }
  .q-chip.positive {
    background-color: rgba($color: #00d993, $alpha: 0.1);
    color: #00d993 !important;
  }
  .q-chip.negative {
    background-color: rgba($color: #fc5a5a, $alpha: 0.1);
    color: #fc5a5a !important;
  }
  .q-chip.orange {
    background-color: rgba($color: #ff974a, $alpha: 0.1);
    color: #ff974a !important;
  }
}
.total-balance {
  color: #f16501;
}
.avg-balance {
  color: #f16501;
}
.icon-img {
  width: 24px;
  height: 24px;
  display: flex;
  img {
    margin: 0 auto;
    width: 18px;
  }
}
.icon-yellow {
  color: #fcb72a;
  background: rgba($color: #fcb72a, $alpha: 0.1);
  img {
    -webkit-filter: invert(0.5) sepia(1) saturate(5);
    filter: invert(0.5) sepia(1) saturate(5);
  }
}
.icon-green {
  color: #7ac142;
  background: rgba($color: #7ac142, $alpha: 0.1);
  img {
    -webkit-filter: invert(0.5) sepia(1) saturate(5) hue-rotate(85deg);
    filter: invert(0.5) sepia(1) saturate(5) hue-rotate(85deg);
  }
}
</style>
