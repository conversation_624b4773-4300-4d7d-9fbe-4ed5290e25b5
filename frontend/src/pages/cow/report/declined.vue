<template>
  <q-page id="cow__decline_transfers_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Partner"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon class="icon-item"
                        name="mdi-close-circle-outline"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}
                    <q-chip dense
                            :class="quick.preTotal >= 0 ? 'positive' : 'negative'">{{ quick.preTotal | percent(1, true) }}</q-chip>
                  </div>
                  <div class="description">Number of Declined Transactions</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img icon-red"><img src="/static/jake/icons/cash_funding.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.totalAmount || 0 | moneyFormat}}
                    <q-chip dense
                            :class="quick.preTotalAmount >= 0 ? 'positive' : 'negative'">{{ quick.preTotalAmount | percent(1, true) }}</q-chip>
                  </div>
                  <div class="description">Total of Declined Transactions ($)</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-equalizer-outline"
                        class="icon-item"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.avgAmount || 0 | moneyFormat }}
                    <q-chip dense
                            :class="quick.preAvgAmount >= 0 ? 'positive' : 'negative'">{{ quick.preAvgAmount | percent(1, true) }}</q-chip>
                  </div>
                  <div class="description">Average of Declined Transactions</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Member Loads Report</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="
                col.field==='Status'">
              <q-chip class="
                font-12
                status-item
                negative">
                Declined
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Amount'">
              {{props.row['Amount'] | moneyFormat}}
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import CowPageMixin from '../../../mixins/cow/CowPageMixin'

export default {
  name: 'cow-decline_transfers',
  mixins: [
    CowPageMixin,
    ListPageMixin,
    EventHandlerMixin('reload-cow-decline_transfers')
  ],
  components: {
  },
  data () {
    return {
      title: 'Declined transactions',
      keyword: '',
      requestUrl: `/admin/cow/decline_transactions/list`,
      downloadUrl: `/admin/cow/decline_transactions/export`,
      columns: generateColumns([
        'Txn Date & Time', 'Account ID', 'Email', 'Amount', 'Reason for Decline', 'Merchant', 'Status'
      ], [], {
        'Txn Date & Time': 'ucd.txnTime',
        'Account ID': 'u.id',
        'Email': 'u.email',
        'Amount': 'ucd.txnAmount',
        'Reason for Decline': 'ucd.declineReason',
        'Merchant': 'm.merchant'
      }),
      dateRange: 'all',
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        }, {
          value: 'filter[u.mobilephone=]',
          label: 'Mobile Phone'
        }, {
          value: 'filter[u.status=]',
          label: 'Status',
          options: [
            { label: 'Active', value: 'active' },
            { label: 'Archived', value: 'closed' }
          ]
        }
      ],
      autoLoad: true
    }
  },
  methods: {
  }
}
</script>
<style lang="scss">
.icon-item {
  color: #fc5a5a;
  background: rgba($color: #fc5a5a, $alpha: 0.1) !important;
}
.icon-img.icon-red {
  width: 24px;
  height: 24px;
  display: flex;
  background: rgba($color: #fc5a5a, $alpha: 0.1) !important;
  img {
    margin: 0 auto;
    width: 18px;
    -webkit-filter: invert(0.5) sepia(1) saturate(5) hue-rotate(-45deg);
    filter: invert(0.5) sepia(1) saturate(5) hue-rotate(-45deg);
  }
}
</style>
