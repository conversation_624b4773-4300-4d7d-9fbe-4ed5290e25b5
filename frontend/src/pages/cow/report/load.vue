<template>
  <q-page id="cow__members__load_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Partner"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div v-if="$store.state.User.teams.indexOf('CashOnWeb Agent') === -1"
           class="row gutter-sm">
        <div class="col-sm-2-5">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-subtitles-outline"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}
                    <q-chip dense
                            :class="quick.preTotal >= 0 ? 'positive' : 'negative'">{{ quick.preTotal | percent(1, true) }}</q-chip>
                  </div>
                  <div class="description">Number of loads</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-2-5">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-subtitles-outline"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalLoad || 0 | moneyFormat}}
                    <q-chip dense
                            :class="quick.preTotalLoad >= 0 ? 'positive' : 'negative'">{{ quick.preTotalLoad | percent(1, true) }}</q-chip>
                  </div>
                  <div class="description">Active Load Amount</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-2-5">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-equalizer-outline"
                        color="warning"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.avgAmount || 0 | moneyFormat }}
                    <q-chip dense
                            :class="quick.preAvgAmount >= 0 ? 'positive' : 'negative'">{{ quick.preAvgAmount | percent(1, true) }}</q-chip>
                  </div>
                  <div class="description">Average Load Amount</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-2-5">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"
                        class="total-balance"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalBalance || 0 | moneyFormat }}</div>
                  <div class="description">Total User Account Balances</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-2-5">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-subtitles-outline"
                        class="total-balance"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalLoadFee || 0 | moneyFormat }}</div>
                  <div class="description">Total Load Fee</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Member Loads Report</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12 status-item"
                      :class="props.row['Status'] === 'loaded' ? 'positive' : 'negative'">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Amount'">
              {{props.row['Amount'] | moneyFormat}}
            </template>
            <template v-else-if="col.field === 'Load Fee'">
              {{props.row['Load Fee'] | moneyFormat}}
            </template>
            <template v-else-if="col.field === 'Account ID'">
              <span @click="goToMember(props.row)"
                    class="account-id">{{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else-if="col.field === 'Loaded By'">
              <span v-if="$store.state.User.teams.indexOf('CashOnWeb Agent') === -1 && $store.state.User.teams.indexOf('CashOnWeb Compliance') === -1"
                    @click="goToAgent(props.row)"
                    class="account-id">{{ _.get(props.row, col.field) }}</span>
              <span v-else>{{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else-if="col.field === 'Receipt'">
              <span class="account-id"
                    @click="receiptInfo(props.row)">View</span>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <Receipt></Receipt>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import CowPageMixin from '../../../mixins/cow/CowPageMixin'
import Receipt from '../common/receipt'
import _ from 'lodash'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'cow-member-loads',
  mixins: [
    CowPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-cow-member-loads')
  ],
  components: {
    Receipt
  },
  computed: {
    visibleColumns () {
      return _.filter(this.columns, c => {
        if ((this.$store.state.User.teams.indexOf('CashOnWeb Agent') !== -1 || this.$store.state.User.teams.indexOf('CashOnWeb Compliance') !== -1) && c.label === 'Loaded By') {
          c.hidden = true
        }
        return !c.hidden
      })
    }
  },
  mounted () {
    this.keyword = localStorage.getItem('openLoadId') ? localStorage.getItem('openLoadId') : ''
    localStorage.setItem('openLoadId', '')
  },
  data () {
    return {
      title: 'Member Loads',
      keyword: '',
      requestUrl: `/admin/cow/members/load/list`,
      filtersUrl: '/admin/cow/members/load/filters',
      downloadUrl: `/admin/cow/members/export-load`,
      columns: generateColumns([
        'Date & Time', 'Account ID', 'Email', 'Transaction ID', 'Amount', 'Load Fee', 'Loaded By', 'Agent Name', 'Status', 'Receipt'
      ], [], {
        'Date & Time': 'ucl.createdAt',
        'Account ID': 'u.id',
        'Email': 'u.email',
        'Transaction ID': 'ucl.id',
        'Amount': 'ucl.initialAmount',
        'Loaded By': 'ucl.createBy',
        'Agent Name': ''
      }),
      dateRange: 'all',
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        },
        // }, {
        //   value: 'filter[u.status=]',
        //   label: 'Status',
        //   options: [],
        //   source: 'loadStatuses'
        // },
        {
          value: 'filter[ucl.type=]',
          label: 'Type',
          options: [
            { label: 'Load', value: 'load_card' },
            { label: 'Refund', value: 'unload' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    receiptInfo (row) {
      row['type'] = row['Status'] === 'loaded' ? 'load' : 'unload'
      this.$root.$emit('show-cow-receipt-dialog', row)
    },
    goToMember (row) {
      const { href } = this.$router.resolve({
        path: `/j/cow/members/${row['Account ID']}`
      })
      window.open(href, '_blank')
    },
    goToAgent (row) {
      const { href } = this.$router.resolve({
        path: `/j/cow/agents`
      })
      localStorage.setItem('agentId', row['Loaded By'])
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss">
.q-table {
  .q-chip.warning {
    background-color: rgba($color: #ffc300, $alpha: 0.1);
    color: #ffc300 !important;
  }
  .q-chip.positive {
    background-color: rgba($color: #00d993, $alpha: 0.1);
    color: #00d993 !important;
  }
  .q-chip.negative {
    background-color: rgba($color: #fc5a5a, $alpha: 0.1);
    color: #fc5a5a !important;
  }
  .q-chip.orange {
    background-color: rgba($color: #ff974a, $alpha: 0.1);
    color: #ff974a !important;
  }
}
.total-balance {
  color: #7ac142;
  background-color: rgba($color: #7ac142, $alpha: 0.1);
}
.status-item {
  text-transform: capitalize;
}
.account-id {
  color: #7ac142;
  text-decoration-color: #7ac142;
  text-decoration-line: underline;
  cursor: pointer;
}
</style>
