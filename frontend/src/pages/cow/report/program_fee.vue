<template>
  <q-page id="cow__program_fee_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Partner"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img icon-yellow"><img src="/static/jake/icons/cash_funding.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}
                    <q-chip dense
                            :class="quick.preTotal >= 0 ? 'positive' : 'negative'">{{ quick.preTotal | percent(1, true) }}</q-chip>

                  </div>
                  <div class="description">Number of Fees</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img icon-green"><img src="/static/jake/icons/cash_funding.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.totalFee || 0 | moneyFormat}}
                    <q-chip dense
                            :class="quick.preTotalFee >= 0 ? 'positive' : 'negative'">{{ quick.preTotalFee | percent(1, true) }}</q-chip>
                  </div>
                  <div class="description">Total Fee Amount</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img icon-green"><img src="/static/jake/icons/cash_funding.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.partner || 0 | moneyFormat }}
                    <q-chip dense
                            :class="quick.prePartner >= 0 ? 'positive' : 'negative'">{{ quick.prePartner | percent(1, true) }}</q-chip>
                  </div>
                  <div class="description">Partner Revenue</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img icon-green"><img src="/static/jake/icons/cash_funding.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.tern || 0 | moneyFormat }}
                    <q-chip dense
                            :class="quick.preTern >= 0 ? 'positive' : 'negative'">{{ quick.preTern | percent(1, true) }}</q-chip>
                  </div>
                  <div class="description">Platform Revenue</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Fee Events</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Date & Time'">
              {{ _.get(props.row, col.field)}}
            </template>
            <template v-else-if="col.field ===  'Transaction Type'">
              <p class="fee-type-item">
                <img v-if="props.row['Transaction Type'] == 'Monthly Fee'"
                     src="/static/jake/icons/cow/monthly_fee.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Approval Fee'"
                     src="/static/jake/icons/cow/approval_fee.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Membership Fee'"
                     src="/static/jake/icons/cow/member_fee.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Unload Fee'"
                     src="/static/jake/icons/cow/unload_fee.svg">
                <img v-else-if="props.row['Transaction Type'] == 'Load Fee'"
                     src="/static/jake/icons/cow/unload_fee.svg">
                {{ _.get(props.row, col.field)}}
              </p>
            </template>
            <template v-else-if="col.field === 'Amount'">
              <span class="amount-item">{{ _.get(props.row, col.field) | moneyFormat}}</span>
            </template>
            <template v-else-if="col.field === 'Transaction ID'">
              <span v-if="props.row['Transaction Type'] === 'Monthly Fee'"
                    @click="goToMember(props.row)"
                    class="account-id">{{ _.get(props.row, col.field) }}</span>
              <span v-else-if="props.row['Transaction Type'] === 'Load Fee' || props.row['Transaction Type'] === 'Membership Fee' || props.row['Transaction Type'] === 'Unload Fee'"
                    @click="goToLoad(props.row)"
                    class="account-id">{{ _.get(props.row, col.field) }}</span>
              <span v-else
                    @click="goToTransaction(props.row)"
                    class="account-id">{{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else-if="col.field === 'Member ID'">
              <span @click="goToMember(props.row)"
                    class="account-id">{{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else-if="col.field === 'First Name' || col.field === 'Last Name' || col.field === 'Email'">
              {{ _.get(props.row, col.field) }}
            </template>
            <template v-else>
              <div class="text-center"
                   notranslate="">{{ _.get(props.row, col.field) | moneyFormat}}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import CowPageMixin from '../../../mixins/cow/CowPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'cow-program_fee',
  mixins: [
    CowPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-cow-program_fee')
  ],
  components: {
  },
  mounted () {
    switch (localStorage.getItem('openFeeType')) {
      case 'Monthly Fee':
        this.filters.push(
          {
            'predicate': '=',
            'field': 'filter[ufh.feeName=]',
            'value': 'Monthly Maintenance fee'
          }
        )
        break
      case 'Unload Fee':
        this.filters.push(
          {
            'predicate': '=',
            'field': 'filter[ufh.feeName=]',
            'value': 'Unload fee'
          }
        )
        break
      case 'Transaction Fee':
      case 'Approval Fee':
        this.filters.push(
          {
            'predicate': '=',
            'field': 'filter[ufh.feeName=]',
            'value': 'Approved Transaction'
          }
        )
        break
      case 'Membership Fee':
      case 'Member Fee':
        this.filters.push(
          {
            'predicate': '=',
            'field': 'filter[ufh.feeName=]',
            'value': 'One-time membership fee'
          }
        )
        break
    }
    this.keyword = localStorage.getItem('openFeeId')
    if (this.keyword || this.filters.length) {
      this.reload()
    }
  },
  data () {
    return {
      title: 'Program Fee Detail',
      keyword: '',
      requestUrl: `/admin/cow/commissions/program/fee/list`,
      downloadUrl: `/admin/cow/commissions/program/fee/export`,
      columns: generateColumns([
        'Transaction Type', 'Date & Time', 'Transaction ID', 'Amount', 'Member ID', 'First Name', 'Last Name', 'Email', 'Partner Revenue', 'Platform Revenue'
      ], [], {
        'Transaction Type': 'ufh.feeName',
        'Date & Time': 'ufh.time',
        'Transaction ID': 'ufh.entityId',
        'Amount': 'ufh.cost + ufh.amount',
        'Member ID': 'u.id',
        'First Name': 'u.firstName',
        'Last Name': 'u.lastName',
        'Email': 'u.email',
        'Partner Revenue': 'ufh.cost',
        'Platform Revenue': 'ufh.amount'
      }),
      dateRange: 'all',
      filterOptions: [
        {
          value: 'filter[u.id]',
          label: 'User ID'
        },
        {
          value: 'filter[ufh.feeName=]',
          label: 'Transaction Type',
          options: [
            { label: 'Monthly Fee', value: 'Monthly Maintenance fee' },
            { label: 'Unload Fee', value: 'Unload fee' },
            { label: 'Membership Fee', value: 'One-time membership fee' },
            { label: 'Approval Fee', value: 'Approved Transaction' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 0
    }
  },
  methods: {
    async reload () {
      await this.request({
        pagination: this.pagination
      })
      localStorage.setItem('openFeeId', '')
      localStorage.setItem('openFeeType', '')
      this.everLoaded = true
    },
    goToMember (row) {
      const { href } = this.$router.resolve({
        path: `/j/cow/members/${row['Member ID']}`
      })
      window.open(href, '_blank')
    },
    goToLoad (row) {
      const { href } = this.$router.resolve({
        path: `/j/cow/report/member_loads`
      })
      localStorage.setItem('openLoadId', row['Transaction ID'])
      window.open(href, '_blank')
    },
    goToTransaction (row) {
      const { href } = this.$router.resolve({
        path: `/j/cow/report/approved`
      })
      localStorage.setItem('approvedTransactionId', row['Transaction ID'])
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss">
.q-table {
  .q-chip.warning {
    background-color: rgba($color: #ffc300, $alpha: 0.1);
    color: #ffc300 !important;
  }
  .q-chip.positive {
    background-color: rgba($color: #00d993, $alpha: 0.1);
    color: #00d993 !important;
  }
  .q-chip.negative {
    background-color: rgba($color: #fc5a5a, $alpha: 0.1);
    color: #fc5a5a !important;
  }
  .q-chip.orange {
    background-color: rgba($color: #ff974a, $alpha: 0.1);
    color: #ff974a !important;
  }
}
.total-balance {
  color: #f16501;
}
.avg-balance {
  color: #f16501;
}
.icon-img {
  width: 24px;
  height: 24px;
  display: flex;
  img {
    margin: 0 auto;
    width: 18px;
  }
}
.icon-yellow {
  color: #fcb72a;
  background: rgba($color: #fcb72a, $alpha: 0.1);
  img {
    -webkit-filter: invert(0.5) sepia(1) saturate(5);
    filter: invert(0.5) sepia(1) saturate(5);
  }
}
.icon-green {
  color: #7ac142;
  background: rgba($color: #7ac142, $alpha: 0.1);
  img {
    -webkit-filter: invert(0.5) sepia(1) saturate(5) hue-rotate(85deg);
    filter: invert(0.5) sepia(1) saturate(5) hue-rotate(85deg);
  }
}
.fee-type-item {
  display: flex;
  line-height: 40px;
  margin: 0;
  img {
    width: 40px;
    margin-right: 10px;
  }
}
.amount-item {
  color: #01da33;
}
.account-id {
  color: #7ac142;
  text-decoration-color: #7ac142;
  text-decoration-line: underline;
  cursor: pointer;
}
</style>
