<template>
  <q-page id="cow__report__void_queue_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Partner"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img"><img src="/static/jake/icons/cow_transactions_icon.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}
                    <q-chip dense
                            :class="quick.preTotal >= 0 ? 'positive' : 'negative'">{{ quick.preTotal | percent(1, true) }}</q-chip>
                  </div>
                  <div class="description">Number of Transaction Voids</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img icon-green"><img src="/static/jake/icons/cow_transactions_green_icon.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.totalAmount || 0 | moneyFormat }}
                    <q-chip dense
                            :class="quick.preTotalAmount >= 0 ? 'positive' : 'negative'">{{ quick.preTotalAmount | percent(1, true) }}</q-chip>
                  </div>
                  <div class="description">Total Void Amount ($)</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-equalizer-outline"
                        color="warning"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.avgAmount || 0 | moneyFormat }}
                    <q-chip dense
                            :class="quick.preAvgAmount >= 0 ? 'positive' : 'negative'">{{ quick.preAvgAmount | percent(1, true) }}</q-chip>

                  </div>
                  <div class="description">Average Void Transaction</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Transactions Report</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field == 'Amount'">
              {{ _.get(props.row, col.field) | moneyFormat }}
            </template>
            <template v-else-if="col.field === 'Account ID'">
              <span class="account-id">{{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else-if="col.field === 'Status'">
              <q-chip class="font-12 status-item negative">
                {{ _.get(props.row, col.field) }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="manualRelease(props.row)">
                    <q-item-main>Release Hold</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, request, notifySuccess } from '../../../common'
import CowPageMixin from '../../../mixins/cow/CowPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'cow-void',
  mixins: [
    CowPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-cow-void')
  ],
  components: {},
  data () {
    return {
      title: 'Transactions Void Queue',
      requestUrl: `/admin/cow/transaction_void/list`,
      filtersUrl: `/admin/cow/transaction/filters`,
      downloadUrl: `/admin/cow/transaction_void/export`,
      keyword: '',
      columns: generateColumns([
        'Date & time', 'Account ID', 'Email',
        'Amount', 'Void Date & Time', 'Merchant', 'Days Voided', 'Status', 'Action'
      ], [], {
        'Date & time': 'uct.txnTime',
        'Account ID': 'u.id',
        'Email': 'u.emial',
        'Amount': 'uct.txnAmount',
        'Void Date & Time': 'uct.postTime',
        'Merchant': 'm.merchantCustName'
      }),
      dateRange: 'all',
      filterOptions: [
        {
          value: 'filter[u.id]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        },
        {
          value: 'uct.tranDesc',
          label: 'Transaction Type',
          options: [
            {
              value: '',
              label: 'All'
            }, {
              value: '10 = Monthly Fee',
              label: '10 = Monthly Fee'
            }, {
              value: '11 = Monthly Fee Reversal',
              label: '11 = Monthly Fee Reversal'
            }, {
              value: '37 = ACH / Payroll Deposit',
              label: '37 = ACH / Payroll Deposit'
            }, {
              value: '40 = Signature (POS) Purchase',
              label: '40 = Signature (POS) Purchase'
            }, {
              value: '41 = Purchase Return',
              label: '41 = Purchase Return'
            }, {
              value: '51 = Flaged for Charge-off',
              label: '51 = Flaged for Charge-off'
            }, {
              value: '148 = Card to Card Transfer Debit',
              label: '148 = Card to Card Transfer Debit'
            }, {
              value: '149 = Card to Card Transfer Credit',
              label: '149 = Card to Card Transfer Credit'
            }, {
              value: '214 = Purchase Declined Transaction Fee',
              label: '214 = Purchase Declined Transaction Fee'
            }, {
              value: '215 = POS Declined Transaction Fee Refund',
              label: '215 = POS Declined Transaction Fee Refund'
            }, {
              value: '220 = Signature (POS) Purchase Fee',
              label: '220 = Signature (POS) Purchase Fee'
            }, {
              value: '404 = Signature (POS) Purchase (International)',
              label: '404 = Signature (POS) Purchase (International)'
            }, {
              value: '461 = Reserve funding deposit',
              label: '461 = Reserve funding deposit'
            }, {
              value: '462 = Reserve funding deposit reversal',
              label: '462 = Reserve funding deposit reversal'
            }, {
              value: '5032 = ACH Debit',
              label: '5032 = ACH Debit'
            }, {
              value: '00220 = Signature (POS) Purchase Fee',
              label: '00220 = Signature (POS) Purchase Fee'
            }, {
              value: '00613 = Reserve Deposit',
              label: '00613 = Reserve Deposit'
            }, {
              value: '00614 = Reserve Deposit Reversal/Withdrawal',
              label: '00614 = Reserve Deposit Reversal/Withdrawal'
            }
          ]
        }, {
          value: 'm.merchantNameAndLocation',
          label: 'Merchant Cust Name'
        },
        {
          value: 'm.listType',
          label: 'Merchant List Type',
          options: [],
          source: 'listTypes'
        }, {
          value: 'mcc',
          label: 'Mcc',
          options: [],
          source: 'mccs'
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    async manualRelease (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to refund the transaction amount to the account balance now?',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/cow/card-transaction/manual-release/${row.id}`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          notifySuccess(resp)
          this.reload()
        }
      }).catch(() => {})
    }
  }
}
</script>
<style lang="scss">
.icon-img {
  width: 24px;
  height: 24px;
  display: flex;
  img {
    margin: 0 auto;
    width: 18px;
  }
  background: rgba($color: #000000, $alpha: 0.1);
  border-radius: 5px;
}
.icon-green {
  background: rgba($color: #00d993, $alpha: 0.1);
}
.account-id {
  color: #7ac142;
  text-decoration-color: #7ac142;
  text-decoration-line: underline;
}
</style>
