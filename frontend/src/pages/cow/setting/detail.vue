<template>
  <q-dialog class="cow-location-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Create New Location</div>
      <div class="font-12 normal text-dark">Please fill in the information below about the location.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-input float-label="Location Name"
                   autocomplete="no"
                   :error="$v.entity['Location Name'].$error"
                   @blur="$v.entity['Location Name'].$touch"
                   v-model="entity['Location Name']"></q-input>
        </div>
        <div class="col-sm-12">
          <div class="row">
            <q-select class="phone-code col-4"
                      autocomplete="no"
                      float-label="Location Phone"
                      filter
                      autofocus-filter
                      v-model="entity['phoneCode']"
                      :options="countryCodeList" />
            <q-input class="phone-number col-8"
                     autocomplete="no"
                     placeholder="Phone Only digits. No country dial code."
                     :error="$v.entity['phone'].$error"
                     @blur="$v.entity['phone'].$touch"
                     v-model="entity['phone']"></q-input>
          </div>
        </div>
        <div class="col-sm-6">
          <q-select autocomplete="no"
                    float-label="Country"
                    placeholder="Country"
                    :options="countries"
                    filter
                    autofocus-filter
                    :disabled="edit"
                    :error="$v.entity['CountryId'].$error"
                    @change="$v.entity['CountryId'].$touch"
                    v-model="entity['CountryId']"></q-select>
        </div>
        <!-- <div class="col-sm-6">
          <q-select float-label="State / Province"
                    autocomplete="no"
                    :options="states"
                    filter
                    autofocus-filter
                    :disabled="edit"
                    :error="$v.entity['State'].$error"
                    :before="stateBefore"
                    @blur="$v.entity['State'].$touch"
                    v-model="entity['State']"></q-select>
        </div> -->
        <div class="col-sm-6">
          <q-input placeholder="City"
                   float-label="City"
                   autocomplete="no"
                   :disabled="edit"
                   :error="$v.entity['Location City'].$error"
                   @input="$v.entity['Location City'].$touch"
                   v-model="entity['Location City']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input autocomplete="no"
                   placeholder="Location Postal Code"
                   float-label="Location Postal Code"
                   :disabled="edit"
                   v-model="entity['Location Postal Code']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input autocomplete="no"
                   placeholder="Address"
                   float-label="Address"
                   :disabled="edit"
                   :error="$v.entity['Location Address'].$error"
                   @input="$v.entity['Location Address'].$touch"
                   v-model="entity['Location Address']"></q-input>
        </div>
        <!-- <div class="col-sm-12 map-item">
          <div id="dispensary-locator-map"></div>
        </div> -->
        <div class="col-sm-12 text-center pt-20">
          <div class="bold">Status</div>
          <div class="mt-8">
            <q-radio v-model="entity['Location Status']"
                     color="blue"
                     :error="$v.entity['Location Status'].$error"
                     @change="$v.entity['Location Status'].$touch"
                     val="Active"
                     label="Active"></q-radio>
            <q-radio v-model="entity['Location Status']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Location Status'].$error"
                     @change="$v.entity['Location Status'].$touch"
                     val="Inactive"
                     label="Inactive"></q-radio>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <template>
        <q-btn label="Cancel"
               no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
        <q-btn v-if="!edit"
               label="Create Location"
               no-caps
               color="positive"
               class="main"
               @click="save" />
        <q-btn v-else
               label="Update Location"
               no-caps
               color="positive"
               class="main"
               @click="save" />
      </template>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request } from '../../../common'
import { required, numeric } from 'vuelidate/lib/validators'
import MexStateListMixin from '../../../mixins/mex/MexStateListMixin'
const countryList = require('country-data').countries

export default {
  name: 'cow-location-detail-dialog',
  mixins: [
    Singleton,
    MexStateListMixin
  ],
  data () {
    return {
      defaultEntity: {
        'Location Status': 'Active',
        'phoneCode': 'CW',
        'phone': ''
      },
      map: null,
      marker: null,
      selected: null,
      position: null,
      countryCodeList: []
    }
  },
  validations: {
    entity: {
      'Location Name': {
        required
      },
      'phone': {
        required,
        numeric
      },
      'CountryId': {
        required
      },
      // 'State': {
      //   required
      // },
      'Location Status': {
        required
      },
      'Location City': {
        required
      },
      // 'Location Postal Code': {
      //   required
      // },
      'Location Address': {
        required
      }
    }
  },
  mounted () {
    countryList.all.map(x => {
      if (x.countryCallingCodes.length) {
        this.countryCodeList.push({
          label: x.emoji ? x.emoji + ' ' + x.countryCallingCodes[0] : x.name + ' ' + x.countryCallingCodes,
          value: x.alpha2,
          code: x.countryCallingCodes[0]
        })
      }
    })
    return this.countryCodeList
  },
  computed: {
    edit () {
      return this.entity['ID']
    }
  },
  methods: {
    onSuccess (resp) {
      notify(resp.message)
      this.$root.$emit('reload-cow-setting')
      this._hide()
    },
    async show () {
      if (this.entity['Location Phone Number']) {
        const phoneList = this.entity['Location Phone Number'].split(' ')
        console.log(phoneList)
        let code = 'CW'
        if (phoneList.length > 1) {
          this.entity['phone'] = phoneList[1]
          this.countryCodeList.forEach(element => {
            console.log(element)
            if (element.code === phoneList[0]) {
              code = element.value
            }
          })
        } else {
          this.entity['phone'] = phoneList[0]
        }
        this.entity['phoneCode'] = code
      }
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      this.entity['Location Phone Number'] = this.entity['phone']
      const resp = await request(`/admin/cow/locator/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    }
  }
}
</script>

<style lang="scss">
.cow-location-detail-dialog {
  .modal-content {
    width: 550px;
  }
  #dispensary-locator-map {
    height: 200px;
    width: 100%;
    z-index: 1;
    background: white;
    margin-top: 10px;
  }
  .phone-code {
    // border-right: none;
    padding-right: 0 !important;
    padding-left: 10px !important;
    min-width: 100px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .phone-number {
    border-left: none;
    max-width: calc(100% - 100px);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
</style>
