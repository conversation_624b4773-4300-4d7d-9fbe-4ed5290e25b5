<template>
  <q-dialog class="cow-velocity-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">New Velocity/Fraud Setting</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <p>Control type</p>
          <q-select autocomplete="no"
                    placeholder="Select Control Type"
                    :options="params.types"
                    filter
                    autofocus-filter
                    v-model="type"></q-select>
        </div>
        <div class="col-sm-12">
          <p>Metric</p>
          <q-select autocomplete="no"
                    placeholder="Select Metric"
                    :options="params.metrics"
                    filter
                    autofocus-filter
                    v-model="metric"></q-select>
        </div>
        <div v-if="entity['type'] !== 'merchant_scan'"
             class="col-sm-12">
          <p>Relation</p>
          <q-select autocomplete="no"
                    placeholder="Select Relation"
                    :options="params.relations"
                    filter
                    autofocus-filter
                    v-model="relation"></q-select>
        </div>
        <div class="col-sm-12"
             v-if="entity['relation'] != '[]' && entity['type'] !== 'merchant_scan'">
          <p>Value</p>
          <input class="input-group"
                 type="number"
                 step="100"
                 v-model="entity.min"
                 :min="0" />
        </div>
        <div class="col-sm-12"
             v-if="entity['relation'] == '[]'">
          <p>Minimum value</p>
          <input class="input-group"
                 type="number"
                 step="100"
                 v-model="entity.min"
                 :min="0" />
        </div>
        <div class="col-sm-12"
             v-if="entity['relation'] == '[]'">
          <p>Maximum value</p>
          <input class="input-group"
                 type="number"
                 step="100"
                 v-model="entity.max"
                 :min="0" />
        </div>
        <div v-if="entity['type'] !== 'merchant_scan'"
             class="col-sm-12">
          <p>Frequency</p>
          <q-select autocomplete="no"
                    placeholder="Select Frequency"
                    :options="params.frequencies"
                    filter
                    autofocus-filter
                    v-model="frequency"></q-select>
        </div>
        <div v-if="entity['type'] === 'merchant_scan'"
             class="col-sm-12">
          <p>List type</p>
          <q-select autocomplete="no"
                    placeholder="None"
                    :options="params.listTypes"
                    filter
                    autofocus-filter
                    v-model="listType"></q-select>
        </div>
        <div class="col-sm-12">
          <p>Actions</p>
          <q-select autocomplete="no"
                    placeholder="Select Actions"
                    :options="params.actions"
                    filter
                    autofocus-filter
                    multiple
                    use-chips
                    stack-label
                    v-model="action"></q-select>
        </div>
        <div class="col-sm-12">
          <p>Exceptions</p>
          <q-select autocomplete="no"
                    placeholder="Nothing selected"
                    :options="params.exceptions"
                    filter
                    autofocus-filter
                    clearable
                    v-model="exception"></q-select>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <template>
        <q-btn label="Cancel"
               no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
        <q-btn v-if="!edit"
               label="Create"
               no-caps
               color="positive"
               class="main"
               @click="save" />
        <q-btn v-else
               label="Update"
               no-caps
               color="positive"
               class="main"
               @click="save" />
      </template>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, request, toSelectOptions } from '../../../common'

export default {
  name: 'cow-velocity-detail-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      params: {
        types: [],
        metrics: [],
        relations: [],
        exceptions: [],
        actions: [],
        frequencies: [],
        listTypes: []
      },
      action: [],
      type: null,
      relation: null,
      metric: null,
      frequency: null,
      min: null,
      max: null,
      exception: null,
      listType: null
    }
  },
  watch: {
    type () {
      this.entity.type = this.type
      this.updateParams()
    },
    action () {
      console.log(this.action)
      console.log(this.action.indexOf('do_nothing'))
      if (this.action.indexOf('do_nothing') !== -1 && this.action.length > 1) {
        if (this.action.indexOf('do_nothing') === 0) {
          this.action.splice('do_nothing', 1)
        } else {
          this.action = ['do_nothing']
        }
      }
      console.log(this.action)
    }
  },
  computed: {
    edit () {
      return this.entity['ID']
    }
  },
  methods: {
    async show () {
      this.$q.loading.show()
      const resp = await request(`admin/cow/velocity/params`, 'get')
      if (resp.success) {
        this.params.types = toSelectOptions(resp.data.types)
        this.params.metrics = toSelectOptions(resp.data.metrics)
        this.params.relations = toSelectOptions(resp.data.relations)
        this.params.actions = toSelectOptions(resp.data.actions)
        this.params.exceptions = toSelectOptions(resp.data.exceptions)
        this.params.frequencies = toSelectOptions(resp.data.frequencies)
        this.params.listTypes = toSelectOptions(resp.data.listTypes)
        if (!this.edit) {
          this.type = this.params.types.length ? this.params.types[0].value : null
          this.relation = this.params.relations.length ? this.params.relations[0].value : null
          this.metric = this.params.metrics.length ? this.params.metrics[0].value : null
          this.frequency = this.params.frequencies.length ? this.params.frequencies[0].value : null
          if (this.params.actions.length) {
            this.action = []
            this.action.push(this.params.actions[0].value)
          }
        } else {
          this.type = this.entity.type
          this.updateParams()
          this.action = this.entity.action.split(',')
          this.relation = this.entity.relation
          this.metric = this.entity.metric
          this.frequency = this.entity.frequency
          this.listType = this.entity.listType
          this.exception = this.entity.exception
        }
      }
      this.$q.loading.hide()
    },
    onSuccess (resp) {
      notify(resp.message)
      this.$root.$emit('reload-cow-velocity-fraud-setting')
      this._hide()
    },
    updateParams () {
      if (this.entity.type === 'merchant_scan') {
        this.params.metrics.forEach(element => {
          element.disable = true
          if (element.value === 'merchant_list_type') {
            element.disable = false
          }
        })
        this.metric = 'merchant_list_type'
      } else {
        this.params.metrics.forEach(element => {
          element.disable = false
          if (element.value === 'merchant_list_type') {
            element.disable = true
          }
        })
        this.metric = this.params.metrics.length ? this.params.metrics[0].value : null
      }
    },
    async save () {
      this.$q.loading.show()
      this.entity.action = this.action.length ? this.action.join(',') : 'do_nothing'
      this.entity.relation = this.relation
      this.entity.metric = this.metric
      this.entity.frequency = this.frequency
      this.entity.listType = this.listType
      this.entity.exception = this.exception
      const resp = await request(`/admin/cow/velocity/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    }
  },
  mounted () {}
}
</script>

<style lang="scss">
.cow-velocity-detail-dialog {
  .modal-content {
    width: 550px;
  }
  p {
    text-align: left;
    font-size: 16px;
    margin-bottom: 5px;
  }
  .input-group {
    width: 100%;
    border: 1px solid #e7e7e7 !important;
    line-height: 36px;
    outline: none;
    border-radius: 10px;
    padding-left: 10px;
  }
}
</style>
