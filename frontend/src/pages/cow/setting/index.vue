<template>
  <q-page id="cow__setting__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }} Settings</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-account-plus-outline"
                 color="positive"
                 label="Create New Location"
                 @click="add"
                 class="btn-sm mr-10"
                 no-caps></q-btn>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>

        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="props.row['Status'] === 'Active' ? 'positive' : 'negative'">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

    <DetailDialog></DetailDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import CowPageMixin from '../../../mixins/cow/CowPageMixin'
import DetailDialog from './detail'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'cow-setting',
  mixins: [
    CowPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-cow-setting')
  ],
  components: {
    DetailDialog
  },
  data () {
    return {
      title: 'Load Location',
      requestUrl: `/admin/cow/locator/list`,
      downloadUrl: `/admin/cow/locator/export`,
      keyword: '',
      columns: generateColumns([
        'Location Name', 'Location Address', 'Location City',
        'Location Postal Code', 'Location Phone Number', 'Location Country',
        'Status', 'Actions'
      ], [], {
        'Location Name': 'l.name',
        'Location Address': 'l.address1',
        'Location City': 'l.city',
        'Location Postal Code': 'l.postalCode',
        'Location Phone Number': 'l.phone',
        'Location Country': 'l.country'
      }),
      filterOptions: [
        {
          value: 'filter[l.name=]',
          label: 'Location Name'
        }, {
          value: 'filter[l.city]',
          label: 'Location City'
        }, {
          value: 'filter[l.phone=]',
          label: 'Phone'
        }, {
          value: 'filter[l.status=]',
          label: 'Status',
          options: [
            { label: 'Active', value: 1 },
            { label: 'Inactive', value: 0 }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    add () {
      this.$root.$emit('show-cow-location-detail-dialog')
    },
    edit (row) {
      console.log(row)
      this.$root.$emit('show-cow-location-detail-dialog', row)
    }
  }
}
</script>
