<template>
  <q-page id="cow__setting__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }} Settings</strong>
        </template>
        <template slot="top-right">
          <q-btn v-if="$store.state.User.teams.indexOf('CashOnWeb Agent') === -1"
                 icon="mdi-account-plus-outline"
                 color="positive"
                 label="Create New"
                 @click="add"
                 class="btn-sm mr-10"
                 no-caps></q-btn>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>

        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Actions'">
              <q-btn-dropdown v-if="$store.state.User.teams.indexOf('CashOnWeb Agent') === -1"
                              class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="deleteItem(props.row)">
                    <q-item-main>Delete</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

    <EditDialog>
    </EditDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, request } from '../../../common'
import CowPageMixin from '../../../mixins/cow/CowPageMixin'
import EditDialog from './edit'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'cow-velocity-fraud-setting',
  mixins: [
    CowPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-cow-velocity-fraud-setting')
  ],
  components: {
    EditDialog
  },
  data () {
    return {
      title: 'Velocity/Fraud',
      requestUrl: `/admin/cow/velocity/list`,
      downloadUrl: `/admin/cow/velocity/export`,
      columns: generateColumns([
        'Card Program', 'Control Type', 'Threshold',
        'Exception', 'Tern Action', 'Actions'
      ]),
      filterOptions: [
        {
          value: 'filter[v.type=]',
          label: 'Control Type',
          options: [
            { label: 'Loads', value: 'loads' },
            { label: 'Usages', value: 'usages' },
            { label: 'Declines', value: 'declines' },
            { label: 'Merchant scan', value: 'merchant_scan' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    add () {
      this.$root.$emit('show-cow-velocity-detail-dialog')
    },
    edit (row) {
      this.$root.$emit('show-cow-velocity-detail-dialog', row)
    },
    async deleteItem (row) {
      if (row['ID']) {
        this.$q.dialog({
          title: 'Confirm',
          message: 'Are you sure that you want to delete the control type: ' + row['Control Type'] + ' Velocity/Fraud Setting?',
          color: 'negative',
          cancel: true
        }).then(async () => {
          this.$q.loading.show()
          await request(`/admin/cow/velocity/delete/${row['ID']}`, 'delete')
          this.$root.$emit('reload-cow-velocity-fraud-setting')
          this.$q.loading.hide()
        }).catch(() => {})
      }
    }
  }
}
</script>
