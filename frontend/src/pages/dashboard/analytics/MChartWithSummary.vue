<template>
  <q-card class="common-dashboard-card">
    <q-card-title>
      <span v-show="titleShow">{{ title }}</span>
    </q-card-title>
    <q-card-main>
      <div class="chart chart-container" :id="chartId + '_chart_graph'"></div>
      <div v-if="isSummary" class="content">
        <div v-for="item in data" :key="item.name" class="row mv-10">
          <div class="col-6" v-line-clamp:20="2">
            {{ item.name }}
          </div>
          <div class="col-2 text-center mcc-value">
            {{ item.value }}
          </div>
          <div class="col-2">
            {{ item.proportion + '%' }}
          </div>
          <div class="col-2" :class="{'mcc-value': item.trend === 1, 'down-rate': item.trend !== 1}">
            <q-icon name="mdi-arrow-up" v-if="item.trend ===1" />
            <q-icon v-else name="mdi-arrow-down" />
            {{ item.rate + '%' }}
          </div>
        </div>
      </div>

      <q-btn round flat
             size="xs"
             @click="reload"
             class="btn-reload"
             icon="mdi-refresh"></q-btn>
    </q-card-main>
  </q-card>
</template>

<script>
import echarts from 'echarts'
import { request } from '../../../common'
import $ from 'jquery'
import _ from 'lodash'
import ChartCardMixin from '../../../mixins/ChartCardMixin'

export default {
  props: [
    'chartId',
    'title',
    'params'
  ],
  mixins: [
    ChartCardMixin
  ],
  data () {
    return {
      titleShow: true,
      chart: null,
      data: {},
      sumData: '',
      isSummary: false,
      chartType: null
    }
  },
  computed: {
    chart_id () {
      return this.chartId + '_chart_graph'
    }
  },
  methods: {
    resize () {
      this.chart.resize()
    },
    initData (data) {
      this.chartType = data
      this.initChart()
      this.reloadType(this.chartType)
      $(window).on('resize', this.resize)
    },
    initChart () {
      this.chart = echarts.init(document.getElementById(this.chart_id), 'primary')
      let option = {
        legend: {
          type: 'scroll',
          textStyle: {
            fontSize: 10
          },
          data: []
        },
        series: [{
          name: 'MCC',
          type: 'pie',
          avoidLabelOverlap: false,
          label: {
            normal: {
              show: false,
              fontSize: 10,
              position: 'center'
            },
            emphasis: {
              show: true
            }
          },
          labelLine: {
            normal: {
              show: false
            }
          },
          selectedMode: 'single',
          center: ['50%', '65%'],
          radius: ['55%', '70%'],
          startAngle: 180,
          data: []
        }]
      }
      this.chart.setOption(option)
    },
    async load (data) {
      if (!this.chart) {
        return
      }
      let url = '/admin/analytics/mcc-with-rate'
      if (this.chartId === 'merchant') {
        url = '/admin/analytics/merchant-with-rate'
      }
      this.$q.loading.show()
      const resp = await request(url, 'get', data)
      if (resp) {
        this.$q.loading.hide()
        this.data = resp.data
        let names = _.transform(resp.data, function (result, n) {
          result.push(n.name)
        })
        this.chart.setOption({
          legend: {
            data: names
          },
          series: [{
            data: resp.data
          }]
        })
      }
    },
    reloadType (data) {
      if (!data) {
        return
      }
      if (data.widget_name === '0') {
        this.titleShow = false
      } else {
        this.titleShow = true
      }
      if (data.chart_type === '3') {
        this.isSummary = true
        let id = '#' + this.chart_id
        $(id).css('display', 'none')
      }
    },

    reload () {
      this.$root.$emit('dashboard-reload-chart', this.chartId)
    }
  }
}
</script>

<style lang="scss">
</style>
