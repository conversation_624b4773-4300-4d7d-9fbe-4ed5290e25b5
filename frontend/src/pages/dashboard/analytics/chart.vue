<template>
  <q-card class="common-dashboard-card">
    <q-card-title>
      <span v-show="titleShow">{{ title }}</span>
      <a href="javascript:"
         @click="toReport">View report</a>
    </q-card-title>
    <q-card-main>
      <div class="total-money">{{ sumData }}</div>
      <div class="chart chart-container"
           :id="chartId + '_chart_graph'"></div>
      <div v-if="chartId === 'load'">
        <load-activity-summary :data="data"
                               :chart-type="chartType"
                               :is-amount-u-s-d="true"
                               v-if="isSummary"></load-activity-summary>
      </div>
      <div v-if="chartId === 'active'">
        <common-activity-summary :types="data.dates"
                                 :items="data.data"
                                 v-if="isSummary"></common-activity-summary>
      </div>
      <div v-if="chartId === 'usage'">
        <common-activity-summary :types="data.usageType"
                                 :items="data.data"
                                 :is-amount-u-s-d="true"
                                 v-if="isSummary"></common-activity-summary>
      </div>
      <div v-if="chartId === 'card_fees_collected'">
        <common-activity-summary :types="data.types"
                                 :items="data.data"
                                 v-if="isSummary"></common-activity-summary>
      </div>
      <div v-if="chartId === 'revenue'">
        <common-activity-summary :types="data.dates"
                                 :items="data.data"
                                 :is-amount-u-s-d="true"
                                 v-if="isSummary"></common-activity-summary>
      </div>
      <div v-if="chartId === 'user'">
        <common-activity-summary :types="data.types"
                                 :items="data.data"
                                 v-if="isSummary"></common-activity-summary>
      </div>
      <div v-if="chartId === 'total_account_balance'">
        <common-activity-summary :types="data.types"
                                 :items="data.data"
                                 v-if="isSummary"></common-activity-summary>
      </div>

      <q-btn round
             flat
             size="xs"
             @click="reload"
             class="btn-reload"
             icon="mdi-refresh"></q-btn>
    </q-card-main>
  </q-card>
</template>

<script>
import $ from 'jquery'
import ChartMixin from '../../../mixins/ChartMixin'
import loadActivitySummary from './summary/loadActivitySummary'
import commonActivitySummary from './summary/CommonActivitySummary'
import ChartCardMixin from '../../../mixins/ChartCardMixin'

export default {
  mixins: [
    ChartMixin,
    ChartCardMixin
  ],
  data () {
    return {
      isSummary: false
    }
  },
  components: {
    loadActivitySummary,
    commonActivitySummary
  },
  computed: {
    chart_id () {
      return this.chartId + '_chart_graph'
    }
  },
  methods: {
    resize () {
      this.chart.resize()
    },
    initData (data) {
      this.chartType = data
      this.initChart()
      this.reloadType(this.chartType)
      $(window).on('resize', this.resize)
    },
    toReport () {
      let cardProgram = this.params.cardProgram
      let start = this.params.start
      let end = this.params.end
      const suffix = `cardProgram=${cardProgram}&start=${start}&end=${end}`
      if (this.chartId === 'load') {
        this.$router.push(`/a/transactions/load?${suffix}`)
      } else if (this.chartId === 'active') {
        this.$router.push(`/a/card-management/card?${suffix}`)
      } else if (this.chartId === 'user') {
        this.$router.push(`/a/user-management/user?${suffix}`)
      } else if (this.chartId === 'card_fees_collected') {
        this.$router.push(`/a/report/fee?${suffix}`)
      } else if (this.chartId === 'usage') {
        this.$router.push(`/a/transactions/list?${suffix}`)
      } else if (this.chartId === 'revenue') {
        this.$router.push(`/a/report/revenue?${suffix}`)
      } else if (this.chartId === 'total_account_balance') {
        this.$router.push(`/a/report/total_account_balance?${suffix}`)
      }
    }
  }
}
</script>
