<template>
  <div class="summary-data">
    <div class="row">
      <div class="col-8">
        <div v-for="(type, k) in types" :key="k + 10" class="mv-10">{{ type }}</div>
      </div>
      <div class="col-4 text-right mcc-value">
        <div v-for="(count, i) in data" :key="i" class="mv-10">{{ count }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { moneyFormat } from '../../../../common'
import _ from 'lodash'

export default {
  props: [
    'types',
    'items',
    'isAmountUSD'
  ],
  computed: {
    data () {
      if (this.isAmountUSD) {
        let data = _.transform(this.items, function (result, n) {
          result.push(moneyFormat(n, 'USD'))
        }, [])
        return data
      }
      return this.items
    }
  }
}
</script>

<style lang="scss">
  @import '../../../../css/variable';
  .summary-data {
    .row {
      margin: 10px !important;
    }
    .mcc-value {
      color: var(--q-color-positive);
    }
  }
</style>
