<template>
  <div class="summary-data">
    <div class="row">
      <div class="col-8">
        <div v-for="date in data.dates" :key="date" class="mv-10">{{ date }}</div>
      </div>
      <div class="col-4 text-right mcc-value">
        <div v-for="(date, i) in contents" :key="i" class="mv-10">{{ date }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'
import { moneyFormat } from '../../../../common'

export default {
  props: [
    'data',
    'chartType',
    'isAmountUSD'
  ],
  methods: {
    formatData (source) {
      if (this.isAmountUSD) {
        let data = _.transform(source, function (result, n) {
          result.push(moneyFormat(n, 'USD'))
        }, [])
        return data
      }
      return source
    }
  },
  computed: {
    contents () {
      if (this.chartType) {
        if (this.chartType.y_axis === '0') {
          return this.formatData(this.data.all)
        }
        if (this.chartType.y_axis === '1') {
          return this.formatData(this.data.first)
        }
        if (this.chartType.y_axis === '2') {
          return this.formatData(this.data.reload)
        }
      }
      return null
    }
  }
}
</script>

<style lang="scss">
  @import '../../../../css/variable';
  .summary-data {
    .row {
      margin: 10px !important;
    }
    .mcc-value {
      color: var(--q-color-positive);
    }
  }
</style>
