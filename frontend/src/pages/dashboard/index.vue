<template>
  <component :is="modeComponent"
             :class="mode"
             v-bind="bindings"></component>
</template>

<script>
import FrameElement from '../common/frame'
import Analytics from './analytics/index'
import Common from './common/index'
import ClfDispensary from '../clf/dashboard/dispensary/index'
import ClfVendor from '../clf/dashboard/vendor/index'
import ClfBank from '../clf/dashboard/bank/index'
import Fis from '../fis/dashboard/index'
import TransferMex from '../mex/dashboard/index'
import Faas from '../faas/base/dashboard/index'
import PageMixin from '../../mixins/PageMixin'

export default {
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'Dashboard',
      modes: {
        affiliate: FrameElement,
        analytics: Analytics,
        common: Common,
        'clf_dispensary': ClfDispensary,
        'clf_vendor': ClfVendor,
        'clf_bank': ClfBank,
        fis: Fis,
        mex: TransferMex,
        faas: Faas
      }
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    },
    mode () {
      return this.user.dashboard || 'common'
    },
    modeComponent () {
      return this.modes[this.mode]
    },
    bindings () {
      const all = {}
      if (this.mode === 'affiliate') {
        all.src = '/admin/dashboard/affiliate'
      }
      return all
    }
  }
}
</script>

<style lang="scss">
</style>
