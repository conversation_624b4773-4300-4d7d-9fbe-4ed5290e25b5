<template>
  <q-dialog class="faas-agent-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="mb-5">
        <div class="avatar">
          <q-icon><img src="/static/faas/img/summary_agents.svg" /></q-icon>
        </div>
      </div>
      <div class="font-16 mb-2">{{ edit ? 'Edit Agent' : 'Create New Agent' }}</div>
      <div class="font-12 normal text-dark">Please fill in the following information below about the agent.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-6">
          <q-input placeholder="First Name"
                   autocomplete="no"
                   :error="$v.entity['First Name'].$error"
                   @input="$v.entity['First Name'].$touch"
                   v-model="entity['First Name']">
          </q-input>
        </div>
        <div class="col-sm-6">
          <q-input placeholder="Last Name"
                   autocomplete="no"
                   :error="$v.entity['Last Name'].$error"
                   @input="$v.entity['Last Name'].$touch"
                   v-model="entity['Last Name']">
          </q-input>
        </div>
        <div class="col-sm-6">
          <q-input placeholder="Email"
                   autocomplete="no"
                   :error="$v.entity['Email'].$error"
                   @input="$v.entity['Email'].$touch"
                   v-model="entity['Email']">
          </q-input>
        </div>
        <div class="col-sm-6">
          <div class="row">
            <q-select class="phone-code col-4"
                      autocomplete="no"
                      filter
                      autofocus-filter
                      v-model="entity['phoneCode']"
                      :options="countries" />
            <q-input class="phone-number col-8"
                     placeholder="Mobile Phone"
                     autocomplete="no"
                     :error="$v.entity['Phone'].$error"
                     @input="$v.entity['Phone'].$touch"
                     v-model="entity['Phone']">
            </q-input>
          </div>
        </div>
        <div class="col-sm-12 text-center mt-5">
          <div class="mb-5 role-label">Role</div>
          <q-radio v-model="entity['User Type']"
                   color="blue"
                   :error="$v.entity['User Type'].$error"
                   @change="$v.entity['User Type'].$touch"
                   val="Administrator"
                   label="Administrator">
          </q-radio>
          <q-radio v-model="entity['User Type']"
                   color="blue"
                   class="ml-15"
                   :error="$v.entity['User Type'].$error"
                   @change="$v.entity['User Type'].$touch"
                   val="Agent"
                   label="Agent">
          </q-radio>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel"
             no-caps
             color="grey-3"
             text-color="tertiary"
             @click="cancel" />
      <q-btn :label="edit ? 'Save Changes' : 'Create New Agent'"
             no-caps
             class="main"
             @click="save" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { required, email, helpers } from 'vuelidate/lib/validators'
import { notifyForm, request, notify } from '../../../../common'
import _ from 'lodash'
const countryList = require('country-data').countries
const alpha = helpers.regex('alpha', /^[0-9+-\s]*$/)

let validations = {
  entity: {}
}
for (const field of [
  'First Name', 'Last Name', 'Email', 'Phone', 'User Type'
]) {
  validations.entity[field] = { required }
}

validations.entity['Phone'] = { required, alpha }
validations.entity['Email'] = { required, email }
export default {
  name: 'faas-agent-detail-dialog',
  mixins: [
    Singleton
  ],
  components: {
  },
  data () {
    return {
      defaultEntity: {
        'User ID': 0,
        'Phone': '',
        'phoneCode': 'US',
        'User Type': 'Agent',
        'Status': 'Active'
      },
      sendKYC: false,
      countries: []
    }
  },
  mounted () {
    countryList.all.map(x => {
      if (x.countryCallingCodes.length) {
        this.countries.push({
          label: x.emoji ? x.emoji + ' ' + x.countryCallingCodes[0] : x.name + ' ' + x.countryCallingCodes,
          value: x.alpha2,
          code: x.countryCallingCodes[0]
        })
      }
    })
    return this.countries
  },
  computed: {
    edit () {
      return this.entity['User ID']
    }
  },
  validations,
  methods: {
    async show () {
      this.$q.loading.show()
      if (this.entity['Mobile Phone']) {
        const phoneList = this.entity['Mobile Phone'].split(' ')
        let code = 'US'
        if (phoneList.length > 1) {
          this.entity['Phone'] = this.entity['Mobile Phone'].split(phoneList[0])[1]
          this.countries.forEach(element => {
            if (element.code === phoneList[0]) {
              code = element.value
            }
          })
        } else {
          this.entity['Phone'] = phoneList[0]
        }
        this.entity['phoneCode'] = code
      }
      const resp = await request(`/admin/faas/base/agents/${this.entity['User ID']}/detail`)
      this.$q.loading.hide()
      if (resp.success) {
        this.entity = _.assignIn({}, this.entity, resp.data.entity)
      }
      this.$q.loading.hide()
    },
    onSuccess (resp) {
      notify(resp.message)
      this.$root.$emit('reload-faas-agents')
      this._hide()
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      this.entity['Mobile Phone'] = this.entity['Phone']
      const resp = await request(`/admin/faas/base/agents/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    },
    cancel () {
      this._hide()
    },
    hide () {
      this.$root.$emit('reload-faas-agent')
    }
  }
}
</script>

<style lang="scss">
.faas-agent-detail-dialog {
  .modal-content {
    width: 600px;
  }
  .gutter-form > div {
    padding-top: 15px;
  }
  .form-field-title {
    font-size: 14px;
    color: #333;
    width: 100%;
    font-weight: 500;
    text-align: left;
    text-transform: none;
    margin-bottom: -10px;
  }
  .avatar .q-icon {
    padding: 10px;
    background: rgba($color: #4acc3d, $alpha: 0.1);
    color: #4acc3d;
    font-size: 20px;
    border-radius: 40px;
  }
  .q-btn.main {
    background: #005daa;
    width: 100%;
    color: #fff;
  }
  .role-label {
    font-weight: 500;
  }
  .phone-code {
    padding-right: 0 !important;
    padding-left: 10px !important;
    min-width: 100px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .phone-number {
    border-left: none;
    max-width: calc(100% - 100px);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
</style>
