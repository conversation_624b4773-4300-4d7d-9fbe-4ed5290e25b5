<template>
  <q-page class="faas_agents_index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Organization"
                  :options="cardPrograms"></q-select>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon class="summary-icon"><img src="/static/faas/img/summary_agents.svg" /></q-icon>
                <div class="column">
                  <div>Total Admins</div>
                  <div class="item-value heavy">
                    {{ quick && quick['totalAdmin'] ? quick['totalAdmin'] : 0 }}
                    <q-chip dense
                            v-if="(quick['percent'] && quick['percent']['totalAdmin'] ? quick['percent']['totalAdmin'] : 0) != 0 "
                            :class="(quick['percent'] && quick['percent']['totalAdmin'] ? quick['percent']['totalAdmin'] : 0 ) > 0 ? 'positive' : 'negative'">
                      <q-icon v-if="(quick['percent'] && quick['percent']['totalAdmin'] ? quick['percent']['totalAdmin'] : 0 ) > 0"
                              class="mdi mdi-trending-up"></q-icon>
                      <q-icon v-if="(quick['percent'] && quick['percent']['totalAdmin'] ? quick['percent']['totalAdmin'] : 0 ) < 0"
                              class="mdi mdi-trending-down"></q-icon>{{ quick['percent'] && quick['percent']['totalAdmin'] ? quick['percent']['totalAdmin'] : 0  | percent(1, true) }}
                    </q-chip>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon class="summary-icon"><img src="/static/faas/img/summary_agents.svg" /></q-icon>
                <div class="column">
                  <div>Total Agents</div>
                  <div class="item-value heavy">
                    {{ quick && quick['totalAgent'] ? quick['totalAgent'] : 0 }}
                    <q-chip dense
                            v-if="(quick['percent'] && quick['percent']['totalAgent'] ? quick['percent']['totalAgent'] : 0) != 0 "
                            :class="(quick['percent'] && quick['percent']['totalAgent'] ? quick['percent']['totalAgent'] : 0 ) > 0 ? 'positive' : 'negative'">
                      <q-icon v-if="(quick['percent'] && quick['percent']['totalAgent'] ? quick['percent']['totalAgent'] : 0 ) > 0"
                              class="mdi mdi-trending-up"></q-icon>
                      <q-icon v-if="(quick['percent'] && quick['percent']['totalAgent'] ? quick['percent']['totalAgent'] : 0 ) < 0"
                              class="mdi mdi-trending-down"></q-icon>{{ quick['percent'] && quick['percent']['totalAgent'] ? quick['percent']['totalAgent'] : 0  | percent(1, true) }}
                    </q-chip>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Agents Report</strong>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm ml-8 export-btn"
                 no-caps></q-btn>
          <q-btn icon="mdi-plus-circle-outline"
                 color="positive"
                 label="Create New Agent"
                 @click="edit(null)"
                 class="btn-sm ml-10"
                 no-caps></q-btn>
        </template>
        <template slot="top-right">
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10 table-btn"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="table-btn"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Role'">
              {{ props.row['User Type'] }}
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>View Agent</q-item-main>
                  </q-item>
                  <!-- <q-item v-close-overlay
                          @click.native="disableFA(props.row)">
                    <q-item-main>Disable 2FA</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="resetFA(props.row)">
                    <q-item-main>Reset 2FA</q-item-main>
                  </q-item> -->
                  <q-item v-if="props.row.Status === 'Invited'"
                          v-close-overlay
                          @click.native="resendInvitation(props.row)">
                    <q-item-main>Resend Invitation Email</q-item-main>
                  </q-item>
                  <q-item v-if="masterAdmin"
                          v-close-overlay
                          @click.native="$c.loginAs(props.row['User ID'])">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

    <Detail></Detail>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notify } from '../../../../common'
import FaasPageMixin from '../../../../mixins/faas/FaasPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import Detail from './detail'

export default {
  name: 'faas-agents',
  mixins: [
    FaasPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-faas-agents')
  ],
  components: {
    Detail
  },
  data () {
    return {
      title: 'Admins & Agents',
      requestUrl: `/admin/faas/base/agents/list`,
      downloadUrl: `/admin/faas/base/agents/export`,
      keyword: '',
      dateRange: 'month',
      columns: generateColumns([
        'User ID', 'First Name', 'Last Name',
        'User Type', 'Email', 'Mobile Phone', 'Create Date', 'Status', 'Actions'
      ], [], {
        'Create Date': 'u.createdAt',
        'User ID': 'u.id',
        'First Name': 'u.firstName',
        'Last Name': 'u.lastName',
        'User Type': 't.name',
        'Email': 'u.email',
        'Mobile Phone': 'u.mobilephone'
      }),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        }, {
          value: 'filter[u.mobilephone=]',
          label: 'Mobile Phone'
        }, {
          value: 'filter[u.status=]',
          label: 'Status',
          options: [
            { label: 'Active', value: 'active' },
            { label: 'Archived', value: 'closed' }
          ]
        }, {
          value: 'filter[t.name=]',
          label: 'User Type',
          options: [
            { label: 'Administrator', value: 'Faas Admin' },
            { label: 'Agent', value: 'Faas Agent' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    statusClass (status) {
      const cls = []
      cls.push({
        'Active': 'positive',
        'Invited': 'blue',
        'Inactive': 'danger'
      }[status] || status)
      return cls
    },
    edit (row) {
      this.$root.$emit('show-faas-agent-detail-dialog', row)
    },
    async resendInvitation (row) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/faas/base/agents/${row['User ID']}/resend-invitation`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
      }
    },
    async disableFA (row) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/faas/base/agents/${row['User ID']}/disable-fa`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
      }
    },
    async resetFA (row) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/faas/base/agents/${row['User ID']}/reset-fa`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
      }
    }
  }
}
</script>
<style lang="scss">
.faas_agents_index_page {
  .page-content {
    .top-statics {
      .q-card-main {
        font-size: 20px;
        color: #191d3d;
      }
      .summary-icon {
        height: 50px !important;
        width: 50px !important;
        font-size: 24px;
        color: #4acc3d;
        background: rgba(74, 204, 61, 0.1) !important;
        border-radius: 50px !important;
        margin-right: 10px;
        align-self: center;
      }
      .item-value {
        font-size: 38px;
      }
    }
    .user-id {
      color: #fa6400;
      text-decoration-line: underline;
      cursor: pointer;
    }
    .export-btn {
      background: #190644 !important;
    }
    .table-btn {
      border: 0.5px solid #5a5a89;
      border-radius: 10px;
    }
  }
  .q-table {
    .locked-column {
      position: relative;
    }
    .locked {
      color: #fc5a5a;
      i.mdi-lock-outline {
        margin-right: 5px;
        position: absolute;
        line-height: 20px;
        left: -20px;
      }
    }
  }
  .danger {
    color: #f10013;
    background: rgba($color: #f10013, $alpha: 0.1) !important;
  }
}
</style>
