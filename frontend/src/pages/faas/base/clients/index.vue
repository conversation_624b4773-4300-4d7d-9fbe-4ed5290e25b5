<template>
  <q-page class="faas_clients_index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Organization"
                  :options="cardPrograms"></q-select>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-briefcase-account-outline"
                        class="summary-icon"></q-icon>
                <div class="column">
                  <div>Total Clients</div>
                  <div class="item-value heavy">{{ quick.total || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-chart-bar"
                        class="summary-icon"></q-icon>
                <div class="column">
                  <div>Average Members</div>
                  <div class="item-value heavy">{{ quick.average || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Clients Report</strong>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm ml-8 export-btn"
                 no-caps></q-btn>
          <q-btn icon="mdi-account-plus-outline"
                 color="positive"
                 label="Create New Client"
                 @click="add"
                 class="btn-sm ml-10"
                 no-caps></q-btn>
        </template>

        <template slot="top-right">
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10 table-btn"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="table-btn"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="props.row['Status'] === 'Active' ? 'positive' : 'negative'">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="['Refund Member ATM Fees', 'Refund Maintenance Fees'].includes(col.field)">
              {{ props.row[col.field] ? 'Yes' : 'No' }}
            </template>
            <template v-else-if="['Rapid Agent Balance', 'BOTM Balance'].includes(col.field)">
              <span>{{ _.get(props.row, col.field) }}</span>
              <a href="javascript:"
                 class="link ml-5 font-14"
                 v-if="_.get(props.row, col.field)"
                 @click="refreshAgentBalance(props.row, col.field)">
                <q-icon :name="props.row.loading ? 'mdi-loading' : 'mdi-refresh'"
                        :class="{'mdi-spin': props.row.loading}"></q-icon>
              </a>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          v-if="props.row['Funding Type'] === 'Prefunded'"
                          @click.native="view(props.row)">
                    <q-item-main>View</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="$c.loginAs(props.row['Client ID'])"
                          v-if="props.row['Funding Type'] === 'Prefunded' && masterAgentAdmin">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="invite(props.row)"
                          v-if="props.row['Funding Type'] === 'Prefunded' && props.row['Status'] === 'Active' && !props.row['Last Login Time']">
                    <q-item-main>Resend Invitation Email</q-item-main>
                  </q-item>
                  <q-item v-if="masterAdmin"
                          v-close-overlay
                          @click.native="$root.$emit('show-mex-employer-rapid-dialog', props.row)">
                    <q-item-main>Config Rapid Account</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="settings(props.row)">
                    <q-item-main>Settings</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="del(props.row)"
                          v-if="props.row['Members'] === 0">
                    <q-item-main>Delete</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

    <DetailDialog></DetailDialog>
    <RapidDialog></RapidDialog>
    <SettingsDialog></SettingsDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, request, notify } from '../../../../common'
import FaasPageMixin from '../../../../mixins/faas/FaasPageMixin'
import DetailDialog from './detail'
import SettingsDialog from './settings'
import RapidDialog from '../../../mex/employers/rapid'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import _ from 'lodash'

export default {
  name: 'faas-clients',
  mixins: [
    FaasPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-faas-clients')
  ],
  components: {
    DetailDialog,
    RapidDialog,
    SettingsDialog
  },
  data () {
    return {
      title: 'Clients',
      requestUrl: `/admin/faas/base/clients/list`,
      downloadUrl: `/admin/faas/base/clients/export`,
      columns: generateColumns([
        'Create Date', 'Client Name', 'Client ID', 'Client Address',
        'Contact Name', 'Email', 'Phone', 'Referer', 'Funding Type',
        'Refund Member ATM Fees', 'Refund Maintenance Fees',
        'Active Members', 'Account Number', 'Account Balance',
        'Enrolled', 'Card Status',
        'Last Login Time', 'Status', 'Actions'
      ], [], {
        'Create Date': 'u.createdAt',
        'Client Name': 'g.name',
        'Client ID': 'u.id',
        'Client Address': 'CONCAT(u.address,u.addressline)',
        'Contact Name': 'CONCAT(u.firstName,u.lastName)',
        'Email': 'u.email',
        'Funding Type': 'g.fundingType',
        'Phone': 'u.mobilephone'
      }),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'Client ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        }, {
          value: 'filter[u.mobilephone=]',
          label: 'Phone'
        }, {
          value: 'filter[u.status=]',
          label: 'Status',
          options: [
            { label: 'Active', value: 'active' },
            { label: 'Inactive', value: 'inactive' }
          ]
        }, {
          value: 'filter[uc.accountNumber=]',
          label: 'Account Number'
        }, {
          value: 'filter[g.fundingType=]',
          label: 'Funding Type',
          options: [
            // { label: 'ACH', value: 'ACH' },
            { label: 'Prefunded', value: 'Prefunded' }
          ]
        }, {
          value: 'filter[g.name=]',
          label: 'Client Name'
        }
      ],
      dateRange: 'month',
      keyword: '',
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    view (row) {
      const { href } = this.$router.resolve({
        path: `/h/faas/base/clients/${row['Client ID']}`
      })
      window.open(href, '_blank')
    },
    add () {
      this.$root.$emit('show-faas-client-detail-dialog')
    },
    edit (row) {
      this.$root.$emit('show-faas-client-detail-dialog', row)
    },
    async invite (row) {
      this.$q.loading.show()
      const resp = await request(`/admin/faas/base/clients/${row['Client ID']}/invite`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
      }
    },
    del (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to delete this client?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request(`/admin/faas/base/clients/${row['Client ID']}/del`, 'post')
        this.loading = false
        if (resp.success) {
          this.reload()
        }
      }).catch(() => {})
    },
    async refreshAgentBalance (row, field) {
      row.loading = true
      const resp = await request(`/admin/faas/base/clients/${row['Client ID']}/refresh-agent-balance`, 'post', {
        field
      })
      row.loading = false
      if (resp.success) {
        row[field] = resp.data
      }
    },
    settings (row) {
      this.$root.$emit('show-faas-client-settings-dialog', row)
    }
  },
  mounted () {
    if (this.masterAdmin) {
      let index = _.findIndex(this.columns, c => {
        return c.field === 'Account Balance'
      })
      if (index >= 0) {
        for (const field of ['Rapid Agent Balance', 'BOTM Balance']) {
          this.columns.splice(++index, 0, {
            field,
            label: field,
            align: 'left',
            sortLabel: null
          })
        }
      }
    }
  }
}
</script>

<style lang="scss">
.faas_clients_index_page {
  .page-content {
    .top-statics {
      .q-card-main {
        font-size: 17px;
        color: #191d3d;
      }
      .summary-icon {
        height: 50px !important;
        width: 50px !important;
        font-size: 24px;
        color: #4acc3d;
        background: rgba(74, 204, 61, 0.1) !important;
        border-radius: 50px !important;
        margin-right: 10px;
        align-self: center;
      }
      .item-value {
        font-size: 38px;
      }
    }
    .user-id {
      color: #fa6400;
      text-decoration-line: underline;
      cursor: pointer;
    }
    .export-btn {
      background: #190644 !important;
    }
    .table-btn {
      border: 0.5px solid #5a5a89;
      border-radius: 10px;
    }
  }
  .q-table {
    .locked-column {
      position: relative;
    }
    .locked {
      color: #fc5a5a;
      i.mdi-lock-outline {
        margin-right: 5px;
        position: absolute;
        line-height: 20px;
        left: -20px;
      }
    }
  }
  .danger {
    color: #f10013;
    background: rgba($color: #f10013, $alpha: 0.1) !important;
  }
}
</style>
