<template>
  <q-page id="faas__clients__profile_page">
    <div class="row gutter-sm mt-0">
      <div class="col-sm-12 col-md-4">
        <BasicCard :entity="entity"
                   @reload="reload(true, true)"></BasicCard>
      </div>
      <div class="col-sm-12 col-md-4">
        <DetailCard :entity="entity"
                    @reload="reload">
          <template v-slot:otherDialog>
            <DetailDialog> </DetailDialog>
          </template>
        </DetailCard>
      </div>
      <div class="col-sm-12 col-md-4">
        <NotesCard :uid="uid" :url="url"></NotesCard>
      </div>
      <div class="col-12">
        <TransactionsCard :uid="uid" :url="url"></TransactionsCard>
      </div>
    </div>
  </q-page>
</template>

<script>
import FaasPageMixin from '../../../../../mixins/faas/FaasPageMixin'
import BasicCard from '../../../components/card/profile/basic'
import DetailCard from '../../../components/card/profile/detail'
import DetailDialog from '../detail'
import NotesCard from '../../../components/card/profile/notes'
import TransactionsCard from '../../../components/card/profile/transactions'
import { EventHandlerMixin, notifySuccess, request } from '../../../../../common'

export default {
  name: 'faas-clients-profile',
  mixins: [
    FaasPageMixin,
    EventHandlerMixin('reload-faas-clients-profile')
  ],
  components: {
    BasicCard,
    DetailCard,
    NotesCard,
    TransactionsCard,
    // ProfileDetailCard,
    DetailDialog
  },
  data () {
    return {
      title: 'Client Profile',
      entity: {},
      url: 'faas/base/clients'
    }
  },
  computed: {
    uid () {
      return this.$route.params.id
    }
  },
  watch: {
    uid () {
      this.reload()
    }
  },
  methods: {
    async reload (indicator = true, force = false) {
      indicator && this.$q.loading.show()
      const resp = await request(`/admin/faas/base/clients/${this.uid}/profile`, 'get', {
        force
      })
      indicator && this.$q.loading.hide()
      if (resp.success) {
        this.entity = resp.data

        if (force) {
          notifySuccess(resp)
        }
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
  #faas__clients__profile_page {
    .q-card {
      &.high-card {
        height: 473px;
      }

      &.wide-card {
        min-height: 300px;

        > .q-card-main {
          position: relative;
          height: 100%;
        }
      }

      overflow: auto;

      > .q-card-primary {
        padding-top: 16px;
      }

      > .q-card-main {
        position: relative;
        height: calc(100% - 67px);
      }
    }

    .q-card-title .font-18.bold {
      height: 33px;
      padding-top: 3px;
    }

    .fields-table {
      width: 100%;

      th {
        padding: 5px 5px 5px 0;
        font-weight: normal;
        font-size: 13px;
        color: #888;
        text-align: left;
        min-width: 100px;
        vertical-align: top;
      }

      td {
        padding: 5px 0 5px 5px;
        text-align: right;
        word-break: break-word;
      }
    }

    .empty-tip {
      height: 100%;
      color: #666;

      .q-icon {
        margin-bottom: 5px;
        font-size: 20px;
      }
    }
  }
</style>
