<template>
  <q-dialog class="faas-clients-settings-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2"
           v-if="$store.state.User.cpKey === 'cp_faas'">Client Settings</div>
      <div class="font-16 mb-2"
           v-if="$store.state.User.cpKey === 'cp_mex'">Employer Settings</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <q-field v-if="$store.state.User.cpKey === 'cp_faas'"
               label="Logo"
               label-width="4"
               helper="Click on '+' button to select the file to upload. Will be applied to client portal and member portal.">
        <q-uploader url="/attachments"
                    name="logo"
                    auto-expand
                    ref="uploader_Logo"
                    @add="$refs.uploader_Logo && $refs.uploader_Logo.upload()"
                    @uploaded="(file, xhr) => uploadedAttachment('Logo', xhr)"
                    extensions=".gif,.jpg,.jpeg,.png"
                    :additional-fields="[{name: 'category', value: 'public'}]"
                    clearable> </q-uploader>
      </q-field>
      <div class="row gutter-sm"
           v-if="entity['Logo'] && $store.state.User.cpKey === 'cp_faas'">
        <div class="offset-3 col text-center">
          <img :src="entity['Logo']"
               alt=""
               class="client-logo">
          <div class="text-right q-mb-lg">
            <q-btn label="Remove"
                   flat
                   @click="removeAttachment('Logo')"
                   icon="mdi-close"
                   color="negative"
                   size="sm"> </q-btn>
          </div>
        </div>
      </div>
      <!-- <q-field label="Subtitle"
               label-width="4"
               helper="The subtitle displayed on the login page.">
        <q-input type="textarea"
                 v-model="entity['clientTitle']"></q-input>
      </q-field> -->
      <q-field v-if="$store.state.User.cpKey === 'cp_faas'"
               label="Referer"
               label-width="4"
               helper="Regular expression of the referer URL. The login page will show the above logo instead when navigated from the URLs that matched the expression.">
        <q-input v-model="entity['Referer']"></q-input>
      </q-field>
      <q-field label="Institution Name"
               :readonly="botm"
               label-width="4">
        <q-input v-model="entity['Institution Name']"></q-input>
      </q-field>
      <q-field label="Institution Address"
               label-width="4">
        <q-input v-model="entity['Institution Address']"></q-input>
      </q-field>
      <q-field label="Account Number"
               label-width="4">
        <q-input v-model="entity['Account Number']"
                 :readonly="botm"></q-input>
      </q-field>
      <q-field label="Routing Number"
               label-width="4">
        <q-input v-model="entity['Routing Number']"
                 :readonly="botm"></q-input>
      </q-field>
      <p class="mt-20 text-left text-faded font-12" v-if="botm">
        The account number & routing number are synced from BOTM's business account details.
        <a href="javascript:" @click="syncBotm" class="ml-10">Update now</a>
      </p>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel"
             no-caps
             color="grey-3"
             text-color="tertiary"
             @click="_hide" />
      <q-btn :label="'Save Changes'"
             no-caps
             color="positive"
             class="main"
             @click="save" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notify, notifyResponse, request } from '../../../../common'
// import { required } from 'vuelidate/lib/validators'
import MexStateListMixin from '../../../../mixins/mex/MexStateListMixin'
import _ from 'lodash'

export default {
  name: 'faas-client-settings-dialog',
  mixins: [
    Singleton,
    MexStateListMixin
  ],
  data () {
    return {
      entity: {
        'Logo': null,
        'Referer': null,
        'Institution Name': '',
        'Institution Address': '',
        'Account Number': '',
        'Routing Number': ''
        // 'clientTitle': ''
      }
    }
  },
  computed: {
    botm () {
      return this.entity && this.entity['Card Provider'] === 'BOTM'
    }
  },
  // validations: {
  //   entity: {
  //     'Logo': { required }
  //   }
  // },
  methods: {
    show (data) {
      this.entity = data
      this.reload()
    },
    async reload () {
      if (this.$store.state.User.cpKey === 'cp_mex') {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/employer/setting/load`, 'get', {
          'EmployerID': this.entity['Employer ID']
        })
        this.$q.loading.hide()
        if (resp.success) {
          _.assignIn(this.entity, resp.data)
        }
      }
    },
    uploadedAttachment (field, xhr) {
      const resp = JSON.parse(xhr.response)
      const file = resp.data['logo']
      this.entity[field] = file.url
      this.entity[`${field}_id`] = file.id
      delete this.entity[`${field}_delete`]
      this.$refs[`uploader_${field}`].reset()
    },
    removeAttachment (field) {
      this.entity[field] = null
      this.entity[`${field}_delete`] = true
    },
    async save () {
      // if (this.$v.$invalid) {
      //   this.$v.$touch()
      //   return notifyForm()
      // }
      this.$q.loading.show()
      let resp = null
      if (this.$store.state.User.cpKey === 'cp_mex') {
        resp = await request(`/admin/mex/employer/setting`, 'post', {
          'Employer ID': this.entity['Employer ID'],
          'Institution Name': this.entity['Institution Name'],
          'Institution Address': this.entity['Institution Address'],
          'Account Number': this.entity['Account Number'],
          'Routing Number': this.entity['Routing Number']
        })
      } else {
        resp = await request(`/admin/faas/base/clients/setLogo`, 'post', {
          'clientLogo': this.entity['Logo'],
          // 'clientTitle': this.entity['clientTitle'],
          'Client ID': this.entity['Client ID'],
          'Referer': this.entity['Referer'],
          'Institution Name': this.entity['Institution Name'],
          'Institution Address': this.entity['Institution Address'],
          'Account Number': this.entity['Account Number'],
          'Routing Number': this.entity['Routing Number']
        })
      }
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.$root.$emit('reload-faas-clients')
        this.$root.$emit('reload-mex-employers')
        this._hide()
      } else {
        notifyResponse(resp.message)
      }
    },
    async syncBotm () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/employer/setting/sync-botm`, 'post', {
        'Employer ID': this.entity['Employer ID']
      })
      this.$q.loading.hide()
      if (resp.success) {
        _.assignIn(this.entity, resp.data)
      }
    }
  }
}
</script>

<style lang="scss">
.faas-clients-settings-dialog {
  .modal-content {
    width: 580px;
    .client-logo {
      max-height: 120px;
    }
    .q-field {
      margin-bottom: 10px;
    }
  }
}
</style>
