<template>
  <q-card id="kycPerformance"
          class="mt-16">
    <q-card-title>
      <div class="row">
        <div class="chart-title">
          <p>KYC Performance</p>
          <span>{{delta}} Passed</span>
          <p class="total-area">Out of {{total}}</p>
        </div>
        <q-btn class="graph-btn down-btn btn-sm ml-auto mr-8 square"
               @click="download()"
               icon="mdi-file-document-outline"></q-btn>
        <!-- <q-btn class="kyc-performance-btn report-btn btn-sm square"
               @click="viewReport()"
               icon="mdi-chart-line"></q-btn> -->
      </div>
    </q-card-title>
    <q-card-main class="chart-item">
      <div id="kycPerformanceChart"></div>
      <div class="kycPerformanceList">
        <p v-for="(item,i) in typeList"
           :key="i">
          <i :style="'backgroundColor:' + colorList[i] + ';width:10px; height: 10px;border-radius:5px;display: inline-block;margin-right: 5px;'"></i>{{item}}
        </p>
      </div>
      <div v-if="visible"
           class="loading">
        <q-spinner-hourglass color="primary"
                             size="3em"
                             :thickness="2" />
      </div>
    </q-card-main>
  </q-card>
</template>

<script>
import echarts from 'echarts'
import $ from 'jquery'
import { request, EventHandlerMixin, notify } from '../../../../common'
import html2canvas from 'html2canvas'
import moment from 'moment'

export default {
  props: {
    'value': {
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  watch: {
    value () {
      this.getData()
    }
  },
  mixins: [
    EventHandlerMixin('reload-kyc-performance-chart', 'getData')
  ],
  data () {
    return {
      delta: 0,
      visible: false,
      chart: null,
      total: 0,
      startTime: moment().startOf('day'),
      endTime: moment().endOf('day'),
      typeList: [
        'OFAC',
        'Scan Verify',
        'Manual Approval'
      ],
      series: {
        coordinateSystem: 'polar',
        name: 'qwertfg',
        itemStyle: {
        },
        roundCap: true,
        showBackground: '#fff',
        labelLine: {
          show: false
        },
        barMaxWidth: '20px',
        type: 'bar',
        data: []
      },
      colorList: [
        '#7a62f9',
        '#ff9ad5',
        '#0061ff'
      ],
      dataList: []
    }
  },
  async mounted () {
    this.getData()
  },
  methods: {
    resize () {
      this.chart.resize()
    },
    // viewReport () {
    //   this.$router.push(`/j/mex/members`)
    // },
    async getData () {
      this.visible = true
      switch (this.value) {
        case 'all':
          this.startTime = null
          this.endTime = null
          break
        case 'today':
          this.startTime = moment().startOf('day')
          this.endTime = moment().endOf('day')
          break
        case 'week':
          this.startTime = moment().startOf('week')
          this.endTime = moment().endOf('week')
          break
        case 'month':
          this.startTime = moment().startOf('month')
          this.endTime = moment().endOf('month')
          break
        case 'quarter':
          this.startTime = moment().startOf('quarter')
          this.endTime = moment().endOf('quarter')
          break
        case 'year':
          this.startTime = moment().startOf('year')
          this.endTime = moment().endOf('year')
          break
      }
      let that = this
      const resp = await request(`/admin/faas/dashboard/kycChart`, 'get', {
        'period': this.value,
        'start': this.startTime ? moment(this.startTime).format('L') : null,
        'end': this.startTime ? moment(this.endTime).format('L') : null
      })
      if (resp.success) {
        this.series.data = resp.data.data
        this.total = resp.data.total
        this.delta = resp.data.pass
        this.dataList = resp.data
        this.series.itemStyle = {
          color: function (params) {
            return that.colorList[params.dataIndex]
          }
        }
        if (!this.$store.state.User.KYCRequired) {
          this.typeList = [
            'OFAC',
            'Manual Approval'
          ]
        }
        this.initChart()
      }
      this.visible = false
    },
    initChart () {
      this.chart = echarts.init(document.getElementById('kycPerformanceChart'), 'primary')
      let that = this
      let option = {
        tooltip: {
          backgroundColor: '#ffffff',
          padding: 0,
          // alwaysShowContent: true,
          formatter: function (params) {
            // console.log(params)
            let str = '<div style="box-shadow: 0 5px 15px 0 rgba(28, 20, 70, 0.1); min-width: 150px;border-radius:4px;padding:15px;padding-top:16px;font-family: Poppins;font-size: 14px;"><p style="text-align:center;margin:0px;color:#231f20;font-weight: 600;">' + params.name + '</p>'
            // console.log(params.name)
            if (params.name === 'Manual Approval') {
              str += '<p style="display:flex; justify-content:space-between;margin:0px;color:#231f20;"><span style="display: inline-block;font-weight: 600;">Passed</span><span  style="display: inline-block; color: #696975;font-weight: 600;text-align: right;font-family: monospace;">' + that.dataList[params.name] + '</span></p></div>'
            } else {
              str += '<p style="display:flex; justify-content:space-between;margin:0px;color:#231f20;"><span style="display: inline-block;font-weight: 600;">Passed</span><span  style="display: inline-block; color: #696975;font-weight: 600;text-align: right;font-family: monospace;">' + that.dataList[params.name]['pass'] + '</span></p>' +
              '<p style="display:flex; justify-content:space-between;text-align:left;margin:0px;color:#231f20;"><span style="display: inline-block;font-weight: 600;">Failed</span><span style="display: inline-block; color: #696975;font-weight: 600;text-align: right;font-family: monospace;">' + that.dataList[params.name]['failed'] + '</span></p></div>'
            }
            return str
          }
        },
        angleAxis: {
          max: 100,
          startAngle: 90,
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitNumber: 20
        },
        radiusAxis: {
          type: 'category',
          data: this.typeList,
          z: 10,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          minorTick: {
            show: false
          }
        },
        xAxis: {
          show: false
        },
        yAxis: {
          show: false
        },
        polar: {
        },
        grid: {
          left: '0',
          right: '0',
          bottom: '0',
          top: '0'
        },
        series: this.series
      }
      // console.log(option)
      this.chart.setOption(option)

      $(window).on('resize', this.resize)
    },
    download () {
      let userAgent = navigator.userAgent
      let name = 'kycPerformance'
      if (!this.chart) {
        return notify('Please wait for the chart to load!', 'negative')
      }
      if (userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Chrome') === -1) {
        return notify('Please take a screenshot manually or use another browser to open the website to save the picture!', 'negative')
      }
      this.$q.loading.show()
      if (userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Chrome') === -1) {
        let temp = this.chart.getDataURL({
          pixelRatio: 2,
          backgroundColor: '#fff'
        })
        this.fileDownload(temp, name)
        this.$q.loading.hide()
      } else {
        let target = null
        let name = 'kycPerformance'
        target = document.getElementById('kycPerformance')
        this.$q.loading.show()
        const targetCss = window.getComputedStyle(target)

        const options = {
          scale: 2,
          width: parseInt(targetCss.width, 10),
          height: parseInt(targetCss.height, 10),
          y: target.offsetTop + 90
        }
        console.log(options)
        // console.log(options)
        html2canvas(target, options).then(canvas => {
        // canvas is the final rendered <canvas> element
          const imgData = canvas.toDataURL('image/jpeg')
          // console.log(imgData)
          this.fileDownload(imgData, name)
          this.$q.loading.hide()
        })
      }
    },
    fileDownload (downloadUrl, filename) {
      let aLink = document.createElement('a')
      aLink.style.display = 'none'
      aLink.href = downloadUrl
      aLink.download = `${filename}`
      // 触发点击-然后移除
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    }
  }
}
</script>
<style lang="scss">
#kycPerformance {
  .chart-title {
    p {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
    span {
      font-size: 28px;
      font-weight: 600;
    }
  }
  .total-area {
    margin: 0;
    font-size: 7px;
    color: #787393;
    line-height: 7px;
  }
  .kyc-performance-btn {
    color: #fff;
    max-height: 32px;
    max-width: 32px;
  }
  .report-btn {
    background: #0062ff;
  }
  .down-btn {
    background: #005daa;
  }

  #kycPerformanceChart {
    height: 272px;
  }
  .chart-item {
    position: relative;
  }
  .loading {
    height: 300px;
    line-height: 300px;
    width: calc(100% - 32px);
    text-align: center;
    position: absolute;
    top: 0;
    z-index: 100;
    background: rgba($color: #000000, $alpha: 0.1);
  }
  .kycPerformanceList {
    max-height: 60px;
    display: flex;
    justify-content: center;
    overflow: scroll;
    p {
      margin: 0 5px;
    }
  }
}
</style>
