<template>
  <q-page class="faas_dashboard_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Organization"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-md-3 col-sm-6 col-xs-12"
             v-for="(n, i) in numbers"
             :key="i">
          <q-card class="top-statics">
            <!-- <q-card-title>
              <span>{{n.title}}</span>
            </q-card-title> -->
            <q-card-main>
              <div class="row">
                <q-icon class="summary-icon members-icon"
                        v-if="n.title ==='Active Member'"><img src="/static/faas/img/summary_accounts.svg"></q-icon>
                <q-icon class="summary-icon commissions-icon"
                        v-else-if="n.title ==='Commissions'"><img src="/static/faas/img/summary_commissions.svg"></q-icon>
                <q-icon class="summary-icon transfers-icon"
                        v-else-if="n.title ==='Transfers'"><img src="/static/faas/img/summary_transfers.svg"></q-icon>
                <q-icon class="summary-icon cards-icon"
                        v-else-if="n.title ==='Active Cards'"><img src="/static/faas/img/summary_cards.svg"></q-icon>
                <div>
                  <div>{{n.title}}</div>
                  <div class="item-value heavy">
                    {{ n.value}}
                    <q-chip dense
                            :class="n.delta >= 0 ? 'positive' : 'negative'">
                      <q-icon v-if="n.delta > 0"
                              class="mdi mdi-trending-up"></q-icon>
                      <q-icon v-if="n.delta < 0"
                              class="mdi mdi-trending-down"></q-icon>{{ n.delta | percent(1, true) }}
                    </q-chip>
                  </div>
                  <div v-if="n.title !== 'Active Member'"
                       class="font-16 item-count mb-10">{{ n.count }} {{n.sub}}</div>
                  <div v-else
                       class="font-16 item-count mb-10">{{n.sub}}{{ n.count | moneyFormat }} </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <div class="row"
           v-if="chartVisible('partnerBalance')">
        <BalanceGraph v-model="chartDateRange"
                      title="Partner Balance"
                      :chartSetting="chartSettingList('partnerBalance')"
                      chartId="partnerBalance"></BalanceGraph>
      </div>
      <div class="row"
           v-if="chartVisible('programBalance')">
        <BalanceGraph v-model="chartDateRange"
                      :chartSetting="chartSettingList('programBalance')"
                      title="Program Balance"
                      chartId="programBalance"></BalanceGraph>
      </div>
      <div class="row">
        <div v-if="chartVisible('memberBreakdown')"
             class="col-md-8 col-sm-6 col-xs-12">
          <Member v-model="chartDateRange"></Member>
        </div>
        <div v-if="chartVisible('kycPerformance')"
             class="col-md-4 col-sm-6 col-xs-12">
          <KYCPerformance v-model="chartDateRange"></KYCPerformance>
        </div>
      </div>
    </div>
  </q-page>
</template>
<script >
import FaasPageMixin from '../../../../mixins/faas/FaasPageMixin'
import BalanceGraph from '../common/balanceGraph'
import { request } from '../../../../common'
import KYCPerformance from '../common/kycPerformance'
import Member from '../common/members.vue'

export default {
  name: 'Faas-Dashboard',
  mixins: [
    FaasPageMixin
  ],
  components: {
    BalanceGraph,
    KYCPerformance,
    Member
  },
  data () {
    return {
      title: 'Dashboard',
      dateRanges: [
        {
          label: 'Month',
          value: 'month'
        },
        {
          label: 'Week',
          value: 'week'
        },
        {
          label: 'All',
          value: 'all'
        },
        {
          label: 'Custom',
          value: 'custom_range'
        }
      ],
      dateRange: 'month',
      chartDateRange: 'month',
      numbers: [
      ],
      chartSetting: null
    }
  },
  methods: {
    chartSettingList (type) {
      if (!this.chartSetting) {
        return []
      }
      var item = this.chartSetting[type]
      if (!item || item.visible === undefined) {
        return []
      }
      return item
    },
    chartVisible (type) {
      // console.log(this.chartSetting)
      if (!this.chartSetting) {
        return false
      }
      // console.log('asdfgytrew')
      var item = this.chartSetting[type]
      if (!item || item.visible === undefined) {
        return false
      }
      return item.visible
    },
    async reload () {
      this.$q.loading.show()
      let data = {}
      if (this.$refs && this.$refs.dateRangeFilter) {
        data = this.$refs.dateRangeFilter.params()
      }
      this.chartDateRange = data.period
      const resp = await request('/admin/faas/dashboard/static/data', 'get', data)
      this.$q.loading.hide()
      if (resp) {
        this.numbers = resp.data
      }
    },
    async getGraphSetting () {
      this.$q.loading.show()
      const resp = await request('/admin/faas/dashboard/graphSetting')
      this.$q.loading.hide()
      if (resp) {
        this.chartSetting = resp.data
        this.$nextTick(() => {})
      }
    }
  },
  async mounted () {
    await this.reload()
    this.$nextTick(() => {
      this.getGraphSetting()
    })
  }
}
</script>
<style lang="scss">
.faas_dashboard_page {
  .top-statics {
    .q-card-main {
      font-size: 20px;
      color: #191d3d;
    }
    .summary-icon {
      height: 50px !important;
      width: 50px !important;
      font-size: 24px;
      margin-top: 20px !important;
      border-radius: 50px !important;
      margin-right: 10px;
    }
    .members-icon {
      background: rgba(#0062ff, 0.1) !important;
    }
    .transfers-icon {
      background: rgba(#ff9f00, 0.1) !important;
    }
    .cards-icon {
      background: rgba(#8676ff, 0.1) !important;
    }
    .commissions-icon {
      background: rgba(#4acc3d, 0.1) !important;
    }
    .item-value {
      font-size: 38px;
    }
    .item-count {
      color: #5a5a89;
    }
  }
  .graph-btn {
    max-height: 55px !important;
    width: 55px;
    color: #fff;
    .q-icon {
      font-size: 24px;
    }
  }
  .upload-btn {
    background: #005daa;
  }
  .down-chart-btn {
    background: #005daa;
  }
}
</style>
