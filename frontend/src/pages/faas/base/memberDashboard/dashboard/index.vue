<template>
  <q-page class="faas__member_dashboard__index_page">
    <div class="page-header">
      <div class="fun-group">
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-md ml-26">
        <div class="col-sm-6 balance">
          <q-card class="pt-30 pb-30 pl-20 pr-20">
            <q-card-title class="">
              <span class="font-28">Card Balance</span>
            </q-card-title>
            <q-card-main class="text-center">
              <div class="text-center mt-10 mb-10 card-img-container">
                <div class="account-image">
                  <img class=""
                       v-if="this.user.clientLogo"
                       :src="this.user.clientLogo">
                  <img class=""
                       v-else-if="this.user.platformLogoUrl"
                       :src="this.user.platformLogoUrl">
                  <div class="text-white label-span">**** **** **** {{ pan }}</div>
                </div>
                <div class="current-balance text-left"
                     v-if="card">
                  <div class="font-16 text-white">Current Balance</div>
                  <div class="font-28 text-white bold">{{ card.balance | moneyFormat }}</div>
                </div>
              </div>
              <div class="action mb-30"
                   v-if="this.userInfo">
                <div v-if="!enrolled"
                     class="item">
                  <q-input v-model="accountNumber"
                           class="mt-5"
                           placeholder="Enter your Card # "></q-input>
                  <q-btn no-caps
                         color="primary"
                         class="mt-15 btn-button"
                         @click="activate"
                         label="Activate Card"></q-btn>
                </div>
                <div v-if="enrolled"
                     class="item">
                  <q-btn no-caps
                         color="primary"
                         class="mt-15 btn-button"
                         @click="suspendCard"
                         :label="active ? 'Suspend My Card' : 'Unsuspend My Card'"></q-btn>
                </div>
              </div>
              <div class="font-28 text-left bold">Spending Breakdown</div>
              <div class="font-16 text-left light-grey">Breakdown of spending based on the following categories</div>
              <div class="list-container">
                <q-list no-border
                        class="pt-0"
                        inset-separator
                        v-if="spendingBreakdown.length > 0">
                  <q-item link
                          class="pt-20 pb-20"
                          v-for="(item, key) in spendingBreakdown"
                          :key="key">
                    <q-item-side class="icon font-20">
                      <q-icon :name="item.icon"> </q-icon>
                    </q-item-side>
                    <q-item-main>
                      <q-item-tile label
                                   class="font-16 bold mb-5">{{ item.title }}</q-item-tile>
                      <q-item-tile sublabel
                                   class="font-14 light-grey">{{ item.count }} {{ item.count > 1 ? 'Transactions' : 'Transaction'}}</q-item-tile>
                    </q-item-main>
                    <q-item-side class="text-right">
                      <div class="font-14 fee text-black bold">{{ item.amount | moneyFormat }}</div>
                      <div class="light-grey font-12">{{ item.percent | percent(0) }} of Spending</div>
                    </q-item-side>
                  </q-item>
                </q-list>
                <div class="no-data text-left mt-15"
                     v-else>
                  <p>There are currently no spending breakdown.</p>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-6 transactions">
          <q-card class="pt-30 pb-30 pl-20 pr-20">
            <q-card-title class="">
              <span class="font-28">My Transactions</span>
            </q-card-title>
            <q-card-main>
              <q-search icon="search"
                        v-model="keyword"
                        type="text"
                        class="search mt-10 mb-20"
                        @keydown.13="search"
                        @clear="search"
                        clearable
                        hide-underline
                        placeholder="Search for transactions">
              </q-search>
              <div class="list-container">
                <q-list no-border
                        class="pt-0"
                        inset-separator
                        v-if="transactions.length > 0">
                  <q-item link
                          class="pt-20 pb-20"
                          v-for="(item, key) in transactions"
                          :key="key">
                    <q-item-side class="text-center light-grey mr-14">
                      <div class="font-12 month bold">{{ item.time | date('MMM') }}</div>
                      <div class="font-12 bold">{{ item.time | date('DD') }}</div>
                    </q-item-side>
                    <q-item-side class="font-20 mr-14">
                      <div class="icon-contain text-center"
                           :class="item.amount >= 0 ? 'up' : 'down'">
                        <q-icon name="mdi-arrow-top-right"
                                v-if="item.amount >= 0"> </q-icon>
                        <q-icon name="mdi-arrow-bottom-left"
                                v-else> </q-icon>
                      </div>
                    </q-item-side>
                    <q-item-main>
                      <q-item-tile label
                                   class="font-16 bold mb-5">{{ item.description }}</q-item-tile>
                      <q-item-tile sublabel
                                   class="font-14 light-grey">{{ item.status }}</q-item-tile>
                    </q-item-main>
                    <q-item-side class="text-right">
                      <div class="font-14 fee bold text-black">{{ item.amount | moneyFormat }}</div>
                      <div class="light-grey font-12">{{ item.balance }}</div>
                    </q-item-side>
                  </q-item>
                </q-list>
                <div class="no-data"
                     v-else>
                  <p>There are currently no recent transactions.</p>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>
<script>
import FaasPageMixin from '../../../../../mixins/faas/FaasPageMixin'
import { notify, notifyForm, request } from '../../../../../common'
import moment from 'moment'

export default {
  name: 'Faas-Member-Dashboard',
  mixins: [
    FaasPageMixin
  ],
  components: {

  },
  data () {
    return {
      dateRange: 'month',
      keyword: null,
      info: [],
      card: [],
      spendingBreakdown: [],
      transactions: [],
      accountNumber: null,
      userInfo: {}
    }
  },
  computed: {
    enrolled () {
      const userInfo = this.userInfo
      return userInfo.id && userInfo.enrolled && ['Active'].includes(userInfo.registerStep)
    },
    active () {
      return this.card && this.card.status === 'active'
    },
    pan () {
      const pan = this.card && this.card.maskedCardNumber ? this.card.maskedCardNumber : '****'
      return pan.substring(pan.length - 4)
    }
  },
  async mounted () {
    this.reload()
  },
  methods: {
    async reload () {
      this.$q.loading.show()
      let data = {}
      if (this.$refs && this.$refs.dateRangeFilter) {
        data = this.$refs.dateRangeFilter.params()
        data.fromDate = moment(data.start || '2021-01-01').format('YYYY-MM-DD')
        data.toDate = moment(data.end || '2038-01-01').format('YYYY-MM-DD')
      }
      data.keywords = this.keyword ? this.keyword : null
      const resp = await request('/admin/faas/base/member/home', 'get', data)
      this.$q.loading.hide()
      if (resp && resp.data) {
        this.info = resp.data
        this.card = resp.data.card
        this.spendingBreakdown = resp.data.spendingBreakdown ? resp.data.spendingBreakdown.map((m, i) => {
          m.icon = this.spendingBreakdownIcons[m.icon] || 'mdi-star-outline'
          return m
        }) : []
        this.transactions = resp.data.transactions
        this.userInfo = resp.data.user
      }
    },
    search () {
      this.$nextTick(() => {
        setTimeout(() => {
          this.reload()
        }, 300)
      })
    },
    async activate () {
      if (!this.accountNumber) {
        notifyForm()
        return
      }
      this.$q.loading.show()
      const resp = await request(`/admin/faas/base/member/enroll`, 'post', {
        accountNumber: this.accountNumber
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
        await this.reload()
      }
    },
    suspendCard () {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to ${this.active ? 'Suspend' : 'Unsuspend'} the card?`,
        color: this.active ? 'negative' : 'positive',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/faas/base/member/suspend`, 'post', {
          maskedCardNumber: this.card.maskedCardNumber,
          status: this.card.status === 'active' ? 'Suspended' : 'Active'
        })
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp)
          await this.reload()
        }
      }).catch(() => {})
    }
  }
}
</script>
<style lang="scss">
@font-face {
  font-family: "OCR A Extended";
  src: url("https://account.usunlocked.com/static/usu/ocraext.ttf")
    format("truetype");
}

.faas__member_dashboard__index_page {
  .balance {
    .list-container {
      max-height: 260px;
      overflow-y: scroll;
    }
  }
  .transactions {
    .q-item-section + .q-item-section {
      margin-left: 18px !important;
    }
    /*.q-card {*/
    /*  height: 768px;*/
    /*}*/
    .list-container {
      max-height: 560px;
      overflow-y: scroll;
    }
  }
  .month {
    text-transform: uppercase;
  }
  .account-image {
    height: 250px;
    background: var(--q-color-primary);
    border-radius: 30px;
    text-align: left;
    img {
      max-height: 46px;
      margin-top: 20px;
      margin-left: 20px;
    }
  }
  .light-grey {
    color: #8e8e93;
  }
  .search {
    background-color: #fff;
    max-width: 500px;
    border-color: #cccccc;
    border-radius: 30px;
    padding: 12px !important;
  }
  .icon {
    .q-icon {
      width: 40px;
      height: 40px;
      background: #eaeaea;
      border-radius: 50%;
      color: #1c1c1e;
      font-size: 20px;
    }
  }
  .icon-contain {
    width: 40px;
    height: 40px;
    line-height: 40px;
    background-color: #34c759;
    border-radius: 50%;
    .q-icon {
      border: 1px solid #fff;
      border-radius: 50%;
      padding: 2px;
      font-size: 14px;
      background-color: #34c759;
      color: #fff;
      vertical-align: unset;
    }
  }
  .icon-contain.down {
    background-color: #ff3b30;
    .q-icon {
      background-color: #ff3b30;
    }
  }
  .card-img-container {
    position: relative;
    display: inline-block;
    width: 80%;
    max-width: 425px;
    .current-balance {
      position: absolute;
      bottom: 20px;
      left: 20px;
    }
  }
  .btn-button {
    background: var(--span-color-primary) !important;
    border-radius: 50px;
  }
  .action {
    .item {
      text-align: center;
      margin: auto 22%;
    }
  }
  .label-span {
    font-size: 24px;
    margin-left: 20px;
    margin-top: 34px;
    font-family: "OCR A Extended", "Rubik", "Helvetica Neue", Helvetica, Arial,
      sans-serif;
  }
}
</style>
