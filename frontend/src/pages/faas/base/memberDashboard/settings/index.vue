<template>
  <q-page class="faas__member_settings__index_page">
    <div class="page-content">
      <div class="row gutter-sm ml-26">
        <div class="col-sm-4">
          <q-card class="account-settings">
            <q-card-title>
              <span class="font-28">Account Settings</span>
            </q-card-title>
            <q-card-main>
              <div class="font-16 bold mt-15 mb-20">
                Change Avatar
              </div>
              <div class="account-box">
                <q-btn flat
                       round
                       class="p-0 btn-avatar mr-10">
                  <div class="avatar"
                       :style="{backgroundImage: `url(${avatar || '/static/img/avatar2.png'})`}"></div>
                </q-btn>
                <q-btn label="Upload New Photo"
                       @click="select"
                       color="primary"
                       class="btn-sm font-16 btn-upload"
                       no-caps> </q-btn>
                <input type="file"
                       class="hide"
                       accept="image/*"
                       ref="file"
                       @change="selectedFile">
              </div>
              <div class="font-16 bold mt-20 mb-30">
                Personal Details
              </div>
              <q-input float-label="First Name"
                       class="mb-30"
                       v-model="firstName"
                       readonly></q-input>
              <q-input float-label="Last Name"
                       class="mb-30"
                       v-model="lastName"
                       readonly></q-input>
              <q-input float-label="Phone Number"
                       class="mb-30"
                       v-model="phoneNumber"
                       readonly></q-input>
              <q-input float-label="Billing/Shipping Address"
                       class="mb-15"
                       v-model="address"
                       readonly></q-input>
              <div class="light-grey font-12 text-center">
                If your information is incorrect please email your updating information to Customer Support.
              </div>
              <div class="text-center mt-50 font-12 underline light-grey">
                <span @click="closeMyAccount"
                      class="cursor-pointer">
                  Close My Account
                </span>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-4">
          <q-card class="mb-16 security-settings">
            <q-card-title>
              <span class="font-28">Security Settings</span>
            </q-card-title>
            <q-card-main>
              <div class="font-16 bold mt-15 mb-5">
                Two-Factor Authentication
              </div>
              <div class="font-12 light-grey">
                Use a secondary device to confirm a one-time code to keep your account safe.
              </div>
              <q-btn :label="twoFAEnabled ? 'Turn Off' : 'Set Up 2FA'"
                     @click="set2FA"
                     color="primary"
                     class="btn-sm font-16 btn-upload mt-10"
                     no-caps></q-btn>
            </q-card-main>
          </q-card>
          <q-card class="customer-support"
                  v-if="user.customerSupportPhone || user.customerSupportEmail">
            <q-card-title>
              <span class="font-28">Customer Support</span>
            </q-card-title>
            <q-card-main>
              <div class="font-12 light-grey mb-20">
                Our customer support is available Monday thru Friday 9am - 6pm.
              </div>
              <q-list class="actions-list"
                      no-border
                      separator>
                <q-item link
                        @click.native="phone"
                        class="pt-20 pb-20">
                  <q-item-side icon="mdi-cellphone"></q-item-side>
                  <q-item-main>
                    <q-item-tile label>Phone</q-item-tile>
                  </q-item-main>
                  <q-item-side icon="mdi-chevron-right"
                               right></q-item-side>
                </q-item>
                <q-item link
                        @click.native="email"
                        class="pt-20 pb-20">
                  <q-item-side icon="mdi-email-outline"></q-item-side>
                  <q-item-main>
                    <q-item-tile label>Email</q-item-tile>
                  </q-item-main>
                  <q-item-side icon="mdi-chevron-right"
                               right></q-item-side>
                </q-item>
              </q-list>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-4">
          <q-card class="card-history">
            <q-card-title>
              <span class="font-28">Card History</span>
            </q-card-title>
            <q-card-main>
              <q-list no-border
                      class="pt-0"
                      inset-separator
                      v-if="cardHistory.length > 0">
                <q-item link
                        class="pt-20 pb-20"
                        v-for="(item, key) in cardHistory"
                        :key="key">
                  <q-item-side class="icon font-20">
                    <q-icon name="mdi-credit-card"> </q-icon>
                  </q-item-side>
                  <q-item-main>
                    <q-item-tile label
                                 class="font-16 bold">Ending in {{ item.pan }}</q-item-tile>
                    <!--                    <q-item-tile sublabel class="font-14 light-grey mt-5">{{ item.desc }}</q-item-tile>-->
                  </q-item-main>
                  <q-item-side class="text-right">
                    <div class="font-14 fee text-black bold">
                      {{ item.balance > 0 ? '+' : '-' }} {{ item.balance | moneyFormat }}
                    </div>
                    <div class="font-12">
                      <q-chip dense
                              :class="item.status === 'Active' ? 'positive' : 'negative'">
                        {{ item.status }}
                      </q-chip>
                    </div>
                  </q-item-side>
                </q-item>
              </q-list>
              <div class="no-data text-left mt-15"
                   v-else>
                <p>There are currently no card history.</p>
              </div>
              <!-- <div class="">
                <q-btn label="Replace Card"
                       @click="replaceCard"
                       color="primary"
                       class="btn-sm font-16 btn-upload mt-10"
                       no-caps> </q-btn>
              </div> -->
            </q-card-main>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>
<script>
import FaasPageMixin from '../../../../../mixins/faas/FaasPageMixin'
import {
  EventHandlerMixin,
  notify,
  notifyResponse,
  request,
  uploadAttachment
} from '../../../../../common'
import $ from 'jquery'

export default {
  name: 'Faas-Member-Settings',
  mixins: [
    FaasPageMixin,
    EventHandlerMixin('reload-faas-member-setting')
  ],
  data () {
    return {
      firstName: null,
      lastName: null,
      phoneNumber: null,
      address: null,
      avatar: null,
      twoFAEnabled: false,
      info: [],
      cardHistory: [],
      file: null,
      attachment: null
    }
  },
  watch: {
    file () {
      this.attachment = null
    }
  },
  async mounted () {
    this.reload()
  },
  methods: {
    async reload () {
      this.cardHistory = []
      this.file = null
      this.$q.loading.show()
      const resp = await request('/admin/faas/base/member/profile', 'get')
      this.$q.loading.hide()
      if (resp) {
        this.info = resp.data
        this.firstName = resp.data.firstName
        this.lastName = resp.data.lastName
        this.mobile = resp.data.mobile
        this.address = resp.data.address
        this.avatar = resp.data.avatar
        this.twoFAEnabled = resp.data.twoFAEnabled

        if (this.info.cardHistory.length) {
          this.cardHistory = this.info.cardHistory.map(item => {
            const pan = item.maskedCardNumber || '****'
            return {
              balance: item.availableBalance || 0,
              pan: pan.substring(pan.length - 4),
              status: item.status,
              detail: item
            }
          })
        }
      }
    },
    async set2FA () {
      const text = this.twoFAEnabled ? 'turn off' : 'turn on'
      const turnOnNotifyText = 'And when you log in to the system again, the Two-factor authentication process will be performed.'
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to ${text} Two-factor authentication?
        ${turnOnNotifyText}`,
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/faas/base/member/set2FA`, 'post', {
          'enable': !this.twoFAEnabled
        })
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp.message)
          this.reload()
        } else {
          notifyResponse(resp.message)
        }
      }).catch(() => {})
    },
    select () {
      if (this.file) {
        return
      }
      $(this.$refs.file).click()
    },
    selectedFile () {
      const input = this.$refs.file
      if (input.files.length) {
        this.file = input.files[0]
      }
      input.type = 'text'
      input.type = 'file'
      this.upload()
    },
    async upload () {
      if (!this.file) {
        return
      }
      if (!this.attachment) {
        this.$q.loading.show({ message: 'Uploading...' })
        const resp = await uploadAttachment(this.file, 'public')
        if (typeof resp === 'string') {
          this.$q.loading.hide()
          return notifyResponse(`Failed to upload: ${resp}`)
        }
        this.attachment = resp
      }
      this.setAvatar()
    },
    async setAvatar () {
      this.$q.loading.show({ message: 'Set avatar...' })
      const resp = await request(`/admin/faas/base/member/profile/update`, 'file', {
        avatar: this.file
      }, true)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        location.reload()
      } else {
        notifyResponse(resp.message)
      }
    },
    async closeMyAccount () {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to close your account?',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/faas/base/member/close`)
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp.message)
          window.location.href = '/logout'
        } else {
          notifyResponse(resp.message)
        }
      }).catch(() => {})
    },
    phone () {
      if (this.user.customerSupportPhone) {
        window.location.href = 'tel:' + this.user.customerSupportPhone
      }
    },
    email () {
      if (this.user.customerSupportEmail) {
        window.location.href = 'mailto:' + this.user.customerSupportEmail
      }
    }
    // replaceCard () {
    //   this.$q.dialog({
    //     title: 'Confirm',
    //     message: `Are you sure that you want to replace the card?`,
    //     color: 'negative',
    //     cancel: true
    //   }).then(async () => {
    //     this.$q.loading.show()
    //     const resp = await request('/admin/faas/base/member/replace-card', 'post')
    //     this.$q.loading.hide()
    //     if (resp.success) {
    //       notify(resp)
    //       this.reload()
    //     }
    //   }).catch(() => {})
    // }
  }
}
</script>
<style lang="scss">
.modal.minimized .modal-content {
  max-width: 500px;
}
.faas__member_settings__index_page {
  .q-card {
    padding: 18px 4px;
  }
  .light-grey {
    color: #8e8e93;
  }
  .icon {
    .q-icon {
      width: 40px;
      height: 40px;
      background: #eaeaea;
      border-radius: 50%;
      color: #1c1c1e;
      font-size: 20px;
    }
  }
  .avatar-setting {
    display: flex;
    align-item: center;
    flex-shrink: 0;
  }
  .account-box {
    display: flex;
    align-items: center;
    cursor: pointer;
    flex-shrink: 0;
    .q-btn-round {
      width: 60px;
      height: 60px;
    }
    .name-desc {
      line-height: 42px;
    }
  }
  .btn-upload {
    background: var(--span-color-primary) !important;
    border-radius: 50px;
  }
  .avatar {
    width: 60px;
    height: 60px;
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    border-radius: 50%;
  }
  .account-settings,
  .card-history {
    height: 676px;
    .q-card-main {
      max-height: 580px;
      overflow-y: scroll;
    }
  }
  .security-settings {
    height: 369px;
  }
  .customer-support {
    height: 291px;
    .q-card-main {
      max-height: 210px;
      overflow-y: scroll;
    }
    .q-list {
      border-top: 1px solid #e0e0e0 !important;
      border-bottom: 1px solid #e0e0e0 !important;
      padding: 0;
    }
  }
}
</style>
