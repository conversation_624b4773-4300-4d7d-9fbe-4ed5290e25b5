<template>
  <q-dialog class="faas-clients-settings-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="mb-5">
        <div class="radius-box">
          <q-icon name="mdi-credit-card"
                  class="font-20"></q-icon>
        </div>
      </div>
      <div class="font-18 mb-2">Replace Card</div>
      <div class="font-12 light-grey normal">
        Your Card will be sent to the address below. If the information is incorrect please contact <NAME_EMAIL>
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt-0 mb-0">
        <div class="col-sm-12 mt-5 pt-0">
          <q-input float-label="Full Card Number"
                   v-model="cardNumber"></q-input>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <q-btn label="Replace Card"
             no-caps
             color="positive"
             class="wp-100 mb-20"
             @click="submit" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../../mixins/Singleton'
import { request, notify, notifyForm } from '../../../../../common'

export default {
  name: 'faas-replace-card-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      cardNumber: ''
    }
  },
  methods: {
    submit () {
      if (!this.cardNumber) {
        return notifyForm()
      }
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to replace the card?`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request('/admin/faas/base/member/replace-card', 'post', {
          cardNumber: this.cardNumber
        })
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp)
          this.$root.$emit('reload-faas-member-setting')
          this._hide()
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
  .faas-clients-settings-dialog {
    .modal-content {
      width: 400px;
      .client-logo {
        max-height: 120px;
      }
    }
    .light-grey {
      color: #8E8E93;
    }
  }
</style>
