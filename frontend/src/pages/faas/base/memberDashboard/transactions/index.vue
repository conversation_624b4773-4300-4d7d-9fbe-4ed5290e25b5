<template>
  <q-page class="faas__member_transactions__index_page">
    <div class="page-header">
      <div class="fun-group">
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row">
        <div class="col-sm-12 transactions">
          <q-card class="pt-30 pb-30 pl-20 pr-20">
            <q-card-title class="">
              <span class="font-28">My Transactions</span>
            </q-card-title>
            <q-card-main>
              <q-search icon="search"
                        v-model="keyword"
                        type="text"
                        class="search mt-10 mb-20"
                        @keydown.13="search"
                        @clear="search"
                        clearable
                        hide-underline
                        placeholder="Search for transactions">
              </q-search>
              <div class="list-container">
                <q-list no-border
                        class="pt-0"
                        inset-separator
                        v-if="transactions.length > 0">
                  <q-item link
                          class="pt-20 pb-20"
                          v-for="(item, key) in transactions"
                          :key="key">
                    <q-item-side class="text-center light-grey mr-14">
                      <div class="font-12 month bold">{{ item.time | date('MMM') }}</div>
                      <div class="font-12 bold">{{ item.time | date('DD') }}</div>
                    </q-item-side>
                    <q-item-side class="font-20 mr-14">
                      <div class="icon-contain text-center"
                           :class="item.amount >= 0 ? 'up' : 'down'">
                        <q-icon name="mdi-arrow-top-right"
                                v-if="item.amount >= 0"> </q-icon>
                        <q-icon name="mdi-arrow-bottom-left"
                                v-else> </q-icon>
                      </div>
                    </q-item-side>
                    <q-item-main>
                      <q-item-tile label
                                   class="font-16 bold mb-5">{{ item.description }}</q-item-tile>
                      <q-item-tile sublabel
                                   class="font-14 light-grey">{{ item.status }}</q-item-tile>
                    </q-item-main>
                    <q-item-side class="text-right">
                      <div class="font-14 fee bold text-black">{{ item.amount | moneyFormat }}</div>
                      <div class="light-grey font-12">{{ item.balance }}</div>
                    </q-item-side>
                  </q-item>
                </q-list>
                <div class="no-data"
                     v-else>
                  <p>There are currently no transactions.</p>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>
<script>
import FaasPageMixin from '../../../../../mixins/faas/FaasPageMixin'
import { request } from '../../../../../common'
import moment from 'moment'

export default {
  name: 'Faas-Member-Transactions',
  mixins: [
    FaasPageMixin
  ],
  components: {

  },
  data () {
    return {
      dateRange: 'month',
      keyword: null,
      info: [],
      transactions: [],
      transactionList: [
        {
          amount: 75,
          category: null,
          description: 'Deposited by Agent',
          group: 'credit',
          icon: null,
          id: 830378,
          result: 'Payout TranID:830373',
          status: 'Executed',
          time: '2021-09-03T13:11:33+00:00',
          txnId: '13150334',
          type: 'Credit'
        },
        {
          amount: -1000,
          category: null,
          description: 'Transfer/Agent Debit',
          group: 'debit',
          icon: null,
          id: 827447,
          result: null,
          status: 'Executed',
          time: '2021-08-30T14:24:14+00:00',
          txnId: '13107580',
          type: 'Debit'
        }
      ]
    }
  },
  async mounted () {
    this.reload()
  },
  methods: {
    async reload () {
      this.$q.loading.show()
      let data = {}
      if (this.$refs && this.$refs.dateRangeFilter) {
        data = this.$refs.dateRangeFilter.params()
        data.fromDate = moment(data.start || '2021-01-01').format('YYYY-MM-DD')
        data.toDate = moment(data.end || '2038-01-01').format('YYYY-MM-DD')
      }
      data.keywords = this.keyword ? this.keyword : null
      const resp = await request('/admin/faas/base/member/transaction/list', 'get', data)
      this.$q.loading.hide()
      if (resp && resp.data) {
        this.info = resp.data
        this.transactions = resp.data.transactions
      }
    },
    search () {
      this.$nextTick(() => {
        setTimeout(() => {
          this.reload()
        }, 300)
      })
    }
  }
}
</script>
<style lang="scss">
.faas__member_transactions__index_page {
  .list-container {
    max-height: 560px;
    overflow-y: scroll;
  }
  .search {
    background-color: #fff;
    max-width: 500px;
    border-color: #cccccc;
    border-radius: 30px;
    padding: 12px !important;
  }
  .q-item-section + .q-item-section {
    margin-left: 18px !important;
  }
  .icon-contain {
    width: 40px;
    height: 40px;
    line-height: 40px;
    background-color: #34c759;
    border-radius: 50%;
    .q-icon {
      border: 1px solid #fff;
      border-radius: 50%;
      padding: 2px;
      font-size: 14px;
      background-color: #34c759;
      color: #fff;
      vertical-align: unset;
    }
  }
  .icon-contain.down {
    background-color: #ff3b30;
    .q-icon {
      background-color: #ff3b30;
    }
  }
  .month {
    text-transform: uppercase;
  }
}
</style>
