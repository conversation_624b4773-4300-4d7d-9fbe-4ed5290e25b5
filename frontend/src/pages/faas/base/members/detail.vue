<template>
  <q-dialog class="faas-member-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="mb-5">
        <div class="avatar">
          <q-icon name="mdi-account-circle-outline"></q-icon>
        </div>
      </div>
      <div class="font-16 mb-2">{{ edit ? 'Edit Member Profile' : 'Create Member Profile' }}</div>
      <div class="font-12 normal text-dark">Please fill in the following information to create a new member profile within your organization.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12"
             v-if="$store.state.User.clientsRequired && $store.state.User.teams.indexOf('Faas Client') === -1">
          <q-select float-label="Client"
                    autocomplete="no"
                    :options="clients"
                    :error="$v.entity['Client'].$error"
                    @change="$v.entity['Client'].$touch"
                    v-model="entity['Client']"></q-select>
        </div>
        <div class="col-sm-12 form-field-title">Contact Info</div>
        <div class="col-sm-6">
          <q-input placeholder="First Name"
                   autocomplete="no"
                   :error="$v.entity['First Name'].$error"
                   @input="$v.entity['First Name'].$touch"
                   v-model="entity['First Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input placeholder="Last Name"
                   autocomplete="no"
                   :error="$v.entity['Last Name'].$error"
                   @input="$v.entity['Last Name'].$touch"
                   v-model="entity['Last Name']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-datetime placeholder="Date of Birth"
                      autocomplete="no"
                      type="date"
                      format="MM/DD/YYYY"
                      :error="$v.entity['Date of Birth'].$error"
                      :readonly="$store.state.User.teams.indexOf('MasterAdmin') !== -1 && entity['KYC passed'] === true"
                      @change="$v.entity['Date of Birth'].$touch"
                      v-model="entity['Date of Birth']"></q-datetime>
        </div>
        <div class="col-sm-12">
          <q-input placeholder="Email"
                   autocomplete="no"
                   :error="$v.entity['Email'].$error"
                   @input="$v.entity['Email'].$touch"
                   v-model="entity['Email']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input placeholder="Mobile Phone"
                   autocomplete="no"
                   :error="$v.entity['Phone'].$error"
                   @input="$v.entity['Phone'].$touch"
                   v-model="entity['Phone']"></q-input>
        </div>
        <div class="col-sm-12 form-field-title">Home Address</div>
        <div class="col-sm-12">
          <q-input placeholder="Home Address 1"
                   autocomplete="no"
                   :error="$v.entity['Home Address 1'].$error"
                   @input="$v.entity['Home Address 1'].$touch"
                   v-model="entity['Home Address 1']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input placeholder="Home Address 2"
                   autocomplete="no"
                   v-model="entity['Home Address 2']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input placeholder="City"
                   autocomplete="no"
                   :error="$v.entity['City'].$error"
                   @input="$v.entity['City'].$touch"
                   v-model="entity['City']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-select autocomplete="no"
                    placeholder="Country"
                    :options="countries"
                    filter
                    autofocus-filter
                    v-model="entity['CountryId']"></q-select>
        </div>
        <div class="col-sm-12">
          <q-select autocomplete="no"
                    placeholder="Province/State"
                    :options="states"
                    filter
                    autofocus-filter
                    :before="stateBefore"
                    v-model="entity['StateId']"></q-select>
        </div>
        <div class="col-sm-12">
          <q-input placeholder="Postal Code"
                   autocomplete="no"
                   :error="$v.entity['Postal Code'].$error"
                   @input="$v.entity['Postal Code'].$touch"
                   v-model="entity['Postal Code']"></q-input>
        </div>
        <!-- <div v-if="$store.state.User.KYCRequired && !edit"
             class="col-sm-12 text-left">
          <q-checkbox v-model="sendKYC"
                      label="Send KYC document upload email Invite to user" />
        </div> -->
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <div class="kyc-box"
             v-if="edit && entity.kycStep">
          <div class="form-field-title">KYC Status</div>
          <q-chip v-if="$store.state.User.KYCRequired || ['OFAC Failed', 'KYC Not Started'].includes(entity.kycStep)"
                  class="font-12 mv-5"
                  :class="kycStatusClass">{{ entity.kycStep }}</q-chip>
          <Statuses :statuses="entity.kycStatuses"></Statuses>

          <div class="mv-10 text-negative"
               v-if="kycFailType">
            <strong>{{ kycFailType }}</strong>: {{ kycFailMsg }}
          </div>
          <div v-if="entity.kycApprover"
               class="font-12 text-faded">
            <span>Manually approved by </span>
            <span notranslate="">{{ entity.kycApprover }} @ </span>
            <span>{{ entity.kycApprovedAt }}</span>
          </div>

          <q-btn :label="$store.state.User.KYCRequired ? 'Start KYC Verification' : 'OFAC Verification'"
                 color="blue"
                 no-caps
                 @click="startKyc('ofac')"
                 v-if="entity.kycStep === 'KYC Not Started'" />
          <div class="row"
               v-else-if="['OFAC Failed', 'KYC Failed (OFAC)', 'KYC Failed (OFAC & Scan)'].includes(entity.kycStep)">
            <q-btn label="Recheck OFAC"
                   color="orange"
                   no-caps
                   @click="startKyc('ofac')"></q-btn>
            <q-btn label="Manually Approve"
                   color="blue"
                   no-caps
                   class="main"
                   v-if="entity.kycStatuses && entity.kycStatuses['ID Scan'] !== null"
                   @click="manualKyc"></q-btn>
          </div>
          <q-btn label="Start ID Scan"
                 color="blue"
                 no-caps
                 @click="startIdScan"
                 class="ph-50"
                 v-else-if="$store.state.User.KYCRequired  && entity.kycStep === 'KYC (Scan Pending)'" />
          <div class="row"
               v-else-if="$store.state.User.KYCRequired && entity.kycStep === 'KYC Failed (Scan)'">
            <q-btn label="Rescan ID"
                   color="orange"
                   no-caps
                   @click="startIdScan"></q-btn>
            <q-btn label="Manually Approve"
                   color="blue"
                   no-caps
                   class="main"
                   @click="manualKyc"></q-btn>
          </div>
        </div>
        <div class="row">
          <q-btn v-if="edit"
                 :label="'Cancel'"
                 no-caps
                 color="grey-3"
                 text-color="tertiary"
                 @click="cancel" />
          <q-btn :label="edit ? 'Save Changes' : 'Create New Member'"
                 no-caps
                 class="main"
                 @click="save" />
        </div>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { required, email, helpers } from 'vuelidate/lib/validators'
import { notifyForm, request, notify } from '../../../../common'
import FaadStateListMixin from '../../../../mixins/mex/MexStateListMixin'
import _ from 'lodash'
import Statuses from './id_status'

const alpha = helpers.regex('alpha', /^[0-9+-\s]*$/)

export default {
  name: 'faas-member-detail-dialog',
  mixins: [
    Singleton,
    FaadStateListMixin
  ],
  components: {
    Statuses
  },
  data () {
    return {
      defaultEntity: {
        'User ID': 0,
        'Phone': ''
      },
      sendKYC: false,
      verifying: false,
      clients: []
    }
  },
  mounted () {
  },
  computed: {
    edit () {
      return this.entity['User ID']
    },
    kycStatusClass () {
      const step = this.entity.kycStep
      if (!step || step === 'KYC Not Started') {
        return 'dark'
      }
      if (['KYC Passed', 'KYC Manually Approved'].includes(step)) {
        return 'positive'
      }
      return 'negative'
    },
    kycFailType () {
      let type = null
      _.forEach(this.entity.kycStatuses || {}, (v, k) => {
        if (v && v !== true) {
          type = k
          return false
        }
      })
      return type
    },
    kycFailMsg () {
      let msg = null
      _.forEach(this.entity.kycStatuses || {}, v => {
        if (v && v !== true) {
          msg = v
          return false
        }
      })
      return msg
    }
  },
  validations () {
    const validations = {
      entity: {}
    }
    for (const field of [
      'First Name', 'Last Name', 'Email', 'Date of Birth',
      'Home Address 1', 'City', 'Postal Code', 'Client' //, 'StateId', 'CountryId'
    ]) {
      if (this.$store.state.User.clientsRequired) {
        validations.entity[field] = { required }
      } else {
        if (field !== 'Client') {
          validations.entity[field] = { required }
        }
      }
    }
    validations.entity['Phone'] = { alpha }
    validations.entity['Email'] = { required, email }
    return validations
  },
  methods: {
    async show () {
      this.$q.loading.show()
      const resp = await request(`/admin/faas/base/members/${this.entity['User ID']}/detail`)
      this.$q.loading.hide()
      if (resp.success) {
        this.entity = _.assignIn({}, this.entity, resp.data.entity)
        this.clients = resp.data.clients || []
        const startKyc = this.entity.startKyc
        delete this.entity.startKyc

        if (startKyc) {
          this.startKyc()
        }
      }
      // this.$q.loading.hide()
    },
    async startKyc (type, param) {
      this.$nextTick(() => {
        this.$el.querySelector('.modal-content').scrollTop = 10000
      })

      this.verifying = true
      if (this.$v.$anyDirty) {
        await this.save()
        if (this.$v.$invalid) {
          this.verifying = false
          return
        }
      }
      if (!type) {
        if (this.entity.kycStatuses['OFAC'] !== true) {
          type = 'ofac'
        } else {
          return
        }
      }
      const oldStep = this.entity.kycStep
      this.$q.loading.show({
        message: 'Verifying ' + type.toUpperCase()
      })
      const resp = await request(`/admin/faas/base/members/${this.entity['User ID']}/id-${type}`, 'post', {
        param
      })
      this.$q.loading.hide()

      this.entity = _.assignIn({}, this.entity, resp.data)
      delete this.entity.startKyc
      this.verifying = false

      if (oldStep !== this.entity.kycStep) {
        this.$root.$emit('reload-faas-members')
        this.$root.$emit('reload-faas-members-profile')
      }

      if (resp.data.kycStep === 'KYC (Scan Pending)' && this.$store.state.User.KYCRequired) {
        this.startIdScan()
      }
    },
    async startIdScan () {
      if (this.$v.$anyDirty) {
        await this.save()
      }
      this._hideAndEmit('show-mex-member-id-upload-dialog', this.entity)
    },
    async manualKyc () {
      if (this.$v.$anyDirty) {
        await this.save()
      }
      this._hideAndEmit('show-mex-member-id-manual-dialog', this.entity)
    },
    onSuccess (resp) {
      if (this.verifying) {
        return
      }
      notify(resp.message)
      this.$root.$emit('reload-faas-members')
      this.$root.$emit('reload-faas-members-profile')
      this.$root.$emit('reload-faas-members-profile-notes')
      this.$root.$emit('reload-mex-employers-employee')
      if (!this.edit) {
        this.visible = false

        setTimeout(() => {
          resp.data.startKyc = true
          this._show(resp.data)
        }, 200)
      } else {
        this._hide()
      }
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      this.entity.sendKYC = this.sendKYC
      const resp = await request(`/admin/faas/base/members/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    },
    cancel () {
      this._hide()
    },
    hide () {
      this.$root.$emit('reload-faas-member')
    }
  }
}
</script>

<style lang="scss">
.faas-member-detail-dialog {
  .modal-content {
    width: 500px;
  }
  .gutter-form > div {
    padding-top: 15px;
  }
  .form-field-title {
    font-size: 14px;
    color: #333;
    width: 100%;
    font-weight: 500;
    text-align: left;
    text-transform: none;
    margin-bottom: -10px;
  }
  .avatar .q-icon {
    padding: 10px;
    background: rgba($color: #4acc3d, $alpha: 0.1);
    color: #4acc3d;
    font-size: 20px;
    border-radius: 40px;
  }
  .q-btn.main {
    background: #005daa;
    width: 100%;
    color: #fff;
  }
  .kyc-box {
    padding: 15px;
    margin-bottom: 15px;
    background-color: #f6f6f6;
    border-radius: 15px;
    text-align: center;
    color: #333;

    .form-field-title {
      margin: 0 auto;
      padding-top: 0 !important;
      text-align: center;
    }

    .faas-member-id-status {
      max-width: 320px;
      margin: 10px auto;
    }
  }
}
</style>
