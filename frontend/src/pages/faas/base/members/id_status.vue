<template>
  <div class="row faas-member-id-status">
    <div v-for="(status, s) in statuses"
         :key="s"
         :class="classType(s)">
      <q-icon v-if="($store.state.User.KYCRequired && s ==='ID Scan') || s ==='OFAC'"
              :name="stepIcon(status)"
              :color="stepColor(status)"></q-icon> <span v-if="($store.state.User.KYCRequired && s ==='ID Scan') || s ==='OFAC'">{{ s }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'faas-member-id-status',
  props: {
    statuses: {
      type: Object
    }
  },
  methods: {
    classType (s) {
      if (s === 'ID Scan' && !this.$store.state.User.KYCRequired) {
        return 'hide-item'
      }
      return ''
    },
    stepIcon (s) {
      if (s === null) {
        return 'mdi-clock-outline'
      }
      if (s === true) {
        return 'mdi-check-circle'
      }
      return s === 'loading' ? 'mdi-spin mdi-loading' : 'mdi-close-circle'
    },
    stepColor (s) {
      if (s === null) {
        return 'faded'
      }
      if (s === true) {
        return 'positive'
      }
      return s === 'loading' ? 'blue' : 'negative'
    }
  }
}
</script>

<style lang="scss">
.faas-member-id-status {
  display: flex;
  justify-content: space-around;
  .q-icon {
    font-size: 23px;
  }
  .hide-item {
    display: none;
  }
}
</style>
