<template>
  <q-page id="faas__member__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Organization"
                  :options="cardPrograms"></q-select>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-xs-12"
             :class="$store.state.User.KYCRequired ? 'col-sm-2-5' : (n.title !=='KYC Pending' ? 'col-sm-3' : '') "
             v-for="(n, i) in numbers"
             :key="i">
          <q-card v-if="n.title !=='KYC Pending' || (n.title ==='KYC Pending' && $store.state.User.KYCRequired)"
                  class="top-statics">
            <!-- <q-card-title>
              <span>{{n.title}}</span>
            </q-card-title> -->
            <q-card-main>
              <div class="row">
                <q-icon class="summary-icon blue"
                        v-if="n.title ==='Invited'"><img src="/static/faas/img/member_invited.svg"></q-icon>
                <q-icon class="summary-icon positive"
                        v-else-if="n.title ==='Active'"><img src="/static/faas/img/member_active.svg"></q-icon>
                <q-icon class="summary-icon warning"
                        v-else-if="n.title ==='Inactive'"><img src="/static/faas/img/member_inactive.svg"></q-icon>
                <q-icon class="summary-icon on-hold"
                        v-else-if="n.title ==='On Hold'"><img src="/static/faas/img/member_on_hold.svg"></q-icon>
                <q-icon class="summary-icon kyc-failed"
                        v-else-if="n.title ==='OFAC/KYC Failed'"><img src="/static/faas/img/member_kyc_failed.svg"></q-icon>
                <div>
                  <div class="font-20">{{n.title}}</div>
                  <div class="item-value heavy">
                    {{ quick && quick[n.title] ? quick[n.title] : 0 }}
                    <q-chip dense
                            v-if="(quick['percent'] && quick['percent'][n.title] ? quick['percent'][n.title] : 0) != 0 "
                            :class="(quick['percent'] && quick['percent'][n.title] ? quick['percent'][n.title] : 0 ) > 0 ? 'positive' : 'negative'">
                      <q-icon v-if="(quick['percent'] && quick['percent'][n.title] ? quick['percent'][n.title] : 0 ) > 0"
                              class="mdi mdi-trending-up"></q-icon>
                      <q-icon v-if="(quick['percent'] && quick['percent'][n.title] ? quick['percent'][n.title] : 0 ) < 0"
                              class="mdi mdi-trending-down"></q-icon>{{ quick['percent'] && quick['percent'][n.title] ? quick['percent'][n.title] : 0  | percent(1, true) }}
                    </q-chip>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Member Report</strong>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm ml-10 export-btn"
                 no-caps></q-btn>
          <q-btn icon="mdi-plus-circle-outline"
                 color="positive"
                 label="Create New Member"
                 @click="edit(null)"
                 class="btn-sm ml-10"
                 no-caps></q-btn>
        </template>
        <template slot="top-right">

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10 table-btn"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="table-btn"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'KYC passed'">
              <q-icon v-if="props.row['KYC passed'] === true"
                      name="mdi-check-circle"
                      color="positive"
                      class="font-15"></q-icon>
              <q-icon v-else
                      name="mdi-close-circle"
                      color="negative"
                      class="font-15"></q-icon>
            </template>
            <template v-else-if="col.field === 'Bank Linking'">
              <q-icon v-if="props.row['Bank Linking'] > 0"
                      name="mdi-check-circle"
                      color="positive"
                      class="font-15"></q-icon>
              <q-icon v-else
                      name="mdi-close-circle"
                      color="negative"
                      class="font-15"></q-icon>
              <span v-if="props.row['Bank Linking'] > 0"> {{props.row['Bank Linking']}} </span>
            </template>
            <template v-else-if="col.field === 'Companions'">
              <q-icon v-if="props.row['Companions'] > 0"
                      name="mdi-check-circle"
                      color="positive"
                      class="font-15"></q-icon>
              <q-icon v-else
                      name="mdi-close-circle"
                      color="negative"
                      class="font-15"></q-icon>
              <span v-if="props.row['Companions'] > 0"> {{props.row['Companions']}} </span>
            </template>
            <template v-else-if="col.field === 'User ID'">
              <span class="user-id"
                    @click="view(props.row)">{{props.row['User ID']}}</span>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="view(props.row)">
                    <q-item-main>View Member</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit Member</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="createCard(props.row)"
                          v-if="props.row['Status'] === 'Onboarded' && !props.row['Account Number']">
                    <q-item-main>Create Card</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="replaceCard(props.row)"
                          v-if="props.row['Account Number']">
                    <q-item-main>Replace Card</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="enrollCard(props.row)"
                          v-if="props.row['Status'] === 'Active' && props.row['Enrolled'] === 'No'">
                    <q-item-main>Enroll/Activate Card</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Status'] !== 'Inactive'">
                    <q-item-main label="Change Status"></q-item-main>
                    <q-item-side right>
                      <q-item-tile icon="mdi-menu-right"></q-item-tile>
                    </q-item-side>
                    <q-popover anchor="bottom left"
                               self="top right"
                               :offset="[0, -40]">
                      <q-list link>
                        <q-item v-close-overlay
                                @click.native="changeCardStatus(props.row, 'Unsuspended')"
                                v-if="props.row['Card Status'] == 'Suspended'">
                          <q-item-main>Unsuspended</q-item-main>
                        </q-item>
                        <q-item v-close-overlay
                                @click.native="changeCardStatus(props.row, 'Suspended')"
                                v-if="props.row['Card Status'] == 'Active'">
                          <q-item-main>Suspended</q-item-main>
                        </q-item>
                        <q-item v-close-overlay
                                @click.native="changeStatus(props.row, 'Active')"
                                v-if="['On Hold'].includes(props.row['Status'])">
                          <q-item-main>Active</q-item-main>
                        </q-item>
                        <q-item v-close-overlay
                                @click.native="changeStatus(props.row, 'On Hold')"
                                v-if="props.row['Status'] !== 'On Hold'">
                          <q-item-main>On Hold</q-item-main>
                        </q-item>
                        <q-item v-close-overlay
                                @click.native="changeStatus(props.row, 'Closed')">
                          <q-item-main>Inactive</q-item-main>
                        </q-item>
                      </q-list>
                    </q-popover>
                  </q-item>
                  <q-item v-if="['Active'].includes(props.row['Status']) && props.row['Enrolled'] === 'Yes' && masterAdmin"
                          v-close-overlay
                          @click.native="changePin(props.row)">
                    <q-item-main>Change PIN</q-item-main>
                  </q-item>
                  <q-item v-if="(['Active'].includes(props.row['Status']) && agentAdmin ) || (['Active', 'On Hold'].includes(props.row['Status']) && masterAdmin)"
                          v-close-overlay
                          @click.native="loginAs(props.row)">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Status'] !== 'Inactive' && props.row['KYC passed'] && masterAdmin"
                          v-close-overlay
                          @click.native="loadUnload(props.row)">
                    <q-item-main>Load/Unload</q-item-main>
                  </q-item>
                  <!-- <q-item v-if="props.row['KYC passed'] && props.row['Status'] === 'Active'"
                          v-close-overlay
                          @click.native="unload(props.row)">
                    <q-item-main>Unload</q-item-main>
                  </q-item> -->
                  <template v-if="config.idScan">
                    <q-item v-if="!props.row['KYC passed']"
                            v-close-overlay
                            @click.native="manually(props.row)">
                      <q-item-main>Manually Approve</q-item-main>
                    </q-item>
                    <!-- <q-item v-if="!props.row['KYC passed'] && (props.row['Status'] ==='Invited' || props.row['Status'] === 'KYC (Scan Pending)' )"
                            v-close-overlay
                            @click.native="sendKyc(props.row)">
                      <q-item-main>Send KYC Invite</q-item-main>
                    </q-item> -->
                  </template>
                  <template v-if="config.api && masterAdmin">
                    <q-item v-close-overlay
                            class="q-item-split">
                      <q-item-main @click.native="showWidget(props.row, 'card_issuing')">View Card-issuing Widget</q-item-main>
                      <q-item-side right
                                   @click.native="showWidget(props.row, 'card_issuing', 'external')">
                        <q-item-tile icon="mdi-open-in-new"></q-item-tile>
                      </q-item-side>
                    </q-item>
                  </template>
                  <template v-if="config.api && masterAdmin && $store.state.User.KYCRequired">
                    <q-item v-close-overlay
                            class="q-item-split">
                      <q-item-main @click.native="showWidget(props.row, 'kyc')">View KYC Widget</q-item-main>
                      <q-item-side right
                                   @click.native="showWidget(props.row, 'kyc', 'external')">
                        <q-item-tile icon="mdi-open-in-new"></q-item-tile>
                      </q-item-side>
                    </q-item>
                  </template>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
    <Detail></Detail>
    <!-- <LoadMember></LoadMember>
    <RefundMember></RefundMember> -->
    <IdUploadDialog></IdUploadDialog>
    <IdInstructionDialog></IdInstructionDialog>
    <IdManualDialog></IdManualDialog>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

    <CardIssuingWidget></CardIssuingWidget>
    <KycWidget></KycWidget>

    <AssignDialog></AssignDialog>
    <AssignedDialog></AssignedDialog>
    <SmsDialog :callback="verifiedSms"></SmsDialog>
    <PinDialog></PinDialog>
    <ReplaceDialog></ReplaceDialog>
    <LoadDialog></LoadDialog>
    <q-dialog v-model="changeStatusDialogModel"
              prevent-close>
      <span slot="title">
        Change Status
        <q-btn class="close"
               round
               flat
               @click="onCancel"
               icon="close" />
      </span>

      <span v-if="selectRow"
            slot="message">
        <p v-if="status ==='Closed'">Please enter the reason you want to inactivate the card belonging to {{$c.fullName(selectRow)}}.</p>
        <p v-if="status ==='Closed'">This will set the card status to inactive and sweep the funds back to the master funding account. This action is irreversible.</p>
        <p v-if="status !=='Closed'">Please enter the reason why you want to change the card's status to {{status}} belonging to {{$c.fullName(selectRow)}}</p>
      </span>

      <div slot="body">
        <q-input v-model="reason" />
      </div>

      <template slot="buttons">
        <q-btn color="negative"
               label="Cancel"
               @click="onCancel" />
        <q-btn color="positive"
               label="Submit"
               @click="onOk" />
      </template>
    </q-dialog>
  </q-page>
</template>
<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, request, notify } from '../../../../common'
import FaasPageMixin from '../../../../mixins/faas/FaasPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import Detail from './detail'
// import LoadMember from './load_member.vue'
// import RefundMember from './refund_member.vue'
import IdUploadDialog from './../../../mex/members/id_upload'
import IdInstructionDialog from '../../../mex/members/id_instruction'
import IdManualDialog from './../../../mex/members/id_manual'
import AssignDialog from './../../../mex/members/assign'
import CardIssuingWidget from '../widgets/card_issuing/index'
import KycWidget from '../widgets/kyc/index'
import AssignedDialog from './../../../mex/members/assigned'
import SmsDialog from './../../../mex/common/sms'
import PinDialog from './../../../mex/members/pin'
import ReplaceDialog from './../../../mex/members/replace'
import LoadDialog from './../../../mex/members/card/load'

export default {
  name: 'faas-members',
  mixins: [
    FaasPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-faas-members', 'searchMember')
  ],
  components: {
    Detail,
    // LoadMember,
    // RefundMember,
    IdUploadDialog,
    IdInstructionDialog,
    IdManualDialog,
    CardIssuingWidget,
    KycWidget,
    AssignDialog,
    AssignedDialog,
    SmsDialog,
    PinDialog,
    ReplaceDialog,
    LoadDialog
  },
  data () {
    let columnsData = []
    if (this.$store.state.User.clientsRequired) {
      columnsData = [
        'User ID', 'Client', 'Client Funding Type', 'Email', 'First Name', 'Last Name',
        'KYC passed', // 'Bank Linking', 'Companions',
        'Create Date', 'Account Number', 'Enrolled', 'Last Login Time',
        'Balance', 'Status', 'Actions'
      ]
    } else {
      columnsData = [
        'User ID', 'Email', 'First Name', 'Last Name',
        'KYC passed', // 'Bank Linking', 'Companions',
        'Create Date', 'Account Number', 'Enrolled', 'Last Login Time',
        'Balance', 'Status', 'Actions'
      ]
    }
    return {
      title: 'Members',
      requestUrl: `/admin/faas/base/members/list`,
      downloadUrl: `/admin/faas/base/members/export`,
      keyword: '',
      dateRange: 'month',
      reason: '',
      selectRow: null,
      status: null,
      changeStatusDialogModel: false,
      columns: generateColumns(columnsData, [], {
        'User ID': 'u.id',
        // 'Client': 'g.name',
        'Email': 'u.email',
        'First Name': 'u.firstName',
        'Last Name': 'u.lastName',
        'Create Date': 'u.createdAt'
      }),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        }, {
          value: 'filter[u.mobilephone=]',
          label: 'Phone'
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 3,
      numbers: [
        {
          title: 'KYC Pending',
          value: 0,
          delta: 0
        },
        {
          title: 'Active',
          value: 0,
          delta: 0
        },
        {
          title: 'Inactive',
          value: 0,
          delta: 0
        },
        {
          title: 'On Hold',
          value: 0,
          delta: 0
        },
        {
          title: 'OFAC/KYC Failed',
          value: 0,
          delta: 0
        }
      ],
      config: {
        idScan: false,
        clients: false,
        portals: false,
        api: false
      },
      requestCb: (data) => {
        this.config = data.config
      }
    }
  },
  mounted () {
    this.keyword = localStorage.getItem('quickSearch') ? localStorage.getItem('quickSearch') : ''
    this.dateRange = localStorage.getItem('quickSearch') ? 'all' : 'month'
    localStorage.setItem('quickSearch', '')
    if (this.$store.state.User.KYCRequired) {
      this.filterOptions.push(
        {
          value: 'Status',
          label: 'Status',
          options: [
            { label: 'Active', value: 'Active' },
            { label: 'KYC Pending', value: 'KYC (Scan Pending)' },
            { label: 'KYC Failed', value: 'KYC Failed' },
            { label: 'OFAC Failed', value: 'OFAC Failed' },
            { label: 'Inactive', value: 'Closed' },
            { label: 'On Hold', value: 'On Hold' }
          ]
        }
      )
    } else {
      this.filterOptions.push(
        {
          value: 'Status',
          label: 'Status',
          options: [
            { label: 'Active', value: 'Active' },
            { label: 'OFAC Failed', value: 'OFAC Failed' },
            { label: 'Inactive', value: 'Closed' },
            { label: 'On Hold', value: 'On Hold' }
          ]
        }
      )
    }
  },
  methods: {
    searchMember (data) {
      this.keyword = data
      this.reload()
    },
    statusClass (status) {
      const cls = []
      cls.push({
        'Active': 'positive',
        'KYC Failed': 'kyc-failed',
        'Invited': 'blue',
        'Inactive': 'warning',
        'OFAC Failed': 'kyc-failed',
        'KYC Failed (Scan)': 'kyc-failed',
        'On Hold': 'on-hold',
        'KYC (Scan Pending)': 'purple',
        'Onboarded': 'blue'
      }[status] || status)
      return cls
    },
    view (row) {
      const { href } = this.$router.resolve({
        path: `/h/faas/base/members/${row['User ID']}`
      })
      window.open(href, '_blank')
    },
    edit (row) {
      this.$root.$emit('show-faas-member-detail-dialog', row)
    },
    loadUnload (row) {
      row['Member ID'] = row['User ID']
      if (this.masterAdmin) {
        this.$root.$emit('show-mex-member-load-dialog', row)
      } else {
        row.smsType = 'loadUnload'
        this.$root.$emit('show-mex-common-sms-dialog', row)
      }
    },
    // unload (row) {
    //   this.$root.$emit('show-faas-refund-member-dialog', row)
    // },
    manually (row) {
      // if (row.Status === 'KYC (Scan Pending)') {
      //   this.$root.$emit('show-mex-member-id-upload-dialog', row)
      // } else if (this.config.idScan && row.Status === 'OFAC Failed') {
      //   this.$root.$emit('show-faas-member-detail-dialog', row)
      // } else {
      this.$root.$emit('show-mex-member-id-manual-dialog', row)
      // }
    },
    assignCard (row) {
      row['Member ID'] = row['User ID']
      this.$root.$emit('show-mex-member-assign-dialog', row)
    },
    changeCard (row) {
      row.smsType = 'change'
      row['Member ID'] = row['User ID']
      this.$root.$emit('show-mex-common-sms-dialog', row)
    },
    createCard (row) {
      this.$q.dialog({
        title: 'Create Card',
        message: `Are you sure that you want to create a new card? The user address is: "${row['Address']}".`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await this.c.request(`/admin/faas/base/members/${row['User ID']}/create-card`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp)
          await this.reload()
        }
      }).catch(() => {})
    },
    replaceCard (row) {
      this.$q.dialog({
        title: 'Replace Card',
        message: `Are you sure that you want to create a new card? The old card will be suspended. The balance on the old card will be transferred to the new card. The user address is: "${row['Address']}".`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await this.c.request(`/admin/faas/base/members/${row['User ID']}/replace-card`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp)
          await this.reload()
        }
      }).catch(() => {})
    },
    enrollCard (row) {
      row.smsType = 'enroll'
      this.$root.$emit('show-mex-common-sms-dialog', row)
    },
    async doEnrollCard (row) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/members/${row['User ID']}/enroll`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        await this.reload()
      }
    },
    verifiedSms (row, token) {
      row.token = token
      if (row.smsType === 'change') {
        row.change = true
        this.$root.$emit('show-mex-member-assign-dialog', row)
      } else if (row.smsType === 'replace') {
        this.$root.$emit('show-mex-member-replace-dialog', row)
      } else if (row.smsType === 'enroll') {
        this.doEnrollCard(row)
      } else if (row.smsType === 'reassign') {
        this.doReassignCard(row)
      } else if (row.smsType === 'loginAs') {
        this.$c.loginAs(row['Member ID'] || row['User ID'])
      } else if (row.smsType === 'changePin') {
        this.$root.$emit('show-mex-member-pin-dialog', row)
      }
    },
    async changeCardStatus (row, status) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/members/${row['User ID']}/change-card-status`, 'post', {
        cardStatus: status
      })
      this.$q.loading.hide()
      if (resp.success) {
        await this.reload()
      }
    },
    onCancel () {
      this.changeStatusDialogModel = false
    },
    async onOk () {
      if (!this.reason) {
        return notify('The reason is required!', 'negative')
      }
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/faas/base/members/${this.selectRow['User ID']}/change-status`, 'post', {
        status: this.status,
        reason: this.reason
      })
      this.$q.loading.hide()
      this.changeStatusDialogModel = false
      if (resp.success) {
        await this.reload()
      }
    },
    changeStatus (row, status) {
      // let message = ''
      this.changeStatusDialogModel = true
      this.selectRow = row
      this.status = status
      // if (status === 'Closed') {
      //   message = `Please enter the reason you want to inactivate the card belonging to ${fullName(row)}.
      //   This will set the card status to inactive and sweep the funds back to the master funding account. This action is irreversible.`
      // } else {
      //   message = `Please enter the reason why you want to change the card's status to "${status}" belonging to ${fullName(row)}`
      // }
      // this.$q.dialog({
      //   title: 'Change Status',
      //   message: message,
      //   cancel: {
      //     color: 'negative',
      //     flat: true,
      //     noCaps: true
      //   },
      //   ok: {
      //     color: 'positive',
      //     label: 'Submit',
      //     noCaps: true
      //   },
      //   prompt: {
      //     model: ''
      //   }
      // }).then(async reason => {
      //   if (!reason) {
      //     return notify('The reason is required!', 'negative')
      //   }
      //   this.$q.loading.show()
      //   const resp = await this.c.request(`/admin/faas/base/members/${row['User ID']}/change-status`, 'post', {
      //     status: status,
      //     reason: reason
      //   })
      //   this.$q.loading.hide()
      //   if (resp.success) {
      //     await this.reload()
      //   }
      // }).catch(() => {})
    },
    loginAs (row) {
      if (this.masterAdmin) {
        this.$c.loginAs(row['User ID'])
      } else if (this.agentAdmin) {
        row.smsType = 'loginAs'
        this.$root.$emit('show-mex-common-sms-dialog', row)
      }
    },
    changePin (row) {
      row['Member ID'] = row['User ID']
      if (this.masterAdmin) {
        this.$root.$emit('show-mex-member-pin-dialog', row)
      } else {
        row.smsType = 'changePin'
        this.$root.$emit('show-mex-common-sms-dialog', row)
      }
    },
    async sendKyc (row) {
      this.$q.loading.show({
        message: 'Send KYC Invite...'
      })
      const resp = await request(`/admin/faas/base/members/${row['User ID']}/sendKyc`, 'get')
      this.$q.loading.hide()
      notify(resp.message)
    },
    showWidget (row, type, scope) {
      if (scope === 'external') {
        const url = `/admin/faas/base/members/${row['User ID']}/widget-sample/${type}`
        window.open(url, '_blank')
      } else {
        type = type.replace('_', '-')
        this.$root.$emit(`show-faas-${type}-dialog`, row)
      }
    }
  }
}
</script>
<style lang="scss">
#faas__member__index_page {
  .summary-icon {
    height: 50px !important;
    width: 50px !important;
    font-size: 24px;
    margin-top: 15px !important;
    border-radius: 50px !important;
    margin-right: 10px;
  }
  .item-value {
    font-size: 30px;
  }
  .create-btn {
    background: #005daa;
  }
  .export-btn {
    background: #190644 !important;
  }
  .table-btn {
    border: 0.5px solid #5a5a89;
    border-radius: 10px;
  }
  .user-id {
    color: #fa6400;
    text-decoration-line: underline;
    cursor: pointer;
  }
  .positive {
    color: #4acc3d;
    background: rgba($color: #4acc3d, $alpha: 0.1) !important;
  }
  .blue {
    color: #005daa;
    background: rgba($color: #005daa, $alpha: 0.1) !important;
  }
  .warning {
    color: #f16501;
    background: rgba($color: #f16501, $alpha: 0.1) !important;
  }
  .on-hold {
    color: #9b71fc;
    background: rgba($color: #9b71fc, $alpha: 0.1) !important;
  }
  .kyc-failed {
    color: #f10013;
    background: rgba($color: #f10013, $alpha: 0.1) !important;
  }
}
</style>
