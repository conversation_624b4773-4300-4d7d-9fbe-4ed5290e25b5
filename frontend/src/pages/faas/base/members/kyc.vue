<template>
  <q-page id="faas__member__kyc__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Organization"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class=" col-xs-12"
             :class="$store.state.User.KYCRequired ? 'col-sm-2-5' : (n.title !=='KYC Failed' ? 'col-sm-3' : 'hide-item') "
             v-for="(n, i) in numbers"
             :key="i">
          <q-card v-if="n.title !=='KYC Failed' || (n.title ==='KYC Failed' && $store.state.User.KYCRequired)"
                  class="top-statics">
            <!-- <q-card-title>
              <span>{{n.title}}</span>
            </q-card-title> -->
            <q-card-main>
              <div class="row">
                <q-icon class="summary-icon blue"
                        v-if="n.title ==='KYC Failed'"
                        name="mdi-shield-remove-outline"></q-icon>
                <q-icon class="summary-icon positive"
                        v-else-if="n.title ==='Approved'"
                        name="mdi-shield-check-outline"></q-icon>
                <q-icon class="summary-icon warning"
                        v-else-if="n.title ==='Rejected'"
                        name="mdi-shield-remove-outline"></q-icon>
                <q-icon class="summary-icon kyc-failed"
                        v-else-if="n.title ==='OFAC Failed'"
                        name="mdi-shield-remove-outline"></q-icon>
                <q-icon class="summary-icon rate"
                        v-else-if="n.title ==='Approval Rate'"
                        name="mdi-check"></q-icon>
                <div>
                  <div class="font-20">{{n.title}}</div>
                  <div class="item-value heavy">
                    <span v-if="n.title === 'Approval Rate'">{{ quick && quick[n.title] ? quick[n.title] : 0 | percent(1)}}</span>
                    <span v-else>{{ quick && quick[n.title] ? quick[n.title] : 0 }}</span>
                    <q-chip dense
                            v-if="(quick['percent'] && quick['percent'][n.title] ? quick['percent'][n.title] : 0) != 0 "
                            :class="(quick['percent'] && quick['percent'][n.title] ? quick['percent'][n.title] : 0 ) > 0  ? 'positive' : 'negative'">
                      <q-icon v-if="(quick['percent'] && quick['percent'][n.title] ? quick['percent'][n.title] : 0 ) > 0 "
                              class="mdi mdi-trending-up"></q-icon>
                      <q-icon v-if="(quick['percent'] && quick['percent'][n.title] ? quick['percent'][n.title] : 0 ) < 0"
                              class="mdi mdi-trending-down"></q-icon>{{ n.delta | percent(1, true) }}
                    </q-chip>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>KYC Exceptions</strong>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm ml-10 export-btn"
                 no-caps></q-btn>
        </template>
        <template slot="top-right">

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10 table-btn"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="table-btn"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'KYC passed'">
              <q-icon v-if="props.row['KYC passed']"
                      name="mdi-check-circle"
                      color="positive"
                      class="font-15"></q-icon>
              <q-icon v-else
                      name="mdi-close-circle"
                      color="negative"
                      class="font-15"></q-icon>
            </template>
            <template v-else-if="col.field === 'Bank Linking'">
              <q-icon v-if="props.row['Bank Linking'] > 0"
                      name="mdi-check-circle"
                      color="positive"
                      class="font-15"></q-icon>
              <q-icon v-else
                      name="mdi-close-circle"
                      color="negative"
                      class="font-15"></q-icon>
              <span v-if="props.row['Bank Linking'] > 0"> {{props.row['Bank Linking']}} </span>
            </template>
            <template v-else-if="col.field === 'Companions'">
              <q-icon v-if="props.row['Companions'] > 0"
                      name="mdi-check-circle"
                      color="positive"
                      class="font-15"></q-icon>
              <q-icon v-else
                      name="mdi-close-circle"
                      color="negative"
                      class="font-15"></q-icon>
              <span v-if="props.row['Companions'] > 0"> {{props.row['Companions']}} </span>
            </template>
            <template v-else-if="col.field === 'User ID'">
              <span class="user-id"
                    @click="view(props.row['User ID'])">{{props.row['User ID']}}</span>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="view(props.row)">
                    <q-item-main>View Member</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Status'] === 'KYC (Scan Pending)' || props.row['Status'] === 'KYC Failed (Scan)' || props.row['Status'] === 'OFAC Failed' || props.row['Status'] === 'KYC Failed' || props.row['Status'] === 'Rejected'"
                          v-close-overlay
                          @click.native="manually(props.row)">
                    <q-item-main>Manually Approve</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Status'] === 'Approved'"
                          v-close-overlay
                          @click.native="reject(props.row)">
                    <q-item-main>Reject Member</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <IdManualDialog></IdManualDialog>
  </q-page>
</template>
<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, request, notify } from '../../../../common'
import FaasPageMixin from '../../../../mixins/faas/FaasPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import IdManualDialog from './../../../mex/members/id_manual'

export default {
  name: 'faas-members',
  mixins: [
    FaasPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-faas-members')
  ],
  components: {
    IdManualDialog
  },
  mounted () {
  },
  data () {
    return {
      title: 'KYC Exceptions',
      requestUrl: `/admin/faas/base/members/kyclist`,
      downloadUrl: `/admin/faas/base/members/kycexport`,
      keyword: '',
      dateRange: 'all',
      columns: generateColumns([
        'User ID', 'First Name', 'Last Name', 'Street Address', 'City', 'State', 'Postal', 'Email Address',
        'Created Date', 'Status', 'Actions'
      ], [], {
      }),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lasstName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        }, {
          value: 'filter[u.mobilephone=]',
          label: 'Phone'
        },
        {
          value: 'Status',
          label: 'Status',
          options: [
            { label: 'Approved', value: 'Approved' },
            { label: 'Rejected', value: 'Rejected' },
            { label: 'KYC Failed', value: 'KYC Failed' },
            { label: 'OFAC Failed', value: 'OFAC Failed' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2,
      numbers: [
        {
          title: 'Approved',
          value: 0,
          delta: 0
        },
        {
          title: 'KYC Failed',
          value: 0,
          delta: 0
        },
        {
          title: 'OFAC Failed',
          value: 0,
          delta: 0
        },
        {
          title: 'Rejected',
          value: 0,
          delta: 0
        },
        {
          title: 'Approval Rate',
          value: 0,
          delta: 0
        }
      ]
    }
  },
  methods: {
    statusClass (status) {
      const cls = []
      cls.push({
        'Approved': 'positive',
        'KYC Failed': 'blue',
        'KYC Failed (Scan)': 'blue',
        'KYC (Scan Pending)': 'blue',
        'Rejected': 'warning',
        'OFAC Failed': 'kyc-failed'
      }[status] || status)
      return cls
    },
    view (row) {
      const { href } = this.$router.resolve({
        path: `/h/faas/base/members/${row['User ID']}`
      })
      window.open(href, '_blank')
    },
    manually (row) {
      this.$root.$emit('show-mex-member-id-manual-dialog', row)
    },
    async reject (row) {
      this.$q.loading.show()
      const resp = await request(`/admin/faas/base/members/${row['User ID']}/reject`)
      this.$q.loading.hide()
      notify(resp.message)
      this.reload()
    }
  }
}
</script>
<style lang="scss">
#faas__member__kyc__index_page {
  .hide-item {
    display: none;
  }
  .summary-icon {
    height: 50px !important;
    width: 50px !important;
    font-size: 24px !important;
    margin-top: 15px !important;
    border-radius: 50px !important;
    margin-right: 10px;
  }
  .item-value {
    font-size: 30px;
  }
  .create-btn {
    background: #005daa;
  }
  .export-btn {
    background: #190644 !important;
  }
  .table-btn {
    border: 0.5px solid #5a5a89;
    border-radius: 10px;
  }
  .user-id {
    color: #fa6400;
    text-decoration-line: underline;
    cursor: pointer;
  }
  .positive {
    color: #4acc3d;
    background: rgba($color: #4acc3d, $alpha: 0.1) !important;
  }
  .blue {
    color: #005daa;
    background: rgba($color: #005daa, $alpha: 0.1) !important;
  }
  .warning {
    color: #ffe900;
    background: rgba($color: #ffe900, $alpha: 0.1) !important;
  }
  .rate {
    color: #9b71fc;
    background: rgba($color: #9b71fc, $alpha: 0.1) !important;
  }
  .kyc-failed {
    color: #f10013;
    background: rgba($color: #f10013, $alpha: 0.1) !important;
  }
}
</style>
