<template>
  <q-card id="faas__members__profile_basic"
          class="high-card">
    <q-card-main>
      <div class="text-center mb-20">
        <div class="avatar mt-5">
          <img :src="'/static/img/avatar.png'"
               alt="">
        </div>
        <div class="font-20 bold mt-5 full-name">{{ $c.fullName(entity) }}</div>
        <div class="text-faded">{{ entity['Phone'] }}</div>
        <q-chip class="font-13 mt-5"
                :class="statusClass(entity['Status'])">
          {{ entity['Status'] }}
        </q-chip>

        <!-- <div class="font-16 heavy text-black mt-15">{{ entity['Balance'] | moneyFormat }}</div> -->
        <div class="font-16 heavy text-black mt-15">
          <q-icon name="mdi-refresh"
                  color="positive"
                  class="mr-5 hidden-only"></q-icon>
          <span class="va-m">{{ entity['Balance'] }}</span>
          <q-icon v-if="entity['Balance']"
                  name="mdi-refresh"
                  color="positive"
                  @click.native="$emit('reload')"
                  class="ml-5 pointer">
            <q-tooltip>Refresh</q-tooltip>
          </q-icon>
        </div>
        <div class="text-faded mt--3">Current Balance</div>

      </div>
      <table class="fields-table">
        <tr v-for="(r, i) in fields"
            :key="i">
          <th>{{ r }}</th>
          <td>{{ entity[r] }}</td>
        </tr>
      </table>
    </q-card-main>
  </q-card>
</template>

<script>

export default {
  name: 'faas-members-profile-basic',
  components: {
  },
  props: {
    entity: {
      type: Object
    }
  },
  data () {
    return {
      fields: [
        'Location', 'Email', 'Phone'
      ]
    }
  },
  methods: {
    statusClass (status) {
      const cls = [{
        'Active': 'positive',
        'KYC Failed': 'kyc-failed',
        'Invited': 'blue',
        'Inactive': 'warning',
        'OFAC Failed': 'kyc-failed',
        'On Hold': 'on-hold'
      }[status] || status]
      return cls
    }
  }
}
</script>

<style lang="scss">
#faas__members__profile_basic {
  .avatar {
    width: 130px;
    height: 130px;
    background-color: #eee;
    border-radius: 130px;
    margin: 0 auto;
    overflow: hidden;
  }

  .radius-box {
    border: none;
    color: white;
    position: absolute;
    right: 16px;
    top: 16px;
    font-size: 18px;
    padding: 4px 5px;
    line-height: 1em;
    cursor: pointer;

    &.bg-positive {
      background-color: #3dd598 !important;
    }
  }

  .fields-table {
    th {
      min-width: 60px !important;
    }
  }
  .positive {
    color: #4acc3d;
    background: rgba($color: #4acc3d, $alpha: 0.1) !important;
  }
  .blue {
    color: #005daa;
    background: rgba($color: #005daa, $alpha: 0.1) !important;
  }
  .warning {
    color: #f16501;
    background: rgba($color: #f16501, $alpha: 0.1) !important;
  }
  .on-hold {
    color: #9b71fc;
    background: rgba($color: #9b71fc, $alpha: 0.1) !important;
  }
  .kyc-failed {
    color: #f10013;
    background: rgba($color: #f10013, $alpha: 0.1) !important;
  }
  .full-name {
    display: flex;
    justify-content: center;
    line-height: 20px;
    i {
      color: #fc5a5a;
      cursor: pointer;
      font-size: 20px;
      margin-left: 5px;
    }
  }
  .action-btn {
    display: flex;
    justify-content: center;
    .load-btn,
    .unload-btn {
      color: #fafafb;
      font-size: 14px;
      height: 39px;
      // line-height: 39px;
      text-transform: none;
      margin-top: 10px;
      display: flex;
      align-items: center;
      i {
        font-size: 20px;
      }
    }
    .load-btn {
      background: #01da33;
    }
    .unload-btn {
      background: #df2f35;
      margin-left: 10px;
    }
  }
}
</style>
