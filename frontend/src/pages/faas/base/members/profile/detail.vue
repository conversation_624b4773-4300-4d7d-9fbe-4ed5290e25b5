<template>
  <q-card id="faas__members__profile_detail"
          class="high-card">
    <q-card-title>
      <div class="row flex-center">
        <div class="font-18 bold">Account Details</div>
        <q-btn icon="mdi-refresh"
               outline
               @click="$emit('reload')"
               class="btn-mini ml-auto"></q-btn>
        <q-btn icon="mdi-pencil-outline"
               outline
               @click="edit"
               class="btn-mini ml-5"></q-btn>
      </div>
    </q-card-title>
    <q-card-main>
      <table class="fields-table mt-10 mb-auto">
        <tr v-for="(r, i) in fields"
            :key="i">
          <th>{{ r }}</th>
          <td :class="r === 'Card Status' && entity[r] !== 'Active' ? 'text-negative' : ''">
            {{ entity[r] }}
            <a href="javascript:"
               class="ml-5 decoration-none"
               @click="editField(r)"
               v-if="entity.Status === 'Active' && ['Card Status'].includes(r)">
              <q-icon name="mdi-pencil-outline"></q-icon>
            </a>
          </td>
        </tr>
      </table>
      <div v-if="entity['isManualApprover'] && entity['idManualApprovalSignature']"
           class="row flex-center mv-auto">
        <q-btn icon="mdi-lock-open-outline"
               no-caps
               color="positive"
               class="btn-sm mb-10 mh-10"
               v-if="entity['Enrolled'] === 'Yes' && entity['Card Status'] === 'Inactive'"
               @click="activateCard"
               label="Activate Card"></q-btn>
        <q-btn icon="mdi-file-download-outline"
               no-caps
               class="btn-sm profile-btn"
               @click="manualDetail"
               label="Manual KYC Details"></q-btn>
      </div>
      <div v-else
           class="row flex-center mv-auto">
        <q-btn icon="mdi-lock-open-outline"
               no-caps
               color="positive"
               class="btn-sm mb-10 mh-10"
               v-if="entity['Enrolled'] === 'Yes' && entity['Card Status'] === 'Inactive'"
               @click="activateCard"
               label="Activate Card"></q-btn>
        <q-btn v-if="$store.state.User.KYCRequired && entity['KYC passed'] === true"
               icon="mdi-file-download-outline"
               no-caps
               class="btn-sm profile-btn"
               @click="download()"
               label="DownLoad KYC Documents"></q-btn>
        <q-btn v-else-if="entity['KYC passed'] !== true"
               icon="mdi-file-download-outline"
               no-caps
               class="btn-sm profile-btn"
               @click="manual()"
               label="Manually Verify KYC"></q-btn>
      </div>
    </q-card-main>

    <HtmlListDialog></HtmlListDialog>
    <DetailDialog></DetailDialog>
    <CardStatusDialog></CardStatusDialog>

    <SmsDialog :callback="verifiedSms"></SmsDialog>
  </q-card>
</template>

<script>
import HtmlListDialog from '../../../../../components/HtmlListDialog'
import DetailDialog from '../detail'
import { request, notifySuccess } from '../../../../../common'
import CardStatusDialog from './../../../../mex/members/card/status'
import SmsDialog from './../../../../mex/common/sms'
import _ from 'lodash'

export default {
  name: 'faas-members-profile-detail',
  components: {
    HtmlListDialog,
    DetailDialog,
    CardStatusDialog,
    SmsDialog
  },
  props: {
    entity: {
      type: Object
    }
  },
  data () {
    return {
      fields: [
        'First Name', 'Last Name', 'Date of Birth', 'Mailing Address',
        'City', 'Country', 'State / Province', 'Postal Code',
        'Barcode Number', 'Card Number', 'Card Status'
      ]
    }
  },
  methods: {
    edit () {
      this.$root.$emit('show-faas-member-detail-dialog', this.entity)
    },
    manual () {
      this.$root.$emit('show-faas-member-id-manual-dialog', this.entity)
    },
    manualDetail () {
      this.$root.$emit('show-faas-member-manual-detail-dialog', this.entity)
    },
    async editField (field) {
      this.entity['Member ID'] = this.entity['User ID']
      if (field === 'Card Status') {
        this.$root.$emit('show-mex-member-card-status-dialog', this.entity)
      }
    },
    verifiedSms (row, token) {
      row.token = token
      if (row.smsType === 'editDetail') {
        this.$root.$emit('save-mex-member-detail')
      }
    },
    async activateCard () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/members/${this.entity['User ID']}/activate-card`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        notifySuccess(resp)
        this.$emit('reload')
      }
    },
    async download () {
      this.$q.loading.show()
      const resp = await request(`/admin/faas/base/members/${this.entity['User ID']}/id-download`)
      this.$q.loading.hide()
      if (resp.success && resp.data) {
        if (resp.data.length > 1) {
          const links = []
          _.forEach(resp.data, (v, k) => {
            links.push(`<a href="${v}/download">File ${k + 1}</a>`)
          })
          this.$root.$emit('show-html-list-dialog', {
            title: 'Download KYC Documents',
            html: `<p>Click on the below links to download the files:</p><ul><li>${links.join('</li><li>')}</li></ul>`
          })
        } else if (resp.data.length) {
          location.href = `${resp.data[0]}/download`
        }
      }
    }
  }
}
</script>

<style lang="scss">
#faas__members__profile_detail {
  .q-card-main {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }
}
</style>
