<template>
  <q-page id="faas__members__profile_page">
    <div class="row gutter-sm mt-0">
      <div class="col-sm-12 col-md-4">
        <BasicCard :entity="entity"
                   @reload="reload(true, true)"></BasicCard>
      </div>
      <div class="col-sm-12 col-md-4">
        <DetailCard :entity="entity"
                    @reload="reload"></DetailCard>
      </div>
      <div class="col-sm-12 col-md-4">
        <NotesCard :uid="uid"></NotesCard>
      </div>
      <div class="col-12">
        <TransactionsCard :uid="uid"></TransactionsCard>
      </div>
      <ManualDetail></ManualDetail>
      <IdManualDialog></IdManualDialog>
    </div>
  </q-page>
</template>

<script>
import FaasPageMixin from '../../../../../mixins/faas/FaasPageMixin'
import BasicCard from './basic'
import DetailCard from './detail'
import NotesCard from './notes'
import TransactionsCard from './transactions'
import ManualDetail from './manualDetail'
import IdManualDialog from './../../../../mex/members/id_manual'

import { EventHandlerMixin, request, notifySuccess } from '../../../../../common'

export default {
  name: 'faas-members-profile',
  mixins: [
    FaasPageMixin,
    EventHandlerMixin('reload-faas-members-profile')
  ],
  components: {
    BasicCard,
    DetailCard,
    NotesCard,
    TransactionsCard,
    ManualDetail,
    IdManualDialog
  },
  data () {
    return {
      title: 'Member Profile',
      entity: {}
    }
  },
  computed: {
    uid () {
      return this.$route.params.id
    }
  },
  watch: {
    uid () {
      this.reload()
    }
  },
  methods: {
    async reload (indicator = true, force = false) {
      indicator && this.$q.loading.show()
      const resp = await request(`/admin/faas/base/members/${this.uid}/profile`, 'get', {
        force
      })
      indicator && this.$q.loading.hide()
      if (resp.success) {
        this.entity = resp.data
        if (force) {
          notifySuccess(resp)
        }
      }
      // test data
      // this.entity = {
      //   'First Name': 'Austin',
      //   'Last Name': 'Robertson',
      //   'Date of Birth': 'June 10, 1995',
      //   'Mailing Address': '123 1st Avenue',
      //   'City': 'New York',
      //   'Country': 'United States',
      //   'State / Province': 'New York',
      //   'Postal Code': '10002',
      //   'Email': '<EMAIL>',
      //   'Phone': '+****************',
      //   'Location': 'United States',
      //   'Status': 'Active',
      //   'Balance': '$1,284.48'
      // }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
#faas__members__profile_page {
  .q-card {
    &.high-card {
      height: 473px;
    }

    &.wide-card {
      min-height: 300px;

      > .q-card-main {
        position: relative;
        height: 100%;
      }
    }

    overflow: auto;

    > .q-card-primary {
      padding-top: 16px;
    }

    > .q-card-main {
      position: relative;
      height: calc(100% - 67px);
    }
  }

  .q-card-title .font-18.bold {
    height: 33px;
    padding-top: 3px;
  }

  .fields-table {
    width: 100%;

    th {
      padding: 5px 5px 5px 0;
      font-weight: normal;
      font-size: 13px;
      color: #888;
      text-align: left;
      min-width: 100px;
      vertical-align: top;
    }

    td {
      padding: 5px 0 5px 5px;
      text-align: right;
      word-break: break-word;
    }
  }

  .empty-tip {
    height: 100%;
    color: #666;

    .q-icon {
      margin-bottom: 5px;
      font-size: 20px;
    }
  }
  .profile-btn {
    background: #00d54f;
    color: #ffffff;
  }
}
</style>
