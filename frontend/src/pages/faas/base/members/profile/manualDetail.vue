<template>
  <q-dialog class="faas-member-manual-detail-dialog"
            v-model="visible">
    <template slot="title">
      <div class="font-18 mb-2">Manual KYC Details</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <img v-if="entity.idManualApprovalSignature && entity"
           :src="entity.idManualApprovalSignature">
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../../mixins/Singleton'

export default {
  name: 'faas-member-manual-detail-dialog',
  mixins: [
    Singleton
  ],
  computed: {},
  methods: {
  }
}
</script>

<style lang="scss">
.faas-member-manual-detail-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
