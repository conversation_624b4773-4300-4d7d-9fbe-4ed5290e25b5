<template>
  <q-card id="faas__members__profile_notes"
          class="high-card">
    <q-card-title>
      <div class="row flex-center">
        <div class="font-18 bold"
             @click="reload">Notes</div>
        <q-btn icon="mdi-pencil-outline"
               label="Add New Note"
               no-caps
               @click="add"
               class="btn-sm ml-auto profile-btn"></q-btn>
      </div>
    </q-card-title>
    <q-card-main>
      <div class="note-card"
           v-for="(item, i) in items"
           :key="i">
        <div class="header">
          <div class="row">
            <div class="title">{{ item.user }}</div>
            <a href="javascript:"
               class="ml-auto close"
               @click="remove(item)">
              <q-icon name="mdi-close"></q-icon>
            </a>
          </div>
          <div class="text-faded font-12">{{ item.time | date('MMMM DD, YYYY @ hh:mm A') }}</div>
        </div>
        <div class="body">
          <ul v-if="item.id">
            <li v-for="(line, j) in lines(item)"
                :key="'line_' + j">{{ line }}</li>
          </ul>
          <template v-else>
            <q-input type="textarea"
                     v-model="item.content"></q-input>
            <div class="text-right mt-8 mb--5">
              <q-btn color="positive"
                     class="btn-sm"
                     label="Save"
                     no-caps
                     icon="mdi-content-save-outline"
                     @click="save(item)"></q-btn>
            </div>
          </template>
        </div>
      </div>

      <q-inner-loading :visible="loading"></q-inner-loading>

      <div class="column flex-center empty-tip"
           v-if="!loading && !items.length">
        <q-icon name="mdi-note-outline"></q-icon>
        <div>No notes...yet</div>
      </div>
    </q-card-main>
  </q-card>
</template>

<script>
import { EventHandlerMixin, request } from '../../../../../common'
import moment from 'moment'
import _ from 'lodash'

export default {
  name: 'faas-members-profile-notes',
  mixins: [
    EventHandlerMixin('reload-faas-members-profile-notes')
  ],
  props: {
    uid: {
      type: String
    }
  },
  data () {
    return {
      loading: false,
      items: []
    }
  },
  watch: {
    uid () {
      this.reload()
    }
  },
  methods: {
    lines (item) {
      return (item.content || '').split('\n').filter(v => (v || '').trim())
    },
    async reload () {
      this.loading = true
      const resp = await request(`/admin/faas/base/members/${this.uid}/notes`)
      this.loading = false
      if (resp.success) {
        this.items = resp.data
      }
    },
    add () {
      this.items.unshift({
        id: null,
        user: this.$store.state.User.fullName,
        time: moment().format(),
        content: ''
      })
    },
    removeItem (item) {
      const pos = this.items.indexOf(item)
      this.items.splice(pos, 1)
    },
    remove (item) {
      if (!item.id) {
        this.removeItem(item)
      } else {
        this.$q.dialog({
          title: 'Confirm',
          message: 'Are you sure that you want to delete this note?',
          color: 'negative',
          cancel: true
        }).then(async () => {
          this.loading = true
          const resp = await request(`/admin/faas/base/members/${this.uid}/notes-del/${item.id}`)
          this.loading = false
          if (resp.success) {
            this.removeItem(item)
          }
        }).catch(() => {})
      }
    },
    async save (item) {
      this.loading = true
      const resp = await request(`/admin/faas/base/members/${this.uid}/notes-add`, 'post', item)
      this.loading = false
      if (resp.success) {
        _.assignIn(item, resp.data)
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
#faas__members__profile_notes {
  > .q-card-main {
    overflow: auto;
  }

  .note-card {
    background-color: #feffc4;
    box-shadow: 0 0 8px rgba(28, 20, 70, 0.15);
    border-radius: 8px;
    margin: 5px 0 15px;
    padding: 15px;

    &:last-of-type {
      margin-bottom: 0;
    }

    .title {
      font-size: 14px;
      font-weight: 500;
    }

    .close {
      color: #888 !important;
      text-decoration: none;

      > .q-icon {
        font-size: 18px;
      }

      &:hover {
        color: var(--q-color-negative) !important;
      }
    }

    .body {
      margin: 10px 0 0;
      font-size: 12px;

      ul {
        margin: 0;
        padding-left: 20px;
      }
    }
  }
}
</style>
