<template>
  <q-card id="faas__members__profile_transactons"
          class="wide-card">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :loading="loading"
               class="inner-table m--15"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">Transaction History</div>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm ml-10 export-btn"
                 no-caps></q-btn>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 class="table-btn "
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field === 'Transaction Type'">
              <q-icon :name="iconName(props.row)"
                      :color="iconColor(props.row)"
                      size="16px"></q-icon>
              {{ props.row['Type'] }}
            </template>
            <template v-else-if="col.field === 'User ID'">
              <span class="account-id">{{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="props.row['Status'] === 'Executed' ? 'positive' : 'warning'">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>
        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </q-card-main>
  </q-card>
</template>

<script>
import ListPageMixin from '../../../../../mixins/ListPageMixin'
import { generateColumns } from '../../../../../common'

export default {
  name: 'faas-members-profile-transactions',
  mixins: [
    ListPageMixin
  ],
  props: {
    uid: {
      type: String
    }
  },
  data () {
    return {
      title: 'Member Profile',
      requestUrl: `/admin/mex/members/transactions/list`,
      downloadUrl: `/admin/mex/members/transactions/export`,
      columns: generateColumns([
        'Transaction Type', 'Date & Time', 'Transaction ID', 'User ID', 'Full Name', 'Amount', 'Status'
      ]),
      keyword: '',
      autoLoad: true
    }
  },
  mounted () {
    // test data
    // this.data = [
    //   {
    //     'Transaction Type': 'DEBIT',
    //     'Date & Time': 'March 26, 2021 • 10:31am',
    //     'Full Name': 'Jose Gonzales ',
    //     'User ID': '3526236',
    //     'Amount': '$1,234.56',
    //     'Balance': '$1,234.56',
    //     'Status': 'Completed'
    //   },
    //   {
    //     'Transaction Type': 'DEBIT',
    //     'Date & Time': 'March 26, 2021 • 10:31am',
    //     'Full Name': 'Jose Gonzales ',
    //     'User ID': '3526236',
    //     'Amount': '$1,234.56',
    //     'Balance': '$1,234.56',
    //     'Status': 'Pending'
    //   },
    //   {
    //     'Transaction Type': 'DEBIT',
    //     'Date & Time': 'March 26, 2021 • 10:31am',
    //     'Full Name': 'Jose Gonzales ',
    //     'User ID': '3526236',
    //     'Balance': '$1,234.56',
    //     'Amount': '$1,234.56',
    //     'Status': 'Pending'
    //   },
    //   {
    //     'Transaction Type': 'DEBIT',
    //     'Date & Time': 'March 26, 2021 • 10:31am',
    //     'Full Name': 'Jose Gonzales ',
    //     'User ID': '3526236',
    //     'Amount': '$1,234.56',
    //     'Balance': '$1,234.56',
    //     'Status': 'Completed'
    //   },
    //   {
    //     'Transaction Type': 'DEBIT',
    //     'Date & Time': 'March 26, 2021 • 10:31am',
    //     'Full Name': 'Jose Gonzales ',
    //     'User ID': '3526236',
    //     'Amount': '$1,234.56',
    //     'Balance': '$1,234.56',
    //     'Status': 'Completed'
    //   },
    //   {
    //     'Transaction Type': 'DEBIT',
    //     'Date & Time': 'March 26, 2021 • 10:31am',
    //     'Full Name': 'Jose Gonzales ',
    //     'User ID': '3526236',
    //     'Amount': '$1,234.56',
    //     'Balance': '$1,234.56',
    //     'Status': 'Completed'
    //   },
    //   {
    //     'Transaction Type': 'DEBIT',
    //     'Date & Time': 'March 26, 2021 • 10:31am',
    //     'Full Name': 'Jose Gonzales ',
    //     'User ID': '3526236',
    //     'Amount': '$1,234.56',
    //     'Balance': '$1,234.56',
    //     'Status': 'Completed'
    //   },
    //   {
    //     'Transaction Type': 'DEBIT',
    //     'Date & Time': 'March 26, 2021 • 10:31am',
    //     'Full Name': 'Jose Gonzales ',
    //     'User ID': '3526236',
    //     'Amount': '$1,234.56',
    //     'Balance': '$1,234.56',
    //     'Status': 'Completed'
    //   },
    //   {
    //     'Transaction Type': 'DEBIT',
    //     'Date & Time': 'March 26, 2021 • 10:31am',
    //     'Full Name': 'Jose Gonzales ',
    //     'User ID': '3526236',
    //     'Amount': '$1,234.56',
    //     'Balance': '$1,234.56',
    //     'Status': 'Completed'
    //   }
    // ]
  },
  methods: {
    iconName (row) {
      const group = row['Group']
      return {
        transfer: 'mdi-flash',
        payment: 'mdi-point-of-sale',
        debit: 'mdi-arrow-down',
        credit: 'mdi-arrow-up'
      }[group] || 'mdi-help-circle-outline'
    },
    iconColor (row) {
      const group = row['Group']
      return {
        transfer: 'orange',
        payment: 'secondary',
        debit: 'negative',
        credit: 'positive'
      }[group] || 'warning'
    },
    getOtherQueryParams () {
      return {
        member: this.uid
      }
    }
  }
}
</script>
<style lang="scss">
#faas__members__profile_transactons {
  margin-bottom: 30px;
  .dot {
    font-size: 5px !important;
    width: 8px !important;
    height: 8px !important;
    min-height: 8px !important;
    padding: 0 !important;
    margin-right: 5px;
  }
  .debit-type {
    background: #f10012;
  }
  .table-btn {
    border: 0.5px solid #5a5a89;
    border-radius: 10px;
  }
  .export-btn {
    background: #190644 !important;
  }
}
</style>
