<template>
  <q-page id="faas__program__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Organization"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row">
        <BalanceGraph title="Program Balance"
                      v-model="chartDateRange"
                      chartId="programBalance"></BalanceGraph>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Transaction History</strong>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm ml-10 export-btn"
                 no-caps></q-btn>
        </template>
        <template slot="top-right">

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10 table-btn"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="table-btn"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="props.row['Status'] === 'Completed' ? 'positive' : 'pending'">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Transaction Type'">
              <q-chip class="dot"
                      :class="typeClass(props.row['Transaction Type'])"></q-chip> {{props.row['Transaction Type']}}
            </template>
            <template v-else-if="col.field === 'Full Name'">
              {{props.row['First Name']}} {{props.row['Last Name']}}
            </template>
            <template v-else-if="col.field === 'User ID'">
              <span class="user-id"
                    @click="view(props.row['Member ID'])">{{props.row['Member ID']}}</span>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>
<script >
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../../common'
import FaasPageMixin from '../../../../mixins/faas/FaasPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import BalanceGraph from '../common/balanceGraph'

export default {
  name: 'faas-partner',
  mixins: [
    FaasPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-faas-partner')
  ],
  components: {
    BalanceGraph
  },
  mounted () {
  },
  data () {
    return {
      title: 'Program Activity',
      requestUrl: `/admin/faas/program/list`,
      downloadUrl: `/admin/faas/program/export`,
      keyword: '',
      dateRange: 'month',
      chartDateRange: 'month',
      columns: generateColumns([
        'Transaction Type', 'Date & time', 'Full Name', 'User ID',
        'Amount', 'Status'
      ], [], {
      }),
      filterOptions: [
        {
          value: 'filter[uct.tranCode=]',
          label: 'Transaction Type',
          options: [
            { label: 'Credit', value: 'CREDIT' },
            { label: 'Debit', value: 'DEBIT' },
            { label: 'Transfer', value: 'transfer' }
          ]
        },
        {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        },
        {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        },
        {
          value: 'filter[u.id=]',
          label: 'Memeber ID'
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2,
      dateRanges: [
        {
          label: 'Month',
          value: 'month'
        },
        {
          label: 'Week',
          value: 'week'
        },
        {
          label: 'All',
          value: 'all'
        },
        {
          label: 'Custom',
          value: 'custom_range'
        }
      ]
    }
  },
  methods: {
    async reload () {
      let data = {}
      if (this.$refs && this.$refs.dateRangeFilter) {
        data = this.$refs.dateRangeFilter.params()
      }
      this.chartDateRange = data.period
      await this.request({
        pagination: this.pagination
      })
      this.everLoaded = true
    },
    typeClass (status) {
      const cls = []
      cls.push({
        'CREDIT': 'credit-type',
        'SPEND': 'debit-type',
        'DEBIT': 'debit-type',
        'TRANSFER': 'transfer-type',
        'TRAN FEE': 'fee-type',
        'MEMBER LOAD': 'member-load-type',
        'MONTHLY FEE': 'fee-type',
        'RETURN': 'credit-type'
      }[status] || status)
      return cls
    },
    view (userId) {
      const { href } = this.$router.resolve({
        path: `/h/faas/base/members/${userId}`
      })
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss">
#faas__program__index_page {
  .graph-btn {
    max-height: 55px !important;
    width: 55px;
    color: #fff;
    .q-icon {
      font-size: 24px;
    }
  }
  .down-chart-btn {
    background: #005daa;
  }
  .export-btn {
    background: #190644 !important;
  }
  .table-btn {
    border: 0.5px solid #5a5a89;
    border-radius: 10px;
  }
  .pending {
    color: #0062ff;
    background: rgba($color: #0062ff, $alpha: 0.1);
  }
  .dot {
    font-size: 5px !important;
    width: 8px !important;
    height: 8px !important;
    min-height: 8px !important;
    padding: 0 !important;
    margin-right: 5px;
  }
  .credit-type {
    background: #0062ff;
  }
  .debit-type {
    background: #f10012;
  }
  .transfer-type {
    background: #9b71fc;
  }
  .fee-type {
    background: #f16501;
  }
  .member-load-type {
    background: #4acc3d;
  }
  .user-id {
    color: #fa6400;
    text-decoration-line: underline;
    cursor: pointer;
  }
}
</style>
