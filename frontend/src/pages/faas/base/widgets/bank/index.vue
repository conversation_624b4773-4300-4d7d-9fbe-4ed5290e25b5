<template>
  <q-dialog
          class="faas-bank-dialog"
          v-model="visible"
  >
    <template slot="title">
      <img class="mt-10" src="/static/faas/img/bank_widget_yodlee.svg"/>
      <div class="title">
        <div class="text-center">
          <div v-show="step === 'introduce'">
            <div class="font-18"><b>Add Banking Information</b></div>
            <div class="desc font-14">PTO Genius uses Yodlee to link your bank account.</div>
          </div>
          <div v-show="step === 'selectBank'">
            <div class="font-18"><b>Select Your Bank</b></div>
          </div>
          <div v-show="step === 'manualEntry'">
            <div class="font-18"><b>Enter Your Banking Information</b></div>
          </div>
        </div>
      </div>
      <q-btn class="back" round flat
             v-show="step !== 'introduce'"
             @click="back" icon="mdi-arrow-left"></q-btn>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <div v-show="step === 'introduce'">
        <div class="">
          <div class="row mb-20">
            <div class="col-sm-2 text-right pr-20">
              <q-icon name="mdi-shield-check"
                      class="font-20 secure-icon"></q-icon>
            </div>
            <div class="col-sm-10">
              <div class="title">
                <b>Secure</b>
              </div>
              <div class="desc">
                Encryption helps protect your personal financial data
              </div>
            </div>
          </div>
          <div class="row mb-20">
            <div class="col-sm-2 text-right pr-20">
              <q-icon name="mdi-lock"
                      class="font-20 secure-icon"></q-icon>
            </div>
            <div class="col-sm-10">
              <div class="title">
                <b>Private</b>
              </div>
              <div class="desc">
                Your credentials will never be made accessible to Yodlee
              </div>
            </div>
          </div>
          <div class="privacy text-center">
            By selecting “Continue” you agree to Yodlee’s <a class="text-black" href="javascript:void(0);">Terms</a>, <a class="text-black" href="javascript:void(0);">Data Policy</a> and <a class="text-black" href="javascript:void(0);">Cookie Privacy</a>.
          </div>
        </div>
      </div>
      <div v-show="step === 'selectBank'">
        <div class="search mb-30">
          <q-input v-model="keywords" placeholder="Don't see your institution? Search here.">
            <template v-slot:before>
              <q-icon name="search" class="font-20 mr-10" />
            </template>
          </q-input>
        </div>
        <div class="row mb-20">
          <div class="col-sm-6 text-right pr-10">
            <q-btn class="logo p-0">
              <img src="/static/faas/img/bank_widget_chase.png"/>
            </q-btn>
          </div>
          <div class="col-sm-6 pl-10">
            <q-btn
                   class="logo p-0"
            >
              <img src="/static/faas/img/bank_widget_second.png"/>
            </q-btn>
          </div>
        </div>
        <div class="row mb-20">
          <div class="col-sm-6 text-right pr-10">
            <q-btn class="logo p-0">
              <img src="/static/faas/img/bank_widget_usbank.png"/>
            </q-btn>
          </div>
          <div class="col-sm-6 pl-10">
            <q-btn
                   class="logo p-0"
            >
              <img src="/static/faas/img/bank_widget_usaa.png"/>
            </q-btn>
          </div>
        </div>
        <div class="row mb-20">
          <div class="col-sm-6 text-right pr-10">
            <q-btn class="logo p-0">
              <img src="/static/faas/img/bank_widget_keybank.png"/>
            </q-btn>
          </div>
          <div class="col-sm-6 pl-10">
            <q-btn
                   class="logo p-0"
            >
              <img src="/static/faas/img/bank_widget_regions.png"/>
            </q-btn>
          </div>
        </div>
      </div>
      <div v-show="step === 'manualEntry'">
        <div class="row">
          <div class="col-sm-12 mb-20">
            <q-input placeholder="Account Number"
                     autocomplete="no"
                     :error="$v.entity['Account Number'].$error"
                     @input="$v.entity['Account Number'].$touch"
                     v-model="entity['Account Number']">
            </q-input>
          </div>
          <div class="col-sm-12">
            <q-input placeholder="Routing Number"
                     autocomplete="no"
                     :error="$v.entity['Routing Number'].$error"
                     @input="$v.entity['Routing Number'].$touch"
                     v-model="entity['Routing Number']">
            </q-input>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div>
        <q-btn v-show="step === 'introduce'"
               color="primary"
               class="wp-100 mb-20 btn-step-one"
               label="Continue"
               @click="jumpTo('selectBank')"
        >
        </q-btn>
        <q-btn v-show="step === 'selectBank'"
               color="primary"
               class="wp-100 mb-20 btn-step-two"
               label="My Bank Isn't Listed"
               @click="jumpTo('manualEntry')"
        >
        </q-btn>
        <q-btn v-show="step === 'manualEntry'"
               color="primary"
               class="wp-100 mb-20 btn-step-two-manual"
               label="Confirm"
               @click="save"
        >
        </q-btn>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../../mixins/Singleton'
import { required } from 'vuelidate/lib/validators'
// import { notifySuccess } from '../../../../../common'

const validations = {
  entity: {
    'Account Number': { required },
    'Routing Number': { required }
  }
}

export default {
  name: 'faas-bank-dialog',
  mixins: [
    Singleton
  ],
  components: {

  },
  data () {
    return {
      step: 'introduce',
      keywords: '',
      defaultEntity: {
        'Account Number': '',
        'Routing Number': ''
      }
    }
  },
  validations,
  computed: {

  },
  methods: {
    show () {
      this.step = 'introduce'
    },
    jumpTo (stepName) {
      this.step = stepName
    },
    save () {
      const _this = this
      this._hide()
      const type = 'success'
      this.$root.$emit('show-message-dialog', {
        title: type === 'success' ? 'Bank Added Successfully' : 'Bank Failed',
        type: type,
        message: type === 'success' ? 'Your Chase account ending in 2827 was successfully added to your bank vault.\n' +
                'You may now use it to make payments.'
          : 'There was an error when capturing your banking information. Please make sure all the information was entered correctly.',
        okLabel: type === 'success' ? 'Go to Bank Vault' : 'Try Adding Bank Again',
        callback: text => {
          if (text) {
            _this.$root.$emit('show-faas-bank-dialog', { step: type === 'success' ? 'introduce' : 'manualEntry' })
          } else {

          }
        }
      })
    },
    back () {
      if (this.step === 'selectBank') {
        this.jumpTo('introduce')
      } else if (this.step === 'manualEntry') {
        this.jumpTo('selectBank')
      }
    }
  }
}
</script>

<style lang="scss">
  @font-face {
    font-family: "OCR A Extended";
    src: url("https://account.usunlocked.com/static/usu/ocraext.ttf") format("truetype");
  }

  .faas-bank-dialog {
    .modal-content {
      width: 400px;
      .modal-header {
      }
      .modal-body {
        text-align: left;
        .secure-icon, .privacy-icon {
          color: var(--span-color-primary);
        }
        .search .q-input {
          border-left: 0;
          box-shadow: unset;
          border-radius: unset;
          border-right: 0;
          border-top: 0;
          margin: 0 46px;
          padding-left: 0!important;
          padding-right: 0!important;
        }
        .logo img {
          width: 120px;
          border: 1px solid rgb(248, 248, 248);
          border-radius: 4px;
          box-shadow: 0 2px 5px 0 rgba(28, 20, 70, 0.2);
        }
        .q-if.q-if-focused {
          box-shadow: unset !important;
          border-color: unset;
        }
      }
      .modal-buttons {
        display: block;
        button {
          /*border-radius: 4px;*/
          /*height: 50px;*/
          margin-left: 0 !important;
          &.btn-step-one {
            background: var(--span-color-primary) !important;
          }
          &.btn-step-two, &.btn-step-two-manual {
            background: var(--span-color-primary) !important;
          }

        }
      }
    }
  }
</style>
