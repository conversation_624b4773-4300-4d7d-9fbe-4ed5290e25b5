<template>
  <div class="card-sim-part card-sim-primary">
    <div class="font-18 text-weight-light">Balance</div>
    <div class="font-28 text-weight-bold mt--5">
      {{ card.balance | moneyFormat }}
      <q-icon name="mdi-refresh" color="white"
              @click.native="widget.refreshBalance"
              class="font-16 pointer"></q-icon>
    </div>

    <template v-if="user.id && !user.enrolled">
      <slot></slot>
    </template>
    <template v-else>
      <div class="label-pan">**** **** **** {{ card.pan }}</div>

      <div class="row mt-25">
        <div class="col-5">
          <div>VALID THRU</div>
          <div>{{ card.month }}/{{ card.year }}</div>
        </div>
        <div class="col-2">
          <div>CVV</div>
          <div>***</div>
        </div>
        <div class="col text-right"
             v-if="card.detail && card.detail.status && card.detail.status !== 'Active'">
          <span class="round-border">{{ card.detail.status }}</span>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: 'faas-card-issuing-card',
  props: {
    widget: {
      type: Object,
      required: true
    },
    user: {
      type: Object,
      required: true
    },
    card: {
      type: Object,
      required: true
    }
  }
}
</script>

<style lang="scss">
  .card-sim-part {
    .round-border {
      max-width: 100%;
      text-align: left;
      display: inline-block;
      word-break: break-all;
      line-height: 1em;
    }
  }
</style>
