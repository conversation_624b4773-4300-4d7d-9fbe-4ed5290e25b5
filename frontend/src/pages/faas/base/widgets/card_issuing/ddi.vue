<template>
  <div class="faas-card-issuing-dialog-ddi">
    <div class="q-subtitle text-center">Direct Deposit Information</div>
    <q-list class="actions-list actions-info-list" no-border separator>
      <q-item v-for="(v, i) of list" :key="i">
        <q-item-main>
          <q-item-tile label>{{ v.label }}</q-item-tile>
          <q-item-tile sublabel>{{ v.value }}</q-item-tile>
        </q-item-main>
        <q-item-side>
          <q-item-tile>
            <q-btn icon="mdi-content-copy"
                   color="primary"
                   @click="c.copyToClipboard(v.value)"
                   round flat></q-btn>
          </q-item-tile>
        </q-item-side>
      </q-item>
    </q-list>
  </div>
</template>

<script>
export default {
  name: 'faas-card-issuing-dialog-ddi',
  props: {
    widget: {
      type: Object,
      required: true
    },
    card: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      list: [
        {
          label: 'Routing Number',
          value: ''
        },
        {
          label: 'Account Number',
          value: ''
        }
      ]
    }
  },
  methods: {
    async reload () {
      this.$q.loading.show()
      const resp = await this.widget.request('/faas/widget/api/card-issuing/ddi')
      this.$q.loading.hide()
      if (resp) {
        this.list[0].value = resp.data.routingNumber
        this.list[1].value = resp.data.accountNumber
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>
