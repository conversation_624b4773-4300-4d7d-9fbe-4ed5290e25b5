<template>
  <div class="faas-card-issuing-dialog-home">
    <q-carousel quick-nav
                class="text-white"
                v-model="slide">
      <q-carousel-slide v-for="(c, i) in cards"
                        :key="i"
                        class="p-0">
        <div class="card-sim-box"
             :class="platformColor">
          <CiCard :user="user"
                  :card="c"
                  :widget="widget">
            <template v-if="['Active'].includes(user.registerStep)">
              <q-input v-model="accountNumber"
                       class="mt-5"
                       placeholder="Enter the barcode number here"></q-input>
              <q-btn outline
                     no-caps
                     class="mt-15 pull-right"
                     @click="activate"
                     label="Activate"></q-btn>
            </template>
            <template v-else>
              <div class="mt-40">
                <div class="font-15">Account status:</div>
                <div class="bold font-22">{{ user.registerStep }}</div>
              </div>
            </template>
          </CiCard>
          <div class="card-sim-part card-sim-secondary"
               v-if="cards.length > 1"></div>
          <div class="card-sim-part card-sim-back"
               v-if="cards.length > 2"></div>
        </div>
        <div class="card-title">{{ c.name }}</div>
      </q-carousel-slide>
    </q-carousel>

    <q-list class="actions-list"
            no-border
            separator>
      <q-item link
              v-for="(t, id) in actions"
              :key="id"
              :class="actionClass"
              @click.native="nav(id)">
        <q-item-side :icon="t.icon"></q-item-side>
        <q-item-main>
          <q-item-tile label>{{ t.name }}</q-item-tile>
        </q-item-main>
        <q-item-side icon="mdi-chevron-right"
                     right></q-item-side>
      </q-item>
      <q-item link
              :class="actionClass"
              @click.native="toggleCard">
        <q-item-side :icon="active ? 'mdi-pause-circle-outline' : 'mdi-play-circle-outline'"></q-item-side>
        <q-item-main>
          <q-item-tile label>{{ active ? 'Suspend My Card' : 'Unsuspend My Card' }}</q-item-tile>
        </q-item-main>
        <q-item-side icon="mdi-chevron-right"
                     right></q-item-side>
      </q-item>
    </q-list>
  </div>
</template>

<script>
import CiCard from './card'
import { notify, notifyForm } from '../../../../../common'

export default {
  name: 'faas-card-issuing-home',
  components: {
    CiCard
  },
  props: {
    widget: {
      type: Object,
      required: true
    },
    user: {
      type: Object,
      required: true
    },
    cards: {
      type: Array,
      required: true
    },
    card: {
      type: Object,
      required: true
    },
    platformColor: {
      type: String,
      required: true
    },
    value: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      accountNumber: null,
      actions: {
        transactions: {
          icon: 'mdi-format-list-bulleted',
          name: 'Transactions'
        },
        spending_down: {
          icon: 'mdi-chart-bar',
          name: 'Spending Breakdown'
        },
        ddi: {
          icon: 'mdi-cash',
          name: 'Direct Deposit Information'
        },
        // view_pin: {
        //   icon: 'mdi-shield-lock-outline',
        //   name: 'View My Pin'
        // },
        replace_card: {
          icon: 'mdi-credit-card-multiple',
          name: 'Replace My Card'
        }
      }
    }
  },
  computed: {
    slide: {
      get () {
        return this.value
      },
      set (v) {
        this.$emit('input', v)
      }
    },
    enrolled () {
      const user = this.user
      return user.id && user.enrolled && ['Active'].includes(user.registerStep)
    },
    actionClass () {
      return this.enrolled ? '' : 'q-item-disabled'
    },
    active () {
      return this.card && this.card.detail && this.card.detail.status === 'Active'
    }
  },
  methods: {
    nav (page) {
      this.$emit('nav', page)
    },
    async activate () {
      if (!this.accountNumber) {
        notifyForm()
        return
      }
      this.$q.loading.show()
      const resp = await this.widget.request(`/faas/widget/api/card-issuing/enroll`, 'post', {
        accountNumber: this.accountNumber
      })
      this.$q.loading.hide()
      if (resp.success) {
        await this.widget.loadCards()
      }
    },
    toggleCard () {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to ${this.active ? 'Suspend' : 'Unsuspend'} the card?`,
        color: this.active ? 'negative' : 'positive',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await this.widget.request(`/faas/widget/api/card-issuing/update-status`, 'post', {
          maskedCardNumber: this.card.detail.maskedCardNumber,
          status: this.active ? 'Suspended' : 'Active'
        })
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp)
          await this.widget.loadCards()
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
.faas-card-issuing-dialog-home {
  text-align: center;

  .q-carousel-inner {
    margin-bottom: 35px;
  }

  .q-carousel-quick-nav {
    background: transparent;
  }
}
</style>
