<template>
  <q-dialog prevent-close
            class="faas-card-issuing-dialog faas-widget-dialog"
            v-model="visible">
    <template slot="title">
      <div class="mb-5">
        <div class="radius-box">
          <q-icon name="mdi-credit-card"
                  class="font-20"></q-icon>
        </div>
      </div>
      <q-btn class="back"
             round
             flat
             v-if="page !== 'home'"
             @click="page = 'home'"
             icon="mdi-arrow-left"></q-btn>
      <div class="font-18 mb-2">{{ title }}</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <CiHome v-show="page === 'home'"
              v-model="slide"
              @nav="page = $event"
              :widget="this"
              :cards="cards"
              :card="card"
              :platformColor="platformColor"
              :user="user"></CiHome>

      <div v-if="page !== 'home'">
        <div class="card-sim-box"
             :class="platformColor">
          <CiCard :card="card"
                  :user="user"
                  :widget="this"></CiCard>
        </div>
      </div>

      <component :is="component"
                 :card="card"
                 :user="user"
                 :widget="this"
                 v-if="page !== 'home'"></component>

      <SmsDialog :who="user"
                 :sms-key="'faasWidget'"
                 :route="'/faas/widget/api/sms'"
                 :params="buildRequestParams({})"
                 :callback="verifiedSms"></SmsDialog>
    </template>
    <template slot="buttons">
      <div></div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../../mixins/Singleton'
import CiCard from './card'
import CiHome from './home'
import CiTransactions from './transactions'
import CiSpendingDown from './spending_down'
import CiDdi from './ddi'
import CiViewPin from './view_pin'
import CiReplaceCard from './replace_card'
import FaasWidgetMixin from '../../../../../mixins/faas/FaasWidgetMixin'
import SmsDialog from '../../../../mex/common/sms'
import { notify } from '../../../../../common'

export default {
  name: 'faas-card-issuing-dialog',
  mixins: [
    Singleton,
    FaasWidgetMixin
  ],
  components: {
    SmsDialog,
    CiCard,
    CiHome,
    CiTransactions,
    CiSpendingDown,
    CiDdi,
    CiViewPin,
    CiReplaceCard
  },
  data () {
    return {
      page: 'home',
      slide: 0,
      cards: [],
      platformColor: ''
    }
  },
  computed: {
    card () {
      return this.cards[this.slide]
    },
    component () {
      return {
        home: CiHome,
        transactions: CiTransactions,
        spending_down: CiSpendingDown,
        ddi: CiDdi,
        view_pin: CiViewPin,
        replace_card: CiReplaceCard
      }[this.page]
    },
    title () {
      if (this.page !== 'home' && this.card) {
        return this.card.name
      }
      if (this.cards.length > 1) {
        return 'My Cards'
      }
      return 'My Card'
    }
  },
  methods: {
    show () {
      this.initData()
      this.loadCards()
    },
    initData () {
      this.slide = 0
      this.page = 'home'
      this.user = {
        accountNumber: null,
        enrolled: false,
        firstName: '',
        lastName: '',
        kycStatus: {
          OFAC: null,
          'ID Scan': null
        },
        cards: []
      }
      this.cards = [
        {
          name: 'Loading...',
          balance: 0,
          pan: '****',
          month: '**',
          year: '**'
        }
      ]
    },
    async loadCards () {
      this.$q.loading.show()
      const resp = await this.request(`/faas/widget/api/card-issuing/cards`)
      this.$q.loading.hide()
      if (resp.success) {
        this.user = resp.data
        if (resp.data.platform === 'iQSTEL') {
          this.platformColor = 'iqstel-card'
        }
        if (this.user.cards.length) {
          this.cards = this.user.cards.map(c => {
            const pan = c.maskedCardNumber || '****'
            return {
              name: this.fullName,
              balance: c.availableBalance || 0,
              pan: pan.substring(pan.length - 4),
              month: '**',
              year: '**',
              detail: c
            }
          })
        } else {
          this.cards = [
            {
              name: this.fullName,
              balance: 0,
              pan: '****',
              month: '**',
              year: '**'
            }
          ]
        }
      }
    },
    async refreshBalance () {
      this.$q.loading.show()
      const resp = await this.request(`/faas/widget/api/card-issuing/balance`)
      this.$q.loading.hide()
      if (resp.success) {
        this.card.balance = resp.data
        notify(resp)
      }
    },
    verifiedSms (row, token) {
      row.token = token
      this.$root.$emit('faas-card-issuing-' + row.smsType, row)
    }
  },
  created () {
    this.initData()
  }
}
</script>

<style lang="scss">
@font-face {
  font-family: "OCR A Extended";
  src: url("https://account.usunlocked.com/static/usu/ocraext.ttf")
    format("truetype");
}

.faas-card-issuing-dialog {
  .modal-content {
    width: 400px;

    .modal-body {
      text-align: left;
    }
  }

  .card-sim-box {
    margin: 0 0 25px;
    position: relative;
    width: 350px;
    max-width: 100%;
    height: 215px;
    left: 50%;
    transform: translate(-50%);

    .card-sim-part {
      width: 100%;
      height: 100%;
      background-color: var(--span-color-primary);
      border-radius: 35px;
      color: white;
      position: relative;
      text-align: left;
      padding: 25px 30px 0;

      .label-pan {
        font-size: 24px;
        margin-top: 12px;
        font-family: "OCR A Extended", "Rubik", "Helvetica Neue", Helvetica,
          Arial, sans-serif;
      }
    }

    .card-sim-primary {
      z-index: 9;
    }

    .card-sim-secondary {
      z-index: 1;
      opacity: 0.6;
      top: 0;
      position: absolute;
      margin-top: 6%;
      transform: scale(0.9);
    }

    .card-sim-back {
      background-color: #eee;
      top: 0;
      position: absolute;
      margin-top: 11.5%;
      transform: scale(0.8);
    }
  }

  .iqstel-card .card-sim-part {
    background-color: red;
  }
  .card-title {
    font-size: 18px;
    color: #333;
  }

  .q-list.actions-list {
    margin: 20px -24px 15px;
    border-top: 1px solid #e0e0e0 !important;
    border-bottom: 1px solid #e0e0e0 !important;
    padding: 0;

    .q-item {
      padding: 14px 24px;
    }

    .q-item-section + .q-item-section {
      margin-left: 16px;
    }
  }

  .q-list.actions-list.actions-info-list {
    .q-item-label {
      font-weight: lighter;
      font-size: 20px;
      margin-bottom: 5px;
      color: black;
    }

    .q-item-sublabel {
      font-weight: bold;
      font-size: 18px;
      color: #333;
      letter-spacing: 1px;
    }
  }

  .q-list.rounded-list {
    padding: 8px 10px;
    overflow: auto;
    max-height: 340px;
    margin: 10px -10px 0;

    .q-item {
      border-radius: 40px;
      padding-right: 25px;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      > * {
        position: relative;
        z-index: 1;
      }

      &:before {
        content: " ";
        background-color: currentColor;
        opacity: 0.25;
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        border-radius: 40px;
      }
    }

    .q-item-side-left {
      width: 45px;
      height: 45px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
    }

    .q-item-label {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 3px;
    }

    .q-item-main {
      .q-item-sublabel {
        font-weight: 300;
      }
    }

    .q-progress {
      height: 6px !important;
      margin: 9px 10px 6px 0;
      border-radius: 3px;
    }
  }

  .modal-buttons {
    display: none;
  }
}
</style>
