<template>
  <div class="faas-card-issuing-dialog-replace_card">
    <div class="q-subtitle text-center">Replace My Card</div>
    <div class="q-body-1 text-faded text-center font-13 mt-10 mb-20">
      Are you share that you want to create a new card? The old card will be suspended.
      The balance on the old card will be transferred to the new card.
    </div>

    <q-btn color="primary"
           class="wp-100 mb-20"
           @click="submit"
           label="Replace Card"></q-btn>
  </div>
</template>

<script>
import { notify } from '../../../../../common'

export default {
  name: 'faas-card-issuing-dialog-replace_card',
  props: {
    widget: {
      type: Object,
      required: true
    },
    card: {
      type: Object,
      required: true
    }
  },
  methods: {
    submit () {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to replace the card?`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await this.widget.request('/faas/widget/api/card-issuing/replace-card', 'post')
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp)
          this.widget.show()
        }
      }).catch(() => {})
    }
  }
}
</script>
