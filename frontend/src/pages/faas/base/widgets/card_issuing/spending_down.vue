<template>
  <div class="faas-card-issuing-dialog-spending_down">
    <div class="row justify-between items-center">
      <div>
        <div class="q-subtitle">Spending Breakdown</div>
        <div class="q-caption">{{ subtitle }}</div>
      </div>
      <DateRangeFilter class="dense"
                       type="button"
                       default="month"
                       :stackLabel="true"
                       @change="reload"
                       ref="dateRangeFilter"></DateRangeFilter>
    </div>
    <q-list no-border sparse class="rounded-list">
      <q-item v-for="(t, i) in list" :key="i" :style="{color: t.color}">
        <q-item-side :style="{backgroundColor: t.color}">
          <q-item-tile :icon="t.icon" color="white"></q-item-tile>
        </q-item-side>
        <q-item-main>
          <q-item-tile label>{{ t.title }}</q-item-tile>
          <q-item-tile sublabel>
            <q-progress :percentage="t.percent * 100"
                        v-if="t.title !== 'Loading...'"
                        :style="{color: t.color + ' !important'}"></q-progress>
          </q-item-tile>
        </q-item-main>
        <q-item-side right v-if="t.title !== 'Loading...'">
          <q-item-tile label>{{ t.amount | moneyFormat }}</q-item-tile>
          <q-item-tile sublabel>{{ t.percent | percent(0) }} of Spending</q-item-tile>
        </q-item-side>
      </q-item>
    </q-list>
  </div>
</template>

<script>
import DateRangeFilter from '../../../../../components/DateRangeFilter'
import moment from 'moment'
import { dateRangePickerText } from '../../../../../common'

export default {
  name: 'faas-card-issuing-dialog-spending_down',
  components: {
    DateRangeFilter
  },
  props: {
    widget: {
      type: Object,
      required: true
    },
    card: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      subtitle: '',
      icons: {
        computer: 'mdi-laptop',
        entertainment: 'mdi-controller-classic-outline',
        finance: 'mdi-finance',
        food: 'mdi-silverware-fork',
        health: 'mdi-stethoscope',
        transportation: 'mdi-car',
        house: 'mdi-home-outline',
        other: 'mdi-shopping'
      },
      list: [
        {
          icon: 'mdi-dots-horizontal',
          color: '#CFE0FE',
          title: 'Loading...'
        }
      ]
    }
  },
  methods: {
    async reload () {
      let data = {}
      if (this.$refs && this.$refs.dateRangeFilter) {
        data = this.$refs.dateRangeFilter.params()

        data.fromDate = moment(data.start || '2021-01-01').format('YYYY-MM-DD')
        data.toDate = moment(data.end || '2038-01-01').format('YYYY-MM-DD')

        this.subtitle = dateRangePickerText(data)
      }
      this.$q.loading.show()
      const resp = await this.widget.request('/faas/widget/api/card-issuing/spending-breakdown', 'get', data)
      this.$q.loading.hide()
      if (resp) {
        this.list = resp.data.map((m, i) => {
          m.icon = this.icons[m.icon] || 'mdi-star-outline'
          return m
        })
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>
