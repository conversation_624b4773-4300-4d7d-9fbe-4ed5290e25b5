<template>
  <div class="faas-card-issuing-dialog-transactions">
    <div class="row justify-between items-center">
      <div>
        <div class="q-subtitle">Transactions</div>
        <div class="q-caption">{{ subtitle }}</div>
      </div>
      <DateRangeFilter class="dense"
                       type="button"
                       default="last_30_days"
                       :stackLabel="true"
                       @change="reload"
                       ref="dateRangeFilter"></DateRangeFilter>
    </div>
    <q-list no-border sparse class="rounded-list">
      <q-item v-for="(t, i) in list" :key="i" :style="{color: t.color}">
        <q-item-side :style="{backgroundColor: t.color}">
          <q-item-tile :icon="t.icon"></q-item-tile>
        </q-item-side>
        <q-item-main>
          <q-item-tile label>{{ t.title }}</q-item-tile>
          <q-item-tile sublabel>{{ t.time }}</q-item-tile>
          <q-item-tile sublabel v-if="t.subtitle && !t.status">{{ t.subtitle }}</q-item-tile>
        </q-item-main>
        <q-item-side right v-if="t.status">
          <q-item-tile label>{{ t.amount | moneyFormat }}</q-item-tile>
          <q-item-tile sublabel>{{ t.status }}</q-item-tile>
        </q-item-side>
      </q-item>
    </q-list>
  </div>
</template>

<script>
import DateRangeFilter from '../../../../../components/DateRangeFilter'
import moment from 'moment'
import { dateRangePickerText } from '../../../../../common'

export default {
  name: 'faas-card-issuing-dialog-transactions',
  components: {
    DateRangeFilter
  },
  props: {
    widget: {
      type: Object,
      required: true
    },
    card: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      subtitle: '',
      list: [
        {
          icon: 'mdi-dots-horizontal',
          color: '#CFE0FE',
          title: 'Loading...'
        }
      ],
      colors: {
        DEBIT: '#EBCFFE',
        CREDIT: '#CFE0FE',
        Other: '#FEF5CF'
      },
      icons: {
        DEBIT: 'mdi-arrow-down',
        CREDIT: 'mdi-arrow-up',
        Other: 'mdi-star-outline'
      }
    }
  },
  methods: {
    async reload () {
      let data = {}
      if (this.$refs && this.$refs.dateRangeFilter) {
        data = this.$refs.dateRangeFilter.params()

        data.fromDate = moment(data.start || '2021-01-01').format('YYYY-MM-DD')
        data.toDate = moment(data.end || '2038-01-01').format('YYYY-MM-DD')

        this.subtitle = dateRangePickerText(data)
      }
      this.$q.loading.show()
      const resp = await this.widget.request('/faas/widget/api/card-issuing/transactions', 'get', data)
      this.$q.loading.hide()
      if (resp) {
        this.list = resp.data.map((m, i) => {
          return {
            icon: this.icons[m.code] || this.icons['Other'],
            color: this.colors[m.code] || this.colors['Other'],
            title: m.description,
            status: m.status,
            amount: (m.code === 'DEBIT' ? -1 : 1) * m.amount,
            time: moment(m.time).format('MMM DD, YYYY • HH:MM')
          }
        })
        if (this.list.length <= 0) {
          this.list = [
            {
              icon: 'mdi-emoticon-neutral-outline',
              color: '#CFE0FE',
              title: 'No recent transactions (' + this.$refs.dateRangeFilter.getText() + ')',
              subtitle: 'Change the date range to view older transactions'
            }
          ]
        }
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>
