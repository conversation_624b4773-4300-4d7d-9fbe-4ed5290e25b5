<template>
  <div class="faas-card-issuing-dialog-view_pin">
    <div class="q-subtitle text-center">View My PIN</div>
    <q-list class="actions-list actions-info-list" no-border separator>
      <q-item v-for="(v, i) of list" :key="i">
        <q-item-main>
          <q-item-tile label>{{ v.label }}</q-item-tile>
          <q-item-tile sublabel>
            <span v-if="v.value">{{ v.value }}</span>
            <a href="javascript:" class="font-14 normal decoration-none"
               @click="send" v-else>
              <q-icon name="mdi-send"></q-icon>
              Send Unlock Code
            </a>
          </q-item-tile>
        </q-item-main>
        <q-item-side v-if="v.value">
          <q-item-tile>
            <q-btn icon="mdi-content-copy"
                   color="primary"
                   @click="c.copyToClipboard(v.value)"
                   round flat></q-btn>
          </q-item-tile>
        </q-item-side>
      </q-item>
    </q-list>
  </div>
</template>

<script>
import { EventHandlerMixin } from '../../../../../common'

export default {
  name: 'faas-card-issuing-dialog-view_pin',
  mixins: [
    EventHandlerMixin('faas-card-issuing-viewPin', 'getPin')
  ],
  props: {
    widget: {
      type: Object,
      required: true
    },
    card: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      list: [
        {
          label: 'Card PIN',
          value: ''
        }
      ]
    }
  },
  methods: {
    async send () {
      this.$root.$emit('show-mex-common-sms-dialog', {
        smsKey: 'faasWidget',
        smsType: 'viewPin'
      })
    },
    async getPin () {
      this.$q.loading.show()
      const resp = await this.widget.request('/faas/widget/api/card-issuing/pin')
      this.$q.loading.hide()
      if (resp) {
        this.list[0].value = resp.data
      }
    }
  }
}
</script>
