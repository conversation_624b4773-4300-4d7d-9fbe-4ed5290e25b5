<template>
  <q-dialog
    class="faas-card-wallet-dialog"
    v-model="visible"
  >
    <template slot="title">
      <div class="mb-5">
        <div class="radius-box">
          <q-icon name="mdi-credit-card" class="font-20"></q-icon>
        </div>
      </div>
      <div class="title">
        <div v-show="step === 'create'">
          <div class="font-18"><b>Card Wallet</b></div>
          <div class="desc font-14">Please enter the following information to add a new payment card to your wallet.</div>
        </div>
        <div v-show="step === 'list'">
          <div class="font-18"><b>My Card Wallet</b></div>
        </div>
      </div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <div v-show="step === 'create'">
        <div class="row mb-20">
          <div class="col-sm-12">
            <q-input autocomplete="no"
                     placeholder="Card Nickname"
                     :error="$v.entity['cardNickname'].$error"
                     @input="$v.entity['cardNickname'].$touch"
                     v-model="entity['cardNickname']">
          <span class="label-item"
                v-if="entity['cardNickname']">Card Nickname</span>
            </q-input>
          </div>
        </div>
        <div class="row mb-20">
          <div class="col-sm-12">
            <q-input autocomplete="no"
                     placeholder="Card Number"
                     :error="$v.entity['cardNumber'].$error"
                     @input="$v.entity['cardNumber'].$touch"
                     v-model="entity['cardNumber']">
          <span class="label-item"
                v-if="entity['cardNumber']">Card Number</span>
            </q-input>
          </div>
        </div>
        <div class="row gutter-form mb-20">
          <div class="col-sm-6">
            <q-input autocomplete="no"
                     placeholder="Expiration Date"
                     :error="$v.entity['expirationDate'].$error"
                     @input="$v.entity['expirationDate'].$touch"
                     v-model="entity['expirationDate']">
              <span class="label-item" v-if="entity['expirationDate']">Expiration Date</span>
            </q-input>
          </div>
          <div class="col-sm-6">
            <q-input autocomplete="no"
                     placeholder="CVV Code"
                     :error="$v.entity['cvvCode'].$error"
                     @input="$v.entity['cvvCode'].$touch"
                     v-model="entity['cvvCode']">
              <span class="label-item" v-if="entity['cvvCode']">CVV Code</span>
            </q-input>
          </div>
        </div>
        <div class="row mb-10">
          <div class="col-sm-12 text-center mt-5">
            <div class="mb-5 radio-label bold">Would you like this to be your primary card?</div>
            <q-radio v-model="entity['primaryCard']"
                     color="blue"
                     :error="$v.entity['primaryCard'].$error"
                     @change="$v.entity['primaryCard'].$touch"
                     val="Yes"
                     label="Yes">
            </q-radio>
            <q-radio v-model="entity['primaryCard']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['primaryCard'].$error"
                     @change="$v.entity['primaryCard'].$touch"
                     val="No"
                     label="No">
            </q-radio>
          </div>
        </div>
      </div>
      <div v-show="step === 'list'" class="list">
        <div class="single-card">
          <q-card class="bg-black text-white mb-10">
            <q-card-main>
              <div class="text-right heart">
                <q-icon name="mdi-cards-heart"></q-icon>
              </div>
              <div class="number text-center mt-25 mb-40 font-16 bold">
                <span class="group mr-20">••••</span>
                <span class="group mr-20">••••</span>
                <span class="group mr-20">••••</span>
                <span class="group">2827</span>
              </div>
              <div class="bottom">
                <div class="left">
                  <div class="name font-12">
                    JAKE BROVDA
                  </div>
                  <div class="date font-12">
                    EXP ••/•• CVV •••
                  </div>
                </div>
                <div class="right">
                  <i>VISA</i>
                  <!--                <i class="fa fa-cc-visa"> </i>-->
                </div>
              </div>
            </q-card-main>
          </q-card>
          <div class="card-name text-center font-16 bold">
            Chase Debit
            <q-icon @click.native="step = 'create'" name="mdi-settings" class="font-16 light-grey ml-10 cursor-pointer"></q-icon>
          </div>
        </div>
        <div class="single-card">
          <q-card class="bg-black text-white mb-10">
            <q-card-main>
              <div class="text-right heart">
                <q-icon name="mdi-cards-heart grey"></q-icon>
              </div>
              <div class="number text-center mt-25 mb-40 font-16 bold">
                <span class="group mr-20">••••</span>
                <span class="group mr-20">••••</span>
                <span class="group mr-20">••••</span>
                <span class="group">2827</span>
              </div>
              <div class="bottom">
                <div class="left">
                  <div class="name font-12">
                    JAKE BROVDA
                  </div>
                  <div class="date font-12">
                    EXP ••/•• CVV •••
                  </div>
                </div>
                <div class="right">
                  <i>VISA</i>
                  <!--                <i class="fa fa-cc-visa"> </i>-->
                </div>
              </div>
            </q-card-main>
          </q-card>
          <div class="card-name text-center font-16 bold">
            Bank of America Debit
            <q-icon @click.native="step = 'create'" name="mdi-settings" class="font-16 light-grey ml-10 cursor-pointer"></q-icon>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div>
        <q-btn v-show="step === 'create'"
               color="primary"
               class="wp-100 mb-20 btn-introduce"
               label="Add New Card"
               @click="save"
        >
        </q-btn>
        <q-btn v-show="step === 'list'"
               color="primary"
               class="wp-100 mb-20 btn-introduce"
               label="Add New Card"
               @click="step = 'create'"
        >
        </q-btn>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../../mixins/Singleton'
import { required } from 'vuelidate/lib/validators'

const validations = {
  entity: {
    'cardNickname': { required },
    'cardNumber': { required },
    'expirationDate': { required },
    'cvvCode': { required },
    'primaryCard': { required }
  }
}

export default {
  name: 'faas-card-wallet-dialog',
  mixins: [
    Singleton
  ],
  components: {
  },
  data () {
    return {
      step: 'list',
      defaultEntity: {
        'cardNickname': '',
        'cardNumber': '',
        'expirationDate': '',
        'cvvCode': '',
        'primaryCard': ''
      },
      currentDetail: {}
    }
  },
  validations,
  computed: {

  },
  methods: {
    show (data) {
      this.step = data.step ? data.step : 'list'
    },
    save () {
      this._hide()
      this.showMessageDialog()
    },
    showMessageDialog () {
      const _this = this
      const type = 'success'
      this.$root.$emit('show-message-dialog', {
        title: type === 'success' ? 'Card Added Successfully' : 'Card Failed',
        type: type,
        message: type === 'success' ? 'Your Visa card ending in 2827 was successfully added to your card wallet.\n' +
          'You may now use it to make payments.'
          : 'There was an error when capturing your card information. Please make sure all the information was entered correctly.',
        okLabel: type === 'success' ? 'Go to Wallet' : 'Try Adding Card Again',
        callback: text => {
          if (text) {
            _this.$root.$emit('show-faas-card-wallet-dialog', { step: type === 'success' ? 'list' : 'create' })
          } else {
          }
        }
      })
    },
    edit () {
      this.step = 'create'
      this.entity.bankNickname = this.currentDetail.name
    }
  }
}
</script>

<style lang="scss">
@font-face {
  font-family: "OCR A Extended";
  src: url("https://account.usunlocked.com/static/usu/ocraext.ttf") format("truetype");
}

.faas-card-wallet-dialog {
  .modal-content {
    width: 400px;
    .radius-box {
      .q-icon {
        color: var(--span-color-primary);
      }
    }
    .amount {
      color: #2ec674;
    }
    .light-grey {
      color: #939493;
    }
    .modal-body {
      text-align: left;
      .label-item {
        position: absolute;
        top: -10px;
        left: 10px;
        background: #fff;
        padding: 0 5px;
        color: #2b2b2b;
        font-size: 10px;
        font-weight: bold;
      }
      .list {
        margin: auto 38px;
      }
      .single-card {
        margin-bottom: 24px;
        &:last-child {
          margin-bottom: 0;
        }
        .card-name {
          .q-icon {
            border: 1px solid #e9e9e9;
            border-radius: 5px;
            padding: 3px;
            background-color: #f9f9f9;
          }
        }
      }
      .q-card {
        .q-card-main {
          padding: 20px;
          .heart {
            .q-icon {
              padding: 4px;
              background: white;
              color: #d76262;
              border-radius: 5px;
              font-size: 17px;
              &.grey {
                color: #959595;
              }
            }
          }
          .number {
            letter-spacing: 3px;
            .group {
            }
          }
          .bottom {
            display: flex;
            .left {
              flex: 1;
            }
            .right {
              align-self: flex-end;
              font-size: 24px;
              font-weight: bold;
            }
          }
        }
      }
    }
    .modal-buttons {
      display: block;
      button {
        margin-left: 0 !important;
        &.btn-introduce {
          background: var(--span-color-primary) !important;
        }
      }
    }
  }
}
</style>
