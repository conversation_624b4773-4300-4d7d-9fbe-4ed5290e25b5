<template>
  <div id="faas_widgets_full">
    <CardIssuingWidget></CardIssuingWidget>
    <KYCWidget></KYCWidget>
    <BankWidget></BankWidget>
    <InternalBankAccountWidget></InternalBankAccountWidget>
    <CardWalletWidget></CardWalletWidget>

    <MessageDialog></MessageDialog>
  </div>
</template>

<script>
import CardIssuingWidget from './card_issuing'
import KYCWidget from './kyc'
import BankWidget from './bank'
import InternalBankAccountWidget from './internal_bank_account'
import MessageDialog from '../../../../components/MessageDialog'
import CardWalletWidget from './card_wallet'

export default {
  name: 'FaasWidgetsFull',
  components: {
    CardIssuingWidget,
    KYCWidget,
    BankWidget,
    InternalBankAccountWidget,
    MessageDialog,
    CardWalletWidget
  },
  mounted () {
    document.title = 'Widget'

    const type = this.$route.params.type
    if (type) {
      const key = type.replace('_', '-')
      this.$root.$emit(`show-faas-${key}-dialog`)
    }
  }
}
</script>

<style lang="scss">
#faas_widgets_full {}
</style>
