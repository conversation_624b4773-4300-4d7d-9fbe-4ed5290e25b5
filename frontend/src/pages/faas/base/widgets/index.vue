<template>
  <q-page class="faas_widgets_index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <q-btn color="primary"
             no-caps
             @click="bankWidget"
             class="ml-10"
             label="Bank Widget"></q-btn>
      <q-btn color="primary"
             no-caps
             @click="internalBankAccountWidget"
             class="ml-10"
             label="Internal Bank Account Widget"></q-btn>
      <q-btn color="primary"
             no-caps
             @click="cardWalletWidget"
             class="ml-10"
             label="Card Wallet Widget"></q-btn>
    </div>
    <BankWidget></BankWidget>
    <InternalBankAccountWidget></InternalBankAccountWidget>
    <CardWalletWidget></CardWalletWidget>

    <MessageDialog></MessageDialog>
  </q-page>
</template>

<script>
import PageMixin from '../../../../mixins/PageMixin'
import BankWidget from './bank'
import InternalBankAccountWidget from './internal_bank_account'
import MessageDialog from '../../../../components/MessageDialog'
import CardWalletWidget from './card_wallet'
export default {
  mixins: [
    PageMixin
  ],
  components: {
    BankWidget,
    InternalBankAccountWidget,
    MessageDialog,
    CardWalletWidget
  },
  data () {
    return {
      title: 'Widgets Preview'
    }
  },
  methods: {
    bankWidget () {
      this.$root.$emit('show-faas-bank-dialog')
    },
    internalBankAccountWidget () {
      this.$root.$emit('show-faas-iba-dialog')
    },
    cardWalletWidget () {
      this.$root.$emit('show-faas-card-wallet-dialog')
    }
  }
}
</script>
