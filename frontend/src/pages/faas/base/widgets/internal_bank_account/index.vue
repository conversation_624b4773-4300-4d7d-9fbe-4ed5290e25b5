<template>
  <q-dialog
    class="faas-internal-bank-account-dialog"
    v-model="visible"
  >
    <template slot="title">
      <div class="mb-5">
        <div class="radius-box">
          <q-icon name="mdi-bank" class="font-20"></q-icon>
        </div>
      </div>
      <div class="title">
        <div v-show="step === 'create'">
          <div class="font-18"><b>Create Internal Bank Account</b></div>
          <div class="desc font-14">Please enter the following information to create a new internal bank account.</div>
        </div>
        <div v-show="step === 'list'">
          <div class="font-18"><b>Internal Bank Accounts</b></div>
        </div>
        <div v-show="step === 'detail'">
          <div class="font-18">
            <b>{{ currentDetail.name }}</b>
            <q-icon name="mdi-square-edit-outline"
                    color="faded"
                    @click.native="edit()"
                    class="font-15 ml-5 cursor-pointer"></q-icon>
          </div>
          <div class="amount font-18 bold">
            {{ currentDetail.amount }}
          </div>
          <div class="light-grey font-12">
            {{ currentDetail.status }}
          </div>
        </div>
      </div>
      <q-btn class="back" round flat
             v-if="step === 'detail'"
             @click="step = 'list'" icon="mdi-arrow-left"></q-btn>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <div v-show="step === 'create'">
        <q-input autocomplete="no"
                 placeholder="Bank Nickname"
                 :error="$v.entity['bankNickname'].$error"
                 @input="$v.entity['bankNickname'].$touch"
                 v-model="entity['bankNickname']">
          <span class="label-item"
                v-if="entity['bankNickname']">Bank Nickname</span>
        </q-input>
      </div>
      <div v-show="step === 'list'" class="list">
        <q-list no-border class="pt-0">
          <q-item link
                  v-for="(item, key) in accountList"
                  :key="key"
                  @click.native="detail(item)"
          >
            <q-item-side icon="mdi-bank" class="font-20 bank"></q-item-side>
            <q-item-main>
              <q-item-tile label class="font-16 bold mb-5">{{ item.name }}</q-item-tile>
              <q-item-tile label class="amount bold font-18">{{ item.amount }}</q-item-tile>
              <q-item-tile sublabel>{{ item.status }}</q-item-tile>
            </q-item-main>
            <q-item-side icon="mdi-arrow-right" class="icon-arrow radius-box"> </q-item-side>
          </q-item>
        </q-list>
      </div>
      <div v-show="step === 'detail'" class="detail">
        <div class="account-info text-center">
          <div class="font-14 mb-10"><b>Account Information</b></div>
          <div class="row">
            <div class="col-sm-6">
              <div class="font-12 mb-5"><b>223 419 289</b></div>
              <div class="light-grey font-12">Account Number <span><i class="mdi mdi-eye font-12 ml-5"> </i></span></div>
            </div>
            <div class="col-sm-6">
              <div class="font-12 mb-5"><b>••• ••• ••• 131</b></div>
              <div class="light-grey font-12">Routing Number <span><i class="mdi mdi-eye-off font-12 ml-5"> </i></span></div>
            </div>
          </div>
        </div>
        <div class="recent-transactions mt-30">
          <div class="font-14"><b>Recent Transactions</b></div>
          <q-list no-border class="pt-0">
            <q-item link
                    class="mb-10 mt-10"
                    v-for="(item, key) in transactionList"
                    :key="key"
            >
              <q-item-side class="text-center light-grey mr-10">
                <div class="font-12 month">{{ item.month }}</div>
                <div class="font-14"><b>{{ item.day }}</b></div>
              </q-item-side>
              <q-item-side class="logo font-20">
                <img src="/static/faas/img/internal_bank_widget_amazon.png"/>
              </q-item-side>
              <q-item-main>
                <q-item-tile label class="font-14 bold mb-5"><b>{{ item.name }}</b></q-item-tile>
                <q-item-tile sublabel class="font-12 light-grey">{{ item.desc }}</q-item-tile>
              </q-item-main>
              <q-item-side class="text-right">
                <div class="font-14 fee"><b>{{ item.fee }}</b></div>
                <div class="light-grey font-12">{{ item.amount }}</div>
              </q-item-side>
            </q-item>
          </q-list>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div>
        <q-btn v-show="step === 'create'"
               color="primary"
               class="wp-100 mb-20 btn-introduce"
               label="Continue"
               @click="save"
        >
        </q-btn>
        <q-btn v-show="step === 'list'"
               color="primary"
               class="wp-100 mb-20 btn-introduce"
               label="Create New Internal Bank Account"
               @click="step = 'create'"
        >
        </q-btn>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../../mixins/Singleton'
import { required } from 'vuelidate/lib/validators'

const validations = {
  entity: {
    'bankNickname': { required }
  }
}

export default {
  name: 'faas-iba-dialog',
  mixins: [
    Singleton
  ],
  components: {
  },
  data () {
    return {
      step: 'list',
      defaultEntity: {
        'bankNickname': ''
      },
      accountList: [
        {
          name: "John's Checking",
          amount: '$1,234.56',
          status: 'Available Balance'
        },
        {
          name: "Tracy's Checking",
          amount: '$1,234.56',
          status: 'Available Balance'
        },
        {
          name: "somebody's Checking",
          amount: '$1,234.56',
          status: 'Available Balance'
        },
        {
          name: "John's Checking",
          amount: '$1,234.56',
          status: 'Available Balance'
        },
        {
          name: "Tracy's Checking",
          amount: '$1,234.56',
          status: 'Available Balance'
        },
        {
          name: "somebody's Checking",
          amount: '$1,234.56',
          status: 'Available Balance'
        }
      ],
      transactionList: [
        {
          name: 'Amazon',
          desc: 'Debit',
          fee: '-$1.25',
          amount: '$1,234.56',
          month: 'MAY',
          day: '10'
        },
        {
          name: 'Uber',
          desc: 'Debit',
          fee: '-$1.25',
          amount: '$1,234.56',
          month: 'MAY',
          day: '10'
        },
        {
          name: 'Netflix',
          desc: 'Debit',
          fee: '-$1.25',
          amount: '$1,234.56',
          month: 'MAY',
          day: '10'
        },
        {
          name: 'Amazon',
          desc: 'Debit',
          fee: '-$1.25',
          amount: '$1,234.56',
          month: 'MAY',
          day: '10'
        },
        {
          name: 'Uber',
          desc: 'Debit',
          fee: '-$1.25',
          amount: '$1,234.56',
          month: 'MAY',
          day: '10'
        },
        {
          name: 'Netflix',
          desc: 'Debit',
          fee: '-$1.25',
          amount: '$1,234.56',
          month: 'MAY',
          day: '10'
        }
      ],
      currentDetail: {}
    }
  },
  validations,
  computed: {

  },
  methods: {
    show (data) {
      this.step = data.step ? data.step : 'list'
    },
    save () {
      const _this = this
      this._hide()
      const type = 'success'
      this.$root.$emit('show-message-dialog', {
        title: type === 'success' ? 'Bank Added Successfully' : 'Adding New Bank has Failed',
        type: type,
        message: type === 'success' ? 'John’s Account ending in 9131 was successfully added to your internal bank vault. You may now use it to make payments.'
          : 'There was an error when creating your new internal bank. Please make sure all the information was entered correctly.',
        okLabel: type === 'success' ? 'Go to Internal Bank Vault' : 'Try Creating Bank Again',
        callback: text => {
          if (text) {
            _this.$root.$emit('show-faas-iba-dialog', { step: type === 'success' ? 'list' : 'create' })
          } else {

          }
        }
      })
    },
    edit () {
      this.step = 'create'
      this.entity.bankNickname = this.currentDetail.name
    },
    detail (item) {
      this.step = 'detail'
      this.currentDetail = item
    },
    goToInternalBankVault () {
      this.step = 'list'
    },
    createBank () {
      this.step = 'create'
    }
  }
}
</script>

<style lang="scss">
@font-face {
  font-family: "OCR A Extended";
  src: url("https://account.usunlocked.com/static/usu/ocraext.ttf") format("truetype");
}

.faas-internal-bank-account-dialog {
  .modal-content {
    width: 400px;
    .radius-box {
      .q-icon {
        color: var(--span-color-primary);
      }
    }
    .amount {
      color: #2ec674;
    }
    .light-grey {
      color: #939493;
    }
    .modal-body {
      text-align: left;
      .label-item {
        position: absolute;
        top: -10px;
        left: 10px;
        background: #fff;
        padding: 0 5px;
        color: #2b2b2b;
        font-size: 10px;
        font-weight: bold;
      }
      .q-item {
        border-radius: 10px;
      }
      .list {
        .q-list {
          max-height: 350px;
          overflow: scroll;
        }
        .q-item {
          padding: 20px 15px;
          margin: 20px 0 0;
          /*border: 1px solid var(--span-color-primary);*/
          border: 1px solid #ccd0ec;
          &:first-child {
            margin-top: 0;
          }
          .bank {
            .q-icon {
              font-size: 20px;
              color: var(--span-color-primary);
            }
          }
        }
      }
      .icon-arrow.radius-box {
        width: 20px;
        height: 20px;
        min-width: unset;
        .q-icon {
          font-size: 12px;
          color: var(--span-color-primary);
        }
      }
      .detail {
        .q-list {
          max-height: 235px;
          overflow: scroll;
        }
        .month {
          font-size: 10px !important;
        }
        .logo {
          img {
            border-radius: 50%;
          }
          width: 40px;
          height: 40px;
        }
        .fee {
          color: #e7595e;
        }
      }
    }
    .modal-buttons {
      display: block;
      button {
        margin-left: 0 !important;
        &.btn-introduce {
          background: var(--span-color-primary) !important;
        }
      }
    }
  }
}
</style>
