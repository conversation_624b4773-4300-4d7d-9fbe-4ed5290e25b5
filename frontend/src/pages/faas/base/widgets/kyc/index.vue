<template>
  <q-dialog class="faas-kyc-dialog faas-widget-dialog"
            prevent-close
            v-model="visible">
    <template slot="title">
      <div class="mb-5">
        <div v-if="step ==='init'"
             class="radius-box">
          <q-icon name="mdi-account-check"
                  class="font-20"></q-icon>
        </div>
        <div v-else-if="step ==='done'"
             class="radius-box done-box">
          <q-icon name="mdi-check"
                  class="font-20"></q-icon>
        </div>
      </div>
      <div class="font-18 mb-2"
           v-if="step ==='init'">Identity Verification</div>
      <div class="font-18 mb-2"
           v-else-if="step ==='done'">Identity Verification Successful</div>
      <div v-if="step ==='init'"
           class="font-14 normal">
        Please fill out the following information to verify the identity of <strong>{{ fullName }}</strong>.
      </div>
      <div v-else-if="step ==='done'"
           class="font-14 normal">Your documents have successfully been uploaded and your identity has been verified.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template v-if="step ==='init'"
              slot="body">
      <div class="col-sm-12 mt-15">
        <q-select autocomplete="no"
                  :options="docTypeList"
                  filter
                  autofocus-filter
                  float-label="Document Type"
                  :error="$v.entity['docType'].$error"
                  @input="$v.entity['docType'].$touch"
                  v-model="entity['docType']">
        </q-select>
      </div>
      <div class="col-sm-12 mt-15 select-country">
        <p>My ID’s country is different from my country of residence</p>
        <q-toggle @input="changeCountry"
                  v-model="entity['selectCountry']" />
      </div>
      <div v-if="entity['selectCountry']"
           class="col-sm-12 mt-15">
        <q-select autocomplete="no"
                  :options="countries"
                  filter
                  autofocus-filter
                  float-label="Country on ID"
                  v-model="entity['country']">
        </q-select>
      </div>
      <div class="row">
        <div class="col-12 text-center file-section pl-0"
             :class="bothSide ? 'both-side' : ''"
             v-show="showSide(side)"
             v-for="(side, index) in ['front', 'back']"
             :key="index">
          <input type="file"
                 class="drop-file-area-input hide"
                 accept="image/*"
                 @change="selectedFile($event.target)"
                 name="file" />
          <input type="hidden"
                 class="file-attachment-id"
                 :name="'attachment_id' + (index ? '2' : '')">
          <div class="drop-file-area"
               @click="chooseFile($event.target)">
            <q-icon v-if="!index && entity['docType'] === 'PSP'"
                    name="mdi-passport"></q-icon>
            <q-icon v-else-if="!index && entity['docType'] === 'DL'"
                    name="mdi-card-bulleted-outline"></q-icon>
            <q-icon v-else
                    name="mdi-card-text-outline"></q-icon>
            <div class="tip pe-none">
              Click here or drag a file here to upload a clear image of the <strong>{{ side }}</strong> of the document.<br />
              <small>(Accepted file formats: .JPG, .PNG)</small>
            </div>
          </div>
          <div class="selected-file-area mv-20 hide">
            <img class="preview"
                 :src="''"
                 alt="" />
            <div class="elements">
              <a href="javascript:"
                 @click="uploadFile($event.target)"
                 class="bold">
                <q-icon name="mdi-cloud-upload-outline"></q-icon> Click to Upload
              </a>
              <span class="tip"></span>
              <a href="javascript:"
                 @click="rotatePicture($event.target, -90)"
                 class="ml-auto mr-10">
                <q-icon name="mdi-rotate-left"></q-icon>
                <q-tooltip>Rotate left</q-tooltip>
              </a>
              <a href="javascript:"
                 @click="rotatePicture($event.target, 90)"
                 class="mr-10">
                <q-icon name="mdi-rotate-right"></q-icon>
                <q-tooltip>Rotate right</q-tooltip>
              </a>
              <a href="javascript:"
                 @click="removeFile($event.target)"
                 class="red">
                <q-icon name="mdi-close"></q-icon> Remove
              </a>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <q-btn v-if="step ==='init'"
             class="col-sm-12 verify-btn"
             :disable="$v.$invalid"
             @click="submit()">Verify my Identity</q-btn>
      <q-btn v-else-if="step ==='done'"
             class="col-sm-12 verify-btn hide-in-iframe"
             :disable="$v.$invalid"
             @click="_hide">Close</q-btn>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../../mixins/Singleton'
import IdScanMixin from '../../../../../mixins/IdScanMixin'
import { required } from 'vuelidate/lib/validators'
import { notifyResponse } from '../../../../../common'
import $ from 'jquery'
import FaasWidgetMixin from '../../../../../mixins/faas/FaasWidgetMixin'

const validations = {
  entity: {
    'docType': { required },
    'selectCountry': {},
    'country': {}
  }
}

export default {
  name: 'faas-kyc-dialog',
  mixins: [
    Singleton,
    IdScanMixin,
    FaasWidgetMixin
  ],
  data () {
    return {
      step: 'init',
      defaultEntity: {
        'docType': null,
        'selectCountry': false,
        'country': null
      },
      docTypeList: [
        {
          value: 'PSP',
          label: 'Passport',
          requirement: 'FRONT'
        },
        {
          value: 'DL',
          label: `Driver's License`,
          requirement: 'BOTH'
        },
        {
          value: 'ID',
          label: `ID Card`,
          requirement: 'BOTH'
        }
      ],
      attachment_id: null,
      attachment_id2: null
    }
  },
  validations,
  computed: {
    bothSide () {
      const def = this.docTypeList.find(dt => {
        return dt.value === this.entity['docType']
      })
      return def && def.requirement === 'BOTH'
    }
  },
  watch: {
    file () {
      this.attachment = null
    }
  },
  methods: {
    show () {
      this.step = 'init'
      this.setupDnd()
      this.updatePictures()
      this.loadUser()
    },
    changeCountry (val) {
      if (!val) {
        this.entity.country = null
      }
    },
    async submit () {
      this.attachment_id = $(this.$el).find('.file-attachment-id:eq(0)').val()
      this.attachment_id2 = $(this.$el).find('.file-attachment-id:eq(1)').val()
      if (this.$v.$invalid) {
        return
      }
      if (!this.validateForm(this.$el)) {
        return
      }
      if (this.entity.selectCountry && !this.entity.country) {
        notifyResponse('Please select a country for your ID.')
        return
      }
      this.$q.loading.show()
      const resp = await this.request(`/faas/widget/api/kyc/id-scan`, 'post', {
        type: this.entity.docType,
        attachment_id: this.attachment_id,
        attachment_id2: this.attachment_id2,
        country: this.entity.country
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.step = 'done'
      }
    },
    showSide (side) {
      const def = this.docTypeList.find(dt => {
        return dt.value === this.entity['docType']
      })
      if (!def) {
        return false
      }
      if (def.requirement === 'BOTH') {
        return true
      }
      return def.requirement.toLowerCase() === side
    }
  }
}
</script>

<style lang="scss">
@font-face {
  font-family: "OCR A Extended";
  src: url("https://account.usunlocked.com/static/usu/ocraext.ttf")
    format("truetype");
}

.faas-kyc-dialog {
  .modal-content {
    width: 400px;

    .modal-body {
      text-align: left;
    }
    .radius-box {
      background: rgba($color: var(—q-primary-color), $alpha: 0.1);
      color: rgba($color: var(—q-primary-color), $alpha: 0.1) !important;
    }
    .drop-file-area .q-icon,
    .radius-box .q-icon {
      color: var(—q-primary-color);
    }
    .done-box {
      background: rgba($color: #2ec674, $alpha: 0.1);
      color: rgba($color: #2ec674, $alpha: 0.1) !important;
      .q-icon {
        color: #2ec674;
      }
    }
    .q-select,
    .q-input {
      height: 50px;
    }
    .label-item {
      position: absolute;
      top: -10px;
      left: 10px;
      background: #fff;
      padding: 0 5px;
      color: #2b2b2b;
    }
    .q-select .q-if-label-above {
      left: 10px;
      top: 8px;
      background: #fff;
      padding: 0 5px;
      color: #2b2b2b;
      line-height: 21px;
    }
    .verify-btn {
      height: 50px;
      background-color: var(--span-color-primary);
      color: #fff;
      text-transform: none;
    }
    .select-country {
      display: flex;
      align-items: center;
      padding: 0px 15px;
      p {
        margin: 0;
      }
      .q-toggle .q-option-inner {
        height: 25px;
        width: 40px;
      }
      .q-toggle-base {
        height: 16px;
      }
      .q-toggle-handle {
        height: 25px;
        width: 25px;
        line-height: 25px;
      }
    }
  }
}
</style>
