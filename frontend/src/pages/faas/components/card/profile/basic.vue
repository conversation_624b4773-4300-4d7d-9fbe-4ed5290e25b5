<template>
  <q-card id="mex__employers__profile_basic"
          class="high-card">
    <q-card-main>
      <div class="radius-box bg-positive"
           v-if="entity['Status'] === 'Active'">
        <q-icon name="mdi-lock-open-outline"></q-icon>
      </div>
      <div class="radius-box bg-negative"
           v-else>
        <q-icon name="mdi-lock-outline"></q-icon>
      </div>

      <div class="text-center mb-20">
        <div class="avatar mt-5">
          <img :src="'/static/img/avatar.png'"
               alt="">
        </div>
        <div class="font-20 bold mt-5">{{ $c.fullName(entity) }}</div>
        <div class="text-faded">{{ entity['CURP ID'] }}</div>

        <div class="text-faded mt-15">Balance</div>
        <div class="font-16 heavy text-black mt--3">
          <q-icon name="mdi-refresh"
                  color="positive"
                  class="mr-5 hidden-only"></q-icon>
          <span class="va-m">{{ entity['Balance'] | moneyFormat }}</span>
          <q-icon name="mdi-refresh"
                  color="positive"
                  @click.native="$emit('reload')"
                  class="ml-5 pointer">
            <q-tooltip>Refresh</q-tooltip>
          </q-icon>
        </div>

        <q-chip class="font-13 mt-5"
                :class="statusClass(entity['Status'])">
          {{ entity['Status'] }}
        </q-chip>
      </div>
      <table class="fields-table">
        <tr v-for="(r, i) in fields"
            :key="i">
          <th>{{ r }}</th>
          <td>{{ entity[r] }}</td>
        </tr>
      </table>
    </q-card-main>
  </q-card>
</template>

<script>
import MexMemberMixin from '../../../../../mixins/mex/MexMemberMixin'

export default {
  name: 'mex-employers-profile-basic',
  mixins: [
    MexMemberMixin
  ],
  props: {
    entity: {
      type: Object
    }
  },
  data () {
    return {
      fields: [
        'Email', 'Phone', 'Role'
      ]
    }
  }
}
</script>

<style lang="scss">
#mex__employers__profile_basic {
  .avatar {
    width: 130px;
    height: 130px;
    background-color: #eee;
    border-radius: 20px;
    margin: 0 auto;
    overflow: hidden;
  }

  .radius-box {
    border: none;
    color: white;
    position: absolute;
    right: 16px;
    top: 16px;
    font-size: 18px;
    padding: 4px 5px;
    line-height: 1em;

    &.bg-positive {
      background-color: #3dd598 !important;
    }
  }

  .fields-table {
    th {
      min-width: 60px !important;
    }
  }
}
</style>
