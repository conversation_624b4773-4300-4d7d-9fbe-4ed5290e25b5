<template>
  <q-dialog class="mex-member-card-status-dialog"
            v-model="visible">
    <template slot="title">
      <div class="mb-5">
        <div class="radius-box">
          <q-icon name="mdi-credit-card"
                  class="font-22"></q-icon>
        </div>
      </div>
      <div class="font-16 mb-2">Change Card Status</div>
      <div class="font-12 normal">Change the card status of <strong notranslate="">{{ $c.fullName(entity) }}</strong></div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <q-select float-label="Card Status"
                autocomplete="no"
                class="mt-20 mb-40"
                :options="statuses"
                :error="$v.entity['Card Status'].$error"
                @blur="$v.entity['Card Status'].$touch"
                v-model="entity['Card Status']"></q-select>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn label="Submit"
               no-caps
               color="positive"
               @click="submit" />
        <q-btn label="Close"
               no-caps
               color="grey-3"
               class="ml-0 mt-8"
               text-color="tertiary"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../../mixins/Singleton'
import { required } from 'vuelidate/lib/validators'
import { cardStatusesByAccountNumber, notifyForm, request, toSelectOptions } from '../../../../../common'

export default {
  name: 'mex-member-card-status-dialog',
  mixins: [
    Singleton
  ],
  props: {
    url: {
      type: String
    }
  },
  computed: {
    statuses () {
      if (!this.entity) {
        return []
      }
      const all = cardStatusesByAccountNumber(this.entity['Barcode Number'] || '')
      if (!all) {
        return []
      }
      return toSelectOptions(all)
    }
  },
  validations: {
    entity: {
      'Card Status': {
        required
      }
    }
  },
  methods: {
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      const data = {
        cardStatus: this.entity['Card Status']
      }
      // let url = `members/${this.entity['Member ID']}`
      // if (!this.entity['Member ID'] && this.entity['Employer ID']) {
      //   url = `employers/${this.entity['Employer ID']}`
      // }
      let id = this.entity['Member ID']
      if (this.entity['Employer ID']) {
        id = this.entity['Employer ID']
      } else if (this.entity['Client ID']) {
        id = this.entity['Client ID']
      }
      const resp = await request(`/admin/${this.url}/${id}/change-card-status`, 'post', data)
      this.$q.loading.hide()
      if (resp.success) {
        this.$root.$emit('reload-mex-members-profile')
        this.$root.$emit('reload-mex-employers-profile')
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.mex-member-card-status-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
