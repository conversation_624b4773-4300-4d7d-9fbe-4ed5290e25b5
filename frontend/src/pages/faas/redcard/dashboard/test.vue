<template>
  <Card title="Red Card Sample Card"
        ref="card"
        @reload="reload"
        :report-url="reportUrl">
    <div class="p-15">
      Another amount: {{ amount | moneyFormat }}
    </div>
  </Card>
</template>

<script>
import Card from '../../generic/dashboard/card'
import { request } from '../../../../common'

export default {
  name: 'RedCardDashboardCardTest',
  components: {
    Card
  },
  data () {
    return {
      graphName: 'sample',
      requestUrl: `/admin/red-card/dashboard/test/data`,
      reportUrl: null,
      amount: 0
    }
  },
  methods: {
    async reload (cb) {
      const resp = await request(this.requestUrl, 'get', {
        ...this.params
      })
      cb && cb(resp)
      if (resp.success) {
        this.onResponseData(resp.data)
      }
    },
    onResponseData (data) {
      this.amount = data.amount
    }
  }
}
</script>
