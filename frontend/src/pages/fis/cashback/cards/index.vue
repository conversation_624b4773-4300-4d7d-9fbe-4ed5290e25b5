<template>
  <q-page id="fis__cashback_cards__index_page"
          class="fis_page fis_report_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
      <div class="fun-group">
        <q-btn label="Opt-In cards (test)"
               color="primary"
               no-caps
               v-if="!$c.isLive()"
               class="btn-sm"
               @click="optIn"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm mb-15">
        <div class="col-sm-2-5">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-chart-bar"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-5">
                      <small class="super">Count</small>
                      <div class="value">{{ quickTotal }}</div>
                    </div>
                    <div class="col-7">
                      <small class="super">Percent</small>
                      <div class="value">100%</div>
                    </div>
                  </div>
                  <div class="description">Total Cards</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-2-5"
             v-for="s in statuses"
             :key="s.key">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-chart-bar"
                        :color="s.color"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-5">
                      <small class="super">Count</small>
                      <div class="value">{{ quick[s.key] || 0 }}</div>
                    </div>
                    <div class="col-7">
                      <small class="super">Percent</small>
                      <div class="value">{{ $c.percent(quick[s.key], quickTotal, 0) }}</div>
                    </div>
                  </div>
                  <div class="description">{{ s.label }} Cards</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }}</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Cashback Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row)">
                {{ props.row['Cashback Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Reward Purse Balance'">
              <span>{{ _.get(props.row, col.field) }}</span>
              <a href="javascript:"
                 class="link ml-5 font-14"
                 @click="refreshBalance(props.row)">
                <q-icon :name="props.row.loading ? 'mdi-loading' : 'mdi-refresh'"
                        :class="{'mdi-spin': props.row.loading}"></q-icon>
              </a>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'opt-out')"
                          v-if="props.row['Cashback Status'] !== 'Cancelled'">
                    <q-item-main>Opt-Out</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'opt-in')"
                          v-if="props.row['Cashback Status'] === 'Cancelled' && !$c.isLive()">
                    <q-item-main>Opt-In (test)</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'hold')"
                          v-if="props.row['Cashback Status'] === 'Active'">
                    <q-item-main>Hold</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'deactivate')"
                          v-if="props.row['Cashback Status'] === 'Active'">
                    <q-item-main>Cancel</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'resume')"
                          v-if="props.row['Cashback Status'] === 'On Hold'">
                    <q-item-main>Resume</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'activate')"
                          v-if="props.row['Cashback Status'] === 'Inactive'">
                    <q-item-main>Activate</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="loginMicroSite(props.row)"
                          v-if="['Active', 'Inactive', 'On Hold'].includes(props.row['Cashback Status'])">
                    <q-item-main>Login Micro-site</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import { generateColumns, notify, request, toSelectOptions } from '../../../../common'

export default {
  name: 'fis-cashback-cards',
  mixins: [
    ListPageMixin,
    FreezeColumnMixin
  ],
  data () {
    return {
      title: 'Opt-in Cards',
      downloadUrl: `/admin/fis/cashback/cards/export`,
      requestUrl: `/admin/fis/cashback/cards/list`,
      filtersUrl: `/admin/fis/cashback/cards/filters`,
      columns: generateColumns([
        'PAN Proxy', 'Opt-In Date', 'Cashback Program ID', 'Cashback Program Name',
        'Pending Transaction Count', 'Pending Dollars Total', 'Avg Pending $',
        'Rewarded Transaction Count', 'Rewarded Dollars Total', 'Avg Rewarded $',
        'Cancelled Transaction Count', 'Cancelled Dollars Total', 'Avg Cancelled $',
        'Reward Purse Balance', 'Cashback Status', 'Action'
      ]),
      freezeColumn: 0,
      freezeColumnRight: 3,
      filterOptions: [
        {
          value: 'filter[a.issuerClientId=]',
          label: 'Program',
          options: [],
          source: 'clients',
          search: true
        },
        {
          value: 'filter[a.optInAt]',
          label: 'Opt-In Date',
          range: [
            {
              value: 'start',
              type: 'date'
            }, {
              value: 'end',
              type: 'date'
            }
          ]
        },
        {
          value: 'filter[a.panProxyNumber=]',
          label: 'PAN Proxy'
        },
        {
          value: 'filter[a.cashbackStatus=]',
          label: 'Cashback Status',
          options: toSelectOptions([
            'Active', 'Inactive', 'On Hold', 'Cancelled'
          ])
        }
      ],
      statuses: [
        { key: 'active', color: 'positive', label: 'Active' },
        { key: 'inactive', color: 'orange', label: 'Inactive' },
        { key: 'onHold', color: 'purple', label: 'On Hold' },
        { key: 'cancelled', color: 'negative', label: 'Cancelled' }
      ]
    }
  },
  computed: {
    quickTotal () {
      const quick = this.quick
      return (quick.active || 0) + (quick.inactive || 0) + (quick.onHold || 0) + (quick.cancelled || 0)
    },
    visibleColumns () {
      return this.columns
    }
  },
  methods: {
    statusClass (row) {
      const status = row['Cashback Status']
      return {
        'Active': 'positive',
        'Inactive': 'orange',
        'On Hold': 'purple',
        'Cancelled': 'negative'
      }[status] || 'dark'
    },
    optIn () {
      this.$q.dialog({
        title: 'Opt-in Cards (test)',
        message: 'Search a card (proxy number) in the monetary activity report, and select the `Opt-In Cashback` action.',
        cancel: true
      }).then(() => {
        this.$router.push('/j/fis/report/monetary')
      }).catch(() => {})
    },
    async changeStatus (row, action) {
      this.$q.loading.show()
      const resp = await request(`/admin/fis/cashback/cards/change-status/${action}`, 'post', row)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
        this.reload()
      }
    },
    async refreshBalance (row) {
      row.loading = true
      const resp = await request(`/admin/fis/cashback/cards/refresh-balance/${row['ID']}`, 'post')
      row.loading = false
      if (resp.success) {
        row['Reward Purse Balance'] = resp.data
      }
    },
    loginMicroSite (row) {
      window.open(`/admin/fis/cashback/cards/login-micro-site/${row['ID']}`, '_blank')
    }
  }
}
</script>
