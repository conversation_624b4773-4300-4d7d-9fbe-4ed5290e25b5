<template>
  <q-page id="fis__cashback_transactions__index_page"
          class="fis_page fis_report_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-4">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-chart-bar"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-4">
                      <small class="super">Count</small>
                      <div class="value">{{ (quick.pendingCount || 0) + (quick.issuedCount || 0) }}</div>
                    </div>
                    <div class="col-4">
                      <small class="super">Amount</small>
                      <div class="value">{{ ((quick.pendingAmount || 0) + (quick.issuedAmount || 0)) | moneyFormat }}</div>
                    </div>
                    <div class="col-4">
                      <small class="super">Avg</small>
                      <div class="value">{{ $c.avg((quick.pendingAmount || 0) + (quick.issuedAmount || 0), (quick.pendingCount || 0) + (quick.issuedCount || 0)) | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">Total Cashback Rewards</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-4">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-history"
                        color="warning"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-4">
                      <small class="super">Count</small>
                      <div class="value">{{ quick.pendingCount || 0 }}</div>
                    </div>
                    <div class="col-4">
                      <small class="super">Amount</small>
                      <div class="value">{{ (quick.pendingAmount || 0) | moneyFormat }}</div>
                    </div>
                    <div class="col-4">
                      <small class="super">Avg</small>
                      <div class="value">{{ $c.avg(quick.pendingAmount || 0, quick.pendingCount || 0) | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">Pending Cashback Rewards</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-4">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-checkbox-marked-circle-outline"
                        color="positive"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-4">
                      <small class="super">Count</small>
                      <div class="value">{{ quick.issuedCount || 0 }}</div>
                    </div>
                    <div class="col-4">
                      <small class="super">Amount</small>
                      <div class="value">{{ (quick.issuedAmount || 0) | moneyFormat }}</div>
                    </div>
                    <div class="col-4">
                      <small class="super">Avg</small>
                      <div class="value">{{ $c.avg(quick.issuedAmount || 0, quick.issuedCount || 0) | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">Issued Cashback Rewards</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-15"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }}</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Reward Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row)">
                {{ props.row['Reward Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="commonAction(props.row, 'reward')"
                          v-if="['Pending', 'Partially Issued', 'Reversed', 'Cancelled'].includes(props.row['Reward Status'])">
                    <q-item-main>Issue Reward</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="cancel(props.row)"
                          v-if="['Pending', 'Paused'].includes(props.row['Reward Status'])">
                    <q-item-main>Cancel Reward</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="commonAction(props.row, 'pending')"
                          v-if="props.row['Reward Status'] === 'Cancelled'">
                    <q-item-main>Pending Reward</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="commonAction(props.row, 'reverse')"
                          v-if="['Partially Issued', 'Issued'].includes(props.row['Reward Status'])">
                    <q-item-main>Reverse Reward</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import { generateColumns, request, notify, toSelectOptions } from '../../../../common'

export default {
  name: 'fis-cashback-transactions',
  mixins: [
    ListPageMixin,
    FreezeColumnMixin
  ],
  data () {
    return {
      title: 'Cashback Transactions',
      downloadUrl: `/admin/fis/cashback/transactions/export`,
      requestUrl: `/admin/fis/cashback/transactions/list`,
      filtersUrl: `/admin/fis/cashback/transactions/filters`,
      columns: generateColumns([
        'Date Time', 'Tran ID', 'Tran Type', 'Reason Code', 'Derived Response', 'Response',
        'Issuing Bank', 'Program', 'Type', 'BIN', 'BIN Currency', 'PAN', 'Card Proxy Number',
        'Debit/Credit', 'Auth Amount', 'Auth Code', 'Txn Local Amount', 'Transaction Currency',
        'Settle Amount', 'MCC', 'MCC Description', 'Merchant', 'Merchant Number', 'Merchant City',
        'Merchant Country', 'Request Code Description', 'Acquirer Reference Number', 'Acquirer Id',
        'Cashback Program Name', 'Cashback Program ID', 'Reward $', 'Reward %', 'Rewarded $',
        'Reward Status', 'Action'
      ]),
      freezeColumn: -1,
      freezeColumnRight: 2,
      filterOptions: [
        {
          value: 'filter[a.issuerClientId=]',
          label: 'Program',
          options: [],
          source: 'clients',
          search: true
        },
        {
          value: 'filter[a.panProxyNumber=]',
          label: 'PAN Proxy'
        },
        {
          value: 'filter[a.rewardStatus=]',
          label: 'Reward Status',
          options: toSelectOptions([
            'Pending', 'Issued', 'Partially Issued', 'Cancelled', 'Reversed', 'Paused'
          ])
        }
      ]
    }
  },
  methods: {
    statusClass (row) {
      const status = row['Reward Status']
      return {
        'Pending': 'blue',
        'Issued': 'positive',
        'Partially Issued': 'positive',
        'Cancelled': 'negative',
        'Reversed': 'orange',
        'Paused': 'purple'
      }[status] || 'dark'
    },
    async commonAction (row, action) {
      this.$q.loading.show()
      const resp = await request(`/admin/fis/cashback/transactions/${row['ID']}/action/${action}`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
        this.reload()
      }
    },
    async cancel (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to cancel this transaction\'s accumulation & rewarding?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        await this.commonAction(row, 'cancel')
      }).catch(() => {})
    }
  }
}
</script>
