<template>
  <q-page id="fis__choose__index_page" class="fis_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
    </div>
    <div class="page-content mt-15">
      <q-card v-for="(v, k) in user.fisAllPlatforms" :key="k"
              class="mb-20">
        <q-card-title>
          {{ k.toUpperCase() }}
          <span slot="subtitle">{{ v }}</span>
        </q-card-title>
        <q-card-separator></q-card-separator>
        <q-card-main class="relative">
          <div class="row gutter-sm mt--5 mb-15 top-statics-row top-statics-row-narrow">
            <div class="col-6 col-sm-3 top-statics max-w-250">
              <div class="row">
                <q-icon name="mdi-cash-multiple" color="primary"></q-icon>
                <div class="column">
                  <div class="value">{{ data[k] && data[k].balance | moneyFormat }}</div>
                  <div class="description">Portfolio Balance</div>
                </div>
              </div>
            </div>
            <div class="col-6 col-sm-3 top-statics max-w-250">
              <div class="row">
                <q-icon name="mdi-cube-outline" color="blue"></q-icon>
                <div class="column">
                  <div class="value">{{ data[k] && data[k].programs || 0 }}</div>
                  <div class="description"># of Programs</div>
                </div>
              </div>
            </div>
            <div class="col-6 col-sm-3 top-statics max-w-250">
              <div class="row">
                <q-icon name="mdi-credit-card-multiple" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ data[k] && data[k].cards || 0 }}</div>
                  <div class="description">Funded Cards #</div>
                </div>
              </div>
            </div>
            <div class="col-6 col-sm-3 top-statics max-w-250">
              <div class="row">
                <q-icon name="mdi-cash" color="orange"></q-icon>
                <div class="column">
                  <div class="value">{{ data[k] && data[k].avg | moneyFormat }}</div>
                  <div class="description">Avg Balance</div>
                </div>
              </div>
            </div>
          </div>
          <q-inner-loading :visible="loading">
            <q-spinner size="30px" color="primary"></q-spinner>
          </q-inner-loading>
        </q-card-main>
        <q-card-actions class="p-15">
          <q-btn color="primary" label="Select"
                 class="pl-20"
                 @click="select(k)"
                 icon-right="mdi-chevron-right"></q-btn>
        </q-card-actions>
      </q-card>
    </div>
  </q-page>
</template>

<script>
import PageMixin from '../../../mixins/PageMixin'
import { request } from '../../../common'

export default {
  name: 'fis-choose',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'Select a Platform',
      data: {}
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  methods: {
    async select (k) {
      this.$q.loading.show()
      const resp = await request(`/admin/fis/update-platform/${k}`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        this.$store.commit('User/update', {
          fisPlatform: k
        })
        this.$router.push('/j/fis/dashboard')
      }
    },
    async reload () {
      this.loading = true
      const resp = await request(`/admin/fis/platforms`)
      this.loading = false
      if (resp.success) {
        this.data = resp.data
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>
