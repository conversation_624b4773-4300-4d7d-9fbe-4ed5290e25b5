<template>
  <Card class="fis-dashboard-card-ach fis-dashboard-card-mixed"
        title="ACH Activity"
        ref="card"
        keyName="AchActivity"
        @reload="reload"
        :report-url="reportUrl">
    <template slot="title">
      <q-btn icon="mdi-poll"
             :class="{active: mode === 'bar'}"
             @click="setMode('bar')"
             class="btn-mini btn-mini-toggle mt--1 ml-10"
             outline></q-btn>
      <q-btn icon="mdi-chart-line-variant"
             :class="{active: mode === 'line'}"
             @click="setMode('line')"
             class="btn-mini btn-mini-toggle mt--1 ml-5 mr-auto"
             outline></q-btn>
    </template>

    <div class="row m-10 mb-20 auto-expand-row">
      <div class="col-5">
        <div class="font-28 mt-5">{{ count | number }}</div>
      </div>
      <div class="col-2">
        <div class="text-gray text-right">TOTAL</div>
        <div class="font-20">{{ amount | moneyFormat }}</div>
      </div>
      <div class="col-2">
        <div class="text-gray text-right">PANS</div>
        <div class="font-20">{{ pcount | number }}</div>
      </div>
      <div class="col-3">
        <div class="text-gray text-right">AVG</div>
        <div class="font-20">{{ count ? (amount / count) : 0 | moneyFormat }}</div>
      </div>
    </div>

    <div class="top-range-label top-range-label-left">
      {{ startDate | date('MM/DD/YY', true) }} thru {{ endDate | date('MM/DD/YY', true) }}
    </div>
    <div class="chart-container line-chart-container"
         v-show="mode === 'line'"></div>

    <div class="chart-container bar-chart-container"
         v-show="mode === 'bar'"></div>
    <div class="row gutter-xs legend"
         v-show="mode === 'bar'">
      <div class="col-auto"
           :style="percentBoxStyle"
           v-for="(p, name) in series"
           :key="'box_' + name">
        <q-chip :style="{backgroundColor: p.color}">
          {{ p.percent | percent }}
          <q-tooltip anchor="bottom middle"
                     self="top middle">{{ p.name }}</q-tooltip>
        </q-chip>
      </div>
    </div>

    <div class="row gutter-xs programs"
         v-show="mode === 'bar'">
      <div class="col-6"
           :class="{active: active === p.name}"
           v-for="(p, name) in series"
           :key="p.name">
        <div class="circle"
             :style="{backgroundColor: p.color}"></div>
        <div class="program-label">{{ p.fullName || p.name }}
          <q-tooltip anchor="bottom left"
                     self="top left">{{ name }}</q-tooltip>
        </div>
      </div>
    </div>
  </Card>
</template>

<script>
import MultipleDataZoomChartMixin from '../../../mixins/fis/MultipleDataZoomChartMixin'
export default {
  name: 'FisDashboardCardAch',
  mixins: [
    MultipleDataZoomChartMixin
  ]
}
</script>

<style lang="scss">
.fis-dashboard-card-ach.fis-dashboard-card-mixed {
  .q-card-main {
    .row.programs {
      height: 30px !important;
    }
  }
}
</style>
