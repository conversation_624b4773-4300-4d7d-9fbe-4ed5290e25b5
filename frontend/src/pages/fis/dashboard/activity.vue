<template>
  <Card class="fis-dashboard-activity"
        title="Card Metrics"
        ref="card"
        keyName="CardMetrics"
        @reload="reload">
    <div class="title">
      {{ values['total'].count | number }}
      <span class="font-12 text-negative va-m ib mt--3"
            v-if="showStillQuerying">
        <q-icon name="mdi-information-outline"
                class="ml-10"
                color="negative"></q-icon>
        Still querying...
        <q-tooltip>The system is still querying the data. Please refresh this single graph a moment later.</q-tooltip>
      </span>
    </div>
    <div class="subtitle">Total Cards <span style="margin-left:40px; text-transform: none;">{{ start | date('MM/DD/YY', true) }} thru {{ end | date('MM/DD/YY', true) }}</span></div>
    <div class="row gutter-xs graphs">
      <template v-for="(s, n) in series">
        <div class="col-6 knob-wrap"
             :key="'g_' + n">
          <q-knob readonly
                  v-model="values[n].percent"
                  size="80px"
                  line-width="7"
                  :color="'activity_' + n + '_active'"
                  :track-color="'activity_' + n"
                  :min="0"
                  :max="100">
            <div class="percent">{{ values[n].percent }}%</div>
          </q-knob>
          <div class="knob-side">
            <div class="count">
              {{ values[n].count | number }}
              <q-icon v-if="['+', '-'].includes(values[n].trend)"
                      :name="values[n].trend === '+' ? 'mdi-trending-up' : 'mdi-trending-down'"
                      :color="values[n].trend === '+' ? 'positive' : 'negative'"></q-icon>
            </div>
            <div class="name">{{ s.name }}</div>
          </div>
        </div>
      </template>
    </div>
  </Card>
</template>

<script>
import _ from 'lodash'
import Card from './card'
import { request, notify } from '../../../common'
import CachedGraph from '../../../mixins/fis/CachedGraph'
export default {
  name: 'FisDashboardActivity',
  mixins: [
    CachedGraph
  ],
  components: {
    Card
  },
  props: {
    params: {
      type: Object
    }
  },
  data () {
    return {
      graphName: 'activity',
      series: {
        inactive: {
          name: 'Inactive Cards'
        },
        active: {
          name: 'Active Cards'
        },
        funded: {
          name: 'Funded Cards'
        },
        activeNoSpends: {
          name: 'Active w/ No Spend'
        },
        activeNoLoads: {
          name: 'Active w/ No Loads'
        },
        negative: {
          name: 'Negative Balances'
        }
      },
      start: null,
      end: null
    }
  },
  computed: {
    values () {
      const res = {}
      const data = this.graphData || {}
      const keys = Object.keys(this.series)
      keys.push('total')
      for (const t of keys) {
        res[t] = _.assignIn({
          count: 0,
          percent: 0,
          trend: '-'
        }, data[t] || {})
      }
      return res
    },
    reqFilters () {
      const p = this.params
      return [
        p.bank,
        p.type,
        p.client,
        p.range,
        p.subprogram,
        p.bin
      ].join('_')
    },
    showStillQuerying () {
      const values = this.graphData || {}
      console.log(values.reqFilters)
      console.log(values.respFilters)
      console.log(this.reqFilters)
      console.log('*********')
      // if (values.reqFilters === this.reqFilters) {
      //   return false
      // }
      // if (!values.respFilters) {
      //   return false
      // }
      return values.reqFilters === values.respFilters
    }
  },
  methods: {
    async reload (p) {
      if (await this.loadWithDbCache(p)) {
        return
      }
      const reqFilters = this.reqFilters
      const resp = await request(`/admin/fis/dashboard/activity/data`, 'get', this.params)
      if (p && p.cb) {
        p.cb(resp)
      }
      if (resp.success) {
        if (!resp.data) {
          notify('The background is processing the card metrics graph data, please refresh and try again later')
        } else {
          this.end = resp.data.end
          this.start = resp.data.start
          resp.data.reqFilters = reqFilters
          this.onResponseData(resp.data)
          await this.saveDbCache(resp.data)
        }
      }
    }
  }
}
</script>

<style lang="scss">
.fis-dashboard-activity.common-dashboard-card {
  .q-card-main {
    padding: 5px 15px !important;
    overflow-x: auto !important;
    overflow-y: hidden !important;

    .knob-wrap {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: start;
      margin-bottom: 3px;

      .knob-side {
        margin-left: 10px;
        margin-top: -5px;
      }
    }

    .q-knob {
      opacity: 1 !important;
      flex-shrink: 0;
    }
    .graphs {
      min-width: 340px;
      overflow: scroll;
    }
    .title {
      font-size: 28px;
      margin-top: -10px;

      .q-icon {
        font-size: 22px;
        margin-top: -1px;
      }
    }
    .subtitle {
      font-size: 13px;
      color: #999;
      text-transform: uppercase;
      margin-top: -5px;
      margin-bottom: 5px;
    }
    .percent {
      font-size: 17px;
      font-weight: 400;
      color: black;
    }
    .count {
      font-size: 22px;
      font-weight: 500;
      color: black;
      .q-icon {
        margin-top: -4px;
      }
    }
    .name {
      font-size: 12.5px;
      color: #777;
      line-height: 1.3em;
      margin-top: -2px;
    }
    .text-activity_inactive {
      color: #f4f4f4;
    }
    .text-activity_inactive_active {
      color: #979797;
    }
    .text-activity_active {
      color: #ecf9ec;
    }
    .text-activity_active_active {
      color: #19c43e;
    }
    .text-activity_funded {
      color: #f3f2fd;
    }
    .text-activity_funded_active {
      color: #9081f2;
    }
    .text-activity_activeNoSpends {
      color: #e4faf4;
    }
    .text-activity_activeNoSpends_active {
      color: #00ce91;
    }
    .text-activity_activeNoLoads {
      color: #e3efff;
    }
    .text-activity_activeNoLoads_active {
      color: #0066ff;
    }
    .text-activity_negative {
      color: #ffe7ea;
    }
    .text-activity_negative_active {
      color: #f30023;
    }
  }
}
</style>
