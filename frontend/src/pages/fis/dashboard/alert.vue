<template>
  <Card class="fis-dashboard-alert fis-dashboard-data-zoom"
        title="Alerts"
        ref="card"
        keyName="Alert"
        @reload="reload"
        :report-url="reportUrl">
    <div class="row header">
      <div class="col-auto title mr-auto">
        <div class="value">{{ total | number }}</div>
      </div>
      <div class="col-auto right">
        <div class="label">PANs</div>
        <div class="value">{{ pans | number }}</div>
      </div>
      <div class="col-auto right">
        <div class="label">Total</div>
        <div class="value">{{ amount | moneyFormat(params.currency, false, false) }}</div>
      </div>
      <div class="col-auto right ml-20 bolder">
        <div class="label">Avg</div>
        <div class="value">{{ avg | moneyFormat(params.currency, false, false) }}</div>
      </div>
    </div>
    <div class="sub-header">
      <div class="label"
           v-if="initialLabel">{{ initialLabel }}</div>
      <div class="label"
           v-else-if="startDate && endDate">
        {{ startDate | date('MM/DD/YY', true) }} thru {{ endDate | date('MM/DD/YY', true) }}
      </div>
    </div>
    <div class="chart-container"></div>
  </Card>
</template>

<script>
import DataZoomChartMixin from '../../../mixins/fis/DataZoomChartMixin'
export default {
  name: 'FisDashboardSettleNoAuth',
  mixins: [
    DataZoomChartMixin
  ],
  data () {
    return {
      graphName: 'alert',
      reportName: '',
      requestUrl: `/admin/fis/dashboard/alert/data`,
      color: '#FFB60D',
      handleColor: '#d38f05',
      gradientColor: '#FFEDD2',
      reportUrlBase: '#/j/fis/monitor'
    }
  }
}
</script>
