<template>
  <Card class="fis-dashboard-balance"
        title="Balances"
        ref="card"
        keyName="Balance"
        @reload="reload"
        :report-url="reportUrl">
    <h3 v-if="clientName">{{ clientName }}</h3>
    <h3 v-else>Top {{ top }} Subprograms <small>(of {{ total }})</small></h3>
    <h5>{{ start | date('MM/DD/YY', true) }} thru {{ end | date('MM/DD/YY', true) }}</h5>
    <div class="chart-container"></div>
    <div class="row gutter-xs legend">
      <div class="col-auto"
           :style="percentBoxStyle"
           v-for="(p,i) in programs"
           :key="i">
        <q-chip :style="{backgroundColor: primaryColors[i]}">
          {{ percentOfProgram(i) | percent(1) }}
          <q-tooltip anchor="bottom middle"
                     self="top middle">{{ p }}</q-tooltip>
        </q-chip>
      </div>
    </div>
    <div class="row gutter-xs programs">
      <div class="col-6"
           v-for="(p,i) in programs"
           :class="{active: active === i}"
           :key="i">
        <div class="circle"
             :style="{backgroundColor: primaryColors[i]}"></div>
        <div class="program-label">{{ p }}
          <q-tooltip anchor="bottom left"
                     self="top left">{{ p }}</q-tooltip>
        </div>
      </div>
    </div>
  </Card>
</template>

<script>
import Card from './card'
import echarts from 'echarts'
import _ from 'lodash'
import { request } from '../../../common'
import CachedGraph from '../../../mixins/fis/CachedGraph'
import $ from 'jquery'
export default {
  name: 'FisDashboardBalance',
  mixins: [
    CachedGraph
  ],
  components: {
    Card
  },
  props: {
    params: {
      type: Object
    }
  },
  data () {
    return {
      graphName: 'balance',
      top: 0,
      total: 0,
      start: null,
      end: null,
      programs: [],
      data: [],
      colors: [
        '#FFC9B8',
        '#FF8A6C',
        '#FF5519',
        '#FFD7BE',
        '#FFB07C',
        '#FF8730',
        '#FDF0CA',
        '#FCDF95',
        '#FACE59',
        '#C1E3F3',
        '#91CEE8',
        '#3AABD8',
        '#C6D8EA',
        '#82C8E5',
        '#5188BF',
        '#D9F0C1',
        '#B2DF83',
        '#89D038'
      ],
      primaries: [
        '#FF5519',
        '#FF8730',
        '#FACE59',
        '#3AABD8',
        '#5188BF',
        '#89D038'
      ],
      active: null,
      chart: null
    }
  },
  computed: {
    primaryColors () {
      return this.colors.filter((c, i) => (i + 1) % 3 === 0)
    },
    rangePrefix () {
      const t = this.params.range || 'Daily'
      return (t[0] || '').toLowerCase()
    },
    rangeName () {
      let t = this.params.range || 'Daily'
      if (t.startsWith('custom_range__')) {
        return 'Range'
      }
      t = t.substr(0, t.length - 2)
      return t === 'Dai' ? 'Day' : t
    },
    clientCount () {
      if (this.data[0] && this.data[0].length) {
        return this.data[0].length
      }
      return this.programs.length ? this.programs.length : 1
    },
    percentBoxStyle () {
      return {
        width: `${100 / this.clientCount}%`
      }
    },
    clientName () {
      if (this.params.client === 'All') {
        return false
      }
      return this.params.client
    }
  },
  methods: {
    async reload (p) {
      if (await this.loadWithDbCache(p)) {
        return
      }
      const resp = await request(`/admin/fis/dashboard/balance/data`, 'get', this.params)
      if (p && p.cb) {
        p.cb(resp)
      }
      if (resp.success) {
        this.onResponseData(resp.data)
        await this.saveDbCache(resp.data)
      }
    },
    onResponseData (data) {
      this.saveCache(data)
      this.top = data.top
      this.total = data.total
      this.start = data.start
      this.end = data.end
      this.programs = data.programs
      this.data = data.data
      this.$nextTick(() => {
        this.initChart()
      })
    },
    percentOfProgram (i) {
      let sum = 0
      let d = 0
      _.forEach(this.data, day => {
        _.forEach(day, (v, k) => {
          sum += v
          if (k === i) {
            d += v
          }
        })
      })
      if (sum === 0) {
        return 1
      }
      return d / sum
    },
    resize () {
      this.chart.resize()
    },
    initChart () {
      const chart = echarts.init(this.$el.querySelector('.chart-container'), 'fis')
      this.chart = chart
      const series = this.data.map((d, i) => {
        return {
          name: `${this.rangePrefix}${i + 1}`,
          type: 'bar',
          label: {
            show: true,
            position: 'bottom',
            formatter: '{a}',
            color: '#999',
            fontSize: 10
          },
          itemStyle: {
            shadowBlur: 5,
            shadowOffsetY: 2,
            shadowColor: '#999'
          },
          data: d.map((v, j) => {
            return {
              value: v,
              itemStyle: {
                color: this.colors[j * this.data.length + i]
              }
            }
          })
        }
      })
      const options = _.defaultsDeep({
        grid: {
          left: '10%',
          top: 5,
          right: 0,
          bottom: 25
        },
        xAxis: {
          show: false,
          type: 'category',
          axisLine: {
            show: false
          },
          axisPointer: {
            show: true,
            lineStyle: {
              type: 'dash'
            },
            label: {
              show: false
            }
          }
        },
        yAxis: {
          axisLabel: {
            color: '#999',
            fontSize: 10,
            formatter: v => {
              if (v >= 1000) {
                return '$' + (v / 1000) + 'k'
              }
              return '$' + v
            }
          },
          splitLine: {
            lineStyle: {
              color: '#e6e6e6'
            }
          },
          axisLine: {
            show: false
          }
        },
        series: series,
        tooltip: {
          trigger: 'item',
          formatter: ({ dataIndex, seriesIndex }) => {
            let s = `<div class="mb-5">
              <div class="chart-tooltip-circle" style="background: ${this.primaryColors[dataIndex]}"></div>
              <span class="font-13">${this.programs[dataIndex]}</span>
            </div>`
            for (let i = 0; i < this.data.length; i++) {
              s += `<div class="font-12 ${i === seriesIndex ? 'bold' : ''}">
                <span class="gray mr-5">${this.rangeName} ${i + 1}:</span>
                <span>${this.c.moneyFormat(this.data[i][dataIndex], this.params.currency, false, false)}</span>
              </div>`
            }
            return s
          }
        }
      }, this.cc.fis.chart)
      chart.setOption(options)
      chart.resize()
      chart.on('mouseover', 'series', p => {
        this.active = p.dataIndex
      })
      chart.on('mouseout', 'series', () => {
        this.active = null
      })
      $(window).on('resize', this.resize)
    }
  }
}
</script>

<style lang="scss">
.fis-dashboard-balance.common-dashboard-card {
  .q-card-main {
    padding: 0 8px !important;
    .row.legend,
    .row.programs {
      padding: 0 4px 0 38px;
      margin-bottom: 12px;
    }
    .q-chip {
      border-radius: 4px;
      padding: 4px 2px;
      min-height: 20px;
      color: white;
      font-size: 12px;
      font-weight: 900;
      box-shadow: 0 2px 5px #999;
      width: 100%;
      text-align: center;
    }
    .chart-container {
      height: calc(100% - 185px) !important;
    }
  }
}
</style>
