<template>
  <q-card class="fis-dashboard-card common-dashboard-card">
    <q-card-title>
      <span>{{ title }}</span>
      <slot name="title"></slot>
      <a :href="reportUrl"
         v-if="reportUrl"
         class="mr-15">View report</a>
      <q-btn round
             flat
             size="xs"
             @click="reload('force')"
             class="btn-reload"
             icon="mdi-refresh"></q-btn>
    </q-card-title>
    <q-card-main>
      <slot></slot>

      <q-inner-loading :visible="loading">
        <q-spinner size="30"></q-spinner>
      </q-inner-loading>
    </q-card-main>
  </q-card>
</template>

<script>
import { EventHandlerMixin } from '../../../common'
import ChartCardMixin from '../../../mixins/ChartCardMixin'
import _ from 'lodash'

export default {
  name: 'FisDashboardCard',
  mixins: [
    ChartCardMixin,
    EventHandlerMixin('admin-reload-dashboard'),
    EventHandlerMixin('admin-reload-dashboard-card', 'reloadCard'),
    EventHandlerMixin('admin-reload-dashboard-cards', 'reloadCards')
  ],
  props: [
    'title',
    'reportUrl',
    'keyName'
  ],
  data () {
    return {
      loading: false
    }
  },
  methods: {
    async reload (mode) {
      if (this.$store.state.Fis['graphLoad'][this.keyName]) {
        this.loading = true
        this.$emit('reload', {
          mode,
          cb: () => {
            this.loading = false
          }
        })
      }
    },
    reloadCard (name) {
      if (this.keyName === name) {
        this.reload()
      }
    },
    reloadCards (names) {
      if (_.isArray(names) && _.includes(names, this.keyName)) {
        this.reload()
      }
    }
  }
}
</script>

<style lang="scss">
.fis-dashboard-card.common-dashboard-card {
  max-width: 100% !important;
  height: 370px !important;
  &.wide {
    max-width: 820px !important;
  }
  .q-card-main.q-card-container {
    padding: 0;
  }
  .btn-reload {
    top: 0;
    bottom: auto;
  }
}
</style>
