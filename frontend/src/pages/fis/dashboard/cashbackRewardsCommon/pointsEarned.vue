<template>
  <q-card id="CashbackRewardsPointsEarnedChart">
    <q-card-title>
      <div class="row">
        <div class="chart-title">
          <p>Cashback Points Earned</p>
          <span>{{delta}}</span>
        </div>
        <q-btn class="cashback-rewards-btn down-btn btn-sm ml-auto mr-8 square"
               @click="download()"
               icon="mdi-file-document-outline"></q-btn>
        <q-btn class="cashback-rewards-btn report-btn btn-sm square"
               @click="viewReport()"
               icon="mdi-chart-line"></q-btn>
      </div>
    </q-card-title>
    <q-card-main class="chart-item">
      <div class="cashback-rewards-chart"
           id="cashbackRewardsPointsEarned"></div>
      <div v-if="visible"
           class="loading">
        <q-spinner-hourglass color="primary"
                             size="3em"
                             :thickness="2" />
      </div>
    </q-card-main>
  </q-card>
</template>

<script>
import echarts from 'echarts'
import $ from 'jquery'
import { request, EventHandlerMixin } from '../../../../common'
import html2canvas from 'html2canvas'

export default {
  props: {
    params: {
      type: Object
    }
  },
  mixins: [
    EventHandlerMixin('cashback-rewards-reload-dashboard')
  ],
  data () {
    return {
      delta: 0,
      visible: false,
      chart: null,
      xAxis: [],
      data: [],
      series:
        {
          name: 'Cashback Points Earned',
          type: 'line',
          color: '#0062ff',
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0, color: 'rgba(80, 181, 255, 1)'
                },
                {
                  offset: 0.7, color: 'rgba(80, 181, 255, 0.1)'
                },
                {
                  offset: 1, color: 'rgba(80, 181, 255, 0)'
                }
              ]
            }
          },
          data: []
        }
    }
  },
  async mounted () {
    this.reload()
  },
  methods: {
    seriesData () {
      return this.data.map(v => v.amount)
    },
    resize () {
      this.chart.resize()
    },
    viewReport () {
      this.$router.push(`/j/fis/transactions`)
    },
    async reload () {
      this.visible = true
      const resp = await request(`/admin/fis/dashboard/fee/data`, 'get', this.params)
      if (resp.success) {
        this.data = Object.values(resp.data.data)
        this.xAxis = resp.data.axis
        this.delta = 0
        this.initChart(resp.data)
      }
      this.visible = false
    },
    initChart (data) {
      this.chart = echarts.init(document.getElementById('cashbackRewardsPointsEarned'), 'primary')
      // let that = this
      // let colorList = ['#4acc3d']
      // let labelList = ['Account Loads', 'Commissions', 'Member Loads', 'Platform Adjustment']
      // let positionStr = ''
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          position: function (pos, params, dom, rect, size) {
            // 鼠标在左侧时 tooltip 显示到右侧，鼠标在右侧时 tooltip 显示到左侧。
            let obj = { top: pos[1] - 290 }
            if (pos[0] < size.viewSize[0] / 2) {
              obj['left'] = pos[0] - 50
              // positionStr = 'left'
            } else {
              // positionStr = 'right'
              obj['right'] = size.viewSize[0] - pos[0] - 50
            }
            return obj
          },
          // alwaysShowContent: true,
          backgroundColor: '#ffffff',
          padding: 0,
          formatter: function (params) {
            return ''
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '60px',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: this.xAxis
          }
        ],
        dataZoom: {
          bottom: '25px'
        },
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: false
            }
          }
        ],
        series: this.seriesData()
      }
      this.chart.setOption(option)

      $(window).on('resize', this.resize)
    },
    titleStr (str = '', range = false) {
      let title = str.split('-')
      let month = {
        '01': 'Jan.',
        '02': 'Feb.',
        '03': 'Mar.',
        '04': 'Apr.',
        '05': 'May',
        '06': 'Jun.',
        '07': 'Jul.',
        '08': 'Aug.',
        '09': 'Sept.',
        '10': 'Oct.',
        '11': 'Nov.',
        '12': 'Dec.'
      }
      let bigMonth = ['01', '03', '05', '07', '08', '10', '12']
      let smallMonth = ['04', '06', '09', '11']
      // let days = ['01', '02', '03', '21', '22', '23', '31']
      if (title.length === 3) {
        if (title[2].slice(-1) === '1' && title[2] !== '11') {
          return month[title[1]] + ' ' + title[2] + 'st, ' + title[0]
        } else if (title[2].slice(-1) === '2' && title[2] !== '12') {
          return month[title[1]] + ' ' + title[2] + 'nd, ' + title[0]
        } else if (title[2].slice(-1) === '3' && title[2] !== '13') {
          return month[title[1]] + ' ' + title[2] + 'rd, ' + title[0]
        } else {
          return month[title[1]] + ' ' + title[2] + 'th, ' + title[0]
        }
      } else if (title.length === 2) {
        if (range) {
          return this.value === 'week' ? 'Week ' + title[1] + ', ' + title[0] : month[title[1]] + ', ' + title[0]
        } else {
          let end = '29th'
          if (bigMonth.indexOf(title[1]) !== -1) {
            end = '31st'
          } else if (smallMonth.indexOf(title[1]) !== -1) {
            end = '30th'
          } else if (title[0] % 4) {
            end = '28th'
          }
          return this.value === 'week' ? 'Week ' + title[1] + ' Sun., ' + title[0] + ' - ' + 'Week ' + title[1] + ' Sat., ' + title[0] : month[title[1]] + ' ' + '01st, ' + title[0] + ' - ' + month[title[1]] + ' ' + end + ', ' + title[0]
        }
      } else if (title.length === 1) {
        if (range) {
          return title[0]
        } else {
          return month['01'] + ' 01st, ' + title[0] + ' - ' + month['12'] + '31st, ' + title[0]
        }
      }
    },
    download () {
      let target = null
      let name = 'Cashback Rewards Points Earned'
      target = document.getElementById('CashbackRewardsPointsEarnedChart')
      this.$q.loading.show()
      const targetCss = window.getComputedStyle(target)

      const options = {
        scale: 2,
        width: parseInt(targetCss.width, 10),
        height: parseInt(targetCss.height, 10),
        y: target.offsetTop + 70
      }
      html2canvas(target, options).then(canvas => {
        // canvas is the final rendered <canvas> element
        const imgData = canvas.toDataURL('image/jpeg')
        this.fileDownload(imgData, name)
        this.$q.loading.hide()
      })
    },
    fileDownload (downloadUrl, filename) {
      let aLink = document.createElement('a')
      aLink.style.display = 'none'
      aLink.href = downloadUrl
      aLink.download = `${filename}`
      // 触发点击-然后移除
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    }
  }
}
</script>
