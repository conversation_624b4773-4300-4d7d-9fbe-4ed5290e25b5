<template>
  <q-card id="CashbackRewardsTopProgramsChart">
    <q-card-title>
      <div class="row">
        <div class="chart-title">
          <p>Top 5 Rewards Programs</p>
          <span>{{delta | moneyFormat}}</span>
        </div>
        <q-btn class="cashback-rewards-btn down-btn btn-sm ml-auto mr-8 square"
               @click="download()"
               icon="mdi-file-document-outline"></q-btn>
        <q-btn class="cashback-rewards-btn report-btn btn-sm square"
               @click="viewReport()"
               icon="mdi-chart-line"></q-btn>
      </div>
    </q-card-title>
    <q-card-main class="chart-item">
      <div class="cashback-rewards-chart"
           id="cashbackRewardsTopPrograms"></div>
      <div v-if="visible"
           class="loading">
        <q-spinner-hourglass color="primary"
                             size="3em"
                             :thickness="2" />
      </div>
    </q-card-main>
  </q-card>
</template>

<script>
import echarts from 'echarts'
import $ from 'jquery'
import { request, EventHandlerMixin } from '../../../../common'
import html2canvas from 'html2canvas'

export default {
  props: {
    params: {
      type: Object
    }
  },
  mixins: [
    EventHandlerMixin('cashback-rewards-reload-dashboard')
  ],
  data () {
    return {
      delta: 0,
      visible: false,
      chart: null,
      data: [],
      colorList: [
        ['#5c9bff', '#90cdfa', '#ffcca6', '#ffde92', '#cdc5ff', '#b8e1b4'],
        ['#3d88ff', '#6abdfa', '#ffb279', '#ffd473', '#b5a9fe', '#72e466'],
        ['#0062ff', '#50b5ff', '#ff974a', '#ffc542', '#9b8afe', '#4bcd3e']
      ],
      xAxis: [],
      series: [
        {
          name: '1D',
          type: 'bar',
          value: 0,
          barMaxWidth: 20,
          barGap: '100%',
          data: [],
          label: {
            show: true,
            position: 'bottom',
            formatter: '{a}',
            color: '#92929d'
          }
        },
        {
          name: '2D',
          type: 'bar',
          value: 0,
          barMaxWidth: 20,
          barGap: '100%',
          data: [],
          label: {
            show: true,
            position: 'bottom',
            formatter: '{a}',
            color: '#92929d'
          }
        },
        {
          name: '3D',
          type: 'bar',
          value: 0,
          barMaxWidth: 20,
          barGap: '100%',
          data: [],
          label: {
            show: true,
            position: 'bottom',
            formatter: '{a}',
            color: '#92929d'
          }
        }
      ]
    }
  },
  async mounted () {
    this.reload()
  },
  methods: {
    seriesData () {
      return this.data.map(v => v.amount)
    },
    resize () {
      this.chart.resize()
    },
    viewReport () {
      this.$router.push(`/j/fis/transactions`)
    },
    async reload () {
      this.visible = true
      const resp = await request(`/admin/fis/cashbackRewards/dashboard/topProgram/data`, 'get', this.params)
      if (resp.success) {
        this.data = resp.data.data
        this.seriesDataList = []
        this.xAxis = []
        this.data.forEach((element, index) => {
          this.xAxis.push(element['name'])
          if (element.data.length > 0) {
            element.data.forEach((e, i) => {
              this.series[i].data.push({
                value: e,
                itemStyle: {
                  color: this.colorList[i][index]
                }
              })
            })
          }
        })
        this.delta = resp.data.total
        this.initChart(resp.data)
      }
      this.visible = false
    },
    initChart (data) {
      this.chart = echarts.init(document.getElementById('cashbackRewardsTopPrograms'), 'primary')
      let that = this
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          position: function (pos, params, dom, rect, size) {
            // 鼠标在左侧时 tooltip 显示到右侧，鼠标在右侧时 tooltip 显示到左侧。
            let obj = { top: pos[1] - 290 }
            if (pos[0] < size.viewSize[0] / 2) {
              obj['left'] = pos[0] - 50
              // positionStr = 'left'
            } else {
              // positionStr = 'right'
              obj['right'] = size.viewSize[0] - pos[0] - 50
            }
            return obj
          },
          // alwaysShowContent: true,
          backgroundColor: '#ffffff',
          padding: 0,
          formatter: function (params) {
            return ''
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '60px',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            show: true,
            data: this.xAxis,
            splitLine: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              interval: 0,
              margin: 25,
              formatter: function (value, index) {
                let str = ''
                switch (index) {
                  case 0:
                    str = '{fistA|' + that.data[index]['percent'] + '}' + '\n' + '{fistB|' + value + '}'
                    break
                  case 1:
                    str = '{secondA|' + that.data[index]['percent'] + '}' + '\n' + '{secondB|' + value + '}'
                    break
                  case 2:
                    str = '{thirdA|' + that.data[index]['percent'] + '}' + '\n' + '{thirdB|' + value + '}'
                    break
                  case 3:
                    str = '{fouthA|' + that.data[index]['percent'] + '}' + '\n' + '{fouthB|' + value + '}'
                    break
                  case 4:
                    str = '{fifthA|' + that.data[index]['percent'] + '}' + '\n' + '{fifthB|' + value + '}'
                    break
                  case 5:
                    str = '{sixthA|' + that.data[index]['percent'] + '}' + '\n' + '{sixthB|' + value + '}'
                    break
                }
                return str
              },
              rich: {
                fistA: {
                  color: '#fff',
                  backgroundColor: '#0062ff',
                  padding: 5,
                  borderRadius: 8
                },
                fistB: {
                  color: '#0062ff',
                  lineHeight: 20
                },
                secondA: {
                  color: '#fff',
                  backgroundColor: '#50b5ff',
                  padding: 5,
                  borderRadius: 8
                },
                secondB: {
                  color: '#50b5ff',
                  lineHeight: 20
                },
                thirdA: {
                  color: '#fff',
                  backgroundColor: '#ff974a',
                  padding: 5,
                  borderRadius: 8,
                  with: '50px'
                },
                thirdB: {
                  color: '#ff974a',
                  lineHeight: 20,
                  with: '50px'
                },
                fouthA: {
                  color: '#fff',
                  backgroundColor: '#ffc542',
                  padding: 5,
                  borderRadius: 8
                },
                fouthB: {
                  color: '#ffc542',
                  lineHeight: 20
                },
                fifthA: {
                  color: '#fff',
                  backgroundColor: '#9b8afe',
                  padding: 5,
                  borderRadius: 8
                },
                fifthB: {
                  color: '#9b8afe',
                  lineHeight: 20
                },
                sixthA: {
                  color: '#fff',
                  backgroundColor: '#4bcd3e',
                  padding: 5,
                  borderRadius: 8
                },
                sixthB: {
                  color: '#4bcd3e',
                  lineHeight: 20
                }
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            splitLine: {
              lineStyle: {
                color: '#eeeafc'
              }
            },
            axisLine: {
              show: false
            }
          }
        ],
        series: this.series
      }
      this.chart.setOption(option)

      $(window).on('resize', this.resize)
    },
    titleStr (str = '', range = false) {
      let title = str.split('-')
      let month = {
        '01': 'Jan.',
        '02': 'Feb.',
        '03': 'Mar.',
        '04': 'Apr.',
        '05': 'May',
        '06': 'Jun.',
        '07': 'Jul.',
        '08': 'Aug.',
        '09': 'Sept.',
        '10': 'Oct.',
        '11': 'Nov.',
        '12': 'Dec.'
      }
      let bigMonth = ['01', '03', '05', '07', '08', '10', '12']
      let smallMonth = ['04', '06', '09', '11']
      // let days = ['01', '02', '03', '21', '22', '23', '31']
      if (title.length === 3) {
        if (title[2].slice(-1) === '1' && title[2] !== '11') {
          return month[title[1]] + ' ' + title[2] + 'st, ' + title[0]
        } else if (title[2].slice(-1) === '2' && title[2] !== '12') {
          return month[title[1]] + ' ' + title[2] + 'nd, ' + title[0]
        } else if (title[2].slice(-1) === '3' && title[2] !== '13') {
          return month[title[1]] + ' ' + title[2] + 'rd, ' + title[0]
        } else {
          return month[title[1]] + ' ' + title[2] + 'th, ' + title[0]
        }
      } else if (title.length === 2) {
        if (range) {
          return this.value === 'week' ? 'Week ' + title[1] + ', ' + title[0] : month[title[1]] + ', ' + title[0]
        } else {
          let end = '29th'
          if (bigMonth.indexOf(title[1]) !== -1) {
            end = '31st'
          } else if (smallMonth.indexOf(title[1]) !== -1) {
            end = '30th'
          } else if (title[0] % 4) {
            end = '28th'
          }
          return this.value === 'week' ? 'Week ' + title[1] + ' Sun., ' + title[0] + ' - ' + 'Week ' + title[1] + ' Sat., ' + title[0] : month[title[1]] + ' ' + '01st, ' + title[0] + ' - ' + month[title[1]] + ' ' + end + ', ' + title[0]
        }
      } else if (title.length === 1) {
        if (range) {
          return title[0]
        } else {
          return month['01'] + ' 01st, ' + title[0] + ' - ' + month['12'] + '31st, ' + title[0]
        }
      }
    },
    download () {
      let target = null
      let name = 'Cashback Rewards Total Spend'
      target = document.getElementById('CashbackRewardsTopProgramsChart')
      this.$q.loading.show()
      const targetCss = window.getComputedStyle(target)

      const options = {
        scale: 2,
        width: parseInt(targetCss.width, 10),
        height: parseInt(targetCss.height, 10),
        y: target.offsetTop + 70
      }
      html2canvas(target, options).then(canvas => {
        // canvas is the final rendered <canvas> element
        const imgData = canvas.toDataURL('image/jpeg')
        this.fileDownload(imgData, name)
        this.$q.loading.hide()
      })
    },
    fileDownload (downloadUrl, filename) {
      let aLink = document.createElement('a')
      aLink.style.display = 'none'
      aLink.href = downloadUrl
      aLink.download = `${filename}`
      // 触发点击-然后移除
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    }
  }
}
</script>
