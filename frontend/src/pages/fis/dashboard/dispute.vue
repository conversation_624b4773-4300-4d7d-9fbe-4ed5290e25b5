<template>
  <Card class="fis-dashboard-dispute fis-dashboard-data-zoom wide"
        title="Dispute Activity"
        ref="card"
        keyName="Dispute"
        @reload="reload"
        :report-url="reportUrl">
    <div class="row header mb-15">
      <div class="col-auto title mr-auto">
        <div class="value">{{ total | number }}</div>
      </div>
      <div class="col-auto right">
        <div class="label">PANs</div>
        <div class="value">{{ pcount | number }}</div>
      </div>
      <div class="col-auto right">
        <div class="label">Total</div>
        <div class="value">{{ amount | moneyFormat(params.currency, false, false) }}</div>
      </div>
      <div class="col-auto right ml-20 bolder">
        <div class="label">Avg</div>
        <div class="value heavy">{{ avg | moneyFormat(params.currency, false, false) }}</div>
      </div>
    </div>
    <div class="sub-header">
      <div class="label"
           v-if="startDate && endDate">
        {{ startDate | date('MM/DD/YY', true) }} thru {{ endDate | date('MM/DD/YY', true) }}
      </div>
      <div class="label"
           v-else-if="initialLabel">{{ initialLabel }}</div>
    </div>
    <div class="chart-container"></div>
  </Card>
</template>

<script>
import Card from './card'
import DataZoomChartMixin from '../../../mixins/fis/DataZoomChartMixin'
export default {
  name: 'FisDashboardDispute',
  mixins: [
    DataZoomChartMixin
  ],
  components: {
    Card
  },
  props: {
    params: {
      type: Object
    }
  },
  data () {
    return {
      graphName: 'dispute',
      requestUrl: `/admin/fis/dashboard/dispute/data`,
      color: '#0062FF',
      handleColor: '#00439D',
      gradientColor: '#D0E6FF'
    }
  },
  methods: {
    initChart () {
      this.initLineChart()
      this.onDataZoom()
    },
    onDataZoom () {
      this.$root.$emit('fis_dashboard_dispute_activity_update', {
        items: this.items,
        initialLabel: this.initialLabel,
        total: this.total,
        pcount: this.pcount,
        startDate: this.startDate,
        endDate: this.endDate
      })
    }
  }
}
</script>
