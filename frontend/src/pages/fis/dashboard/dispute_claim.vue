<template>
  <Card class="fis-dashboard-dispute-claim fis-dashboard-data-zoom"
        title="Dispute Claims"
        ref="card"
        keyName="DisputeClaim"
        @reload="reload"
        :report-url="reportUrl">
    <div class="row header mb-10">
      <div class="col-auto title mr-auto">
        <div class="value">{{ total | number }}</div>
        <div class="label"
             v-if="initialLabel">{{ initialLabel }}</div>
        <div class="label"
             v-else-if="startDate && endDate">
          {{ startDate | date('MM/DD/YY', true) }} thru {{ endDate | date('MM/DD/YY', true) }}
        </div>
      </div>
      <div class="col-auto right">
        <div class="label">PANs</div>
        <div class="value heavy mt-3">{{ pcount | number }}</div>
      </div>
      <div class="col-auto right">
        <div class="label">% Open</div>
        <div class="value heavy mt-3">{{ openPercent | percent }}</div>
      </div>
    </div>
    <div class="chart-container"></div>
    <div class="row gutter-xs programs">
      <div class="col-12"
           :class="{active: active === name}"
           v-for="(p, name) in detail"
           :key="name">
        <div class="circle"
             :style="{backgroundColor: p.color}"></div>
        <div class="program-label">{{ name }}
          <q-tooltip anchor="bottom left"
                     self="top left">{{ name }}</q-tooltip>
        </div>
      </div>
    </div>
  </Card>
</template>

<script>
import Card from './card'
import echarts from 'echarts'
import _ from 'lodash'
import moment from 'moment'
import CachedGraph from '../../../mixins/fis/CachedGraph'
import { EventHandlerMixin, request } from '../../../common'
import $ from 'jquery'
export default {
  name: 'FisDashboardDisputeClaim',
  mixins: [
    CachedGraph,
    EventHandlerMixin('fis_dashboard_dispute_claim_update', 'updateGraph')
  ],
  components: {
    Card
  },
  props: {
    params: {
      type: Object
    }
  },
  data () {
    return {
      graphName: 'dispute_claim',
      reportName: 'dispute',
      requestUrl: `/admin/fis/dashboard/dispute_claim/data`,
      active: null,
      initialLabel: '',
      startDate: null,
      endDate: null,
      items: [],
      total: 0,
      pcount: 0,
      chart: null
    }
  },
  computed: {
    reportUrl () {
      let start = moment(this.startDate).format('YYYY-MM-DD')
      let end = moment(this.endDate).format('YYYY-MM-DD')
      if (this.initialLabel && this.initialRange) {
        start = this.initialRange[0]
        end = this.initialRange[1]
      }
      return `#/j/fis/report/dispute?format=Detail&start=${start || ''}&end=${end || ''}` + this.commonReportUrlSuffix()
    },
    openPercent () {
      if (this.total <= 0 || this.items.length <= 0) {
        return 0
      }
      const items = this.initialLabel ? this.items.slice(-1) : this.items
      let open = 0
      for (const item of items) {
        for (const key in item.detail) {
          if (item.detail.hasOwnProperty(key) && key.startsWith('Open Claim')) {
            open += item.detail[key].count
          }
        }
      }
      return open / this.total
    },
    pans () {
      const pans = this.items[0].pans
      if (_.isArray(pans)) {
        return pans.length
      }
      return pans
    },
    detail () {
      const d = {}
      for (const [key, color] of [
        ['Open Claim', '#7ACA0D'],
        ['Open Claim (Claim being processed)', '#01A5B7'],
        ['Closed Claim (Permanent credit applied)', '#FFB300'],
        ['Closed Claim (Deleted)', '#DF2025'],
        ['Other', '#ECF1F5']
      ]) {
        d[key] = {
          amount: 0,
          count: 0,
          pcount: 0,
          pans: 0,
          color
        }
      }
      if (this.total <= 0 || this.items.length <= 0) {
        return d
      }
      const items = this.initialLabel ? this.items.slice(-1) : this.items
      for (const item of items) {
        for (const key in item.detail) {
          if (item.detail.hasOwnProperty(key)) {
            d[key].amount += item.detail[key].amount
            d[key].count += item.detail[key].count
            d[key].pcount += item.detail[key].pcount
            d[key].pans += item.detail[key].pans
          }
        }
      }
      for (const item of Object.values(d)) {
        item.avg = item.count > 0 ? (item.amount / item.count) : 0
      }
      this.$nextTick(() => {
        this.initChart()
      })
      return d
    }
  },
  methods: {
    async reload (p) {
      if (await this.loadWithDbCache(p)) {
        return
      }
      const resp = await request(this.requestUrl, 'get', this.params)
      if (p && p.cb) {
        p.cb(resp)
      }
      if (resp.success) {
        this.onResponseData(resp.data)
        await this.saveDbCache(resp.data)
      }
    },
    onResponseData (data) {
      this.saveCache(data)
      if (data.data[0]) {
        const d = data.data[0]
        this.updateGraph({
          initialLabel: data.initialLabel,
          startDate: d.date,
          endDate: d.date,
          items: [d],
          total: d.count,
          pcount: d.pcount
        })
      }
    },
    updateGraph (arg) {
      this.initialLabel = arg.initialLabel
      this.startDate = arg.startDate
      this.endDate = arg.endDate
      this.items = arg.items
      this.total = arg.total
      this.pcount = arg.pcount
      this.$nextTick(() => {
        this.initChart()
      })
    },
    resize () {
      this.chart.resize()
    },
    initChart () {
      const counts = []
      _.forEach(this.detail, (d, name) => {
        counts.push({
          name,
          value: d.count,
          itemStyle: {
            color: d.color,
            shadowBlur: 5,
            shadowOffsetY: 2,
            shadowColor: '#999'
          }
        })
      })
      const chart = echarts.init(this.$el.querySelector('.chart-container'), 'fis')
      this.chart = chart
      const options = _.defaultsDeep({
        grid: {
          left: 32,
          top: 5,
          right: 10,
          bottom: 10
        },
        xAxis: {
          show: false,
          data: _.keys(this.detail),
          axisPointer: {
            show: true,
            lineStyle: {
              type: 'dash'
            },
            label: {
              show: false
            }
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          axisLine: {
            show: false
          },
          axisLabel: {
            color: '#999',
            fontSize: 10,
            formatter: v => {
              if (v >= 1000) {
                return (v / 1000) + 'k'
              }
              return v
            }
          },
          splitLine: {
            lineStyle: {
              color: '#e6e6e6'
            }
          }
        },
        series: [
          {
            type: 'bar',
            data: counts,
            barWidth: '60%',
            barMinHeight: 0
          }
        ],
        tooltip: {
          // alwaysShowContent: true,
          formatter: (params) => {
            if (!params && !params.length) {
              return ''
            }
            if (params[0].name === '') {
              return ''
            }
            const d = this.detail[params[0].name]
            if (!d) {
              return ''
            }
            this.active = params[0].name
            return `<div class="mb-5"><div class="chart-tooltip-circle" style="background: ${d.color}"></div> ${params[0].name}</div>
              <div class="field-row"><div class="field-label">PANs:</div><div class="field-value">${this.$options.filters.number(d.pans)}</div></div>
              <div class="field-row"><div class="field-label">Amount:</div><div class="field-value">${this.c.moneyFormat(d.amount, this.params.currency, false, false)}</div></div>
              <div class="field-row boldest"><div class="field-label">Count:</div><div class="field-value">${this.$options.filters.number(d.count)}</div></div>
              <div class="field-row"><div class="field-label">Avg:</div><div class="field-value">${d.count > 0 ? this.c.moneyFormat(d.amount / d.count, this.params.currency, false, false) : 0}</div></div>`
          }
        }
      }, this.cc.fis.chart)
      chart.setOption(options)
      chart.resize()
      chart.on('mouseover', p => {
        this.active = p.name
      })
      chart.on('mouseout', () => {
        this.active = null
      })
      $(window).on('resize', this.resize)
    }
  },
  mounted () {
    this.$el.addEventListener('mouseleave', () => {
      this.active = null
    })
  }
}
</script>

<style lang="scss">
.common-dashboard-card.fis-dashboard-dispute-claim.fis-dashboard-data-zoom {
  .q-card-main {
    justify-content: flex-start !important;
    padding: 0 10px 15px !important;
    .header {
      font-size: 26px;
      padding: 0 4px;
      color: #333;
    }
    .row.programs {
      max-width: 340px;
      margin: 7px auto 0 20px !important;
      flex-direction: row;
      .col-12 {
        padding: 3px 0 0 6px;
        transform: scale(0.9);
      }
    }
    .chart-container {
      margin-top: 0 !important;
      height: calc(100% - 195px) !important;
    }
  }
}
</style>
