<template>
  <Card class="fis-dashboard-fee fis-dashboard-data-zoom"
        title="Fees"
        ref="card"
        keyName="Fee"
        @reload="reload"
        :report-url="reportUrl">
    <div class="row header">
      <div class="col-auto title mr-auto">
        <div class="value">{{ total | number }}</div>
      </div>
      <div class="col-auto right">
        <div class="label">PANs</div>
        <div class="value">{{ pcount | number }}</div>
      </div>
      <div class="col-auto right">
        <div class="label">Total</div>
        <div class="value">{{ amount | moneyFormat(params.currency, false, false) }}</div>
      </div>
      <div class="col-auto right ml-20 bolder">
        <div class="label">Avg</div>
        <div class="value">{{ avg | moneyFormat(params.currency, false, false) }}</div>
      </div>
    </div>
    <div class="sub-header">
      <div class="label"
           v-if="startDate && endDate">
        {{ startDate | date('MM/DD/YY', true) }} thru {{ endDate | date('MM/DD/YY', true) }}
      </div>
      <div class="label"
           v-else-if="initialLabel">{{ initialLabel }}</div>
    </div>
    <div class="chart-container"></div>
  </Card>
</template>

<script>
import DataZoomChartMixin from '../../../mixins/fis/DataZoomChartMixin'
export default {
  name: 'FisDashboardFee',
  mixins: [
    DataZoomChartMixin
  ],
  data () {
    return {
      graphName: 'fee',
      requestUrl: `/admin/fis/dashboard/fee/data`,
      color: '#F5051B',
      handleColor: '#ba0414',
      gradientColor: '#FEF8F8'
    }
  }
}
</script>
