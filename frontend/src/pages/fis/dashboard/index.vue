<template>
  <q-page class="fis__dashboard__index_page graph__dashboard__index_page">
    <div class="page-header">
      <!-- <div v-if="$store.state.User.fisPlatform === 'ppm'"
           class="title">
        <q-select v-model="rewardsDashboard"
                  @input="reload"
                  :options="dashboardList"></q-select>
      </div> -->
      <div class="fun-group">
        <q-field class="group-type-field"
                 label="Group Type">
          <q-select v-model="filter.type"
                    :filter="true"
                    @input="changeFliter('type')"
                    :options="filters.types"></q-select>
        </q-field>
        <q-field label="Bank">
          <q-select v-model="filter.bank"
                    :filter="true"
                    @input="changeFliter('bank')"
                    :options="filters.banks"></q-select>
        </q-field>
        <q-field label="BIN">
          <q-select v-model="filter.bin"
                    :filter="true"
                    @input="changeFliter('bin')"
                    :options="filters.bins"></q-select>
        </q-field>
        <q-field label="Issuing Client"
                 class="currency-field">
          <q-select v-model="filter.client"
                    :filter="true"
                    @input="changeFliter('client')"
                    :options="filters.clients"></q-select>
        </q-field>

        <q-field class="subprogram-field"
                 label="Subprogram">
          <q-select v-model="filter.subprogram"
                    :filter="true"
                    :options="filters.subprograms"></q-select>
        </q-field>
        <DateRangeFilter label="Range"
                         :ranges="filters.ranges"
                         :default="filter.range"
                         @change="rangeFilterChanged"
                         ref="dateRangeFilter"></DateRangeFilter>
        <!-- <q-field label="Currency"
                 class="currency-field">
          <q-select :value="filter.currency"
                    :options="filters.currencies"></q-select>
        </q-field> -->
        <q-btn @click="reload"
               color="primary"
               class="mr-15"
               label="Filter"></q-btn>
        <q-btn @click="resetFilter"
               color="black"
               class="mr-15"
               label="Reset"></q-btn>
        <q-btn-dropdown flat
                        dense
                        class="ml-auto"
                        icon="mdi-grid"
                        @show="cacheConfig()"
                        @hide="saveConfig()">
          <q-list link
                  class="graph-setting">
            <q-item v-for="(item, index) in graphs"
                    :key="`g-${index}`">

              <!-- <q-item-side icon="check"
                           v-if="!item.hidden" />
              <q-item-side v-else />
              <q-item-main :label="item.label"
                           :class="{'text-grey': item.hidden}" /> -->
              <q-item-main @click.native="item.hidden = !item.hidden"
                           :class="{'text-grey': item.hidden}">
                <q-icon name="mdi-check"
                        v-if="!item.hidden">
                </q-icon>
                <span>{{item.label}}</span>
              </q-item-main>
              <q-icon class="sort-icon"
                      v-if="index != 0 && index != 1"
                      @click.native="sort('up', index)"
                      name="mdi-arrow-up">
              </q-icon>
              <q-icon class="sort-icon"
                      @click.native="sort('down', index)"
                      v-if="index < graphs.length && index != 0"
                      name="mdi-arrow-down">
              </q-icon>
            </q-item>
          </q-list>
        </q-btn-dropdown>
      </div>
    </div>
    <div class="page-content mt-20">
      <div v-if="rewardsDashboard">
        <RewardsDashboard :params="filter"></RewardsDashboard>
      </div>
      <q-carousel v-if="!rewardsDashboard"
                  arrows
                  quick-nav
                  no-swipe
                  handle-arrow-keys
                  @input="changeGraph">
        <q-carousel-slide v-for="(item, index) in graphsList"
                          :key="index">
          <Draggable group="graph"
                     class="row draggable-container"
                     @end="saveOrder"
                     handle=".q-card-title"
                     v-model="graphShowList">
            <template v-for="(comp, i) in graphShowList">
              <div class="col-md-6 col-lg-4"
                   :key="i"
                   v-if="!comp.hidden && i < (index + 1) * num && i >= (index * num)">
                <component :is="comp.key"
                           :filters="filters"
                           :params="filter"></component>
              </div>
            </template>
          </Draggable>
        </q-carousel-slide>
      </q-carousel>
      <!-- <Draggable group="graph"
                 class="row draggable-container gutter-sm"
                 @end="saveOrder"
                 handle=".q-card-title"
                 v-model="graphs">
        <template v-for="(comp, i) in graphs">
          <div class="col-4"
               :key="i"
               v-if="!comp.hidden">
            <component :is="comp.key"
                       :filters="filters"
                       :params="filter"></component>
          </div>
        </template>
      </Draggable> -->
    </div>

    <q-inner-loading :visible="loading">
      <q-spinner :size="50"></q-spinner>
    </q-inner-loading>
  </q-page>
</template>

<script>
import { EventHandlerMixin, request } from '../../../common'
import Portfolio from './portfolio'
import Balance from './balance'
import Usage from './usage'
import Transaction from './transaction'
import Load from './load'
import Fee from './fee'
import Dispute from './dispute'
import DisputeClaim from './dispute_claim'
import Negative from './negative'
import SettleNoAuth from './settle_no_auth'
import Alert from './alert'
import CardSpend from './spend'
import Mcc from './mcc'
import AchActivity from './ach'
import PeoplePay from './people'
import CardMetrics from './activity'
import Draggable from 'vuedraggable'
import CustomRangeMixin from '../../../mixins/fis/CustomRangeMixin'
import PageMixin from '../../../mixins/PageMixin'
import RewardsDashboard from './rewardsDashboard'
import _ from 'lodash'

export default {
  name: 'FisDashboard',
  mixins: [
    CustomRangeMixin,
    PageMixin,
    EventHandlerMixin('reload-fis-dash-filters', 'initTopFilters')
  ],
  components: {
    Portfolio,
    Balance,
    Usage,
    Transaction,
    Load,
    Fee,
    Dispute,
    DisputeClaim,
    Negative,
    SettleNoAuth,
    Alert,
    CardSpend,
    Mcc,
    Draggable,
    RewardsDashboard,
    AchActivity,
    PeoplePay,
    CardMetrics
  },
  data () {
    return {
      loading: false,
      rewardsDashboard: false,
      dashboardList: [
        {
          label: 'Dashboard',
          value: false
        },
        {
          label: 'Cashback Rewards Dashboard',
          value: true
        }
      ],
      num: 6,
      slide: 'item-0',
      graphsList: [],
      graphShowList: [],
      reloadFlag: [],
      pageNum: 0,
      filter: {
        bank: 'All',
        type: '',
        client: 'All',
        range: 'Weekly',
        currency: 'USD',
        subprogram: 'All',
        bin: 'All',
        offset: null
      },
      initFilters: {
        banks: [],
        types: [],
        clients: [],
        ranges: [],
        currencies: [],
        subprograms: [],
        bins: []
      },
      filters: {
        banks: [],
        types: [],
        clients: [],
        ranges: [],
        currencies: [],
        subprograms: [],
        bins: []
      },
      graphs: []
    }
  },
  methods: {
    async changeFliter (type) {
      if (type === 'type' && !this.filter.type) {
        this.filter = {
          bank: 'All',
          type: '',
          client: 'All',
          range: 'Weekly',
          currency: 'USD',
          subprogram: 'All',
          bin: 'All',
          offset: null
        }
        let { ...tempList } = this.initFilters
        this.filters = tempList
        return
      }
      this.loading = true
      const resp = await request(`/admin/fis/dashboard/custome-filters`, 'post', this.filter)
      this.loading = false
      switch (type) {
        case 'type':
          this.filter = {
            bank: 'All',
            type: this.filter.type,
            client: 'All',
            range: 'Weekly',
            currency: 'USD',
            subprogram: 'All',
            bin: 'All',
            offset: null
          }
          break
        case 'bank':
          this.filter = {
            bank: this.filter.bank,
            type: this.filter.type,
            client: 'All',
            range: 'Weekly',
            currency: 'USD',
            subprogram: 'All',
            bin: 'All',
            offset: null
          }
          break
        case 'bin':
          this.filter = {
            bank: this.filter.bank,
            type: this.filter.type,
            client: 'All',
            range: 'Weekly',
            currency: 'USD',
            subprogram: 'All',
            bin: this.filter.bin,
            offset: null
          }
          break
      }
      if (resp.success) {
        this.filters = resp.data
      }
      // let _this = this
      // let { ...tempList } = this.initFilters
      // if (type === 'group') {
      //   console.log(this.initFilters)
      //   console.log(tempList)
      //   this.filter = {
      //     bank: 'All',
      //     type: this.filter.type,
      //     client: 'All',
      //     range: 'Daily',
      //     currency: 'USD',
      //     subprogram: 'All',
      //     bin: 'All'
      //   }

      //   if (this.filter.type) {
      //     this.initFilters.types.forEach(item => {
      //       if (item.value === this.filter.type) {
      //         if (item.type === 'bin') {
      //           let tmp = toSelectOptions(item.list)
      //           let list = [{
      //             value: '',
      //             label: 'All'
      //           }]
      //           tempList.bins = tmp.length > 1 ? list.concat(tmp) : tmp
      //           _this.filter.bin = tempList.bins.length ? tempList.bins[0].value : ''
      //         }
      //         if (item.type === 'client') {
      //           let tmpClient = []
      //           this.initFilters.clients.forEach(e => {
      //             if (item.list.indexOf(e.value) !== -1) {
      //               tmpClient.push(e)
      //             }
      //           })
      //           let list = [{
      //             value: '',
      //             label: 'All'
      //           }]
      //           tempList.clients = tmpClient.length > 1 ? list.concat(tmpClient) : tmpClient
      //           _this.filter.client = tempList.clients.length ? tempList.clients[0].value : ''
      //         }
      //         if (item.type === 'subprogram') {
      //           let tmpSubprogram = []
      //           this.initFilters.subprograms.forEach(e => {
      //             if (item.list.indexOf(e.value) !== -1) {
      //               tmpSubprogram.push(e)
      //             }
      //           })
      //           let list = [{
      //             value: '',
      //             label: 'All'
      //           }]
      //           tempList.subprograms = tmpSubprogram.length > 1 ? list.concat(tmpSubprogram) : tmpSubprogram
      //           _this.filter.subprogram = tempList.subprograms.length ? tempList.subprograms[0].value : ''
      //         }
      //       }
      //     })
      //   }
      // }
      // this.filters = tempList
      // this.$nextTick()
    },
    sort (type, j) {
      let list = [...this.graphs]
      let temp = list[j]
      if (type === 'up') {
        list[j] = list[j - 1]
        list[j - 1] = temp
      } else if (type === 'down') {
        list[j] = list[j + 1]
        list[j + 1] = temp
      }
      this.graphs = list
    },
    reload () {
      if (!this.rewardsDashboard) {
        this.reloadFlag = ['item-' + this.pageNum]
        this.$root.$emit('admin-reload-dashboard')
      } else {
        this.$root.$emit('cashback-rewards-reload-dashboard')
      }
    },
    resetFilter () {
      this.filter = {
        bank: 'All',
        type: '',
        client: 'All',
        range: 'Weekly',
        currency: 'USD',
        subprogram: 'All',
        bin: 'All',
        offset: null
      }
      this.$nextTick(() => {
        this.reload()
      })
    },

    async initTopFilters () {
      this.loading = true
      const resp = await request('/admin/fis/dashboard/filters')
      this.loading = false
      if (resp.success) {
        let { ...temp } = resp.data
        this.initFilters = temp
        this.filters = resp.data
        this.filters.ranges.push({
          label: 'Custom Range',
          value: 'custom_range'
        })
        this.filter.range = this.filters.ranges.length ? this.filters.ranges[1].value : null
        this.graphs = resp.data.graphs
        this.updateGraph()
      }
    },
    updateGraph (added) {
      let num = 0
      this.graphShowList = []
      this.graphsList = []
      this.graphs.forEach(e => {
        if (!e.hidden) {
          this.graphShowList.push(e)
        }
      })
      this.graphShowList.forEach((element, index) => {
        if (index % this.num === 0) {
          this.graphsList.push('item-' + (index / this.num))
          num++
        }
      })
      if (this.graphShowList.length > num * this.num) {
        this.graphsList.push('item-' + num)
      }
      this.changeGraph(this.pageNum, added)
    },
    async saveOrder (evt) {
      if (evt.oldIndex === evt.newIndex) {
        return
      }
      let temp = []
      let i = 0
      this.graphs.forEach(e => {
        if (e.hidden) {
          temp.push(e)
        } else {
          temp.push(this.graphShowList[i])
          i++
        }
      })
      if (temp.length) {
        this.graphs = temp
      }
      this.saveConfig()
    },
    cacheConfig () {
      this.oldGraphs = _.cloneDeep(this.graphs)
    },
    async saveConfig () {
      let update = true
      const added = []
      if (this.oldGraphs) {
        const o = JSON.stringify(this.oldGraphs)
        const n = JSON.stringify(this.graphs)
        if (o === n) {
          update = false
        } else {
          const wasHidden = {}
          _.forEach(this.oldGraphs, v => {
            if (v.hidden) {
              wasHidden[v.key] = true
            }
          })
          _.forEach(this.graphs, v => {
            if (!v.hidden && wasHidden[v.key]) {
              added.push(v.key)
            }
          })
        }
      }

      if (update) {
        this.updateGraph(added)
        await request('/admin/fis/dashboard/save-config', 'post', {
          graphs: this.graphs
        })
      }
    },
    changeGraph (e, added = null) {
      this.pageNum = e
      this.graphs.forEach(element => {
        if (e.hidden) {
          this.$store.commit('Fis/updateGraphLoad', {
            name: element.key,
            value: false
          })
        } else {
          this.$store.commit('Fis/updateGraphLoad', {
            name: element.key,
            value: true
          })
        }
      })
      this.graphShowList.forEach((element, index) => {
        if (index >= e * this.num && index < (e + 1) * this.num) {
          this.$store.commit('Fis/updateGraphLoad', {
            name: element.key,
            value: true
          })
        } else {
          this.$store.commit('Fis/updateGraphLoad', {
            name: element.key,
            value: false
          })
        }
      })
      if (this.reloadFlag.indexOf('item-' + e) === -1) {
        this.reloadFlag.push('item-' + e)
        this.$root.$emit('admin-reload-dashboard')
      } else if (added && added.length) {
        this.$nextTick(() => {
          this.$root.$emit('admin-reload-dashboard-cards', added)
        })
      }
    }
  },
  async mounted () {
    await this.initTopFilters()
    this.reload()
  }
}
</script>

<style lang="scss">
.fis__dashboard__index_page.graph__dashboard__index_page {
  .q-carousel {
    margin: auto -20px;
    padding-left: 20px;
    padding-right: 20px;
    padding-bottom: 30px;

    .draggable-container > div {
      padding: 8px;
    }

    .q-carousel-slide {
      padding: 0;
    }

    .q-carousel-left-arrow,
    .q-carousel-right-arrow {
      color: #fff !important;
      // background: var(--q-color-primary) !important;
      opacity: 0;
    }
    &:hover {
      .q-carousel-left-arrow,
      .q-carousel-right-arrow {
        opacity: 1;
        &:hover {
          background: var(--q-color-primary) !important;
        }
      }
    }
    .q-carousel-quick-nav {
      background: none;
      bottom: -10px;
    }
  }
  .date-range-filter {
    width: 255px;
    .q-field-label {
      flex-basis: 50px !important;
    }
  }
  .page-content > .row {
    max-width: 1232px;
  }
  .common-dashboard-card {
    .q-card-main {
      > h3 {
        margin: 0;
        font-size: 16px;
        line-height: 1em;
        color: black;
        text-align: center;
        small {
          font-size: 12px;
          color: #666;
        }
      }
      > h5 {
        margin: 3px 0 0;
        font-size: 12px;
        font-weight: normal;
        line-height: 1em;
        color: #666;
        text-align: center;
        transform: scale(0.9);
      }
    }
  }
}
@media (max-width: 1250px) {
  .fis__dashboard__index_page.graph__dashboard__index_page
    .page-content
    > .row {
    max-width: 816px;
  }
}
@media (max-width: 918px) {
  .fis__dashboard__index_page.graph__dashboard__index_page {
    .fun-group .q-field {
      width: calc(33.3% - 11px) !important;
      margin-right: 10px !important;
      .row {
        display: flex;
        justify-content: space-between;
      }
      .q-field-content {
        flex-basis: auto;
      }
    }
    .fun-group .q-btn {
      margin-left: 0;
      // margin-top: 32px;
    }
  }
}
@media (max-width: 575px) {
  .fis__dashboard__index_page.graph__dashboard__index_page {
    .fun-group .q-field {
      width: calc(50% - 11px) !important;
    }
    .fun-group .q-btn {
      margin-left: 0;
      margin-top: 32px;
    }
    .q-carousel {
      padding-left: 0px;
      padding-right: 0px;
      .draggable-container > div {
        width: 100%;
      }
    }
  }
}
@media (max-width: 375px) {
  .fis__dashboard__index_page.graph__dashboard__index_page {
    .fun-group .q-field {
      min-width: 100% !important;
    }
  }
}
.graph-setting {
  min-width: 250px;

  .q-item {
    padding: 0 !important;
    padding-right: 10px !important;
    &:hover {
      .sort-icon {
        visibility: visible;
        opacity: 1;
        pointer-events: auto;
      }
    }
  }
  .q-item-main {
    padding: 8px 16px;

    > span {
      white-space: nowrap;
    }
  }

  span {
    margin-left: 6px;
    font-size: 1rem;
    color: #0c0c0c;
  }
  .q-icon {
    font-size: 24px;
    color: #737373;
  }
  .sort-icon {
    visibility: hidden;
    opacity: 0;
    pointer-events: none;
  }
  .text-grey {
    span {
      margin-left: 30px;
    }
  }
}
</style>
