<template>
  <Card class="fis-dashboard-mcc"
        title="MCC Metrics"
        ref="card"
        keyName="Mcc"
        @reload="reload"
        :report-url="reportUrl">
    <q-btn-toggle v-model="mode"
                  dense
                  no-caps
                  class="q-btn-toggle-sm mode-toggle"
                  color="grey-3"
                  text-color="dark"
                  toggle-color="primary"
                  toggle-text-color="white"
                  :options="modes" />
    <q-btn-toggle v-model="scope"
                  dense
                  no-caps
                  class="q-btn-toggle-sm scope-toggle"
                  color="grey-3"
                  text-color="dark"
                  toggle-color="primary"
                  toggle-text-color="white"
                  :options="scopes" />
    <h5>{{ start | date('MM/DD/YY', true) }} thru {{ end | date('MM/DD/YY', true) }}</h5>
    <div class="chart-wrap">
      <div class="chart-container"></div>
      <div class="legend-area">
        <div class="legend-top">
          <template v-if="activeData">
            <h5>
              #{{ active + 1}}
              <div class="sub">
                <div class="chart-tooltip-circle"
                     :style="{backgroundColor: colors[active]}"></div> {{ activeData.mcc }}
              </div>
            </h5>
            <div class="des">{{ activeData.des }}</div>
          </template>
          <template v-else>
            <h5>{{ scope === 'top10' ? 'TOP 10' : 'All' }}</h5>
            <div class="wide">Categories</div>
          </template>
        </div>
        <div class="legend-center">
          <h4>{{ activeData ? activeData.amt : amt | moneyFormat }}</h4>
          <div class="sub">{{ activeData ? activeData.cnt : cnt | number }} Transactions</div>
        </div>
        <div class="legend-bottom row">
          <div class="col-4">
            <div class="label">PANS</div>
            <div class="value">{{ activeData ? activeData.pan : (scope === 'all' ? pans.all : pans[mode]) | number }}</div>
          </div>
          <div class="col-4">
            <div class="label">AVG.</div>
            <div class="value">{{ activeData ? activeData.avg : c.avg(amt, cnt) | moneyFormat }}</div>
          </div>
          <div class="col-4">
            <div class="label">%</div>
            <div class="value">{{ activeData ? activeData[mode + 'Per'] : c.avg(mode === 'amt' ? amt : cnt, mode === 'amt' ? amtTotal : cntTotal) | percent(2, false, false) }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="row gutter-xs programs pl-20 mt-6">
      <div class="col-sm-2-5"
           :class="{active: active === i}"
           v-for="(p, i) in detail[mode]"
           @mouseenter="highlight(i)"
           @mouseleave="downplay(i)"
           :key="p.mcc">
        <div class="circle"
             :style="{backgroundColor: colors[i]}"></div>
        <div class="program-label">
          {{ p.mcc }}
        </div>
        <q-tooltip anchor="bottom left"
                   self="top left">{{ p.des }}</q-tooltip>
      </div>
    </div>
  </Card>
</template>

<script>
import Card from './card'
import echarts from 'echarts'
import _ from 'lodash'
import moment from 'moment'
import { avg, request, toSelectOptions } from '../../../common'
import CachedGraph from '../../../mixins/fis/CachedGraph'
import $ from 'jquery'
export default {
  name: 'FisDashboardMCC',
  mixins: [
    CachedGraph
  ],
  components: {
    Card
  },
  props: {
    params: {
      type: Object
    },
    filters: {
      type: Object
    }
  },
  data () {
    return {
      graphName: 'mcc',
      start: null,
      end: null,
      modes: [
        { value: 'amt', label: 'Volume' },
        { value: 'cnt', label: 'Count' }
      ],
      mode: 'amt',
      scopes: toSelectOptions(['top10', 'all'], true),
      scope: 'top10',
      data: {},
      detail: {
        amt: [],
        cnt: []
      },
      pans: {
        total: 0,
        amt: 0,
        cnt: 0
      },
      colors: [
        '#F30023',
        '#0071F0',
        '#19C53E',
        '#8D7DEF',
        '#F85E63',
        '#F58C66',
        '#F2B800',
        '#B467D6',
        '#007F36',
        '#00CF91'
      ],
      active: null,
      chart: null
    }
  },
  computed: {
    amtTotal () {
      return _.sumBy(this.data.detail, 'amt')
    },
    cntTotal () {
      return _.sumBy(this.data.detail, 'cnt')
    },
    amt () {
      if (this.scope === 'top10') {
        return _.sumBy(this.detail[this.mode], 'amt')
      }
      return this.amtTotal
    },
    cnt () {
      if (this.scope === 'top10') {
        return _.sumBy(this.detail[this.mode], 'cnt')
      }
      return this.cntTotal
    },
    activeData () {
      return this.detail[this.mode][this.active]
    },
    reportUrl () {
      let url = `#/a/fis/report/monetary?from-chart=card_spend` + this.commonReportUrlSuffix()
      if (this.data.start) {
        url += '&start=' + moment.utc(this.data.start).format('YYYY-MM-DD')
      }
      if (this.data.end) {
        url += '&end=' + moment.utc(this.data.end).format('YYYY-MM-DD')
      }
      return url
    }
  },
  watch: {
    mode (v, o) {
      if (v && o) {
        this.initChart()
      }
    }
  },
  methods: {
    async reload (p) {
      if (await this.loadWithDbCache(p)) {
        return
      }
      const resp = await request(`/admin/fis/dashboard/mcc/data`, 'get', this.params)
      if (p && p.cb) {
        p.cb(resp)
      }
      if (resp.success) {
        this.end = resp.data.end
        this.start = resp.data.start
        this.onResponseData(resp.data)
        await this.saveDbCache(resp.data)
      }
    },
    onResponseData (data) {
      for (const d of data.detail) {
        d.amt = parseInt(d.amt)
        d.cnt = parseInt(d.cnt)
        d.avg = avg(d.amt, d.cnt)
      }
      this.saveCache(data)
      this.data = data
      for (const d of this.data.detail) {
        d.amtPer = avg(d.amt, this.amtTotal)
        d.cntPer = avg(d.cnt, this.cntTotal)
      }
      this.detail.amt = _.slice(_.reverse(_.sortBy(this.data.detail, 'amt')), 0, 10)
      this.detail.cnt = _.slice(_.reverse(_.sortBy(this.data.detail, 'cnt')), 0, 10)
      this.$nextTick(() => {
        this.initChart()
      })
    },
    initChart () {
      const chart = echarts.init(this.$el.querySelector('.chart-container'), 'fis')
      const data = []
      let total = 0
      _.forEach(this.detail[this.mode], (v, i) => {
        const vv = v[this.mode]
        data.push({
          value: vv,
          itemStyle: {
            color: this.colors[i]
          }
        })
        total += vv
      })
      const other = 0.36
      data.push({
        value: total / (1 - other) * other,
        itemStyle: {
          color: '#fff'
        }
      })
      const options = _.defaultsDeep({
        series: [
          {
            name: this.title,
            type: 'pie',
            left: 0,
            top: '-70%',
            right: 0,
            bottom: 0,
            radius: ['75%', '90%'],
            center: ['50%', '80%'],
            hoverOffset: 5,
            startAngle: 205,
            data: data,
            labelLine: {
              normal: {
                show: false
              }
            },
            itemStyle: {
              borderCap: 'round',
              borderJoin: 'round'
            }
          }
        ]
      }, this.cc.fis.chart)
      chart.setOption(options)
      chart.resize()
      chart.on('mouseover', 'series', p => {
        this.active = p.dataIndex
      })
      chart.on('mouseout', 'series', () => {
        this.active = null
      })
      this.chart = chart
      $(window).on('resize', this.resize)
    },
    resize () {
      this.chart.resize()
    },
    chartAction (type, dataIndex) {
      this.chart.dispatchAction({
        type,
        seriesIndex: 0,
        dataIndex
      })
    },
    highlight (dataIndex) {
      this.active = dataIndex
      this.chartAction('highlight', dataIndex)
    },
    downplay (dataIndex) {
      this.active = null
      this.chartAction('downplay', dataIndex)
    }
  }
}
</script>

<style lang="scss">
.fis-dashboard-mcc {
  .q-card-main {
    padding: 0 10px 15px !important;
  }
  .mode-toggle {
    position: absolute;
    left: 10px;
    z-index: 10;
  }
  .scope-toggle {
    position: absolute;
    right: 10px;
    z-index: 10;
  }
  .chart-wrap,
  .chart-container {
    position: relative;
    width: 315px;
    height: 250px !important;
    margin: 0 auto;
  }
  .chart-container {
    position: absolute;
    margin-top: 0 !important;
    z-index: 1;
  }
  .legend-area {
    position: absolute;
    width: 315px;
    margin: 0 auto;
    text-align: center;
    padding-top: 70px;
    .legend-top {
      color: red;
      font-size: 12px;
      font-weight: 300;
      text-transform: uppercase;
      height: 47px;
      h5 {
        margin: 0 0 3px 0;
        font-size: 20px;
        font-weight: 500;
        line-height: 1em;
        letter-spacing: 2px;
        display: flex;
        justify-content: center;
        align-items: center;
        .sub {
          margin-left: 10px;
          font-size: 14px;
          color: #888;
          letter-spacing: normal;
        }
      }
      .des {
        max-width: 170px;
        margin: 0 auto;
        text-overflow: ellipsis;
        text-transform: none;
        overflow: hidden;
        line-height: 1em;
        -webkit-line-clamp: 2;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        hyphens: auto;
      }
      .wide {
        letter-spacing: 3px;
      }
    }
    .legend-center {
      margin-top: 15px;
      h4 {
        font-size: 26px;
        line-height: 1em;
        margin-bottom: 3px;
      }
      .sub {
        color: #888;
      }
    }
    .legend-bottom {
      width: 185px;
      margin: 15px auto 0;
      font-size: 12px;
      color: #666;
      .label {
        font-weight: 500;
      }
    }
  }
  .program-label {
    font-weight: 300;
    cursor: pointer;
  }
}
</style>
