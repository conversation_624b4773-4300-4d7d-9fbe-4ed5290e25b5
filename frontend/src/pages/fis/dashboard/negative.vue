<template>
  <Card class="fis-dashboard-negative fis-dashboard-data-zoom"
        title="Negative Balances"
        ref="card"
        keyName="Negative"
        @reload="reload"
        :report-url="reportUrl">
    <div class="row header">
      <div class="col-auto title mr-auto">
        <div class="value">{{ total | number }}</div>
      </div>
      <div class="col-auto right">
        <div class="label">PANs</div>
        <div class="value">{{ pcount | number }}</div>
      </div>
      <div class="col-auto right">
        <div class="label">Total</div>
        <div class="value red">({{ amount | moneyFormat(params.currency, false, false) }})</div>
      </div>
      <div class="col-auto right ml-20 bolder">
        <div class="label">Avg</div>
        <div class="value red">({{ avg | moneyFormat(params.currency, false, false) }})</div>
      </div>
    </div>
    <div class="sub-header">
      <div class="label"
           v-if="initialLabel">{{ initialLabel }}</div>
      <div class="label"
           v-else-if="startDate && endDate">
        {{ startDate | date('MM/DD/YY', true) }} thru {{ endDate | date('MM/DD/YY', true) }}
      </div>
    </div>
    <div class="chart-container"></div>
  </Card>
</template>

<script>
import DataZoomChartMixin from '../../../mixins/fis/DataZoomChartMixin'
import _ from 'lodash'
export default {
  name: 'FisDashboardNegative',
  mixins: [
    DataZoomChartMixin
  ],
  data () {
    return {
      graphName: 'negative',
      negativeAmount: true,
      requestUrl: `/admin/fis/dashboard/negative/data`,
      color: '#AA4EFF',
      handleColor: '#9430A1',
      gradientColor: '#f4e3ff'
    }
  },
  computed: {
    mergedTotal () {
      if (this.initialLabel) {
        return this.items.length ? this.items[this.items.length - 1].count : 0
      }
      const proxies = {}
      _.forEach(this.items, item => {
        _.forEach(item.detail, (v, k) => {
          proxies[k] = v
        })
      })
      return Object.keys(proxies).length
    },
    mergedAmount () {
      if (this.initialLabel) {
        return this.items.length ? this.items[this.items.length - 1].amount : 0
      }
      const proxies = {}
      _.forEach(this.items, item => {
        _.forEach(item.detail, (v, k) => {
          proxies[k] = v
        })
      })
      let amount = 0
      _.forEach(proxies, (v, k) => {
        amount += v
      })
      return amount
    },
    mergedAvg () {
      if (!this.mergedTotal) {
        return 0
      }
      return this.mergedAmount / this.mergedTotal
    }
  },
  methods: {
  }
}
</script>
