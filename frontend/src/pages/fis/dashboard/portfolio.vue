<template>
  <Card class="fis-dashboard-portfolio"
        title="Portfolio"
        ref="card"
        keyName="Portfolio"
        @reload="reload"
        :report-url="reportUrl">
    <div class="ph-15">
      <div>
        <div class="latest-title">{{ latest | moneyFormat(params.currency, false, false) }}</div>
      </div>
      <div class="row sub-title-row">
        <div :class="compare ? 'col-8' : 'col-10'"
             class="current-date">
          <q-btn flat
                 :label="$options.filters.date(date, 'MMMM DD, YYYY', true)"
                 size="sm"
                 no-caps
                 icon-right="mdi-calendar">
            <q-popover v-model="selectingDate"
                       @hide="selectedDate">
              <q-datetime-picker type="date"
                                 v-model="date"></q-datetime-picker>
            </q-popover>
          </q-btn>
        </div>
        <div class="col-2 text-right scale-95"
             v-if="compare">Day %</div>
        <div class="col-2 text-right scale-95">Growth %</div>
      </div>
      <div class="row value-row"
           v-for="(row, i) in rows"
           :key="i">
        <div :class="compare ? 'col-4' : 'col-5'"
             v-for="(type, j) in ['start', 'end']"
             :key="j">
          <div>
            <span class="color-block"
                  :style="{backgroundColor: rowDef[i][type].color}"></span>
            <span class="row-title">{{ rowDef[i][type].title }}</span>
          </div>
          <div class="row-value"
               v-if="i === 'sum' || i === 'avg'">
            {{ row[type] | moneyFormat(params.currency, false, false) }}
          </div>
          <div class="row-value"
               v-else-if="i === 'count'">
            {{ row[type] | number }}
          </div>
        </div>
        <div class="col-2 text-right scale-95"
             v-for="key in (compare ? ['day', 'delta'] : ['delta'])"
             :key="key"
             :class="row[key] > 0 ? 'positive' : 'negative'">
          <q-icon :name="row[key] > 0 ? 'mdi-arrow-up-bold' : 'mdi-arrow-down-bold'"
                  v-if="row[key] !== 0"></q-icon>
          {{ row[key] | abs | percent }}
        </div>
      </div>
    </div>
    <div class="chart-container"></div>
    <div class="day-selector"
         :class="{initialized: series.start}">
      <q-btn flat
             :class="canScrollLeft ? '' : 'opacity-25'"
             :style="{width: `${cellWidth / 2}px`, height: `${cellWidth}px`}"
             class="btn-arrow"
             icon="mdi-chevron-left"
             @click="scrollLeft"></q-btn>
      <div class="dates-container"
           :style="{width: `calc(100% - ${cellWidth})px`}"
           ref="dates">
        <div class="dates"
             :style="{width: datesWidth + 'px'}">
          <q-btn round
                 flat
                 class="btn-date"
                 @click="selectDate(d, $event)"
                 v-for="d in dates"
                 :key="d"
                 :style="{width: `${cellWidth}px`, height: `${cellWidth}px`}"
                 :class="{active: date === d}">
            {{ d | date('DD', true) }}<br />
            {{ d | date('MMM', true) }}
          </q-btn>
        </div>
      </div>
      <q-btn flat
             :class="canScrollRight ? '' : 'opacity-25'"
             :style="{width: `${cellWidth / 2}px`, height: `${cellWidth}px`}"
             class="btn-arrow opacity"
             icon="mdi-chevron-right"
             @click="scrollRight"></q-btn>
    </div>
  </Card>
</template>

<script>
import Card from './card'
import _ from 'lodash'
import moment from 'moment'
import echarts from 'echarts'
import DateBasedChartMixin from '../../../mixins/fis/DateBasedChartMixin'
import $ from 'jquery'

export default {
  name: 'FisDashboardPortfolio',
  mixins: [
    DateBasedChartMixin
  ],
  components: {
    Card
  },
  props: {
    params: {
      type: Object
    }
  },
  data () {
    return {
      graphName: 'portfolio',
      latest: null,
      latestDate: null,
      rows: [],
      rowDef: {
        sum: {
          start: {
            color: '#9A47FF',
            title: 'Beginning Balance'
          },
          end: {
            color: '#4C84FF',
            title: 'Ending Balance'
          }
        },
        count: {
          start: {
            color: '#B87EFF',
            title: 'Funded Cards'
          },
          end: {
            color: '#81A8FF',
            title: 'Funded Cards'
          }
        },
        avg: {
          start: {
            color: '#D1ABFF',
            title: 'Avg Balance'
          },
          end: {
            color: '#AEC7FF',
            title: 'Avg Balance'
          }
        }
      },
      series: [],
      requestUrl: `/admin/fis/dashboard/portfolio/data`,
      requestCb: data => {
        this.rows = data.rows
        this.series = data.series
        this.latest = data.latest
        this.latestDate = data.latestDate
      },
      selectingDate: false,
      chart: null
    }
  },
  computed: {
    compare () {
      return this.rows.sum && 'day' in this.rows.sum
    }
  },
  methods: {
    selectedDate () {
      this.date = moment(this.date).utc().format('YYYY-MM-DDTHH:mm:ssZ')
      this.offset = 4
      this.params.direct = true
      this.$refs.card.reload()
      this.selectingDate = false
    },
    resize () {
      this.chart.resize()
    },
    initChart () {
      console.log(this.series)
      let start = []
      let end = []
      let lastStart = this.series['start'][0]
      let lastEnd = this.series['end'][0]
      this.series.start.forEach((e, k) => {
        if (e) {
          lastStart = e
          start.push(e)
        } else {
          k > 0 ? start.push(lastStart) : start.push(e)
        }
      })

      this.series.end.forEach((e, k) => {
        if (e) {
          lastEnd = e
          end.push(e)
        } else {
          k > 0 ? end.push(lastEnd) : end.push(e)
        }
      })
      console.log(start)
      console.log(end)

      const chart = echarts.init(this.$el.querySelector('.chart-container'), 'fis')
      this.chart = chart
      const options = _.defaultsDeep({
        grid: {
          left: 0,
          top: 15,
          right: 0,
          bottom: 0
        },
        xAxis: {
          show: false,
          type: 'category',
          data: this.axis,
          boundaryGap: false,
          axisPointer: {
            show: true,
            lineStyle: {
              type: 'dash'
            },
            label: {
              show: false
            }
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          show: false,
          scale: true,
          offset: 20,
          type: 'value'
        },
        series: [
          {
            data: start,
            type: 'line',
            smooth: false,
            symbol: (value, params) => {
              return params.name === this.axisCurrent ? 'emptyCircle' : 'none'
            },
            symbolSize: 10,
            areaStyle: {
              opacity: 0.05
            },
            z: 2,
            animation: false
          },
          {
            data: end,
            type: 'line',
            smooth: false,
            symbol: (value, params) => {
              return params.name === this.axisCurrent ? 'emptyCircle' : 'none'
            },
            symbolSize: 10,
            areaStyle: {
              opacity: 0.05
            },
            z: 1,
            animation: false
          }
        ],
        tooltip: {
          formatter: (params) => {
            if (!params && !params.length) {
              return ''
            }
            if (params[0].name === '') {
              return ''
            }
            let s = `${params[0].name}<br/>`
            _.forEach(params, param => {
              s += `<div><div class="chart-tooltip-box" style="background: ${param.color}"></div> <small>${this.c.moneyFormat(param.value, this.params.currency, false, false)}</small></div>`
            })
            return s
          }
        }
      }, this.cc.fis.chart)
      chart.setOption(options)
      chart.resize()

      setTimeout(() => {
        chart.resize()
      }, 200)
      $(window).on('resize', this.resize)
    }
  }
}
</script>

<style lang="scss">
.fis-dashboard-portfolio {
  .latest-title {
    display: inline-block;
    margin-right: 15px;
    font-size: 22px;
    font-weight: 600;
    color: #388e3c;
    line-height: 32px;
    vertical-align: top;
  }

  .sub-title-row {
    font-size: 12px;
    font-weight: 600;
    font-style: italic;
    margin: 8px 0 10px;
    color: #4c6072;
  }

  .current-date .q-btn {
    padding: 0;
    min-height: 0;
    color: black;

    .q-btn-inner {
      font-size: 12px;
    }

    &:hover {
      color: var(--q-color-primary);
    }
  }

  .value-row {
    font-size: 12px;

    > div {
      position: relative;
    }

    .color-block {
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 3px;
      margin-right: 4px;
    }

    .row-title {
      color: #a9a9a9;
      font-style: italic;
      font-size: 13px;
      white-space: nowrap;
      width: calc(100% - 14px);
      display: block;
      position: absolute;
      top: -2px;
      left: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .row-value {
      margin-bottom: 5px;
      padding-left: 14px;
      color: #333;
      font-style: italic;
    }

    .positive {
      color: #6fae3d;
    }

    .negative {
      color: #be031a;
    }
  }

  .chart-container {
    margin-top: auto !important;
    height: calc(100% - 225px) !important;

    > div:first-of-type {
      height: 100% !important;

      > canvas {
        top: auto !important;
        bottom: 0 !important;
      }
    }

    .chart-tooltip-box {
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 3px;
      background: silver;
      margin-right: 3px;
      vertical-align: middle;
    }
  }
}
</style>
