<template>
  <Card class="fis-dashboard-settle-no-auth fis-dashboard-data-zoom"
        title="Settle No Auth"
        ref="card"
        keyName="SettleNoAuth"
        @reload="reload"
        :report-url="filteredReportUrl">
    <div class="row header">
      <div class="col-auto title mr-auto">
        <div class="value">{{ total | number }}</div>
      </div>
      <div class="col-auto right">
        <div class="label">PANs</div>
        <div class="value">{{ pcount | number }}</div>
      </div>
      <div class="col-auto right">
        <div class="label">Total</div>
        <div class="value">{{ amount | moneyFormat(params.currency, false, false) }}</div>
      </div>
      <div class="col-auto right ml-20 bolder">
        <div class="label">Avg</div>
        <div class="value">{{ avg | moneyFormat(params.currency, false, false) }}</div>
      </div>
    </div>
    <div class="sub-header">
      <div class="label"
           v-if="startDate && endDate">
        {{ startDate | date('MM/DD/YY', true) }} thru {{ endDate | date('MM/DD/YY', true) }}
      </div>
       <div class="label"
           v-else-if="initialLabel">{{ initialLabel }}</div>
    </div>
    <div class="chart-container"></div>
  </Card>
</template>

<script>
import DataZoomChartMixin from '../../../mixins/fis/DataZoomChartMixin'
export default {
  name: 'FisDashboardSettleNoAuth',
  mixins: [
    DataZoomChartMixin
  ],
  data () {
    return {
      graphName: 'settle_no_auth',
      reportName: 'monetary',
      requestUrl: `/admin/fis/dashboard/settle_no_auth/data`,
      color: '#DC7A11',
      handleColor: '#DB7A11',
      gradientColor: '#FBE7D8'
    }
  },
  computed: {
    filteredReportUrl () {
      return this.reportUrl + '&txnTypeName=Purchase Approved Settled NoAuth' + this.commonReportUrlSuffix()
    }
  }
}
</script>
