<template>
  <Card class="fis-dashboard-card-spend fis-dashboard-card-mixed"
        title="Card Spend"
        ref="card"
        keyName="CardSpend"
        @reload="reload"
        :report-url="reportUrl">
    <template slot="title">
      <q-btn icon="mdi-poll"
             :class="{active: mode === 'bar'}"
             @click="setMode('bar')"
             class="btn-mini btn-mini-toggle mt--1 ml-10"
             outline></q-btn>
      <q-btn icon="mdi-chart-line-variant"
             :class="{active: mode === 'line'}"
             @click="setMode('line')"
             class="btn-mini btn-mini-toggle mt--1 ml-5 mr-auto"
             outline></q-btn>
    </template>

    <div class="row m-10 auto-expand-row">
      <div class="col-5">
        <div class="text-gray">SPEND</div>
        <div class="font-20"
             v-if="amount < 1000000000">{{ amountFormat }}</div>
        <div class="font-20"
             v-else>
          ${{ Math.round(amount / 100000) | number }}k
          <q-tooltip>{{ amount | moneyFormat }}</q-tooltip>
        </div>
      </div>
      <div class="col-2">
        <div class="text-gray">TRANS</div>
        <div class="font-20">{{ count | number }}</div>
      </div>
      <div class="col-2">
        <div class="text-gray">PANS</div>
        <div class="font-20">{{ pcount | number }}</div>
      </div>
      <div class="col-3">
        <div class="text-gray">AVERAGE</div>
        <div class="font-20">{{ count ? (amount / count) : 0 | moneyFormat }}</div>
      </div>
    </div>
    <h5>{{ start | date('MM/DD/YY', true) }} thru {{ end | date('MM/DD/YY', true) }}</h5>

    <!-- <div class="top-range-label"
         v-show="mode === 'line'">
      <template v-if="startDate && endDate">
        {{ startDate | date('MM/DD/YY', true) }} thru {{ endDate | date('MM/DD/YY', true) }}
      </template>
    </div> -->
    <div class="chart-container line-chart-container"
         v-show="mode === 'line'"></div>

    <div class="chart-container bar-chart-container"
         v-show="mode === 'bar'"></div>
    <div class="row gutter-xs legend"
         v-show="mode === 'bar'">
      <div class="col-auto"
           :style="percentBoxStyle"
           v-for="(p, name) in series"
           :key="'box_' + name">
        <q-chip :style="{backgroundColor: p.color}">
          {{ p.percent | percent }}
          <q-tooltip anchor="bottom middle"
                     self="top middle">{{ p.fullName || p.name }}</q-tooltip>
        </q-chip>
      </div>
    </div>

    <div class="row gutter-xs programs"
         :class="{high: this.mode === 'line'}">
      <div class="col-6"
           :class="{active: active === p.name}"
           v-for="(p, name) in allSeries"
           :key="p.name">
        <div class="circle"
             :style="{backgroundColor: p.color}"></div>
        <div class="program-label">{{ p.fullName || p.name }}
          <q-tooltip anchor="bottom left"
                     self="top left">{{ name }}</q-tooltip>
        </div>
      </div>
    </div>
  </Card>
</template>

<script>
import MultipleDataZoomChartMixin from '../../../mixins/fis/MultipleDataZoomChartMixin'
import _ from 'lodash'
import { avg } from '../../../common'
export default {
  name: 'FisDashboardCardSpend',
  mixins: [
    MultipleDataZoomChartMixin
  ],
  data () {
    return {
      requestUrl: `/admin/fis/dashboard/card-spend/data`,
      graphName: 'spend',
      reportName: 'spend',
      defaultMode: 'line',
      series: {
        'Purchase Approved Settled': {
          name: 'Purchase Approved Settled',
          fullName: 'Purchase (Auth)',
          amount: 0,
          count: 0,
          pcount: 0,
          avg: 0,
          percent: 0,
          color: '#F83D03',
          subColor: '#ff7245'
        },
        'Purchase Approved Settled NoAuth': {
          name: 'Purchase Approved Settled NoAuth',
          fullName: 'Purchase (No Auth)',
          amount: 0,
          count: 0,
          pcount: 0,
          avg: 0,
          percent: 0,
          color: '#F98730',
          subColor: '#ffae70'
        },
        'ATM Approved Settled': {
          name: 'ATM Approved Settled',
          fullName: 'ATM W/D (Auth)',
          amount: 0,
          count: 0,
          pcount: 0,
          avg: 0,
          percent: 0,
          color: '#5188BF',
          subColor: '#84bffa'
        },
        'ATM Approved Settled NoAuth': {
          name: 'ATM Approved Settled NoAuth',
          fullName: 'ATM W/D (No Auth)',
          amount: 0,
          count: 0,
          pcount: 0,
          avg: 0,
          percent: 0,
          color: '#39ABD8',
          subColor: '#88d7f6'
        }
      },
      seriesNames: [
        'Purchase Approved Settled', 'Purchase Approved Settled NoAuth',
        'ATM Approved Settled', 'ATM Approved Settled NoAuth'
      ],
      handleColor: '#DBE9FC',
      handleDarkColor: '#0071F0',
      withTotalLine: 'Total'
    }
  },
  methods: {
    buildTooltipHtml (d) {
      const detail = _.cloneDeep(this.series)
      let html = `<div class="chart-tooltip-title">${this.$options.filters.date(this.c.fullUTC(d.date), 'DD MMM YYYY')}</div>
              <table class="table chart-tooltip-table">
              <thead>
              <tr>
                <th>TRANS</th>
                <th>SPEND</th>
                <th>PANS</th>
                <th>AVG</th>
                <th>%</th>
              </tr>
              </thead>
              <tbody>`
      let total = 0
      _.forEach(d.rs, (v, k) => {
        total += parseInt(v.amount)
      })
      _.forEach(detail, (p, name) => {
        p.count = p.amount = p.avg = p.percent = p.pcount = 0
        _.forEach(d.rs, (v, k) => {
          if (v.name === name) {
            p.count = parseInt(v.count)
            p.amount = parseInt(v.amount)
            p.pcount = parseInt(v.pcount)
            p.avg = avg(p.amount, p.count)
            p.percent = avg(p.amount, total)
            return false
          }
        })
        html += `<tr>
                <td><div class="chart-tooltip-circle" style="background: ${p.color}"></div> ${p.count}</td>
                <td>${this.c.moneyFormat(p.amount)}</td>
                <td>${this.$options.filters.number(p.pcount)}</td>
                <td>${this.c.moneyFormat(p.avg)}</td>
                <td>${this.$options.filters.percent(p.percent, 1)}</td>
              </tr>`
      })
      if (this.mode === 'line' && this.withTotalLine) {
        const tl = {
          count: 0,
          amount: 0,
          pcount: 0,
          avg: 0,
          percent: 0
        }
        _.forEach(detail, (p, name) => {
          tl.count += p.count
          tl.amount += p.amount
          tl.pcount += p.pcount
          tl.avg = avg(tl.amount, tl.count)
          tl.percent = avg(tl.amount, total)
        })
        html += `<tr class="total">
                <td><div class="chart-tooltip-circle" style="border: 2px dotted #FBB72B; background: #fff6e5;"></div> ${tl.count}</td>
                <td>${this.c.moneyFormat(tl.amount)}</td>
                <td>${this.$options.filters.number(tl.pcount)}</td>
                <td>${this.c.moneyFormat(tl.avg)}</td>
                <td>${this.$options.filters.percent(tl.percent, 1)}</td>
              </tr>`
      }
      html += `</tbody></table>`
      return html
    }
  }
}
</script>

<style lang="scss">
.fis-dashboard-card-spend {
  .q-card-main {
    .programs {
      flex-direction: row !important;
    }
    .programs.high {
      height: 90px !important;
      .col-6:last-child {
        .circle {
          border: 2px dotted #fbb72b !important;
          background: #fff6e5 !important;
        }
      }
    }
  }
}
</style>
