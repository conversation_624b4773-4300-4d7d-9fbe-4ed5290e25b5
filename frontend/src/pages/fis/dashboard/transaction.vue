<template>
  <Card class="fis-dashboard-transaction"
        title="Transactions"
        ref="card"
        keyName="Transaction"
        @reload="reload"
        :report-url="reportUrl">
    <div class="header">{{ totalCount | number }}</div>
    <h5>{{ start | date('MM/DD/YY', true) }} thru {{ end | date('MM/DD/YY', true) }}</h5>
    <div class="chart-container"></div>
    <div class="stack-bar">
      <div class="item"
           v-for="(p, name) in detail"
           :style="itemStyle(p)"
           :key="name">
        <q-tooltip anchor="bottom left"
                   self="top left">
          <div class="bold">{{ name }}</div>
          <div class="font-12">Amount: {{ p.amount | moneyFormat(params.currency, false, false) }}</div>
        </q-tooltip>
      </div>
    </div>
    <div class="row gutter-xs programs">
      <div class="col-4"
           :class="{active: active === name}"
           v-for="(p, name) in detail"
           :key="name">
        <div class="circle"
             :style="{backgroundColor: p.color}"></div>
        <div class="program-label">{{ name }}
          <q-tooltip anchor="bottom left"
                     self="top left">{{ name }}</q-tooltip>
        </div>
      </div>
    </div>
  </Card>
</template>

<script>
import Card from './card'
import echarts from 'echarts'
import _ from 'lodash'
import { request } from '../../../common'
import CachedGraph from '../../../mixins/fis/CachedGraph'
import $ from 'jquery'
export default {
  name: 'FisDashboardTransaction',
  mixins: [
    CachedGraph
  ],
  components: {
    Card
  },
  props: {
    params: {
      type: Object
    }
  },
  data () {
    return {
      graphName: 'transaction',
      totalCount: 0,
      detail: {},
      active: null,
      start: null,
      end: null,
      chart: null
    }
  },
  computed: {
    totalAmount () {
      let sum = 0
      _.forEach(this.detail, (v, k) => {
        sum += v.amount
      })
      return sum
    }
  },
  methods: {
    async reload (p) {
      if (await this.loadWithDbCache(p)) {
        return
      }
      const resp = await request(`/admin/fis/dashboard/transaction/data`, 'get', this.params)
      if (p && p.cb) {
        p.cb(resp)
      }
      if (resp.success) {
        this.onResponseData(resp.data)
        await this.saveDbCache(resp.data)
      }
    },
    onResponseData (data) {
      this.saveCache(data)

      this.totalCount = data.totalCount
      this.detail = data.detail
      this.end = data.end
      this.start = data.start
      this.$nextTick(() => {
        this.initChart()
      })
    },
    resize () {
      this.chart.resize()
    },
    initChart () {
      const counts = []
      _.forEach(this.detail, (d, name) => {
        counts.push({
          name,
          value: d.count,
          itemStyle: {
            color: d.color,
            shadowBlur: 5,
            shadowOffsetY: 2,
            shadowColor: '#999'
          }
        })
      })

      const chart = echarts.init(this.$el.querySelector('.chart-container'), 'fis')
      this.chart = chart
      const options = _.defaultsDeep({
        grid: {
          left: 32,
          top: 5,
          right: 10,
          bottom: 10
        },
        xAxis: {
          show: false,
          data: _.keys(this.detail),
          axisPointer: {
            show: true,
            lineStyle: {
              type: 'dash'
            },
            label: {
              show: false
            }
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          axisLine: {
            show: false
          },
          axisLabel: {
            color: '#999',
            fontSize: 10,
            formatter: v => {
              if (v >= 1000) {
                return (v / 1000) + 'k'
              }
              return v
            }
          },
          splitLine: {
            lineStyle: {
              color: '#e6e6e6'
            }
          }
        },
        series: [
          {
            type: 'bar',
            data: counts,
            barWidth: '60%',
            barMinHeight: 0
          }
        ],
        tooltip: {
          formatter: (params) => {
            if (!params && !params.length) {
              return ''
            }
            if (params[0].name === '') {
              return ''
            }
            const d = this.detail[params[0].name]
            if (!d) {
              return ''
            }
            this.active = params[0].name
            return `<div class="mb-5"><div class="chart-tooltip-circle" style="background: ${d.color}"></div> ${params[0].name}</div>
              <div class="field-row"><div class="field-label">PANs:</div><div class="field-value">${this.$options.filters.number(d.pcount)}</div></div>
              <div class="field-row"><div class="field-label">Amount:</div><div class="field-value">${this.c.moneyFormat(d.amount, this.params.currency, false, false)}</div></div>
              <div class="field-row boldest"><div class="field-label">Count:</div><div class="field-value">${this.$options.filters.number(d.count)}</div></div>
              <div class="field-row"><div class="field-label">Avg:</div><div class="field-value">${d.count > 0 ? this.c.moneyFormat(d.amount / d.count, this.params.currency, false, false) : 0}</div></div>`
          }
        }
      }, this.cc.fis.chart)
      chart.setOption(options)
      chart.resize()
      chart.on('mouseover', p => {
        this.active = p.name
      })
      chart.on('mouseout', () => {
        this.active = null
      })
      $(window).on('resize', this.resize)
    },
    itemStyle (p) {
      return {
        backgroundColor: p.color,
        width: `${p.amount / this.totalAmount * 100}%`
      }
    }
  },
  mounted () {
    this.$el.addEventListener('mouseleave', () => {
      this.active = null
    })
  }
}
</script>

<style lang="scss">
.fis-dashboard-transaction {
  .q-card-main {
    padding: 0 10px 15px !important;

    .header {
      font-size: 26px;
      padding: 0 4px;
      color: #333;
    }

    .stack-bar {
      margin: 5px 10px 5px 30px;
      display: flex;
      box-shadow: 0 2px 5px #999;
      border-radius: 5px;
      overflow: hidden;

      .item {
        height: 10px;
      }
    }

    .row.programs {
      max-width: 340px;
      margin: 7px auto 0 20px !important;

      .col-4 {
        padding: 3px 0 0 6px;
        transform: scale(0.9);
      }
    }

    .chart-container {
      height: calc(100% - 150px) !important;
    }
  }
}
</style>
