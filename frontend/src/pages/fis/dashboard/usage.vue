<template>
  <Card class="fis-dashboard-usage"
        title="Usage"
        ref="card"
        keyName="Usage"
        @reload="reload"
        :report-url="reportUrl">
    <h3>Credits & Debits</h3>
    <h5>{{ startDate | date('MM/DD/YY', true) }} thru {{ endingDate | date('MM/DD/YY', true) }}</h5>
    <div class="chart-container"></div>
    <div class="chart-labels">
      <div class="filter">Bank: {{ params.bank }}</div>
      <div class="filter">Type: {{ typeName }}</div>
      <div class="filter">Program: {{ params.client }}</div>
      <div class="credit"
           :class="{active: active === 0}">
        <div class="chart-tooltip-circle"></div>
        {{ credit | moneyFormat(params.currency, false, false) }}
      </div>
      <div class="debit"
           :class="{active: active === 1}">
        {{ debit | moneyFormat(params.currency, false, false) }}
        <div class="chart-tooltip-circle"></div>
      </div>
    </div>
    <div class="diff-label">difference</div>
    <div class="diff-value">{{ diff | moneyFormat(params.currency, false, false) }}</div>
  </Card>
</template>

<script>
import Card from './card'
import echarts from 'echarts'
import _ from 'lodash'
import { request } from '../../../common'
import CachedGraph from '../../../mixins/fis/CachedGraph'
import $ from 'jquery'

export default {
  name: 'FisDashboardUsage',
  mixins: [
    CachedGraph
  ],
  components: {
    Card
  },
  props: {
    params: {
      type: Object
    },
    filters: {
      type: Object
    }
  },
  data () {
    return {
      graphName: 'usage',
      startDate: null,
      endingDate: null,
      credit: 0,
      debit: 0,
      active: null,
      chart: null
    }
  },
  computed: {
    diff () {
      return (this.credit || 0) + (this.debit || 0)
    },
    rangeName () {
      let t = this.params.range || 'Daily'
      if (t.startsWith('custom_range__')) {
        return 'Range'
      }
      t = t.substr(0, t.length - 2)
      return _.lowerCase(t === 'Dai' ? 'Day' : t)
    },
    typeName () {
      if (!this.params.type) {
        return 'All'
      }
      const type = _.find(this.filters.types, { value: this.params.type })
      if (type) {
        return type.label
      }
      return 'Unknown'
    }
  },
  methods: {
    async reload (p) {
      if (await this.loadWithDbCache(p)) {
        return
      }
      const resp = await request(`/admin/fis/dashboard/usage/data`, 'get', this.params)
      if (p && p.cb) {
        p.cb(resp)
      }
      if (resp.success) {
        this.onResponseData(resp.data)
        await this.saveDbCache(resp.data)
      }
    },
    onResponseData (data) {
      this.saveCache(data)

      this.startDate = data.startDate
      this.endingDate = data.endingDate
      this.credit = data.credit
      this.debit = data.debit

      this.$nextTick(() => {
        this.initChart()
      })
    },
    resize () {
      this.chart.resize()
    },
    initChart () {
      const chart = echarts.init(this.$el.querySelector('.chart-container'), 'fis')
      this.chart = chart
      const options = _.defaultsDeep({
        series: [
          {
            name: this.title,
            type: 'pie',
            radius: ['75%', '90%'],
            hoverOffset: 5,
            startAngle: 270,
            data: [
              {
                value: Math.abs(this.credit),
                itemStyle: {
                  color: '#7BCA0C'
                }
              },
              {
                value: Math.abs(this.debit),
                itemStyle: {
                  color: '#F5051B'
                }
              }
            ],
            labelLine: {
              normal: {
                show: false
              }
            },
            itemStyle: {
              borderCap: 'round',
              borderJoin: 'round'
            }
          }
        ]
      }, this.cc.fis.chart)
      chart.setOption(options)
      chart.resize()
      chart.on('mouseover', 'series', p => {
        this.active = p.dataIndex
      })
      chart.on('mouseout', 'series', () => {
        this.active = null
      })
      $(window).on('resize', this.resize)
    }
  }
}
</script>

<style lang="scss">
.fis-dashboard-usage {
  .q-card-main {
    padding: 0 10px 15px !important;
  }

  .diff-label {
    font-weight: bold;
    color: #a1a3a7;
    text-align: center;
    margin: 5px 0 3px;
  }

  .diff-value {
    text-align: center;
    color: black;
  }

  .chart-labels {
    position: absolute;
    text-align: center;
    left: 50%;
    top: 53%;
    transform: translate(-50%, -50%);

    .filter {
      font-size: 12px;
      font-weight: 500;
      color: black;
      margin-bottom: 2px;
    }

    .credit {
      margin-top: 10px;
      color: #7bca0c;

      .chart-tooltip-circle {
        background-color: #7bca0c;
      }
    }

    .debit {
      margin-top: 4px;
      color: #f5051b;

      .chart-tooltip-circle {
        background-color: #f5051b;
      }
    }

    .credit,
    .debit {
      font-size: 15px;
      transition: all 0.3s;

      &.active {
        font-size: 18px;
        font-weight: bold;
      }
    }
  }
}
</style>
