<template>
  <q-dialog
    class="fis-monitor-detail-dialog admin-root"
    v-model="visible"
  >
    <span slot="title">Detail</span>
    <div slot="body">
      <q-inner-loading :visible="!detail">
        <q-spinner size="40"></q-spinner>
      </q-inner-loading>

      <q-tabs inverted v-if="detail" v-model="tabs[0]">
        <q-tab default slot="title" name="monitor" label="Monitor setting"/>
        <q-tab-pane name="monitor" keep-alive>
          <table class="form-table form-table-lg">
            <tr v-for="(v, k) in detail.velocity" :key="k">
              <th>{{ k }}</th>
              <td>{{ v }}</td>
            </tr>
          </table>
        </q-tab-pane>
      </q-tabs>

      <q-tabs inverted v-if="detail" v-model="tabs[1]">
        <q-tab default slot="title" name="primary" label="Primary data that triggered the event"/>
        <q-tab-pane name="primary" keep-alive>
          <q-table :columns="columns" :data="primary"></q-table>
        </q-tab-pane>
      </q-tabs>

      <q-tabs inverted v-if="detail" v-model="tabs[2]">
        <q-tab default slot="title" name="related" label="Other related data"/>
        <q-tab-pane name="related" keep-alive>
          <q-table :columns="columns" :data="related"></q-table>
        </q-tab-pane>
      </q-tabs>
    </div>
    <template slot="buttons">
      <div class="flex-center text-right">
        <q-btn flat label="OK"
               color="primary"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { generateColumns, request } from '../../../common'

export default {
  name: 'fis-monitor-detail-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      detail: null,
      tabs: ['monitor', 'primary', 'related']
    }
  },
  computed: {
    columns () {
      if (!this.detail || !this.detail.trigger) {
        return []
      }
      return generateColumns(Object.keys(this.detail.trigger))
    },
    primary () {
      if (!this.detail || !this.detail.trigger) {
        return []
      }
      return [
        this.detail.trigger
      ]
    },
    related () {
      if (!this.detail || !this.detail.details) {
        return []
      }
      return this.detail.details
    }
  },
  methods: {
    show () {
      this.detail = null
      this.init()
    },
    async init () {
      const resp = await request(`/admin/fis/monitor/${this.entity.ID}/view`)
      if (resp.success) {
        this.detail = resp.data
      }
    }
  }
}
</script>

<style lang="scss">
.fis-monitor-detail-dialog {
  .modal-content {
    width: 800px;
  }

  .q-tab {
    align-items: flex-start;
  }

  .q-tab-label {
    text-transform: none;
  }

  .form-table th {
    width: 130px;
  }
}
</style>
