<template>
  <q-page id="fis__monitor__index_page"
          class="fis_page fis_report_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
      <div class="fun-group">
        <q-btn @click="clearSelected"
               color="negative"
               :disable="!selectedIds || selectedIds.length <= 0"
               class="ml-10"
               label="Clear Selected"></q-btn>
      </div>
    </div>
    <div class="page-content mt-15">
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freezeColumn.sync="freezeColumn"
                    :columnStyles="columnStyles"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="{even: props.row.__index % 2}">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyle(i)"
                :class="{freeze: i <= freezeColumn, 'last-freeze': i === freezeColumn}">
            <template v-if="col.field === 'Date Time'">
              {{ props.row[col.field] | date }}
            </template>
            <template v-else-if="col.field === 'Select'">
              <q-checkbox v-model="props.row.selected"></q-checkbox>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown size="xs"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="view(props.row)">
                    <q-item-main>View</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="clear(props.row)">
                    <q-item-main>Clear</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload">
            <q-btn color="dark"
                   class="btn-export"
                   @click="download"
                   label="Export" />
          </Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <DetailDialog></DetailDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import { generateColumns } from '../../../common'
import DetailDialog from './detail'

export default {
  name: 'fis-monitor',
  mixins: [
    ListPageMixin,
    FreezeColumnMixin
  ],
  components: {
    DetailDialog
  },
  data () {
    return {
      title: 'Monitoring/Watchlist',
      downloadUrl: `/admin/fis/monitor/export`,
      filtersUrl: `/admin/fis/monitor/filters`,
      requestUrl: `/admin/fis/monitor/list`,
      columns: generateColumns([
        'Select',
        'Date Time', 'PAN Proxy', 'Program',
        'Monitor Setting Name', 'Control Type', 'Transaction Types',
        'Monitoring Logic', 'Action'
      ]),
      freezeColumn: 0,
      timeField: 'date_range',
      filterOptions: [
        {
          value: 'currency',
          label: 'Currency',
          options: [],
          source: 'currencies',
          search: true
        },
        {
          value: 'bank',
          label: 'Bank',
          options: [],
          source: 'banks',
          search: true
        },
        {
          value: 'type',
          label: 'Type',
          options: [],
          source: 'types',
          search: true
        },
        {
          value: 'client',
          label: 'Program',
          options: [],
          source: 'clients',
          search: true
        },
        {
          value: 'date_range',
          label: 'Date Range',
          range: [
            {
              value: 'start',
              type: 'date'
            }, {
              value: 'end',
              type: 'date'
            }
          ]
        },
        {
          value: 'filter[a.pan]',
          label: 'Card Proxy Number'
        },
        {
          value: 'filter[v.name]',
          label: 'Monitor Setting Name',
          options: [],
          source: 'monitorSettingNames'
        },
        {
          value: 'filter[v.type]',
          label: 'Control Type',
          options: [],
          source: 'controlTypes'
        },
        {
          value: 'filter[v.transactionType]',
          label: 'Transaction Type',
          options: [],
          source: 'transactionTypes'
        }
      ]
    }
  },
  computed: {
    selectedIds () {
      const ids = []
      for (const d of this.data) {
        if (d.selected) {
          ids.push(d.ID)
        }
      }
      return ids
    }
  },
  methods: {
    view (item) {
      this.$root.$emit('show-fis-monitor-detail-dialog', item)
    },
    clear (item) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you wish to remove this account from the watchlist?',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await this.c.request(`/admin/fis/monitor/${item.ID}/clear`, 'delete', item)
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
        }
      }).catch(() => {})
    },
    clearSelected () {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you wish to remove these ${this.selectedIds.length} records from the watchlist?`,
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await this.c.request(`/admin/fis/monitor/clear/selected`, 'delete', {
          ids: this.selectedIds
        })
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
#fis__monitor__index_page {
  .range-dates-field,
  .card-proxy-number-field {
    .q-field-label {
      flex-basis: 70px;
    }
  }
}
</style>
