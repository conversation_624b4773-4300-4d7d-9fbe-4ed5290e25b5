<template>
  <q-page id="fis__report_auth__index_page"
          class="fis_page fis_report_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freezeColumn.sync="freezeColumn"
                    :columnStyles="columnStyles"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="{even: props.row.__index % 2}">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyle(i)"
                :class="{freeze: i <= freezeColumn, 'last-freeze': i === freezeColumn}">
            <template v-if="col.field === 'Actions'">
              <q-btn-dropdown size="xs"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          v-for="(action, name) in reportActions"
                          :key="action"
                          @click.native="takeReportAction(action, props.row)">
                    <q-item-main>{{ name }}</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload">
            <q-btn color="dark"
                   class="btn-export"
                   @click="download"
                   label="Export" />
          </Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import FisReportActionsMixin from '../../../../mixins/fis/FisReportActionsMixin'

export default {
  name: 'fis-auth-report',
  mixins: [
    ListPageMixin,
    FreezeColumnMixin,
    FisReportActionsMixin
  ],
  data () {
    return {
      title: 'Authorization Activity',
      downloadUrl: `/admin/fis/report/auth/export`,
      requestUrl: `/admin/fis/report/auth/list`,
      filtersUrl: `/admin/fis/report/auth/filters`,
      reportKey: 'auth',
      columns: [
        {
          field: 'Date Time',
          label: 'Date Time',
          align: 'left'
        },
        {
          field: 'Tran ID',
          label: 'Tran ID',
          align: 'left'
        },
        {
          field: 'Tran Type',
          label: 'Tran Type',
          align: 'left'
        },
        {
          field: 'Source',
          label: 'Source',
          align: 'left'
        },
        {
          field: 'Reason Code',
          label: 'Reason Code',
          align: 'left'
        },
        {
          field: 'Request Code',
          label: 'Request Code',
          align: 'left'
        },
        {
          field: 'Response',
          label: 'Response',
          align: 'left'
        },
        {
          field: 'Issuing Bank',
          label: 'Issuing Bank',
          align: 'left'
        },
        {
          field: 'Issuing Client',
          label: 'Issuing Client',
          align: 'left'
        },
        {
          field: 'Group Type',
          label: 'Group Type',
          align: 'left'
        },
        {
          field: 'BIN',
          label: 'BIN',
          align: 'left'
        },
        {
          field: 'BIN Currency',
          label: 'BIN Currency',
          align: 'left'
        },
        {
          field: 'PAN',
          label: 'PAN',
          align: 'left'
        },
        {
          field: 'Card Proxy Number',
          label: 'Card Proxy Number',
          align: 'left'
        },
        {
          field: 'Debit/Credit',
          label: 'Debit/Credit',
          align: 'left'
        },
        {
          field: 'Txn Amount',
          label: 'Txn Amount',
          align: 'right'
        },
        {
          field: 'Transaction Currency',
          label: 'Transaction Currency',
          align: 'left'
        },
        {
          field: 'Auth Code',
          label: 'Auth Code',
          align: 'left'
        },
        {
          field: 'MCC',
          label: 'MCC',
          align: 'left'
        },
        {
          field: 'MCC Description',
          label: 'MCC Description',
          align: 'left'
        },
        {
          field: 'Merchant',
          label: 'Merchant',
          align: 'left'
        },
        {
          field: 'Merchant Number',
          label: 'Merchant Number',
          align: 'left'
        },
        {
          field: 'POS Entry Description',
          label: 'POS Entry Description',
          align: 'left'
        },
        {
          field: 'Acquirer Id',
          label: 'Acquirer Id',
          align: 'left'
        },
        {
          field: 'Actions',
          label: 'Actions',
          align: 'left'
        }
      ],
      freezeColumn: 0,
      filterOptions: [
        {
          value: 'currency',
          label: 'Currency',
          options: [],
          source: 'currencies',
          search: true
        },
        {
          value: 'bank',
          label: 'Bank',
          options: [],
          source: 'banks',
          search: true
        },
        {
          value: 'type',
          label: 'Group Type',
          options: [],
          source: 'types',
          search: true
        },
        {
          value: 'client',
          label: 'Issuing Client',
          options: [],
          source: 'clients',
          search: true
        },
        {
          value: 'date_range',
          label: 'Date Range',
          range: [
            {
              value: 'start',
              type: 'date'
            }, {
              value: 'end',
              type: 'date'
            }
          ]
        },
        {
          value: 'filter[a.cardNumberProxy]',
          label: 'Card Proxy Number'
        },
        {
          value: 'filter[a.txnUid]',
          label: 'Tran ID'
        },
        {
          value: 'filter[a.authorizationCode=]',
          label: 'Auth Code'
        },
        {
          value: 'transaction_amount',
          label: 'Transaction Amount',
          range: [
            {
              value: 'range[a.txnLocalAmount][min]',
              type: 'localAmount'
            },
            {
              value: 'range[a.txnLocalAmount][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'filter[a.transactionCurrencyAlpha=]',
          label: 'Transaction Currency',
          options: [],
          source: 'transactionCurrencies'
        },
        {
          value: 'filter[a.txnTypeName=]',
          label: 'Tran Type',
          options: [],
          source: 'tranTypes'
        },
        {
          value: 'filter[a.reasonCodeDescription=]',
          label: 'Tran Type',
          options: [],
          source: 'reasonCodes'
        },
        {
          value: 'filter[a.responseDescription=]',
          label: 'Response',
          options: [],
          source: 'responses'
        },
        {
          value: 'filter[a.txnSign=]',
          label: 'Debit/Credit',
          options: [
            {
              label: 'Debit',
              value: '-1'
            },
            {
              label: 'Credit',
              value: '1'
            }
          ]
        },
        {
          value: 'filter[a.mcc=]',
          label: 'MCC'
        },
        {
          value: 'filter[a.mccDescription=]',
          label: 'MCC Description',
          options: [],
          source: 'mccDescriptions'
        },
        {
          value: 'filter[a.merchantName]',
          label: 'Merchant Name'
        },
        {
          value: 'filter[a.merchantNumber]',
          label: 'Merchant Number'
        },
        {
          value: 'filter[a.merchantCity]',
          label: 'Merchant City'
        },
        {
          value: 'filter[a.merchantCountryName]',
          label: 'Merchant Country',
          options: [],
          source: 'merchantCountries'
        },
        {
          value: 'filter[a.posEntryDescription]',
          label: 'POS Entry Description'
        },
        {
          value: 'filter[a.acquirerId]',
          label: 'Acquirer ID'
        }
      ],
      reportProxyField: 'Card Proxy Number'
    }
  }
}
</script>
