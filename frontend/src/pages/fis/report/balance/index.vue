<template>
  <q-page id="fis__report_balance__index_page"
          class="fis_page fis_report_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
      <div class="fun-group">
        <q-field label="Currency"
                 class="currency-field">
          <q-select v-model="filter.currency"
                    :options="filters.currencies"></q-select>
        </q-field>
        <q-field label="Bank">
          <q-select v-model="filter.bank"
                    :filter="true"
                    :options="filters.banks"></q-select>
        </q-field>
        <q-field label="Type">
          <q-select v-model="filter.type"
                    :filter="true"
                    :options="filters.types"></q-select>
        </q-field>
        <q-field label="Program"
                 class="currency-field">
          <q-select v-model="filter.client"
                    :filter="true"
                    :options="filters.clients"></q-select>
        </q-field>
        <q-field label="Range"
                 class="range-dates-field w55-field">
          <q-select v-model="filter.range"
                    :options="filters.ranges"></q-select>
          <q-datetime v-if="filter.range !== 'Daily'"
                      :class="{show: filter.range === 'custom_range'}"
                      v-model="filter.startDate"
                      :max="max"
                      type="date" />
          <span class="separator"
                :class="{show: filter.range === 'custom_range'}"
                v-if="filter.range !== 'Daily'">~</span>
          <q-datetime v-model="filter.endDate"
                      :max="max"
                      :class="{show: filter.range === 'custom_range'}"
                      type="date" />
        </q-field>
        <q-field label="Format"
                 class="w55-field">
          <q-select v-model="filter.format"
                    :options="filters.formats"></q-select>
        </q-field>
        <q-btn @click="reload"
               color="primary"
               class="mr-15"
               label="Filter"></q-btn>

        <q-btn @click="resetFilter"
               color="black"
               class="mr-15"
               label="Reset"></q-btn>
      </div>
    </div>
    <div class="page-content mt-15">
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table"
               @request="request"
               separator="none"
               row-key="id">
        <StickyHead slot="header"
                    slot-scope="props"
                    :freezeColumn.sync="freezeColumn"
                    :columnStyles="columnStyles"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="{even: props.row.__index % 2}">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyle(i)"
                :class="{freeze: i <= freezeColumn, 'last-freeze': i === freezeColumn}">
            <template v-if="col.field.endsWith(' Balance') || col.field.endsWith(' Avg $')">
              {{ props.row[col.field] | moneyFormat(filter.currency, false, false) }}
            </template>
            <template v-else-if="col.field.endsWith(' Card #')">
              {{ props.row[col.field] | number }}
            </template>
            <template v-else-if="['% of Portfolio'].includes(col.field)">
              {{ props.row[col.field] | percent }}
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload">
            <q-btn color="dark"
                   class="btn-export"
                   @click="download"
                   label="Export" />
          </Pagination>
        </div>
      </q-table>
    </div>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import FisReportPageMixin from '../../../../mixins/fis/FisReportPageMixin'

export default {
  name: 'fis-balance-report',
  mixins: [
    ListPageMixin,
    FisReportPageMixin
  ],
  data () {
    return {
      title: 'Balance Report',
      downloadUrl: `/admin/fis/report/balance/export`,
      requestUrl: `/admin/fis/report/balance/list`,
      requestCb: data => {
        this.columns = this.presetColumns.concat(data.columns)
        this.columns = this.columns.map(c => {
          c.sortable = true
          return c
        })
      },
      reportKey: 'balance',
      pagination: {
        rowsNumber: 0,
        rowsPerPage: Number.MAX_SAFE_INTEGER,
        page: 1,
        sortBy: null,
        descending: false
      },
      presetColumns: [
        {
          field: 'Rank',
          label: 'Rank',
          align: 'left'
        },
        {
          field: 'Issuing Bank',
          label: 'Issuing Bank',
          align: 'left'
        },
        {
          field: 'Issuing Client',
          label: 'Program',
          align: 'left'
        },
        {
          field: 'PAN Proxy Number',
          label: 'PAN Proxy Count',
          align: 'left'
        },
        {
          field: 'Range',
          label: 'Range',
          align: 'left'
        }
      ],
      columns: [],
      max: new Date()
    }
  },
  computed: {
    rangePrefix () {
      const t = this.filter.range || 'Daily'
      return (t[0] || '').toUpperCase()
    }
  },
  mounted () {
    this.filter.client = 'All (itemized)'
    this.columns = this.presetColumns
  }
}
</script>

<style lang="scss">
#fis__report_balance__index_page {
  .page-header .title {
    margin-bottom: 15px;
  }

  .main-table {
    .text-negative {
      color: #be031a !important;
    }

    .text-positive {
      color: #6fae3d !important;
    }
  }
}
</style>
