<template>
  <q-page id="fis__report_dispute__index_page"
          class="fis_page fis_report_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freezeColumn.sync="freezeColumn"
                    :columnStyles="columnStyles"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="{even: props.row.__index % 2}">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyle(i)"
                :class="{freeze: i <= freezeColumn, 'last-freeze': i === freezeColumn}">
            <template v-if="col.field === 'Actions'">
              <q-btn-dropdown size="xs"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          v-for="(action, name) in reportActions"
                          :key="action"
                          @click.native="takeReportAction(action, props.row)">
                    <q-item-main>{{ name }}</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload">
            <q-btn color="dark"
                   class="btn-export"
                   @click="download"
                   label="Export" />
          </Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns } from '../../../../common'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import FisReportActionsMixin from '../../../../mixins/fis/FisReportActionsMixin'

export default {
  name: 'fis-dispute-report',
  mixins: [
    ListPageMixin,
    FreezeColumnMixin,
    FisReportActionsMixin
  ],
  data () {
    return {
      title: 'Dispute Activity',
      downloadUrl: `/admin/fis/report/dispute/export`,
      requestUrl: `/admin/fis/report/dispute/list`,
      filtersUrl: `/admin/fis/report/dispute/filters`,
      reportKey: 'dispute',
      columns: generateColumns([
        'Date', 'Tran ID', 'Original Transaction Date', 'Issuing Bank',
        'Issuing Client', 'Group Type', 'BIN', 'BIN Currency',
        'PAN', 'Card Proxy Number', 'Cardholder Name',
        'Open Date', 'Claim Requested', 'Aging (days)', 'Tran Description',
        'Original Tran Type Name', 'Merchant', 'State', 'Country',
        'Disputed Amount', 'Dispute Type', 'Dispute State',
        'Dispute Status', 'Dispute Reason', 'Claim ID', 'Actions'
      ]),
      freezeColumn: 0,
      timeField: 'date_range',
      filterOptions: [
        {
          value: 'currency',
          label: 'Currency',
          options: [],
          source: 'currencies',
          search: true
        },
        {
          value: 'bank',
          label: 'Bank',
          options: [],
          source: 'banks',
          search: true
        },
        {
          value: 'type',
          label: 'Group Type',
          options: [],
          source: 'types',
          search: true
        },
        {
          value: 'client',
          label: 'Issuing Client',
          options: [],
          source: 'clients',
          search: true
        },
        {
          value: 'date_range',
          label: 'Date Range',
          range: [
            {
              value: 'start',
              type: 'date'
            }, {
              value: 'end',
              type: 'date'
            }
          ]
        },
        {
          value: 'filter[a.cardNumberProxy]',
          label: 'Card Proxy Number'
        },
        {
          value: 'filter[a.txnUid]',
          label: 'Tran ID'
        },
        {
          value: 'filter[a.cardHolderName]',
          label: 'Cardholder Name'
        },
        {
          value: 'disputed_amount',
          label: 'Disputed Amount',
          range: [
            {
              value: 'range[a.disputedAmount][min]',
              type: 'amount'
            },
            {
              value: 'range[a.disputedAmount][max]',
              type: 'amount'
            }
          ]
        },
        {
          value: 'filter[a.originalTxnTypeName=]',
          label: 'Tran Type',
          options: [],
          source: 'tranTypes'
        },
        {
          value: 'filter[a.disputeType=]',
          label: 'Dispute Type',
          options: [],
          source: 'disputeTypes'
        },
        {
          value: 'filter[a.disputeState=]',
          label: 'Dispute State',
          options: [],
          source: 'disputeStates'
        },
        {
          value: 'filter[a.disputeStatus=]',
          label: 'Dispute Status',
          options: [],
          source: 'disputeStatuses'
        },
        {
          value: 'filter[a.disputeReason=]',
          label: 'Dispute Reason',
          options: [],
          source: 'disputeReasons'
        },
        {
          value: 'filter[a.claimIdentifier]',
          label: 'Claim ID'
        },
        {
          value: 'date_dispute_received',
          label: 'Date Dispute Received',
          range: [
            {
              value: 'range[a.dateDisputeFormReceived][start]',
              type: 'date'
            },
            {
              value: 'range[a.dateDisputeFormReceived][end]',
              type: 'date'
            }
          ]
        },
        {
          value: 'provisional_credit_date',
          label: 'Provisional Credit Date',
          range: [
            {
              value: 'range[a.provisionalCreditDate][start]',
              type: 'date'
            },
            {
              value: 'range[a.provisionalCreditDate][end]',
              type: 'date'
            }
          ]
        },
        {
          value: 'claim_close_date',
          label: 'Claim Close Date',
          range: [
            {
              value: 'range[a.claimCloseDate][start]',
              type: 'date'
            },
            {
              value: 'range[a.claimCloseDate][end]',
              type: 'date'
            }
          ]
        },
        {
          value: 'final_credit_date',
          label: 'Final Credit Date',
          range: [
            {
              value: 'range[a.finalCreditDate][start]',
              type: 'date'
            },
            {
              value: 'range[a.finalCreditDate][end]',
              type: 'date'
            }
          ]
        },
        {
          value: 'filter[a.liability=]',
          label: 'Liability',
          options: [],
          source: 'liabilities'
        },
        {
          value: 'filter[a.merchantName]',
          label: 'Merchant Name'
        },
        {
          value: 'filter[a.merchantState]',
          label: 'Merchant State'
        },
        {
          value: 'filter[a.merchantCountryAlpha]',
          label: 'Merchant Country',
          options: [],
          source: 'merchantCountries'
        }
      ],
      reportProxyField: 'Card Proxy Number'
    }
  }
}
</script>
