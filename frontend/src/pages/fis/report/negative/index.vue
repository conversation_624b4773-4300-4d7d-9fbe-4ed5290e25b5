<template>
  <q-page id="fis__report_negative__index_page"
          class="fis_page fis_report_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div>
          <h5>{{ quick.count | number }}</h5>
          <div class="description"># of PANs</div>
        </div>
        <div>
          <h5>{{ quick.amount | moneyFormat('USD', true) }}</h5>
          <div class="description">$'s in Negative Balances</div>
        </div>
        <div>
          <h5>{{ quick.avg | moneyFormat('USD', true) }}</h5>
          <div class="description">Avg Balance</div>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn-dropdown split
                          flat
                          round
                          dense
                          class="q-mr-md"
                          icon="refresh"
                          @click="reload">
            <q-list link>
              <q-item v-close-overlay
                      @click.native="reload('force')">
                <q-item-main>
                  <q-item-tile label>Force reload</q-item-tile>
                </q-item-main>
              </q-item>
            </q-list>
          </q-btn-dropdown>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freezeColumn.sync="freezeColumn"
                    :columnStyles="columnStyles"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="{even: props.row.__index % 2}">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyle(i)"
                :class="{freeze: i <= freezeColumn, 'last-freeze': i === freezeColumn}">
            <template v-if="col.field === 'Actions'">
              <q-btn-dropdown size="xs"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          v-for="(action, name) in reportActions"
                          :key="action"
                          @click.native="takeReportAction(action, props.row)">
                    <q-item-main>{{ name }}</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload">
            <q-btn color="dark"
                   class="btn-export"
                   @click="download"
                   label="Export" />
          </Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns } from '../../../../common'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import FisReportActionsMixin from '../../../../mixins/fis/FisReportActionsMixin'
export default {
  name: 'fis-negative-report',
  mixins: [
    ListPageMixin,
    FreezeColumnMixin,
    FisReportActionsMixin
  ],
  data () {
    return {
      title: 'Negative Balances Report',
      downloadUrl: `/admin/fis/report/negative/export`,
      requestUrl: `/admin/fis/report/negative/list`,
      filtersUrl: `/admin/fis/report/negative/filters`,
      autoInitFiltersDialog: false,
      columns: generateColumns([
        'PAN Proxy Number', 'Issuing Client', 'Group Type', 'BIN', 'BIN Currency',
        'Date Card Negative', 'Number of Days in Negative',
        'Balance', 'Dispute Field?', 'Actions'
      ]),
      freezeColumn: 0,
      autoLoad: true,
      filterOptions: [
        {
          value: 'currency',
          label: 'Currency',
          options: [],
          source: 'currencies',
          search: true
        },
        {
          value: 'bank',
          label: 'Bank',
          options: [],
          source: 'banks',
          search: true
        },
        {
          value: 'type',
          label: 'Group Type',
          options: [],
          source: 'types',
          search: true
        },
        {
          value: 'client',
          label: 'Issuing Client',
          options: [],
          source: 'clients',
          search: true
        },
        {
          value: 'filter[a.panProxyNumber]',
          label: 'PAN Proxy Number'
        }
      ]
    }
  }
}
</script>
