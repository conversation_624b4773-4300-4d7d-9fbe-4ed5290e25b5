<template>
  <q-page id="fis__report_non_monetary__index_page"
          class="fis_page fis_report_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freezeColumn.sync="freezeColumn"
                    :columnStyles="columnStyles"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="{even: props.row.__index % 2}">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyle(i)"
                :class="{freeze: i <= freezeColumn, 'last-freeze': i === freezeColumn}">
            <template v-if="col.field === 'Actions'">
              <q-btn-dropdown size="xs"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          v-for="(action, name) in reportActions"
                          :key="action"
                          @click.native="takeReportAction(action, props.row)">
                    <q-item-main>{{ name }}</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload">
            <q-btn color="dark"
                   class="btn-export"
                   @click="download"
                   label="Export" />
          </Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns } from '../../../../common'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import FisReportActionsMixin from '../../../../mixins/fis/FisReportActionsMixin'

export default {
  name: 'fis-none-monetary-report',
  mixins: [
    ListPageMixin,
    FreezeColumnMixin,
    FisReportActionsMixin
  ],
  data () {
    return {
      title: 'Non-Monetary Activity',
      downloadUrl: `/admin/fis/report/non-monetary/export`,
      requestUrl: `/admin/fis/report/non-monetary/list`,
      filtersUrl: `/admin/fis/report/non-monetary/filters`,
      reportKey: 'non-monetary',
      columns: generateColumns([
        'Date Time', 'Issuing Client', 'BIN', 'BIN Currency',
        'PAN', 'Card Proxy Number', 'Cardholder FirstName', 'Cardholder LastName',
        'Cardholder Email', 'Cardholder City', 'Cardholder State', 'Cardholder Country',
        'Card Status', 'Memo/Comments/Client Defined', 'Request Code', 'Request Code Description',
        'SourceCode Description', 'Person ID', 'Notes', 'Actions'
      ]),
      freezeColumn: 0,
      filterOptions: [
        {
          value: 'currency',
          label: 'Currency',
          options: [],
          source: 'currencies',
          search: true
        },
        {
          value: 'client',
          label: 'Issuing Client',
          options: [],
          source: 'clients',
          search: true
        },
        {
          value: 'date_range',
          label: 'Date Range',
          range: [
            {
              value: 'start',
              type: 'date'
            }, {
              value: 'end',
              type: 'date'
            }
          ]
        },
        {
          value: 'filter[a.cardNumberProxy]',
          label: 'Card Proxy Number'
        },
        {
          value: 'filter[a.cardStatus=]',
          label: 'Card Status',
          options: [],
          source: 'cardStatuses'
        },
        {
          value: 'filter[a.requestCode=]',
          label: 'Request Code'
        },
        {
          value: 'filter[a.requestCodeDescription=]',
          label: 'Request Code Description',
          options: [],
          source: 'requestCodeDescriptions'
        },
        {
          value: 'filter[a.cardholderMailingAddressLine1]',
          label: 'Cardholder Mailing Address Line 1'
        },
        {
          value: 'filter[a.cardholderMailingAddressLine2]',
          label: 'Cardholder Mailing Address Line 2'
        },
        {
          value: 'filter[a.cardholderMailingAddressLine3]',
          label: 'Cardholder Mailing Address Line 3'
        },
        {
          value: 'filter[a.cardholderMailingZip]',
          label: 'Cardholder Mailing Zip'
        },
        {
          value: 'filter[a.cardholderTelephoneNumber]',
          label: 'Cardholder Telephone'
        },
        {
          value: 'filter[a.cardholderTelephoneNumberExt]',
          label: 'Cardholder Telephone Ext'
        },
        {
          value: 'filter[a.marketSegmentDescription=]',
          label: 'Market Segment',
          options: [],
          source: 'marketSegments'
        }
      ],
      reportProxyField: 'Card Proxy Number'
    }
  }
}
</script>
