<template>
  <q-page id="fis__report_portfolio__index_page"
          class="fis_page fis_report_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
      <div class="fun-group">
        <q-field label="Currency"
                 class="currency-field">
          <q-select v-model="filter.currency"
                    :options="filters.currencies"></q-select>
        </q-field>
        <q-field label="Bank">
          <q-select v-model="filter.bank"
                    :filter="true"
                    :options="filters.banks"></q-select>
        </q-field>
        <q-field label="Type">
          <q-select v-model="filter.type"
                    :filter="true"
                    :options="filters.types"></q-select>
        </q-field>
        <q-field label="Program"
                 class="currency-field">
          <q-select v-model="filter.client"
                    :filter="true"
                    :options="filters.clients"></q-select>
        </q-field>
        <q-field label="Range"
                 class="range-dates-field w55-field">
          <q-select v-model="filter.range"
                    :options="filters.ranges"></q-select>
          <q-datetime v-if="filter.range !== 'Daily'"
                      :class="{show: filter.range === 'custom_range'}"
                      v-model="filter.startDate"
                      :max="max"
                      type="date" />
          <span class="separator"
                :class="{show: filter.range === 'custom_range'}"
                v-if="filter.range !== 'Daily'">~</span>
          <q-datetime v-model="filter.endDate"
                      :max="max"
                      :class="{show: filter.range === 'custom_range'}"
                      type="date" />
        </q-field>
        <q-field label="Format"
                 class="w55-field">
          <q-select v-model="filter.format"
                    :options="filters.formats"></q-select>
        </q-field>
        <q-field label="Balance"
                 class="currency-field">
          <q-select v-model="filter.balance"
                    :filter="true"
                    :options="filters.balances"></q-select>
        </q-field>
        <q-field label="PAN Proxy Number"
                 class="proxy-field">
          <q-input v-model="tradFilter['a.panProxyNumber']"></q-input>
        </q-field>
        <q-btn @click="reload"
               color="primary"
               class="mr-15"
               label="Filter"></q-btn>
        <q-btn @click="resetFilter"
               color="black"
               class="mr-15"
               label="Reset"></q-btn>
      </div>
    </div>
    <div class="page-content mt-15">
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table"
               @request="request"
               separator="none"
               row-key="id">
        <StickyHead slot="header"
                    slot-scope="props"
                    :freezeColumn.sync="freezeColumn"
                    :columnStyles="columnStyles"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="{even: props.row.__index % 2}">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyle(i)"
                :class="{freeze: i <= freezeColumn, 'last-freeze': i === freezeColumn}">
            <template v-if="!col.field">
            </template>
            <template v-else-if="col.field === 'Date'">
              {{ props.row[col.field] | date('MMMM DD, YYYY') }}
            </template>
            <template v-else-if="['Starting Balance', 'Avg Starting Balance', 'Ending Balance', 'Avg Ending Balance'].includes(col.field)">
              {{ props.row[col.field] | moneyFormat(filter.currency, false, false) }}
            </template>
            <template v-else-if="['Balance % Change', '% Card Growth'].includes(col.field)">
              <span :class="cellClass(props.row[col.field], col.field, props.row)">
                {{ props.row[col.field] | percent(2, true) }}
              </span>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown size="xs"
                              :label="col.label"
                              v-if="filter.format === 'Detail'">
                <q-list link>
                  <q-item v-close-overlay
                          v-for="(action, name) in reportActions"
                          :key="action"
                          @click.native="takeReportAction(action, props.row)">
                    <q-item-main>{{ name }}</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload">
            <q-btn color="dark"
                   class="btn-export"
                   @click="download"
                   label="Export" />
          </Pagination>
        </div>
      </q-table>
    </div>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import FisReportPageMixin from '../../../../mixins/fis/FisReportPageMixin'
import FisReportActionsMixin from '../../../../mixins/fis/FisReportActionsMixin'

export default {
  name: 'fis-portfolio-report',
  mixins: [
    ListPageMixin,
    FisReportPageMixin,
    FisReportActionsMixin
  ],
  data () {
    return {
      title: 'Portfolio Report',
      max: new Date(),
      downloadUrl: `/admin/fis/report/portfolio/export`,
      requestUrl: `/admin/fis/report/portfolio/list`,
      reportKey: 'portfolio',
      columns: [
        {
          field: 'Date',
          label: 'Date',
          align: 'left'
        },
        {
          field: 'Issuing Bank',
          label: 'Issuing Bank',
          align: 'left'
        },
        {
          field: 'Issuing Client',
          label: 'Issuing Client',
          align: 'left'
        },
        {
          field: 'Group Type',
          label: 'Group Type',
          align: 'left'
        },
        {
          field: 'BIN',
          label: 'BIN',
          align: 'left'
        },
        {
          field: 'PAN Proxy Number',
          label: 'PAN Proxy Count',
          align: 'left'
        },
        {
          field: 'Starting Balance',
          label: 'Starting Balance',
          align: 'right'
        },
        {
          field: 'Avg Starting Balance',
          label: 'Avg Starting Balance',
          align: 'right'
        },
        {
          field: 'Ending Balance',
          label: 'Ending Balance',
          align: 'right'
        },
        {
          field: 'Avg Ending Balance',
          label: 'Avg Ending Balance',
          align: 'right'
        },
        {
          field: 'Balance % Change',
          label: 'Balance % Change',
          align: 'right'
        },
        {
          field: 'Starting Cards #',
          label: 'Starting Cards #',
          align: 'right'
        },
        {
          field: 'Ending Cards #',
          label: 'Ending Cards #',
          align: 'right'
        },
        {
          field: '% Card Growth',
          label: '% Card Growth',
          align: 'right'
        },
        {
          field: 'Actions',
          label: 'Actions',
          align: 'left'
        }
      ],
      tradFilter: {
        'a.panProxyNumber': null
      }
    }
  },
  mounted () {
    const query = this.$route.query
    if (query && query.cardNumberProxy) {
      this.tradFilter['a.panProxyNumber'] = query.cardNumberProxy
    }
  }
}
</script>

<style lang="scss">
#fis__report_portfolio__index_page {
  .page-header .title {
    margin-bottom: 15px;
  }

  .main-table {
    .text-negative {
      color: #be031a !important;
    }

    .text-positive {
      color: #6fae3d !important;
    }
  }
}
</style>
