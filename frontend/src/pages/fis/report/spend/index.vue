<template>
  <q-page id="fis__report_spend__index_page"
          class="fis_page fis_report_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freezeColumn.sync="freezeColumn"
                    :columnStyles="columnStyles"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="{even: props.row.__index % 2}">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyle(i)"
                :class="{freeze: i <= freezeColumn, 'last-freeze': i === freezeColumn}">
            <template v-if="col.field === 'Actions'">
              <q-btn-dropdown size="xs"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          v-for="(action, name) in reportActions"
                          :key="action"
                          @click.native="takeReportAction(action, props.row)">
                    <q-item-main>{{ name }}</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload">
            <q-btn color="dark"
                   class="btn-export"
                   @click="download"
                   label="Export" />
          </Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import FisReportActionsMixin from '../../../../mixins/fis/FisReportActionsMixin'

export default {
  name: 'fis-spend-report',
  mixins: [
    ListPageMixin,
    FreezeColumnMixin,
    FisReportActionsMixin
  ],
  data () {
    return {
      title: 'Card Spend Activity',
      downloadUrl: `/admin/fis/report/spend/export`,
      requestUrl: `/admin/fis/report/spend/list`,
      filtersUrl: `/admin/fis/report/spend/filters`,
      reportKey: 'spend',
      columns: [

        {
          field: 'Program',
          label: 'Program',
          align: 'left'
        },
        {
          field: 'Date',
          label: 'Date',
          align: 'left'
        },
        {
          field: 'Bank Name',
          label: 'Bank Name',
          align: 'left'
        },
        {
          field: 'Client Name',
          label: 'Client Name',
          align: 'left'
        },
        {
          field: 'Issuer Client ID',
          label: 'Issuer Client ID',
          align: 'left'
        },
        {
          field: 'Currency',
          label: 'Currency',
          align: 'left'
        },
        {
          field: 'Pan Count',
          label: 'Pan Count',
          align: 'left'
        },
        {
          field: 'Record Count',
          label: 'Record Count',
          align: 'left'
        },
        {
          field: 'PPN Count',
          label: 'PPN Count',
          align: 'left'
        },
        {
          field: 'Txn USD Amount',
          label: 'Txn USD Amount',
          align: 'left'
        },
        {
          field: 'Txn Type Name',
          label: 'Txn Type Name',
          align: 'left'
        },
        {
          field: 'Txn Sign',
          label: 'Txn Sign',
          align: 'left'
        },
        {
          field: 'Request Code',
          label: 'Request Code',
          align: 'left'
        },
        {
          field: 'Mcc',
          label: 'Mcc',
          align: 'left'
        },
        {
          field: 'Mcc Description',
          label: 'Mcc Description',
          align: 'left'
        }
      ],
      freezeColumn: 0,
      filterOptions: [
        {
          value: 'currency',
          label: 'Currency',
          options: [],
          source: 'currencies',
          search: true
        },
        {
          value: 'bank',
          label: 'Bank',
          options: [],
          source: 'banks',
          search: true
        },
        {
          value: 'type',
          label: 'Group Type',
          options: [],
          source: 'types',
          search: true
        },
        {
          value: 'client',
          label: 'Issuing Client',
          options: [],
          source: 'clients',
          search: true
        },
        {
          value: 'date_range',
          label: 'Date Range',
          range: [
            {
              value: 'start',
              type: 'date'
            }, {
              value: 'end',
              type: 'date'
            }
          ]
        }
      ],
      reportProxyField: 'Card Proxy Number'
    }
  }
}
</script>
