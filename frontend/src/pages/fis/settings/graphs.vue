<template>
  <q-page id="fis__settings_graphs__index_page"
          class="fis_page">
    <div class="page-header column">
      <div class="title mb-15">{{ title }}</div>
    </div>
    <div class="page-content">
      <table class="table table-data">
        <thead>
          <tr>
            <th>Chart</th>
            <th>Visibility</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, i) in items"
              :key="i">
            <th>
              {{ item.name }}
            </th>
            <td>
              <q-checkbox v-model="item.show"></q-checkbox>
            </td>
          </tr>
          <tr>
            <th>Inactive card days</th>
            <td>
              <q-input autocomplete="no"
                       type="number"
                       :step="1"
                       @input="checkDays"
                       v-model="days"></q-input>
            </td>
          </tr>
        </tbody>
      </table>

      <q-btn label="Submit"
             class="mv-15"
             color="primary"
             @click="submit"></q-btn>

      <q-inner-loading :visible="loading">
        <q-spinner size="40"></q-spinner>
      </q-inner-loading>
    </div>
  </q-page>
</template>

<script>
import PageMixin from '../../../mixins/PageMixin'
import { notify, request } from '../../../common'

export default {
  name: 'fis-settings-graphs',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'Chart Settings',
      loading: false,
      items: [],
      days: 30
    }
  },
  methods: {
    checkDays () {
      if (this.days < 1) {
        this.days = 1
      }
    },
    async reload () {
      this.loading = true
      const resp = await request(`/admin/fis/settings/graphs`)
      this.loading = false
      if (resp.success) {
        this.items = resp.data.all
        this.days = resp.data.days
      }
    },

    async submit () {
      this.loading = true
      const resp = await request(`/admin/fis/settings/graphs/save`, 'post', {
        items: this.items,
        days: this.days
      })
      this.loading = false
      if (resp.success) {
        notify(resp.message)
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
#fis__settings_graphs__index_page {
  .table.table-data {
    border: 1px solid #ddd;
    border-collapse: collapse;

    thead {
      th {
        padding: 10px 8px;
        font-weight: bold;
        color: #000;
      }
    }

    tbody {
      th {
        min-width: 150px;
      }
    }

    th {
      min-width: 100px;
      text-align: left;
      font-weight: 500;
    }

    th,
    td {
      border-bottom: 1px solid #ddd;
      border-right: 1px solid #ddd;
      padding: 4px 8px;
    }
  }
}
</style>
