<template>
  <q-page id="fis__settings_monitor__detail_page"
          class="fis_page">
    <div class="page-header column">
      <div class="title mb-15">{{ id ? (viewMode ? 'View' : 'Edit') : 'Add New' }} {{ title }}</div>
    </div>
    <div class="page-content">
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Monitor Setting name <span class="text-negative ml-3">*</span>
        </div>
        <q-input v-model="form.name"
                 :disable="viewMode"
                 :error="$v.form.name.$error"
                 @blur="$v.form.name.$touch"></q-input>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Description
        </div>
        <q-input v-model="form.comment"
                 :disable="viewMode"
                 type="textarea"
                 :rows="2"></q-input>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Program <span class="text-negative ml-3">*</span>
        </div>
        <q-select :options="source.clients"
                  :disable="viewMode"
                  :error="$v.form.issuerClientId.$error"
                  @blur="$v.form.issuerClientId.$touch"
                  v-model="form.issuerClientId"></q-select>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Control Type <span class="text-negative ml-3">*</span>
        </div>
        <q-select :options="source.controlTypes"
                  :disable="viewMode"
                  :error="$v.form.controlType.$error"
                  @blur="$v.form.controlType.$touch"
                  v-model="form.controlType"></q-select>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Transaction Types <span class="text-negative ml-3">*</span>
        </div>
        <q-select :options="transactionTypes"
                  :disable="viewMode"
                  multiple
                  chips
                  filter
                  :error="$v.form.transactionType.$error"
                  @blur="$v.form.transactionType.$touch"
                  v-model="form.transactionType"></q-select>
      </div>
      <div class="col q-field"
           v-if="merchantRelated">
        <div class="q-field-label items-center flex">
          Merchant Criteria <span class="text-negative ml-3">*</span>
        </div>
        <q-select :options="allMerchantCriteria"
                  :disable="viewMode"
                  :error="$v.form.merchantCriteria.$error"
                  @blur="$v.form.merchantCriteria.$touch"
                  v-model="form.merchantCriteria"></q-select>
      </div>
      <div class="col q-field"
           v-if="form.merchantCriteria === 'MCC Code(s)'">
        <div class="q-field-label items-center flex">
          Select one or more MCC Code(s):
        </div>
        <q-select :options="mccs"
                  :disable="viewMode"
                  multiple
                  chips
                  filter
                  filter-placeholder="Type MCC or name to search"
                  v-model="form.mccs"></q-select>
      </div>
      <div class="col q-field"
           v-if="merchantRelated && form.merchantCriteria !== 'Count of distinct Merchants'">
        <div class="q-field-label items-center flex">
          Merchant Name <span class="font-12 thin ml-5">(optional, fuzzy match)</span>
        </div>
        <q-input v-model="form.merchantName"
                 :disable="viewMode"></q-input>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Metric <span class="text-negative ml-3">*</span>
        </div>
        <q-select :options="metrics"
                  :disable="viewMode"
                  :error="$v.form.metric.$error"
                  @blur="$v.form.metric.$touch"
                  v-model="form.metric"></q-select>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Relation <span class="text-negative ml-3">*</span>
        </div>
        <q-select :options="source.relations"
                  :disable="viewMode"
                  :error="$v.form.relation.$error"
                  @blur="$v.form.relation.$touch"
                  v-model="form.relation"></q-select>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Metric <span v-show="form.relation === '[]'">Minimum</span> Value
          <span class="text-negative ml-3">*</span>
          <span class="font-12 thin ml-3"
                v-if="accountBalanceLookup">($)</span>
          <span class="font-12 thin ml-3"
                v-else>(# or $)</span>
        </div>
        <q-input v-model="form.min"
                 :disable="viewMode"
                 :error="$v.form.min.$error"
                 @blur="$v.form.min.$touch"></q-input>
      </div>
      <div class="col q-field"
           v-show="form.relation === '[]'">
        <div class="q-field-label items-center flex">
          Metric Maximum Value <span class="text-negative ml-3">*</span> <span class="font-12 thin ml-3">(# or $)</span>
        </div>
        <q-input v-model="form.max"
                 :disable="viewMode"
                 :error="$v.form.max.$error"
                 @blur="$v.form.max.$touch"></q-input>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Frequency <span class="text-negative ml-3">*</span>
        </div>
        <q-select :options="frequencies"
                  :disable="viewMode"
                  :error="$v.form.frequency.$error"
                  @blur="$v.form.frequency.$touch"
                  v-model="form.frequency"></q-select>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Actions
        </div>
        <q-select :options="source.actions"
                  :disable="viewMode"
                  multiple
                  chips
                  v-model="form.action"></q-select>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Custom Alert Message
        </div>
        <q-input v-model="form.alertMessage"
                 :disable="viewMode"></q-input>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Public
        </div>
        <q-checkbox v-model="form.public"
                    :disable="viewMode"
                    label="Share this monitor setting to others so that they can subscribe."></q-checkbox>
      </div>
      <div class="mv-20"
           v-if="!viewMode">
        <q-btn color="primary"
               :disable="$v.$invalid"
               @click="submit"
               label="Submit"></q-btn>
        <q-btn color="light"
               label="Cancel"
               to="/j/fis/settings/monitor"
               class="ml-15"></q-btn>
      </div>

      <q-inner-loading :visible="loading">
        <q-spinner size="40"></q-spinner>
      </q-inner-loading>
    </div>
  </q-page>
</template>

<script>
import _ from 'lodash'
import PageMixin from '../../../../mixins/PageMixin'
import { notify, request } from '../../../../common'
import { required, requiredIf } from 'vuelidate/lib/validators'

export default {
  name: 'fis-settings-monitor-detail',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'Monitoring Setting',
      loading: false,
      source: {
        clients: [],
        controlTypes: [],
        transactionResponses: [],
        merchantCriteria: [],
        metrics: [],
        relations: [],
        frequencies: [],
        actions: []
      },
      form: {
        action: [],
        mccs: [],
        transactionType: []
      }
    }
  },
  computed: {
    id () {
      return parseInt(this.$route.params.id)
    },
    transactionTypes () {
      const ct = this.form.controlType
      if (!ct || ct === 'All') {
        return []
      }
      return this.source[`transactionTypes_${ct}`] || []
    },
    viewMode () {
      return this.$route.path.endsWith('/view')
    },
    metrics () {
      return this.source.metrics.filter(v => {
        if (this.accountBalanceLookup) {
          return v.label === 'Amount'
        }
        if (this.form.controlType === 'Non-Monetary Dataset') {
          return v.label === 'Count'
        }
        if (this.form.merchantCriteria === 'Count of distinct Merchants') {
          return v.label === 'Count'
        }
        return true
      })
    },
    frequencies () {
      return this.source.frequencies.filter(v => {
        if (this.accountBalanceLookup) {
          return v.label !== 'Per transaction'
        }
        return true
      })
    },
    accountBalanceLookup () {
      return this.form.controlType === 'Balances Dataset' && this.form.transactionType === 'Account Balance Lookup'
    },
    merchantRelated () {
      return !([
        'Balances Dataset',
        'Non-Monetary Dataset'
      ].includes(this.form.controlType))
    },
    mccRelated () {
      return ([
        'Authorizations Dataset',
        'Monetary Dataset'
      ].includes(this.form.controlType))
    },
    mccs () {
      return this.source.mccs.map(m => {
        m.label = `(${m.value}) ${m.label}`
        return m
      })
    },
    allMerchantCriteria () {
      return this.source.merchantCriteria.filter(m => {
        if (!this.merchantRelated && m.label === 'Count of distinct Merchants') {
          return false
        }
        if (!this.mccRelated && m.label === 'MCC Code(s)') {
          return false
        }
        return true
      })
    }
  },
  watch: {
    'form.controlType' (nv, ov) {
      if (ov) {
        this.$nextTick(() => {
          if (this.form.transactionType && this.form.transactionType.length) {
            this.form.transactionType = []
          }
          if (!_.find(this.allMerchantCriteria, m => {
            return m.value === this.form.merchantCriteria
          })) {
            this.form.merchantCriteria = 'Any Merchant'
          }
        })
      }
    },
    'form.transactionType' (nv, ov) {
      if (ov && ov.length) {
        this.$nextTick(() => {
          if (!_.find(this.metrics, m => {
            return m.value === this.form.metric
          })) {
            this.form.metric = null
          }
          if (!_.find(this.frequencies, m => {
            return m.value === this.form.frequency
          })) {
            this.form.frequency = null
          }
        })
      }
    }
  },
  validations: {
    form: {
      name: {
        required
      },
      issuerClientId: {
        required
      },
      controlType: {
        required
      },
      transactionType: {
        required
      },
      merchantCriteria: {
        required: requiredIf(function () {
          return this.merchantRelated
        })
      },
      metric: {
        required
      },
      relation: {
        required
      },
      min: {
        required
      },
      max: {
        required: requiredIf(function () {
          return this.relation === '[]'
        })
      },
      frequency: {
        required
      }
    }
  },
  methods: {
    async reload () {
      this.loading = true
      const resp = await request(`/admin/fis/settings/monitor/detail?id=${this.id}`)
      this.loading = false
      if (resp.success) {
        this.source = resp.data.source
        this.form = _.assignIn({
          action: [],
          alertMessage: null,
          comment: null,
          controlType: null,
          frequency: null,
          mccs: [],
          merchantCriteria: null,
          metric: null,
          min: null,
          name: null,
          public: true,
          relation: null,
          transactionType: []
        }, resp.data.form)
      }
    },

    async submit () {
      if (this.$v.$invalid) {
        return
      }
      this.loading = true
      const resp = await request(`/admin/fis/settings/monitor/save`, 'post', this.form)
      this.loading = false
      if (resp.success) {
        notify(resp.message)
        this.$router.replace('/j/fis/settings/monitor')
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>
