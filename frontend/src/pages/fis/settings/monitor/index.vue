<template>
  <q-page id="fis__settings_monitor__index_page"
          class="fis_page fis_report_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
      <div class="fun-group">
        <q-field label="Program"
                 class="currency-field">
          <q-select v-model="filter.client"
                    :options="filters.clients"></q-select>
        </q-field>
        <q-field label="Control Type"
                 class="w100-field">
          <q-select v-model="filter.controlType"
                    :options="filters.controlTypes"></q-select>
        </q-field>
        <q-field label="Transaction Type"
                 class="w130-field">
          <q-select v-model="filter.transactionType"
                    :options="transactionTypes"></q-select>
        </q-field>
        <q-btn @click="reload"
               color="primary ml-auto"
               label="Filter"></q-btn>
        <q-btn @click="resetFilter"
               color="black"
               class="ml-auto"
               label="Reset"></q-btn>
        <q-btn color="primary"
               to="/j/fis/settings/monitor/0/detail"
               icon="mdi-plus"
               class="min-w-100"
               label="Add"></q-btn>
      </div>
    </div>
    <div class="page-content mt-15">
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table"
               @request="request"
               separator="none"
               row-key="id">
        <StickyHead slot="header"
                    slot-scope="props"
                    :freezeColumn.sync="freezeColumn"
                    :columnStyles="columnStyles"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="{even: props.row.__index % 2, tr_inactive: props.row.Status === 'Inactive'}">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyle(i)"
                :class="{freeze: i <= freezeColumn, 'last-freeze': i === freezeColumn}">
            <template v-if="col.field === 'Action'">
              <q-btn-dropdown size="xs"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          :to="`/j/fis/settings/monitor/${props.row.ID}/view`">
                    <q-item-main>View</q-item-main>
                  </q-item>
                  <template v-if="props.row.Subscribed === 'Owner'">
                    <q-item v-close-overlay
                            :to="`/j/fis/settings/monitor/${props.row.ID}/detail`">
                      <q-item-main>Edit</q-item-main>
                    </q-item>
                    <q-item>
                      <q-item-main label="Change Status"></q-item-main>
                      <q-item-side right>
                        <q-item-tile icon="mdi-menu-right"></q-item-tile>
                      </q-item-side>
                      <q-popover anchor="bottom left"
                                 self="top right"
                                 :offset="[0, -40]">
                        <q-list link>
                          <template v-for="s in ['Active', 'Inactive']">
                            <q-item v-close-overlay
                                    :key="s"
                                    @click.native="changeStatus(props.row, s)"
                                    v-if="props.row['Status'] !== s">
                              <q-item-main v-text="s"></q-item-main>
                            </q-item>
                          </template>
                        </q-list>
                      </q-popover>
                    </q-item>
                    <q-item v-close-overlay
                            @click.native="remove(props.row)">
                      <q-item-main>Delete</q-item-main>
                    </q-item>
                  </template>
                  <q-item v-close-overlay
                          v-else-if="props.row.Subscribed === 'Yes'"
                          @click.native="subscribe(props.row, false)">
                    <q-item-main>Unsubscribe</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-else-if="props.row.Subscribed === 'No'"
                          @click.native="subscribe(props.row, true)">
                    <q-item-main>Subscribe</q-item-main>
                  </q-item>
<!--                  <q-item v-close-overlay-->
<!--                          @click.native="runForPast(props.row)"-->
<!--                          v-if="['Owner', 'Yes'].includes(props.row.Subscribed)">-->
<!--                    <q-item-main>Search Historical Transactions</q-item-main>-->
<!--                  </q-item>-->
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload">
            <q-btn color="dark"
                   class="btn-export"
                   @click="download"
                   label="Export" />
          </Pagination>
        </div>
      </q-table>
    </div>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import FisReportPageMixin from '../../../../mixins/fis/FisReportPageMixin'
import _ from 'lodash'
import { generateColumns, request, notify, notifySuccess } from '../../../../common'

export default {
  name: 'fis-settings-monitor',
  mixins: [
    ListPageMixin,
    FisReportPageMixin
  ],
  data () {
    return {
      title: 'Monitoring Settings',
      downloadUrl: `/admin/fis/settings/monitor/export`,
      requestUrl: `/admin/fis/settings/monitor/list`,
      columns: generateColumns([
        'ID', 'Name', 'Creator', 'Program',
        'Control Type', 'Transaction Types',
        'Monitoring Logic', 'System Action',
        'Public', 'Subscribed', 'Description',
        'Status', 'Action'
      ]),
      filters: {
        clients: [],
        controlTypes: []
      },
      filter: {
        client: 'All',
        controlType: 'All',
        transactionType: 'All'
      },
      freezeColumn: 0
    }
  },
  computed: {
    transactionTypes () {
      const ct = this.filter.controlType
      if (!ct || ct === 'All') {
        return [
          {
            label: 'All',
            value: 'All'
          }
        ]
      }
      return this.filters[`transactionTypes_${ct}`] || []
    }
  },
  methods: {
    resetFilter () {
      this.filter = {
        client: 'All',
        controlType: 'All',
        transactionType: 'All'
      }
      this.reload()
    },
    getQueryParams () {
      return _.pick(this.filter, ['client', 'controlType', 'transactionType'])
    },
    async initTopFilters () {
      this.loading = true
      const resp = await request(`/admin/fis/settings/monitor/filters`)
      this.loading = false
      if (resp.success) {
        this.filters = resp.data
      }
    },
    remove (item) {
      this.$q.dialog({
        title: `Delete Monitoring Setting`,
        message: `Are you sure that you want to delete this monitoring setting?`,
        cancel: true,
        color: 'negative'
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/fis/settings/monitor/delete?id=${item.ID}`, 'delete')
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
        }
      }).catch(() => {})
    },
    async subscribe (item, subscribe) {
      this.loading = true
      const resp = await request(`/admin/fis/settings/monitor/subscribe?id=${item.ID}&subscribe=${subscribe ? 'yes' : 'no'}`, 'post')
      this.loading = false
      if (resp.success) {
        notify(resp.message)
        item.Subscribed = subscribe ? 'Yes' : 'No'
      }
    },
    async changeStatus (item, status) {
      this.loading = true
      const resp = await request(`/admin/fis/settings/monitor/change-status?id=${item.ID}&status=${status}`, 'post')
      this.loading = false
      if (resp.success) {
        notify(resp.message)
        item.Status = status
      }
    },
    runForPast (item) {
      request(`/admin/fis/settings/monitor/run-for-past?id=${item.ID}`, 'post', {}, true)
      notifySuccess('Submitted the request. The system will search in the background. The records will be added to the Monitoring list automatically if any. Please check later.')
    }
  }
}
</script>

<style lang="scss">
#fis__settings_monitor__index_page {
  .fun-group .q-field {
    min-width: 200px !important;
  }
}
</style>
