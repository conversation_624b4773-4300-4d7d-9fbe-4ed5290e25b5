<template>
  <q-page id="fis__settings_types__index_page"
          class="fis_page">
    <div class="page-header column">
      <div class="title mb-15">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="row">
        <div class="col-3">
          <q-toolbar color="white"
                     class="custom-toolbar">
            <q-btn flat
                   round
                   color="primary"
                   @click="addType"
                   icon="mdi-plus-circle-outline">
              <q-tooltip>Add Program Type</q-tooltip>
            </q-btn>
            <!-- <q-btn flat
                   round
                   :disable="!selectedItem"
                   color="primary"
                   @click="addSubType"
                   icon="mdi-plus-circle-multiple-outline">
              <q-tooltip>Add Sub Type</q-tooltip>
            </q-btn> -->
            <q-btn flat
                   round
                   :disable="!selectedItem"
                   color="negative"
                   @click="deleteType"
                   icon="mdi-delete-outline">
              <q-tooltip>Delete</q-tooltip>
            </q-btn>
            <q-btn flat
                   round
                   color="primary"
                   class="ml-auto"
                   @click="reload"
                   icon="mdi-refresh">
              <q-tooltip>Reload</q-tooltip>
            </q-btn>
          </q-toolbar>
          <q-tree :nodes="nodes"
                  ref="tree"
                  default-expand-all
                  :selected.sync="selectedId"
                  class="custom-tree"
                  node-key="id"
                  label-key="name"></q-tree>
        </div>
        <div class="col-9 custom-form">
          <template v-if="selectedItem">
            <q-field label="Parent Type"
                     label-width="2"
                     v-if="parentItem">
              <q-input type="text"
                       disable
                       :value="parentItem.name"></q-input>
            </q-field>
            <q-field :label="selectedFolder ? 'Type Name' : 'Sub Type Name'"
                     label-width="2">
              <q-input type="text"
                       v-model="selectedItem.name"></q-input>
            </q-field>
            <q-field label="Description"
                     label-width="2">
              <q-input type="textarea"
                       rows="2"
                       :max-height="100"
                       v-model="selectedItem.description"></q-input>
            </q-field>
            <q-field label="Type"
                     label-width="2">
              <q-select :options="groupType"
                        filter
                        v-model="selectedItem.type"></q-select>
            </q-field>
            <template v-if="selectedItem.type == 'subprogram'">
              <q-field label="Subprogram"
                       label-width="2"
                       helper="Add a subprogram id in each row. You can also select a known subprogram from below dropdown list to insert.">
                <q-input type="textarea"
                         rows="5"
                         :max-height="200"
                         v-model="selectedItem.subprograms"></q-input>
              </q-field>
              <q-field label=" "
                       label-width="2">
                <q-select :options="subprograms"
                          filter
                          v-model="subprogram"></q-select>
              </q-field>
            </template>
            <template v-else-if="selectedItem.type == 'bin'">
              <q-field label="BINs"
                       label-width="2"
                       helper="Add a BIN in each row. You can also select an unassigned BIN from below dropdown list to insert.">
                <q-input type="textarea"
                         rows="5"
                         :max-height="200"
                         v-model="selectedItem.bins"></q-input>
              </q-field>
              <q-field label=" "
                       label-width="2">
                <q-select :options="bins"
                          filter
                          v-model="bin"></q-select>
              </q-field>
            </template>
            <template v-else-if="selectedItem.type == 'client'">
              <q-field label="Issuing Client"
                       label-width="2"
                       helper="Add a Issuing Client id in each row. You can also select a known Issuing Client from below dropdown list to insert.">
                <q-input type="textarea"
                         rows="5"
                         :max-height="200"
                         v-model="selectedItem.programs"></q-input>
              </q-field>
              <q-field label=" "
                       label-width="2">
                <q-select :options="programs"
                          filter
                          v-model="program"></q-select>
              </q-field>
            </template>

            <div class="row mt-20">
              <div class="col">
                <q-btn label="Save"
                       @click="submit"
                       color="primary"></q-btn>
              </div>
            </div>
          </template>
          <template v-else>
            <q-alert color="white"
                     text-color="dark"
                     icon="mdi-alert-circle-outline">
              Choose a type from left side to configure the name, description or other fields.
            </q-alert>
          </template>
        </div>
      </div>

      <q-inner-loading :visible="loading">
        <q-spinner size="40"></q-spinner>
      </q-inner-loading>
    </div>

    <HtmlListDialog></HtmlListDialog>
  </q-page>
</template>

<script>
import PageMixin from '../../../mixins/PageMixin'
import { notify, notifyResponse, request, toSelectOptions } from '../../../common'
import HtmlListDialog from '../../../components/HtmlListDialog'
import _ from 'lodash'

export default {
  name: 'fis-settings-types',
  mixins: [
    PageMixin
  ],
  components: {
    HtmlListDialog
  },
  data () {
    return {
      title: 'Group Type Settings',
      loading: false,
      nodes: [],
      bins: [],
      bin: null,
      programs: [],
      program: null,
      subprograms: [],
      subprogram: null,
      selectedId: null,
      groupType: [
        {
          label: 'BIN',
          value: 'bin'
        },
        {
          label: 'Subprogram',
          value: 'subprogram'
        },
        {
          label: 'Issuing Client',
          value: 'client'
        }
      ]
    }
  },
  computed: {
    selectedItem () {
      if (!this.selectedId) {
        return null
      }
      for (const tag of this.nodes) {
        if (tag.id === this.selectedId) {
          return tag
        }
        for (const sub of tag.children) {
          if (sub.id === this.selectedId) {
            return sub
          }
        }
      }
      return null
    },
    selectedFolder () {
      if (!this.selectedId) {
        return null
      }
      for (const tag of this.nodes) {
        if (tag.id === this.selectedId) {
          return tag
        }
      }
      return null
    },
    parentItem () {
      if (!this.selectedId) {
        return null
      }
      for (const tag of this.nodes) {
        if (tag.children) {
          for (const sub of tag.children) {
            if (sub.id === this.selectedId) {
              return tag
            }
          }
        }
      }
      return null
    }
  },
  watch: {
    bin () {
      if (!this.bin) {
        return
      }
      let old = this.selectedItem.bins
      if (!old) {
        old = ''
      }
      old = old.trim()
      const prefix = (!old || old.endsWith('\n')) ? '' : '\n'
      this.selectedItem.bins = `${old}${prefix}${this.bin}\n`
      this.bin = null
    },
    program () {
      if (!this.program) {
        return
      }
      let old = this.selectedItem.programs
      if (!old) {
        old = ''
      }
      old = old.trim()
      const prefix = (!old || old.endsWith('\n')) ? '' : '\n'
      this.selectedItem.programs = `${old}${prefix}${this.program}\n`
      this.program = null
    },
    subprogram () {
      if (!this.subprogram) {
        return
      }
      let old = this.selectedItem.subprograms
      if (!old) {
        old = ''
      }
      old = old.trim()
      const prefix = (!old || old.endsWith('\n')) ? '' : '\n'
      this.selectedItem.subprograms = `${old}${prefix}${this.subprogram}\n`
      this.subprogram = null
    }
  },
  methods: {
    async reload () {
      this.loading = true
      const resp = await request(`/admin/fis/settings/types`)
      this.loading = false
      if (resp.success) {
        this.nodes = resp.data.data
        this.bins = toSelectOptions(resp.data.bins)
        this.programs = toSelectOptions(resp.data.programs)
        this.subprograms = toSelectOptions(resp.data.subprograms)

        this.$nextTick(() => {
          this.$refs.tree.expandAll()
        })
      }
    },

    async submit () {
      if (!this.selectedItem.name) {
        notify('Please input the name!', 'negative')
        return
      }
      this.loading = true
      if (this.selectedItem.type === 'bin') {
        this.selectedItem.subprograms = ''
        this.selectedItem.programs = ''
      } else if (this.selectedItem.type === 'subprogram') {
        this.selectedItem.bins = ''
        this.selectedItem.programs = ''
      } else if (this.selectedItem.type === 'client') {
        this.selectedItem.bins = ''
        this.selectedItem.subprograms = ''
      }
      const resp = await request(`/admin/fis/settings/types/save`, 'post', this.selectedItem, true)
      this.loading = false
      if (resp.success) {
        notify(resp.message)
        this.selectedItem.id = resp.data
        this.selectedId = resp.data
        this.reload()
      } else {
        if (resp.data && resp.data.duplicated) {
          const data = []
          _.forEach(resp.data.duplicated, (v, k) => {
            data.push({
              value: k,
              type: v
            })
          })
          this.$root.$emit('show-html-list-dialog', {
            title: 'Error',
            message: resp.message,
            list: {
              columns: [{
                field: 'value',
                label: 'BIN',
                align: 'left'
              }, {
                field: 'type',
                label: 'Type',
                align: 'left'
              }],
              data
            }
          })
        } else {
          notifyResponse(resp)
        }
      }
    },

    addType () {
      const item = {
        id: 'new_' + (+new Date()),
        name: 'New Type',
        description: '',
        parent_id: null,
        bins: '',
        subprograms: '',
        programs: '',
        children: '',
        type: 'bin'
      }
      this.nodes.push(item)
      this.selectedId = item.id
    },

    addSubType () {
      let parent
      if (this.selectedFolder) {
        parent = this.selectedFolder
      } else {
        parent = this.parentItem
      }
      if (!parent) {
        return
      }
      const item = {
        id: 'new_' + (+new Date()),
        name: 'New Sub Type',
        description: '',
        parent_id: parent.id,
        programs: '',
        children: ''
      }
      parent.children.push(item)
      this.selectedId = item.id

      this.$nextTick(() => {
        this.$refs.tree.expandAll()
      })
    },

    async deleteType () {
      const item = this.selectedItem
      if (item.children && item.children.length) {
        return notify('Please delete all the sub types first!', 'negative')
      }
      if (typeof (item.id) === 'string' && item.id.startsWith('new_')) {
        const items = this.selectedFolder ? this.nodes : this.parentItem.children
        const index = items.indexOf(item)
        items.splice(index, 1)
        this.selectedId = null
        return
      }
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to delete this item?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request(`/admin/fis/settings/types/delete`, 'delete', item)
        this.loading = false
        if (resp.success) {
          notify(resp.message)
          this.selectedId = null
          this.nodes = resp.data

          this.$nextTick(() => {
            this.$refs.tree.expandAll()
          })
        }
      }).catch(() => {})
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
#fis__settings_types__index_page {
  .custom-tree {
    padding-right: 20px;
    max-height: 350px;
    overflow-y: auto;
    margin-top: 10px;

    .q-tree-node-header .row {
      min-height: 15px;
    }
  }

  .custom-form {
    padding-left: 20px;
    border-left: 1px solid rgba(0, 0, 0, 0.1);

    .q-field {
      margin-bottom: 10px;
    }
  }
}
</style>
