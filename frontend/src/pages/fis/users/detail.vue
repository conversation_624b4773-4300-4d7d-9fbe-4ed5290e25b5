<template>
  <q-page id="fis__users__detail_page"
          class="fis_page">
    <div class="page-header column">
      <div class="title mb-15">{{ id ? 'Edit' : 'Add New' }} {{ title }}</div>
    </div>
    <div class="page-content">
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          First Name <span class="text-negative ml-3">*</span>
        </div>
        <q-input v-model="form.firstName"
                 autocomplete="no"
                 :error="$v.form.firstName.$error"
                 @blur="$v.form.firstName.$touch"></q-input>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Last Name <span class="text-negative ml-3">*</span>
        </div>
        <q-input v-model="form.lastName"
                 autocomplete="no"
                 :error="$v.form.lastName.$error"
                 @blur="$v.form.lastName.$touch"></q-input>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Email <span class="text-negative ml-3">*</span>
        </div>
        <q-input v-model="form.email"
                 autocomplete="no"
                 :error="$v.form.email.$error"
                 @blur="$v.form.email.$touch"></q-input>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Status <span class="text-negative ml-3">*</span>
        </div>
        <q-select :options="source.statuses"
                  :error="$v.form.status.$error"
                  @blur="$v.form.status.$touch"
                  v-model="form.status"></q-select>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          User Type <span class="text-negative ml-3">*</span>
        </div>
        <q-select :options="source.roles"
                  :error="$v.form.role.$error"
                  @blur="$v.form.role.$touch"
                  v-model="form.role"></q-select>
      </div>
      <div class="col q-field"
           v-show="isProgramOwner">
        <div class="q-field-label items-center flex">
          Company <span class="text-negative ml-3">*</span>
        </div>
        <q-input v-model="form.company"
                 autocomplete="no"
                 :error="$v.form.company.$error"
                 @blur="$v.form.company.$touch"></q-input>
      </div>
      <div class="col q-field"
           v-show="isProgramOwner">
        <div class="q-field-label items-center flex">
          Platforms <span class="text-negative ml-3">*</span>
        </div>
        <div class="mv-10"
             v-for="(p, k) in source.platforms"
             :key="k">
          <div class="mb-8">
            <q-checkbox v-model="form.platforms"
                        :error="$v.form.platforms.$error"
                        @change="$v.form.platforms.$touch"
                        :label="p + ' (' + k.toUpperCase() + ')'"
                        :val="k"></q-checkbox>
          </div>

          <q-field label="Accessible Issuing Clients"
                   v-show="form.platforms.includes(k)"
                   :label-width="12"
                   icon="mdi-blank">
            <q-select multiple
                      chips
                      v-model="form.programs[k]"
                      :options="source.programs[k]"></q-select>
          </q-field>
        </div>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Default Platform
        </div>
        <q-select :options="defaultPlatforms"
                  v-model="form.defaultPlatform"></q-select>
      </div>
      <div class="col q-field">
        <div class="q-field-label items-center flex">
          Avatar
        </div>
        <q-uploader url="/attachments"
                    name="avatar"
                    auto-expand
                    ref="uploader_avatar"
                    @add="$refs.uploader_avatar && $refs.uploader_avatar.upload()"
                    @uploaded="(file, xhr) => uploadedAttachment('avatar', xhr)"
                    :additional-fields="[{name: 'category', value: 'public'}]"
                    extensions=".gif,.jpg,.jpeg,.png"
                    clearable></q-uploader>
        <div class="text-grey font-13 mt-5">Click on '+' button to select the file to upload. Please ensure that the file has been uploaded before submitting the form.</div>

        <div class="row gutter-sm mt-5"
             v-if="form.avatar">
          <div class="col">
            <img :src="form.avatar"
                 class="max-w-250"
                 alt="">
            <div class="q-mb-lg">
              <q-btn label="Remove"
                     flat
                     @click="removeAttachment('avatar')"
                     icon="mdi-close"
                     color="negative"
                     size="sm"></q-btn>
            </div>
          </div>
        </div>
      </div>
      <div class="col q-field"
           v-if="form.id">
        <div class="col q-field"
             v-if="form.onboard">
          <div class="q-field-label items-center flex">
            Register Date
          </div>
          <div class="mv-5">{{ form.createdAt | date('L LT') }}</div>
        </div>
        <q-btn color="warning"
               no-caps
               v-else
               @click="invite"
               label="Resend Welcome Email"></q-btn>
      </div>
      <div class="mv-20">
        <q-btn color="primary"
               :disable="$v.$invalid"
               @click="submit"
               label="Submit"></q-btn>
        <q-btn color="light"
               label="Cancel"
               to="/j/fis/users"
               class="ml-15"></q-btn>
      </div>

      <q-inner-loading :visible="loading">
        <q-spinner size="40"></q-spinner>
      </q-inner-loading>
    </div>
  </q-page>
</template>

<script>
import PageMixin from '../../../mixins/PageMixin'
import { notify, request } from '../../../common'
import { required, requiredIf, email } from 'vuelidate/lib/validators'
import _ from 'lodash'

export default {
  name: 'fis-users-detail',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'User',
      source: {
        roles: [],
        statuses: [],
        platforms: [],
        programs: {
          ppm: [],
          dpp: []
        }
      },
      form: {
        company: 'FIS',
        platforms: [],
        programs: {},
        onboard: true,
        createdAt: null
      }
    }
  },
  computed: {
    id () {
      return parseInt(this.$route.params.id || 0)
    },
    defaultPlatforms () {
      const selected = this.form.platforms
      if (!selected || selected.length <= 0) {
        return []
      }
      if (selected.length === 1) {
        const v = selected[0]
        return [
          { label: this.source.platforms[v] + ' (' + v.toUpperCase() + ')', value: v }
        ]
      }
      const all = [
        { label: 'None', value: '' }
      ]
      for (const v of selected) {
        all.push({ label: this.source.platforms[v] + ' (' + v.toUpperCase() + ')', value: v })
      }
      return all
    },
    isProgramOwner () {
      const roles = this.source.roles || []
      for (const role of roles) {
        if (role.value === this.form.role) {
          return role.label.indexOf('Program Owner') >= 0
        }
      }
      return false
    }
  },
  watch: {
    defaultPlatforms (all) {
      if (all.length === 0) {
        this.form.defaultPlatform = null
      } else if (all.length === 1) {
        this.form.defaultPlatform = all[0].value
      }
    },
    'form.role' (v, o) {
      if (o === undefined) {
        this.form.platforms = Object.keys(this.source.platforms)
        return
      }
      if (this.isProgramOwner) {
        this.form.platforms = []
        this.form.company = ''
      } else {
        this.form.platforms = Object.keys(this.source.platforms)
        this.form.company = 'FIS'
      }
    }
  },
  validations: {
    form: {
      firstName: {
        required
      },
      lastName: {
        required
      },
      email: {
        required,
        email
      },
      status: {
        required
      },
      role: {
        required
      },
      platforms: {
        notEmpty () {
          return this.form.platforms.length > 0
        }
      },
      company: {
        required: requiredIf(function () {
          return this.isProgramOwner
        })
      }
    }
  },
  methods: {
    async reload () {
      this.loading = true
      const resp = await request(`/admin/fis/users/detail?id=${this.id}`)
      this.loading = false
      if (resp.success) {
        this.source = resp.data.source
        this.form = resp.data.form
        if (!this.form.platforms) {
          this.$set(this.form, 'platforms', [])
        }
        if (!this.form.defaultPlatform) {
          this.$set(this.form, 'defaultPlatform', null)
        }
        if (!this.form.programs) {
          this.$set(this.form, 'programs', {})
        }
        _.forEach(this.source.platforms, (v, k) => {
          if (this.form.programs[k] === undefined) {
            this.$set(this.form.programs, k, null)
          }
        })
      }
    },

    async submit () {
      if (this.$v.$invalid) {
        return
      }
      this.loading = true
      const resp = await request(`/admin/fis/users/save`, 'post', this.form)
      this.loading = false
      if (resp.success) {
        notify(resp.message)
        this.$router.replace('/j/fis/users')
      }
    },

    async invite () {
      this.loading = true
      const resp = await request(`/admin/fis/users/${this.form.id}/invite`, 'post', this.form)
      this.loading = false
      if (resp.success) {
        notify(resp.message)
      }
    },

    removeAttachment (field) {
      this.form[field] = null
      this.form[`${field}_delete`] = true
    },

    uploadedAttachment (field, xhr) {
      const resp = JSON.parse(xhr.response)
      const file = resp.data[field]
      this.form[field] = file.url
      this.form[`${field}_id`] = file.id
      delete this.form[`${field}_delete`]

      this.$refs[`uploader_${field}`].reset()
    }
  },
  mounted () {
    this.reload()
  }
}
</script>
