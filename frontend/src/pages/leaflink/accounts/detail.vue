<template>
  <q-dialog class="leaflink-account-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <h1>{{entity['First Name']}} {{entity['Last Name']}}</h1>
      <p>{{entity['User ID']}}</p>
      <q-chip class="font-12"
              :class="statusClass(entity['Status'])">
        {{ entity['Status'] }}
      </q-chip>
      <q-list bordered
              separator>
        <q-item v-ripple>
          <q-item-side>
            Bank Name
          </q-item-side>
          <q-item-main>
            {{entity['Company Name']}}
          </q-item-main>
        </q-item>
        <q-item v-ripple>
          <q-item-side>
            Bank ID
          </q-item-side>
          <q-item-main>
            {{entity['Bank ID']}}
          </q-item-main>
        </q-item>
        <q-item v-ripple>
          <q-item-side>
            Email
          </q-item-side>
          <q-item-main>
            {{entity['Company Email']}}
          </q-item-main>
        </q-item>
        <q-item v-ripple>
          <q-item-side>
            Phone
          </q-item-side>
          <q-item-main>
            {{entity['Mobile Phone']}}
          </q-item-main>
        </q-item>
      </q-list>
    </template>

    <template slot="buttons">
      <div></div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'

export default {
  name: 'leaflink-account-detail-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
    }
  },
  computed: {
  },
  methods: {
    statusClass (status) {
      const cls = []
      cls.push({
        'Pending': 'status-pending',
        'Initial': 'status-initial',
        'Active': 'status-positive'
      }[status] || status)

      return cls
    }
  }
}
</script>

<style lang="scss">
.leaflink-account-detail-dialog {
  .modal-content {
    width: 400px;
    h1 {
      color: #171726;
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 5px;
      text-transform: capitalize;
    }
    p {
      color: #92929e;
      font-size: 8px;
      font-weight: 500;
      margin-bottom: 5px;
    }
    .q-list {
      border-top: none;
      border-right: none;
      border-left: none;
      max-width: 325px;
      margin: 0 auto;
      padding: 0;
      .q-item {
        padding: 16px 0;
      }
      .q-item-side {
        color: #92929e;
      }
      .q-item-division {
        border-color: #e2e2ea;
      }
      .q-item-main {
        text-align: right;
        color: #171725;
        font-size: 16px;
      }
    }
    .q-chip.status-positive {
      background-color: rgba($color: #00d993, $alpha: 0.1);
      color: #00d993 !important;
    }
    .q-chip.status-initial {
      background-color: rgba($color: #00b8ff, $alpha: 0.1);
      color: #00b8ff !important;
    }
    .q-chip.status-pending {
      background-color: rgba($color: #ffc300, $alpha: 0.1);
      color: #ffc300 !important;
    }
  }
}
</style>
