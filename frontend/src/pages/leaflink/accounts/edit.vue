<template>
  <q-dialog class="leaflink-account-edit-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Edit Account</div>
      <div class="font-12 normal text-dark">Please fill in the information below about the account.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-12">
          <q-input float-label="Company Name"
                   autocomplete="no"
                   v-model="entity['Company Name']"></q-input>
        </div>
        <div class="col-6">
          <q-input float-label="First Name"
                   autocomplete="no"
                   :error="$v.entity['First Name'].$error"
                   @blur="$v.entity['First Name'].$touch"
                   v-model="entity['First Name']"></q-input>
        </div>
        <div class="col-6">
          <q-input float-label="Last Name"
                   autocomplete="no"
                   :error="$v.entity['Last Name'].$error"
                   @blur="$v.entity['Last Name'].$touch"
                   v-model="entity['Last Name']"></q-input>
        </div>
        <div class="col-6">
          <q-input float-label="Company Email"
                   autocomplete="no"
                   :error="$v.entity['Company Email'].$error"
                   @blur="$v.entity['Company Email'].$touch"
                   v-model="entity['Company Email']"></q-input>
        </div>
        <div class="col-6">
          <div class="row">
            <q-select class="phone-code col-4"
                      autocomplete="no"
                      float-label="Phone"
                      filter
                      autofocus-filter
                      v-model="entity['phoneCode']"
                      :options="countries" />
            <q-input class="phone-number col-8"
                     placeholder="Phone Only digits. No country dial code."
                     :error="$v.entity['phone'].$error"
                     @blur="$v.entity['phone'].$touch"
                     v-model="entity['phone']">
            </q-input>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn label="Save"
               no-caps
               class="col-12 save-btn"
               @click="save" />
        <q-btn label="Cancel"
               no-caps
               color="grey-3"
               text-color="tertiary"
               class="col-12 cancel-btn"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request } from '../../../common'
import { required, numeric, email } from 'vuelidate/lib/validators'
import _ from 'lodash'
const countryList = require('country-data').countries

export default {
  name: 'leaflink-account-edit-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        'phoneCode': 'CW',
        'phone': ''
      },
      LoadLocatorOptions: [],
      countries: []
    }
  },
  computed: {
    edit () {
      return this.entity['User ID']
    }
  },
  mounted () {
    countryList.all.map(x => {
      if (x.countryCallingCodes.length) {
        this.countries.push({
          label: x.emoji ? x.emoji + ' ' + x.countryCallingCodes[0] : x.name + ' ' + x.countryCallingCodes,
          value: x.alpha2,
          code: x.countryCallingCodes[0]
        })
      }
    })
    return this.countries
  },
  validations: {
    entity: {
      'First Name': {
        required
      },
      'Last Name': {
        required
      },
      'Company Email': {
        required,
        email
      },
      'phone': {
        required,
        numeric
      }
    }
  },
  methods: {
    async show () {
      if (this.entity['Mobile Phone']) {
        const phoneList = this.entity['Mobile Phone'].split(' ')
        console.log(phoneList)
        let code = 'CW'
        if (phoneList.length > 1) {
          this.entity['phone'] = phoneList[1]
          this.countries.forEach(element => {
            console.log(element)
            if (element.code === phoneList[0]) {
              code = element.value
            }
          })
        } else {
          this.entity['phone'] = phoneList[0]
        }
        this.entity['phoneCode'] = code
      }
    },
    onSuccess (resp) {
      notify(resp.message)

      if (this.origin && this.origin['User ID']) {
        _.assignIn(this.origin, resp.data)
      }

      this.$root.$emit('reload-cow-agents')
      this._hide()
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      this.entity['Mobile Phone'] = this.entity['phone']
      const resp = await request(`/admin/cow/agents/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    },
    async toggleStatus () {
      if (!this.origin) {
        return
      }
      this.$q.loading.show()
      const resp = await request(`/admin/cow/agents/${this.entity['User ID']}/toggle-status`, 'post', {
        Status: this.origin['Status'] === 'Active' ? 'Archived' : 'Active'
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    }
  }
}
</script>

<style lang="scss">
.leaflink-account-edit-dialog {
  .modal-content {
    width: 550px;
  }
  .phone-code {
    // border-right: none;
    padding-right: 0 !important;
    padding-left: 10px !important;
    min-width: 100px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .phone-number {
    border-left: none;
    max-width: calc(100% - 100px);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .save-btn {
    background: #00becb;
    color: #fff;
    font-weight: 600;
    .q-btn-inner {
      line-height: 30px;
    }
  }
  .cancel-btn {
    margin-left: 0 !important;
    margin-top: 10px;
    .q-btn-inner {
      line-height: 30px;
      font-weight: 600;
    }
  }
}
</style>
