<template>
  <q-page id="leaflink__account__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="ACH Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img"><img src="/static/jake/icons/leaflink_user.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.totalSize || 0 }}
                  </div>
                  <div class="description">Total Accounts</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img  active-item"><img src="/static/jake/icons/leaflink_user.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.activeAccounts || 0 }}
                  </div>
                  <div class="description">Active Accounts</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img pending-item"><img src="/static/jake/icons/leaflink_user.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.pendingAccounts || 0 }}
                  </div>
                  <div class="description">Pending Accounts</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img initial-item"><img src="/static/jake/icons/leaflink_user.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.initialAccounts || 0 }}
                  </div>
                  <div class="description">Initial Accounts</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :rows-per-page-options="[ 10, 15, 20, 25, 50, 100]"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Company Report</strong>
        </template>
        <template slot="top-right">
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn icon="mdi-file-download-outline"
                 label="Export as CSV"
                 @click="download"
                 class="btn-sm mr-8 export-btn"
                 no-caps></q-btn>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Account Info'">
              <q-chip class="account-info"
                      v-if="props.row['Account Info'].length !== 0">View
                <q-popover class="account-detail-item">
                  <p class="font-14">
                    Account Number: {{props.row['Account Info']['number']}}
                  </p>
                  <p class="font-14">
                    Routing Number: {{props.row['Account Info']['routing']}}
                  </p>
                </q-popover>
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="view(props.row)">
                    <q-item-main>View</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="email(props.row)">
                    <q-item-main>Email</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="archive(props.row)">
                    <q-item-main>Archive</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <AccountDetail></AccountDetail>
    <EditAccount></EditAccount>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import LeaflinkPageMixin from '../../../mixins/leaflink/LeaflinkPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import AccountDetail from './detail'
import EditAccount from './edit'
import _ from 'lodash'

export default {
  name: 'leaflink-account',
  mixins: [
    LeaflinkPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-leaflink-account')
  ],
  components: {
    AccountDetail,
    EditAccount
  },
  data () {
    return {
      title: 'Accounts',
      requestUrl: `/admin/leaflink/accounts/list`,
      downloadUrl: `/admin/leaflink/accounts/export`,
      keyword: '',
      dateRange: 'all',
      columns: generateColumns([
        'Bank ID', 'Account Info', 'Institution Name', 'Routing Number', 'Account Linking Method', 'Company Name', 'Method', 'First Name', 'Last Name', 'Company Email', 'Last Update', 'Status', 'Actions'
      ], [], {
        'Bank ID': 'config.id',
        'Company Name': 'config.companyName',
        'First Name': 'user.firstName',
        'Last Name': 'user.lastName',
        'Company Email': 'user.email',
        'Last Update': 'user.updatedAt'
      }),
      filterOptions: [
        {
          value: 'filter[config.id=]',
          label: 'Company ID'
        }, {
          value: 'filter[config.companyName=]',
          label: 'Company Name'
        }, {
          value: 'filter[user.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[user.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[config.bankAccountId=]',
          label: 'Bank Account ID'
        }, {
          value: 'filter[user.status=]',
          label: 'Status',
          options: [
            { label: 'Active', value: 'active' },
            { label: 'Archived', value: 'closed' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  computed: {
    visibleColumns () {
      return _.filter(this.columns, c => {
        if (this.$store.state.User.teams.indexOf('MasterAdmin') === -1 && this.$store.state.User.teams.indexOf('LeafLink Employer') === -1 && c.label === 'Account Info') {
          c.hidden = true
        }
        return !c.hidden
      })
    }
  },
  methods: {
    statusClass (status) {
      const cls = []
      cls.push({
        'Pending': 'status-pending',
        'Initial': 'status-initial',
        'Active': 'status-positive'
      }[status] || status)

      return cls
    },
    view (row) {
      this.$root.$emit('show-leaflink-account-detail-dialog', row)
    },
    edit (row) {
      this.$root.$emit('show-leaflink-account-edit-dialog', row)
    },
    email (row) {
    },
    archive (row) {
    }
  }
}
</script>
<style lang="scss">
#leaflink__account__index_page {
  .icon-img {
    width: 24px;
    height: 24px;
    display: flex;
    background: rgba($color: #000000, $alpha: 0.1);
    border-radius: 5px;
    img {
      width: 18px;
      max-width: 100%;
      margin: 0 auto;
    }
  }
  .active-item {
    background: rgba($color: #00d993, $alpha: 0.1);
    img {
      filter: invert(0.5) sepia(1) saturate(5) hue-rotate(85deg);
    }
  }
  .pending-item {
    background: rgba($color: #ffc300, $alpha: 0.1);
    img {
      filter: invert(0.5) sepia(1) saturate(5) hue-rotate(10deg);
    }
  }
  .initial-item {
    background: rgba($color: #00b8ff, $alpha: 0.1);
    img {
      filter: invert(0.5) sepia(1) saturate(5) hue-rotate(180deg);
    }
  }
  .export-btn {
    background: #00becb;
    color: #fff;
  }
  .q-chip.status-positive {
    background-color: rgba($color: #00d993, $alpha: 0.1);
    color: #00d993 !important;
  }
  .q-chip.status-initial {
    background-color: rgba($color: #00b8ff, $alpha: 0.1);
    color: #00b8ff !important;
  }
  .q-chip.status-pending {
    background-color: rgba($color: #ffc300, $alpha: 0.1);
    color: #ffc300 !important;
  }
  .account-info {
    background-color: rgba($color: #00d993, $alpha: 0.1);
    color: #00d993 !important;
  }
}
.account-detail-item.q-popover {
  padding: 10px;
  p {
    margin: 0;
  }
}
</style>
