<template>
  <q-card id="accounts">
    <q-card-title>
      <div class="row">
        <div class="chart-title">
          <p>Accounts</p>
          <span>{{delta}}</span>
        </div>
        <q-btn class="accounts-btn down-btn btn-sm ml-auto mr-8 square"
               @click="download()"
               icon="mdi-file-document-outline"></q-btn>
        <q-btn class="accounts-btn report-btn btn-sm square"
               @click="viewReport()"
               icon="mdi-chart-line"></q-btn>
      </div>
    </q-card-title>
    <q-card-main class="chart-item">
      <div id="accountsChart"></div>
      <div v-if="visible"
           class="loading">
        <q-spinner-hourglass color="primary"
                             size="3em"
                             :thickness="2" />
      </div>
    </q-card-main>
  </q-card>
</template>

<script>
import echarts from 'echarts'
import $ from 'jquery'
import { request, EventHandlerMixin, notify } from '../../../common'
import html2canvas from 'html2canvas'
import moment from 'moment'

export default {
  components: {
  },
  props: {
    'value': {
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  watch: {
    value () {
      this.getData()
    }
  },
  mixins: [
    EventHandlerMixin('reload-accounts-chart', 'getData')
  ],
  data () {
    return {
      delta: 0,
      visible: false,
      chart: null,
      startTime: moment().startOf('month'),
      endTime: moment().endOf('month')
    }
  },
  async mounted () {
    this.getData()
  },
  methods: {
    resize () {
      this.chart.resize()
    },
    viewReport () {
      this.$router.push(`/j/leaflink/accounts`)
    },
    async getData () {
      this.visible = true
      switch (this.value) {
        case 'all':
          this.startTime = null
          this.endTime = null
          break
        case 'week':
          this.startTime = moment().startOf('week')
          this.endTime = moment().endOf('week')
          break
        case 'month':
          this.startTime = moment().startOf('month')
          this.endTime = moment().endOf('month')
          break
        case 'quarter':
          this.startTime = moment().startOf('quarter')
          this.endTime = moment().endOf('quarter')
          break
        case 'year':
          this.startTime = moment().startOf('year')
          this.endTime = moment().endOf('year')
          break
        case 'today':
          this.startTime = this.startDate
          this.endTime = this.endDate
          break
      }
      const resp = await request(`/admin/leaflink/accountsChart`, 'get', { 'period': this.value, 'start': this.startTime ? moment(this.startTime).format('L') : null, 'end': this.startTime ? moment(this.endTime).format('L') : null })
      if (resp.success) {
        this.dataList = resp.data.data
        this.delta = resp.data.total
        this.initChart()
      }
      this.visible = false
    },
    initChart () {
      this.chart = echarts.init(document.getElementById('accountsChart'), 'primary')
      let colorList = ['#ff8e32', '#00d993', '#ff4852']
      let labelList = ['Pending', 'Active', 'Inactive']
      let positionStr = ''
      let option = {
        xAxis: {
          type: 'category',
          show: false
        },
        grid: {
          left: '60px',
          right: '20px',
          top: '5px',
          bottom: '20px'
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          }
        },
        series: {
          data: this.dataList,
          type: 'bar',
          barWidth: '40px',
          itemStyle: {
            barBorderRadius: [20, 20, 0, 0],
            color: function (params) {
              return colorList[params.dataIndex]
            }
          },
          label: {
            show: true,
            position: 'bottom',
            color: '#92929e',
            formatter: function (params) {
              // build a color map as your need.
              return labelList[params.dataIndex]
            }
          }
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: '#ffffff',
          padding: 0,
          // alwaysShowContent: true,
          position: function (pos, params, dom, rect, size) {
            // 鼠标在左侧时 tooltip 显示到右侧，鼠标在右侧时 tooltip 显示到左侧。
            let obj = { top: pos[1] - 110 }
            if (pos[0] < size.viewSize[0] / 2) {
              obj['left'] = pos[0] - 50
              positionStr = 'left'
            } else {
              positionStr = 'right'
              obj['right'] = size.viewSize[0] - pos[0] - 50
            }
            return obj
          },
          formatter: function (params) {
            // build a color map as your need.
            return '<div class="tooltip-area-' + positionStr + '" style="box-shadow: 0 5px 15px 0 rgba(28, 20, 70, 0.1);border-radius:4px;padding:7px;font-family: Poppins;font-size: 14px;"><p style="text-align:center;margin:0px;color:#231f20;font-weight: 600;margin:0px;">' + params.value + '</p>' +
            '<p style="color: #696975;font-size: 7px;text-align:center;margin:0px;">' + labelList[params.dataIndex] + '</p>' +
            '<p style="color: #696975;font-size: 7px;text-align:center;margin:0px;">Companies</p>' +
            '</div>'
          }
        }
      }
      this.chart.setOption(option)

      $(window).on('resize', this.resize)
    },
    download () {
      let target = null
      let userAgent = navigator.userAgent
      if (userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Chrome') === -1) {
        return notify('Please take a screenshot manually or use another browser to open the website to save the picture!', 'negative')
      }
      let name = 'processedPaymentsChart'
      target = document.getElementById('processedPaymentsChart')
      this.$q.loading.show()
      const targetCss = window.getComputedStyle(target)

      const options = {
        scale: 2,
        width: parseInt(targetCss.width, 10),
        height: parseInt(targetCss.height, 10),
        y: target.offsetTop + 400
      }
      // console.log(options)
      html2canvas(target, options).then(canvas => {
        // canvas is the final rendered <canvas> element
        const imgData = canvas.toDataURL('image/jpeg')
        console.log(imgData)
        this.fileDownload(imgData, name)
        this.$q.loading.hide()
      })
    },
    fileDownload (downloadUrl, filename) {
      let aLink = document.createElement('a')
      aLink.style.display = 'none'
      aLink.href = downloadUrl
      aLink.download = `${filename}`
      // 触发点击-然后移除
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    }
  }
}
</script>
<style lang="scss">
#accounts {
  margin: 0 !important;
  .accounts-btn {
    max-height: 32px !important;
    color: #fff;
  }
  .report-btn {
    background: #1b1443;
  }
  .down-btn {
    background: #00becb;
  }
  .chart-title {
    p {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
    span {
      font-size: 28px;
      font-weight: 600;
    }
  }
  .manual-load-big-btn {
    background: #00d993;
    color: #fff;
    max-height: 38px;
  }

  #accountsChart {
    height: 400px;
  }
  .chart-item {
    position: relative;
  }
  .loading {
    height: 400px;
    line-height: 400px;
    width: calc(100% - 32px);
    text-align: center;
    position: absolute;
    top: 0;
    z-index: 100;
    background: rgba($color: #000000, $alpha: 0.1);
  }
  .tooltip-area-left :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    left: 35px;
  }
  .tooltip-area-right :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    right: 35px;
  }
}
</style>
