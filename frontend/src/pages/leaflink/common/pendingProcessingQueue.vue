<template>
  <q-card id="pendingProcessingQueue">
    <q-card-title>
      <div class="row">
        <div class="chart-title">
          <p>Pending Processing Queue</p>
          <span>{{delta | moneyFormat}}</span>
        </div>
        <q-btn class="pendingProcessingQueue-btn down-btn btn-sm ml-auto mr-8 square"
               @click="download()"
               icon="mdi-file-document-outline">Download Report</q-btn>
        <q-btn class="pendingProcessingQueue-btn report-btn btn-sm square"
               @click="viewReport()"
               icon="mdi-chart-line">View Report</q-btn>
      </div>
    </q-card-title>
    <q-card-main class="chart-item">
      <div id="pendingProcessingQueueChart"></div>
      <div v-if="visible"
           class="loading">
        <q-spinner-hourglass color="primary"
                             size="3em"
                             :thickness="2" />
      </div>
    </q-card-main>
  </q-card>
</template>

<script>
import echarts from 'echarts'
import $ from 'jquery'
import { request, EventHandlerMixin, moneyFormat, notify } from '../../../common'
import html2canvas from 'html2canvas'
import moment from 'moment'

export default {
  components: {
  },
  props: {
    'value': {
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  watch: {
    value () {
      this.getData()
    }
  },
  mixins: [
    EventHandlerMixin('reload-pending-processing-queue-chart', 'getData')
  ],
  data () {
    return {
      delta: 0,
      visible: false,
      chart: null,
      series: [
        {
          name: 'Processing',
          type: 'bar',
          // stack: 'total',
          color: '#ff9031',
          barWidth: '40px',
          barGap: '-100%',
          itemStyle: {
            barBorderRadius: [0, 20, 20, 0]
          },
          zlevel: 3,
          label: {
            show: false
          },
          emphasis: {
            focus: 'series'
          },
          data: [0]
        },
        {
          name: 'Errored or Failed',
          type: 'bar',
          // stack: 'total',
          color: '#ffc200',
          barWidth: '40px',
          barGap: '-100%',
          zlevel: 2,
          itemStyle: {
            barBorderRadius: [0, 20, 20, 0]
          },
          label: {
            show: false
          },
          emphasis: {
            focus: 'series'
          },
          data: [0]
        },
        {
          name: 'Bank Returned',
          type: 'bar',
          // stack: 'total',
          color: '#ff4852',
          barWidth: '40px',
          barGap: '-100%',
          zlevel: 1,
          itemStyle: {
            barBorderRadius: [0, 20, 20, 0]
          },
          label: {
            show: false
          },
          emphasis: {
            focus: 'series'
          },
          data: [0]
        }
      ],
      startTime: moment().startOf('month'),
      endTime: moment().endOf('month')
    }
  },
  async mounted () {
    this.getData()
  },
  methods: {
    resize () {
      this.chart.resize()
    },
    viewReport () {
      this.$router.push(`/j/leaflink/pending`)
    },
    async getData () {
      this.visible = true
      switch (this.value) {
        case 'all':
          this.startTime = null
          this.endTime = null
          break
        case 'week':
          this.startTime = moment().startOf('week')
          this.endTime = moment().endOf('week')
          break
        case 'month':
          this.startTime = moment().startOf('month')
          this.endTime = moment().endOf('month')
          break
        case 'quarter':
          this.startTime = moment().startOf('quarter')
          this.endTime = moment().endOf('quarter')
          break
        case 'year':
          this.startTime = moment().startOf('year')
          this.endTime = moment().endOf('year')
          break
        case 'today':
          this.startTime = this.startDate
          this.endTime = this.endDate
          break
      }
      const resp = await request(`/admin/leaflink/pendingProcessingQueueChart`, 'get', { 'period': this.value, 'start': this.startTime ? moment(this.startTime).format('L') : null, 'end': this.startTime ? moment(this.endTime).format('L') : null })
      if (resp.success) {
        this.dataList = resp.data
        this.delta = resp.data.total
        this.series[0].data = [(this.dataList['Processing'][0]['total'] / 100)]
        this.series[1].data = [(this.dataList['Processing'][0]['total'] * 1 + this.dataList['Errored or Failed'][0]['total'] * 1) / 100]
        this.series[2].data = [(this.dataList['Processing'][0]['total'] * 1 + this.dataList['Errored or Failed'][0]['total'] * 1 + this.dataList['Bank Returned'][0]['total'] * 1) / 100]
        this.initChart()
        console.log(this.series)
        console.log(this.dataList)
      }
      this.visible = false
    },
    initChart () {
      this.chart = echarts.init(document.getElementById('pendingProcessingQueueChart'), 'primary')
      let that = this
      // let colorList = ['#ff9031', '#ffc200', '#ff4852']
      // let labelList = ['Processing', 'Returned NSF', 'Returned Invalid Bank']
      let positionStr = ''
      let option = {
        legend: {
          bottom: '0px',
          icon: 'pin',
          data: ['Processing', 'Errored or Failed', 'Bank Returned']
        },
        tooltip: {
          // trigger: 'axis',
          backgroundColor: '#ffffff',
          padding: 0,
          position: function (pos, params, dom, rect, size) {
            // 鼠标在左侧时 tooltip 显示到右侧，鼠标在右侧时 tooltip 显示到左侧。
            let obj = { top: pos[1] - 150 }
            if (pos[0] < size.viewSize[0] / 2) {
              obj['left'] = pos[0] - 50
              positionStr = 'left'
            } else {
              positionStr = 'right'
              obj['right'] = size.viewSize[0] - pos[0] - 50
            }
            // console.log(obj)
            return obj
          },
          formatter: function (params) {
            console.log(params)
            return '<div class="tooltip-area-' + positionStr + '" style="min-width: 300px;box-shadow: 0 5px 15px 0 rgba(28, 20, 70, 0.1);border-radius:4px;padding:30px;padding-top:16px;font-family: Poppins;font-size: 14px;"><p style="text-align:center;margin:0px;color:#231f20;font-weight: 600;">' + params.seriesName + '</p>' +
            '<p style="text-align:left;margin:0px;color:#231f20;display:flex;justify-content:space-between;"><span>Total($)</span><span>' + moneyFormat(that.dataList[params.seriesName][0]['total']) + '</span></p>' +
            '<p style="text-align:left;margin:0px;color:#231f20;display:flex;justify-content:space-between;"><span># of Txns</span><span>' + that.dataList[params.seriesName][0]['c'] + '</span></p>' +
            '<p style="text-align:left;margin:0px;color:#231f20;display:flex;justify-content:space-between;"><span>Avg</span><span>' + moneyFormat(that.dataList[params.seriesName][0]['av']) + '</span></p></div>'
          }
        },
        xAxis: {
          type: 'value'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '40px',
          containLabel: true
        },
        yAxis: {
          type: 'category'
        },
        series: this.series
      }
      this.chart.setOption(option)

      $(window).on('resize', this.resize)
    },
    download () {
      let target = null
      let userAgent = navigator.userAgent
      if (userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Chrome') === -1) {
        return notify('Please take a screenshot manually or use another browser to open the website to save the picture!', 'negative')
      }
      let name = 'pendingProcessingQueue'
      target = document.getElementById('pendingProcessingQueue')
      this.$q.loading.show()
      const targetCss = window.getComputedStyle(target)

      const options = {
        scale: 2,
        width: parseInt(targetCss.width, 10),
        height: parseInt(targetCss.height, 10),
        y: target.offsetTop + 400
      }
      // console.log(options)
      html2canvas(target, options).then(canvas => {
        // canvas is the final rendered <canvas> element
        const imgData = canvas.toDataURL('image/jpeg')
        console.log(imgData)
        this.fileDownload(imgData, name)
        this.$q.loading.hide()
      })
    },
    fileDownload (downloadUrl, filename) {
      let aLink = document.createElement('a')
      aLink.style.display = 'none'
      aLink.href = downloadUrl
      aLink.download = `${filename}`
      // 触发点击-然后移除
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    }
  }
}
</script>
<style lang="scss">
#pendingProcessingQueue {
  margin: 0 !important;
  .pendingProcessingQueue-btn {
    max-height: 32px !important;
    color: #fff;
    text-transform: capitalize;
  }
  .report-btn {
    background: #1b1443;
  }
  .down-btn {
    background: #00becb;
  }
  .chart-title {
    p {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
    span {
      font-size: 28px;
      font-weight: 600;
    }
  }
  .manual-load-big-btn {
    background: #00d993;
    color: #fff;
    max-height: 38px;
  }

  #pendingProcessingQueueChart {
    height: 400px;
  }
  .chart-item {
    position: relative;
  }
  .loading {
    height: 400px;
    line-height: 400px;
    width: calc(100% - 32px);
    text-align: center;
    position: absolute;
    top: 0;
    z-index: 100;
    background: rgba($color: #000000, $alpha: 0.1);
  }
  .tooltip-area-left :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    left: 40px;
  }
  .tooltip-area-right :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    right: 40px;
  }
}
</style>
