<template>
  <q-card id="processedPayments">
    <q-card-title>
      <div class="row">
        <div class="chart-title">
          <p>Gross Revenue</p>
          <span>{{delta | moneyFormat}}</span>
        </div>
        <q-btn class="processedPayments-btn down-btn btn-sm ml-auto mr-8 square"
               @click="download()"
               icon="mdi-file-document-outline"></q-btn>
        <q-btn class="processedPayments-btn report-btn btn-sm square"
               @click="viewReport()"
               icon="mdi-chart-line"></q-btn>
      </div>
    </q-card-title>
    <q-card-main class="chart-item">
      <div id="processedPaymentsChart"></div>
      <div v-if="visible"
           class="loading">
        <q-spinner-hourglass color="primary"
                             size="3em"
                             :thickness="2" />
      </div>
    </q-card-main>
  </q-card>
</template>

<script>
import echarts from 'echarts'
import $ from 'jquery'
import { request, EventHandlerMixin, moneyFormat, notify } from '../../../common'
import html2canvas from 'html2canvas'

export default {
  components: {
  },
  props: {
    'value': {
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  watch: {
    value () {
      this.getData()
    }
  },
  mixins: [
    EventHandlerMixin('reload-processed-payments-chart', 'getData')
  ],
  data () {
    return {
      delta: 0,
      visible: false,
      grossData: [],
      chart: null,
      xAxis: [],
      series: [
        {
          name: 'ACH Push',
          type: 'line',
          color: '#ff4852',
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0, color: 'rgba(255, 72, 82, 1)'
                },
                {
                  offset: 0.7, color: 'rgba(255, 72, 82, 0.1)'
                },
                {
                  offset: 1, color: 'rgba(255, 255, 255, 0)'
                }
              ]
            }
          },
          data: []
        },
        {
          name: 'ACH Pull',
          type: 'line',
          color: '#00cf08',
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0, color: 'rgba(0, 208, 11, 1)'
                },
                {
                  offset: 0.7, color: 'rgba(0, 208, 11, 0.1)'
                },
                {
                  offset: 1, color: 'rgba(255, 255, 255, 0)'
                }
              ]
            }
          },
          data: []
        },
        {
          name: 'Gross Revenue',
          type: 'line',
          color: '#fcb72a',
          lineStyle: {
            type: 'dotted'
          },
          data: []
        }
      ]
    }
  },
  async mounted () {
    this.getData()
  },
  methods: {
    resize () {
      this.chart.resize()
    },
    viewReport () {
      this.$router.push(`/j/leaflink/payments`)
    },
    async getData () {
      this.visible = true
      console.log(this.value)
      const resp = await request(`/admin/leaflink/processedPaymentsChart`, 'get', { 'period': this.value })
      if (resp.success) {
        this.series[0].data = Object.values(resp.data.chartData.credit)
        this.series[1].data = Object.values(resp.data.chartData.debit)
        this.series[2].data = Object.values(resp.data.chartData.grossRevenue)

        this.grossData = resp.data.chartData.grossRevenue
        this.xAxis = resp.data.key
        this.delta = resp.data.balance
        this.initChart(resp.data)
      }
      this.visible = false
    },
    initChart (data) {
      this.chart = echarts.init(document.getElementById('processedPaymentsChart'), 'primary')
      let that = this
      let positionStr = ''
      let colorList = ['#ff4852', '#00cf08']
      let labelList = ['ACH Push', 'ACH Pull']
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          // alwaysShowContent: true,
          backgroundColor: '#ffffff',
          padding: 0,
          position: function (pos, params, dom, rect, size) {
            // 鼠标在左侧时 tooltip 显示到右侧，鼠标在右侧时 tooltip 显示到左侧。
            let obj = { top: pos[1] - 290 }
            if (pos[0] < size.viewSize[0] / 2) {
              obj['left'] = pos[0] - 50
              positionStr = 'left'
            } else {
              positionStr = 'right'
              obj['right'] = size.viewSize[0] - pos[0] - 50
            }
            return obj
          },
          formatter: function (params) {
            let endValue = that.chart.getOption().dataZoom[0].endValue
            let startValue = that.chart.getOption().dataZoom[0].startValue
            // console.log(params)
            // console.log(data)
            return '<div class="tooltip-area-' + positionStr + '" style="box-shadow: 0 5px 15px 0 rgba(28, 20, 70, 0.1);border-radius:20px;padding:30px;padding-top:16px;font-family: Poppins;font-size: 14px;"><p style="text-align:center;margin:0px;color:#231f20;font-weight: 600;">' + that.titleStr(params[0]['name']) + '</p>' +
              '<p style="text-align:center;margin:0px;color:#231f20;font-weight: 600;">Date ranges: ' + that.titleStr(that.xAxis[startValue], true) + ' ~ ' + that.titleStr(that.xAxis[endValue], true) + '</p>' +
              '<table style="min-width:500px;">' +
              '<tr style="color:#231f20;width:100%;">' +
              '<th style="text-align:left;font-weight: 600;">Payments Type</th><th style="text-align:left;"># of TXNs</th><th style="text-align:left;">Total ($)</th><th style="text-align:left;">Average ($)</th>' +
              '</tr>' +
              '<tr style="color:#414141">' +
              '<td style="color:' + colorList[0] + ';font-weight: 600;">' + labelList[0] + '</td><td>' + data.chartCount['credit'][params[0]['name']] + '</td><td>' + moneyFormat(data.chartData['credit'][params[0]['name']] * 100) + '</td><td>' + moneyFormat(data.chartAvg['credit'][params[0]['name']] * 100) + '</td>' +
              '</tr>' +
              '<tr style="color:#414141">' +
              '<td style="color:' + colorList[1] + ';font-weight: 600;">' + labelList[1] + '</td><td>' + data.chartCount['debit'][params[0]['name']] + '</td><td>' + moneyFormat(data.chartData['debit'][params[0]['name']] * 100) + '</td><td>' + moneyFormat(data.chartAvg['debit'][params[0]['name']] * 100) + '</td>' +
              '</tr>' +
              '<tr style="color:#231f20;font-weight: 600;">' +
              '<td>Total</td><td>' + (data.chartCount['credit'][params[0]['name']] + data.chartCount['debit'][params[0]['name']]) + '</td>' +
              '<td>' + moneyFormat((data.chartData['credit'][params[0]['name']] + data.chartData['debit'][params[0]['name']]) * 100) +
              '</td>' +
              '<td>' + '</td>' +
              '</tr>' +
              '<tr style="color:#414141;font-weight: 600;">' + '<td><i style="background:#fcb72a;width:10px; height: 10px;border-radius:5px;display: inline-block;margin-right: 5px;"></i>Gross Revenue</td><td>' + moneyFormat(data.chartData['grossRevenue'][params[0]['name']] * 100) + '</td>' +
              '<td>' + '</td>' +
              '<td>' + '</td>' +
              '</tr>' +
              '</table>' +
              '</div>'
          }
        },
        legend: {
          bottom: '0px',
          icon: 'pin',
          data: ['ACH Push', 'ACH Pull', 'Gross Revenue']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '60px',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: this.xAxis
          }
        ],
        dataZoom: {
          bottom: '25px',
          fillerColor: 'rgba(26, 26, 67, 0.5)',
          handleStyle: {
            color: 'rgba(26, 26, 67, 0.5)'
          },
          moveHandleStyle: {
            color: 'rgba(26, 26, 67, 0.5)'
          }
        },
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: false
            }
          }
        ],
        series: this.series
      }
      // console.log(this.series)
      this.chart.setOption(option)
      this.chart.on('dataZoom', function (params) {
        // console.log(params)
        // console.log(that.xAxis[that.chart.getOption().dataZoom[0].endValue])
        // console.log(that.grossData)
        that.delta = that.grossData[that.xAxis[that.chart.getOption().dataZoom[0].endValue]] * 100
        // console.log(that.delta)
      })
      $(window).on('resize', this.resize)
    },
    download () {
      let target = null
      let userAgent = navigator.userAgent
      if (userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Chrome') === -1) {
        return notify('Please take a screenshot manually or use another browser to open the website to save the picture!', 'negative')
      }

      let name = 'processedPayments'
      target = document.getElementById('processedPayments')
      this.$q.loading.show()
      const targetCss = window.getComputedStyle(target)

      const options = {
        scale: 2,
        width: parseInt(targetCss.width, 10),
        height: parseInt(targetCss.height, 10),
        y: target.offsetTop + 400
      }
      // console.log(options)
      html2canvas(target, options).then(canvas => {
        // canvas is the final rendered <canvas> element
        const imgData = canvas.toDataURL('image/jpeg')
        console.log(imgData)
        this.fileDownload(imgData, name)
        this.$q.loading.hide()
      })
    },
    fileDownload (downloadUrl, filename) {
      let aLink = document.createElement('a')
      aLink.style.display = 'none'
      aLink.href = downloadUrl
      aLink.download = `${filename}`
      // 触发点击-然后移除
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    },
    titleStr (str = '', range = false) {
      let title = str.split('-')
      let month = {
        '01': 'Jan.',
        '02': 'Feb.',
        '03': 'Mar.',
        '04': 'Apr.',
        '05': 'May',
        '06': 'Jun.',
        '07': 'Jul.',
        '08': 'Aug.',
        '09': 'Sept.',
        '10': 'Oct.',
        '11': 'Nov.',
        '12': 'Dec.'
      }
      let bigMonth = ['01', '03', '05', '07', '08', '10', '12']
      let smallMonth = ['04', '06', '09', '11']
      // let days = ['01', '02', '03', '21', '22', '23', '31']
      if (title.length === 3) {
        if (title[2].slice(-1) === '1' && title[2] !== '11') {
          return month[title[1]] + ' ' + title[2] + 'st, ' + title[0]
        } else if (title[2].slice(-1) === '2' && title[2] !== '12') {
          return month[title[1]] + ' ' + title[2] + 'nd, ' + title[0]
        } else if (title[2].slice(-1) === '3' && title[2] !== '13') {
          return month[title[1]] + ' ' + title[2] + 'rd, ' + title[0]
        } else {
          return month[title[1]] + ' ' + title[2] + 'th, ' + title[0]
        }
      } else if (title.length === 2) {
        if (range) {
          return this.value === 'week' ? 'Week ' + title[1] + ', ' + title[0] : month[title[1]] + ', ' + title[0]
        } else {
          let end = '29th'
          if (bigMonth.indexOf(title[1]) !== -1) {
            end = '31st'
          } else if (smallMonth.indexOf(title[1]) !== -1) {
            end = '30th'
          } else if (title[0] % 4) {
            end = '28th'
          }
          return this.value === 'week' ? 'Week ' + title[1] + ' Sun., ' + title[0] + ' - ' + 'Week ' + title[1] + ' Sat., ' + title[0] : month[title[1]] + ' ' + '01st, ' + title[0] + ' - ' + month[title[1]] + ' ' + end + ', ' + title[0]
        }
      } else if (title.length === 1) {
        if (range) {
          return title[0]
        } else {
          return month['01'] + ' 01st, ' + title[0] + ' - ' + month['12'] + '31st, ' + title[0]
        }
      }
    }
  }
}
</script>
<style lang="scss">
#processedPayments {
  margin: 0 !important;
  .processedPayments-btn {
    max-height: 32px !important;
    color: #fff;
  }
  .report-btn {
    background: #1b1443;
  }
  .down-btn {
    background: #00becb;
  }
  .chart-title {
    p {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
    span {
      font-size: 28px;
      font-weight: 600;
    }
  }
  .manual-load-big-btn {
    background: #00d993;
    color: #fff;
    max-height: 38px;
  }

  #processedPaymentsChart {
    height: 400px;
  }
  .chart-item {
    position: relative;
  }
  .loading {
    height: 400px;
    line-height: 400px;
    width: calc(100% - 32px);
    text-align: center;
    position: absolute;
    top: 0;
    z-index: 100;
    background: rgba($color: #000000, $alpha: 0.1);
  }
}
</style>
