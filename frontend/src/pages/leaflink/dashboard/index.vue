<template>
  <q-page class="leaflink_dashboard_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="ACH Program"
                  :options="cardPrograms"></q-select>
        <!-- <q-select v-model="selectedBank"
                  class="dense"
                  stack-label="Bank"
                  :options="bank"></q-select> -->
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-2-5  col-xs-6"
             v-for="(n, i) in numbers"
             :key="i">
          <q-card>
            <q-card-title>
              <span>{{n.title}}</span>
            </q-card-title>
            <q-card-main>
              <div v-if="n.title !== 'Linked Accounts'"
                   class="font-22 heavy">{{ n.value | moneyFormat}}</div>
              <div v-else
                   class="font-22 heavy">{{ n.value}}</div>
              <div class="font-14 item-count mb-10">{{ n.count }} {{n.sub}}</div>
              <a :href="n.url"
                 @click="viewReport(n.title)"
                 class="link bold font-12">View Report <i class="mdi mdi-arrow-right"></i></a>
            </q-card-main>
          </q-card>
        </div>
        <!-- <div class="col-sm-3">
          <q-card>
            <q-card-title>
              <span>Total Transactions</span>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy mb-10">{{ 0 | moneyFormat}}</div>
              <a href="/j/leaflink/payments"
                 class="link bold font-12">View Report <i class="mdi mdi-arrow-right"></i></a>
            </q-card-main>
          </q-card>
        </div> -->
      </div>
      <div class="row">
        <div class="col-8">
          <q-card>
            <ProcessedPayments v-model="chartDateRange"></ProcessedPayments>
          </q-card>
        </div>
        <div class="col-4">
          <q-card>
            <Accounts v-model="chartDateRange"></Accounts>
          </q-card>
        </div>
        <div class="col-12">
          <q-card>
            <PendingProcessingQueue v-model="chartDateRange"></PendingProcessingQueue>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script>
import LeaflinkPageMixin from '../../../mixins/leaflink/LeaflinkPageMixin'
import ProcessedPayments from './../common/processedPayments'
import Accounts from './../common/accounts'
import PendingProcessingQueue from './../common/pendingProcessingQueue'
import { request } from '../../../common'

export default {
  name: 'LeaflinkDashboard',
  mixins: [
    LeaflinkPageMixin
  ],
  components: {
    ProcessedPayments,
    Accounts,
    PendingProcessingQueue
  },
  data () {
    return {
      title: 'ACH Dashboard',
      dateRange: 'all',
      chartDateRange: 'all',
      numbers: [
        { title: 'Total Transactions', sub: 'Transactions', count: 0, value: 0, url: 'javascript:' },
        { title: 'ACH Push', sub: 'ACH Push', count: 0, value: 0, url: 'javascript:' },
        { title: 'ACH Pull', sub: 'ACH Pull', count: 0, value: 0, url: 'javascript:' },
        { title: 'Linked Accounts', sub: '', count: null, value: 0, url: 'javascript:' },
        { title: 'Pending Payments', sub: '', count: 0, value: 0, url: 'javascript:' }
      ]
    }
  },
  methods: {
    async reload () {
      this.$q.loading.show()
      let data = {}
      if (this.$refs && this.$refs.dateRangeFilter) {
        data = this.$refs.dateRangeFilter.params()
      }
      this.chartDateRange = data.period
      const res = await request('/admin/leaflink/dashboard/static', 'get', data)
      if (res.success) {
        this.numbers[0].value = res.data.total
        this.numbers[0].count = res.data.totalCount
        this.numbers[1].value = res.data.credits
        this.numbers[1].count = res.data.creditsCount
        this.numbers[2].value = res.data.debits
        this.numbers[2].count = res.data.debitsCount
        this.numbers[3].value = res.data.linkedAccount
        this.numbers[4].value = res.data.pending
        this.numbers[4].count = res.data.pendingCount
      }
      this.$q.loading.hide()
    },
    viewReport (type) {
      if (type === 'Total Transactions' || type === 'ACH Push' || type === 'ACH Pull') {
        this.$router.push(`/j/leaflink/payments`)
      }
      if (type === 'Linked Accounts') {
        this.$router.push(`/j/leaflink/accounts`)
      }
      if (type === 'Pending Payments') {
        this.$router.push(`/j/leaflink/pending`)
      }
    }
  },
  async mounted () {
    this.reload()
  }
}
</script>
<style lang="scss">
.leaflink_dashboard_page {
  .q-card {
    margin: 10px;
    .item-count {
      color: #797396;
    }
  }
}
</style>
