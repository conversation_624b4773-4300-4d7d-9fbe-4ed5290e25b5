<template>
  <q-card class="wide-card">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :loading="loading"
               class="inner-table"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">API Call Log Report</div>
        </template>
        <template slot="top-right">
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>
          <q-btn icon="mdi-file-download-outline"
                 label="Export as CSV"
                 @click="download"
                 class="btn-sm mr-8 export-btn"
                 no-caps></q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field === 'Response Code'">
              <q-chip class="font-12 status-item"
                      :class="props.row['Response Code'] === '200' ? 'success-item' : 'failed-item'">
                {{ props.row['Response Code'] }}
              </q-chip>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>
      </q-table>
    </q-card-main>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-card>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { generateColumns } from '../../../common'

export default {
  name: 'leaflink-api-call',
  mixins: [
    ListPageMixin
  ],
  data () {
    return {
      title: 'Member Profile',
      requestUrl: `/admin/leaflink/api/list`,
      downloadUrl: `/admin/leaflink/api/export`,
      columns: generateColumns([
        'User', 'API Call', 'Response Code', 'Time of Call'
      ]),
      keyword: '',
      filterOptions: [
        {
          value: 'filter[l.response=]',
          label: 'Response Code',
          options: [
            { label: '200', value: '200' },
            { label: '409', value: '409' }
          ]
        }
      ],
      autoLoad: true
    }
  },
  methods: {
  }
}
</script>
<style lang="scss">
.failed-item {
  background: rgba($color: #ffc200, $alpha: 0.1) !important;
  color: #ffc200;
}
.success-item {
  background: rgba($color: #50b5ff, $alpha: 0.1) !important;
  color: #50b5ff;
  text-transform: capitalize;
}
</style>
