<template>
  <q-card class="wide-card">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :loading="loading"
               class="inner-table"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">Email Log Report</div>
        </template>
        <template slot="top-right">
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>
          <q-btn icon="mdi-file-download-outline"
                 label="Export as CSV"
                 @click="download"
                 class="btn-sm mr-8 export-btn"
                 no-caps></q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field === 'Send Status'">
              <q-chip class="font-12 status-item"
                      :class="statusClass(props.row['Send Status'])">
                {{ props.row['Send Status'] }}
              </q-chip>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>
      </q-table>
    </q-card-main>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-card>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { generateColumns } from '../../../common'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'leaflink-email-send',
  mixins: [
    ListPageMixin,
    FreezeColumnMixin
  ],
  data () {
    return {
      title: 'Member Profile',
      requestUrl: `/admin/leaflink/email/list`,
      downloadUrl: `/admin/leaflink/email/export`,
      columns: generateColumns([
        'ID', 'Name', 'Email Address', 'Send Date', 'Email Subject', 'Send Status'
      ]),
      keyword: '',
      filterOptions: [
        {
          value: 'filter[e.status=]',
          label: 'Send Status',
          options: [
            { label: 'Disabled', value: 'disabled' },
            { label: 'Pending', value: 'pending' },
            { label: 'Sent', value: 'sent' }
          ]
        },
        {
          value: 'filter[e.recipients]',
          label: 'Name'
        },
        {
          value: 'filter[e.recipients]',
          label: 'Email Address'
        }
      ],
      autoLoad: true
    }
  },
  methods: {
    statusClass (status) {
      const cls = []
      cls.push({
        'pending': 'status-pending',
        'sent': 'status-sent',
        'disabled': 'status-disabled'
      }[status] || status)

      return cls
    }
  }
}
</script>
<style lang="scss">
.status-pending {
  background: rgba($color: #50b5ff, $alpha: 0.1) !important;
  color: #50b5ff;
  text-transform: capitalize;
}
.status-disabled {
  background: rgba($color: #fb6969, $alpha: 0.1) !important;
  color: #fb6969;
  text-transform: capitalize;
}
.status-sent {
  background: rgba($color: #00d993, $alpha: 0.1) !important;
  color: #00d993;
  text-transform: capitalize;
}
</style>
