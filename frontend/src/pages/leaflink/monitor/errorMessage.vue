<template>
  <q-dialog class="leaflink__nacha__error__meaasge__index_page"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Error Log</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row mt--10 mb-5">
        <p class="title-desc col-12">Error Message</p>
        <p class="content-desc col-12">{{ entity['Error Message']}}</p>
      </div>
      <div class="row mt--40 mb-5">
        <p class="title-desc col-12">Failed Transactions</p>
        <p class="content-desc col-12">{{ entity['Error Id']}}</p>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn label="View Transactions"
               no-caps
               color="grey-3"
               class="view-transaction"
               @click="viewTransaction()" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'

export default {
  name: 'leaflink-nacha-error-meaasge-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
    }
  },
  computed: {
  },
  mounted () {
  },
  methods: {
    viewTransaction () {
      this._hide()
      // console.log(this.entity)
      localStorage.setItem('batchId', this.entity['id'])
      setTimeout(() => {
        // this.$router.push(`/j/leaflink/payments`)
        const { href } = this.$router.resolve({
          path: `/j/leaflink/payments`
        })
        window.open(href, '_blank')
      }, 500)
    }
  }
}
</script>

<style lang="scss">
.leaflink__nacha__error__meaasge__index_page {
  .modal-content {
    width: 300px;
    .modal-buttons {
      padding-top: 0;
    }
  }
  .view-transaction {
    font-size: 8px;
    font-weight: 600;
    background-color: #1b1443 !important;
    color: #ffffff !important;
  }
  .title-desc {
    font-size: 7px;
    font-weight: 600;
    color: #333333;
    margin: 0;
    text-align: left;
  }
  .content-desc {
    font-size: 7px;
    font-weight: 600;
    color: #333333;
    padding: 7.5px;
    border: solid 0.5px #e2e2eb;
    border-radius: 5px;
    margin-top: 10px;
    margin-bottom: 0;
    text-align: left;
  }
}
</style>
