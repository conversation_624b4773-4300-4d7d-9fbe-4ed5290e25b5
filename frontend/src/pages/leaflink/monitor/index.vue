<template>
  <q-page id="leaflink__system_monitor__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="ACH Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img"><img src="/static/leaflink/img/send_icon.svg"></span>
                <div class="column">
                  <div class="value">{{  last.apiCall || 'No Data' }}
                  </div>
                  <div class="description">Last API Call</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img initial-item"><img src="/static/leaflink/img/send_icon.svg"></span>
                <div class="column">
                  <div class="value">{{ last.emailLog || 'No Data' }}
                  </div>
                  <div class="description">Last Email Sent</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <ApiReport></ApiReport>

      <EmaliReport></EmaliReport>
    </div>
  </q-page>
</template>

<script>
import { EventHandlerMixin, request } from '../../../common'
import LeaflinkPageMixin from '../../../mixins/leaflink/LeaflinkPageMixin'
import ApiReport from './api'
import EmaliReport from './email'

export default {
  name: 'leaflink-system-monitor',
  mixins: [
    LeaflinkPageMixin,
    EventHandlerMixin('leaflink-api-call'),
    EventHandlerMixin('eaflink-email-send')
  ],
  components: {
    ApiReport,
    EmaliReport
  },
  data () {
    return {
      title: 'System Monitor',
      last: {
        apiCall: '',
        emailLog: ''
      }
    }
  },
  async mounted () {
    this.reload()
  },
  methods: {
    async reload () {
      this.$q.loading.show()
      const res = await request('/admin/leaflink/system/monitor', 'get')
      if (res.success) {
        this.last = res.data
      }
      this.$q.loading.hide()
    }
  }
}
</script>
<style lang="scss">
#leaflink__system_monitor__index_page {
  .top-statics .row {
    align-items: center;
  }
  .icon-img {
    padding: 8px;
    background: rgba($color: #1c1446, $alpha: 0.1);
    display: flex;
    border-radius: 5px;
  }
  .export-btn {
    background: #00becb;
    color: #fff;
  }
  .wide-card {
    margin-top: 20px;
    .q-card-container {
      padding: 0;
      padding-bottom: 16px;
    }
  }
}
</style>
