<template>
  <q-page id="leaflink__nacha__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="ACH Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img"><img src="/static/jake/icons/leaflink_payments.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.lastSend || 0 }}
                  </div>
                  <div class="description">Last Nacha File Sent</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>

        <div class="col-md-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img"><img src="/static/jake/icons/leaflink_bank.svg"></span>
                <div class="column">
                  <div class="value">ACH Nacha Limits</div>
                  <div class="description">Daily DR: {{ quick.nachaDebitCount_Daily | moneyFormat }} / {{ quick.nachaDebitLimit_Daily | moneyFormat }}</div>
                  <div class="description">Daily CR: {{ quick.nachaCreditCount_Daily | moneyFormat }} / {{ quick.nachaCreditLimit_Daily | moneyFormat }}</div>
                  <div class="description">Weekly DR: {{ quick.nachaDebitCount_Weekly | moneyFormat }} / {{ quick.nachaDebitLimit_Weekly | moneyFormat }}</div>
                  <div class="description">Weekly CR: {{ quick.nachaCreditCount_Weekly | moneyFormat }} /  {{ quick.nachaCreditLimit_Weekly | moneyFormat }}</div>
                  <div class="description">Monthly DR: {{ quick.nachaDebitCount_Monthly | moneyFormat }} / {{ quick.nachaDebitLimit_Monthly | moneyFormat }}</div>
                  <div class="description">Monthly CR: {{ quick.nachaCreditCount_Monthly | moneyFormat }} / {{ quick.nachaCreditLimit_Monthly | moneyFormat }}</div>
                  </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-md-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img"><img src="/static/jake/icons/leaflink_bank.svg"></span>
                <div class="column">
                  <div class="value">B2B Nacha Limits</div>
                  <div class="description">Daily DR: {{ quick.b2bNachaDebitCount_Daily | moneyFormat }} / {{ quick.b2bNachaDebitLimit_Daily | moneyFormat }}</div>
                  <div class="description">Daily CR: {{ quick.b2bNachaCreditCount_Daily | moneyFormat }} / {{ quick.b2bNachaCreditLimit_Daily | moneyFormat }}</div>
                  <div class="description">Weekly DR: {{ quick.b2bNachaDebitCount_Weekly | moneyFormat }} / {{ quick.b2bNachaDebitLimit_Weekly | moneyFormat }}</div>
                  <div class="description">Weekly CR: {{ quick.b2bNachaCreditCount_Weekly | moneyFormat }} /  {{ quick.b2bNachaCreditLimit_Weekly | moneyFormat }}</div>
                  <div class="description">Monthly DR: {{ quick.b2bNachaDebitCount_Monthly | moneyFormat }} / {{ quick.b2bNachaDebitLimit_Monthly | moneyFormat }}</div>
                  <div class="description">Monthly CR: {{ quick.b2bNachaCreditCount_Monthly | moneyFormat }} / {{ quick.b2bNachaCreditLimit_Monthly | moneyFormat }}</div>
                  </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Nacha File Report</strong>
        </template>
        <template slot="top-right">
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn icon="mdi-file-download-outline"
                 label="Export as CSV"
                 @click="download"
                 class="btn-sm mr-8 export-btn"
                 no-caps></q-btn>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Transaction Count'">
              {{ _.get(props.row, col.field) }}<span @click="viewTransaction(props.row)"
                    class="view-transaction">View Transactions →</span>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn v-if="props.row['Status'] !== 'sent' && props.row['Status'] !== 'processing'"
                     label="View Error"
                     @click="viewError(props.row)"
                     class="btn-sm mr-8 view-error-btn"
                     no-caps></q-btn>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <ErrorMessage></ErrorMessage>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import LeaflinkPageMixin from '../../../mixins/leaflink/LeaflinkPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import ErrorMessage from './errorMessage'

export default {
  name: 'nacha-monitor',
  mixins: [
    LeaflinkPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-nacha-monitor')
  ],
  components: {
    ErrorMessage
  },
  data () {
    return {
      title: 'Nacha File Monitor',
      requestUrl: `/admin/leaflink/nacha/list`,
      downloadUrl: `/admin/leaflink/nacha/export`,
      keyword: '',
      dateRange: 'all',
      columns: generateColumns([
        'File Name', 'Send Date', 'Transaction Count', 'Status', 'Action'
      ], [], {
        'File Name': 'achb.batchFileName',
        'Send Date': 'achb.sentTime',
        'Transaction Count': 'achb.transactionCount'
      }),
      filterOptions: [
        {
          value: 'filter[achb.batchFileName]',
          label: 'File Name'
        },
        {
          value: 'filter[achb.batchStatus=]',
          label: 'Status',
          options: [
            { label: 'Processing', value: 'Processing' },
            { label: 'Settled', value: 'Settled' },
            { label: 'Failed', value: 'Failed' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    viewError (row) {
      this.$root.$emit('show-leaflink-nacha-error-meaasge-dialog', row)
    },
    statusClass (status) {
      const cls = []
      cls.push({
        'processing': 'status-processing',
        'sent': 'status-settled',
        'failed': 'status-failed'
      }[status] || status)

      return cls
    },
    viewTransaction (row) {
      const { href } = this.$router.resolve({
        path: `/j/leaflink/payments`
      })
      localStorage.setItem('batchId', row['id'])
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss">
#leaflink__nacha__index_page {
  .icon-img {
    width: 24px;
    height: 24px;
    display: flex;
    background: rgba($color: #000000, $alpha: 0.1);
    border-radius: 5px;
    img {
      width: 18px;
      max-width: 100%;
      margin: 0 auto;
    }
  }
  .export-btn {
    background: #00becb;
    color: #fff;
  }
  .view-error-btn {
    color: #fafafb;
    font-size: 6px;
    font-weight: 600;
    border-radius: 5px;
    background-color: #92929d;
  }
  .view-transaction {
    font-size: 7px;
    font-weight: 600;
    color: #00becb;
    margin-left: 10px;
    cursor: pointer;
  }
  .status-processing {
    background-color: rgba($color: #ffc300, $alpha: 0.1);
    color: #ffc300;
    text-transform: capitalize;
  }
  .status-failed {
    background-color: rgba($color: #ff4852, $alpha: 0.1);
    color: #ff4852;
    text-transform: capitalize;
  }
  .status-settled {
    background-color: rgba($color: #00d993, $alpha: 0.1);
    color: #00d993;
    text-transform: capitalize;
  }
}
</style>
