<template>
  <q-page id="leaflink__payments__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="ACH Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img"><img src="/static/jake/icons/leaflink_payments.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}
                  </div>
                  <div class="description">Payments Processed</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon class="credit-item"
                        name="mdi-arrow-down"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.credit || 0 | moneyFormat }}
                  </div>
                  <div class="description">ACH Push</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon class="debit-item"
                        name="mdi-arrow-up"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.debit || 0 | moneyFormat}}
                  </div>
                  <div class="description">ACH Pull</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon class="gross-item"
                        name="mdi-equalizer-outline"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.revenue || 0 | moneyFormat }}
                  </div>
                  <div class="description">Gross Revenue</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Payments Report</strong>
        </template>
        <template slot="top-right">
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn icon="mdi-file-download-outline"
                 label="Export as CSV"
                 @click="download"
                 class="btn-sm mr-8 export-btn"
                 no-caps></q-btn>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                <span v-if="props.row['Status'] === 'received' || props.row['Status'] ==='processing' ">
                  Pending</span>
                <span v-else-if="props.row['Status'] ==='returned' ">
                  Invalid Bank</span>
                <span v-else-if="props.row['Status'] ==='questionable' ">Errored or Failed</span>
                <span v-else>{{props.row['Status']}}</span>
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Payment Amount'">
              {{ props.row['Payment Amount'] | moneyFormat}}
            </template>
            <!-- <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="view(props.row)">
                    <q-item-main>View</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Status'] === 'questionable'"
                          v-close-overlay
                          @click.native="resent(props.row)">
                    <q-item-main>Resend</q-item-main>
                  </q-item>
                  <q-item v-else-if="props.row['Status'] === 'returned'"
                          v-close-overlay
                          @click.native="email(props.row)">
                    <q-item-main>Email</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template> -->
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import LeaflinkPageMixin from '../../../mixins/leaflink/LeaflinkPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'leaflink-payments',
  mixins: [
    LeaflinkPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-leaflink-payments')
  ],
  components: {
  },
  data () {
    return {
      title: 'ACH Payments',
      requestUrl: `/admin/leaflink/payments/list`,
      downloadUrl: `/admin/leaflink/payments/export`,
      keyword: '',
      dateRange: 'all',
      columns: generateColumns([
        'Bank ID', 'Company Name', 'Institution',
        'ACH Batch ID', 'Created Date', 'Send Date',
        'Trx Type', 'Payment Amount', 'Return Date', 'Status' //, 'Actions'
      ], [], {
        'Bank ID': 'ach.bankAccountId',
        // 'Company Name': 'c.companyName',
        'ACH Batch ID': 'ach.batchId',
        'Created Date': 'uct.createdAt',
        'Send Date': 'ach.createdAt',
        'Payment Amount': 'uct.txnAmount'
      }),
      filterOptions: [
        {
          value: 'filter[ach.bankAccountId]',
          label: 'Bank ID'
        },
        // {
        //   value: 'filter[c.companyName]',
        //   label: 'Company Name'
        // },
        {
          value: 'filter[ach.batchId=]',
          label: 'ACH Batch ID'
        },
        {
          value: 'filter[uct.tranCode=]',
          label: 'Trx type',
          options: [
            { label: 'Debit', value: 'debit' },
            { label: 'Credit', value: 'credit' }
          ]
        },
        {
          value: 'tranStatus',
          label: 'Status',
          options: [
            { label: 'Canceled', value: 'canceled' },
            { label: 'Settled', value: 'settled' },
            { label: 'Sent', value: 'sent' },
            { label: 'Processing', value: 'processing' },
            { label: 'Error', value: 'questionable' },
            { label: 'Returned', value: 'returned' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 1
    }
  },
  mounted () {
    if (localStorage.getItem('batchId')) {
      this.filters.push(
        {
          'predicate': '=',
          'field': 'filter[ach.batchId=]',
          'label': 'ACH Batch ID',
          'value': localStorage.getItem('batchId')
        }
      )
      localStorage.setItem('batchId', '')
    }
    // this.reload()
  },
  methods: {
    view (row) {
    },
    email (row) {
    },
    resent (row) {
    },
    statusClass (status) {
      const cls = []
      cls.push({
        'processing': 'status-processing',
        'received': 'status-processing',
        'returned': 'status-returned',
        'questionable': 'status-error',
        'canceled': 'status-canceled',
        'sent': 'status-sent',
        'settled': 'status-settled'
      }[status] || status)

      return cls
    }
  }
}
</script>
<style lang="scss">
#leaflink__payments__index_page {
  .icon-img {
    width: 24px;
    height: 24px;
    display: flex;
    background: rgba($color: #000000, $alpha: 0.1);
    border-radius: 5px;
    img {
      width: 18px;
      max-width: 100%;
      margin: 0 auto;
    }
  }
  .credit-item,
  .status-settled {
    background: rgba($color: #3dd598, $alpha: 0.1) !important;
    color: #3dd598;
    text-transform: capitalize;
  }

  .status-error {
    background: rgba($color: #ffc200, $alpha: 0.1) !important;
    color: #ffc200;
  }
  .gross-item,
  .status-sent {
    background: rgba($color: #50b5ff, $alpha: 0.1) !important;
    color: #50b5ff;
    text-transform: capitalize;
  }
  .debit-item,
  .status-returned {
    background-color: rgba($color: #ff4852, $alpha: 0.1);
    color: #ff4852 !important;
  }
  .status-processing {
    background: rgba($color: #a461d8, $alpha: 0.1) !important;
    color: #a461d8;
  }
  .status-canceled {
    background-color: rgba($color: #000000, $alpha: 0.1);
    color: #000000 !important;
    text-transform: capitalize;
  }
  .export-btn {
    background: #00becb;
    color: #fff;
  }
}
</style>
