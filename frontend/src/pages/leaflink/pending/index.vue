<template>
  <q-page id="leaflink__pending__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="ACH Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img processing-item"><img src="/static/jake/icons/leaflink_pending.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.pending || 0 | moneyFormat }}
                  </div>
                  <div class="description">Processing Payments ($)</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img bank-item"><img src="/static/jake/icons/leaflink_bank.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.return || 0 | moneyFormat }}
                  </div>
                  <div class="description">Bank Returned Transactions ($)</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <span class="icon-img forbidden-item"><img src="/static/jake/icons/leaflink_forbidden.svg"></span>
                <div class="column">
                  <div class="value">{{ quick.error || 0 | moneyFormat}}
                  </div>
                  <div class="description">Error Payments ($)</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }} Report</strong>
        </template>
        <template slot="top-right">
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn icon="mdi-file-download-outline"
                 label="Export as CSV"
                 @click="download"
                 class="btn-sm mr-8 export-btn"
                 no-caps></q-btn>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                <span v-if="props.row['Status'] === 'received' || props.row['Status'] ==='processing' ">
                  Pending</span>
                <span v-else-if="props.row['Status'] ==='returned' ">
                  Invalid Bank</span>
                <span v-else>Errored or Failed</span>
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Voice Total'">
              {{ props.row['Voice Total'] | moneyFormat}}
            </template>
            <!-- <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-if="props.row['Status'] === 'questionable'"
                          v-close-overlay
                          @click.native="email(props.row)">
                    <q-item-main>Email</q-item-main>
                  </q-item>
                  <q-item v-else-if="props.row['Status'] === 'returned'"
                          v-close-overlay
                          @click.native="resent(props.row)">
                    <q-item-main>Email</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template> -->
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import LeaflinkPageMixin from '../../../mixins/leaflink/LeaflinkPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'leaflink-pending',
  mixins: [
    LeaflinkPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-leaflink-pending')
  ],
  components: {
  },
  data () {
    return {
      title: 'Pending ACH Queue',
      requestUrl: `/admin/leaflink/pending/list`,
      downloadUrl: `/admin/leaflink/pending/export`,
      keyword: '',
      dateRange: 'all',
      columns: generateColumns([
        'Bank Account ID', 'Company Name', 'Institution',
        'ACH Batch ID', 'Creat Date', 'Send Date',
        'Trx Type', 'Voice Total', 'Status', 'Return Date' //, 'Actions'
      ], [], {
        'Bank Account ID': 'ach.bankAccountId',
        // 'Company Name': 'c.companyName',
        'ACH Batch ID': 'ach.batchId',
        'Creat Date': 'uct.createdAt',
        'Send Date': 'ach.createdAt',
        'Trx Type': 'uct.tranCode',
        'Voice Total': 'uct.txnAmount',
        'Status': 'ach.tranStatus'
      }),
      filterOptions: [
        {
          value: 'filter[ach.bankAccountId]',
          label: 'Bank Account ID'
        },
        // {
        //   value: 'filter[c.companyName]',
        //   label: 'Company Name'
        // },
        {
          value: 'filter[ach.batchId]',
          label: 'ACH Batch ID'
        },
        {
          value: 'filter[uct.tranCode=]',
          label: 'Trx type',
          options: [
            { label: 'Debit', value: 'debit' },
            { label: 'Credit', value: 'credit' }
          ]
        },
        {
          value: 'tranStatus',
          label: 'Status',
          options: [
            { label: 'Processing', value: 'processing' },
            { label: 'Error', value: 'questionable' },
            { label: 'Returned', value: 'returned' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 1
    }
  },
  methods: {
    resent (row) {
    },
    email (row) {
    },
    statusClass (status) {
      const cls = []
      cls.push({
        'returned': 'status-return',
        'questionable': 'status-error',
        'processing': 'status-pending',
        'received': 'status-pending'
      }[status] || status)

      return cls
    }
  }
}
</script>
<style lang="scss">
#leaflink__pending__index_page {
  .icon-img {
    width: 24px;
    height: 24px;
    display: flex;
    background: rgba($color: #000000, $alpha: 0.1);
    border-radius: 5px;
    img {
      width: 18px;
      max-width: 100%;
      margin: 0 auto;
    }
  }
  .processing-item {
    background: rgba($color: #0064ff, $alpha: 0.1) !important;
    color: #3dd598;
    img {
      filter: invert(0.5) sepia(1) saturate(5) hue-rotate(180deg);
    }
  }
  .bank-item {
    background: rgba($color: #ff4852, $alpha: 0.1) !important;
    color: #ff4852;
    img {
      filter: invert(0.5) sepia(1) saturate(5) hue-rotate(-20deg);
    }
  }
  .forbidden-item {
    background: rgba($color: #ffc200, $alpha: 0.1) !important;
    color: #50b5ff;
    img {
      filter: invert(0.5) sepia(1) saturate(5) hue-rotate(10deg);
    }
  }
  .export-btn {
    background: #00becb;
    color: #fff;
  }
  .q-chip.status-pending {
    background-color: rgba($color: #a461d8, $alpha: 0.1);
    color: #a461d8 !important;
  }
  .q-chip.status-return {
    background-color: rgba($color: #ff4852, $alpha: 0.1);
    color: #ff4852 !important;
  }
  .q-chip.status-error {
    background-color: rgba($color: #ffc200, $alpha: 0.1);
    color: #ffc200 !important;
  }
}
</style>
