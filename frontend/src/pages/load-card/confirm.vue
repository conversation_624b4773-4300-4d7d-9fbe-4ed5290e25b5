<template>
  <q-page :padding="true" class="q-px-md" id="load-card-confirm-page">
    <div class="q-subheading q-mb-md text-weight-bold">Payment Details</div>
    <div class="row">
      <div class="col-6">
        <div class="info-box">
          <div class="info-box-title">Payment option</div>
          <div class="info-box-body">
            <img :src="method.artwork" :alt="method.name">
          </div>
        </div>
      </div>
      <div class="col-6 q-pl-xs">
        <div class="info-box">
          <div class="info-box-title">Currency</div>
          <div class="info-box-body currency-info">
            {{ method.payAmount.currency }}
          </div>
        </div>
      </div>
    </div>

    <div class="q-subheading q-my-md text-weight-bold">Purchase Summary</div>
    <div class="row purchase-summary">
      <div :class="summaryColClass">
        <div class="info-box">
          <div class="info-box-title">Load amount</div>
          <div class="info-box-body">
            <Number :value="quote.loadAmount.amount | moneyMajorAmount(quote.loadAmount.currency)"
                    :prefix="quote.loadAmount.symbol"></Number>
          </div>
        </div>
      </div>
      <div :class="summaryColClass">
        <div class="info-box">
          <div class="info-box-title">Load fee</div>
          <div class="info-box-body">
            <Number :value="quote.loadFee.amount | moneyMajorAmount(quote.loadFee.currency)"
                    :prefix="quote.loadFee.symbol"></Number>
          </div>
        </div>
      </div>
      <div :class="summaryColClass">
        <div class="info-box">
          <div class="info-box-title">Membership fee</div>
          <div class="info-box-body">
            <Number :value="quote.membershipFee.amount | moneyMajorAmount(quote.membershipFee.currency)"
                    :prefix="quote.membershipFee.symbol"></Number>
          </div>
        </div>
      </div>
      <div :class="summaryColClass" v-if="discount">
        <div class="info-box text-positive">
          <div class="info-box-title">Discount</div>
          <div class="info-box-body">
            <Number :value="discount | moneyMajorAmount(quote.currency)"
                    :prefix="quote.membershipFee.symbol"></Number>
          </div>
        </div>
      </div>

      <div class="info-box q-mt-md other-fee" v-if="quote.otherFees.length">
        <div class="info-box-title">Other fees</div>
        <div class="info-box-body">
          <div v-for="(f, i) in quote.otherFees" :key="i">
            {{ f.type }}: <Number :value="f.amount | moneyMajorAmount(f.currency)"
                                  :prefix="f.coin.symbol"></Number>
          </div>
        </div>
      </div>
    </div>

    <div class="q-subheading q-my-md text-weight-bold" v-if="needInput">Required Information</div>
    <q-field :error="$v.issuer.$error" label="Bank" v-if="quote.method === 'iDeal'"
             class="full-width">
      <q-select v-model="issuer" @change="$v.issuer.$touch"
                placeholder="Choose one of the issuers"
                :options="issuers"/>
    </q-field>

    <q-field :error="$v.state.$error" v-if="quote.method === 'BrazilPayBoleto'"
             class="full-width q-mb-md">
      <q-input v-model="state" @change="$v.state.$touch"
               float-label="State/Region (2 letter state code)"></q-input>
    </q-field>
    <q-field :error="$v.documentId.$error" v-if="quote.method === 'BrazilPayBoleto'"
             class="full-width">
      <q-input v-model="documentId" @change="$v.documentId.$touch" float-label="CPF/CNPJ"
               placeholder="Document Id"></q-input>
    </q-field>

    <q-btn class="full-width uppercase q-mt-lg uppercase"
           @click="submit" :disabled="disabled"
           color="positive" label="Continue to Payment"></q-btn>
  </q-page>
</template>

<script>
import { mapState } from 'vuex'
import { required } from 'vuelidate/lib/validators'
import { request } from '../../common'
import _ from 'lodash'
import Number from '../../components/number.vue'

export default {
  components: {
    Number
  },
  data () {
    return {
      issuers: [],
      issuer: null,
      state: null,
      documentId: null
    }
  },
  validations: {
    issuer: {
      required
    },
    state: {
      required
    },
    documentId: {
      required
    }
  },
  computed: {
    ...mapState({
      userCard: state => state.UserCard,
      method: state => state.Load.method,
      quote: state => state.Load.quote,
      user: state => state.User
    }),
    discount () {
      return this.quote.loadFeeDiscount.amount +
        this.quote.membershipFeeDiscount.amount
    },
    summaryColClass () {
      const discount = this.discount
      return discount ? 'col-3' : 'col-4'
    },
    needInput () {
      return _.includes(['iDeal', 'BrazilPayBoleto'], this.quote.method)
    },
    disabled () {
      if (this.quote.method === 'iDeal') {
        return !this.issuer
      } else if (this.quote.method === 'BrazilPayBoleto') {
        return !this.state || !this.documentId
      }
      return false
    }
  },
  methods: {
    async submit () {
      if (this.disabled) {
        return
      }
      this.$q.loading.show()
      const resp = await request('/api/load/create-payment', 'post', {
        loadId: this.quote.id,
        issuerId: this.issuer,
        state: this.state,
        documentId: this.documentId
      })
      this.$q.loading.hide()
      const data = resp.data
      if (resp.success) {
        this.$store.commit('Load/update', {
          payment: data
        })
        this.$router.push(`/load-card/${this.userCard.id}/pay`)
      } else if (data && data.url === '/load-card') {
        this.$router.replace(`/load-card/${this.userCard.id}`)
      }
    }
  },
  async mounted () {
    if (this.quote.method === 'iDeal') {
      const resp = await request('/api/load-method/issuers', 'get', {
        method: this.method.id,
        currency: this.quote.currency,
        country: this.user.country.iso
      })
      if (resp.success) {
        this.issuers = resp.data.map(i => ({
          value: i.id,
          label: i.name
        }))
      }
    }
  }
}
</script>

<style lang="scss">
  #load-card-confirm-page {
    .info-box {

      .info-box-body {
        font-size: 18px;

        > img {
          height: 60px;
        }
      }

      .currency-info {
        height: 60px;
      }
    }

    .purchase-summary {
      > div[class^="col-"] {
        padding: 4px 0 0 4px;

        &:first-of-type {
          padding-left: 0;
        }
      }

      .info-box {
        height: 90px;

        &.other-fee {
          height: auto;
        }
      }
    }
  }
</style>
