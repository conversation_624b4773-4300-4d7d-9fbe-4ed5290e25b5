<template>
  <q-page :padding="true" class="q-px-md">
    <div class="q-subheading q-mb-lg text-weight-bold">Load / Top Up Card</div>
    <div class="row">
      <div class="col-5 relative-position">
        <Card :image="cardImage"
              :holder="userCard ? userCard.holder : null"
              :number="userCard ? userCard.pan : null"></Card>
      </div>
      <div class="col-7 q-pl-md flex flex-end">
        <q-field :helper="limit"
                 :error="$v.amount.$error"
                 :error-label="errorLabel"
                 class="full-width items-end">
          <q-input type="number" v-model="amount" :decimals="2"
                   @input="$v.amount.$touch"
                   :numeric-keyboard-toggle="true" float-label="Amount" />
        </q-field>
      </div>
    </div>
    <q-btn class="full-width uppercase q-mt-lg uppercase"
           :disabled="disabled" @click="submit"
           color="positive" label="Load Card">
      <q-spinner slot="loading"></q-spinner>
    </q-btn>
  </q-page>
</template>

<script>
import { mapState } from 'vuex'
import { required, minValue, maxValue } from 'vuelidate/lib/validators'
import { request, moneyFormat, moneyMajorAmount, moneyMinorAmount } from '../../common'
import Card from '../../components/card.vue'

export default {
  components: {
    Card
  },
  validations () {
    return {
      amount: {
        required,
        minValue: minValue(this.min),
        maxValue: maxValue(this.max),
        maxBalance: maxValue(this.maxBalance)
      }
    }
  },
  computed: {
    ...mapState({
      userCard: state => state.UserCard
    }),
    amount: {
      get () {
        return moneyMajorAmount(this.$store.state.Load.amount) || null
      },
      set (amount) {
        this.$store.commit('Load/update', {
          amount: moneyMinorAmount(amount)
        })
      }
    },
    card () {
      return this.userCard.card
    },
    cardImage () {
      return this.card ? this.card.artwork : ''
    },
    disabled () {
      return !this.cardImage || !this.amount || this.$v.$error
    },
    limit () {
      if (!this.card) {
        return ''
      }
      const min = moneyFormat(this.card.minLoadAmount)
      const max = moneyFormat(this.card.maxLoadAmount)
      return `${min} ~ ${max}`
    },
    min () {
      return this.card ? moneyMajorAmount(this.card.minLoadAmount) : 0
    },
    max () {
      return this.card ? moneyMajorAmount(this.card.maxLoadAmount) : 0
    },
    maxBalance () {
      return this.card ? moneyMajorAmount(this.card.maxBalance - this.userCard.balance) : 0
    },
    errorLabel () {
      if (!this.card) {
        return null
      }
      if (this.amount > this.maxBalance) {
        const limit = moneyFormat(this.card.maxBalance, 'USD')
        const balance = moneyFormat(this.userCard.balance, 'USD')
        return `Exceed max balance limit ${limit}. Current balance: ${balance}`
      }
      return null
    }
  },
  methods: {
    async submit () {
      if (this.disabled) {
        return
      }
      this.$q.loading.show()
      const resp = await request('/api/load/query-payment', 'post', {
        userCardId: this.userCard.id,
        amount: this.$store.state.Load.amount
      })
      this.$q.loading.hide()
      if (resp.success) {
        const data = resp.data
        this.$store.commit('Load/update', data)

        if (data.pendingExceed) {
          this.$q.dialog({
            title: 'Warning',
            message: `Your pending payments exceed the maximum card balance. Confirmed payments in excess
              of ${data.pendingExceed} will not be loaded to your card. Do you wish to continue?`,
            color: 'warning',
            cancel: true
          })
            .then(() => {
              this.next()
            })
            .catch(() => {})
        } else {
          this.next()
        }
      }
    },
    next () {
      this.$router.push(`/load-card/${this.userCard.id}/method`)
    }
  }
}
</script>

<style lang="scss">
</style>
