<template>
  <q-page :padding="true" class="q-px-md" id="load-card-page-method">
    <div class="q-subheading q-mb-md text-weight-bold">
      Card Load Payment of {{ load.amount | moneyFormat('USD') }} + Fee
    </div>
    <div class="q-body-1 q-mb-md text-weight-regular">
      Load fees differ per payment method. Click the payment methods below to view the load fee.
    </div>

    <div class="row gutter-xs">
      <div class="col-6 q-mb-sm" v-for="m in load.methods" :key="m.id">
        <div class="method-artwork"
             :class="{active: m === load.method, out: load.method && m !== load.method}"
             @click="select(m)">
          <img :src="m.artwork" :alt="m.name">
          <q-icon name="mdi-check-circle-outline" size="16px"
                  v-if="m === load.method"/>
        </div>
        <div class="row method-amount" :class="m.payAmounts.length > 1 ? 'multiple' : ''">
          <div class="method-number">
            <span class="q-caption text-grey">Pay</span>
            {{ m.payAmount.amount | moneyFormat(m.payAmount.currency) }}
          </div>
          <div class="method-currency">
            <i class="currency-flag" :class="flagClass(m.payAmount)"></i>
            <q-btn-dropdown :label="m.payAmount.currency" v-if="m.payAmounts.length > 1">
              <q-list link>
                <q-item v-for="p in m.payAmounts" :key="p.id" v-close-overlay
                        @click.native="selectAmount(m, p)">
                  <q-item-side>
                    <i class="currency-flag" :class="flagClass(p)"></i>
                  </q-item-side>
                  <q-item-main>{{ p.currency }}</q-item-main>
                </q-item>
              </q-list>
            </q-btn-dropdown>
            <span v-else>{{ m.payAmount.currency }}</span>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script>
import { mapState } from 'vuex'
import { request } from '../../common'
import _ from 'lodash'

export default {
  computed: {
    ...mapState({
      userCard: state => state.UserCard,
      load: state => state.Load
    })
  },
  methods: {
    flagClass (m) {
      return 'currency-flag-' + _.lowerCase(m.currency)
    },
    select (m) {
      const mm = this.load.method === m ? null : m
      this.$store.commit('Load/update', {
        method: mm
      })
      this.showNext()
    },
    showNext () {
      if (!this.load.method) {
        return
      }
      this.$q.dialog({
        title: 'Load fee',
        message: this.load.method.loadFee,
        position: 'bottom',
        ok: 'NEXT',
        color: 'positive'
      })
        .then(() => {
          this.submit()
        })
        .catch(() => {
          this.$store.commit('Load/update', {
            method: null
          })
        })
    },
    selectAmount (method, amount) {
      this.$store.commit('Load/selectAmount', {
        method,
        amount
      })
      this.$forceUpdate()
    },
    async submit () {
      this.$q.loading.show()
      const resp = await request('/api/load/quote-payment', 'post', {
        userCardId: this.userCard.id,
        amount: this.$store.state.Load.amount,
        partner: this.load.method.partner.id,
        method: this.load.method.id,
        currency: this.load.method.payAmount.currency
      })
      this.$q.loading.hide()
      if (resp.success) {
        const data = resp.data
        this.$store.commit('Load/update', {
          quote: data
        })
        this.$router.push(`/load-card/${this.userCard.id}/confirm`)
      }
    }
  },
  mounted () {
    this.showNext()
  }
}
</script>

<style lang="scss">
  #load-card-page-method {
    .method-artwork {
      padding: 5px;
      border-radius: 4px;
      border: 1px solid #e0e0e0;
      position: relative;
      width: 100%;
      overflow: hidden;
      justify-content: center;
      align-items: center;
      display: flex;
      padding-bottom: 30%;
      padding-top: 30%;

      &.active {
        border-color: #21BA45;

        .q-icon {
          position: absolute;
          right: 5px;
          top: 5px;
          color: #21BA45;
        }
      }

      &.out {
        opacity: .65;
      }

      img {
        position: absolute;
        max-width: calc(100% - 10px);
        max-height: calc(100% - 10px);
        height: 100%;
      }
    }
  }

  @media (max-width: 375px) {
    #load-card-page-method {
      .method-number {
        .q-caption {
          display: none;
        }
      }
    }
  }

  @media (max-width: 320px) {
    #load-card-page-method {
      .method-amount {
        padding-left: 3px;
      }
      .method-number {
        font-size: 12px;
      }
    }
  }
</style>
