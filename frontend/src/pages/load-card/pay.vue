<template>
  <q-page :padding="true" class="q-px-md" id="load-card-page-pay">
    <div class="q-subheading q-mb-md text-weight-bold">To be paid</div>
    <div class="row method-amount">
      <div class="method-number">
        {{ quote.payAmount.amount | moneyFormat(quote.payAmount.currency) }}
      </div>
      <div class="method-currency">
        <i class="currency-flag" :class="flagClass(quote.payAmount)"></i>
        <span>{{ quote.payAmount.currency }}</span>
      </div>
    </div>

    <q-btn v-if="payment.redirect" @click="openInNew"
      class="full-width q-my-lg" color="positive"
           label="CONTINUE TO PAY" icon="mdi-open-in-new"></q-btn>
    <div class="q-my-lg" v-if="payment.instruction">
      <div v-if="quote.method === 'Western Union'">
        Please use below payment instructions to perform a QuickPay transaction
        at a QuickPay enabled Western Union agent.
        For QuickPay locations:
        <a href="http://locations.westernunion.com" target="_blank">http://locations.westernunion.com</a>
        (filter for only QuickPay results).
      </div>
      <div class="q-subheading text-weight-bold" v-else>
        Please follow these instructions to transfer money:
      </div>
      <q-list class="q-mt-md">
        <q-item v-for="(is, i) in payment.instruction" :key="i">
          <q-item-side>
            <q-item-tile>{{ is.key }}</q-item-tile>
          </q-item-side>
          <q-item-main>
            <q-item-tile>{{ is.value }}</q-item-tile>
          </q-item-main>
        </q-item>
      </q-list>

      <q-btn class="full-width uppercase q-mt-lg uppercase"
             @click="submit"
             color="positive" label="CONTINUE"></q-btn>
    </div>
  </q-page>
</template>

<script>
import { mapState } from 'vuex'
import _ from 'lodash'
import { request, callNative } from '../../common'
import { NATIVE_CALL } from '../../const'

export default {
  computed: {
    ...mapState({
      userCard: state => state.UserCard,
      quote: state => state.Load.quote,
      payment: state => state.Load.payment
    })
  },
  methods: {
    flagClass (m) {
      return 'currency-flag-' + _.lowerCase(m.currency)
    },
    openInNew () {
      callNative(NATIVE_CALL.OPEN_IN_SAFARI, this.payment.redirect)

      this.$q.dialog({
        title: 'Pay',
        message: 'Please finish the payment in new window.',
        ok: 'Done',
        cancel: 'Later',
        preventClose: true
      }).then(() => {
        this.submit()
      }).catch(() => {
        this.submit()
      })
    },
    async submit () {
      this.$q.loading.show()
      const resp = await request('/api/load/payment-status', 'post', {
        loadId: this.quote.id
      })
      this.$q.loading.hide()
      const data = resp.data
      if (resp.success) {
        this.$store.commit('Load/update', {
          status: data
        })
        this.$router.push(`/load-card/${this.userCard.id}/status`)
      }
    }
  }
}
</script>

<style lang="scss">
  #load-card-page-pay {
    .q-list {
      overflow: auto;

      .q-item {
        padding: 4px 12px;
        min-height: 0;

        .q-item-side-left {
          width: 115px;
          text-align: right;
        }
      }
    }
  }
</style>
