<template>
  <q-page :padding="true" class="q-px-md" id="load-card-status-page">
    <div class="q-subheading q-mb-md text-weight-bold">Status</div>
    <div class="row">
      <div class="col-6">
        <div class="info-box">
          <div class="info-box-title">Current Status</div>
          <div class="info-box-body">
            <span class="status-label">{{ status.status }}</span>
          </div>
        </div>
      </div>
      <div class="col-6 q-pl-xs">
        <div class="info-box">
          <div class="info-box-title">Transaction Number</div>
          <div class="info-box-body">
            {{ status.transactionNo }}
          </div>
        </div>
      </div>
    </div>

    <q-btn class="full-width uppercase q-mt-lg uppercase"
           @click="submit"
           color="positive" label="Finish"></q-btn>
  </q-page>
</template>

<script>
import { mapState } from 'vuex'
import { callNative } from '../../common'
import { NATIVE_CALL } from '../../const'

export default {
  computed: {
    ...mapState({
      status: state => state.Load.status
    })
  },
  methods: {
    submit () {
      callNative(NATIVE_CALL.CLOSE_LOAD_CARD_PAGE)
    }
  }
}
</script>

<style lang="scss">
  #load-card-status-page {
    .status-label {
      color: #FF8D00;
      border: 1px solid #FF8D00;
      border-radius: 3px;
      padding: 3px 10px;
    }

    .info-box {
      height: 93px;
      justify-content: space-around;
    }
  }
</style>
