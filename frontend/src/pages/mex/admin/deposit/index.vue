<template>
  <q-page id="mex__employer_deposit__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3 col-12">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total deposit</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }}</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-if="props.row['Status'] == 'Pending'"
                          v-close-overlay
                          @click.native="cancel(props.row)">
                    <q-item-main>Cancel</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Status'] == 'Pending'"
                          v-close-overlay
                          @click.native="load(props.row)">
                    <q-item-main>Load</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, request } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'

export default {
  name: 'mex-employer-deposit',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-employer-deposits')
  ],
  data () {
    return {
      title: 'Deposits',
      requestUrl: `/admin/mex/employer/deposits/list`,
      downloadUrl: `/admin/mex/employer/deposits/export`,
      columns: generateColumns([
        'ID', 'Employer', 'Amount', 'Post Date', 'Trace #', 'Load Time', 'Status', 'Actions'
      ], [], []),
      filterOptions: [
        {
          value: 'filter[ed.id=]',
          label: 'Deposit ID'
        }, {
          value: 'filter[ed.traceId]',
          label: 'Trace #'
        }, {
          value: 'filter[u.status=]',
          label: 'Status',
          options: [
            { label: 'Pending', value: 'pending' },
            { label: 'Canceled', value: 'cancel' }
          ]
        }
      ],
      keyword: '',
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    statusClass (status) {
      const cls = []
      cls.push({
        'Pending': 'blue',
        'Completed': 'positive',
        'Cancelled': 'gray'
      }[status] || status)
      return cls
    },
    cancel (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to cancel this deposit?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request(`/admin/mex/employer/deposits/${row['ID']}/cancel`, 'post')
        this.loading = false
        if (resp.success) {
          this.reload()
        }
      }).catch(() => {})
    },
    load (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to load ' + row['Amount'] + ' to the employer ' + row['Employer'] + '?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request(`/admin/mex/employer/deposits/${row['ID']}/load`, 'post')
        this.loading = false
        if (resp.success) {
          this.reload()
        }
      }).catch(() => {})
    }
  }
}
</script>
