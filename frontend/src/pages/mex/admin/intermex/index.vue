<template>
  <q-page id="mex__transfer_admin_intermex_transfer__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div :class="summaryCol">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-comment-arrow-right-outline"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.total || 0 }}</div>
                  <div class="description">Number of Completed Transfers</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="summaryCol">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-usd"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.amount || 0 | moneyFormat }}</div>
                  <div class="description">Total Amount Transferred</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="summaryCol">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-chart-bar"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.average || 0 | moneyFormat }}</div>
                  <div class="description">Average Transfer Amount</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Transfer' && props.row[col.field]">
              <a :href="`#/j/mex/transfers/${props.row[col.field]}`"
                 class="mex-amount"
                 target="_blank">{{ _.get(props.row, col.field) }}</a>
            </template>
            <template v-else-if="col.field === 'Settlement' && props.row[col.field]">
              <a :href="`#/j/mex/fundings/${props.row[col.field]}`"
                 target="_blank">{{ _.get(props.row, col.field) }}</a>
            </template>
            <template v-else-if="['Transfer Amount', 'Transfer Amount Received', 'Transfer Fee', 'Transfer Cost',
                'Shared Revenue', 'Partner Revenue', 'Program Revenue', 'FX Processing Fee'].includes(col.field)">
              <div class="mex-amount">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else-if="['Transfer Exchange Rate'].includes(col.field)">
              <div class="mex-amount">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns, toSelectOptions } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
export default {
  name: 'mex-admin-intermex-history',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin
  ],
  data () {
    const columns = [
      'Create Date', 'Recipient Seen Code Date', 'Complete Date', 'Cancel Date', 'Member ID', 'First Name', 'Last Name',
      'Transfer ID', 'Processor', 'Recipient Full Name', 'Recipient Country',
      'Transfer Amount', 'Transfer Amount Received',
      'Transfer Exchange Rate', 'Intermex Exchange Rate', 'Transfer Method', 'Transfer Method Type',
      'Intermex ID', 'Transfer Fee', 'Transfer Cost',
      'Shared Revenue', 'Partner Revenue', 'Program Revenue'
    ]
    if (this.isMasterAdmin()) {
      columns.push('FX Processing Fee')
    }
    for (const col of ['Status', 'Action']) {
      columns.push(col)
    }
    return {
      title: 'Intermex Transfers',
      filtersUrl: `/admin/mex/intermex-transfers/filters`,
      requestUrl: `/admin/mex/intermex-transfers/list`,
      downloadUrl: `/admin/mex/intermex-transfers/export`,
      columns: generateColumns(columns, [
        'Transfer Exchange Rate', 'Transfer Amount', 'Transfer Amount Received',
        'Transfer Fee', 'Transfer Cost', 'Shared Revenue', 'Partner Revenue',
        'Program Revenue', 'FX Processing Fee', 'Transfer'
      ], {
        'Create Date': 't.createdAt',
        'Member ID': 's.id',
        'First Name': 's.firstName',
        'Last Name': 's.lastName',
        'Transfer ID': 't.id',
        'Recipient Full Name': 'CONCAT(u.firstName,u.lastName)'
      }),
      filterOptions: [
        {
          value: 'filter[t.id=]',
          label: 'Transfer ID'
        }, {
          value: 'filter[s.id=]',
          label: 'Member ID'
        }, {
          value: 'filter[s.firstName=]',
          label: 'Member First Name'
        }, {
          value: 'filter[s.lastName=]',
          label: 'Member Last Name'
        }, {
          value: 'filter[r.firstName=]',
          label: 'Recipient First Name'
        }, {
          value: 'filter[r.lastName=]',
          label: 'Recipient Last Name'
        }, {
          value: 'filter[e.id=]',
          label: 'Employer',
          options: [],
          source: 'employers'
        },
        {
          value: 'filter[t.status=]',
          label: 'Transfer Method',
          options: toSelectOptions([
            'Cash Pickup', 'Bank Transfer'
          ], true)
        }, {
          value: 'date_range',
          label: 'Create Date',
          range: [{
            value: 'range[t.createdAt][start]',
            type: 'date'
          }, {
            value: 'range[t.createdAt][end]',
            type: 'date'
          }]
        }
      ],
      freezeColumn: -1,
      autoLoad: true,
      keyword: ''
    }
  },
  computed: {
    statColClass () {
      return this.masterAdmin ? 'col-sm-2' : 'col-sm-2-5'
    }
  },
  methods: {
    statusClass (status) {
      return {
        'Pending': 'dark',
        'Confirmation': 'warning',
        'Queued': 'purple',
        'Processing': 'purple',
        'Created': 'blue',
        'Completed': 'positive',
        'Canceled': 'orange',
        'Error': 'negative',
        'Expired': 'pansy'
      }[status] || status
    }
  }
}
</script>
