<template>
  <q-page id="mex__transfer_fundings__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm mb-15 top-statics-row top-statics-row-lg">
        <div :class="statColClass"
             class="col-8"
             v-if="masterAdmin">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-alpha-r-box"
                        color="orange"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super nowrap">
                        BOTM.Program
                        <q-icon name="mdi-refresh"
                                color="positive"
                                @click.native="refreshBotm"
                                class="ml-5 pointer">
                          <q-tooltip>Refresh</q-tooltip>
                        </q-icon>
                      </small>
                      <div class="value font-mono">{{ quick.botmBalance | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">
                    Program Account Balance
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="statColClass"
             class="col-8">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-alpha-r-box" color="blue"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-3">
                      <small class="super">Count</small>
                      <div class="value font-mono">{{ quick.pendingTransferCount || 0 }}</div>
                    </div>
                    <div class="col-9">
                      <small class="super">
                        Amount
                        <q-icon name="mdi-refresh"
                                color="positive"
                                @click.native="refreshTransfer('intermex')"
                                class="ml-5 pointer">
                          <q-tooltip>Refresh</q-tooltip>
                        </q-icon>
                      </small>
                      <div class="value font-mono">{{ quick.pendingTransfer | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">Total Pending Transfer</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
         <div :class="statColClass"
             class="col-8">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-alpha-r-box" color="green"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-3">
                      <small class="super">Count</small>
                      <div class="value font-mono">{{ quick.completedTransferCount || 0 }}</div>
                    </div>
                    <div class="col-9">
                      <small class="super">
                        Amount
                        <q-icon name="mdi-refresh"
                                color="positive"
                                @click.native="refreshTransfer('intermex')"
                                class="ml-5 pointer">
                          <q-tooltip>Refresh</q-tooltip>
                        </q-icon>
                      </small>
                      <div class="value font-mono">{{ quick.completedTransfer | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">Total Completed Transfer Wait to Settle</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="statColClass"
             class="col-8">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-alpha-r-circle"
                        color="blue"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super">Total.Funded</small>
                      <div class="value font-mono">{{ quick.uniTellerTopUps | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">
                    <code>
                      Intermex Total Funded
                    </code>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="statColClass"
             class="col-8">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-bank-transfer-in"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-3">
                      <small class="super">Count</small>
                      <div class="value font-mono">{{ quick.cnt || 0 }}</div>
                    </div>
                    <div class="col-9">
                      <small class="super">Amount</small>
                      <div class="value font-mono">{{ quick.depositAmount | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">Total Bank Deposits</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="statColClass"
             class="col-8">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-alpha-r-circle"
                        color="blue"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super">Total Group Fee</small>
                      <div class="value font-mono">{{ quick.totalFee | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">
                    <code>
                      Intermex Total Fee
                      <q-tooltip>Total Group Fee of Intermex</q-tooltip>
                    </code>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="statColClass"
             class="col-8">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-credit-card-multiple"
                        color="blue"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-3">
                      <small class="super">Count</small>
                      <div class="value font-mono">{{ quick.bankCount || 0 }}</div>
                    </div>
                    <div class="col-9 pl-10">
                      <small class="super">Amount</small>
                      <div class="value font-mono">{{ quick.bankAmount | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">Bank Transfer</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="statColClass"
             class="col-8">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-multiple"
                        color="negative"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-3">
                      <small class="super">Count</small>
                      <div class="value font-mono">{{ quick.cashCount || 0 }}</div>
                    </div>
                    <div class="col-9">
                      <small class="super">Amount</small>
                      <div class="value font-mono">{{ quick.cashAmount | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">Cash Pickup</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="statColClass"
             class="col-8">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-alpha-r-circle"
                        color="blue"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super">Total Rate Revenue</small>
                      <div class="value font-mono">{{ quick.totalFxRateReve | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">
                    <code>
                      Total Fx Rate Revenue
                      <q-tooltip>Total Revenue from the Rate</q-tooltip>
                    </code>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Intermex Funding Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row['Intermex Funding Status'])">
                {{ props.row['Intermex Funding Status'] }}
              </q-chip>
              <q-icon class="ml-5 pointer"
                      name="mdi-information-outline"
                      v-if="props.row['Comment']">
                <q-tooltip v-html="props.row['Comment']"></q-tooltip>
              </q-icon>
            </template>
            <template v-else-if="['Intermex Balance', 'Deposit Amount', 'Total Transfer $', 'Bank Transfer $',
              'Avg Bank Transfer $', 'Cash Pickup $', 'Avg Cash Pickup $'].includes(col.field)">
              <div class="mex-amount"
                   :class="{heavy: col.field === 'Deposit Amount'}">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else-if="['Avg USD/MXN Rate', 'Bank Transfer #', 'Cash Pickup #'].includes(col.field)">
              <div class="mex-amount">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="send(props.row)"
                          v-if="props.row['Send Type'] !== 'MANUAL' && ['Created', 'Funding', 'Transferring'].includes(props.row['Intermex Funding Status'])">
                    <q-item-main>Send</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="updateStatusToSent(props.row)"
                          v-if="props.row['Send Type'] === 'MANUAL' && ['Created', 'Funding', 'Transferring'].includes(props.row['Intermex Funding Status'])">
                    <q-item-main>Set to "Sent"</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="updateStatus(props.row, 'Sent')"
                          v-if="['Completed'].includes(props.row['Intermex Funding Status']) && !props.row.isSetBalacne">
                    <q-item-main>Set to "Sent"</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="completedOrphan(props.row)"
                          v-if="['Created', 'Sent'].includes(props.row['Intermex Funding Status'])">
                    <q-item-main>Set to Completed</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="deleteItem(props.row)"
                          v-if="props.row.isSetBalacne">
                    <q-item-main>Delete</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="downloadProof(props.row)"
                          v-if="['Sent', 'Completed'].includes(props.row['Intermex Funding Status']) && !props.row.isSetBalacne">
                    <q-item-main>Download Proof Image</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <PromptDialog></PromptDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notify, request, toSelectOptions } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import MexProcessorMixin from '../../../../mixins/mex/MexProcessorMixin'
import PromptDialog from '../../../../components/PromptDialog'

export default {
  name: 'mex-intermex-fundings',
  mixins: [
    MexPageMixin,
    MexProcessorMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-intermex-fundings')
  ],
  components: {
    PromptDialog
  },
  data () {
    let columns = [
      'Deposit Date',
      'Batch ID',
      'Intermex Balance',
      'Deposit Amount',
      'Total Transfer $',
      'Total FxRateRevenue $',
      'Send Type',
      'Settlement Entity',
      'Settlement Date',
      'Settlement File Name',
      'Bank Transfer #',
      'Bank Transfer $',
      'Avg Bank Transfer $',
      'Cash Pickup #',
      'Cash Pickup $',
      'Avg Cash Pickup $',
      'Intermex Funding Status',
      'Action'
    ]
    return {
      title: 'Intermex Daily Settlement',
      requestUrl: `/admin/mex/intermex-fundings/list`,
      downloadUrl: `/admin/mex/intermex-fundings/export`,
      columns: generateColumns(columns, ['Avg USD/MXN Rate', 'Bank Transfer #', 'Cash Pickup #', 'Intermex Balance', 'Deposit Amount', 'Total Transfer $', 'Bank Transfer $',
        'Avg Bank Transfer $', 'Cash Pickup $', 'Avg Cash Pickup $']),
      filterOptions: [
        {
          value: 'filter[t.id=]',
          label: 'Batch ID'
        }, {
          value: 'depositDate',
          label: 'Deposit Date',
          range: [
            {
              value: 'range[t.depositDate][start]',
              type: 'date'
            }, {
              value: 'range[t.depositDate][end]',
              type: 'date'
            }
          ]
        }, {
          value: 'filter[t.status=]',
          label: 'Intermex Funding Status',
          options: toSelectOptions([
            'Created', 'Sent', 'Completed'
          ])
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true
    }
  },
  computed: {
    statColClass () {
      return this.masterAdmin ? 'col-sm-5' : 'col-sm-2-5'
    }
  },
  methods: {
    init () {
      if (this.$route.params.id) {
        this.filters = [
          {
            field: 'filter[t.id=]',
            predicate: '=',
            value: this.$route.params.id
          }
        ]
      }
    },
    statusClass (status) {
      return {
        'Created': 'dark',
        'Sent': 'blue',
        'Updated': 'orange',
        'Completed': 'positive',
        'Completed Orphan': 'positive',
        'Canceled': 'purple'
      }[status] || status
    },
    async send (row) {
      let message = 'Are you sure that you want to send the settlement'
      this.$q.dialog({
        title: `Confirm`,
        message: `${message} with the amount ${row['Deposit Amount']}?`,
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/intermex-fundings/send/${row['Batch ID']}`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
          notify(resp)
        }
      })
    },
    async updateStatusToSent (row) {
      const message = 'Are you sure that you have sent the settlement manually'
      this.$q.dialog({
        title: `Confirm`,
        message: `${message} with the amount ${row['Deposit Amount']}?`,
        cancel: true
      }).then(() => {
        this.updateStatus(row, 'Sent')
      })
    },
    async updateStatus (row, status, other = {}) {
      this.$q.loading.show()
      other.status = status
      const resp = await request(`/admin/mex/intermex-fundings/update-status/${row['Batch ID']}`, 'post', other)
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
        notify(resp)
      }
    },
    async downloadProof (row) {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/intermex-fundings/download-proof/${row['Batch ID']}`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        location.href = resp.data
        notify('Downloading...')
      }
    },
    async completedOrphan (row) {
      this.$q.dialog({
        title: `Complete Settlement`,
        message: `Are you sure that you want to complete settlement to Intermex now? Please enter "Yes" below:`,
        prompt: {
          model: ''
        },
        cancel: true
      }).then(async text => {
        if (text !== 'Yes') {
          return this.$q.notify('Operation cancelled')
        }
        this.updateStatus(row, 'Completed')
      }).catch(() => {})
    },
    deleteItem (row) {
      this.$q.dialog({
        title: `Delete Set Intermex Balance Record`,
        message: `Are you sure that you want to delete the reccord? Please enter "Yes" below:`,
        prompt: {
          model: ''
        },
        cancel: true
      }).then(async text => {
        if (text !== 'Yes') {
          return this.$q.notify('Operation cancelled')
        }
        this.$q.loading.show()
        const resp = await request(`/admin/mex/intermex-fundings/delete-balance-record/${row['Batch ID']}`, 'post')
        this.$q.loading.hide()
        this.reload()
        if (resp.success) {
          notify(resp)
        }
      }).catch(() => {})
    }
  }
}
</script>
