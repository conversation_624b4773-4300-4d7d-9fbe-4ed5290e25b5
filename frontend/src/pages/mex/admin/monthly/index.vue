<template>
  <q-page id="mex__reports_daily__index_page">
    <div class="page-header">
      <div class="title mt-15">{{ title }}</div>
      <div class="fun-group">
        <q-field label="Partner"
                 class="w-180 q-field-label-right">
          <q-select v-model="partner"
                    @input="reload"
                    :options="partners"></q-select>
        </q-field>
        <q-field v-show="scope === 'platform'"
                 label="Type"
                 class="w-180 q-field-label-right">
          <q-select v-model="type"
                    @input="reload"
                    :options="types"></q-select>
        </q-field>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row mb-15">
        <div class="min-w-600"
             :class="colCls">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-chart-bar"
                        color="positive"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-2">
                      <small class="super">Members</small>
                      <div class="value font-mono">{{ quick['Members with Loads'] || 0 }}</div>
                    </div>
                    <div class="col-2">
                      <small class="super">Count</small>
                      <div class="value font-mono">{{ quick['Total count of Loads'] || 0 }}</div>
                    </div>
                    <div class="col-5">
                      <small class="super">Total.Amount</small>
                      <div class="value font-mono">{{ quick['Total $ of Loads'] || 0 }}</div>
                    </div>
                    <div class="col-3">
                      <small class="super">Avg.$.Per.Member</small>
                      <div class="value font-mono">{{ quick['Avg $ of Loads'] || 0 }}</div>
                    </div>
                  </div>
                  <div class="description">Member Loads</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="min-w-600"
             :class="colCls">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-chart-bar"
                        color="negative"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-2">
                      <small class="super">Members</small>
                      <div class="value font-mono">{{ quick['Members with Spend'] || 0 }}</div>
                    </div>
                    <div class="col-2">
                      <small class="super">Count</small>
                      <div class="value font-mono">{{ quick['Total count of Spend'] || 0 }}</div>
                    </div>
                    <div class="col-5">
                      <small class="super">Total.Amount</small>
                      <div class="value font-mono">{{ quick['Total $ of Spend'] || 0 }}</div>
                    </div>
                    <div class="col-3">
                      <small class="super">Avg.$.Per.Member</small>
                      <div class="value font-mono">{{ quick['Avg $ of Spend'] || 0 }}</div>
                    </div>
                  </div>
                  <div class="description">Member Spend</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="min-w-600"
             :class="colCls">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-chart-bar"
                        color="blue"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-2">
                      <small class="super">Members</small>
                      <div class="value font-mono">{{ quick['Members with Transfers'] || 0 }}</div>
                    </div>
                    <div class="col-2">
                      <small class="super">Count</small>
                      <div class="value font-mono">{{ quick['Total count of Transfers'] || 0 }}</div>
                    </div>
                    <div class="col-5">
                      <small class="super">Total.Amount</small>
                      <div class="value font-mono">{{ quick['Total $ of Transfers'] || 0 }}</div>
                    </div>
                    <div class="col-3">
                      <small class="super">Avg.$.Per.Member</small>
                      <div class="value font-mono">{{ quick['Avg $ of Transfers'] || 0 }}</div>
                    </div>
                  </div>
                  <div class="description">Member Transfers</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="min-w-600"
             :class="colCls">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-chart-bar"
                        color="blue"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-2">
                      <small class="super">Members</small>
                      <div class="value font-mono">{{ quick['Members with Bank Transfers'] || 0 }}</div>
                    </div>
                    <div class="col-2">
                      <small class="super">Count</small>
                      <div class="value font-mono">{{ quick['Total count of Bank Transfers'] || 0 }}</div>
                    </div>
                    <div class="col-5">
                      <small class="super">Total.Amount</small>
                      <div class="value font-mono">{{ quick['Total $ of Bank Transfers'] || 0 }}</div>
                    </div>
                    <div class="col-3">
                      <small class="super">Avg.$.Per.Member</small>
                      <div class="value font-mono">{{ quick['Avg $ of Bank Transfers'] || 0 }}</div>
                    </div>
                  </div>
                  <div class="description">Bank Transfers</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>

        <div class="min-w-600"
             :class="colCls">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-chart-bar"
                        color="blue"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-2">
                      <small class="super">Members</small>
                      <div class="value font-mono">{{ quick['Members with Cash Pickup Transfers'] || 0 }}</div>
                    </div>
                    <div class="col-2">
                      <small class="super">Count</small>
                      <div class="value font-mono">{{ quick['Total count of Cash Pickup Transfers'] || 0 }}</div>
                    </div>
                    <div class="col-5">
                      <small class="super">Total.Amount</small>
                      <div class="value font-mono">{{ quick['Total $ of Cash Pickup Transfers'] || 0 }}</div>
                    </div>
                    <div class="col-3">
                      <small class="super">Avg.$.Per.Member</small>
                      <div class="value font-mono">{{ quick['Avg $ of Cash Pickup Transfers'] || 0 }}</div>
                    </div>
                  </div>
                  <div class="description">Cash Pickup Transfers</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="min-w-400"
             :class="colCls"
             v-if="platform">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="positive"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-7">
                      <small class="super">Total</small>
                      <div class="value font-mono">{{ quick['Platform Net Revenue'] || 0 }}</div>
                    </div>
                    <div class="col-5">
                      <small class="super">ARPU</small>
                      <div class="value font-mono">{{ quick['Platform Net Revenue Avg'] || 0 }}</div>
                    </div>
                  </div>
                  <div class="description">Platform Net Revenue</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="min-w-400"
             :class="colCls">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="orange"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-7">
                      <small class="super">Total</small>
                      <div class="value font-mono">{{ quick['Partner Net Revenue'] || 0 }}</div>
                    </div>
                    <div class="col-5">
                      <small class="super">ARPU</small>
                      <div class="value font-mono">{{ quick['Partner Net Revenue Avg'] || 0 }}</div>
                    </div>
                  </div>
                  <div class="description">Partner Net Revenue</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn icon="mdi-refresh"
                 color="orange"
                 label="Update Now"
                 @click="forceReload"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <span class="font-12 text-faded"
                v-if="quick.updatedAt">
            Last updated at: {{ quick.updatedAt }}
          </span>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 label="Export as XLSX"
                 color="primary"
                 @click="download"
                 class="btn-sm mr-8 export-btn"
                 no-caps></q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="font-18"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="['Members with Loads', 'Total count of Loads', 'Avg $ of Loads', 'Total $ of Loads',
            'Total $ of Spend', 'Avg $ of Spend', 'Avg count of Loads',  'Members with Spend', 'Total count of Spend',
            'Avg count of Spend', 'Members with Transfers', 'Total count of Transfers', 'Avg count of Transfers',
            'Platform Net Revenue Growth', 'Partner Net Revenue Growth', 'Rapyd Net Revenue Growth', 'UniTeller Net Revenue Growth', 'Intermex Net Revenue Growth',
            'Total $ of PIN POS Spend', 'Total $ of Other POS Spend',  'Total $ of ATM Spend', 'Total $ of Transfers',
             'Total $ of Bank Transfers', 'Total $ of Cash Pickup Transfers', 'Avg $ of Transfers', 'KYC Costs',
             'Fee Credits', 'Partner Gross Revenue', 'Partner Net Revenue', 'Partner Net Revenue AVG', 'Avg $ of Loads',
             'Total $ of Loads', 'Total $ of Spend', 'Avg $ of Spend', 'Total $ of PIN POS Spend', 'Total $ of Other POS Spend',
             'Total $ of ATM Spend', 'Total $ of Transfers', 'Total $ of Bank Transfers', 'Total $ of Cash Pickup Transfers',
             'Avg $ of Transfers', 'Transfers Income', 'FX Income',   'KYC Costs', 'Fee Credits', 'Platform Gross Revenue',
             'Platform Revenue Fix', 'Platform Net Revenue', 'Platform Net Revenue Avg', 'Total Net Revenue',
             'Revenue to settle', 'Partner Revenue Fix', 'UniTeller Revenue Fix', 'UniTeller Revenue Fix',
             'Total Profit',
             'Partner Net Revenue', 'Partner Net Revenue Avg', 'Rapyd Fee', 'Intermex Fee'].includes(col.field)">
              <div class="mex-amount">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else-if="['Members with Loads', 'Total count of Loads', 'Avg count of Loads', 'Members with Spend',
            'Total count of Spend', 'Avg count of Spend', 'Members with Transfers', 'Total count of Transfers', 'Avg count of Transfers',
             'Platform Net Revenue Growth', 'Partner Net Revenue Growth', 'Rapyd Net Revenue Growth',
             'UniTeller Net Revenue Growth', 'Intermex Net Revenue Growth'].includes(col.field)">
              <div class="mex-amount">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else>
              <div notranslate="">
                <q-icon name="mdi-information-outline"
                        size="20px"
                        class="mr-5 pointer"
                        color="primary"
                        @click.native="showFixDetails(props.row, col.field)"
                        v-if="(col.field === 'Platform Revenue Fix' && props.row['fixPlatformDetails']) || (col.field === 'Partner Revenue Fix' && props.row['fixPartnerDetails'])"></q-icon>
                {{ _.get(props.row, col.field) }}
              </div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <HtmlListDialog></HtmlListDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import ForceReloadMixin from '../../../../mixins/ForceReloadMixin'
import HtmlListDialog from '../../../../components/HtmlListDialog'
import _ from 'lodash'

export default {
  name: 'mex-admin-monthly-report',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    ForceReloadMixin
  ],
  components: {
    HtmlListDialog
  },
  data () {
    return {
      scope: this.$route.params.scope,
      title: 'Snapshot Report',
      chartDateRange: 'month',
      dateRange: 'month',
      requestUrl: `/admin/mex/reports/monthly/list`,
      downloadUrl: `/admin/mex/reports/monthly/export`,
      columns: [],
      filterOptions: [],
      autoLoad: true,
      freezeColumn: 0,
      keyword: '',
      quick: {
        updatedAt: ''
      },
      types: [],
      type: null,
      partners: [
        { value: 'total', label: 'Total' },
        { value: 'rapyd', label: 'Rapyd' },
        { value: 'uniteller', label: 'UniTeller' },
        { value: 'intermex', label: 'Intermex' }
      ],
      partner: 'total'
    }
  },
  watch: {
    type: {
      immediate: true,
      handler () {
        this.updateTitleAndColumns()
      }
    },
    partner: {
      immediate: true,
      handler () {
        this.updateTitleAndColumns()
      }
    },
    scope () {
      this.reload()
    }
  },
  computed: {
    platform () {
      return this.scope === 'platform'
    },
    colCls () {
      return this.platform ? 'col-2-5' : 'col-3'
    }
  },
  methods: {
    init () {
      const platform = this.platform
      this.title = platform ? 'Platform Snapshot Report' : 'Partner Snapshot Report'

      if (platform) {
        this.types = [
          { value: 'month', label: 'Monthly' },
          { value: 'week', label: 'Weekly' },
          { value: 'day', label: 'Daily' }
        ]
      } else {
        this.types = [
          { value: 'month', label: 'Monthly' }
        ]
      }

      if (!this.type) {
        this.type = platform ? 'week' : 'month'
      } else if (this.type !== 'month' && !platform) {
        this.type = 'month'
      }

      this.updateTitleAndColumns()
    },
    initData (route) {
      this.scope = route.params.scope
      this.init()
    },
    getTypeName () {
      if (this.type === 'day') {
        return 'daily'
      }
      return this.type + 'ly'
    },
    updateTitleAndColumns () {
      const platform = this.scope === 'platform'
      const prefix = platform ? 'Platform' : 'Partner'
      this.title = prefix + ' ' + _.startCase(this.getTypeName()) + ' Snapshot Report'
      let partner = 'Partner'
      if (this.partner === 'rapyd') {
        partner = 'Rapyd'
      }
      if (this.partner === 'uniteller') {
        partner = 'UniTeller'
      }
      if (this.partner === 'intermex') {
        partner = 'Intermex'
      }
      let columns = [
        'Period',
        _.startCase(this.type),

        'Members with Loads',
        'Total count of Loads',
        'Total $ of Loads',
        'Avg count of Loads',
        'Avg $ of Loads',

        'Members with Spend',
        'Total count of Spend',
        'Total $ of Spend',
        'Avg count of Spend',
        'Avg $ of Spend',

        'Total $ of PIN POS Spend',
        'Total $ of Other POS Spend',
        'Total $ of ATM Spend',

        'Members with Transfers',
        'Total count of Transfers',
        'Total $ of Transfers',
        'Total $ of Bank Transfers',
        'Total $ of Cash Pickup Transfers',
        'Avg count of Transfers',
        'Avg $ of Transfers',
        'Rapyd Fee',

        'Transfers Income',
        'FX Income',

        'KYC Costs',
        'Fee Credits',

        'Platform Gross Revenue',
        'Partner Gross Revenue',

        'Platform Revenue Fix',
        'Partner Revenue Fix',

        'Platform Net Revenue',
        'Platform Net Revenue Avg',
        'Platform Net Revenue Growth',

        partner + ' Net Revenue',
        partner + ' Net Revenue Avg',
        partner + ' Net Revenue Growth',
        'Intermex Additional Cost $0.75',
        'Total Net Revenue',
        'Revenue to settle',
        'Total Profit'
      ]
      if (!platform) {
        const hidden = [
          'Transfers Income',
          'FX Income',
          'Platform Revenue Fix',
          partner + ' Revenue Fix',
          'Total Net Revenue',
          'Intermex Additional Cost $0.75',
          'Revenue to settle',
          'Total Profit'
        ]
        columns = _.filter(columns, c => {
          return !c.startsWith('Platform ') && !hidden.includes(c)
        })
      }
      columns = generateColumns(columns, _.slice(columns, 1))
      this.columns = columns.map(c => {
        c.tooltip = {
          'Members with Loads': 'The count of members who ever received the employer payout or ACH credits',
          'Total count of Loads': 'Total count of loads',
          'Total $ of Loads': 'Total $ amount of loads',
          'Avg count of Loads': 'Average count of loads received for each member',
          'Avg $ of Loads': 'Average $ amount of loads received for each member',
          'Members with Spend': 'The count of members who ever spent their money',
          'Total count of Spend': 'Total count of member spend (transaction code: <code>POS_PURCHASE</code>, <code>POS_PURCHASE_WITH_CASHBACK</code>, <code>POS_ECOM_PURCHASE</code>, <code>AUTH_COMPLETION_DEBIT</code>, <code>ATM_WITHDRAW</code>)',
          'Total $ of Spend': 'Total $ amount of member spend',
          'Avg count of Spend': 'Average count of spend for each member',
          'Avg $ of Spend': 'Average $ amount of spend for each member',
          'Total $ of PIN POS Spend': 'Total $ amount of member PIN POS spend (transaction code: <code>POS_PURCHASE</code>, <code>POS_PURCHASE_WITH_CASHBACK</code>)',
          'Total $ of Other POS Spend': 'Total $ amount of member other POS spend (transaction code: <code>POS_ECOM_PURCHASE</code>, <code>AUTH_COMPLETION_DEBIT</code>)',
          'Total $ of ATM Spend': 'Total $ amount of member ATM withdraw (transaction code: <code>ATM_WITHDRAW</code>)',
          'Members with Transfers': 'The count of members who ever sent a Rapyd transfer',
          'Total count of Transfers': 'Total count of Rapyd transfers',
          'Total $ of Transfers': 'Total $ amount of Rapyd transfers',
          'Avg count of Transfers': 'Average count of transfers for each member',
          'Avg $ of Transfers': 'Average $ amount of transfers for each member',
          'Transfers Income': 'The shared revenue from Rapyd transfers. <code>Income = Fee - Cost</code>. <code>Fee = Bank ($1) + shared rate markup (0.02 -> 0.012)</code>',
          'FX Income': 'Tern\'s revenue from transfers. <code>Income = rate markup (0.08)</code>',
          'KYC Costs': 'Total $ amount of the KYC costs for the members',
          'Fee Credits': 'Total $ amount of credits for the maintenance fee and ATM fee',
          'Platform Gross Revenue': '<code>Transfers Income</code> * 0.5 + <code>FX Income</code> * 0.5 (from 2024)',
          'Partner Gross Revenue': '<code>Transfers Income</code> * 0.5 + <code>FX Income</code> * 0.5 (from 2024)',
          'Platform Revenue Fix': 'Usually because of the previous transfer status changes. Completed becomes error, or the reverse.',
          'Partner Revenue Fix': 'Same as <code>Platform Revenue Fix</code>',
          'Platform Net Revenue': '<code>Platform Gross Revenue</code> - <code>Fee Credits</code> * 0.5',
          'Platform Net Revenue AVG': '<code>Platform Net Revenue</code> / <code>Members with Transfers</code>',
          'Platform Net Revenue Growth': '(<code>This period\'s platform net revenue</code> - <code>Previous</code>) / <code>Previous</code>',
          'Partner Net Revenue': '<code>Partner Gross Revenue</code> - <code>Fee Credits</code> * 0.5 - <code>KYC Costs</code>',
          'Partner Net Revenue AVG': '<code>Partner Net Revenue</code> / <code>Members with Transfers</code>',
          'Partner Net Revenue Growth': '(<code>This period\'s Rapyd net revenue</code> - <code>Previous</code>) / <code>Previous</code>',
          'Rapyd Net Revenue': '<code>Rapyd Gross Revenue</code> - <code>Fee Credits</code> * 0.5 - <code>KYC Costs</code>',
          'Rapyd Net Revenue AVG': '<code>Partner Net Revenue</code> / <code>Members with Transfers</code>',
          'Rapyd Net Revenue Growth': '(<code>This period\'s Rapyd net revenue</code> - <code>Previous</code>) / <code>Previous</code>',
          'UniTeller Net Revenue': '<code>UniTeller Gross Revenue</code> - <code>Fee Credits</code> * 0.5 - <code>KYC Costs</code>',
          'UniTeller Net Revenue AVG': '<code>UniTeller Net Revenue</code> / <code>Members with Transfers</code>',
          'UniTeller Net Revenue Growth': '(<code>This period\'s UniTeller net revenue</code> - <code>Previous</code>) / <code>Previous</code>',
          'Intermex Net Revenue': '<code>Intermex Gross Revenue</code> - <code>Fee Credits</code> * 0.5 - <code>KYC Costs</code>',
          'Intermex Net Revenue AVG': '<code>Intermex Net Revenue</code> / <code>Members with Transfers</code>',
          'Intermex Net Revenue Growth': '(<code>This period\'s Intermex net revenue</code> - <code>Previous</code>) / <code>Previous</code>',
          'Total Net Revenue': '<code>Platform Net Revenue</code> + <code>Partner Net Revenue</code>',
          'Revenue to settle': '<code>Total Net Revenue</code> + <code>KYC Costs</code> - <code>UniTeller Revenue</code> - <code>Intermex Additional Cost $0.75</code>',
          'Total Profit': '<code>Total Net Revenue</code> - <code>KYC Costs</code>'
        }[c.field]
        return c
      })
      console.log(this.columns)
    },
    getOtherQueryParams () {
      return {
        force: this.force,
        type: this.type,
        scope: this.$route.params.scope,
        partner: this.partner
      }
    },
    showFixDetails (row, type) {
      let details = []
      let title = type + ' Details'
      if (type === 'Platform Revenue Fix') {
        details = row['fixPlatformDetails']
      } else if (type === 'Partner Revenue Fix') {
        details = row['fixPartnerDetails']
      }
      this.$root.$emit('show-html-list-dialog', {
        title,
        html: `<ul><li>${details.join('</li><li>')}</li></ul>`
      })
    }
  },
  beforeRouteUpdate (to, from, next) {
    this.initData(to)
    next()
  },
  mounted () {
    this.initData(this.$route)
  }
}
</script>
