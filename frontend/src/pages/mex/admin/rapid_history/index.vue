<template>
  <q-page id="mex__transfer_admin_rapid_history__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row">
        <div class="col-sm-4 col-12">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-bank"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super">Amount</small>
                      <div class="value font-mono">{{ quick.rapidBalance | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">
                    Base Agent Account Balance
                    <q-icon name="mdi-refresh" color="positive" @click.native="refreshRapid"
                            class="ml-5 pointer">
                      <q-tooltip>Refresh</q-tooltip>
                    </q-icon>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-4 col-12">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-bank-transfer-in" color="positive"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-4">
                      <small class="super">Count</small>
                      <div class="value font-mono">{{ quick.creditCount || 0 }}</div>
                    </div>
                    <div class="col-8">
                      <small class="super">Amount</small>
                      <div class="value font-mono">{{ quick.creditAmount | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">Total Credit</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-4 col-12">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-bank-transfer-out" color="negative"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-4">
                      <small class="super">Count</small>
                      <div class="value font-mono">{{ quick.debitCount || 0 }}</div>
                    </div>
                    <div class="col-8">
                      <small class="super">Amount</small>
                      <div class="value font-mono">{{ quick.debitAmount | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">Total Debit</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-refresh" color="positive"
                 label="Update Now"
                 @click="forceReload"
                 class="btn-sm mr-8" no-caps></q-btn>

          <q-btn icon="mdi-file-download-outline" color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8" no-caps></q-btn>

          <q-search icon="search" v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat round dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating class="dot" v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat round dense
                 color="faded"
                 class="mr-10"
                 @click="reload"
                 icon="refresh"/>
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body" slot-scope="props" :props="props" :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols" :key="col.field" :align="col.align"
                :style="columnStyles[i]" :class="columnClasses[i]">
            <template v-if="col.field === 'Claimed Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row['Claimed Status'])">
                {{ props.row['Claimed Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Amount'">
             <div class="mex-amount">
                {{ props.row['Amount'] }}
             </div>
            </template>
            <template v-else-if="col.field === 'Previous Balance'">
              <div class="mex-amount">
                {{ props.row['Previous Balance'] }}
              </div>
            </template>
            <template v-else-if="col.field === 'Current Balance'">
              <div class="mex-amount">
                {{ props.row['Current Balance'] }}
              </div>
            </template>
            <template v-else-if="col.field === 'User' && props.row[col.field]">
              <a :href="`#/j/mex/members/${props.row[col.field]}`" target="_blank">{{ _.get(props.row, col.field) }}</a>
            </template>
            <template v-else-if="col.field === 'Transfer' && props.row[col.field]">
              <a class="mex-amount" :href="`#/j/mex/transfers/${props.row[col.field]}`" target="_blank">{{ _.get(props.row, col.field) }}</a>
            </template>
            <template v-else-if="col.field === 'Settlement' && props.row[col.field]">
              <a :href="`#/j/mex/fundings/${props.row[col.field]}`" target="_blank">{{ _.get(props.row, col.field) }}</a>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns, notify, request, toSelectOptions } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import ForceReloadMixin from '../../../../mixins/ForceReloadMixin'
import _ from 'lodash'

export default {
  name: 'mex-admin-rapyd-history',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    ForceReloadMixin,
    FreezeColumnMixin
  ],
  data () {
    return {
      title: 'Rapid Agent Account History',
      requestUrl: `/admin/mex/rapid-transactions/list`,
      filtersUrl: `/admin/mex/rapid-transactions/filters`,
      downloadUrl: `/admin/mex/rapid-transactions/export`,
      columns: generateColumns([
        'Txn Date',
        'Txn Ref Number',
        'Txn Type',
        'Rapid Agent Account',
        'Account Number',
        'Amount',
        'First Name',
        'Last Name',
        'Txn Id', 'Txn Code', 'Txn Status',
        'Description 1',
        'Description 2',
        'User',
        'Transfer',
        'Settlement',
        'Transaction',
        'Claimed Date',
        'Claimed Status',
        'Assigned Id', 'Fee Code',
        'Previous Balance',
        'Current Balance'
      ], ['Transfer', 'Previous Balance', 'Current Balance', 'Amount']),
      filterOptions: [
        {
          value: 'filter[t.txnRefNumber=]',
          label: 'Txn Ref Number'
        }, {
          value: 'filter[t.txnCode=]',
          label: 'Txn Code'
        }, {
          value: 'filter[t.txnStatus=]',
          label: 'Txn Status'
        }, {
          value: 'filter[u.id=]',
          label: 'User Id'
        }, {
          value: 'txnDate',
          label: 'Txn Date',
          range: [
            {
              value: 'range[t.txnDate][start]',
              type: 'date'
            }, {
              value: 'range[t.txnDate][end]',
              type: 'date'
            }
          ]
        }, {
          value: 'filter[t.txnType=]',
          label: 'Txn Type',
          options: toSelectOptions([
            'CREDIT', 'DEBIT'
          ], true)
        }, {
          value: 'filter[t.agentNumber=]',
          label: 'Rapid Agent Account',
          options: [],
          source: 'agentNumbers'
        }, {
          value: 'txn_amount',
          label: 'Txn Amount',
          range: [
            {
              value: 'range[t.amount][min]',
              type: 'localAmount'
            },
            {
              value: 'range[t.amount][max]',
              type: 'localAmount'
            }
          ]
        }, {
          value: 'view_type',
          label: 'View Type',
          options: toSelectOptions([
            'all',
            'hide_payouts',
            'hide_transfers',
            'hide_transfers_payouts',
            'hide_transfers_payouts_fee_credits'
          ], true)
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true
    }
  },
  methods: {
    statusClass (status) {
      return {
        'Claimed': 'positive',
        'Canceled': 'purple'
      }[status] || 'negative'
    },
    async refreshRapid () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/transfer-fundings/refresh-rapid`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        _.assignIn(this.quick, resp.data)
        notify(resp)
      }
    }
  }
}
</script>
