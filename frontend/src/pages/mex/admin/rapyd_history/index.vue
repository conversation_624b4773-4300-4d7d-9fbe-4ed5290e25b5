<template>
  <q-page id="mex__transfer_admin_rapyd_history__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3 col-6 col-xs-10">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-briefcase-account-outline"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.rapydBalance | moneyFormat }}</div>
                  <div class="description">
                    Wallet Balance
                    <q-icon name="mdi-refresh" color="positive" @click.native="refreshRapyd"
                            class="ml-5 pointer">
                      <q-tooltip>Refresh</q-tooltip>
                    </q-icon>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6 col-xs-10">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-upload" color="positive"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.top_up_funds_in | moneyFormat }}</div>
                  <div class="description">Total Top-up</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6 col-xs-10">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-download" color="negative"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.payout_funds_out | moneyFormat }}</div>
                  <div class="description">Total Payouts</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6 col-xs-10">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-card-bulleted-outline" color="orange"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.rapyd_fees_funds_out | moneyFormat }}</div>
                  <div class="description">Total Fees</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline" color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8" no-caps></q-btn>

          <q-search icon="search" v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat round dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating class="dot" v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat round dense
                 color="faded"
                 class="mr-10"
                 @click="reload"
                 icon="refresh"/>
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body" slot-scope="props" :props="props" :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols" :key="col.field" :align="col.align"
                :style="columnStyles[i]" :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Transfer' && props.row[col.field]">
              <a :href="`#/j/mex/transfers/${props.row[col.field]}`" target="_blank">{{ _.get(props.row, col.field) }}</a>
            </template>
            <template v-else-if="col.field === 'Settlement' && props.row[col.field]">
              <a :href="`#/j/mex/fundings/${props.row[col.field]}`" target="_blank">{{ _.get(props.row, col.field) }}</a>
            </template>
            <template v-else-if="['Amount', 'Balance', 'Transfer Amount', 'Rapyd Fee', 'Revenue',].includes(col.field)">
              <div class="mex-amount">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="editComment(props.row)">
                    <q-item-main>Edit Comment</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns, notify, request, toSelectOptions } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import _ from 'lodash'

export default {
  name: 'mex-admin-rapyd-history',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin
  ],
  data () {
    return {
      title: 'Rapyd Transfers Account History',
      filtersUrl: `/admin/mex/rapyd-transactions/filters`,
      requestUrl: `/admin/mex/rapyd-transactions/list`,
      downloadUrl: `/admin/mex/rapyd-transactions/export`,
      columns: generateColumns([
        'Date',
        'Transaction ID',
        'Amount',
        'Balance',
        'Type',
        'Subtype',
        'Reason',
        'Transfer',
        'Transfer Amount',
        'Rapyd Fee',
        'Revenue',
        'Settlement',
        'Status',
        'Comment',
        'Action'
      ], ['Amount', 'Balance', 'Transfer Amount', 'Rapyd Fee', 'Revenue']),
      filterOptions: [
        {
          value: 'filter[t.id=]',
          label: 'Transaction ID'
        }, {
          value: 'date',
          label: 'Date',
          range: [
            {
              value: 'range[t.createdAt][start]',
              type: 'date'
            }, {
              value: 'range[t.createdAt][end]',
              type: 'date'
            }
          ]
        }, {
          value: 'filter[t.status=]',
          label: 'Status',
          options: toSelectOptions([
            'CLOSED', 'CANCELED'
          ], true)
        }, {
          value: 'filter[t.type=]',
          label: 'Type',
          options: [],
          source: 'types'
        }, {
          value: 'filter[t.subtype=]',
          label: 'Subtype',
          options: [],
          source: 'subtypes'
        }
      ],
      freezeColumn: -1,
      autoLoad: true
    }
  },
  computed: {
    statColClass () {
      return this.masterAdmin ? 'col-sm-2' : 'col-sm-2-5'
    }
  },
  methods: {
    statusClass (status) {
      return {
        'CLOSED': 'blue',
        'CANCELED': 'orange'
      }[status] || status
    },
    async refreshRapyd () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/transfer-fundings/refresh-rapyd`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        _.assignIn(this.quick, resp.data)
        notify(resp)
      }
    },
    editComment (row) {
      this.$q.dialog({
        title: 'Edit Comment',
        message: `Please enter the comment below:`,
        cancel: true,
        prompt: {
          model: row.Comment
        }
      }).then(async content => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/rapyd-transactions/${row.ID}/edit-comment`, 'post', {
          content
        })
        this.$q.loading.hide()
        if (resp.success) {
          notify()
          this.reload()
        }
      }).catch(() => {})
    }
  }
}
</script>
