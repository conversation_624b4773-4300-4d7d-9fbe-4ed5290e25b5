<template>
  <q-page id="mex__transfer_admin_uniteller_transfer_report_index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div :class="summaryCol">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-comment-arrow-right-outline"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.count || 0 }}</div>
                  <div class="description">Number of Transfers</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="summaryCol">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-usd"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.total || 0 | moneyFormat }}</div>
                  <div class="description">Total Amount</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="summaryCol">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row"><q-icon name="mdi-cash-usd"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.fee || 0 | moneyFormat }}</div>
                  <div class="description">Total Fee</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="summaryCol">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-usd"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.cost || 0 | moneyFormat }}</div>
                  <div class="description">Total Cost</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>

        <div :class="summaryCol">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-usd"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.fxRevenue || 0 | moneyFormat }}</div>
                  <div class="description">Total Fx Revenue</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="summaryCol">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-usd"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.revenue || 0 | moneyFormat }}</div>
                  <div class="description">Total Revenue</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="['Total Transfer', 'Count Of Transfer', 'Transfer Fee', 'Transfer Cost',
                'Fx Revenue', 'Revenue'].includes(col.field)">
              <div class="mex-amount">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-if="props.row['Status'] == 'Pending'"
                          @click.native="update(props.row)">
                    <q-item-main>Update Settle Status</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns, notifySuccess, request } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
export default {
  name: 'mex-admin-uniteller-history',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin
  ],
  data () {
    const columns = [
      'Month', 'Total Transfer', 'Count Of Transfer', 'Transfer Fee',
      'Transfer Cost', 'Fee Revenue', 'FX Revenue', 'Revenue', 'Status', 'Action'
    ]
    return {
      title: 'UniTeller Transfer Summary',
      filtersUrl: `/admin/mex/uniteller-transfer-report/filters`,
      requestUrl: `/admin/mex/uniteller-transfer-report/list`,
      downloadUrl: `/admin/mex/uniteller-transfer-report/export`,
      columns: generateColumns(columns, [
        'Total Transfer', 'Count Of Transfer', 'Transfer Fee',
        'Transfer Cost', 'Fee Revenue', 'FX Revenue', 'Revenue'
      ]),
      filterOptions: [
        {
          value: 'filter[utr.month=]',
          label: 'Month',
          options: [],
          source: 'monthList'
        }
      ],
      freezeColumn: -1,
      autoLoad: true,
      keyword: ''
    }
  },
  computed: {
    statColClass () {
      return this.masterAdmin ? 'col-sm-2 col-6 col-xs-10' : 'col-sm-3 col-6 col-xs-10'
    }
  },
  methods: {
    statusClass (status) {
      return {
        'Pending': 'blue',
        'Completed': 'positive'
      }[status] || status
    },
    async update (row) {
      this.$q.dialog({
        title: `Update`,
        message: 'Are you sure that you have settled the revenue from Uniteller? ',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/uniteller-transfer-report/update/${row['ID']}`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
          notifySuccess(resp)
        }
      }).catch(() => {})
    }
  },
  mounted () {
    this.columns = this.columns.map(c => {
      c.tooltip = {
        'Transfer Fee': 'The fees we collect from members for transfers',
        'Transfer Cost': 'The fees Uniteller collect from tern for transfers',
        'Fee Revenue': 'Fee revenue = Transfer Fee - Transfer Cost',
        'FX Revenue': 'The Revenue on exchange rates = Total Transfer * Exchange rate markup(2%)',
        'Revenue': 'Final Revenue = Fee Revenue + FX Revenue'
      }[c.field]
      return c
    })
  }
}
</script>
