<template>
  <q-page id="mex__agents__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3 col-12">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.total || 0 }}</div>
                  <div class="description">Total Agents</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Agents Report</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-account-plus-outline"
                 color="positive"
                 label="Create New Agent"
                 @click="add"
                 class="btn-sm mr-10"
                 no-caps></q-btn>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="props.row['Status'] === 'Active' ? 'positive' : 'negative'">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'Active')"
                          v-if="props.row['Status'] === 'Archived'">
                    <q-item-main>Change to Active</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'Archived')"
                          v-else>
                    <q-item-main>Change to Archived</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="masterAdmin"
                          @click.native="$c.loginAs(props.row['User ID'])">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="masterAdmin || agentAdmin"
                          @click.native="resetFA(props.row)">
                    <q-item-main>Reset 2FA</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="updatePassword(props.row)"
                          v-if="props.row['Status'] === 'Active'">
                    <q-item-main>Reminder Update Password</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

    <DetailDialog></DetailDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import MexPageMixin from '../../../mixins/mex/MexPageMixin'
import DetailDialog from './detail'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'mex-agents',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-agents')
  ],
  components: {
    DetailDialog
  },
  data () {
    return {
      title: 'Agents',
      requestUrl: `/admin/mex/agents/list`,
      downloadUrl: `/admin/mex/agents/export`,
      columns: generateColumns([
        'Create Date', 'User ID', 'First Name', 'Last Name',
        'User Type', 'Email', 'Mobile Phone', 'Last Login Time',
        'Status', 'Actions'
      ], [], {
        'Create Date': 'u.createdAt',
        'User ID': 'u.id',
        'First Name': 'u.firstName',
        'Last Name': 'u.lastName',
        'User Type': 't.name',
        'Email': 'u.email',
        'Mobile Phone': 'u.mobilephone',
        'Last Login Time': 'u.lastLogin'
      }),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        }, {
          value: 'filter[u.mobilephone=]',
          label: 'Mobile Phone'
        }, {
          value: 'filter[u.status=]',
          label: 'Status',
          options: [
            { label: 'Active', value: 'active' },
            { label: 'Archived', value: 'closed' }
          ]
        }, {
          value: 'filter[t.name=]',
          label: 'User Type',
          options: [
            { label: 'Administrator', value: 'TransferMex Admin' },
            { label: 'Agent', value: 'TransferMex Agent' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    add () {
      this.$root.$emit('show-mex-agent-detail-dialog')
    },
    edit (row) {
      this.$root.$emit('show-mex-agent-detail-dialog', row)
    },
    async changeStatus (row, status) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/agents/${row['User ID']}/toggle-status`, 'post', {
        Status: status
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    },
    async resetFA (row) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/agents/${row['User ID']}/resetFA`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    },
    async updatePassword (row) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/agents/${row['User ID']}/update-password`, 'POST')
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    }
  }
}
</script>
