<template>
  <q-dialog
    class="mex-common-deposit-dialog"
    v-model="visible"
  >
    <template slot="title">
      <div class="font-16 mb-2">Direct Deposit Info</div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-6">
          <q-input float-label="Account Number" autocomplete="no"
                   readonly
                   v-model="entity['Account Number']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Routing Number" autocomplete="no"
                   readonly
                   v-model="entity['Routing Number']"></q-input>
        </div>
      </div>
      <q-table v-if="logs.length" :data="logs"
               :columns="columns"
               notranslate=""
               class="clean mt-25">
        <template slot="top-left">
          <strong>Users that viewed the info</strong>
        </template>
      </q-table>
    </template>
    <template slot="buttons">
      <q-btn label="Close" no-caps
             color="blue" class="max-w-300"
             text-color="white"
             @click="_hide" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { request } from '../../../common'
import _ from 'lodash'

export default {
  name: 'mex-common-deposit-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        'Account Number': null,
        'Routing Number': null
      },
      logs: [],
      columns: [
        { field: 'time', label: 'Date & Time', align: 'left' },
        { field: 'name', label: 'TransferMex User', align: 'left' }
      ]
    }
  },
  methods: {
    async show () {
      this.visible = false
      await this.init()
      if (this.entity['Account Number']) {
        this.visible = true
      }
    },
    async init () {
      this.$q.loading.show()
      let resp = null
      if (this.entity['isEmployer']) {
        resp = await request(`/admin/mex/employee/${this.entity['Member ID']}/deposit-info`, 'post')
      } else {
        resp = await request(`/admin/mex/members/${this.entity['Member ID']}/deposit-info`, 'post', {
          token: this.entity.token
        })
      }
      this.$q.loading.hide()
      if (resp.success) {
        _.assignIn(this.entity, resp.data)
        this.logs = resp.data.logs
      }
    }
  }
}
</script>

<style lang="scss">
.mex-common-deposit-dialog {
  .modal-content {
    width: 500px;
  }
}
</style>
