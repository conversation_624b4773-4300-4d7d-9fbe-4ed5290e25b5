<template>
  <div id="membersStatus">
    <q-card>
      <q-card-title>
        <div class="row">
          <div class="chart-title">
            <p>Members</p>
            <span class="font-mono">{{total}}</span>
              <span>
               Members
                <q-chip dense
                      :class="delta >= 0 ? 'positive' : 'negative'">{{ delta | percent(1, true) }}</q-chip>
              </span>
          </div>
          <q-btn class="members-btn down-btn btn-sm ml-auto mr-8 square"
                 @click="download()"
                 icon="mdi-file-document-outline"></q-btn>
          <q-btn class="members-btn report-btn btn-sm square"
                 @click="viewReport()"
                 icon="mdi-chart-line"></q-btn>
        </div>
      </q-card-title>
      <q-card-main class="chart-item">
        <!-- <div id="employersChart"></div> -->
        <div id="membersChart"
             class="row">
          <div class="member-status-item col-sm-4 col-xs-6"
               v-for="(item,i) in statusList"
               :key="i">
            <q-knob font-size="12px"
                    size="50px"
                    v-model="statusList[i]['delta']"
                    disabled
                    readonly
                    :class="knobClass(i)"
                    class="q-ma-md">
              {{ item.delta }}%
            </q-knob>
            <div class="count-desc">
              <p class="count">{{item.count}}</p>
              <p class="name">{{i}}</p>
            </div>
          </div>
        </div>
        <div v-if="visible"
             class="loading">
          <q-spinner-hourglass color="primary"
                               size="3em"
                               :thickness="2" />
        </div>
      </q-card-main>
    </q-card>
  </div>
</template>

<script>
// import echarts from 'echarts'
// import $ from 'jquery'
import { request, EventHandlerMixin, notify } from '../../../common'
import html2canvas from 'html2canvas'
import moment from 'moment'

export default {
  components: {
  },
  props: {
    'value': {
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  watch: {
    value () {
      this.getData()
    }
  },
  mixins: [
    EventHandlerMixin('reload-employers-chart', 'getData')
  ],
  data () {
    return {
      delta: 0,
      total: 0,
      visible: false,
      chart: null,
      series: [],
      startTime: moment().startOf('month'),
      endTime: moment().endOf('month'),
      statusList: [],
      knobColor: {
        'Active': 'active-color',
        'KYC Failed': 'kyc-failed-color',
        'Onboarded': 'onboarded-color',
        'Initial': 'initial-color',
        'Closed': 'closed-color',
        'On Hold': 'on-hold-color'
      }
    }
  },
  async mounted () {
    this.getData()
  },
  methods: {
    resize () {
      this.chart.resize()
    },
    knobClass (i) {
      return this.knobColor[i]
    },
    viewReport () {
      this.$router.push(`/j/mex/members`)
    },
    async getData () {
      this.visible = true
      switch (this.value) {
        case 'all':
          this.startTime = null
          this.endTime = null
          break
        case 'week':
          this.startTime = moment().startOf('week')
          this.endTime = moment().endOf('week')
          break
        case 'month':
          this.startTime = moment().startOf('month')
          this.endTime = moment().endOf('month')
          break
        case 'quarter':
          this.startTime = moment().startOf('quarter')
          this.endTime = moment().endOf('quarter')
          break
        case 'year':
          this.startTime = moment().startOf('year')
          this.endTime = moment().endOf('year')
          break
        case 'today':
          this.startTime = this.startDate
          this.endTime = this.endDate
          break
      }
      const resp = await request(`/admin/mex/membersChart`, 'get', { 'period': this.value, 'start': this.startTime ? moment(this.startTime).format('L') : null, 'end': this.startTime ? moment(this.endTime).format('L') : null })
      if (resp.success) {
        this.delta = resp.data.per
        this.total = resp.data.total
        this.statusList = resp.data.data
        // this.initChart(resp.data)
      }
      this.visible = false
    },
    // initChart (chartData) {
    //   this.chart = echarts.init(document.getElementById('employersChart'), 'primary')
    //   // let that = this
    //   let positionStr = ''
    //   let colorList = ['#0062ff', '#ff974a', '#3dd598', '#ffc542', '#7a62f9']
    //   let labelList = chartData.key
    //   let seriesData = []
    //   chartData.key.forEach(element => {
    //     seriesData.push(chartData[element]['count'])
    //   })
    //   this.series = {
    //     data: seriesData,
    //     type: 'bar',
    //     itemStyle: {
    //       barBorderRadius: [10, 10, 0, 0],
    //       color: function (params) {
    //         return colorList[params.dataIndex]
    //       }
    //     },
    //     label: {
    //       show: true,
    //       position: 'bottom',
    //       color: '#92929e',
    //       formatter: function (params) {
    //         // build a color map as your need.
    //         return labelList[params.dataIndex]
    //       }
    //     },
    //     barMaxWidth: '40px'
    //   }
    //   let option = {
    //     legend: {
    //       bottom: '0px',
    //       icon: 'pin',
    //       data: ['']
    //     },
    //     tooltip: {
    //       trigger: 'item',
    //       // alwaysShowContent: true,
    //       // axisPointer: {
    //       //   type: 'cross',
    //       //   label: {
    //       //     backgroundColor: '#6a7985'
    //       //   }
    //       // },
    //       backgroundColor: '#ffffff',
    //       position: function (pos, params, dom, rect, size) {
    //         // 鼠标在左侧时 tooltip 显示到右侧，鼠标在右侧时 tooltip 显示到左侧。
    //         let obj = { top: pos[1] - 180 }
    //         if (pos[0] < size.viewSize[0] / 2) {
    //           obj['left'] = pos[0] - 50
    //           positionStr = 'left'
    //         } else {
    //           positionStr = 'right'
    //           obj['right'] = size.viewSize[0] - pos[0] - 50
    //         }
    //         console.log(obj)
    //         return obj
    //       },
    //       padding: 0,
    //       formatter: function (params) {
    //         // console.log('234567uijhgfd')
    //         console.log(params)
    //         return '<div class="tooltip-area-' + positionStr + '" style="color:#1c1446;font-weight: 600;box-shadow: 0 5px 15px 0 rgba(28, 20, 70, 0.1);border-radius:4px;padding:30px;padding-top:16px;font-family: Poppins;font-size: 14px;"><p style="text-align:center;margin:0px;">' + labelList[params.dataIndex] + '</p>' +
    //           '<div style="line-height:30px;min-width:215px;display:flex;justify-content:space-between;color:#231f20;">Total($)<span style="color:#696975;font-weight: 500;">' + moneyFormat(chartData[labelList[params.dataIndex]]['total']) + '</span></div>' +
    //           '<div style="line-height:30px;min-width:215px;display:flex;justify-content:space-between;color:#231f20;">#of Members<span style="color:#696975;font-weight: 500;">' + params.value + '</span></div>' +
    //           '<div style="line-height:30px;min-width:215px;display:flex;justify-content:space-between;color:#231f20;">Avg.<span style="color:#696975;font-weight: 500;">' + moneyFormat(chartData[labelList[params.dataIndex]]['avg']) + '</span></div>' +
    //           '</div>'
    //       }
    //     },
    //     xAxis: {
    //       type: 'category'
    //     },
    //     grid: {
    //       top: '10px',
    //       left: '3%',
    //       right: '4%',
    //       bottom: '40px'
    //     },
    //     yAxis: {
    //       type: 'value',
    //       axisLine: {
    //         show: false
    //       }
    //     },
    //     series: this.series
    //   }
    //   console.log(option)
    //   this.chart.setOption(option)

    //   $(window).on('resize', this.resize)
    // },
    download () {
      let userAgent = navigator.userAgent
      if (userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Chrome') === -1) {
        return notify('Please take a screenshot manually or use another browser to open the website to save the picture!', 'negative')
      }
      let target = null
      let name = 'membersStatus'
      target = document.getElementById('membersStatus')
      this.$q.loading.show()
      const targetCss = window.getComputedStyle(target)

      const options = {
        scale: 2,
        width: parseInt(targetCss.width, 10),
        height: parseInt(targetCss.height, 10),
        y: target.offsetTop + 70
      }
      console.log(options)
      html2canvas(target, options).then(canvas => {
        // canvas is the final rendered <canvas> element
        const imgData = canvas.toDataURL('image/jpeg')
        console.log(imgData)
        this.fileDownload(imgData, name)
        this.$q.loading.hide()
      })
    },
    fileDownload (downloadUrl, filename) {
      let aLink = document.createElement('a')
      aLink.style.display = 'none'
      aLink.href = downloadUrl
      aLink.download = `${filename}`
      // 触发点击-然后移除
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    }
  }
}
</script>
<style lang="scss">
#membersStatus {
  margin: 0 !important;
  .members-btn {
    max-height: 32px !important;
    color: #fff;
    text-transform: capitalize;
  }
  .avg-area {
    margin: 0;
    font-size: 7px;
    color: #787393;
    line-height: 7px;
  }
  .report-btn {
    background: #0062ff;
  }
  .down-btn {
    background: #00de00;
  }
  .chart-title {
    p {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
    span {
      font-size: 28px;
      font-weight: 600;
    }
  }

  #membersChart {
    height: 307px;
    display: flex;
    .member-status-item {
      display: flex;
      //  align-items: center;
      margin-top: 15px;
      .count-desc {
        display: inline-block;
        max-width: calc(100% - 82px);
      }
      p {
        margin: 0;
      }
      .count {
        color: #171726;
        font-size: 16px;
      }
      .name {
        color: #696975;
        font-size: 14px;
      }
      .q-knob-label {
        color: #171726;
        font-weight: 600;
      }
      .q-ma-md {
        margin: 6px;
      }
    }
    .active-color {
      color: #00d993;
    }
    .kyc-failed-color {
      color: #ff94d8;
    }
    .onboarded-color {
      color: #ffc200;
    }
    .initial-color {
      color: #0064ff;
    }
    .closed-color {
      color: rgba(#ff4852, 0.1);
    }
    .on-hold-color {
      color: #444450;
    }
  }
  @media screen and (max-width: 1140px) {
    #membersChart .member-status-item {
      display: block !important;
      .count-desc {
        display: block;
        max-width: 100%;
        margin-left: 10px;
      }
    }
  }
  @media screen and (max-width: 990px) {
    #membersChart .member-status-item {
      display: flex !important;
      .count-desc {
        display: inline-block;
        max-width: calc(100% - 82px);
        margin-left: 0;
      }
    }
  }
  @media screen and (max-width: 900px) {
    #membersChart .member-status-item {
      display: block !important;
      .count-desc {
        display: block;
        max-width: 100%;
        margin-left: 10px;
      }
    }
  }
  @media screen and (max-width: 575px) {
    #membersChart .member-status-item {
      display: flex !important;
      .count-desc {
        display: inline-block;
        max-width: calc(100% - 82px);
        margin-left: 0;
      }
    }
  }
  .chart-item {
    position: relative;
  }
  .loading {
    height: 307px;
    line-height: 307px;
    width: calc(100% - 32px);
    text-align: center;
    position: absolute;
    top: 0;
    z-index: 100;
    background: rgba($color: #000000, $alpha: 0.1);
  }
}
</style>
