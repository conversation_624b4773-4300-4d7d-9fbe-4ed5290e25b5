<template>
  <q-card id="promo">
    <q-card-title>
      <div class="row">
        <div class="chart-title">
          <p>Promo Record</p>
          <span class="font-mono">{{delta}}</span>
        </div>
        <q-btn class="promo-btn refresh-btn btn-sm ml-auto mr-8 square"
               @click="reloadData(true)"
               icon="mdi-refresh"></q-btn>
        <q-btn class="promo-btn down-btn btn-sm mr-8 square"
               @click="download()"
               icon="mdi-file-document-outline"></q-btn>
      </div>
    </q-card-title>
    <q-card-main class="chart-item">
      <div id="promoChart"></div>
      <div v-if="visible"
           class="loading">
        <q-spinner-hourglass color="primary"
                             size="3em"
                             :thickness="2" />
      </div>
    </q-card-main>
  </q-card>
</template>

<script>
import echarts from 'echarts'
import $ from 'jquery'
import { request, EventHandlerMixin, notify } from '../../../common'
import html2canvas from 'html2canvas'

export default {
  props: {
    'value': {
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  watch: {
    value () {
      this.getData()
    }
  },
  mixins: [
    EventHandlerMixin('reload-promo-chart', 'getData')
  ],
  data () {
    return {
      delta: 0,
      visible: false,
      chart: null,
      force: false,
      xAxis: [],
      series: [
        {
          name: 'Free Transfer',
          type: 'line',
          color: '#2ddb96',
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0, color: 'rgba(45, 219, 150, 1)'
                },
                {
                  offset: 0.7, color: 'rgba(75, 205, 62, 0.1)'
                },
                {
                  offset: 1, color: 'rgba(255, 255, 255, 0)'
                }
              ]
            }
          },
          data: []
        },
        {
          name: 'Free Bank',
          type: 'line',
          color: '#fc5a5a',
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0, color: 'rgba(252, 90, 90, 1)'
                },
                {
                  offset: 0.7, color: 'rgba(252, 90, 90, 0.1)'
                },
                {
                  offset: 1, color: 'rgba(255, 255, 255, 0)'
                }
              ]
            }
          },
          data: []
        },
        {
          name: 'Free Cash Pickup',
          type: 'line',
          color: '#9c85ca',
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0, color: 'rgba(156, 133, 202, 1)'
                },
                {
                  offset: 0.7, color: 'rgba(156, 133, 202, 0.1)'
                },
                {
                  offset: 1, color: 'rgba(255, 255, 255, 0)'
                }
              ]
            }
          },
          data: []
        }
      ]
    }
  },
  async mounted () {
    this.getData()
  },
  methods: {
    resize () {
      this.chart.resize()
    },
    async reloadData (force = false) {
      this.force = force
      this.getData()
    },
    async getData () {
      this.visible = true
      const resp = await request(`/admin/mex/promoChart`, 'get', { 'force': this.force ? 1 : 0, 'period': this.value })
      if (resp.success) {
        this.series[0].data = Object.values(resp.data.chartCount.freeTransfer)
        this.series[1].data = Object.values(resp.data.chartCount.freeBank)
        this.series[2].data = Object.values(resp.data.chartCount.freeCashPickup)
        this.xAxis = resp.data.key
        this.delta = resp.data.total
        this.initChart(resp.data)
      }
      this.visible = false
    },
    initChart (data) {
      this.chart = echarts.init(document.getElementById('promoChart'), 'primary')
      let that = this
      let colorList = ['#00d893', '#fc5a5a', '#9c85ca']
      let labelList = ['Free Transfer', 'Free Bank', 'Free Cash Pickup']
      let positionStr = ''
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          backgroundColor: '#ffffff',
          position: function (pos, params, dom, rect, size) {
            // 鼠标在左侧时 tooltip 显示到右侧，鼠标在右侧时 tooltip 显示到左侧。
            let obj = { top: pos[1] - 270 }
            if (pos[0] < size.viewSize[0] / 2) {
              obj['left'] = pos[0] - 50
              positionStr = 'left'
            } else {
              positionStr = 'right'
              obj['right'] = size.viewSize[0] - pos[0] - 50
            }
            return obj
          },
          padding: 0,
          formatter: function (params) {
            let endValue = that.chart.getOption().dataZoom[0].endValue
            let startValue = that.chart.getOption().dataZoom[0].startValue
            return '<div class="tooltip-area-' + positionStr + '" style="box-shadow: 0 5px 15px 0 rgba(28, 20, 70, 0.1);border-radius:20px;padding:30px;padding-top:16px;font-family: Poppins;font-size: 14px;"><p style="text-align:center;margin:0px;color:#231f20;font-weight: 600;">' + that.titleStr(params[0]['name']) + '</p>' +
              '<p style="text-align:center;margin:0px;color:#231f20;font-weight: 600;">Date ranges: ' + that.titleStr(that.xAxis[startValue], true) + ' ~ ' + that.titleStr(that.xAxis[endValue], true) + '</p>' +
              '<table style="min-width:500px;">' +
              '<tr style="color:#231f20;width:100%;">' +
              '<th style="text-align:left;font-weight: 600;">Promo Type</th><th style="text-align:right;">Total</th><th style="text-align:right;">Used</th>' +
              '</tr>' +
              '<tr style="color:#414141">' +
              '<td style="color:' + colorList[0] + ';font-weight: 600;">' + labelList[0] + '</td><td style="text-align: right;font-family: monospace;font-weight: 600;">' + data.chartCount['freeTransfer'][params[0]['name']] + '</td><td style="text-align: right;font-family: monospace;font-weight: 600;">' + data.chartUsed['freeTransfer'][params[0]['name']] + '</td>' +
              '</tr>' +
              '<tr style="color:#414141">' +
              '<td style="color:' + colorList[1] + ';font-weight: 600;">' + labelList[1] + '</td><td style="text-align: right;font-family: monospace;font-weight: 600;">' + data.chartCount['freeBank'][params[0]['name']] + '</td><td style="text-align: right;font-family: monospace;font-weight: 600;">' + data.chartUsed['freeBank'][params[0]['name']] + '</td>' +
              '</tr>' +
              '<tr style="color:#414141">' +
              '<td style="color:' + colorList[2] + ';font-weight: 600;">' + labelList[2] + '</td><td style="text-align: right;font-family: monospace;font-weight: 600;">' + data.chartCount['freeCashPickup'][params[0]['name']] + '</td><td style="text-align: right;font-family: monospace;font-weight: 600;">' + data.chartUsed['freeCashPickup'][params[0]['name']] + '</td>' +
              '</tr>' +
              '</table>' +
              '</div>'
          }
        },
        legend: {
          bottom: '0px',
          icon: 'pin',
          data: ['Free Transfer', 'Free Bank', 'Free Cash Pickup']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '60px',
          containLabel: true
        },
        axisLabel: {
          color: '#999',
          fontSize: 10,
          formatter: v => {
            if (v >= 1000 || v < -1000) {
              return (v / 1000) + 'k'
            }
            return v
          }
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: this.xAxis
          }
        ],
        dataZoom: {
          bottom: '25px',
          fillerColor: 'rgba(0, 222,0, 0.5)',
          handleStyle: {
            color: 'rgba(0, 222,0, 0.5)'
          },
          moveHandleStyle: {
            color: 'rgba(0, 222,0, 0.5)'
          }
        },
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: false
            }
          }
        ],
        series: this.series
      }
      this.chart.setOption(option)

      $(window).on('resize', this.resize)
    },
    titleStr (str = '', range = false) {
      let title = str.split('-')
      let month = {
        '01': 'Jan.',
        '02': 'Feb.',
        '03': 'Mar.',
        '04': 'Apr.',
        '05': 'May',
        '06': 'Jun.',
        '07': 'Jul.',
        '08': 'Aug.',
        '09': 'Sept.',
        '10': 'Oct.',
        '11': 'Nov.',
        '12': 'Dec.'
      }
      let bigMonth = ['01', '03', '05', '07', '08', '10', '12']
      let smallMonth = ['04', '06', '09', '11']
      // let days = ['01', '02', '03', '21', '22', '23', '31']
      if (title.length === 3) {
        if (title[2].slice(-1) === '1' && title[2] !== '11') {
          return month[title[1]] + ' ' + title[2] + 'st, ' + title[0]
        } else if (title[2].slice(-1) === '2' && title[2] !== '12') {
          return month[title[1]] + ' ' + title[2] + 'nd, ' + title[0]
        } else if (title[2].slice(-1) === '3' && title[2] !== '13') {
          return month[title[1]] + ' ' + title[2] + 'rd, ' + title[0]
        } else {
          return month[title[1]] + ' ' + title[2] + 'th, ' + title[0]
        }
      } else if (title.length === 2) {
        if (range) {
          return this.value === 'week' ? 'Week ' + title[1] + ', ' + title[0] : month[title[1]] + ', ' + title[0]
        } else {
          let end = '29th'
          if (bigMonth.indexOf(title[1]) !== -1) {
            end = '31st'
          } else if (smallMonth.indexOf(title[1]) !== -1) {
            end = '30th'
          } else if (title[0] % 4) {
            end = '28th'
          }
          return this.value === 'week' ? 'Week ' + title[1] + ' Sun., ' + title[0] + ' - ' + 'Week ' + title[1] + ' Sat., ' + title[0] : month[title[1]] + ' ' + '01st, ' + title[0] + ' - ' + month[title[1]] + ' ' + end + ', ' + title[0]
        }
      } else if (title.length === 1) {
        if (range) {
          return title[0]
        } else {
          return month['01'] + ' 01st, ' + title[0] + ' - ' + month['12'] + '31st, ' + title[0]
        }
      }
    },
    download () {
      let userAgent = navigator.userAgent
      if (userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Chrome') === -1) {
        return notify('Please take a screenshot manually or use another browser to open the website to save the picture!', 'negative')
      }
      let target = null
      let name = 'promoChart'
      target = document.getElementById('promoChart')
      this.$q.loading.show()
      const targetCss = window.getComputedStyle(target)

      const options = {
        scale: 2,
        width: parseInt(targetCss.width, 10),
        height: parseInt(targetCss.height, 10),
        y: target.offsetTop + 70
      }
      console.log(options)
      html2canvas(target, options).then(canvas => {
        // canvas is the final rendered <canvas> element
        const imgData = canvas.toDataURL('image/jpeg')
        console.log(imgData)
        this.fileDownload(imgData, name)
        this.$q.loading.hide()
      })
    },
    fileDownload (downloadUrl, filename) {
      let aLink = document.createElement('a')
      aLink.style.display = 'none'
      aLink.href = downloadUrl
      aLink.download = `${filename}`
      // 触发点击-然后移除
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    }
  }
}
</script>
<style lang="scss">
#promo {
  .chart-title {
    p {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
    span {
      font-size: 28px;
      font-weight: 600;
    }
  }
  .promo-btn {
    // background: #0064ff !important;
    color: #fff;
    max-height: 32px;
    max-width: 32px;
  }
  .refresh-btn {
    color: var(--q-color-primary);
  }
  .report-btn {
    background: #0062ff;
  }
  .down-btn {
    background: #00de00;
  }
  #promoChart {
    height: 400px;
  }
  .chart-item {
    position: relative;
  }
  .loading {
    height: 400px;
    line-height: 400px;
    width: calc(100% - 32px);
    text-align: center;
    position: absolute;
    top: 0;
    z-index: 100;
    background: rgba($color: #000000, $alpha: 0.1);
  }
  .tooltip-area-left :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    left: 40px;
  }
  .tooltip-area-right :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    right: 40px;
  }
}
</style>
