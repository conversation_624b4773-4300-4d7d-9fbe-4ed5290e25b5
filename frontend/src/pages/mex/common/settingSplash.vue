<template>
  <q-dialog class="mex-setting-splash-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Setting Splash for {{type}}</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-6">
          <q-select float-label="Splash Page"
                    autocomplete="no"
                    :options="countries"
                    filter
                    autofocus-filter
                    :error="$v.entity['SplashId'].$error"
                    @blur="$v.entity['SplashId'].$touch"
                    v-model="entity['SplashId']"></q-select>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel"
             no-caps
             color="grey-3"
             text-color="tertiary"
             @click="_hide" />
      <q-btn label="'Setting'"
             no-caps
             color="positive"
             class="main"
             @click="save" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request } from '../../../common'
import _ from 'lodash'
import MexStateListMixin from '../../../mixins/mex/MexStateListMixin'
import { required } from 'vuelidate/lib/validators'

const validations = {
  entity: {}
}
for (const field of [
  'SplashId'
]) {
  validations.entity[field] = { required }
}

export default {
  name: 'mex-setting-splash-dialog',
  mixins: [
    Singleton,
    MexStateListMixin
  ],
  data () {
    return {
      defaultEntity: {},
      type: '',
      ids: []
    }
  },
  computed: {},
  validations,
  methods: {
    show (data) {
      this.type = data.type
      this.ids = data.ids
    },
    onSuccess (resp) {
      notify(resp.message)

      if (this.origin && this.origin['Employer ID']) {
        _.assignIn(this.origin, resp.data)
      }

      this.$root.$emit('reload-mex-employers')
      this._hide()
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      const resp = await request(`/admin/mex/employers/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    }
  }
}
</script>

<style lang="scss">
.mex-employer-detail-dialog {
  .modal-content {
    width: 580px;
  }
}
</style>
