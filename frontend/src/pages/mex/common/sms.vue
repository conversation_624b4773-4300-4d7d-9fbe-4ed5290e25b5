<template>
  <q-dialog
    class="mex-common-sms-dialog"
    v-model="visible"
    prevent-close
  >
    <template slot="title">
      <div class="mb-5">
        <div class="radius-box">
          <q-icon name="mdi-cellphone-android" class="font-22"></q-icon>
        </div>
      </div>
      <div class="font-16 mb-2">Two Factor Authentication</div>
      <div class="font-12 normal text-dark">Please enter the {{ chars.length }} digit code sent to:</div>
      <div class="font-14 normal text-positive bold mv-5" notranslate="">{{ user.mobilePhone || user.phone || user.mobile }}</div>
      <div class="font-12 normal text-dark">This code expires in 10 minutes.</div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <div class="row justify-center mb-15">
        <q-input v-model="chars[i]" class="narrow"
                 :ref="`input_${i}`"
                 @keyup="onInput(i, $event)"
                 v-for="(v, i) in chars" :key="i"></q-input>
      </div>
      <div class="font-12 mb-10 mh-20 text-faded">
        Didn't receive a code?
        <a href="javascript:" @click="init(true)">Resend code</a> or enter the app auth code if you've set it up
      </div>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel" no-caps
             color="grey-3"
             text-color="tertiary"
             @click="_hide" />
      <q-btn label="Confirm"
             no-caps
             :disable="$v.$invalid"
             color="positive" class="main"
             @click="submit" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, ref, request } from '../../../common'
import _ from 'lodash'

export default {
  name: 'mex-common-sms-dialog',
  mixins: [
    Singleton
  ],
  props: {
    callback: {
      type: Function,
      required: true
    },
    who: {
      type: Object
    },
    route: {
      type: String,
      default: '/admin/mex/user/sms'
    },
    params: {
      type: Object,
      default: () => {
        return {}
      }
    },
    smsKey: {
      type: String
    }
  },
  data () {
    return {
      chars: []
    }
  },
  computed: {
    sms () {
      return this.chars.join('').trim()
    },
    user () {
      return this.who || this.$store.state.User
    }
  },
  validations: {
    chars: {
      valid () {
        return this.sms && this.sms.length === this.chars.length
      }
    }
  },
  methods: {
    reset () {
      this.chars = []
      for (let i = 0; i < 6; i++) {
        this.chars.push('')
      }
    },
    async show (arg) {
      this.reset()
      this.visible = false

      if (arg && (arg.smsKey || this.smsKey) && this.smsKey !== arg.smsKey) {
        return
      }

      if (this.user.pinToken && this.callback) {
        return this.callback(this.origin, this.user.pinToken)
      }

      const resp = await this.init()
      if (resp.success) {
        this.visible = true
      }
    },
    async init (msg) {
      this.$q.loading.show({
        message: 'Sending the unlock code'
      })
      const resp = await request(this.route, 'post', this.params)
      this.$q.loading.hide()
      if (msg && resp.success) {
        notify()
      }
      return resp
    },
    async submit () {
      if (!this.sms || this.sms.length < this.chars.length) {
        return
      }
      this.$q.loading.show({
        message: 'Verifying...'
      })
      const resp = await request(`${this.route}-verify`, 'post', _.assignIn(this.params, {
        code: this.sms
      }))
      this.$q.loading.hide()
      if (resp.success) {
        this.$store.dispatch('User/setPinToken', resp.data)
        if (this.callback) {
          this.callback(this.origin, resp.data)
        }
        this._hide()
      }
    },
    filledChars () {
      if (!this.sms) {
        return -1
      }
      const chars = this.sms.split('')
      let pos = 0
      let update = false
      for (let i = 0; i < this.chars.length; i++) {
        if (this.chars[i] !== (chars[i] || '')) {
          update = true
        }
        this.chars[i] = chars[i] || ''
        if (this.chars[i]) {
          pos = i + 1
        }
      }
      if (update) {
        this.$forceUpdate()
      }
      return pos
    },
    onInput (index, evt) {
      if (evt.key === 'Backspace') {
        if (index > 0) {
          const input = ref(this.$refs[`input_${index - 1}`])
          input && input.focus()
        }
      } else if (evt.key === 'Enter') {
        if (index >= this.chars.length - 1) {
          this.submit()
        }
      } else {
        let filled = this.filledChars()
        if (filled >= this.chars.length) {
          filled = this.chars.length - 1
        }
        const input = ref(this.$refs[`input_${filled}`])
        input && input.focus()
      }
    }
  },
  mounted () {
    this.reset()
  }
}
</script>

<style lang="scss">
.mex-common-sms-dialog {
  .modal-content {
    width: 400px;

    .q-if.narrow {
      width: 42px;
      font-size: 22px;

      + .q-if.narrow {
        margin-left: 8px;
      }

      input {
        text-align: center;
      }
    }
  }
}
</style>
