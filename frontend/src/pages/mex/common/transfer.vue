<template>
  <div id="transfersGraph">
    <q-card>
      <q-card-title>
        <div class="row">
          <div class="chart-title">
            <p>Transfer</p>
            <span class="font-mono">{{total | moneyFormat}}<q-chip dense
                      :class="delta >= 0 ? 'positive' : 'negative'">{{ delta | percent(1, true) }}</q-chip></span>
            <p class="avg-area font-mono">{{avg | moneyFormat}} per Transfer</p>
          </div>
          <q-btn class="transfers-btn refresh-btn btn-sm ml-auto mr-8 square"
                 @click="reloadData()"
                 icon="mdi-refresh"></q-btn>
          <q-btn class="transfers-btn down-btn btn-sm mr-8 square"
                 @click="download()"
                 icon="mdi-file-document-outline"></q-btn>
          <q-btn class="transfers-btn report-btn btn-sm square"
                 @click="viewReport()"
                 icon="mdi-chart-line"></q-btn>
        </div>
      </q-card-title>
      <q-card-main class="chart-item">
        <div id="transfersChart"></div>
        <div v-if="visible"
             class="loading">
          <q-spinner-hourglass color="primary"
                               size="3em"
                               :thickness="2" />
        </div>
      </q-card-main>
    </q-card>
  </div>
</template>

<script>
import echarts from 'echarts'
import $ from 'jquery'
import { request, EventHandlerMixin, moneyFormat, notify } from '../../../common'
import html2canvas from 'html2canvas'
import moment from 'moment'

export default {
  components: {
  },
  props: {
    'value': {
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  watch: {
    value () {
      this.getData()
    }
  },
  mixins: [
    EventHandlerMixin('reload-transfers-chart', 'getData')
  ],
  data () {
    return {
      total: 0,
      delta: 0,
      avg: 0,
      seriesData: {
        'Mexico Bank Transfer': [],
        'Rapyd Cash Pickup': [],
        'UniTeller Cash Pickup': []
      },
      force: false,
      visible: false,
      chart: null,
      series: [
        {
          name: 'Mexico Bank Transfer',
          type: 'bar',
          stack: 'total',
          color: '#00de00',
          barWidth: '40px',
          barGap: '-100%',
          itemStyle: {
            // barBorderRadius: [0, 20, 20, 0]
          },
          zlevel: 3,
          label: {
            show: false
          },
          emphasis: {
            focus: 'series'
          },
          data: [220]
        },
        {
          name: 'Rapyd Cash Pickup',
          type: 'bar',
          stack: 'total',
          color: '#ff974a',
          barWidth: '40px',
          barGap: '-100%',
          zlevel: 2,
          barMinHeight: 2,
          itemStyle: {
          //  barBorderRadius: [0, 20, 20, 0]
          },
          label: {
            show: false
          },
          emphasis: {
            focus: 'series'
          },
          data: [440]
        },
        {
          name: 'UniTeller Cash Pickup',
          type: 'bar',
          stack: 'total',
          color: '#9c85ca',
          barWidth: '40px',
          barGap: '-100%',
          zlevel: 2,
          barMinHeight: 2,
          itemStyle: {
          //  barBorderRadius: [0, 20, 20, 0]
          },
          label: {
            show: false
          },
          emphasis: {
            focus: 'series'
          },
          data: [440]
        }
      ],
      startTime: moment().startOf('month'),
      endTime: moment().endOf('month')
    }
  },
  async mounted () {
    this.getData()
  },
  methods: {
    resize () {
      this.chart.resize()
    },
    async reloadData () {
      this.force = true
      this.getData()
    },
    viewReport () {
      this.$router.push(`/j/mex/transfers`)
    },
    async getData () {
      this.visible = true
      switch (this.value) {
        case 'all':
          this.startTime = null
          this.endTime = null
          break
        case 'week':
          this.startTime = moment().startOf('week')
          this.endTime = moment().endOf('week')
          break
        case 'month':
          this.startTime = moment().startOf('month')
          this.endTime = moment().endOf('month')
          break
        case 'quarter':
          this.startTime = moment().startOf('quarter')
          this.endTime = moment().endOf('quarter')
          break
        case 'year':
          this.startTime = moment().startOf('year')
          this.endTime = moment().endOf('year')
          break
        case 'today':
          this.startTime = this.startDate
          this.endTime = this.endDate
          break
      }
      const resp = await request(`/admin/mex/transfersChart`, 'get', {
        'force': this.force ? 1 : 0,
        'period': this.value,
        'start': this.startTime ? moment(this.startTime).format('L') : null,
        'end': this.startTime ? moment(this.endTime).format('L') : null
      })
      if (resp.success) {
        this.dataList = resp.data
        this.delta = resp.data.pre
        this.total = resp.data.total
        this.avg = resp.data.avg
        this.series[0].data = [resp.data.bank.amount / 100]
        this.series[1].data = resp.data.cash.amount ? [resp.data.cash.amount / 100] : 0
        this.series[2].data = resp.data.uniteller.amount ? [resp.data.uniteller.amount / 100] : 0
        this.seriesData['Mexico Bank Transfer'] = resp.data.bank
        this.seriesData['Rapyd Cash Pickup'] = resp.data.cash
        this.seriesData['UniTeller Cash Pickup'] = resp.data.uniteller
        this.initChart()
      }
      this.visible = false
    },
    initChart () {
      this.chart = echarts.init(document.getElementById('transfersChart'), 'primary')
      let that = this
      let positionStr = ''
      let option = {
        legend: {
          bottom: '0px',
          icon: 'pin',
          data: ['Mexico Bank Transfer', 'Rapyd Cash Pickup', 'UniTeller Cash Pickup']
        },
        tooltip: {
          trigger: 'item',
          // alwaysShowContent: true,
          // axisPointer: {
          //   type: 'cross',
          //   label: {
          //     backgroundColor: '#6a7985'
          //   }
          // },
          backgroundColor: '#ffffff',
          position: function (pos, params, dom, rect, size) {
            // 鼠标在左侧时 tooltip 显示到右侧，鼠标在右侧时 tooltip 显示到左侧。
            let obj = { top: pos[1] - 180 }
            if (pos[0] < size.viewSize[0] / 2) {
              obj['left'] = pos[0] - 50
              positionStr = 'left'
            } else {
              positionStr = 'right'
              obj['right'] = size.viewSize[0] - pos[0] - 50
            }
            // console.log(obj)
            return obj
          },
          padding: 0,
          formatter: function (params) {
            // console.log('234567uijhgfd')
            // console.log(params)
            let total = 0
            let amount = 0
            let avgAmount = 0
            if (params['seriesName'] === 'Rapyd Cash Pickup') {
              total = that.dataList.cash.total
              amount = that.dataList.cash.amount
              avgAmount = that.dataList.cash.avgAmount
            } else if (params['seriesName'] === 'UniTeller Cash Pickup') {
              total = that.dataList.uniteller.total
              amount = that.dataList.uniteller.amount
              avgAmount = that.dataList.uniteller.avgAmount
            } else {
              total = that.dataList.bank.total
              amount = that.dataList.bank.amount
              avgAmount = that.dataList.bank.avgAmount
            }
            return '<div class="tooltip-area-' + positionStr + '" style="color:#1c1446;font-weight: 600;box-shadow: 0 5px 15px 0 rgba(28, 20, 70, 0.1);border-radius:4px;padding:30px;padding-top:16px;font-family: Poppins;font-size: 14px;"><p style="text-align:center;margin:0px;">' + params['seriesName'] + '</p>' +
              '<div style="line-height:30px;min-width:215px;display:flex;justify-content:space-between;color:#231f20;">Total($)<span style="color:#696975;font-weight: 600;text-align: right;font-family: monospace;">' + moneyFormat(amount) + '</span></div>' +
              '<div style="line-height:30px;min-width:215px;display:flex;justify-content:space-between;color:#231f20;">#of Transfer<span style="color:#696975;font-weight: 600;text-align: right;font-family: monospace;">' + total + '</span></div>' +
              '<div style="line-height:30px;min-width:215px;display:flex;justify-content:space-between;color:#231f20;">Avg.<span style="color:#696975;font-weight: 600;text-align: right;font-family: monospace;">' + moneyFormat(avgAmount) + '</span></div>' +
              '</div>'
          }
        },
        xAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          axisLabel: {
            color: '#999',
            fontSize: 10,
            formatter: v => {
              if (v >= 1000) {
                return (v / 1000) + 'k'
              }
              return v
            }
          }
        },
        grid: {
          top: '10px',
          left: '3%',
          right: '4%',
          bottom: '60px',
          containLabel: true
        },
        yAxis: {
          type: 'category',
          show: false
        },
        series: this.series
      }
      this.chart.setOption(option)

      $(window).on('resize', this.resize)
      this.chart.on('legendselectchanged', e => {
        let total = 0
        let count = 0
        let preAmount = 0
        let legendList = ['Mexico Bank Transfer', 'Rapyd Cash Pickup', 'UniTeller Cash Pickup']
        let _this = this
        legendList.forEach(function (item) {
          if (e.selected[item]) {
            total += _this.seriesData[item].amount * 100
            count += _this.seriesData[item].total * 1
            preAmount += _this.seriesData[item].preAmount * 100
          }
        })
        this.total = total ? total / 100 : 0
        this.avg = total ? (total / count) / 100 : 0
        this.delta = preAmount ? 1 * (total / preAmount - 1) : 1
      })
    },
    download () {
      let userAgent = navigator.userAgent
      if (userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Chrome') === -1) {
        return notify('Please take a screenshot manually or use another browser to open the website to save the picture!', 'negative')
      }
      let target = null
      let name = 'transfersGraph'
      target = document.getElementById('transfersGraph')
      this.$q.loading.show()
      const targetCss = window.getComputedStyle(target)

      const options = {
        scale: 2,
        width: parseInt(targetCss.width, 10),
        height: parseInt(targetCss.height, 10),
        y: target.offsetTop + 70
      }
      console.log(options)
      html2canvas(target, options).then(canvas => {
        // canvas is the final rendered <canvas> element
        const imgData = canvas.toDataURL('image/jpeg')
        console.log(imgData)
        this.fileDownload(imgData, name)
        this.$q.loading.hide()
      })
    },
    fileDownload (downloadUrl, filename) {
      let aLink = document.createElement('a')
      aLink.style.display = 'none'
      aLink.href = downloadUrl
      aLink.download = `${filename}`
      // 触发点击-然后移除
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    }
  }
}
</script>
<style lang="scss">
#transfersGraph {
  margin: 0 !important;
  .transfers-btn {
    max-height: 32px !important;
    color: #fff;
    text-transform: capitalize;
  }
  .avg-area {
    margin: 0;
    font-size: 7px;
    color: #787393;
    line-height: 7px;
  }
  .refresh-btn {
    color: var(--q-color-primary);
  }
  .report-btn {
    background: #0062ff;
  }
  .down-btn {
    background: #00de00;
  }
  .chart-title {
    p {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
    span {
      font-size: 28px;
      font-weight: 600;
    }
  }

  #transfersChart {
    height: 300px;
  }
  .chart-item {
    position: relative;
  }
  .loading {
    height: 300px;
    line-height: 300px;
    width: calc(100% - 32px);
    text-align: center;
    position: absolute;
    top: 0;
    z-index: 100;
    background: rgba($color: #000000, $alpha: 0.1);
  }
  .tooltip-area-left :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    left: 40px;
  }
  .tooltip-area-right :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    right: 40px;
  }
}
</style>
