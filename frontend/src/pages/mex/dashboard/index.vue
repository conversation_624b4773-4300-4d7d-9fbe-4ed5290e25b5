<template>
  <q-page class="mex__dashboard__index_page">
    <div class="page-header">
      <div class="title">Dashboard Overview</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3  col-xs-6"
             v-for="(n, i) in numbers"
             :key="i">
          <q-card>
            <q-card-title>
              <span>{{ n.title }}</span>
              <q-chip dense
                      v-if="n.delta !== null"
                      class="font-mono"
                      :class="n.delta >= 0 ? 'positive' : 'negative'">{{ n.delta | percent(1, true) }}</q-chip>
            </q-card-title>
            <q-card-main>
              <div v-if="['Partner Revenue'].includes(n.title)"
                   class="font-22 heavy mb-10 font-mono">{{ n.value || 0 | moneyFormat}}</div>
              <div v-else
                   class="font-22 heavy mb-10 font-mono">{{ n.value || 0 }}</div>
              <!-- <a :href="n.url"
                 class="link bold font-12">View Report <i class="mdi mdi-arrow-right"></i></a> -->
            </q-card-main>
          </q-card>
        </div>

        <div class="col-sm-12 col-xs-12">
          <ProgramBalance :viewReportFlag="true"
                          v-model="chartDateRange"></ProgramBalance>
        </div>

        <div class="col-md-6 col-xs-12">
          <MembersStatus v-model="chartDateRange"></MembersStatus>
        </div>
        <div class="col-md-6 col-xs-12">
          <Transfers v-model="chartDateRange"></Transfers>
        </div>
        <div class="col-md-6 col-xs-12">
          <Employers v-model="chartDateRange"></Employers>
        </div>
        <div class="col-md-6  col-xs-12">
          <KYCPerformance :viewReportFlag="true"
                          v-model="chartDateRange"></KYCPerformance>
        </div>
      </div>
    </div>

    <q-inner-loading :visible="loading">
      <q-spinner :size="50"></q-spinner>
    </q-inner-loading>
  </q-page>
</template>

<script>
import MexPageMixin from '../../../mixins/mex/MexPageMixin'
import ProgramBalance from '../common/programBalance'
import Transfers from '../common/transfer'
import { request } from '../../../common'
import Employers from '../common/employers'
import MembersStatus from '../common/members'
import KYCPerformance from '../common/kycPerformance'

export default {
  name: 'MexDashboard',
  mixins: [
    MexPageMixin
  ],
  components: {
    ProgramBalance,
    Transfers,
    Employers,
    MembersStatus,
    KYCPerformance
  },
  data () {
    return {
      loading: false,
      numbers: [
        { title: 'Active Members', delta: 0, value: 0, url: 'javascript:' },
        { title: 'Active Card', delta: 0, value: 0, url: 'javascript:' },
        { title: 'Transfers', delta: 0, value: 0, url: 'javascript:' },
        { title: 'Partner Revenue', delta: 0, value: 0, url: 'javascript:' },
        { title: 'Rapyd Balance', delta: null, value: 0, url: 'javascript:' },
        { title: 'UniTeller Balance', delta: null, value: 0, url: 'javascript:' }
      ],
      chartDateRange: 'all',
      dateRange: 'all'
    }
  },
  methods: {
    async reload () {
      let data = {}
      if (this.$refs && this.$refs.dateRangeFilter) {
        data = this.$refs.dateRangeFilter.params()
      }
      this.chartDateRange = data.period
      this.$root.$emit('admin-reload-dashboard')
      const resp = await request(`/admin/mex/dashboard/static`, 'get', data)
      if (resp.success) {
        // member
        this.numbers[0].value = resp.data.memberCount
        this.numbers[0].delta = resp.data.memberPre
        // member card
        this.numbers[1].value = resp.data.memberCardCount
        this.numbers[1].delta = resp.data.memberCardPre
        // transfer
        this.numbers[2].value = resp.data.transferCount
        this.numbers[2].delta = resp.data.transferPre
        // revenue
        this.numbers[3].value = resp.data.revenueTotal
        this.numbers[3].delta = resp.data.revenuePre
        // Rapyd balance
        this.numbers[4].value = resp.data.rapydBalance
        this.numbers[4].delta = null
        // UniTeller balance
        this.numbers[5].value = resp.data.unitellerBalance
        this.numbers[5].delta = null
      }
    }
  },
  async mounted () {
    this.reload()
  }
}
</script>
