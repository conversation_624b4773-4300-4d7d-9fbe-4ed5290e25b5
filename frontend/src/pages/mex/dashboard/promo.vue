<template>
  <q-page class="mex__promo__index_page">
    <div class="page-header">
      <div class="title">Promo Overview</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-4  col-xs-6"
             v-for="(n, i) in numbers"
             :key="i">
          <q-card>
            <q-card-title>
              <span>{{ n.title }}</span>
            </q-card-title>
            <q-card-main v-if="n.title == 'Free Money Transfer'">
              <div class="font-22 heavy mb-10 font-mono">{{ quick.freeTransfer || 0 }}</div>
              <div class="font-14 item-count mb-10">
                <span>
                  {{ quick.freeTransferUsed || 0}}</span>
                {{n.sub}}
              </div>
            </q-card-main>
            <q-card-main v-else-if="n.title == 'Free Bank'">
              <div class="font-22 heavy mb-10 font-mono">{{ quick.freeBank || 0 }}</div>
              <div class="font-14 item-count mb-10">
                <span>
                  {{ quick.freeBankUsed || 0}}</span>
                {{n.sub}}
              </div>
            </q-card-main>
            <q-card-main v-else-if="n.title == 'Free Cash Pickup'">
              <div class="font-22 heavy mb-10 font-mono">{{ quick.freeCashPickup || 0 }}</div>
              <div class="font-14 item-count mb-10">
                <span>
                  {{ quick.freeCashPickupUsed || 0}}</span>
                {{n.sub}}
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <div class="row gutter-sm mt-16">
        <div class="col-sm-12">
          <PromoRecordChart v-model="chartDateRange"></PromoRecordChart>
        </div>
        <div class="col-md-6 col-xs-12">
          <PromoChart v-model="chartDateRange"></PromoChart>
        </div>
        <div class="col-md-6 col-xs-12">
          <topPromoEmployer v-model="chartDateRange"></topPromoEmployer>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Promo History</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field === 'Member ID'">
              <span @click="goToMember(props.row)"
                    class="account-id">{{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else-if="col.field === 'Status'">
              <q-chip v-if="props.row['Status'] === 'init'"
                      class="font-12 status-item positive">
                Not In Use
              </q-chip>
              <q-chip v-else-if="props.row['Status'] === 'cancelled'"
                      class="font-12 status-item gray">
                Cancelled
              </q-chip>
              <q-chip v-else
                      class="font-12 status-item blue">
                Used
              </q-chip>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import MexPageMixin from '../../../mixins/mex/MexPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import ForceReloadMixin from '../../../mixins/ForceReloadMixin'
import ListPageMixin from '../../../mixins/ListPageMixin'
import PromoRecordChart from '../common/promoChart'
import topPromoEmployer from '../common/topPromoEmployer'
import PromoChart from '../common/promo'

export default {
  name: 'MexPromo',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    ForceReloadMixin,
    EventHandlerMixin('reload-promo')
  ],
  components: {
    PromoChart,
    topPromoEmployer,
    PromoRecordChart
  },
  data () {
    return {
      loading: false,
      filtersUrl: `/admin/mex/promo/filters`,
      requestUrl: `/admin/mex/promo/list`,
      downloadUrl: `/admin/mex/promo/export`,
      keyword: '',
      numbers: [
        { title: 'Free Money Transfer', sub: 'used' },
        { title: 'Free Bank', sub: 'used' },
        { title: 'Free Cash Pickup', sub: 'used' }
      ],
      chartDateRange: 'all',
      dateRange: 'all',
      columns: generateColumns([
        'Promo Type', 'Date & time', 'First Name', 'Last Name', 'Member ID', 'Transfer ID',
        'Status'
      ]),
      filterOptions: [
        {
          value: 'filter[mp.type]',
          label: 'Promo Type',
          options: [
            { label: 'Free Bank', value: 'free_bank' },
            { label: 'Free Cash Pickup', value: 'free_cash_pickup' },
            { label: 'Free Transfer', value: 'free_transfer' }
          ]
        },
        {
          value: 'status',
          label: 'Status',
          options: [
            { label: 'Used', value: 'used' },
            { label: 'Not in use', value: 'init' },
            { label: 'Cancelled', value: 'cancelled' }
          ]
        },
        {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        },
        {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        },
        {
          value: 'filter[u.id=]',
          label: 'Memeber ID'
        },
        {
          value: 'date_range',
          label: 'Date & Time',
          range: [{
            value: 'range[mp.createdAt][start]',
            type: 'date'
          }, {
            value: 'range[mp.createdAt][end]',
            type: 'date'
          }]
        },
        {
          value: 'filter[e.id=]',
          label: 'Employer',
          options: [],
          source: 'employers'
        }
      ],
      autoLoad: true
    }
  },
  methods: {
    async reload () {
      let data = {}
      if (this.$refs && this.$refs.dateRangeFilter) {
        data = this.$refs.dateRangeFilter.params()
      }
      this.chartDateRange = data.period
      await this.request({
        pagination: this.pagination
      })
      this.everLoaded = true
    },
    goToMember (row) {
      const { href } = this.$router.resolve({
        path: `/j/mex/members/${row['Member ID']}`
      })
      window.open(href, '_blank')
    }
  },
  async mounted () {
    this.reload()
  }
}
</script>
<style lang="scss">
#mex__promo__index_page {
  .account-id {
    color: #7ac142;
    text-decoration-color: #7ac142;
    text-decoration-line: underline;
    cursor: pointer;
  }
}
</style>
