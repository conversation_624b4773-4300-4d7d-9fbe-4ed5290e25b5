<template>
  <q-card id="balanceChart"
          ref="balanceChart">
    <q-card-title>
      <div class="row">
        <div class="chart-title">
          <p>{{ employerPre }} Balance</p>
          <p>
            <span class="balance">{{ delta | moneyFormat}}</span>
            <span v-if="isBotm && oldRapidEmployer">
              Old: {{ rapidBalance | moneyFormat}}, New: {{ botmBalance | moneyFormat}}
            </span>
            <a href="javascript:"
               class="link ml-5 balance"
               @click="getData(true)">
              <q-icon :name="loading ? 'mdi-loading' : 'mdi-refresh'"
                      :class="{'mdi-spin': loading}">
              </q-icon>
            </a>
          </p>
          <q-tooltip v-if="isBotm"
                     class="note">
            There is some delay in the graph data, since the card providers records need to be synchronized, please refresh to check.
          </q-tooltip>
        </div>
        <q-btn v-if="viewReportFlag"
               class="chart-btn down-btn btn-sm ml-auto mr-8 square"
               @click="download('balanceChart')"><i class="mdi mdi-file-upload-outline"></i></q-btn>
        <q-btn v-if="viewReportFlag"
               class="chart-btn view-btn btn-sm square"
               @click="view()"><i class="mdi mdi-chart-bar"></i></q-btn>
      </div>
    </q-card-title>
    <q-card-main class="chart-item">
      <p style="min-height: 42px;"
         class="mb-0">
        <span style="color:#4acc3d">{{ employerPre }} Deposits</span>
        {{ deposits * 100 | moneyFormat}}
        <span style="color:#fc5a5a"> {{ textEmployee }} Payouts</span>
        {{ payouts * 100 | moneyFormat}}
        <span style="color:#ff974a"> Adjustment/Reversals</span>
        <span>{{ adjustment * 100 | moneyFormat}}</span>
      </p>
      <div id="employerBalance"></div>
      <div v-if="visible"
           class="loading">
        <q-spinner-hourglass color="primary"
                             size="3em"
                             :thickness="2" />
      </div>
    </q-card-main>
  </q-card>
</template>
<script>
import html2canvas from 'html2canvas'
import echarts from 'echarts'
import $ from 'jquery'
import { request, moneyFormat, EventHandlerMixin, notify } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'

export default {
  name: 'balanceChart',
  props: {
    'value': {
    },
    'viewReportFlag': {
      defalut: true
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  watch: {
    value () {
      this.getData()
    }
  },
  mixins: [
    MexPageMixin,
    EventHandlerMixin('reload-balanc-chart', 'getData')
  ],
  async mounted () {
    this.getData()
  },
  computed: {
    employerPre () {
      return this.$store.state.User.cpKey === 'cp_mex' ? 'Employer' : 'Client'
    }
  },
  data () {
    let type = this.$store.state.User.cpKey === 'cp_mex' ? 'Employee' : 'Member'
    let str = this.$store.state.User.cpKey === 'cp_mex' ? 'Employer' : 'Client'
    let seriesList = [
      {
        name: str + ' Deposits',
        type: 'line',
        color: '#2ddb96',
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0, color: 'rgba(45, 219, 150, 1)'
              },
              {
                offset: 0.7, color: 'rgba(75, 205, 62, 0.1)'
              },
              {
                offset: 1, color: 'rgba(255, 255, 255, 0)'
              }
            ]
          }
        },
        data: []
      },
      {
        name: type + ' Payouts',
        type: 'line',
        color: '#fc5a5a',
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0, color: 'rgba(252, 90, 90, 1)'
              },
              {
                offset: 0.7, color: 'rgba(252, 90, 90, 0.1)'
              },
              {
                offset: 1, color: 'rgba(255, 255, 255, 0)'
              }
            ]
          }
        },
        data: []
      }
    ]
    // if (this.$store.state.User.cpKey === 'cp_mex') {
    seriesList.push({
      name: 'Adjustment/Reversals',
      type: 'line',
      color: '#ff974a',
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0, color: 'rgba(255, 151, 74, 1)'
            },
            {
              offset: 0.7, color: 'rgba(255, 151, 74, 0.1)'
            },
            {
              offset: 1, color: 'rgba(255, 255, 255, 0)'
            }
          ]
        }
      },
      data: []
    })
    // }
    seriesList.push(
      {
        name: this.$store.state.User.cpKey === 'cp_mex' ? 'Employer Balance' : 'Client Balance',
        type: 'line',
        color: '#0062ff',
        lineStyle: {
          type: 'dotted'
        },
        data: []
      }
    )
    return {
      delta: 0,
      loading: false,
      deposits: 0,
      payouts: 0,
      adjustment: 0,
      visible: false,
      chart: null,
      xAxis: [],
      chartHeight: 0,
      series: seriesList,
      isBotm: false,
      botmBalance: 0,
      rapidBalance: 0,
      oldRapidEmployer: false
    }
  },
  methods: {
    view () {
      window.open(`/admin#/h/mex/employer/payments`, '_blank')
    },
    heightResize (height) {
      this.chartHeight = height
      if (this.chart) {
        this.chart.resize({ height: height })
      }
    },
    resize () {
      this.chart.resize()
    },
    async refreshAgentBalance (row) {
      this.loading = true
      const resp = await request(`/admin/mex/employer/refresh-agent-balance`, 'get')
      this.loading = false
      if (resp.success) {
        this.delta = resp.data
      }
    },
    async getData (isRefresh = false) {
      // this.autoHeight = this.$refs.balanceChart.offsetHeight - 100
      this.visible = true
      const resp = await request(`/admin/mex/employer/employerBalanceChart`, 'get', { 'period': this.value, isRefresh: isRefresh })
      if (resp.success) {
        console.log(this.series)
        this.series[0].data = Object.values(resp.data.chartData.deposits)
        this.series[1].data = Object.values(resp.data.chartData.payouts)
        // if (this.user.cpKey === 'cp_mex') {
        this.series[2].data = Object.values(resp.data.chartData.adjustment)
        this.series[3].data = Object.values(resp.data.chartData.balance)
        // } else {
        //   this.series[2].data = Object.values(resp.data.chartData.balance)
        // }
        this.xAxis = resp.data.key
        this.delta = resp.data.isBotm ? resp.data.realBalance : resp.data.balance
        this.deposits = resp.data.deposits
        this.payouts = resp.data.payouts
        this.adjustment = resp.data.adjustment
        this.isBotm = resp.data.isBotm
        this.botmBalance = resp.data.botmBalance
        this.rapidBalance = resp.data.rapidBalance
        this.oldRapidEmployer = resp.data.oldRapidEmployer
        this.initChart(resp.data)
      }
      this.visible = false
    },
    initChart (data) {
      this.chart = echarts.init(document.getElementById('employerBalance'), 'primary')
      let that = this
      const type = this.$store.state.User.cpKey === 'cp_mex' ? 'Employee' : 'Member'
      let colorList = ['#2ddb96', '#fc5a5a']
      let labelList = [this.employerPre + ' Deposits', type + ' Payouts']
      const legendData = [this.employerPre + ' Deposits', type + ' Payouts']
      // if (this.user.cpKey === 'cp_mex') {
      colorList.push('#ff974a')
      labelList.push('Adjustment/Reversals')
      legendData.push('Adjustment/Reversals')
      // }
      legendData.push(this.employerPre + ' Balance')
      let positionStr = ''
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          title: {
            text: 'Income of Germany and France since 1950'
          },
          backgroundColor: '#ffffff',
          position: function (pos, params, dom, rect, size) {
            // 鼠标在左侧时 tooltip 显示到右侧，鼠标在右侧时 tooltip 显示到左侧。
            let obj = { top: pos[1] - 270 }
            if (pos[0] < size.viewSize[0] / 2) {
              obj['left'] = pos[0] - 50
              positionStr = 'left'
            } else {
              positionStr = 'right'
              obj['right'] = size.viewSize[0] - pos[0] - 50
            }
            return obj
          },
          padding: 0,
          formatter: function (params) {
            let endValue = that.chart.getOption().dataZoom[0].endValue
            let startValue = that.chart.getOption().dataZoom[0].startValue
            let str = '<div class="tooltip-area-' + positionStr + '" style="box-shadow: 0 5px 15px 0 rgba(28, 20, 70, 0.1);border-radius:20px;padding:30px;padding-top:16px;font-family: Poppins;font-size: 14px;"><p style="text-align:center;margin:0px;color:#231f20;font-weight: 600;">' + that.titleStr(params[0]['name']) + '</p>' +
              '<p style="text-align:center;margin:0px;color:#231f20;font-weight: 600;">Date ranges: ' + that.titleStr(that.xAxis[startValue], true) + ' ~ ' + that.titleStr(that.xAxis[endValue], true) + '</p>' +
              '<table style="min-width:500px;">' +
              '<tr style="color:#231f20;width:100%;">' +
              '<th style="text-align:left;font-weight: 600;">Type</th><th style="text-align:left;"># of TXNs</th><th style="text-align:left;">Total ($)</th><th style="text-align:left;">Average ($)</th>' +
              '</tr>' +
              '<tr style="color:#414141">' +
              '<td style="color:' + colorList[0] + ';font-weight: 600;">' + labelList[0] + '</td><td>' + data.chartCount['deposits'][params[0]['name']] + '</td><td>' + moneyFormat(data.chartData['deposits'][params[0]['name']] * 100) + '</td><td>' + moneyFormat(data.chartAvg['deposits'][params[0]['name']] * 100) + '</td>' +
              '</tr>' +
              '<tr style="color:#414141">' +
              '<td style="color:' + colorList[1] + ';font-weight: 600;">' + labelList[1] + '</td><td>' + data.chartCount['payouts'][params[0]['name']] + '</td><td>' + moneyFormat(data.chartData['payouts'][params[0]['name']] * 100) + '</td><td>' + moneyFormat(data.chartAvg['payouts'][params[0]['name']] * 100) + '</td>' +
              '</tr>'
            // if (that.$store.state.User.cpKey === 'cp_mex') {
            str += '<tr style="color:#f16501">' +
                '<td style="color:' + colorList[2] + ';font-weight: 600;">' + labelList[2] + '</td><td>' + data.chartCount['adjustment'][params[0]['name']] + '</td><td>' + moneyFormat(data.chartData['adjustment'][params[0]['name']] * 100) + '</td><td>' + moneyFormat(data.chartAvg['adjustment'][params[0]['name']] * 100) + '</td>' +
                '</tr>'
            // }
            str += '<tr style="color:#231f20;font-weight: 600;">' +
              '<td>Total</td><td>' + (data.chartCount['deposits'][params[0]['name']] + data.chartCount['payouts'][params[0]['name']] + data.chartCount['adjustment'][params[0]['name']]) + '</td><td>' + moneyFormat(data.chartData['deposits'][params[0]['name']] * 100 + data.chartData['payouts'][params[0]['name']] * 100 + data.chartData['adjustment'][params[0]['name']] * 100) + '</td><td>' + '</td>' +
              '</tr>' +
              '<tr style="color:#414141;font-weight: 600;">' + '<td><i style="background:#0062ff;width:10px; height: 10px;border-radius:5px;display: inline-block;margin-right: 5px;"></i>' + that.employerPre + ' Balance</td><td>' + moneyFormat(data.chartData['balance'][params[0]['name']] * 100) + '</td>' +
              '<td>' + '</td>' +
              '<td>' + '</td>' +
              '</tr>' +
              '</table>' +
              '</div>'
            return str
          }
        },
        legend: {
          bottom: '0px',
          icon: 'pin',
          data: legendData
        },
        grid: {
          left: '3%',
          right: '4%',
          top: '10px',
          bottom: '90px',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: this.xAxis
          }
        ],
        axisLabel: {
          color: '#999',
          fontSize: 10,
          formatter: v => {
            if (v >= 1000 || v < -1000) {
              return (v / 1000) + 'k'
            }
            return v
          }
        },
        dataZoom: {
          bottom: '50px',
          fillerColor: 'rgba(0, 222,0, 0.5)',
          handleStyle: {
            color: 'rgba(0, 222,0, 0.5)'
          },
          moveHandleStyle: {
            color: 'rgba(0, 222,0, 0.5)'
          }
        },
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: false
            }
          }
        ],
        series: this.series
      }
      this.chart.setOption(option)

      $(window).on('resize', this.resize)
      if (this.chartHeight) {
        this.chart.resize({ height: this.chartHeight })
      }
    },
    titleStr (str = '', range = false) {
      let title = str.split('-')
      let month = {
        '01': 'Jan.',
        '02': 'Feb.',
        '03': 'Mar.',
        '04': 'Apr.',
        '05': 'May',
        '06': 'Jun.',
        '07': 'Jul.',
        '08': 'Aug.',
        '09': 'Sept.',
        '10': 'Oct.',
        '11': 'Nov.',
        '12': 'Dec.'
      }
      let bigMonth = ['01', '03', '05', '07', '08', '10', '12']
      let smallMonth = ['04', '06', '09', '11']
      // let days = ['01', '02', '03', '21', '22', '23', '31']
      if (title.length === 3) {
        if (title[2].slice(-1) === '1' && title[2] !== '11') {
          return month[title[1]] + ' ' + title[2] + 'st, ' + title[0]
        } else if (title[2].slice(-1) === '2' && title[2] !== '12') {
          return month[title[1]] + ' ' + title[2] + 'nd, ' + title[0]
        } else if (title[2].slice(-1) === '3' && title[2] !== '13') {
          return month[title[1]] + ' ' + title[2] + 'rd, ' + title[0]
        } else {
          return month[title[1]] + ' ' + title[2] + 'th, ' + title[0]
        }
      } else if (title.length === 2) {
        if (range) {
          return this.value === 'week' ? 'Week ' + title[1] + ', ' + title[0] : month[title[1]] + ', ' + title[0]
        } else {
          let end = '29th'
          if (bigMonth.indexOf(title[1]) !== -1) {
            end = '31st'
          } else if (smallMonth.indexOf(title[1]) !== -1) {
            end = '30th'
          } else if (title[0] % 4) {
            end = '28th'
          }
          return this.value === 'week' ? 'Week ' + title[1] + ' Sun., ' + title[0] + ' - ' + 'Week ' + title[1] + ' Sat., ' + title[0] : month[title[1]] + ' ' + '01st, ' + title[0] + ' - ' + month[title[1]] + ' ' + end + ', ' + title[0]
        }
      } else if (title.length === 1) {
        if (range) {
          return title[0]
        } else {
          return month['01'] + ' 01st, ' + title[0] + ' - ' + month['12'] + '31st, ' + title[0]
        }
      }
    },
    download (type) {
      let userAgent = navigator.userAgent
      if (userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Chrome') === -1) {
        return notify('Please take a screenshot manually or use another browser to open the website to save the picture!', 'negative')
      }
      let target = null
      let name = type
      target = document.getElementById(type)
      this.$q.loading.show()
      const targetCss = window.getComputedStyle(target)

      const options = {
        scale: 2,
        width: parseInt(targetCss.width, 10),
        height: parseInt(targetCss.height, 10),
        y: target.offsetTop + 100
      }
      // console.log(options)
      html2canvas(target, options).then(canvas => {
        // canvas is the final rendered <canvas> element
        const imgData = canvas.toDataURL('image/jpeg')
        console.log(imgData)
        this.fileDownload(imgData, name)
        this.$q.loading.hide()
      })
    },
    fileDownload (downloadUrl, filename) {
      let aLink = document.createElement('a')
      aLink.style.display = 'none'
      aLink.href = downloadUrl
      aLink.download = `${filename}`
      // 触发点击-然后移除
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    }
  }
}
</script>
<style lang="scss">
#balanceChart {
  margin: 0 10px;
  padding: 10px;
  height: 100%;
  .chart-title {
    p {
      margin: 0;
      font-size: 14px;
      color: #191d3d;
    }
    .balance {
      color: #191d3d;
      font-weight: bold;
      font-size: 28px;
    }
    .note {
      font-size: 14px;
      line-height: 16px;
      color: rgb(255, 151, 74);
    }
  }
  #employerBalance {
    min-height: 358px;
    height: calc(100% - 142px);
  }
  .chart-item {
    position: relative;
  }
  .loading {
    min-height: 400px;
    line-height: 400px;
    width: calc(100% - 32px);
    text-align: center;
    position: absolute;
    top: 0;
    z-index: 100;
    background: rgba($color: #000000, $alpha: 0.1);
  }
  .tooltip-area-left :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    left: 40px;
  }
  .tooltip-area-right :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    right: 40px;
  }
}
</style>
