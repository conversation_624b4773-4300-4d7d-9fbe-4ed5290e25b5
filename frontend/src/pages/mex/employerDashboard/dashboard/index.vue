<template>
  <q-page class="mex__employer_dashboard__index_page">
    <div class="page-header">
      <div class="fun-group">
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row">
        <div class="col-sm-6 col-xs-12 mt-30">
          <balanceChart ref="balanceChart"
                        v-model="chartDateRange"></balanceChart>
        </div>
        <div class="col-sm-6 col-xs-12 mt-30">
          <q-card id="depositDetail"
                  class="text-center"
                  :class="showDetail">
            <p class="title">Direct Deposit Details</p>
            <img v-if="this.user.cpKey === 'cp_mex'"
                 class="account-image"
                 src="/static/abel/icons/mex-account-image.png">
            <q-list>
              <q-item @click.native="viewAccount()">
                <q-item-side class="item-desc">
                  <span class="transaction-icon account-icon"><img src="/static/abel/icons/mex-bank-icon.svg"></span>
                  <p v-if="showDetail"
                     class="item-header">Hide Account Details</p>
                  <p v-else
                     class="item-header">Show Account Details</p>
                </q-item-side>
                <q-item-side>
                  <i v-if="showDetail"
                     class="mdi mdi-chevron-down"></i>
                  <i v-else
                     class="mdi mdi-chevron-right"></i>
                </q-item-side>
              </q-item>
              <q-item v-if="showDetail"
                      class="detail-list">
                <q-item-side class="item-desc">
                  <span class="transaction-icon hide-icon"><img src="/static/abel/icons/mex-bank-icon.svg"></span>
                  <div>
                    <div class="detial-item">
                      <p class="title">Institution Name</p>
                      <p>
                        <span ref="name">{{ this.user.employerDashboardInstitutionName || '-' }}</span>
                        <q-icon v-if="this.user.employerDashboardInstitutionName"
                                @click.native="copy(user.employerDashboardInstitutionName)"
                                name="mdi-content-copy light-grey font-14 ml-10"> </q-icon>
                      </p>
                    </div>
                    <div class="detial-item">
                      <p class="title">Institution Address</p>
                      <p><span ref="address">{{ this.user.employerDashboardInstitutionAddress || '-' }}</span>
                        <q-icon v-if="this.user.employerDashboardInstitutionAddress"
                                @click.native="copy(user.employerDashboardInstitutionAddress)"
                                name="mdi-content-copy light-grey font-14 ml-10"> </q-icon>
                      </p>
                    </div>
                    <div class="detial-item">
                      <p class="title">Routing Number</p>
                      <p><span ref="rounting">{{ this.user.employerDashboardRoutingNumber || '-' }}</span>
                        <q-icon v-if="this.user.employerDashboardRoutingNumber"
                                @click.native="copy(user.employerDashboardRoutingNumber)"
                                name="mdi-content-copy light-grey font-14 ml-10"> </q-icon>
                      </p>
                    </div>
                    <div class="detial-item">
                      <p class="title">Account Number</p>
                      <p><span ref="account">{{ this.user.employerDashboardAccountNumber || '-' }}</span>
                        <q-icon v-if="this.user.employerDashboardAccountNumber"
                                @click.native="copy(user.employerDashboardAccountNumber)"
                                name="mdi-content-copy light-grey font-14 ml-10"> </q-icon>
                      </p>
                    </div>
                  </div>
                </q-item-side>
              </q-item>
              <q-item @click.native="viewPayments()">
                <q-item-side class="item-desc">
                  <span class="transaction-icon payment-icon"><img src="/static/abel/icons/mex-bank-icon.svg"></span>
                  <p class="item-header">{{ this.user.cpKey === 'cp_mex' ? 'Payments & Deposits' : 'Deposits & Payouts'
                    }}</p>

                </q-item-side>
                <q-item-side>
                  <i class="mdi mdi-chevron-right"></i>
                </q-item-side>
              </q-item>
            </q-list>
          </q-card>
        </div>
      </div>
      <div class="row">
        <div class="col-sm-6 col-xs-12 mt-30">
          <q-card id="depositsChart">
            <q-card-title>
              <div class="row">
                <div class="chart-title">
                  <p>{{ this.user.cpKey === 'cp_mex' ? 'Employer Deposits' : 'Client Deposits' }}</p>
                  <span>Recent Deposits</span>
                </div>
                <q-btn class="chart-btn down-btn btn-sm ml-auto mr-8 square"
                       @click="download('depositsChart')"><i class="mdi mdi-file-upload-outline"></i></q-btn>
                <q-btn class="chart-btn view-btn btn-sm square"
                       @click="view('depositsChart')"><i class="mdi mdi-chart-bar"></i></q-btn>
              </div>
            </q-card-title>
            <q-card-main class="chart-item">
              <q-list v-if="deposits.length">
                <q-item v-for="(deposit, key) in deposits"
                        :key="key">
                  <q-item-side class="item-desc">
                    <span class="transaction-icon"><i class="mdi mdi-arrow-down"></i></span>
                    <div>
                      <p class="item-header">Deposit</p>
                      <p>{{deposit['sendDate']}}</p>
                    </div>
                  </q-item-side>
                  <q-item-side>
                    <p class="item-header mr-40">{{ deposit['amount'] | moneyFormat}}</p>
                    <!-- <p class="item-balance">{{ deposit['balance'] | moneyFormat}}</p> -->
                  </q-item-side>
                </q-item>
              </q-list>
              <div class="no-data"
                   v-else>
                <p>There are currently no recent deposits transactions.</p>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-6 col-xs-12 mt-30">
          <q-card id="payoutsChart">
            <q-card-title>
              <div class="row">
                <div class="chart-title">
                  <p>{{ textEmployee }} Payouts</p>
                  <span>Recent Payouts</span>
                </div>
                <q-btn class="chart-btn down-btn btn-sm ml-auto mr-8 square"
                       @click="download('payoutsChart')"><i class="mdi mdi-file-upload-outline"></i></q-btn>
                <q-btn class="chart-btn view-btn btn-sm square"
                       @click="view('payoutsChart')"><i class="mdi mdi-chart-bar"></i></q-btn>
              </div>
            </q-card-title>
            <q-card-main class="chart-item">
              <q-list v-if="payouts.length">
                <q-item v-for="(payout, key) in payouts"
                        :key="key">
                  <q-item-side class="item-desc">
                    <span v-if="payout['amount'] >= 0"
                          class="transaction-icon"><i class="mdi mdi-arrow-down"></i></span>
                    <span v-if="payout['amount'] < 0"
                          class="transaction-icon"><i class="mdi mdi-arrow-up"></i></span>
                    <div>
                      <p class="item-header"
                         v-if="payout['employee']">{{payout['employee']['firstName']}}
                        {{payout['employee']['lastName']}}</p>
                      <p v-else
                         class="item-header"></p>
                      <p>{{payout['sendDate']}}</p>
                    </div>
                  </q-item-side>
                  <q-item-side>
                    <p class="item-header mr-40">{{ payout['amount'] | moneyFormat}}</p>
                    <!-- <p class="item-balance">{{ payout['balance'] | moneyFormat}}</p> -->
                  </q-item-side>
                </q-item>
              </q-list>
              <div class="no-data"
                   v-else>
                <p>There are currently no recent payout transactions.</p>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <div class="row">
        <div class="col-sm-6 col-xs-12 mt-30">
          <q-card id="employerChart">
            <q-card-title>
              <div class="row">
                <div class="chart-title">
                  <p>{{ textEmployees }}</p>
                  <span> {{employee['total'] ?  employee['total'] : 0}} {{ textEmployees }}</span>
                </div>
                <q-btn class="chart-btn down-btn btn-sm ml-auto mr-8 square"
                       @click="download('employerChart')"><i class="mdi mdi-file-upload-outline"></i></q-btn>
                <q-btn class="chart-btn view-btn btn-sm square"
                       @click="view('employerChart')"><i class="mdi mdi-chart-bar"></i></q-btn>
              </div>
            </q-card-title>
            <q-card-main class="chart-item">
              <div class="row">
                <div class="col-sm-4 employees-item">
                  <span class="employees-icon"><img src="/static/abel/icons/mex_employer_employee.svg"></span>
                  <div>
                    <p class="item-header">Active {{ textEmployees }}</p>
                    <p>{{ employee['active'] ? employee['active'] : 0}}</p>
                  </div>
                </div>
                <div class="col-sm-4 employees-item">
                  <span class="employees-icon"><i class="mdi mdi-arrow-down"></i></span>
                  <div>
                    <p class="item-header">Total Deposits</p>
                    <p>{{ employee['totalDeposits'] ? employee['totalDeposits'] : 0 | moneyFormat}}</p>
                  </div>
                </div>
                <div class="col-sm-4 employees-item">
                  <span class="employees-icon average-icon"><img src="/static/abel/icons/mex_employer_payments.svg"></span>
                  <div>
                    <p class="item-header">Average Deposit</p>
                    <p>{{ employee['avgDeposits'] ? employee['avgDeposits'] : 0 | moneyFormat}}</p>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>
<script>
import html2canvas from 'html2canvas'
import balanceChart from './balance'
import { request, notify } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import copy from 'copy-to-clipboard'

export default {
  name: 'MexEmployerDashboard',
  mixins: [
    MexPageMixin
  ],
  components: {
    balanceChart
  },
  data () {
    return {
      dateRange: 'month',
      chartDateRange: 'month',
      deposits: [],
      detail: {
        name: 'Swift Prepaid Solutions, Inc.',
        address: '2150 E. Lake Cook Rd. Suite 150 Buffalo Grove, IL 60089',
        rounting: '123 456 789',
        account: '12 345 6789 01234'
      },
      payouts: [],
      employee: [],
      showDetail: null
    }
  },
  methods: {
    view (type) {
      if (type === 'depositsChart') {
        localStorage.setItem('paymentType', 'deposit')
        window.open(`/admin#/h/mex/employer/payments`, '_blank')
      }
      if (type === 'payoutsChart') {
        localStorage.setItem('paymentType', 'payout')
        window.open(`/admin#/h/mex/employer/payments`, '_blank')
      }
      if (type === 'employerChart') {
        window.open(`/admin#/h/mex/employer/employee`, '_blank')
      }
    },
    viewPayments () {
      window.open(`/admin#/h/mex/employer/payments`, '_blank')
    },
    viewAccount () {
      this.showDetail = this.showDetail ? null : 'show-detail'
      this.$nextTick(() => {
        let height = document.getElementById('depositDetail').offsetHeight
        // console.log(height)
        this.$refs['balanceChart'].heightResize(height - 158)
      })
    },
    download (type) {
      let userAgent = navigator.userAgent
      if (userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Chrome') === -1) {
        return notify('Please take a screenshot manually or use another browser to open the website to save the picture!', 'negative')
      }
      let target = null
      let name = type
      target = document.getElementById(type)
      this.$q.loading.show()
      const targetCss = window.getComputedStyle(target)

      const options = {
        scale: 2,
        width: parseInt(targetCss.width, 10),
        height: parseInt(targetCss.height, 10),
        y: target.offsetTop + 100
      }
      console.log(options)
      html2canvas(target, options).then(canvas => {
        // canvas is the final rendered <canvas> element
        const imgData = canvas.toDataURL('image/jpeg')
        console.log(imgData)
        this.fileDownload(imgData, name)
        this.$q.loading.hide()
      })
    },
    fileDownload (downloadUrl, filename) {
      let aLink = document.createElement('a')
      aLink.style.display = 'none'
      aLink.href = downloadUrl
      aLink.download = `${filename}`
      // 触发点击-然后移除
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    },
    async reload () {
      let data = {}
      this.$q.loading.show()
      if (this.$refs && this.$refs.dateRangeFilter) {
        data = this.$refs.dateRangeFilter.params()
      }
      this.chartDateRange = data.period
      this.$root.$emit('admin-reload-dashboard')
      const resp = await request(`/admin/mex/employer/dashboard/static`, 'get', data)
      if (resp.success) {
        this.payouts = resp.data.payouts
        this.deposits = resp.data.deposits
        this.employee = resp.data.employee
        this.detail = resp.data.depositDetails
      }
      this.$q.loading.hide()
    },
    copy (content) {
      copy(content)
      notify('Copied to the clipboard')
    }
  },
  async mounted () {
    if (this.user.cpKey === 'cp_faas') {
      this.dateRanges = [
        {
          label: 'Month',
          value: 'month'
        },
        {
          label: 'Week',
          value: 'week'
        },
        {
          label: 'All',
          value: 'all'
        },
        {
          label: 'Custom',
          value: 'custom_range'
        }
      ]
    }
    this.reload()
  }
}
</script>
<style lang="scss">
.mex__employer_dashboard__index_page {
  .chart-btn {
    max-height: 55px !important;
    width: 55px;
    color: #fff;

    i {
      font-size: 24px;
    }
  }

  .down-btn {
    background-color: var(--span-color-primary);
  }

  .view-btn {
    background-color: #0062ff;
  }

  .light-grey {
    color: #8e8e93;
  }

  #depositDetail {
    padding: 20px;
    margin: 0 10px;
    min-height: 516px;

    .title {
      font-size: 28px;
      font-weight: bold;
      color: #191d3d;
      text-align: center;
    }

    .account-image {
      max-height: 260px;
      margin: 0 auto;
    }

    .account-icon {
      background-color: #0062ff;
    }

    .payment-icon {
      background-color: #00d54f;
    }

    .q-item {
      cursor: pointer;

      i {
        font-size: 20px;
      }

      .item-desc {
        max-width: 90%;
      }
    }

    .detail-list {
      display: block;

      .hide-icon {
        visibility: hidden;
      }

      p {
        font-size: 16px;
        font-weight: 600;
        color: #191d3d;
        margin: 0;
      }

      .title {
        text-align: left;
        color: rgba($color: #191d3d, $alpha: 0.5);
      }
    }
  }

  .show-detail {
    height: auto !important;
  }

  #depositsChart,
  #payoutsChart {
    margin: 0 10px;
    padding: 10px;

    .chart-title {
      p {
        margin: 0;
        font-size: 14px;
        color: #191d3d;
      }

      span {
        color: #191d3d;
        font-weight: bold;
        font-size: 28px;
      }
    }

    .chart-item {
      height: 300px;
    }
  }

  #employerChart {
    margin: 0 10px;
    padding: 10px;

    .chart-title {
      p {
        margin: 0;
        font-size: 14px;
        color: #191d3d;
      }

      span {
        color: #191d3d;
        font-weight: bold;
        font-size: 28px;
      }
    }

    .chart-item {
      .employees-item {
        padding: 8px 0;
        display: flex;

        .employees-icon {
          max-height: 55px;
          max-width: 55px;
          background-color: #00d54f;
          border-radius: 10px;
          display: flex;
          align-items: center;
          margin-right: 10px;
          padding: 1em;
          justify-content: center;

          i {
            line-height: 2em;
            font-size: 28px;
            color: #fff;
          }
        }

        .average-icon {
          background-color: #ff974a;
        }

        p {
          margin: 0;
          font-size: 18px;
          color: #191d3d;
          font-weight: bold;
        }

        .item-header {
          font-size: 14px;
          font-weight: normal;
        }
      }
    }
  }

  .q-list {
    border: none;
    height: 100%;
    overflow-y: scroll;

    .q-item {
      padding: 8px 0;
      justify-content: space-between;

      .item-desc {
        display: flex;
        max-width: 60%;
        align-items: center;

        .transaction-icon {
          max-height: 55px;
          max-width: 55px;
          background-color: rgba($color: #00d54f, $alpha: 0.1);
          border-radius: 10px;
          display: flex;
          align-items: center;
          margin-right: 10px;
          padding: 1em;
          justify-content: center;

          i {
            line-height: 2em;
            font-size: 28px;
            color: #00d54f;
          }
        }
      }

      p {
        margin: 0;
        font-size: 14px;
        color: #191d3d;
      }

      .item-header {
        font-size: 18px;
        font-weight: bold;
      }

      .item-balance {
        color: #5a5a89;
        text-align: right;
      }
    }
  }
}
</style>
