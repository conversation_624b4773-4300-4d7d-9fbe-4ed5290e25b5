<template>
  <q-dialog class="mex-employee-edit-last-deposit-dialog"
            v-model="visible">
    <template slot="title">
      <template>
        <div class="mb-5">
          <span class="total-img">
            <img src="/static/abel/icons/mex_total_balance.svg">
          </span>
        </div>
        <div class="font-18 mb-2">Edit Last Deposit </div>
        <div class="font-14 normal">
          Your Last Deposit was {{entity['Last Payout Amount Value'] * 100 | moneyFormat}} Please input the proper amount below.
        </div>
      </template>
      <q-btn class="close"
             round
             flat
             @click="cancel"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row">
        <div class="col-12">
          <q-input type="number"
                   prefix="$"
                   :max="max"
                   v-model="amount"></q-input>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <div class="row">
          <q-btn label="Cancel"
                 no-caps
                 color="negative"
                 @click="cancel"></q-btn>
          <q-btn label="Done"
                 no-caps
                 color="positive"
                 @click="confirm"></q-btn>
        </div>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { EventHandlerMixin, notifySuccess, request } from '../../../../common'

export default {
  name: 'mex-employee-edit-last-deposit-dialog',
  mixins: [
    Singleton,
    EventHandlerMixin('mex-employee-edit-last-deposit-dialog', 'show')
  ],
  data () {
    return {
      max: 0,
      amount: 0
    }
  },
  created () { },
  components: {},
  computed: {},
  methods: {
    cancel () {
      this._hide()
      this.$root.$emit('reload-mex-employers-employee')
    },
    show () {
      this.amount = this.entity['Last Payout Amount Value']
      this.max = this.amount
    },
    async confirm () {
      if (this.amount > this.max) {
        notifySuccess('The max value is ' + this.max + ', please check the input', 'Message', 'negative')
        return
      }
      if (this.amount <= 0) {
        notifySuccess('Please enter a number greater than 0.', 'Message', 'negative')
        return
      }
      this.$q.loading.show()
      const resp = await request(`/admin/mex/employer/employees/editDeposit`, 'post', {
        amount: this.amount,
        id: this.entity['Last Payout ID'],
        employee: this.entity['Employee ID']
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.cancel()
      }
    }
  },
  mounted () {
  }
}
</script>

<style lang="scss">
.mex-employee-edit-last-deposit-dialog {
  .modal-content {
    width: 450px;
  }
  .q-search {
    max-width: 402px;
    margin: 0 auto;
  }
  .down-btn {
    font-size: 16px;
    background: #9c8bff;
    .q-btn-inner {
      line-height: 28px;
      color: #fafafb;
    }
  }
  .continue-btn {
    background-color: #7ac142;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
    .q-btn-inner {
      line-height: 28px;
      color: #fafafb;
    }
  }
  .q-if.dense {
    padding: 8px !important;
  }
  .total-img {
    background: rgba($color: #4acc3d, $alpha: 0.1);
    width: 50px;
    padding: 14px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    border-radius: 25px;
  }
  .select-list {
    h1 {
      font-size: 20px;
      color: #191d3d;
      text-align: left;
      margin: 0;
      font-weight: bold;
    }
    p {
      margin: 0;
      text-align: left;
    }
    .payout-employee-list {
      border: none;
      .q-item {
        padding-left: 0;
        padding-right: 0;
      }
    }
    .has-border {
      .q-item {
        border-bottom: 1px solid #959595;
      }
    }
  }
  .q-table-container .q-table-middle {
    margin: 0;
  }
  .done-icon {
    font-size: 60px;
    line-height: 50px;
  }
  .amount-input {
    min-width: 100px;
  }
  .total-payout {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .text-blod {
    font-weight: bold;
  }
  .delete-icon {
    display: flex;
    align-items: center;
    cursor: pointer;
    // justify-content: center;
    padding: 5px;
    font-size: 16px;
    border-radius: 10px;
    border: 1px solid #f1f1f5;
    i {
      margin: 0 auto;
    }
  }
}
</style>
