<template>
  <q-dialog class="mex-member-id-manual-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="mb-5">
        <q-icon name="mdi-square-edit-outline"
                color="faded"
                class="font-35"></q-icon>
      </div>
      <div class="font-16 mb-2">Manual KYC Approval</div>
      <div class="font-12 normal">Please sign your name below to approve the member.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt-0"
           notranslate="">
        <div class="col-6">
          <q-input float-label="Member"
                   autocomplete="no"
                   readonly
                   :value="$c.fullName(entity)"></q-input>
        </div>
        <div class="col-6">
          <q-input float-label="Employer"
                   autocomplete="no"
                   readonly
                   :value="entity['Employer']"></q-input>
        </div>
        <div class="col-6">
          <q-input float-label="Gov ID (SSN/CURP)"
                   autocomplete="no"
                   readonly
                   :value="entity['CURP ID']"></q-input>
        </div>
        <div class="col-6">
          <q-input float-label="Approver"
                   autocomplete="no"
                   readonly
                   v-model="approver"></q-input>
        </div>
      </div>
      <div class="w-500 mv-20 mh-auto text-left">
        <q-option-group class="mt-15"
                        type="checkbox"
                        color="blue"
                        v-model="checks"
                        :options="options" />
      </div>

      <div class="q-body-2 relative">
        Please sign below:
        <a href="javascript:"
           class="text-negative signature-pad-clear-btn"
           @click="clearSignature">Clear</a>
      </div>
      <div class="signature-pad mv-10">
        <canvas class="bg"></canvas>
        <canvas class="front"></canvas>
      </div>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel"
             no-caps
             color="grey-3"
             text-color="tertiary"
             @click="_hide" />
      <q-btn label="Manually Approve Card"
             no-caps
             color="blue"
             class="main"
             @click="submit" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notifyForm, request, uploadAttachment } from '../../../../common'
import $ from 'jquery'
import SignaturePad from 'signature_pad'

export default {
  name: 'mex-member-id-manual-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      checks: [],
      options: [
        {
          value: 'name',
          label: 'I have verified the customer name matches the presented passport.'
        },
        {
          value: 'employer',
          label: 'I have verified that the customer is assigned to the correct employer.'
        },
        {
          value: 'curp_id',
          label: 'I have verified that the customer has provided a valid Gov ID (SSN/CURP).'
        },
        {
          value: 'photo',
          label: 'I have verified that the customers likeness matches the presented passport photo.'
        }
      ],
      spCanvas: null,
      spCanvasBg: null,
      signaturePad: null
    }
  },
  computed: {
    approver () {
      return this.$store.state.User.fullName
    }
  },
  validations: {
    checks: {
      all () {
        return this.checks.length >= this.options.length
      }
    }
  },
  methods: {
    show () {
      this.checks = []

      setTimeout(() => {
        this.resizeCanvas()
      }, 200)
    },
    hide () {
      setTimeout(() => {
        this.clearSignature()
      }, 200)
    },
    submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm('Please check all the items.')
      }
      if (this.isCanvasBlank(this.spCanvas)) {
        return notifyForm('Please sign your name.')
      }

      this.$q.loading.show()
      const ctxBg = this.spCanvasBg.getContext('2d')
      ctxBg.drawImage(this.spCanvas, 0, 0, this.spCanvas.width, this.spCanvas.height,
        0, 0, this.spCanvasBg.clientWidth, this.spCanvasBg.clientHeight)
      this.spCanvasBg.toBlob(async blob => {
        const file = await uploadAttachment(blob, 'id_manual_approval_signature')
        if (typeof file === 'object') {
          const resp = await request(`/admin/mex/members/${this.entity['Member ID']}/id-manual`, 'post', {
            checks: this.checks,
            signature: file.id
          })
          this.$q.loading.hide()
          if (resp.success) {
            this.entity.statuses = resp.data
            this.$root.$emit('reload-mex-members')
            this.$root.$emit('reload-mex-members-profile', false)
            this._hideAndEmit('show-mex-member-id-success-dialog', this.entity)
          }
        } else {
          this.$q.loading.hide()
        }
      })
    },
    clearSignature () {
      this.signaturePad.clear()
      this.drawBackground()
    },
    isCanvasBlank (canvas) {
      var context = canvas.getContext('2d')

      var pixelBuffer = new Uint32Array(
        context.getImageData(0, 0, canvas.width, canvas.height).data.buffer
      )

      var length = pixelBuffer.length
      var threshold = 1000
      var filled = 0
      for (var i = 0; i < length; i++) {
        var pixel = pixelBuffer[i]
        if (pixel !== 0) {
          filled++
          if (filled > threshold) {
            return false
          }
        }
      }
      return true
    },
    drawBackground () {
      const text = `TransferMex - ${this.entity['First Name']} ${this.entity['Last Name']} - Manual KYC Approval`
      const ctx = this.spCanvasBg.getContext('2d')
      ctx.save()

      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, 0, this.spCanvasBg.width, this.spCanvasBg.height)

      let width = $(this.spCanvasBg).width()
      let fontSize = width / 40

      ctx.rotate(-15 * Math.PI / 180)

      ctx.font = fontSize + 'px sans-serif'
      ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'

      const measure = ctx.measureText(text)
      const xCount = Math.ceil(width / measure.width) + 2
      const yCount = Math.ceil(width * 0.28 / 40) + 10

      for (let j = 0; j < yCount; j++) {
        const base = j * measure.width * 0.5
        for (let i = 0; i < xCount; i++) {
          const x = base + (i - 4.2) * measure.width * 1.1
          const y = j * fontSize * 2
          ctx.fillText(text, x, y)
        }
      }
      ctx.restore()
    },
    resizeCanvas () {
      var $sp = $(this.spCanvas)

      $sp.width(550)
      $sp.height($sp.width() * 0.28)

      var ratio = Math.max(window.devicePixelRatio || 1, 1)
      this.spCanvas.width = this.spCanvas.offsetWidth * ratio
      this.spCanvas.height = this.spCanvas.offsetHeight * ratio
      this.spCanvas.getContext('2d').scale(ratio, ratio)

      this.spCanvasBg.width = this.spCanvas.width
      this.spCanvasBg.height = this.spCanvas.height
      this.spCanvasBg.getContext('2d').scale(ratio, ratio)

      this.clearSignature()
    }
  },
  mounted () {
    this.spCanvas = $(this.$el).find('.signature-pad > canvas.front')[0]
    this.spCanvasBg = $(this.$el).find('.signature-pad > canvas.bg')[0]

    this.signaturePad = new SignaturePad(this.spCanvas)
  }
}
</script>

<style lang="scss">
.mex-member-id-manual-dialog {
  .modal-content {
    width: 600px;

    .q-option {
      align-items: start;

      .q-option-inner + .q-option-label {
        font-weight: normal;
        margin-left: 5px !important;
      }
    }
  }
}
</style>
