<template>
  <div class="row mex-member-id-status">
    <div class="col"
         v-for="(status, s) in statuses"
         :key="s">
      <q-icon :name="stepIcon(status)"
              :color="stepColor(status)"></q-icon> {{ s }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'mex-member-id-status',
  props: {
    statuses: {
      type: Object
    }
  },
  methods: {
    stepIcon (s) {
      if (s === null) {
        return 'mdi-clock-outline'
      }
      if (s === true) {
        return 'mdi-check-circle'
      }
      return s === 'loading' ? 'mdi-spin mdi-loading' : 'mdi-close-circle'
    },
    stepColor (s) {
      if (s === null) {
        return 'faded'
      }
      if (s === true) {
        return 'positive'
      }
      return s === 'loading' ? 'blue' : 'negative'
    }
  }
}
</script>

<style lang="scss">
.mex-member-id-status {
  .q-icon {
    font-size: 23px;
  }
}
</style>
