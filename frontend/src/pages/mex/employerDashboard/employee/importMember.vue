<template>
  <q-dialog class="faas-import-member-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="mb-5">
        <div class="avatar">
          <q-icon name="mdi-account-circle-outline"></q-icon>
        </div>
      </div>
      <div v-if="step == 'init'"
           class="font-16 mb-2">Create New Member</div>
      <div v-else-if="step == 'list'"
           class="font-16 mb-2">Confirm New Member</div>
      <div v-else-if="step == 'cronJob'"
           class="font-16 mb-2">Confirm</div>
      <div v-if="step=='init'"
           class="font-12 normal text-dark">Please fill out the fields below or upload an XLS file to create a new member(s).</div>
      <div v-if="step=='list'"
           class="font-12 normal text-dark">Please confirm the list of members added below.</div>
      <div v-if="step=='cronJob'"
           class="font-12 normal text-dark">Upload success and Batch import data is being processed in the background.</div>
      <q-btn v-if="step == 'list'"
             class="back"
             round
             flat
             @click="back"
             icon="mdi-chevron-left" />
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div v-if="step == 'init'"
           class="row gutter-form mt--10 mb-5">
        <div class="col-sm-6">
          <q-input placeholder="First Name"
                   autocomplete="no"
                   :error="$v.entity['First Name'].$error"
                   @input="$v.entity['First Name'].$touch"
                   v-model="entity['First Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input placeholder="Last Name"
                   autocomplete="no"
                   :error="$v.entity['Last Name'].$error"
                   @input="$v.entity['Last Name'].$touch"
                   v-model="entity['Last Name']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-datetime placeholder="Date of Birth"
                      autocomplete="no"
                      type="date"
                      format="MM/DD/YYYY"
                      :error="$v.entity['Date of Birth'].$error"
                      @input="$v.entity['Date of Birth'].$touch"
                      v-model="entity['Date of Birth']"></q-datetime>
        </div>
        <div class="col-sm-12">
          <q-input placeholder="Email"
                   autocomplete="no"
                   :error="$v.entity['Email'].$error"
                   @input="$v.entity['Email'].$touch"
                   v-model="entity['Email']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input placeholder="Mobile Phone"
                   autocomplete="no"
                   :error="$v.entity['Mobile Phone'].$error"
                   @input="$v.entity['Mobile Phone'].$touch"
                   v-model="entity['Mobile Phone']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input placeholder="Street Address"
                   autocomplete="no"
                   :error="$v.entity['Street Address'].$error"
                   @input="$v.entity['Street Address'].$touch"
                   v-model="entity['Street Address']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input placeholder="City"
                   autocomplete="no"
                   :error="$v.entity['City'].$error"
                   @input="$v.entity['City'].$touch"
                   v-model="entity['City']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-select autocomplete="no"
                    placeholder="Country"
                    :options="countries"
                    filter
                    autofocus-filter
                    v-model="entity['CountryId']"></q-select>
        </div>
        <div class="col-sm-6">
          <q-select autocomplete="no"
                    placeholder="Province/State"
                    :options="states"
                    filter
                    autofocus-filter
                    :before="stateBefore"
                    v-model="entity['StateId']"></q-select>
        </div>
        <div class="col-sm-6">
          <q-input placeholder="Postal Code"
                   autocomplete="no"
                   v-model="entity['Postal Code']"></q-input>
        </div>
        <div class="col-sm-12 split-lint">
          <p><span>OR</span></p>
        </div>
        <div class="col-sm-12">
          <div class="upload-area"
               @click="select"
               :class="{selected: file}">
            <q-icon name="mdi-file-document-outline"></q-icon>
            <template v-if="file">
              <div class="mt-10">Selected file:</div>
              <div class="font-13 text-blue">
                <span>{{ file.name }}</span>
                <a href="javascript:"
                   class="ml-5 link"
                   @click.stop="file = null">
                  <q-icon name="mdi-close-circle-outline"
                          class="font-20"
                          color="negative"></q-icon>
                  <q-tooltip>Remove and reselect</q-tooltip>
                </a>
              </div>
            </template>
            <template v-else>
              <div class="mt-10">Upload .XLSX</div>
              <div class="font-13 text-dark">(Drop or click here)</div>
            </template>
          </div>
          <input type="file"
                 class="hide"
                 accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                 ref="file"
                 @change="selectedFile">
        </div>
      </div>
      <div v-else-if="step=='list'"
           class="row import-list">
        <h1 class="pt-20">New {{ textEmployees }} List</h1>
        <q-list class="import-member-list col-12">
          <q-item v-for="(member, i) in importMembers"
                  :key="i">
            <q-item-side>
              <q-item-tile avatar>
                <img src="/static/img/avatar.png">
              </q-item-tile>
            </q-item-side>
            <q-item-main>
              <p class="mb-0 employee-name">{{ member['First Name'] }} {{ member['Last Name']}}</p>
              <p class="mb-0">Member</p>
            </q-item-main>
            <q-item-side right>
              <span @click="view(member)"
                    class="delete-icon"><i class="mdi mdi-arrow-right-circle-outline"></i></span>
            </q-item-side>
          </q-item>
        </q-list>
      </div>
      <form ref="form"
            method="post"
            action="/admin/download">
        <input type="hidden"
               name="path"
               v-model="url">
      </form>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn v-if="step == 'init'"
               label="Download XLSX Template"
               no-caps
               color="blue"
               class="max-w-350"
               @click="downloadTemplate" />
        <q-btn :label="step == 'init' ? 'Continue' : 'Done'"
               no-caps
               color="positive"
               class="max-w-350"
               @click="next" />
        <a v-if="step == 'init'"
           href="javascript:"
           class="font-13 mt-10 text-blue"
           @click="notes = !notes">{{ notes ? 'Hide' : 'View' }} notes</a>
        <ul class="notes"
            v-if="notes">
          <li>Please delete the first data row in the above template as it's just a sample.</li>
          <li>The <i>Card Number #</i> and <i>Email Address</i> need to be unique in the system.</li>
          <li>The <i>Date of Birth</i> format: mm/dd/yyyy</li>
          <li>The <i>State/Province</i> and <i>Country</i> need to match the values contained in
            <a href="javascript:;"
               @click="downLoadCountryList">this spreadsheet</a>.
          </li>
          <li>The <i>Mobile Phone and Card Number #</i> is optional.</li>
          <li>The row number in the error message (if any) is counting from the title row, with the title row included, which is usually same as the row number in Microsoft Excel.</li>
          <li>More than 100 data will be imported asynchronously in the background</li>
          <li>Just import the first sheet by default, multiple sheets need to be spli</li>
        </ul>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import { uploadAttachment, notify, notifySuccess, notifyResponse, request } from '../../../../common'
import FaadStateListMixin from '../../../../mixins/mex/MexStateListMixin'
// import _ from 'lodash'
import $ from 'jquery'
import { required, email, helpers } from 'vuelidate/lib/validators'
const alpha = helpers.regex('alpha', /^[0-9+-\s]*$/)

export default {
  name: 'faas-import-member-detail-dialog',
  mixins: [
    Singleton,
    FaadStateListMixin,
    MexPageMixin
  ],
  components: {
  },
  validations () {
    if (this.file) {
      return {
        entity: {
          'First Name': {},
          'Last Name': {},
          'Date of Birth': {},
          'Mobile Phone': {},
          'Street Address': {},
          'City': {},
          'Email': {},
          'Postal Code': {}
        }
      }
    } else {
      return {
        entity: {
          'First Name': {
            required
          },
          'Last Name': {
            required
          },
          'Date of Birth': {
            required
          },
          'Mobile Phone': {
            alpha
          },
          'Email': {
            required,
            email
          },
          'Street Address': {
            required
          },
          'City': {
            required
          },
          'Postal Code': { required }
        }
      }
    }
  },
  watch: {
    file () {
      this.attachment = null
    }
  },
  data () {
    return {
      url: null,
      entity: {
        'First Name': '',
        'Last Name': '',
        'Date of Birth': '',
        'Mobile Phone': '',
        'Street Address': '',
        'City': '',
        'CountryId': '',
        'StateId': '',
        'Postal Code': '',
        'Card Number': ''
      },
      sendKYC: false,
      verifying: false,
      clients: [],
      file: null,
      attachment: null,
      notes: false,
      step: 'init',
      importMembers: []
    }
  },
  mounted () {
  },
  computed: {
  },
  methods: {
    async show () {
      this.file = null
      this.notes = false
      this.step = 'init'
      this.importMembers = []
    },
    view (row) {
      this.cancel()
      window.open(`/admin#/h/mex/employer/employee/${row['User ID']}`, '_blank')
    },
    async next () {
      if (this.step === 'init') {
        this.importMembers = []
        if (!this.file && this.$v.$invalid) {
          notifySuccess('Please fill out the fields below or upload an XLS file to create a new ' + this.textEmployee + '(s).', 'Message', 'negative')
          return
        }
        if (this.file) {
          if (!this.attachment) {
            this.$q.loading.show({ message: 'Uploading...' })
            const category = 'FaasImportMembers'
            const resp = await uploadAttachment(this.file, category)
            if (typeof resp === 'string') {
              this.$q.loading.hide()
              return notifyResponse(`Failed to upload: ${resp}`)
            }
            this.attachment = resp
          }
          this.$q.loading.show({ message: 'Importing and verifying ' + this.textEmployee + '...' })
          const resp = await request(`/admin/faas/base/members/import`, 'post', {
            attachment: this.attachment.id,
            type: 'import'
          }, true)
          this.$q.loading.hide()
          if (resp.success) {
            if (resp.data === 'Import processing') {
              this.step = 'cronJob'
              this.$root.$emit('reload-mex-employers-employee')
              return
              // this.cancel()
              // notifySuccess(`Upload success and : ${resp.data}`)
            } else {
              resp.data.forEach(e => {
                this.importMembers.push(e)
              })
              this.$root.$emit('reload-mex-employers-employee')
            }
          } else {
            this.file = null
            this.$root.$emit('show-html-list-dialog', {
              title: 'Error',
              html: resp.message
            })
            return
          }
        } else {
          this.$q.loading.show({ message: 'Creating and verifying ' + this.textEmployee + '...' })
          const resp = await request(`/admin/faas/base/members/import`, 'post', {
            type: 'create',
            member: this.entity
          }, true)
          this.$q.loading.hide()
          if (resp.success) {
            resp.data.forEach(e => {
              this.importMembers.push(e)
            })
            this.$root.$emit('reload-mex-employers-employee')
          } else {
            this.file = null
            this.$root.$emit('show-html-list-dialog', {
              title: 'Error',
              html: resp.message
            })
            return
          }
        }
        this.step = 'list'
      } else if (this.step === 'list' || this.step === 'cronJob') {
        this.cancel()
      }
    },
    back () {
      if (this.step === 'list') {
        this.step = 'init'
      }
    },
    cancel () {
      this._hide()
    },
    hide () {
      this.$root.$emit('mex-employers-employee')
    },
    async downLoadCountryList () {
      this.$q.loading.show({
        message: 'DownLoading...'
      })
      this.searchEmployees = []
      // console.log('asdfghjuytrwqert5')
      const resp = await request(`/admin/faas/base/members/countryTemplate`, 'post', {})
      if (resp.success) {
        this.url = resp.data
        this.$nextTick(() => {
          this.$refs.form.submit()
        })
      }
      setTimeout(() => {
        this.$q.loading.hide()
      }, 1000)
    },
    // downloadTemplate () {
    //   location.href = `/download?path=static/faas/template/members.xlsx`
    // },
    async downloadTemplate () {
      this.$q.loading.show({
        message: 'DownLoading...'
      })
      this.searchEmployees = []
      const resp = await request(`/admin/mex/employer/employees/exportMemberTemplate`, 'post', {
      })
      if (resp.success) {
        this.url = resp.data
        this.$nextTick(() => {
          this.$refs.form.submit()
        })
      }
      setTimeout(() => {
        this.$q.loading.hide()
      }, 1000)
    },
    select () {
      if (this.file) {
        return
      }
      $(this.$refs.file).click()
    },
    selectedFile () {
      const input = this.$refs.file
      if (input.files.length) {
        this.file = input.files[0]
      }
      input.type = 'text'
      input.type = 'file'
    },
    getValidDragFile (e) {
      if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length) {
        const file = e.dataTransfer.files[0]
        console.log(file)
        if (file && file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          return file
        }
      }
      return null
    },
    setupDnd () {
      const area = $(this.$el).find('.upload-area'),
        dom = area.get(0)

      dom.addEventListener('dragover', e => {
        e.stopPropagation()
        e.preventDefault()
        area.addClass('dropping')
      }, false)

      dom.addEventListener('dragleave', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')
      }, false)

      dom.addEventListener('drop', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')

        const file = this.getValidDragFile(e)
        if (file) {
          this.file = file
        } else {
          notify('Please select a .xlsx file.', 'negative')
        }
      }, false)
    }
  }
}
</script>

<style lang="scss">
.faas-import-member-detail-dialog {
  .modal-content {
    width: 500px;
  }
  .split-lint p {
    height: 1px;
    margin-top: 30px;
    margin-bottom: 10px;
    padding-top: 0 !important;
    padding-right: 16px;
    border-top: 1px solid #ddd;
    text-align: center;
  }
  .split-lint span {
    position: relative;
    top: -10px;
    background: #fff;
    padding: 0 20px;
  }
  .gutter-form > div {
    padding-top: 15px;
  }
  .avatar .q-icon {
    padding: 10px;
    background: rgba($color: #4acc3d, $alpha: 0.1);
    color: #4acc3d;
    font-size: 20px;
    border-radius: 40px;
  }
  .q-btn.main {
    background: #005daa;
    width: 100%;
    color: #fff;
  }
  .import-list {
    h1 {
      font-size: 20px;
      color: #191d3d;
      text-align: left;
      margin: 0;
      font-weight: bold;
    }
    p {
      margin: 0;
      text-align: left;
    }
    .import-member-list {
      border: none;
      max-height: 80%;
      overflow: scroll;
      .q-item {
        padding-left: 0;
        padding-right: 0;
      }
    }
  }
}
</style>
