<template>
  <q-dialog class="faas-import-member-error-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Error Message List</div>
    </template>
    <template slot="body">
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Error Message Report</strong>
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>
        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
      <ManageFiltersDialog ref="filtersDialog"
                           :visible.sync="filtersDialog"
                           :filters.sync="filters"
                           :options="filterOptions"
                           @submit="reload"></ManageFiltersDialog>
    </template>
    <template slot="buttons">
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FaadStateListMixin from '../../../../mixins/mex/MexStateListMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import { request, generateColumns, EventHandlerMixin } from '../../../../common'
import ListPageMixin from '../../../../mixins/ListPageMixin'

export default {
  name: 'faas-import-member-error-dialog',
  mixins: [
    Singleton,
    FaadStateListMixin,
    MexPageMixin,
    FreezeColumnMixin,
    ListPageMixin,
    EventHandlerMixin('reload-faas-import-member-error-dialog')
  ],
  components: {
  },
  data () {
    return {
      requestUrl: `/admin/mex/employee/import/errormessage/list`,
      columns: generateColumns([
        'ID', 'Reason'], [], {}),
      filterOptions: []
    }
  },
  mounted () {
  },
  computed: {
  },
  methods: {
    async show () {
      this.data = []
      this.reload()
      // console.log('1234erfgbvcdfewd')
    },
    async reload () {
      // console.log('1234567543')
      await this.request({
        pagination: this.pagination
      })
      this.everLoaded = true
      console.log(this.pagination)
    },
    async request ({ pagination }) {
      this.loading = true
      // console.log('sadfgyt6543wer54e')
      const data = this.$refs.filtersDialog.getFilters()
      data.keyword = this.keyword
      data.importRecord = this.entity.id
      data.type = this.entity.type
      // console.log(this.entity)
      const resp = await request(`/admin/mex/employee/import/errormessage/list/${pagination.page}/${pagination.rowsPerPage}`, 'get', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.count
        this.quick = resp.data.quick
      }
    }
  }
}
</script>

<style lang="scss">
.faas-import-member-error-dialog {
  .modal-content {
    width: 500px;
  }
}
</style>
