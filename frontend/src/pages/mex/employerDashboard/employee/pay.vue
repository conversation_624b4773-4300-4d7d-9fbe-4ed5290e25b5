<template>
  <q-dialog id="mex-employee-pay-dialog"
            prevent-close
            :class="(step === 'list' || step === 'confirmFile') ? 'big-dialog' :''"
            v-model="visible">
    <template slot="title">
      <template>
        <div class="mb-5">
          <span v-if="step !=='done'"
                class="total-img">
            <img src="/static/abel/icons/mex_total_balance.svg">
          </span>
          <span v-else
                class="done-icon">
            <q-icon name="mdi-check-circle"
                    color="positive"
                    class="done-icon"></q-icon>
          </span>
        </div>
        <div v-if="(step ==='init' || step ==='list') && this.user.cpKey === 'cp_mex'"
             class="font-18 mb-2">Create New Payout</div>
        <div v-if="(step ==='init' || step ==='list') && this.user.cpKey === 'cp_faas'"
             class="font-18 mb-2">Pay members and issue cards</div>
        <div v-else-if="step ==='confirm' || step ==='confirmFile'"
             class="font-18 mb-2">Confirm Payout</div>
        <div v-else-if="step ==='done'"
             class="font-18 mb-2">Payout Sent Succesfully</div>
        <div v-if="step ==='init'"
             class="font-14 normal">
          Please search for a user or upload an XLS <span v-if="$store.state.User.cpKey === 'cp_mex'">or NACHA payroll</span> file to start a payout.
        </div>
        <div v-else-if="step==='list' || step==='confirm' || step ==='confirmFile' "
             class="font-14 normal">
          Please review the batch payment below to make sure you have entered all the correct information.
        </div>
        <div v-else-if="step==='done'"
             class="font-14 normal">
          Your payout to your <span>{{ textEmployees }}</span> has been made successfully and should show in their balance.
        </div>
        <div v-else-if="step==='cronJob'"
             class="font-14 normal">
          Your payout to your <span>{{ textEmployees }}</span> has been imported successfully and processing in the background.
        </div>
      </template>
      <q-btn v-if="step !== 'init' && step !== 'done'"
             class="back"
             round
             flat
             @click="back"
             icon="mdi-chevron-left" />
      <q-btn v-if="step == 'init'"
             class="close"
             round
             flat
             @click="cancel"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row">
        <q-search v-if="step==='init' || step ==='list' "
                  icon="search"
                  :value="keyword"
                  type="text"
                  class="col-12 dense"
                  @input="searchEmployee"
                  clearable
                  placeholder="Search...">
        </q-search>
        <div class="col-12">
          <q-popover v-model="showing">
            <div class="row">
              <q-list class="payout-employee-list">
                <q-item v-for="(employee, i) in searchEmployees"
                        :key="i">
                  <q-item-side>
                    <q-item-tile avatar>
                      <img src="/static/img/avatar.png">
                    </q-item-tile>
                  </q-item-side>
                  <q-item-main>
                    <p class="mb-0 employee-name">{{ $c.fullName(employee) }}</p>
                    <p class="mb-0">{{ textEmployee }}</p>
                  </q-item-main>
                  <q-item-side right>
                    <q-item-tile v-if="employee['select']"
                                 icon="mdi-check-circle"
                                 color="green" />
                    <q-btn class="add-employee"
                           v-else
                           @click="addEmployee(employee)"><i class="mdi mdi-account-plus-outline"></i></q-btn>
                  </q-item-side>
                </q-item>
              </q-list>
            </div>
          </q-popover>
        </div>
      </div>
      <div v-if="step==='init'"
           class="pt-20 col-12">
        <div class="upload-area"
             @click="select"
             :class="{selected: file}">
          <img src="/static/wilen/document-icon.svg">
          <template v-if="file">
            <div class="mt-10">Selected file:</div>
            <div class="font-13 text-blue">
              <span>{{ file.name }}</span>
              <a href="javascript:"
                 class="ml-5 link"
                 @click.stop="file = null">
                <q-icon name="mdi-close-circle-outline"
                        class="font-20"
                        color="negative"></q-icon>
                <q-tooltip>Remove and reselect</q-tooltip>
              </a>
            </div>
          </template>
          <template v-else>
            <div class="mt-10">Upload an XLS <span v-if="$store.state.User.cpKey === 'cp_mex'"> or CSV, NACHA formatted</span> file by dragging the file here.</div>
          </template>
        </div>
        <input v-if="$store.state.User.cpKey === 'cp_mex'"
               type="file"
               class="hide"
               accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,.dat,.csv,.mf,.txt"
               ref="file"
               @change="selectedFile">
        <input v-if="$store.state.User.cpKey !== 'cp_mex'"
               type="file"
               class="hide"
               accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
               ref="file"
               @change="selectedFile">
      </div>
      <div v-if="step==='init' && file"
           class="mt-10 pt-20 col-12">
        <q-input float-label="Total Amount"
                 type="number"
                 prefix="$"
                 v-model="inputTotal"></q-input>
      </div>
      <div v-if="step==='init'"
           class="pt-20 col-12">
        <q-datetime type="date"
                    format="MM/DD/YYYY"
                    :min="minDate"
                    placeholder='Select Posting Date'
                    v-model="postingDate"></q-datetime>
      </div>
      <div class="pt-20  col-12"
           v-if="file && step==='init' && hashCheckError">
        <q-checkbox v-model="skipHashCheck"
                    label="Skip File hash duplication check"></q-checkbox>
      </div>
      <div v-if="selectEmployees.length > 0 && (step==='init' || step==='confirm' || step==='done')"
           class="select-list">
        <h1 v-if="step==='init'"
            class="pt-20">Payout List</h1>
        <div class="row">
          <q-list class="payout-employee-list col-12"
                  :class="(step==='confirm' || step==='done') ? 'has-border' : ''">
            <q-item v-for="(employee, i) in selectEmployees"
                    :key="i">
              <q-item-side>
                <q-item-tile avatar>
                  <img src="/static/img/avatar.png">
                </q-item-tile>
              </q-item-side>
              <q-item-main>
                <p class="mb-0 employee-name">{{ $c.fullName(employee) }}</p>
                <p class="mb-0">{{ textEmployee }}</p>
              </q-item-main>
              <q-item-side right>
                <q-input v-if="step ==='init'"
                         type="number"
                         prefix="$"
                         v-model="selectEmployees[i]['amount']"
                         @keyup="onInput"></q-input>
                <p v-if="step !=='init'"
                   class="mb-0 employee-name text-center">${{ employee['amount'] }}</p>
                <p v-if="step !=='init'"
                   class="mb-0">{{ employee['postingDate']}}</p>
              </q-item-side>
            </q-item>
          </q-list>
        </div>
        <div class="total-payout">
          <div>
            <p>Total {{ textEmployees }}</p>
            <p class="text-blod text-center">{{ selectEmployees.length}}</p>
          </div>
          <div>
            <p>TOTAL</p>
            <p class="text-blod text-center">${{ total }}</p>
          </div>
        </div>
      </div>
      <div v-if="step==='list'">
        <q-table :data="selectEmployees"
                 :columns="columns"
                 hide-bottom
                 :pagination.sync="paginitionFile"
                 class="main-table sticky-table mt-16"
                 separator="none">
          <!-- <q-tr slot="header"
                slot-scope="props"
                :props="props">
            <q-th v-for="col in props.cols"
                  :key="col.name"
                  :props="props">
            </q-th>
          </q-tr> -->
          <q-tr slot="body"
                slot-scope="props"
                :props="props">
            <q-td v-for="col in props.cols"
                  :key="col.field">
              <template v-if="col.field ==='Employee' || col.field ==='Member' ">
                <div notranslate="">{{ $c.fullName(props.row) }}</div>
              </template>
              <template v-else-if="col.field ==='Type'">
                <div notranslate="">Checking</div>
              </template>
              <template v-else-if="col.field ==='Payment Date'">
                <div notranslate="">{{ props.row['postingDate'] }}</div>
              </template>
              <template v-else-if="col.field ==='Account #'">
                <div notranslate="">{{ props.row['Barcode Number'] }}</div>
              </template>
              <template v-else-if="col.field ==='Routing'">
                <div notranslate="">{{ props.row['Routing Number'] }}</div>
              </template>
              <template v-else-if="col.field ==='Amount'">
                <div notranslate="">
                  <q-input class="amount-input"
                           type="number"
                           prefix="$"
                           min="0"
                           v-model="selectEmployees[props.row.__index]['amount']"
                           @keyup="onInput"></q-input>
                </div>
              </template>
              <template v-else-if="col.field ==='Action'">
                <div notranslate=""><span @click="deleteSelect(props.row)"
                        class="delete-icon"><i class="mdi mdi-delete-outline"></i></span></div>
              </template>
              <template v-else>
                <div notranslate="">{{ _.get(props.row, col.field) }}</div>
              </template>
            </q-td>
          </q-tr>

        </q-table>
        <div class="total-payout mt-20">
          <div>
            <p>Total {{ textEmployees }}</p>
            <p class="text-blod">{{ selectEmployees.length}}</p>
          </div>
          <div>
            <p>TOTAL</p>
            <p class="text-blod">${{ total }}</p>
          </div>
        </div>
      </div>
      <div v-if="step ==='confirmFile'">
        <div class="row gutter-sm">
          <div class="col-sm-4">
            <q-card class="top-statics">
              <q-card-main>
                <div class="row">
                  <div class="column">
                    <div class="description">Effective date</div>
                    <div class="value">{{ confirmPostingDate }}</div>
                  </div>
                </div>
              </q-card-main>
            </q-card>
          </div>
          <div class="col-sm-4">
            <q-card class="top-statics">
              <q-card-main>
                <div class="row">
                  <div class="column">
                    <div class="description">$$ Total</div>
                    <div class="value">{{ confirmTotal | moneyFormat }}</div>
                  </div>
                </div>
              </q-card-main>
            </q-card>
          </div>
          <div class="col-sm-4">
            <q-card class="top-statics">
              <q-card-main>
                <div class="row">
                  <div class="column">
                    <div class="description">Number of Credits</div>
                    <div class="value">{{ confirmData.length }}</div>
                  </div>
                </div>
              </q-card-main>
            </q-card>
          </div>
        </div>
        <q-table :data="confirmData"
                 class="main-table sticky-table mt-16"
                 separator="none"
                 :columns="confirmColumns"
                 :pagination.sync="paginitionFile"
                 v-if="confirmData.length">
          <q-tr slot="body"
                slot-scope="props"
                :props="props">
            <q-td v-for="col in props.cols"
                  :key="col.field">
              <template v-if="col.field === 'Amount' || col.field === 'amount' || col.field === 'Payout Amount'">
                {{_.get(props.row, col.field) | moneyFormat}}
              </template>
              <template v-else-if="col.field ==='Payment Date'">
                <div v-if="isCustomCsv"
                     notranslate="">{{ _.get(props.row, col.field) }}</div>
                <div v-else
                     notranslate="">{{ confirmPostingDate }}</div>
              </template>
              <template v-else-if="col.field ==='User ID'">
                {{_.get(props.row, 'Employee ID') || _.get(props.row, 'Member ID')}}
              </template>
              <template v-else>
                <div notranslate="">{{ _.get(props.row, col.field) }}</div>
              </template>
            </q-td>
          </q-tr>
        </q-table>
      </div>
      <form ref="form"
            method="post"
            action="/admin/download">
        <input type="hidden"
               name="path"
               v-model="url">
      </form>
    </template>
    <template slot="buttons">
      <div v-if="step === 'init'"
           class="stacks single">
        <q-btn label="Download XLS Payout Template"
               no-caps
               class="down-btn"
               text-color="tertiary"
               @click="downloadTemplate"></q-btn>
        <q-btn label="Continue"
               no-caps
               color="positive"
               class="continue-btn"
               @click="nextConfirm"></q-btn>
      </div>
      <div v-else-if="step === 'list'"
           class="stacks">
        <div class="row">
          <q-btn label="Cancel"
                 no-caps
                 color="negative"
                 @click="cancel"></q-btn>
          <q-btn label="Continue"
                 no-caps
                 color="positive"
                 @click="nextConfirm"></q-btn>
        </div>
      </div>
      <div v-else-if="step === 'confirm'"
           class="stacks single">
        <q-btn label="Send Payout"
               no-caps
               color="positive"
               @click="next"></q-btn>
      </div>
      <div v-else-if="step === 'confirmFile'"
           class="stacks single">
        <q-btn :label="postingDate ? 'Schedule Payout' : 'Send Payout'"
               no-caps
               color="positive"
               :disabled="disableSend"
               @click="nextConfirm"></q-btn>
      </div>
      <div v-else-if="step === 'done' || step === 'cronJob'"
           class="stacks single">
        <q-btn v-if="step === 'cronJob'"
               label="See the Schedule"
               no-caps
               color="positive"
               @click="viewBatch"></q-btn>
        <q-btn label="Done"
               no-caps
               color="positive"
               @click="cancel"></q-btn>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import { moneyFormat, EventHandlerMixin, uploadAttachment, notifyResponse, notify, request, notifySuccess, generateColumns } from '../../../../common'
import $ from 'jquery'
import moment from 'moment'
import { add, bignumber } from 'mathjs'
import { date } from 'quasar'
const { addToDate } = date

export default {
  name: 'mex-employee-pay-dialog',
  mixins: [
    Singleton,
    MexPageMixin,
    EventHandlerMixin('mex-employee-pay-dialog', 'show')
  ],
  data () {
    const type = this.$store.state.User.cpKey === 'cp_mex' ? 'Employee' : 'Member'
    return {
      minDate: null,
      paginitionFile: {
        page: 1,
        rowsPerPage: 10
      },
      url: null,
      showing: false,
      keyword: '',
      file: null,
      attachment: null,
      postingDate: null,
      selectEmployees: [],
      searchEmployees: [],
      selectEmployeeIds: [],
      total: 0,
      step: 'init',
      confirmTotal: 0,
      cnofirmData: [],
      confirmColumns: [],
      confirmPostingDate: null,
      isCustomCsv: false,
      columns: generateColumns([type, 'User ID', 'Payment Date', 'Amount', 'Action'], [], []),
      hashCheckError: false,
      skipHashCheck: false,
      inputTotal: 0,
      batchFileId: null,
      disableSend: false
    }
  },
  watch: {
    file () {
      this.attachment = null
      this.inputTotal = 0
    }
  },
  created () { },
  components: {},
  computed: {},
  methods: {
    init () {
      let today = new Date()
      this.minDate = addToDate(today, { days: 1 })
      this.step = 'init'
      this.keyword = ''
      this.total = 0
      this.showing = false
      this.postingDate = null
      this.selectEmployees = []
      this.searchEmployees = []
      this.selectEmployeeIds = []
      this.file = null
      this.hashCheckError = false
      this.skipHashCheck = false
      this.batchFileId = null
      this.disableSend = false
    },
    cancel () {
      this.init()
      this._hide()
      this.$root.$emit('reload-mex-employers-employee')
    },
    viewBatch () {
      let url = `/admin#/h/mex/employer/reports/batchSummary/detail/${this.batchFileId}`
      this.init()
      this._hide()
      window.open(url, '_blank')
    },
    show () {
      this.init()
      if (this.entity['User ID']) {
        // console.log(this.entity)
        this.selectEmployees.push(this.entity)
        this.entity['select'] = true
        this.selectEmployeeIds.push(this.entity['User ID'])
      }
    },
    back () {
      if (this.step === 'list') {
        this.keyword = ''
        this.showing = false
        this.step = 'init'
      } else if (this.step === 'confirm') {
        this.step = 'list'
      } else if (this.step === 'confirmFile') {
        this.step = 'init'
      }
    },
    async nextConfirm () {
      if (this.file) {
        if (!this.inputTotal) {
          return notifyResponse(`Please input the total amount for the payroll file!`)
        }
        if (!this.attachment) {
          this.$q.loading.show({ message: 'Uploading...' })
          const category = this.user.cpKey === 'cp_faas' ? 'faasPayMembers' : 'transferMexPayEmployees'
          const resp = await uploadAttachment(this.file, category)
          if (typeof resp === 'string') {
            this.$q.loading.hide()
            return notifyResponse(`Failed to upload: ${resp}`)
          }
          this.attachment = resp
        }

        this.$q.loading.show({ message: 'Importing and verifying Pay ' + this.textEmployee + '...' })
        const resp = await request(`/admin/mex/employee/checkPayEmployee`, 'post', {
          attachment: this.attachment.id,
          postingDate: this.postingDate ? moment(this.postingDate).format('MM/DD/YYYY') : 'Immediately',
          confirm: this.step === 'confirmFile',
          skipHashCheck: this.skipHashCheck,
          totalAmount: this.inputTotal
        }, true)
        this.$q.loading.hide()
        if (resp.success) {
          if (resp.data.type && resp.data.type === 'cronJob') {
            this.step = 'confirmFile'
            this.paginitionFile = {
              page: 1,
              rowsPerPage: 10 // 当前每页显示的行数
            }
            this.confirmData = resp.data.data
            this.confirmTotal = resp.data.total
            if (resp.data.fileType === 'NACHA') {
              this.confirmColumns = generateColumns(['accountNumber', 'externalEmployeeId', 'Payment Date', 'amount'], [], [])
            } else if (resp.data.fileType === 'CSV') {
              if (resp.data.isCustomCsv) {
                this.isCustomCsv = true
                this.confirmColumns = generateColumns(resp.data.customFields, [], [])
              } else {
                this.confirmColumns = generateColumns(['ABA Number', 'Account Number', 'Amount', 'ID Number', 'Name'], [], [])
              }
            } else {
              this.confirmColumns = generateColumns(['User ID', 'Account Number', 'First Name', 'Last Name', 'Payment Date', 'Payout Amount'], [], [])
            }
            this.confirmPostingDate = this.postingDate ? moment(this.postingDate).format('MM/DD/YYYY') : 'Immediately'
            this.disableSend = false
            if (!resp.data.rightTotal) {
              notifyResponse(`The total amount entered does not match the total amount (${moneyFormat(resp.data.total, 'USD')}) parsed in the file, please check the payments and modify the payroll!`)
              this.disableSend = resp.data.bigTotal
            }
            return
          } else if (resp.data.step && resp.data.step === 'Import processing') {
            this.step = 'cronJob'
            this.batchFileId = resp.data.id
            return
          } else {
            resp.data.forEach(e => {
              if (this.selectEmployeeIds.indexOf(e['User ID']) === -1) {
                this.selectEmployeeIds.push(e['User ID'])
                e['select'] = true
                this.selectEmployees.push(e)
                this.total = add(bignumber(this.total), bignumber(e['amount']))
              }
            })
          }
          // this.total = this.total.toFixed(2)
        } else {
          if (resp.data && resp.data.type !== 'confirmFile') {
            this.file = null
          }
          if (resp.message.indexOf('is the same as the previously uploaded file') !== -1) {
            this.hashCheckError = true
          }
          this.$root.$emit('show-html-list-dialog', {
            title: 'Error',
            html: resp.message
          })
          return
        }
      }
      let flag = false
      this.selectEmployees.forEach(e => {
        if (!e.amount) {
          flag = true
        }
      })
      if (!this.total || flag) {
        notifySuccess('Please input the payout amount for each ' + this.textEmployee + '.', 'Message', 'negative')
        return
      }
      if (!this.postingDate && this.step === 'init') {
        this.$q.dialog({
          title: 'Confirm',
          message: 'Are you sure that you want to load $' + this.total + ' on ' + this.selectEmployees.length + ' of ' + this.textEmployees + ' ?',
          color: 'pative',
          cancel: true
        }).then(async () => {
          this.next()
        }).catch(() => {})
      } else {
        this.next()
      }
    },
    async next () {
      if (this.step === 'init') {
        if (!this.selectEmployees.length) {
          notifySuccess('Please select at least one ' + this.textEmployee + '.', 'Message', 'negative')
          return
        }
        // console.log(moment(this.postingDate).format('MM/DD/YYYY'))
        this.keyword = ''
        this.selectEmployees.forEach(e => {
          e['postingDate'] = this.postingDate ? moment(this.postingDate).format('MM/DD/YYYY') : 'Immediately'
        })
        this.step = 'list'
      } else if (this.step === 'list') {
        this.step = 'confirm'
      } else if (this.step === 'confirm') {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/employer/employees/pay`, 'post', {
          total: this.total,
          employees: this.selectEmployees,
          postingDate: this.postingDate ? moment(this.postingDate).format('MM/DD/YYYY') : 'Immediately'
        })
        if (resp.success) {
          this.batchId =
          this.step = 'done'
        }
        this.$q.loading.hide()
      }
    },
    async downloadTemplate () {
      this.$q.loading.show({
        message: 'DownLoading...'
      })
      this.searchEmployees = []
      // console.log('asdfghjuytrwqert5')
      const resp = await request(`/admin/mex/employer/employees/exportTemplate`, 'post', {
        // keyword: val, // this.keyword,
        paySearch: true
      })
      if (resp.success) {
        // console.log(resp.data)
        // await request('/admin/download', 'post', {
        //   path: resp.data
        // })

        if (resp.data) {
          this.url = resp.data
          this.$nextTick(() => {
            this.$refs.form.submit()
          })
        } else {
          notifySuccess(resp.message)
        }
      }
      setTimeout(() => {
        this.$q.loading.hide()
      }, 1000)
      // location.href = `/download?path=static/mex/template/employee_pay.xlsx`
    },
    onInput () {
      this.total = 0
      this.selectEmployees.forEach(element => {
        if (element['amount'] > 0) {
          this.total = add(bignumber(this.total), bignumber(element['amount']))
        } else {
          element['amount'] = 0
        }
      })
      // this.total = this.total.toFixed(2)
      console.log(this.total)
      // console.log(this.selectEmployees)
    },
    addEmployee (row) {
      // console.log('asdfghyutre')
      this.searchEmployees.forEach(element => {
        if (element['User ID'] === row['User ID']) {
          element['select'] = true
          if (this.step === 'list') {
            row['postingDate'] = this.postingDate ? moment(this.postingDate).format('MM/DD/YYYY') : 'Immediately'
          }
          this.selectEmployeeIds.push(row['User ID'])
          this.selectEmployees.push(row)
        }
      })
      // console.log(this.searchEmployees)
    },
    deleteSelect (row) {
      let temp = []
      let temIds = []
      this.selectEmployees.forEach(element => {
        if (element['User ID'] !== row['User ID']) {
          temp.push(element)
          temIds.push(element['User ID'])
        }
      })
      this.selectEmployees = temp
      this.selectEmployeeIds = temIds
      // console.log(this.selectEmployees)
      // console.log(this.selectEmployeeIds)
      this.onInput()
    },
    async searchEmployee (val) {
      // console.log('werwqerw123423')
      // console.log(val)
      // console.log(this.keyword)
      this.keyword = val
      if (!val) {
        // console.log('werwqerw')
        return
      }
      this.$q.loading.show({
        message: 'Search...'
      })
      this.searchEmployees = []
      let searchlist = []
      // console.log('asdfghjuytrwqert5')
      const resp = await request(`/admin/mex/employer/employees/list/1/0`, 'get', {
        keyword: val, // this.keyword,
        paySearch: true
      })
      if (resp.success) {
        // console.log(this.selectEmployeeIds)
        if (!resp.data.count) {
          notify('No data found')
        } else {
          resp.data.data.forEach(element => {
            if (this.selectEmployeeIds.indexOf(element['User ID']) !== -1) {
              element['select'] = true
            } else {
              element['select'] = false
            }
            element['amount'] = 0
            searchlist.push(element)
          })
          // console.log(this.searchEmployees)
          this.searchEmployees = searchlist
          this.showing = true
        }
      }
      this.$q.loading.hide()
    },
    select () {
      if (this.file) {
        return
      }
      $(this.$refs.file).click()
    },
    selectedFile () {
      const input = this.$refs.file
      if (input.files.length) {
        this.file = input.files[0]
      }
      input.type = 'text'
      input.type = 'file'
    },
    getValidDragFile (e) {
      if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length) {
        const file = e.dataTransfer.files[0]
        // console.log(file)
        // console.log(file.name.indexOf('.dat') !== -1)
        if (file && this.$store.state.User.cpKey === 'cp_mex' && (file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.name.indexOf('.DAT') !== -1 || file.name.indexOf('.MF') !== -1)) {
          return file
        }
        if (file && this.$store.state.User.cpKey === 'cp_faas' && file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          return file
        }
      }
      return null
    },
    setupDnd () {
      const area = $(this.$el).find('.upload-area'),
        dom = area.get(0)

      dom.addEventListener('dragover', e => {
        e.stopPropagation()
        e.preventDefault()
        area.addClass('dropping')
      }, false)

      dom.addEventListener('dragleave', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')
      }, false)

      dom.addEventListener('drop', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')

        const file = this.getValidDragFile(e)
        if (file) {
          this.file = file
        } else {
          if (this.$store.state.User.cpKey === 'cp_mex') {
            notify('Please select a .xlsx or NACHA file.', 'negative')
          } else {
            notify('Please select a .xlsx file.', 'negative')
          }
        }
      }, false)
    }
  },
  mounted () {
    this.setupDnd()
  }
}
</script>

<style lang="scss">
#mex-employee-pay-dialog {
  .modal-content {
    width: 450px;
  }
  .top-statics {
    text-align: center;
    .column {
      width: 100%;
    }
    .value {
      font-size: 20px;
      font-weight: 700;
    }
    .description {
      color: #666;
      margin-top: -3px;
      font-size: 12px;
    }
  }
  .upload-area {
    border: 2px dashed #e2e2e2;
    border-radius: 14px;
    padding: 25px 15px;
    cursor: pointer;
    display: block;
  }

  .q-search {
    max-width: 402px;
    margin: 0 auto;
  }
  .down-btn {
    font-size: 16px;
    background: #9c8bff;
    .q-btn-inner {
      line-height: 28px;
      color: #fafafb;
    }
  }
  .continue-btn {
    background-color: #7ac142;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
    .q-btn-inner {
      line-height: 28px;
      color: #fafafb;
    }
  }
  .q-if.dense {
    padding: 8px !important;
  }
  .total-img {
    background: rgba($color: #4acc3d, $alpha: 0.1);
    width: 50px;
    padding: 14px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    border-radius: 25px;
  }
  .select-list {
    h1 {
      font-size: 20px;
      color: #191d3d;
      text-align: left;
      margin: 0;
      font-weight: bold;
    }
    p {
      margin: 0;
      text-align: left;
    }
    .payout-employee-list {
      border: none;
      .q-item {
        padding-left: 0;
        padding-right: 0;
      }
    }
    .has-border {
      .q-item {
        border-bottom: 1px solid #959595;
      }
    }
  }
  .q-table-container .q-table-middle {
    margin: 0;
  }
  .done-icon {
    font-size: 60px;
    line-height: 50px;
  }
  .amount-input {
    min-width: 100px;
  }
  .total-payout {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .text-blod {
    font-weight: bold;
  }
  .delete-icon {
    display: flex;
    align-items: center;
    cursor: pointer;
    // justify-content: center;
    padding: 5px;
    font-size: 16px;
    border-radius: 10px;
    border: 1px solid #f1f1f5;
    i {
      margin: 0 auto;
    }
  }
}
#mex-employee-pay-dialog.big-dialog {
  .modal-content {
    width: 700px;
  }
}
.payout-employee-list {
  p {
    color: #959595;
  }
  .employee-name {
    line-height: 1.25;
    font-size: 16px;
    font-weight: 600;
    color: #191d3d;
  }
  .add-employee {
    background: #0062ff;
    color: #fff;
    font-size: 14px;
    padding: 5px 11px;
  }
}
</style>
