<template>
  <q-card id="mex__members__profile_basic"
          class="high-card">
    <q-card-main>
      <div class="text-center mb-20">
        <div class="avatar mt-5">
          <img :src="'/static/img/avatar.png'"
               alt="">
        </div>
        <div class="font-20 bold mt-5">{{ $c.fullName(entity) }}</div>
        <div class="text-faded">{{ entity['User ID'] }}</div>

        <!-- <div class="text-faded mt-15">Balance</div>
        <div class="font-16 heavy text-black mt--3">
          <q-icon name="mdi-refresh"
                  color="positive"
                  class="mr-5 hidden-only"></q-icon>
          <span class="va-m">{{ entity['Balance'] | moneyFormat }}</span>
          <q-icon name="mdi-refresh"
                  color="positive"
                  @click.native="$emit('reload')"
                  class="ml-5 pointer">
            <q-tooltip>Refresh</q-tooltip>
          </q-icon>
        </div> -->

        <q-chip class="font-13 mt-5"
                :class="statusClass(entity['Account Status'])">
          {{ entity['Account Status'] }}
        </q-chip>
        <div class="font-16 heavy text-black mt-15">
          <span class="va-m">{{ entity['Last Payout Amount'] }}</span>
        </div>
        <div class="text-faded mt--3">Last Deposit</div>
      </div>
      <table class="fields-table">
        <tr v-for="(r, i) in fields"
            :key="i">
          <th>{{ r }}</th>
          <td>{{ entity[r] }}</td>
        </tr>
      </table>
    </q-card-main>
  </q-card>
</template>

<script>
import MexMemberMixin from '../../../../../mixins/mex/MexMemberMixin'

export default {
  name: 'mex-members-profile-basic',
  mixins: [
    MexMemberMixin
  ],
  props: {
    entity: {
      type: Object
    }
  },
  data () {
    return {
      fields: [
        'Location', 'Email', 'Phone'
      ]
    }
  }
}
</script>

<style lang="scss">
#mex__members__profile_basic {
  .avatar {
    width: 130px;
    height: 130px;
    background-color: #eee;
    border-radius: 20px;
    margin: 0 auto;
    overflow: hidden;
  }

  .radius-box {
    border: none;
    color: white;
    position: absolute;
    right: 16px;
    top: 16px;
    font-size: 18px;
    padding: 4px 5px;
    line-height: 1em;

    &.bg-positive {
      background-color: #3dd598 !important;
    }
  }

  .fields-table {
    th {
      min-width: 60px !important;
    }
  }
}
</style>
