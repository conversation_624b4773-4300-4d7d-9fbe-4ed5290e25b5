<template>
  <q-card class="wide-card employee-dds-list">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="inner-table m--15"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div v-if="type == 'dd'"
               class="font-16 bold">Direct Deposit PDFs</div>
          <div v-else
               class="font-16 bold">W2s PDFs</div>
        </template>
        <template slot="top-right">
          <q-btn v-if="$store.state.User.cpKey === 'cp_mex' && !$store.state.User.isReadOnlyAdmin"
                 @click="uploadDdPdf"
                 class="btn-sm mr-10"
                 color="orange"
                 icon="mdi-folder-zip-outline"
                 :label="type == 'dd' ? 'Upload Direct Deposit PDF' : 'W2s PDF'"
                 no-caps />
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="font-18"
                 @click="reload"
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          tag="a"
                          link
                          :href="props.row['Url']"
                          target="_blank">
                    <q-item-main>View</q-item-main>
                  </q-item>
                  <q-item v-if=" !$store.state.User.isReadOnlyAdmin" v-close-overlay
                          @click.native="rename(props.row)">
                    <q-item-main>Rename</q-item-main>
                  </q-item>
                  <q-item  v-if=" !$store.state.User.isReadOnlyAdmin" v-close-overlay
                          @click.native="deletePdf(props.row)">
                    <q-item-main>Delete</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </q-card-main>
    <!-- <UploadDirectDepositPdf></UploadDirectDepositPdf> -->
  </q-card>
</template>

<script>
import MexPageMixin from '../../../../../mixins/mex/MexPageMixin'
import ListPageMixin from '../../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, request, notify } from '../../../../../common'
// import UploadDirectDepositPdf from './upload_dd_pdf'

export default {
  name: 'mex-employee-profile-dds',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    EventHandlerMixin('reload-mex-employee-profile-dds')
  ],
  // components: {
  //   UploadDirectDepositPdf
  // },
  props: {
    uid: {
      type: String
    },
    url: {
      type: String
    },
    type: {
      type: String,
      default: 'dd'
    }
  },
  data () {
    return {
      title: 'Employee Payout',
      requestUrl: this.url || (this.type === 'dd' ? `/admin/mex/employee/dds/list` : `/admin/mex/employee/w2s/list`),
      columns: generateColumns([
        'Upload Date', 'PDF file', 'Actions'
      ]),
      keyword: '',
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 5,
        page: 1
      },
      autoLoad: true
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        employee: this.uid
      }
    },
    uploadDdPdf () {
      this.$root.$emit('show-mex-employer-dd-pdf-dialog', {
        'ID': this.uid,
        'isPlatform': !!this.url,
        'type': this.type
      })
    },
    deletePdf (row) {
      console.log('aaaaaaaaaaaaaa')
      let str = 'payroll'
      if (this.type === 'w2s') {
        str = 'W2s'
      }
      console.log(this.type)
      console.log(str)
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to delete this ${str} file`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request(this.url ? `/admin/mex/members/${this.uid}/dds/remove` : `/admin/mex/employee/remove-dd-pdf`, 'post', {
          employeeId: this.uid,
          id: row['id'],
          'type': this.type
        })
        this.loading = false
        if (resp.success) {
          notify(resp.message)
          await this.reload()
        }
      }).catch(() => {})
    },
    rename (row) {
      let str = 'payment'
      if (this.type === 'w2s') {
        str = 'W2s'
      }
      this.$q.dialog({
        title: `Rename ${str} File `,
        message: `Are you sure that you want to rename the ${str} file? Please enter the new name below:`,
        prompt: {
          model: ''
        },
        cancel: true
      }).then(async text => {
        if (!text) {
          return this.$q.notify('The new name cannot be empty!')
        }
        this.loading = true
        const resp = await request(this.url ? `/admin/mex/members/${this.uid}/dds/rename` : `/admin/mex/employee/rename-dd-pdf`, 'post', {
          employeeId: this.uid,
          name: text,
          id: row['id']
        })
        this.loading = false
        if (resp.success) {
          notify(resp.message)
          await this.reload()
        }
      }).catch(() => {})
    }
  }
}
</script>
