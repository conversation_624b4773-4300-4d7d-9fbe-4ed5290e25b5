<template>
  <q-card id="mex__employee__profile_detail"
          class="high-card">
    <q-card-title>
      <div class="row flex-center">
        <div class="font-18 bold">Account Details</div>
        <q-btn icon="mdi-refresh"
               outline
               @click="$emit('reload')"
               class="btn-mini ml-auto"></q-btn>
        <!-- <q-btn icon="mdi-pencil-outline"
               outline
               @click="edit"
               class="btn-mini ml-5"></q-btn> -->
      </div>
    </q-card-title>
    <q-card-main>
      <table class="fields-table mt-10 mb-auto">
        <tr v-for="(r, i) in fields"
            :key="i">
          <th>{{ r }}</th>
          <td>
            {{ entity[r] }}
          </td>
        </tr>
      </table>
      <!-- <div class="row flex-center mt-20 mb-auto">
        <q-btn icon="mdi-file-document-outline"
               no-caps
               color="blue"
               class="btn-sm mb-10"
               v-if="entity['Status'] && (['Initial'].includes(entity['Status']) || entity['Status'].startsWith('KYC Failed'))"
               @click="manual"
               label="Manually Verify KYC"></q-btn>
        <q-btn icon="mdi-file-download-outline"
               no-caps
               color="blue"
               class="btn-sm mb-10"
               v-if="entity['KYC Passed']"
               @click="downloadKYC"
               label="Download KYC Documents"></q-btn>
      </div> -->
    </q-card-main>

    <HtmlListDialog></HtmlListDialog>
    <PromptDialog></PromptDialog>

    <!-- <IdUploadDialog></IdUploadDialog>
    <IdInstructionDialog></IdInstructionDialog>
    <IdSuccessDialog></IdSuccessDialog>
    <IdManualDialog></IdManualDialog> -->

  </q-card>
</template>

<script>
import HtmlListDialog from '../../../../../components/HtmlListDialog'
import PromptDialog from '../../../../../components/PromptDialog'
// import IdUploadDialog from '../id_upload'
// import IdInstructionDialog from '../id_instruction'
// import IdSuccessDialog from '../id_success'
// import IdManualDialog from '../id_manual'
// import { request } from '../../../../../common'
// import _ from 'lodash'

export default {
  name: 'mex-employee-profile-detail',
  components: {
    HtmlListDialog,
    PromptDialog
    // IdUploadDialog,
    // IdInstructionDialog,
    // IdSuccessDialog,
    // IdManualDialog
  },
  props: {
    entity: {
      type: Object
    }
  },
  data () {
    return {
      fields: [
        'First Name', 'Last Name', 'Date of Birth', 'Mailing Address',
        'City', 'Country', 'State / Province', 'Postal Code',
        'External Employee ID'
      ]
    }
  },
  methods: {
    edit () {
      this.$root.$emit('show-mex-employee-detail-dialog', this.entity)
    }
    // manual () {
    //   this.$root.$emit('show-mex-member-id-manual-dialog', this.entity)
    // }
    // async downloadKYC () {
    //   this.$q.loading.show()
    //   const resp = await request(`/admin/mex/employee/${this.entity['User ID']}/id-download`)
    //   this.$q.loading.hide()
    //   if (resp.success && resp.data) {
    //     if (resp.data.length > 1) {
    //       const links = []
    //       _.forEach(resp.data, (v, k) => {
    //         links.push(`<a href="${v}/download">File ${k + 1}</a>`)
    //       })
    //       this.$root.$emit('show-html-list-dialog', {
    //         title: 'Download KYC Documents',
    //         html: `<p>Click on the below links to download the files:</p><ul><li>${links.join('</li><li>')}</li></ul>`
    //       })
    //     } else if (resp.data.length) {
    //       location.href = `${resp.data[0]}/download`
    //     }
    //   }
    // }
  }
}
</script>

<style lang="scss">
#mex__members__profile_detail {
  .q-card-main {
    display: flex;
    flex-direction: column;
    justify-content: start;
    overflow: auto;
  }
}
</style>
