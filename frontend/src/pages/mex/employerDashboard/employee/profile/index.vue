<template>
  <q-page id="mex__employees__profile_page">
    <div class="row gutter-sm mt-0">
      <div class="col-sm-12 col-md-4">
        <BasicCard :entity="entity"
                   @reload="reload(true, true)"></BasicCard>
      </div>
      <div class="col-sm-12 col-md-4">
        <DetailCard :entity="entity"
                    @reload="reload"></DetailCard>
      </div>
      <div class="col-sm-12 col-md-4">
        <NotesCard :uid="uid"></NotesCard>
      </div>
      <div class="col-12">
        <PayoutReport :uid="uid"></PayoutReport>
      </div>
      <div class="col-12"
           v-if="user.cpKey === 'cp_mex'">
        <Dds :uid="uid"></Dds>
      </div>
      <div class="col-12"
           v-if="user.cpKey === 'cp_mex'">
        <Dds :uid="uid"
             :type="'w2s'"></Dds>
      </div>
      <div class="mb-30"></div>
    </div>
    <Reversed></Reversed>
    <UploadDirectDepositPdf></UploadDirectDepositPdf>
  </q-page>
</template>

<script>
import MexPageMixin from '../../../../../mixins/mex/MexPageMixin'
import BasicCard from './basic'
import DetailCard from './detail'
import NotesCard from './notes'
import PayoutReport from './payoutReport'
import Dds from './dds'
import Reversed from '../../payments/reversed'
import { EventHandlerMixin, notifySuccess, request } from '../../../../../common'
import UploadDirectDepositPdf from './upload_dd_pdf'

export default {
  name: 'mex-employee-profile',
  mixins: [
    MexPageMixin,
    EventHandlerMixin('reload-mex-employee-profile')
  ],
  components: {
    BasicCard,
    Reversed,
    DetailCard,
    NotesCard,
    PayoutReport,
    Dds,
    UploadDirectDepositPdf
  },
  data () {
    return {
      title: 'Employee Profile',
      entity: {}
    }
  },
  computed: {
    uid () {
      return this.$route.params.id
    }
  },
  watch: {
    uid () {
      this.reload()
    }
  },
  methods: {
    async reload (indicator = true, force = false) {
      indicator && this.$q.loading.show()
      const resp = await request(`/admin/mex/employee/${this.uid}/profile`, 'get', {
        force
      })
      indicator && this.$q.loading.hide()
      if (resp.success) {
        this.entity = resp.data

        if (force) {
          notifySuccess(resp)
        }
      }
    },
    uploadDdPdf (uid, url = null, type = 'dd') {
      // this.$root.$emit('show-mex-employer-dd-pdf-dialog',)
      this.$root.$emit('uploadPdf', {
        'ID': uid,
        'isPlatform': !!url,
        'type': type
      })
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
#mex__employees__profile_page {
  .q-card {
    &.high-card {
      height: 473px;
    }

    &.wide-card {
      min-height: 300px;

      > .q-card-main {
        position: relative;
        height: 100%;
      }
    }

    overflow: auto;

    > .q-card-primary {
      padding-top: 16px;
    }

    > .q-card-main {
      position: relative;
      height: calc(100% - 67px);
    }
  }

  .q-card-title .font-18.bold {
    height: 33px;
    padding-top: 3px;
  }

  .fields-table {
    width: 100%;

    th {
      padding: 5px 5px 5px 0;
      font-weight: normal;
      font-size: 13px;
      color: #888;
      text-align: left;
      min-width: 100px;
      vertical-align: top;
    }

    td {
      padding: 5px 0 5px 5px;
      text-align: right;
      word-break: break-word;
    }
  }

  .empty-tip {
    height: 100%;
    color: #666;

    .q-icon {
      margin-bottom: 5px;
      font-size: 20px;
    }
  }
}
</style>
