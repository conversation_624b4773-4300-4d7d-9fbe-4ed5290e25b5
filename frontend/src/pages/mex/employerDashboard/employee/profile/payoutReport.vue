<template>
  <q-card class="wide-card employee-payout-list">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="inner-table m--15"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">{{ textEmployee }} {{title}}</div>
          <q-btn icon="mdi-file-download-outline"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm ml-8 export-btn"
                 no-caps></q-btn>
        </template>
        <template slot="top-right">
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10 font-18"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="font-18"
                 @click="reload"
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                <span v-if="props.row['Status'] === 'init'">Pending</span>
                <span v-else-if="props.row['Status'] === 'Executed'">Completed</span>
                <span v-else-if="props.row['Status'] === 'Reversed'">Reversed</span>
                <span v-else-if="props.row['Status'] === 'canceled'">Canceled</span>
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          v-if="props.row['Status'] === 'init' && (props.row['Payment Type'] === 'Employee Payout' || props.row['Payment Type'] === 'Member Payout')"
                          @click.native="reverse(props.row)">
                    <q-item-main>Cancel</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-else-if="props.row['Status'] === 'Executed' && (props.row['Payment Type'] === 'Employee Payout' || props.row['Payment Type'] === 'Member Payout')"
                          @click.native="reverse(props.row)">
                    <q-item-main>Reverse</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </q-card-main>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-card>
</template>

<script>
import MexPageMixin from '../../../../../mixins/mex/MexPageMixin'
import ListPageMixin from '../../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../../../common'

export default {
  name: 'mex-employee-profile-payout',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    EventHandlerMixin('reload-mex-employee-profile-payout')
  ],
  props: {
    uid: {
      type: String
    }
  },
  data () {
    return {
      title: this.$store.state.User.group.fundingType !== 'ACH' ? ' Payout' : ' ACH',
      requestUrl: this.$store.state.User.group.fundingType !== 'ACH' ? `/admin/mex/employee/payout/list` : `/admin/mex/employee/ach/` + this.uid + `/list`,
      downloadUrl: this.$store.state.User.group.fundingType !== 'ACH' ? `/admin/mex/employee/payout/export` : `/admin/mex/employee/ach/` + this.uid + `/export`,
      columns: generateColumns([
        'Payment Type', 'Transfer to', 'Role Type', 'Amount', 'Date & Time', 'Status', 'Actions'
      ]),
      keyword: '',
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 5,
        page: 1
      },
      filterOptions: [
        {
          value: 'filter[uct.accountStatus=]',
          label: 'Status',
          options: [
            { label: 'Canceled', value: 'canceled' },
            { label: 'Pending', value: 'init' },
            { label: 'Completed', value: 'Executed' },
            { label: 'Reversed', value: 'Reversed' }
          ]
        }
      ],
      autoLoad: true
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        employee: this.uid
      }
    },
    statusClass (status) {
      const cls = []
      cls.push({
        'Reversed': 'negative',
        'init': 'pending',
        'Executed': 'positive'
      }[status] || status)
      return cls
    },
    reverse (row) {
      this.$root.$emit('show-mex-payment-reversed-dialog', row)
    }
  }
}
</script>
<style lang="scss">
.employee-payout-list {
  .export-btn {
    background-color: #190644;
    color: #fff;
    .q-btn-inner {
      font-size: 15px !important;
    }
  }
  .q-if.dense {
    padding: 8px !important;
  }
  .q-chip.pending {
    color: #ff9f00;
    background-color: rgba($color: #ff9f00, $alpha: 0.1);
  }
}
</style>
