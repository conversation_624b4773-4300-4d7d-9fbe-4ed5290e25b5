<template>
  <q-dialog class="mex-employer-dd-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div v-if="entity.type && entity.type == 'dd'"
           class="font-16 mb-2">Upload Direct Deposit PDF</div>
      <div v-else-if="entity.type && entity.type == 'w2s'"
           class="font-16 mb-2">Upload W2s PDF</div>
      <div class="font-12 normal">Please select/drag a PDF file to upload.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row flex-center">
        <div class="col-7">
          <div class="upload-area"
               @click="selectFile"
               :class="{selected: file}">
            <q-icon name="mdi-file-document-outline"></q-icon>
            <template v-if="file">
              <div class="mt-10">Selected file:</div>
              <div class="font-13 text-blue">
                <span>{{ file.name }}</span>
                <a href="javascript:"
                   class="ml-5 link"
                   @click.stop="file = null">
                  <q-icon name="mdi-close-circle-outline"
                          class="font-20"
                          color="negative"></q-icon>
                  <q-tooltip>Remove and reselect</q-tooltip>
                </a>
              </div>
            </template>
            <template v-else>
              <div class="mt-10">Upload .pdf</div>
              <div class="font-13 text-dark">(Drop or click here)</div>
            </template>
          </div>
          <input type="file"
                 class="hide"
                 :accept="fileAccept"
                 ref="file"
                 @change="selectedFile">
        </div>
      </div>
      <q-field v-if="entity.type && entity.type == 'dd'"
               class="mt-50 mb-15"
               helper="Fill the payment date.">
        <q-datetime type="date"
                    format="MM/DD/YYYY"
                    placeholder='Select Payment Date'
                    v-model="postingDate">
        </q-datetime>
      </q-field>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn label="Continue"
               no-caps
               color="positive"
               :disable="!file"
               @click="submit" />
        <a href="javascript:"
           class="font-13 mt-10 text-blue"
           @click="notes = !notes">{{ notes ? 'Hide' : 'View' }} notes</a>
        <ul class="notes"
            v-if="notes">
          <li>The PDF file will be attached to employee's profile page.</li>
        </ul>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../../mixins/Singleton'
import { notify, notifyResponse, request, uploadAttachment } from '../../../../../common'
import DndUploadFileMixin from '../../../../../mixins/DndUploadFileMixin'

export default {
  name: 'mex-employer-dd-pdf-dialog',
  mixins: [
    Singleton,
    DndUploadFileMixin
  ],
  data () {
    return {
      file: null,
      attachment: null,
      notes: false,
      acceptFileTypes: [
        '.pdf'
      ],
      postingDate: null
    }
  },
  watch: {
    file () {
      this.attachment = null
    }
  },
  methods: {
    show () {
      this.file = null
      this.attachment = null
      this.notes = false
      this.postingDate = null
    },
    isFileUploaded () {
      return this.attachment
    },
    async submit () {
      if (!this.file) {
        return
      }
      if (!this.postingDate && this.entity.type === 'dd') {
        return
      }
      if (!this.attachment) {
        this.$q.loading.show({ message: 'Uploading...' })
        const resp = await uploadAttachment(this.file, 'mex_dd_pdf')
        if (typeof resp === 'string') {
          this.$q.loading.hide()
          return notifyResponse(`Failed to upload: ${resp}`)
        }
        this.attachment = resp
      }
      this.$q.loading.show({ message: 'Processing...' })
      let resp = null
      if (this.entity['isPlatform']) {
        resp = await request(`/admin/mex/members/${this.entity['ID']}/upload-dd-pdf`, 'post', {
          attachment: this.attachment.id,
          postingDate: this.postingDate,
          type: this.entity.type
        }, true)
      } else {
        resp = await request(`/admin/mex/employee/upload-dd-pdf`, 'post', {
          attachment: this.attachment.id,
          postingDate: this.postingDate,
          employeeId: this.entity['ID'],
          type: this.entity.type
        }, true)
      }

      this.$q.loading.hide()
      if (resp.success) {
        this.$root.$emit('reload-mex-employee-profile-dds')
        this._hide()
        notify(resp.data)
      }
    }
  }
}
</script>

<style lang="scss">
.mex-employer-dd-dialog {
  .modal-content {
    width: 450px;
  }

  .notes {
    margin-top: 10px;
    font-size: 13px;
    color: var(--q-color-faded);

    i {
      font-style: normal;
      border: 1px solid #ddd;
      border-radius: 3px;
      padding: 0 5px;
    }
  }
}
</style>
