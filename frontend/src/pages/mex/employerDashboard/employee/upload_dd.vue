<template>
  <q-dialog class="mex-employer-dd-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">{{title}}</div>
      <div class="font-12 normal">Please select/drag a ZIP or PDF file to upload.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row flex-center">
        <div class="col-7">
          <div class="upload-area"
               @click="selectFile"
               :class="{selected: file}">
            <q-icon name="mdi-file-document-outline"></q-icon>
            <template v-if="file">
              <div class="mt-10">Selected file:</div>
              <div class="font-13 text-blue">
                <span>{{ file.name }}</span>
                <a href="javascript:"
                   class="ml-5 link"
                   @click.stop="file = null">
                  <q-icon name="mdi-close-circle-outline"
                          class="font-20"
                          color="negative"></q-icon>
                  <q-tooltip>Remove and reselect</q-tooltip>
                </a>
              </div>
            </template>
            <template v-else>
              <div class="mt-10">Upload .zip/.pdf</div>
              <div class="font-13 text-dark">(Drop or click here)</div>
            </template>
          </div>
          <input type="file"
                 class="hide"
                 :accept="fileAccept"
                 ref="file"
                 @change="selectedFile">
        </div>
      </div>
      <q-field v-if="!isW2s"
               class="mt-50 mb-15"
               helper="Fill when the external employee ID in our database has a prefix but the PDF does not.">
        <q-input float-label="External Employee ID Prefix"
                 v-model="prefix"></q-input>
      </q-field>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn label="Continue"
               no-caps
               color="positive"
               :disable="!file"
               @click="submit" />
        <a href="javascript:"
           class="font-13 mt-10 text-blue"
           @click="notes = !notes">{{ notes ? 'Hide' : 'View' }} notes</a>
        <ul class="notes"
            v-if="notes">
          <li>The ZIP file needs to include the PDF file(s) with the direct deposit data.</li>
          <li>We will extract the <i v-if="isW2s">SSN</i><i v-else>ID</i> from the PDF files and match with <span v-if="isW2s">the last four SSN.</span><span v-else>the external employee ID.</span></li>
          <li>The PDF files will be split and attached to employee's profile page.</li>
        </ul>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notify, notifyResponse, request, uploadAttachment } from '../../../../common'
import DndUploadFileMixin from '../../../../mixins/DndUploadFileMixin'

export default {
  name: 'mex-employer-dd-dialog',
  mixins: [
    Singleton,
    DndUploadFileMixin
  ],
  data () {
    return {
      file: null,
      attachment: null,
      notes: false,
      acceptFileTypes: [
        '.zip',
        '.pdf'
      ],
      prefix: '',
      title: 'Upload Direct Deposit PDFs',
      isW2s: false
    }
  },
  watch: {
    file () {
      this.attachment = null
    }
  },
  methods: {
    show (data) {
      this.file = null
      this.attachment = null
      this.notes = false
      this.title = data.w2 ? 'Upload W2s PDFs' : 'Upload Direct Deposit PDFs'
      this.isW2s = data.w2
    },
    isFileUploaded () {
      return this.attachment
    },
    async submit () {
      if (!this.file) {
        return
      }
      if (!this.attachment) {
        this.$q.loading.show({ message: 'Uploading...' })
        const resp = await uploadAttachment(this.file, this.isW2s ? 'mex_w2s_pdf' : 'mex_dd_pdf')
        if (typeof resp === 'string') {
          this.$q.loading.hide()
          return notifyResponse(`Failed to upload: ${resp}`)
        }
        this.attachment = resp
      }
      this.$q.loading.show({ message: 'Processing...' })
      let resp = null
      if (this.entity['Employer ID']) {
        resp = await request(`/admin/mex/employer/upload-dd-pdf`, 'post', {
          attachment: this.attachment.id,
          prefix: this.prefix,
          employerId: this.entity['Employer ID'],
          isWs: this.isW2s
        }, true)
      } else {
        resp = await request(`/admin/mex/employer/employees/upload-dd-pdf`, 'post', {
          attachment: this.attachment.id,
          prefix: this.prefix,
          isWs: this.isW2s
        }, true)
      }
      this.$q.loading.hide()
      if (resp.success) {
        this._hide()
        notify(resp.data)
      }
    }
  }
}
</script>

<style lang="scss">
.mex-employer-dd-dialog {
  .modal-content {
    width: 450px;
  }

  .notes {
    margin-top: 10px;
    font-size: 13px;
    color: var(--q-color-faded);

    i {
      font-style: normal;
      border: 1px solid #ddd;
      border-radius: 3px;
      padding: 0 5px;
    }
  }
}
</style>
