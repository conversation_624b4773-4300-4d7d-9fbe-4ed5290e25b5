<template>
  <q-dialog class="mex-batch-action-dialog"
            v-model="visible">
    <template slot="title">
      <template>
        <div class="mb-5">
          <span v-if="step !== 'done'"
                class="total-img">
            <img src="/static/abel/icons/mex_phone_icon.svg">
          </span>
          <span v-else
                class="done-icon">
            <q-icon name="mdi-check-circle"
                    color="positive"
                    class="done-icon"></q-icon>
          </span>
        </div>
        <div v-if="step === 'init'"
             class="font-18 mb-2">Two-Factor Authentication</div>
        <div v-else-if="step === 'confirm'"
             class="font-18 mb-2">
          <span>Are you sure you want to {{entity['type']}} this batch payout?</span>
        </div>
        <div v-else-if="step === 'done'"
             class="font-18 mb-2">
          <span>Your batch payouts has been {{doneStr}} successfully.</span>
        </div>
        <div v-if="step === 'init'"
             class="font-14 normal">
          Please enter the 6 digit code sent to the mobile phone associated with your account.
        </div>
      </template>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div v-if="step ==='init'"
           class="row justify-center mb-15">
        <q-input v-model="chars[i]"
                 class="narrow"
                 :ref="`input_${i}`"
                 @keyup="onInput(i, $event)"
                 v-for="(v, i) in chars"
                 :key="i"></q-input>
      </div>
      <div v-else-if="step ==='confirm'">
        <p class="desc-item"><span>File Name</span><span class="value-item">{{ entity['File name']}}</span></p>
        <p class="desc-item"><span>Create Date</span><span class="value-item">{{ entity['Batch Create Date']}}</span></p>
        <p class="desc-item"><span>Total Load</span><span class="value-item">{{ entity['Total Load']}}</span></p>
        <p v-if="entity['type'] !== 'reschedule' && entity['type'] !== 'reset' "
           class="desc-item"><span>Send Date</span><span class="value-item">{{ entity['Batch Send Date']}}</span></p>
        <p v-if="entity['type'] === 'reschedule'"
           class="desc-item"><span>Old Send Date</span><span class="value-item">{{ entity['Batch Send Date']}}</span></p>
        <p class="desc-item"
           v-if="entity['type'] === 'reschedule' || entity['type'] === 'reset'">
          <span>New Send Date</span>
          <q-datetime type="date"
                      format="MM/DD/YYYY"
                      :min="minDate"
                      placeholder='Select New Send Date'
                      v-model="postingDate">
          </q-datetime>
        </p>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn v-if="step==='init'"
               label="Confirm"
               no-caps
               color="positive"
               class="continue-btn"
               @click="next" />
        <q-btn v-if="step==='confirm'"
               label="Yes"
               no-caps
               color="positive"
               class="continue-btn"
               @click="next" />
        <q-btn v-if="step==='confirm'"
               label="No"
               no-caps
               color="negative"
               class="continue-btn"
               @click="close()" />
        <q-btn v-if="step==='done'"
               label="Done"
               no-caps
               color="positive"
               class="continue-btn"
               @click="close()" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../../mixins/Singleton'
import { EventHandlerMixin, ref, request, notify } from '../../../../../common'
import MexPageMixin from '../../../../../mixins/mex/MexPageMixin'
import moment from 'moment'
import { date } from 'quasar'
const { addToDate } = date

export default {
  name: 'mex-batch-action-dialog',
  mixins: [
    Singleton,
    MexPageMixin,
    EventHandlerMixin('mex-batch-action-dialog', 'show')
  ],
  data () {
    return {
      step: 'init',
      doneStr: '',
      postingDate: null,
      minDate: null,
      chars: []
    }
  },
  created () { },
  components: {},
  computed: {
    sms () {
      return this.chars.join('').trim()
    }
  },
  validations: {
    chars: {
      valid () {
        return this.sms && this.sms.length === this.chars.length
      }
    }
  },
  methods: {
    reset () {
      let today = new Date()
      this.step = 'init'
      this.postingDate = null
      this.minDate = addToDate(today, { days: 1 })
      this.chars = []
      for (let i = 0; i < 6; i++) {
        this.chars.push('')
      }
    },
    doneString () {
      let cls = ''
      cls = {
        'cancel': 'canceled',
        'send': 'sent',
        'reschedule': 'rescheduled'
      }[this.entity['type']] || this.entity['type']
      return cls
    },
    async show () {
      this.reset()
      this.visible = false
      // console.log(this.entity)
      if (this.$store.state.User.isMasterLogin) {
        this.step = 'confirm'
        this.visible = true
        this.doneStr = this.doneString()
      } else {
        this.step = 'init'
        const resp = await this.init()
        if (resp.success) {
          this.visible = true
          this.doneStr = this.doneString()
        }
      }
    },
    async init (msg) {
      this.$q.loading.show({
        message: 'Sending the unlock code'
      })
      const resp = await request(`/admin/mex/user/sms`, 'post')
      this.$q.loading.hide()
      if (msg && resp.success) {
        notify()
      }
      return resp
    },
    async next () {
      if (this.step === 'init') {
        if (!this.sms || this.sms.length < this.chars.length) {
          return
        }
        this.$q.loading.show({
          message: 'Verifying...'
        })
        const resp = await request(`/admin/mex/user/sms-verify`, 'post', {
          code: this.sms
        })
        if (resp.success) {
          this.step = 'confirm'
        }
        this.$q.loading.hide()
      } else if (this.step === 'confirm') {
        if (this.entity['type'] === 'cancel') {
          this.$q.loading.show({
            message: 'Canceling...'
          })
        } else if (this.entity['type'] === 'send') {
          this.$q.loading.show({
            message: 'Sending...'
          })
        } else if (this.entity['type'] === 'reschedule') {
          this.$q.loading.show({
            message: 'Rescheduling...'
          })
        } else if (this.entity['type'] === 'reset') {
          this.$q.loading.show({
            message: 'Reset...'
          })
        }
        const resp = await request(`/admin/mex/employer/reports/batch/action`, 'post', {
          type: this.entity.type,
          id: this.entity['Batch ID'],
          postingDate: this.postingDate ? moment(this.postingDate).format('MM/DD/YYYY') : ''
        })
        if (resp.success) {
          this.step = 'done'
          this.$root.$emit('reload-mex-employers-batch-summary-report')
        }
        this.$q.loading.hide()
      }
    },
    close () {
      this.step = 'init'
      this._hide()
    },
    filledChars () {
      if (!this.sms) {
        return -1
      }
      const chars = this.sms.split('')
      let pos = 0
      let update = false
      for (let i = 0; i < this.chars.length; i++) {
        if (this.chars[i] !== (chars[i] || '')) {
          update = true
        }
        this.chars[i] = chars[i] || ''
        if (this.chars[i]) {
          pos = i + 1
        }
      }
      if (update) {
        this.$forceUpdate()
      }
      return pos
    },
    onInput (index, evt) {
      if (evt.key === 'Backspace') {
        if (index > 0) {
          const input = ref(this.$refs[`input_${index - 1}`])
          input && input.focus()
        }
      } else if (evt.key === 'Enter') {
        if (index >= this.chars.length - 1) {
          this.submit()
        }
      } else {
        let filled = this.filledChars()
        if (filled >= this.chars.length) {
          filled = this.chars.length - 1
        }
        const input = ref(this.$refs[`input_${filled}`])
        input && input.focus()
      }
    }
  }
}
</script>

<style lang="scss">
.mex-batch-action-dialog {
  .modal-content {
    width: 450px;
  }
  .down-btn {
    font-size: 16px;
    background: #9c8bff;
    .q-btn-inner {
      line-height: 28px;
      color: #fafafb;
    }
  }
  .continue-btn {
    background-color: #7ac142;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
    .q-btn-inner {
      line-height: 28px;
      color: #fafafb;
    }
  }
  .q-if.dense {
    padding: 8px !important;
  }

  .total-img {
    background: rgba($color: #4acc3d, $alpha: 0.1);
    width: 50px;
    padding: 14px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    border-radius: 25px;
  }
  .done-icon {
    font-size: 60px;
    line-height: 50px;
  }
  .q-if.narrow {
    width: 42px;
    font-size: 22px;

    + .q-if.narrow {
      margin-left: 8px;
    }

    input {
      text-align: center;
    }
  }
  .amount-item {
    color: #01da33;
    font-weight: bold;
  }
  .desc-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    margin: 0;
    padding: 23px 0;
    border-bottom: 1px solid #5a5a89;
    .value-item {
      color: #191d3d;
      font-weight: bold;
    }
  }
}
</style>
