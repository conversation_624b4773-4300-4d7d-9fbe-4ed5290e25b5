<template>
  <q-page id="mex__reports_daily__index_page">
    <div class="page-header">
      <div class="title mt-15">{{ title }}</div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn v-if="!$store.state.User.isReadOnlyAdmin"
                 icon="mdi-refresh"
                 color="orange"
                 label="Update Now"
                 @click="forceReload"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn icon="mdi-file-download-outline"
                 label="Export as XLSX"
                 color="primary"
                 @click="download"
                 class="btn-sm mr-8 export-btn"
                 no-caps></q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="font-18"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <div notranslate="">{{ _.get(props.row, col.field) }}</div>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../../mixins/ListPageMixin'
import { generateColumns } from '../../../../../common'
import MexPageMixin from '../../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../../mixins/FreezeColumnMixin'
import ForceReloadMixin from '../../../../../mixins/ForceReloadMixin'

export default {
  name: 'mex-employers-daily-activity-report',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    ForceReloadMixin
  ],
  mounted () {
    this.title = this.user.cpKey === 'cp_mex' ? 'Daily Employer Activity Report' : 'Daily Client Activity Report'
    let columns = []
    if (this.user.cpKey === 'cp_mex') {
      columns = [
        'Date', 'Beginning Balance', 'Employer Deposits', 'Employer Adjustments', 'Employees Payments',
        'Employees Paid', 'Avg Payment', 'Employees Reversals', 'Employees Reversed', 'Ending Balance'
      ]
    } else {
      columns = [
        'Date', 'Beginning Balance', 'Client Deposits', 'Client Adjustments', 'Members Payments',
        'Members Paid', 'Avg Payment', 'Members Reversals', 'Members Reversed', 'Ending Balance'
      ]
    }
    this.columns = generateColumns(columns, columns)
  },
  data () {
    return {
      title: '',
      chartDateRange: 'month',
      dateRange: 'month',
      requestUrl: `/admin/mex/employer/reports/daily/list`,
      downloadUrl: `/admin/mex/employer/reports/daily/export`,
      columns: [],
      filterOptions: [],
      autoLoad: true,
      freezeColumn: -1,
      keyword: '',
      forceSync: true
    }
  }
}
</script>
