<template>
  <q-page id="mex__reports_month__index_page">
    <div class="page-header">
      <div class="title mt-15">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="type"
                  class="dense"
                  @input="reload"
                  stack-label="Type"
                  :options="typeList"></q-select>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3  col-xs-6"
             v-for="(n, i) in numbers"
             :key="i">
          <q-card>
            <q-card-title>
              <span>{{n.title}}</span>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy">
                <span v-if="n.title === 'Total Deposits'">
                  {{quick['totalDeposits'] ? quick['totalDeposits'] : 0 | moneyFormat }}</span>
                <span v-if="n.title === 'Total Payouts'">
                  {{quick['totalPayouts'] ? quick['totalPayouts'] : 0 | moneyFormat }}</span>
                <span v-if="n.title === 'Total Reversals'">
                  {{quick['totalReversals'] ? quick['totalReversals'] : 0 | moneyFormat }}</span>
                <span v-if="n.title === 'Total Balance'">
                  {{quick['totalBalance'] ? quick['totalBalance'] : 0 | moneyFormat }}</span>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn v-if="!$store.state.User.isReadOnlyAdmin"
                 icon="mdi-refresh"
                 color="orange"
                 label="Update Now"
                 @click="forceReload"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn icon="mdi-file-download-outline"
                 label="Export as XLSX"
                 color="primary"
                 @click="download"
                 class="btn-sm mr-8 export-btn"
                 no-caps></q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="font-18"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <div notranslate="">{{ _.get(props.row, col.field) }}</div>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../../mixins/ListPageMixin'
import { generateColumns } from '../../../../../common'
import MexPageMixin from '../../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../../mixins/FreezeColumnMixin'
import ForceReloadMixin from '../../../../../mixins/ForceReloadMixin'
import _ from 'lodash'

export default {
  name: 'mex-employers-month-activity-report',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    ForceReloadMixin
  ],
  watch: {
    type: {
      immediate: true,
      handler () {
        this.updateTitleAndColumns()
      }
    },
    scope () {
      this.reload()
    }
  },
  mounted () {
    this.updateTitleAndColumns()
  },
  data () {
    return {
      title: '',
      requestUrl: `/admin/mex/employer/reports/month/list`,
      downloadUrl: `/admin/mex/employer/reports/month/export`,
      columns: [],
      filterOptions: [],
      autoLoad: true,
      freezeColumn: -1,
      keyword: '',
      forceSync: true,
      type: 'month',
      numbers: [
        { title: 'Total Deposits', count: 0, value: 0 },
        { title: 'Total Payouts', count: 0, value: 0 },
        { title: 'Total Reversals', count: 0, value: 0 },
        { title: 'Total Balance', count: 0, value: 0 }
      ],
      typeList: [
        { value: 'month', label: 'Monthly' },
        // { value: 'week', label: 'Weekly' },
        { value: 'day', label: 'Daily' }
      ]
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        force: this.force,
        type: this.type
      }
    },
    getTypeName () {
      if (this.type === 'day') {
        return 'daily'
      }
      return this.type + 'ly'
    },
    updateTitleAndColumns () {
      let prefix = this.user.cpKey === 'cp_mex' ? 'Employer' : 'Client'
      this.title = prefix + ' ' + _.startCase(this.getTypeName()) + ' Deposit & Payout Report'
      let columns = []
      if (this.user.cpKey === 'cp_mex') {
        columns = this.$store.state.User.isDebugLogin ? [
          _.startCase(this.type), 'Beginning Balance', 'Employer Deposits', 'Deposits Count', 'Employer Adjustments', 'Employees Payments', 'Payments Count', 'Real Employees Payments', 'Real Payments Count', 'Reversals', 'Reversals Count', 'Ending Balance'
        ] : [
          _.startCase(this.type), 'Beginning Balance', 'Employer Deposits', 'Deposits Count', 'Employer Adjustments', 'Employees Payments', 'Payments Count', 'Reversals', 'Reversals Count', 'Ending Balance'
        ]
      } else {
        columns = [
          _.startCase(this.type), 'Beginning Balance', 'Client Deposits', 'Deposits Count', 'Client Adjustments', 'Members Payments', 'Payments Count', 'Reversals', 'Reversals Count', 'Ending Balance'
        ]
      }
      this.columns = generateColumns(columns, columns)
    }
  }
}
</script>
