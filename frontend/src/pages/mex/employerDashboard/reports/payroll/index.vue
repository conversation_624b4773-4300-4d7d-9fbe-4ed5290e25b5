<template>
  <q-page id="mex__employer_payroll_exceptions__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-usd"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="description">Total Exceptions</div>
                  <div class="value font-mono">{{ quick.total || 0 | moneyFormat }}</div>
                  <div class="sub-description">{{ quick.totalCount || 0 }} transactions</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-usd"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="description">Total Processed</div>
                  <div class="value font-mono">{{ quick.success || 0 | moneyFormat }}</div>
                  <div class="sub-description">{{ quick.successCount || 0 }} transactions</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-usd"
                        color="negative"></q-icon>
                <div class="column">
                  <div class="description">Total Errors</div>
                  <div class="value font-mono">{{ quick.error || 0 | moneyFormat}}</div>
                  <div class="sub-description">{{ quick.errorCount || 0 }} transactions</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <!-- <q-icon name="mdi-cash-usd"
                        color="negative"></q-icon> -->
                <div class="column">
                  <div class="description">% Resolved</div>
                  <div class="value">{{ quick.percent || 0 | percent(1, true)}}</div>
                  <div class="sub-description">Total Processed / Total Exceptions</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }} Report</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-13"
                      :class="props.row['Status'] !== 'Error' ? 'positive' : 'negative'">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else>
              <div class="mex-amount" v-if="col.field === 'Amount'">
                {{ _.get(props.row, col.field) }}
              </div>
              <div v-else notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../../../common'
import MexPageMixin from '../../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../../mixins/FreezeColumnMixin'

export default {
  name: 'mex-employer-payroll_exceptions',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-employer-payroll_exceptions')
  ],
  data () {
    return {
      title: 'Payroll Exceptions',
      requestUrl: `/admin/mex/employer/reports/payroll_exceptions/list`,
      downloadUrl: `/admin/mex/employer/reports/payroll_exceptions/export`,
      columns: generateColumns([
        'Exception Date/Time',
        'Payroll Batch ID',
        'File Name',
        'Payroll File Type',
        'Member ID',
        'External Employee ID',
        'First Name',
        'Last Name',
        'Email',
        'Amount',
        'Error Reason',
        'Status'
      ]),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'Member ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'Member First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Member Last Name'
        }, {
          value: 'filter[pr.externalEmployeeId=]',
          label: 'External Employee Id'
        }, {
          value: 'filter[pr.status=]',
          label: 'Status',
          options: [
            { label: 'Error', value: 'Error' },
            { label: 'Success', value: 'Success' }
          ]
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 1,
      autoLoad: true,
      keyword: ''
    }
  },
  methods: {
  }
}
</script>
<style lang="scss">
#mex__employer_payroll_exceptions__index_page {
  .top-statics .description {
    font-size: 8px;
    color: #1c1446;
    font-weight: 600;
  }
}
</style>
