<template>
  <q-dialog
    class="mex-employer-assign-dialog"
    v-model="visible"
  >
    <template slot="title">
      <div class="mb-5">
        <div class="radius-box">
          <q-icon name="mdi-credit-card" class="font-22"></q-icon>
        </div>
      </div>
      <div class="font-16 mb-2">{{ change ? 'Change Card' : 'Assign a Card' }}</div>
      <div class="font-12 normal">Please assign a {{ change ? 'new' : '' }} card to <strong notranslate="">{{ $c.fullName(entity) }}</strong> by scanning or entering the barcode number.</div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <q-input float-label="Account Number" autocomplete="no"
               class="mt-20 mb-40"
               :error="$v.entity['Barcode Number'].$error"
               @blur="$v.entity['Barcode Number'].$touch"
               v-model="entity['Barcode Number']"></q-input>
      <q-input float-label="Full Card Number" autocomplete="no"
               class="mt-40 mb-10"
               :error="$v.entity['Full Card Number'].$error"
               @blur="$v.entity['Full Card Number'].$touch"
               v-model="entity['Full Card Number']"></q-input>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn :label="change ? 'Change Card' : 'Assign Card'" no-caps
               color="positive"
               @click="submit" />
        <q-btn label="Close" no-caps
               color="grey-3" class="ml-0 mt-8"
               text-color="tertiary"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { required } from 'vuelidate/lib/validators'
import { notifyForm, request } from '../../../common'

export default {
  name: 'mex-employer-assign-dialog',
  mixins: [
    Singleton
  ],
  computed: {
    change () {
      return this.origin && this.origin.change
    }
  },
  validations: {
    entity: {
      'Barcode Number': {
        required
      },
      'Full Card Number': {
        required
      }
    }
  },
  methods: {
    show () {
      this.$set(this.entity, 'Barcode Number', '')
      this.$set(this.entity, 'Full Card Number', '')
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      const data = {
        barcodeNumber: this.entity['Barcode Number'],
        fullCardNumber: this.entity['Full Card Number']
      }
      if (this.change) {
        data.change = true
        data.token = this.entity.token
      }
      const resp = await request(`/admin/mex/employers/${this.entity['Employer ID']}/assign`, 'post', data)
      this.$q.loading.hide()
      if (resp.success) {
        this.$root.$emit('reload-mex-employers')
        this._hideAndEmit('show-mex-member-assigned-dialog', this.entity)
      }
    }
  }
}
</script>

<style lang="scss">
.mex-employer-assign-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
