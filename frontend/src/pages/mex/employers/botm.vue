<template>
  <q-dialog class="mex-employer-botm-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">BOTM Account Configuration</div>
      <div class="font-12 normal text-dark">
        Please fill in the BOTM parameters below about the {{ user.cpKey === 'cp_mex' ? 'employer' : 'client' }}
        "{{ entity['Employer Name'] ? entity['Employer Name'] : entity['Client Name'] }}".
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-20">
        <div class="col-12">
          <q-input float-label="Business ID"
                   autocomplete="no"
                   readonly
                   v-model="entity['Business ID']"></q-input>
        </div>
      </div>

      <div class="row gutter-form mt--10 mb-20">
        <div class="col-12">
          <q-input float-label="Business Account ID"
                   autocomplete="no"
                   v-model="entity['Business Account ID']"></q-input>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel"
             no-caps
             color="grey-3"
             text-color="tertiary"
             @click="_hide" />
      <q-btn label="Save Changes"
             no-caps
             color="positive"
             class="main"
             @click="save" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request } from '../../../common'

const validations = {
  entity: {}
}

export default {
  name: 'mex-employer-botm-dialog',
  mixins: [
    Singleton
  ],
  validations,
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  methods: {
    async show () {
      this.$q.loading.show()
      let url = ''
      if (this.user.cpKey === 'cp_mex') {
        url = `/admin/mex/employers/${this.entity['Employer ID']}/botm`
      } else if (this.user.cpKey === 'cp_faas') {
        url = `/admin/faas/base/clients/${this.entity['Client ID']}/botm`
      }
      const resp = await request(url)
      this.$q.loading.hide()
      if (resp.success) {
        this.$set(this.entity, 'Business ID', resp.data.businessId)
        this.$set(this.entity, 'Business Account ID', resp.data.businessAccountId)
      }
    },
    async save () {
      const accountId = this.entity['Business Account ID'] || ''
      if (!accountId && this.entity['Funding Type'] === 'Prefunded') {
        return notifyForm()
      }
      this.$q.loading.show()
      let url = ''
      if (this.user.cpKey === 'cp_mex') {
        url = `/admin/mex/employers/${this.entity['Employer ID']}/save-botm`
      } else if (this.user.cpKey === 'cp_faas') {
        url = `/admin/faas/base/clients/${this.entity['Client ID']}/save-botm`
      }
      const resp = await request(url, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.mex-employer-botm-dialog {
  .modal-content {
    width: 400px;
  }
}
</style>
