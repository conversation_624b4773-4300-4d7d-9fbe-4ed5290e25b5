<template>
  <q-dialog class="mex-employer-botm-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Deposit Record</div>
      <div class="font-12 normal text-dark">
        Please fill in the deposit parameters below for the {{ user.cpKey === 'cp_mex' ? 'employer' : 'client' }}
        "{{ depositInfo['Employer Name'] ? depositInfo['Employer Name'] : depositInfo['Client Name'] }}".
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-20">
        <div class="col-12 pt-15 text-left">
          <div class="font-12">Transaction Type</div>
          <div class="mt-8">
            <q-radio v-model="depositInfo['Transaction Type']"
                     color="blue"
                     :error="$v.depositInfo['Transaction Type'].$error"
                     @change="$v.depositInfo['Transaction Type'].$touch"
                     val="CREDIT"
                     label="CREDIT"></q-radio>
            <q-radio v-model="depositInfo['Transaction Type']"
                     color="blue"
                     class="ml-15"
                     :error="$v.depositInfo['Transaction Type'].$error"
                     @change="$v.depositInfo['Transaction Type'].$touch"
                     val="DEBIT"
                     label="DEBIT"></q-radio>
          </div>
        </div>
        <div class="col-12">
          <q-input float-label="Amount"
                   autocomplete="no"
                   :error="$v.depositInfo['Amount'].$error"
                   @change="$v.depositInfo['Amount'].$touch"
                   v-model="depositInfo['Amount']"></q-input>
        </div>
        <div class="col-12">
          <q-input float-label="Transaction ID"
                   autocomplete="no"
                   :error="$v.depositInfo['Transaction ID'].$error"
                   @change="$v.depositInfo['Transaction ID'].$touch"
                   v-model="depositInfo['Transaction ID']"></q-input>
        </div>
        <div class="col-12">
          <q-datetime float-label="Date"
                      autocomplete="no"
                      type="datetime"
                      format="MM/DD/YYYY HH:mm"
                      :max="maxDate"
                      :format24h="true"
                      :error="$v.depositInfo['Date'].$error"
                      @change="$v.depositInfo['Date'].$touch"
                      placeholder='Select Date'
                      v-model="depositInfo['Date']"></q-datetime>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Comment"
                   autocomplete="no"
                   v-model="depositInfo['Comment']"></q-input>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel"
             no-caps
             color="grey-3"
             text-color="tertiary"
             @click="_hide" />
      <q-btn label="Save"
             no-caps
             color="positive"
             class="main"
             @click="save" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request } from '../../../common'
import { required, minValue } from 'vuelidate/lib/validators'

const validations = {
  depositInfo: {
    Amount: {
      required,
      minValue: minValue(0.01)
    },
    'Transaction ID': {
      required
    },
    'Date': {
      required
    },
    'Transaction Type': {
      required
    }
  }
}

export default {
  name: 'mex-employer-add-deposit-dialog',
  mixins: [
    Singleton
  ],
  validations,
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  data () {
    return {
      maxDate: null,
      depositInfo: {
        'Transaction ID': null,
        'Amount': 0,
        'Date': null,
        'Transaction Type': 'CREDIT',
        'Comment': ''
      }
    }
  },
  methods: {
    async show () {
      let today = new Date()
      this.maxDate = today
      this.depositInfo = {
        'Transaction ID': null,
        'Amount': 0,
        'Date': null,
        'Transaction Type': 'CREDIT',
        'Comment': ''
      }
    },
    async save () {
      console.log(this.entity)
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      let url = ''
      if (this.user.cpKey === 'cp_mex') {
        url = `/admin/mex/employers/${this.entity['Employer ID']}/add-deposit`
      } else if (this.user.cpKey === 'cp_faas') {
        url = `/admin/faas/base/clients/${this.entity['Client ID']}/add-deposit`
      }
      const resp = await request(url, 'post', this.depositInfo)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.mex-employer-add-deposit-dialog {
  .modal-content {
    width: 400px;
  }
}
</style>
