<template>
  <q-dialog
    class="mex-employer-email-dialog"
    v-model="visible"
    prevent-close
  >
    <template slot="title">
      <div class="mb-5">
        <q-icon v-if="submitted" name="mdi-check-circle" class="font-35 text-positive"></q-icon>
        <div class="radius-box" v-else>
          <q-icon name="mdi-email-outline" class="font-22"></q-icon>
        </div>
      </div>
      <div class="font-16 mb-2">{{ submitted ? 'Email Sent Successfully' : 'Email Confirmation' }}</div>
      <div class="font-12 normal mh-20" v-if="submitted">
        The payroll details have been successfully sent to
        <strong class="heavy">{{ entity['Employer Name'] }}</strong>.
      </div>
      <div class="font-12 normal" v-else>
        Are you sure that you wish to send the current payroll list to
        <strong class="heavy">{{ entity['Employer Name'] }}</strong>?
      </div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <div class="font-14 heavy text-blue mb-10" notranslate="">{{ entity['Email'] }}</div>
      <div class="mt-30 mb-20">
        <q-field label="Additional email addresses"
                 helper="Add one email address in each line"
                 :label-width="12">
          <q-input type="textarea"
                   v-model="addEmails"
                   rows="2"></q-input>
        </q-field>
      </div>
    </template>
    <template slot="buttons">
      <template v-if="submitted">
        <q-btn label="Done"
               no-caps
               color="positive"
               @click="_hide" />
      </template>
      <template v-else>
        <q-btn label="Cancel" no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
        <q-btn label="Send Email"
               no-caps
               color="positive" class="main"
               @click="submit" />
      </template>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, request } from '../../../common'

export default {
  name: 'mex-employer-email-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      submitted: false,
      addEmails: ''
    }
  },
  methods: {
    async show () {
      this.addEmails = ''
      this.submitted = false
      this.$q.loading.show()
      const resp = await request(`/admin/mex/employers/${this.entity['Employer ID']}/email/init`, 'get')
      this.$q.loading.hide()
      if (resp.success) {
        this.addEmails = resp.data
      }
    },
    async submit () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/employers/${this.entity['Employer ID']}/email`, 'post', {
        addEmails: this.addEmails
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp, 'positive', 3000)
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.mex-employer-email-dialog {
  .modal-content {
    width: 400px;
  }
}
</style>
