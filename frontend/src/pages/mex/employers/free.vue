<template>
  <q-dialog class="mex-employer-free-config-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Employer Free Configuration</div>
      <div class="font-12 normal text-dark">
        Please fill in the info below to config transfer free for the {{ user.cpKey === 'cp_mex' ? 'employer' : 'client' }}
        "{{ entity['Employer Name'] ? entity['Employer Name'] : entity['Client Name'] }}".
        <p>If there is already a same type of free configuration, this operation will update it.</p>
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-20">
        <div class="col-12">
          <q-select float-label="Type"
                    autocomplete="no"
                    :options="TypeList"
                    filter
                    autofocus-filter
                    :error="$v.entity['Type'].$error"
                    @blur="$v.entity['Type'].$touch"
                    v-model="entity['Type']"></q-select>
        </div>
      </div>
      <div class="row gutter-form mt--10 mb-20">
        <div class="col-12">
          <q-select float-label="Transfer Type"
                    autocomplete="no"
                    :options="transferTypes"
                    filter
                    autofocus-filter
                    :error="$v.entity['MethodType'].$error"
                    @blur="$v.entity['MethodType'].$touch"
                    v-model="entity['MethodType']"></q-select>
        </div>
      </div>
      <div v-if="entity['Type'] == 'fixed'"
           class="row gutter-form mt--10 mb-20">
        <div class="col-12">
          <q-datetime float-label="Start Time"
                      autocomplete="no"
                      type="date"
                      format="MM/DD/YYYY"
                      v-model="entity['StartTime']"></q-datetime>
        </div>
      </div>
      <div class="row gutter-form mt--10 mb-20">
        <div class="col-12">
          <q-input float-label="Days"
                   autocomplete="no"
                   :type="'number'"
                   :error="$v.entity['Days'].$error"
                   @blur="$v.entity['Days'].$touch"
                   v-model="entity['Days']"></q-input>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel"
             no-caps
             color="grey-3"
             text-color="tertiary"
             @click="_hide" />
      <q-btn label="Save Changes"
             no-caps
             color="positive"
             class="main"
             @click="save" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request } from '../../../common'
import { required, minValue } from 'vuelidate/lib/validators'
import moment from 'moment'

const validations = {
  entity: {
    Type: { required },
    MethodType: { required },
    Days: { required, minValue: minValue(1) }
  }
}

export default {
  name: 'mex-employer-free-config-dialog',
  mixins: [
    Singleton
  ],
  validations,
  data () {
    return {
      defaultEntity: {
        'Type': 'register',
        'Days': 0,
        'StartTime': null,
        'MethodType': 'Both'
      },
      TypeList: [
        {
          label: 'Fixed Start Time',
          value: 'fixed'
        },
        {
          label: 'Member Register Time',
          value: 'register'
        }
      ],
      transferTypes: [
        {
          label: 'Both Bank Transfer and Cash Pickup',
          value: 'Both'
        },
        {
          label: 'Bank Transfer',
          value: 'Bank'
        },
        {
          label: 'Cash Pickup',
          value: 'Cash'
        }
      ]
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  methods: {
    async show () {
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      let url = ''
      if (this.user.cpKey === 'cp_mex') {
        url = `/admin/mex/employers/${this.entity['Employer ID']}/add-free-config`
      } else if (this.user.cpKey === 'cp_faas') {
        url = `/admin/faas/base/clients/${this.entity['Client ID']}/add-free-config`
      }
      const data = {
        'Type': this.entity.Type,
        'Days': this.entity.Days,
        'StartTime': this.entity.StartTime ? moment(this.entity.StartTime).format('MM/DD/YYYY HH:mm') : null,
        'MethodType': this.entity.MethodType
      }
      const resp = await request(url, 'post', data)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.mex-employer-free-config-dialog {
  .modal-content {
    width: 400px;
  }
}
</style>
