<template>
  <q-dialog class="mex-employer-global-dialog"
            v-model="visible">
    <template slot="title">
      <div class="font-16 mb-2">Global Config</div>
      <div class="font-12 normal text-dark ph-40">
        Global configurations for all the employers. Please be careful to change them.
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <q-list no-border>
        <q-item>
          <q-item-main>
            <q-field helper="When disabled, the members cannot create transfers by Intermex">
              <q-toggle v-model="form.enableIntermex"
                        label="Enable Intermex Transfer"></q-toggle>
            </q-field>
          </q-item-main>
        </q-item>
        <q-item>
          <q-item-main>
            <q-field helper="When disabled, the members cannot create transfers by Rapyd">
              <q-toggle v-model="form.enableTransfer"
                        label="Enable Rapyd(Bank) Transfer"></q-toggle>
            </q-field>
          </q-item-main>
        </q-item>
        <q-item>
          <q-item-main>
            <q-field helper="When disabled, the members cannot create transfers by UniTeller">
              <q-toggle v-model="form.enableCashPickupTransfer"
                        label="Enable UniTeller(Cash Pickup and Bank Transfer) Transfer"></q-toggle>
            </q-field>
          </q-item-main>
        </q-item>
        <q-item>
          <q-item-main>
            <q-field helper="When disabled, the Rapyd transfers will be queued in pending status and cannot be canceled by members">
              <q-toggle @input="changeBank"
                        v-model="form.enableTransferInstant"
                        label="Release Rapyd(Bank) Transfer Instantly"></q-toggle>
            </q-field>
          </q-item-main>
        </q-item>
        <q-item>
          <q-item-main>
            <q-field helper="When disabled, the UniTeller transfers will be queued in pending status and cannot be canceled by members">
              <q-toggle v-model="form.enableCashPickupTransferInstant"
                        label="Release UniTeller(Cash Pickup and Bank Transfer) Transfer Instantly"></q-toggle>
            </q-field>
          </q-item-main>
        </q-item>
        <q-item>
          <q-item-main>
            <q-field helper="When disabled, the Intermex transfers will be queued in pending status and cannot be canceled by members">
              <q-toggle v-model="form.enableIntermexTransferInstant"
                        label="Release Intermex Transfer Instantly"></q-toggle>
            </q-field>
          </q-item-main>
        </q-item>
        <q-item>
          <q-item-main>
            <q-field helper="Also the card enrollment/activation">
              <q-toggle v-model="form.enableBalance"
                        label="Allow Balance/PIN Viewing"></q-toggle>
            </q-field>
          </q-item-main>
        </q-item>
        <q-item>
          <q-item-main>
            <q-field helper="The reason for this change">
              <q-input v-model="form.comment"
                       type="textarea"
                       label="Comment" />
            </q-field>
          </q-item-main>
        </q-item>
      </q-list>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel"
             color="grey"
             no-caps
             @click="_hide" />
      <q-btn label="Save"
             no-caps
             color="blue"
             text-color="white"
             @click="save" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, notifySuccess, request } from '../../../common'
import _ from 'lodash'
export default {
  name: 'mex-employer-global-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      form: {
        enableIntermex: null,
        enableTransfer: null,
        enableCashPickupTransfer: null,
        enableTransferInstant: null,
        enableCashPickupTransferInstant: null,
        enableIntermexTransferInstant: null,
        enableBalance: null,
        comment: null
      }
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    },
    masterAdmin () {
      const user = this.user
      return user && user.currentRole === 'MasterAdmin'
    }
  },
  methods: {
    async show () {
      this.visible = false
      await this.init()
      this.visible = true
    },
    async init () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/employers/0/global-config`)
      this.$q.loading.hide()
      if (resp.success) {
        this.form = resp.data
      }
    },
    changeBank (value) {
      if (value) {
        notifySuccess('Please make sure the Rapyd API works and there is enough balance. If possible please ask others first.', 'Attention', 'negative')
      }
    },
    async save () {
      if (!_.trim(this.form.comment)) {
        notify('Please enter the reason why you want to change the Global config.', 'negative')
        return
      }
      this.$q.loading.show()
      const resp = await request(`/admin/mex/employers/0/global-config`, 'post', this.form)
      this.$q.loading.hide()
      if (resp.success) {
        this.form = resp.data
        notify(resp)
        this.$root.$emit('reload-mex-employers')
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.mex-employer-global-dialog {
  .modal-content {
    width: 500px;
  }
}
</style>
