<template>
  <q-dialog
    class="mex-employer-passcode-dialog"
    v-model="visible"
  >
    <template slot="title">
      <div class="font-16 mb-2">Passcode</div>
      <div class="font-12 normal text-dark ph-40">
        Please provide the following passcode for the payroll file
        for the <strong>{{ entity['Employer Name'] }}</strong> payroll administrator.
      </div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <div class="mb-25 text-blue font-24 heavy" notranslate="">{{ passcode }}</div>
      <q-table :data="logs"
               :columns="columns"
               notranslate=""
               class="clean">
      </q-table>
    </template>
    <template slot="buttons">
      <q-btn label="Close" no-caps
             color="blue" class="max-w-300"
             text-color="white"
             @click="_hide" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { request } from '../../../common'

export default {
  name: 'mex-employer-passcode-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      passcode: null,
      logs: [],
      columns: [
        { field: 'time', label: 'Date & Time', align: 'left' },
        { field: 'name', label: 'TransferMex User', align: 'left' }
      ]
    }
  },
  methods: {
    async show () {
      this.visible = false
      await this.init()
      if (this.passcode) {
        this.visible = true
      }
    },
    async init () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/employers/${this.entity['Employer ID']}/passcode`, 'post', {
        token: this.entity.token
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.passcode = resp.data.passcode
        this.logs = resp.data.logs
      }
    }
  }
}
</script>

<style lang="scss">
.mex-employer-passcode-dialog {
  .modal-content {
    width: 500px;
  }
}
</style>
