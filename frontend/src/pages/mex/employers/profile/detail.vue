<template>
  <q-card id="mex__employers__profile_detail"
          class="high-card">
    <q-card-title>
      <div class="row flex-center">
        <div class="font-18 bold">Account Details</div>
        <q-btn icon="mdi-refresh"
               outline
               @click="$emit('reload')"
               class="btn-mini ml-auto"></q-btn>
        <q-btn icon="mdi-pencil-outline"
               outline
               @click="edit"
               class="btn-mini ml-5"></q-btn>
      </div>
    </q-card-title>
    <q-card-main>
      <table class="fields-table mt-10 mb-auto">
        <tr v-for="(r, i) in fields"
            :key="i">
          <th>{{ r }}</th>
          <td>{{ entity[r] }}</td>
        </tr>
      </table>
    </q-card-main>

    <DetailDialog></DetailDialog>
  </q-card>
</template>

<script>
import DetailDialog from '../detail'

export default {
  name: 'mex-members-profile-detail',
  components: {
    DetailDialog
  },
  props: {
    entity: {
      type: Object
    }
  },
  data () {
    return {
      fields: [
        'First Name', 'Last Name', 'Date of Birth', 'Mailing Address',
        'City', 'Country', 'State / Province', 'Postal Code',
        'Card Provider', 'Funding Type'
      ]
    }
  },
  methods: {
    edit () {
      this.$root.$emit('show-mex-employer-detail-dialog', this.entity)
    }
  }
}
</script>

<style lang="scss">
#mex__employers__profile_detail {
  .q-card-main {
    display: flex;
    flex-direction: column;
    justify-content: start;
    overflow: auto;
  }
}
</style>
