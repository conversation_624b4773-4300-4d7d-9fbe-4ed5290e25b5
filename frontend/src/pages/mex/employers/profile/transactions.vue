<template>
  <q-card class="wide-card mb-30">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="inner-table m--15"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">
            <span class="va-m">Transaction History</span>
            <q-icon name="mdi-refresh"
                    color="positive"
                    @click.native="forceReload"
                    class="ml-5 pointer">
              <q-tooltip>Refresh</q-tooltip>
            </q-icon>
          </div>
        </template>
        <template slot="top-right">
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field === 'Type'">
              <q-icon :name="iconName(props.row)"
                      :color="iconColor(props.row)"
                      size="16px"></q-icon>
              {{ props.row['Type'] }}
            </template>
            <template v-else-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row)">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Amount'">
              <span :class="amountClass(props.row)">{{ props.row['Amount'] }}</span>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </q-card-main>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-card>
</template>

<script>
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns, toSelectOptions, notify } from '../../../../common'

export default {
  name: 'mex-employers-profile-transactions',
  mixins: [
    MexPageMixin,
    ListPageMixin
  ],
  props: {
    uid: {
      type: String
    }
  },
  data () {
    return {
      title: 'Employer Profile',
      requestUrl: `/admin/mex/members/transactions/list`,
      columns: generateColumns([
        'Type', 'Date & Time', 'Transaction ID',
        'Description', 'Message', 'Amount', 'Status'
      ], ['Amount']),
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 5,
        page: 1
      },
      filterOptions: [
        {
          value: 'filter[uct.id=]',
          label: 'Transaction ID'
        },
        {
          value: 'filter[uct.accountStatus=]',
          label: 'Status',
          options: toSelectOptions([
            'Pending', 'Executed', 'Declined', 'Expired',
            'Confirmation', 'Created', 'Canceled', 'Error',
            'Returned', 'Completed'
          ])
        }
      ],
      autoLoad: true,
      force: false,
      requestCb: () => {
        if (this.force) {
          notify('Submitted update request. Please refresh the list again several minutes later.', 'positive', 3000)
        }
        this.force = false
      }
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        member: this.uid,
        force: this.force
      }
    },
    statusClass (row) {
      const s = row['Status']
      return {
        Pending: 'blue',
        Approved: 'positive',
        Executed: 'positive',
        Declined: 'negative',
        Expired: 'orange',
        Created: 'blue',
        Completed: 'positive',
        Canceled: 'orange',
        Error: 'negative',
        Returned: 'negative'
      }[s] || s
    },
    iconName (row) {
      const group = row['Group']
      return {
        transfer: 'mdi-flash',
        payment: 'mdi-point-of-sale',
        debit: 'mdi-arrow-down',
        credit: 'mdi-arrow-up'
      }[group] || 'mdi-help-circle-outline'
    },
    iconColor (row) {
      const group = row['Group']
      return {
        transfer: 'orange',
        payment: 'secondary',
        debit: 'negative',
        credit: 'positive'
      }[group] || 'warning'
    },
    amountClass (row) {
      const status = row['Status']
      if (['Declined', 'Expired'].includes(status)) {
        return 'text-faded'
      }
      let amount = row['Amount']
      if (!amount) {
        return
      }
      amount = '' + amount
      return amount.startsWith('-') ? 'text-negative' : 'text-positive'
    },
    async forceReload () {
      this.force = true
      return this.reload()
    }
  }
}
</script>
