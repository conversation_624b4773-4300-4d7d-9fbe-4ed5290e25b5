<template>
  <q-dialog class="mex-employer-rapid-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Rapid Account Configuration</div>
      <div class="font-12 normal text-dark">
        Please fill in the Rapid parameters below about the {{ user.cpKey === 'cp_mex' ? 'employer' : 'client' }}
        "{{ entity['Employer Name'] ? entity['Employer Name'] : entity['Client Name'] }}".
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div v-if="$store.state.User.cpKey === 'cp_faas'"
             class="col-sm-12">
          <q-input float-label="Agent Name"
                   autocomplete="no"
                   v-model="entity['Agent Name']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Agent Number"
                   autocomplete="no"
                   v-model="entity['Agent Number']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Agent ANI"
                   autocomplete="no"
                   v-model="entity['Agent ANI']"></q-input>
        </div>
        <div v-if="$store.state.User.cpKey === 'cp_faas'"
             class="col-sm-12">
          <q-input float-label="Card Program Name"
                   autocomplete="no"
                   v-model="entity['Card Program Name']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Card Program ID"
                   autocomplete="no"
                   v-model="entity['Card Program ID']"></q-input>
        </div>
        <div v-if="$store.state.User.cpKey === 'cp_faas'"
             class="col-sm-12">
          <q-input float-label="User Profile Group Name"
                   autocomplete="no"
                   v-model="entity['User Profile Group Name']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="User Profile Group ID"
                   autocomplete="no"
                   v-model="entity['User Profile Group ID']"></q-input>
        </div>
      </div>

      <div class="font-12 mt-10 text-dark">Clear the values to use the base Rapid agent account.</div>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel"
             no-caps
             color="grey-3"
             text-color="tertiary"
             @click="_hide" />
      <q-btn label="Save Changes"
             no-caps
             color="positive"
             class="main"
             @click="save" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request } from '../../../common'

const validations = {
  entity: {}
}

export default {
  name: 'mex-employer-rapid-dialog',
  mixins: [
    Singleton
  ],
  validations,
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  methods: {
    async show () {
      this.$q.loading.show()
      let url = ''
      if (this.user.cpKey === 'cp_mex') {
        url = `/admin/mex/employers/${this.entity['Employer ID']}/rapid`
      } else if (this.user.cpKey === 'cp_faas') {
        url = `/admin/faas/base/clients/${this.entity['Client ID']}/rapid`
      }
      const resp = await request(url)
      this.$q.loading.hide()
      if (resp.success) {
        if (this.user.cpKey === 'cp_faas') {
          this.$set(this.entity, 'Agent Name', resp.data.rapidAgentName)
          this.$set(this.entity, 'Card Program Name', resp.data.rapidCardProgram)
          this.$set(this.entity, 'User Profile Group Name', resp.data.rapidUserProfileGroupName)
        }
        this.$set(this.entity, 'Agent Number', resp.data.rapidAgentNumber)
        this.$set(this.entity, 'Agent ANI', resp.data.rapidAgentAni)
        this.$set(this.entity, 'Card Program ID', resp.data.rapidCardProgramId)
        this.$set(this.entity, 'User Profile Group ID', resp.data.rapidUserProfileGroupId)
      }
    },
    async save () {
      const number = (this.entity['Agent Number'] || '').trim()
      const ani = (this.entity['Agent ANI'] || '').trim()
      if ((number && !ani) || (!number && ani)) {
        return notifyForm()
      }
      this.$q.loading.show()
      let url = ''
      if (this.user.cpKey === 'cp_mex') {
        url = `/admin/mex/employers/${this.entity['Employer ID']}/save-rapid`
      } else if (this.user.cpKey === 'cp_faas') {
        url = `/admin/faas/base/clients/${this.entity['Client ID']}/save-rapid`
      }
      const resp = await request(url, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.mex-employer-rapid-dialog {
  .modal-content {
    width: 400px;
  }
}
</style>
