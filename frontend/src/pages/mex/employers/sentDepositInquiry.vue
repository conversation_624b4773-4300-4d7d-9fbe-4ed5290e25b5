<template>
  <q-dialog class="sent-deposit-inquiry-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Deposit Info</div>
      <div class="font-12 normal text-dark">
        Please fill in the info below.
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-12">
          <q-select :float-label="'Employer Name'"
                    autocomplete="no"
                    :options="employerList"
                    :error="$v.entity['Employer Name'].$error"
                    @change="$v.entity['Employer Name'].$touch"
                    v-model="entity['Employer Name']"></q-select>
        </div>
        <div class="col-12">
          <q-select :float-label="'Deposit Account'"
                    autocomplete="no"
                    :options="depositType"
                    :error="$v.entity['Deposit Account'].$error"
                    @change="$v.entity['Deposit Account'].$touch"
                    v-model="entity['Deposit Account']"></q-select>
        </div>
        <div class="col-12">
          <q-input float-label="Total Dollars Of Deposit(s)"
                   type="number"
                   prefix="$"
                   :min="0"
                   :step="0.01"
                   :error="$v.entity['Total Dollars Of Deposit'].$error"
                   @change="$v.entity['Total Dollars Of Deposit'].$touch"
                   v-model="entity['Total Dollars Of Deposit']"></q-input>
        </div>
        <div class="col-12">
          <q-input float-label="Additional Comments"
                   rows="2"
                   type="textarea"
                   v-model="entity['Additional Comments']"></q-input>
        </div>
        <div class="col-12">
          <q-datetime float-label="Deposit Date"
                      autocomplete="no"
                      type="date"
                      format="MM/DD/YYYY"
                      :max="maxDate"
                      :error="$v.entity['Deposit Date'].$error"
                      @change="$v.entity['Deposit Date'].$touch"
                      v-model="entity['Deposit Date']"></q-datetime>
        </div>
        <div class="pt-20 col-12">
          <q-uploader url="/attachments"
                      name="file"
                      auto-expand
                      ref="uploader_send_deposit_inquiry_file"
                      @add="uploadFile()"
                      @fail="cancelUpload('send_deposit_inquiry_file')"
                      @uploaded="(file, xhr) => uploadedAttachment('send_deposit_inquiry_file', xhr)"
                      :additional-fields="[{name: 'category', value: 'send_deposit_inquiry_file'}]"
                      extensions=".gif,.jpg,.jpeg,.png,image/*,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                      clearable></q-uploader>
          <div class="text-grey font-13 mt-5">Click on '+' button to select the file to upload.</div>
        </div>
        <div class="pt-20 col-12">
          <div v-for="(file, index) in fileList"
               :key="index"
               class="q-mb-lg col-12 file-item">
            <span class="file-name">{{file.name}}</span>
            <q-btn label="Remove"
                   flat
                   @click="removeAttachment(file.id)"
                   icon="mdi-close"
                   color="negative"
                   size="sm"></q-btn>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel"
             no-caps
             color="grey-3"
             text-color="tertiary"
             @click="_hide" />
      <q-btn label="Send"
             no-caps
             color="positive"
             class="main"
             @click="save" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyResponse, notify, request, notifyForm } from '../../../common'
import { required } from 'vuelidate/lib/validators'
import moment from 'moment'

const validations = {
  entity: {}
}

for (const field of [
  'Employer Name', 'Total Dollars Of Deposit', 'Deposit Account', 'Deposit Date'
]) {
  validations.entity[field] = { required }
}

export default {
  name: 'sent-deposit-inquiry-dialog',
  mixins: [
    Singleton
  ],
  validations,
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  data () {
    return {
      employerList: [],
      depositType: [
        {
          label: 'Prefund',
          value: 'Prefund'
        },
        {
          label: 'ACH',
          value: 'ACH'
        }
      ],
      maxDate: null,
      fileList: [],
      uploading: false
    }
  },
  methods: {
    async show () {
      this.visible = false
      await this.init()
      this.visible = true
    },
    async init () {
      this.fileList = []
      this.uploading = false
      this.maxDate = new Date()
      this.$q.loading.show()
      const resp = await request(`/admin/mex/employers/getOptionList`)
      this.$q.loading.hide()
      if (resp.success) {
        this.employerList = resp.data.employerList
      }
    },
    async save () {
      if (this.uploading) {
        notifyResponse('Please upload the image or waiting for the uploading to be done.')
        return
      }
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      let fileIds = []
      if (this.fileList.length) {
        this.fileList.forEach(item => {
          fileIds.push(item.id)
        })
        this.entity['fileIds'] = fileIds
      }
      this.entity['Deposit Date'] = moment(this.entity['Deposit Date']).format('MM/DD/YYYY')
      let resp = await request(`/admin/mex/employers/${this.entity['Employer Name']}/sentDepositInquiry`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this._hide()
        notify(resp.data)
      }
    },
    cancelUpload (field) {
      this.uploading = false
      this.$refs[`uploader_${field}`].reset()
    },
    uploadFile () {
      if (this.$refs.uploader_send_deposit_inquiry_file) {
        this.uploading = true
        this.$refs.uploader_send_deposit_inquiry_file.upload()
      }
    },
    uploadedAttachment (field, xhr) {
      const resp = JSON.parse(xhr.response)
      this.fileList.push(resp.data.file)
      console.log(field)
      console.log(this.fileList)
      this.uploading = false
      this.$refs[`uploader_${field}`].reset()
    },
    removeAttachment (id) {
      let temp = []
      this.fileList.forEach(item => {
        if (item.id !== id) {
          temp.push(item)
        }
      })
      this.fileList = temp
    }
  },
  mounted () {
  }
}
</script>

<style lang="scss">
.sent-deposit-inquiry-dialog {
  .modal-content {
    width: 400px;
  }
  .file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .file-name {
    max-width: 60%;
    word-wrap: break-word;
  }
}
</style>
