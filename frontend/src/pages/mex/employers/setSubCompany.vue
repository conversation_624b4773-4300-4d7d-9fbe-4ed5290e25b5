<template>
  <q-dialog class="sent-deposit-inquiry-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Set Sub Company</div>
      <div class="font-12 normal text-dark">
        Please fill in the info below.
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-12">
          <q-select :float-label="'Sub Employers'"
                    autocomplete="no"
                    filter
                    autofocus-filter
                    multiple
                    use-chips
                    stack-label
                    :options="employerList"
                    v-model="subEmployers"></q-select>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel"
             no-caps
             color="grey-3"
             text-color="tertiary"
             @click="_hide" />
      <q-btn label="Send"
             no-caps
             color="positive"
             class="main"
             @click="save" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, request } from '../../../common'

export default {
  name: 'set-sub-company-dialog',
  mixins: [
    Singleton
  ],
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  data () {
    return {
      employerList: [],
      subEmployers: []
    }
  },
  methods: {
    async show () {
      this.visible = false
      await this.init()
      this.visible = true
    },
    async init () {
      this.subEmployers = this.entity['subEmployerIds']
      this.$q.loading.show()
      const resp = await request(`/admin/mex/employers/getOptionList`)
      this.$q.loading.hide()
      if (resp.success) {
        let result = resp.data.employerList
        this.employerList = []
        result.forEach(item => {
          if (!item.isPrimaryCompany) {
            this.employerList.push(item)
          }
        })
      }
    },
    async save () {
      // if (!this.subEmployers.length) {
      //   return notifyForm('Please select a sub employer')
      // }
      this.$q.loading.show()
      let resp = await request(`/admin/mex/employers/${this.entity['Employer ID']}/config-sub-company`, 'post', {
        subEmployerIds: this.subEmployers.join(',')
      })
      this.$q.loading.hide()
      if (resp.success) {
        this._hide()
        notify(resp.data)
        this.$root.$emit('reload-mex-employers')
      }
    }
  },
  mounted () {
  }
}
</script>

<style lang="scss">
.set-sub-company-dialog {
  .modal-content {
    width: 400px;
  }
}
</style>
