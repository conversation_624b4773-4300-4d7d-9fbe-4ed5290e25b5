<template>
  <q-page id="mex__transfer_fundings__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm mb-15 top-statics-row top-statics-row-lg">
        <div :class="statColClass"
             class="col-8 col-xs-10">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-bank-transfer-in"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-3">
                      <small class="super">Count</small>
                      <div class="value font-mono">{{ quick.cnt || 0 }}</div>
                    </div>
                    <div class="col-9">
                      <small class="super">Amount</small>
                      <div class="value font-mono">{{ quick.depositAmount | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">Total Bank Deposits</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="statColClass"
             class="col-8 col-xs-10"
             v-if="masterAdmin">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-alpha-r-box"
                        color="orange"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-6">
                      <small class="super nowrap">
                        Base.Rapid.Agent
                        <q-icon name="mdi-refresh"
                                color="positive"
                                @click.native="refreshRapid"
                                class="ml-5 pointer">
                          <q-tooltip>Refresh</q-tooltip>
                        </q-icon>
                      </small>
                      <div class="value font-mono">{{ quick.rapidBalance | moneyFormat }}</div>
                    </div>
                    <div class="col-6">
                      <small class="super nowrap">
                        BOTM.Program
                        <q-icon name="mdi-refresh"
                                color="positive"
                                @click.native="refreshBotm"
                                class="ml-5 pointer">
                          <q-tooltip>Refresh</q-tooltip>
                        </q-icon>
                      </small>
                      <div class="value font-mono">{{ quick.botmBalance | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">
                    Program Account Balance
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="statColClass"
             class="col-8 col-xs-10"
             v-if="masterAdmin">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-alpha-r-box"
                        color="orange"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super">
                        Rapid.Pending.Amount
                      </small>
                      <div class="value font-mono">{{ quick.rapidOtherAmount | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">
                    Other Pending Rapid Agent Amounts
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="statColClass"
             class="col-8 col-xs-10">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-alpha-r-circle"
                        color="blue"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super">
                        Rapyd.Balance
                        <q-icon name="mdi-refresh"
                                color="positive"
                                @click.native="refreshRapyd"
                                class="ml-5 pointer">
                          <q-tooltip>Refresh</q-tooltip>
                        </q-icon>
                      </small>
                      <div class="value font-mono">
                        {{ quick.rapydBalance | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description font-mono">
                    Threshold: {{ quick.threshold | moneyFormat('USD', false, true, 0) }}
                    <q-icon name="mdi-tune"
                            color="positive"
                            @click.native="setThreshold"
                            class="ml-5 pointer font-14">
                      <q-tooltip>Change threshold</q-tooltip>
                    </q-icon>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="statColClass"
             class="col-8 col-xs-10 min-w-350">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-sigma"
                        color="purple"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super">
                        Total.Property
                        <q-icon name="mdi-refresh"
                                color="positive"
                                @click.native="refreshProperty"
                                class="ml-5 pointer">
                          <q-tooltip>Refresh</q-tooltip>
                        </q-icon>
                      </small>
                      <div class="value font-mono">{{ quick.property | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">
                    <code>
                      Rapid
                      <q-tooltip>Balance of our Rapid base agent account and the pending amounts under other agents</q-tooltip>
                    </code> +
                    <code>
                      Rapyd
                      <q-tooltip>Balance of our Rapyd wallet</q-tooltip>
                    </code> +
                    <code>
                      Botm
                      <q-tooltip>Balance of our BOTM</q-tooltip>
                    </code> +
                    <code>
                      Settlements
                      <q-tooltip>The Created/Updated settlements to Rapyd</q-tooltip>
                    </code> -
                    <code>
                      Queued
                      <q-tooltip>The total amount of processing/queued transfers</q-tooltip>
                    </code>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="statColClass"
             class="col-8 col-xs-10">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-alpha-r-circle"
                        color="blue"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super">Total.Funded</small>
                      <div class="value font-mono">{{ quick.rapydTopUps | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">Rapyd Total Funded</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="statColClass"
             class="col-8 col-xs-10">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-credit-card-multiple"
                        color="blue"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-3">
                      <small class="super">Count</small>
                      <div class="value font-mono">{{ quick.bankCount || 0 }}</div>
                    </div>
                    <div class="col-9 pl-10">
                      <small class="super">Amount</small>
                      <div class="value font-mono">{{ quick.bankAmount | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">Bank Transfer</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="statColClass"
             class="col-8 col-xs-10">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-multiple"
                        color="negative"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-3">
                      <small class="super">Count</small>
                      <div class="value font-mono">{{ quick.cashCount || 0 }}</div>
                    </div>
                    <div class="col-9">
                      <small class="super">Amount</small>
                      <div class="value font-mono">{{ quick.cashAmount | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">Cash Pickup</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn icon="mdi-flash"
                 color="primary"
                 label="Create Settlement"
                 @click="createSettlement"
                 class="btn-sm mr-10"
                 no-caps></q-btn>

          <q-btn icon="mdi-plus"
                 color="primary"
                 label="Add Prefunding Event"
                 @click="addPrefunding"
                 class="btn-sm mr-10"
                 no-caps></q-btn>

          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Rapyd Funding Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row['Rapyd Funding Status'])">
                {{ props.row['Rapyd Funding Status'] }}
              </q-chip>
              <q-icon class="ml-5 pointer"
                      name="mdi-information-outline"
                      v-if="props.row['Comment']">
                <q-tooltip v-html="props.row['Comment']"></q-tooltip>
              </q-icon>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="send(props.row)"
                          v-if="props.row['Send Type'] !== 'MANUAL' && ['Created', 'Funding', 'Transferring'].includes(props.row['Rapyd Funding Status'])">
                    <q-item-main>Send</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="updateStatusToSent(props.row)"
                          v-if="props.row['Send Type'] === 'MANUAL' && ['Created', 'Funding', 'Transferring'].includes(props.row['Rapyd Funding Status'])">
                    <q-item-main>Set to "Sent"</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="updateStatus(props.row, 'Canceled')"
                          v-if="['Created'].includes(props.row['Rapyd Funding Status'])">
                    <q-item-main>Cancel</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="updateStatus(props.row, 'Updated')"
                          v-if="['Sent'].includes(props.row['Rapyd Funding Status'])">
                    <q-item-main>Set to "Updated"</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="updateStatus(props.row, 'Sent')"
                          v-if="['Updated'].includes(props.row['Rapyd Funding Status'])">
                    <q-item-main>Set to "Sent"</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="completedOrphan(props.row)"
                          v-if="['Updated'].includes(props.row['Rapyd Funding Status'])">
                    <q-item-main>Set to "Completed Orphan"</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="downloadProof(props.row)"
                          v-if="['Sent', 'Updated', 'Confirmed', 'Completed Orphan'].includes(props.row['Rapyd Funding Status'])">
                    <q-item-main>Download Proof Image</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else-if="['Rapyd Balance', 'Deposit Amount', 'Total Transfer $', 'Bank Transfer $',
             'Avg Bank Transfer $', 'Cash Pickup $', 'Avg Cash Pickup $'].includes(col.field)">
              <div class="mex-amount"
                   :class="{heavy: col.field === 'Deposit Amount'}">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else-if="['Avg USD/MXN Rate', 'Bank Transfer #', 'Cash Pickup #'].includes(col.field)">
              <div class="mex-amount">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <PromptDialog></PromptDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notify, request, toSelectOptions } from '../../../common'
import MexPageMixin from '../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import MexProcessorMixin from '../../../mixins/mex/MexProcessorMixin'
import PromptDialog from '../../../components/PromptDialog'
import _ from 'lodash'

export default {
  name: 'mex-transfer-fundings',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    MexProcessorMixin,
    EventHandlerMixin('reload-mex-transfer-fundings')
  ],
  components: {
    PromptDialog
  },
  data () {
    return {
      title: 'Rapyd Daily Settlement',
      requestUrl: `/admin/mex/transfer-fundings/list`,
      downloadUrl: `/admin/mex/transfer-fundings/export`,
      columns: generateColumns([
        'Deposit Date',
        'Batch ID',
        'Rapyd Balance',
        'Deposit Amount',
        'Total Transfer $',
        'Avg USD/MXN Rate',
        'Send Type',
        'Bank Transfer #',
        'Bank Transfer $',
        'Avg Bank Transfer $',
        'Cash Pickup #',
        'Cash Pickup $',
        'Avg Cash Pickup $',
        'Rapyd Funding Status',
        'Action'
      ], ['Avg USD/MXN Rate', 'Bank Transfer #', 'Cash Pickup #', 'Rapyd Balance', 'Deposit Amount', 'Total Transfer $', 'Bank Transfer $',
        'Avg Bank Transfer $', 'Cash Pickup $', 'Avg Cash Pickup $']),
      filterOptions: [
        {
          value: 'filter[t.id=]',
          label: 'Batch ID'
        }, {
          value: 'depositDate',
          label: 'Deposit Date',
          range: [
            {
              value: 'range[t.depositDate][start]',
              type: 'date'
            }, {
              value: 'range[t.depositDate][end]',
              type: 'date'
            }
          ]
        }, {
          value: 'filter[t.status=]',
          label: 'Rapyd Funding Status',
          options: toSelectOptions([
            'Created', 'Sent', 'Updated', 'Confirmed'
          ])
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true
    }
  },
  computed: {
    statColClass () {
      return this.masterAdmin ? 'col-sm-5' : 'col-sm-2-5'
    }
  },
  methods: {
    init () {
      if (this.$route.params.id) {
        this.filters = [
          {
            field: 'filter[t.id=]',
            predicate: '=',
            value: this.$route.params.id
          }
        ]
      }
    },
    statusClass (status) {
      return {
        'Created': 'dark',
        'Sent': 'blue',
        'Updated': 'orange',
        'Confirmed': 'positive',
        'Completed Orphan': 'positive',
        'Canceled': 'purple'
      }[status] || status
    },
    async send (row) {
      let message = 'Are you sure that you want to send the settlement'
      this.$q.dialog({
        title: `Confirm`,
        message: `${message} with the amount ${row['Deposit Amount']}?`,
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/transfer-fundings/send/${row['Batch ID']}`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
          notify(resp)
        }
      })
    },
    async updateStatusToSent (row) {
      const message = 'Are you sure that you have sent the settlement manually'
      this.$q.dialog({
        title: `Confirm`,
        message: `${message} with the amount ${row['Deposit Amount']}?`,
        cancel: true
      }).then(() => {
        this.updateStatus(row, 'Sent')
      })
    },
    async updateStatus (row, status, other = {}) {
      this.$q.loading.show()
      other.status = status
      const resp = await request(`/admin/mex/transfer-fundings/update-status/${row['Batch ID']}`, 'post', other)
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
        notify(resp)
      }
    },
    async downloadProof (row) {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/transfer-fundings/download-proof/${row['Batch ID']}`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        location.href = resp.data
        notify('Downloading...')
      }
    },
    async refreshRapyd () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/transfer-fundings/refresh-rapyd`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        _.assignIn(this.quick, resp.data)
        notify(resp)
      }
    },
    async refreshProperty () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/transfer-fundings/refresh-property`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        _.assignIn(this.quick, resp.data)
        notify(resp)
      }
    },
    async completedOrphan (row) {
      this.$root.$emit('show-prompt-dialog', {
        message: 'Please enter the amount (USD) that Rapyd topped up to our account\'s wallet:',
        callback: async (text) => {
          if (text) {
            this.updateStatus(row, 'Confirmed', {
              amount: text
            })
          }
        }
      })
    },
    createSettlement () {
      this.$q.dialog({
        title: `Create Settlement`,
        message: `Are you sure that you want to create settlement to Rapyd now according to the balance & threshold setting? Please enter "Yes" below:`,
        prompt: {
          model: ''
        },
        cancel: true
      }).then(async text => {
        if (text !== 'Yes') {
          return this.$q.notify('Operation cancelled')
        }
        this.$q.loading.show()
        const resp = await request(`/admin/mex/transfer-fundings/create-settlement`, 'post')
        this.$q.loading.hide()
        this.reload()
        if (resp.success) {
          notify(resp)
        }
      }).catch(() => {})
    },
    addPrefunding () {
      this.$q.dialog({
        title: `Add Prefunding Event`,
        message: `Please enter the amount (USD) that is prefunding to Rapyd:`,
        prompt: {
          model: '',
          type: 'number'
        },
        cancel: true
      }).then(async amount => {
        if (!amount || _.trim(amount) === '') {
          return this.$q.notify(`Invalid amount!`)
        }
        this.$q.dialog({
          title: `Confirm`,
          message: `Are you sure that you want to add the prefunding event with the amount $${amount}?`,
          cancel: true
        }).then(async () => {
          this.$q.loading.show()
          const resp = await request(`/admin/mex/transfer-fundings/add-prefunding-event`, 'post', {
            amount
          })
          this.$q.loading.hide()
          if (resp.success) {
            this.reload()
          }
        }).catch(() => {})
      }).catch(() => {})
    },
    setThreshold () {
      this.$q.dialog({
        title: `Change Threshold`,
        message: `Please enter the new Rapyd balance threshold amount (USD):`,
        prompt: {
          model: this.quick.threshold / 100,
          type: 'number'
        },
        cancel: true
      }).then(async amount => {
        if (!amount || _.trim(amount) === '') {
          return this.$q.notify(`Invalid amount!`)
        }
        this.$q.loading.show()
        const resp = await request(`/admin/mex/transfer-fundings/update-rapyd-threshold`, 'post', {
          amount
        })
        this.$q.loading.hide()
        if (resp.success) {
          this.quick.threshold = resp.data
          notify(resp)
        }
      }).catch(() => {})
    }
  }
}
</script>
