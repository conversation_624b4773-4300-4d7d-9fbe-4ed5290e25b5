<template>
  <q-page id="mex__botm_kyc_exceptions__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
      </div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }} Report</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'BOTM Status'">
              <q-chip class="font-13"
                      :class="props.row['BOTM Status'] == 'Pending' ? 'negative': 'positive'">
                {{ _.get(props.row, col.field) }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'SPAN Status'">
              <q-chip class="font-13"
                      :class="props.row['SPAN Status'] == 'Pending' ? 'negative': 'positive'">
                {{ _.get(props.row, col.field) }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Member ID'">
              <span class="font-12 view-btn"
                    @click="view(props.row)"> {{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="viewKyc(props.row)">
                    <q-item-main>View SPAN's OFAC Detail</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['BOTM Status'] == 'Pending'"
                          v-close-overlay
                          @click.native="syncKyc(props.row)">
                    <q-item-main>Sync KYC Status</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import MexPageMixin from '../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'mex-payroll_exceptions',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-payroll_exceptions')
  ],
  data () {
    return {
      title: 'KYC Exceptions',
      requestUrl: `/admin/mex/botm_kyc_exceptions/list`,
      downloadUrl: `/admin/mex/botm_kyc_exceptions/export`,
      columns: generateColumns([
        'Member ID',
        'First Name',
        'Last Name',
        'Email',
        'Employer',
        'DOB',
        'Mobile Phone',
        'Country',
        'Province',
        'City',
        'Postal Code',
        'Address 1',
        'Address 2',
        'Government ID',
        'Business ID',
        'External ID',
        'SPAN Status',
        'BOTM Status',
        'BOTM Approve Date',
        'Action'
      ]),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'Member ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'Member First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Member Last Name'
        }, {
          value: 'filter[bke.status=]',
          label: 'BOTM Status',
          options: [
            { label: 'Error', value: 'Error' },
            { label: 'Approved', value: 'Approved' },
            { label: 'Manual Approval', value: 'Manual Approval' },
            { label: 'Pending', value: 'Pending' }
          ]
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true,
      keyword: ''
    }
  },
  methods: {
    view (row) {
      window.open(`/admin#/j/mex/members/${row['Member ID']}`, '_blank')
    },
    viewKyc (row) {
      window.open(`/admin/mex/member/${row['Member ID']}/view-ofac-details`, '_blank')
    },
    async syncKyc (row) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/botm_kyc_exception/${row['ID']}/sync`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    }
  }
}
</script>
<style lang="scss">
#mex__botm_kyc_exceptions__index_page {
  .view-btn {
    color: #0062ff;
    cursor: pointer;
    text-decoration: underline;
  }
}
</style>
