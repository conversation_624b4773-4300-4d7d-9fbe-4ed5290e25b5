<template>
  <q-page id="mex__load_transfer__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>

    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-usd"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.load || 0 }}</div>
                  <div class="description">Total Loaded</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-usd"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.transfer || 0 }}</div>
                  <div class="description">Total Transferred</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>

        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-usd"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.bank || 0 }}</div>
                  <div class="description">Total Bank Transferred</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-usd"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.cash || 0 }}</div>
                  <div class="description">Total Cash Pickup Transferred</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Load & Transfer Report</strong>
        </template>
        <template slot="top-right">
          <q-btn v-if="$store.state.User.teams.indexOf('MasterAdmin') !== -1 || $store.state.User.teams.indexOf('TransferMex Admin') !== -1 "
                 icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="['Total $ Of Loads', 'Avg $ Of Loads', 'Total $ Of Transfers', 'Avg $ Of Transfers',
             'Total $ Of Bank Transfers', 'Avg $ Of Bank Transfers', 'Total $ Of Cash Pickup Transfers', 'Avg $ Of Cash Pickup Transfers'].includes(col.field)">
              <div class="mex-amount">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else-if="['Total Count Of Loads', 'Total Count Of Transfers', 'Total Count Of Bank Transfers', 'Total Count Of Cash Pickup Transfers'].includes(col.field)">
              <div class="mex-amount">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import MexPageMixin from '../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'mex-load-transfer',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-load-transfers')
  ],
  data () {
    const columns = [
      'Employer', 'Total Count Of Loads', 'Total $ Of Loads', 'Avg $ Of Loads', 'Total Count Of Transfers', 'Total $ Of Transfers', 'Avg $ Of Transfers', 'Total Count Of Bank Transfers', 'Total $ Of Bank Transfers', 'Avg $ Of Bank Transfers',
      'Total Count Of Cash Pickup Transfers', 'Total $ Of Cash Pickup Transfers', 'Avg $ Of Cash Pickup Transfers'
    ]
    return {
      title: 'Load & Transfer',
      filtersUrl: `/admin/mex/load_transfer/filters`,
      requestUrl: `/admin/mex/load_transfer/list`,
      downloadUrl: `/admin/mex/load_transfer/export`,
      columns: generateColumns(columns, ['Total Count Of Loads', 'Total Count Of Transfers',
        'Total Count Of Bank Transfers', 'Total Count Of Cash Pickup Transfers', 'Total $ Of Loads', 'Avg $ Of Loads',
        'Total $ Of Transfers', 'Avg $ Of Transfers',
        'Total $ Of Bank Transfers', 'Avg $ Of Bank Transfers', 'Total $ Of Cash Pickup Transfers', 'Avg $ Of Cash Pickup Transfers']),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'Employer',
          options: [],
          source: 'employers'

        }
      ],
      freezeColumn: 0
    }
  },
  methods: {
  }
}
</script>
