<template>
  <q-dialog
    class="mex-member-activated-dialog"
    v-model="visible"
  >
    <template slot="title">
      <div class="mb-5">
        <q-icon name="mdi-check-circle" color="positive" class="font-35"></q-icon>
      </div>
      <div class="font-16 mb-2">Member is Activated</div>
      <div class="font-12 normal">The account and the card are active and ready to be used!</div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel" no-caps
             color="grey-3"
             text-color="tertiary"
             @click="_hide" />
      <q-btn label="Add Another Member"
             no-caps
             color="blue" class="main"
             @click="add" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'

export default {
  name: 'mex-member-activated-dialog',
  mixins: [
    Singleton
  ],
  methods: {
    add () {
      this._hideAndEmit('show-mex-member-detail-dialog')
    }
  }
}
</script>

<style lang="scss">
.mex-member-activated-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
