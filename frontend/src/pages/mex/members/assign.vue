<template>
  <q-dialog class="mex-member-assign-dialog"
            v-model="visible">
    <template slot="title">
      <div class="mb-5">
        <div class="radius-box">
          <q-icon name="mdi-credit-card"
                  class="font-22"></q-icon>
        </div>
      </div>
      <div class="font-16 mb-2">{{ migrate ? 'Migrate to BOTM card' : (change ? 'Change Card' : 'Assign a Card') }}</div>
      <div class="font-12 normal">Please assign a {{ change ? 'new' : '' }} card to <strong notranslate="">{{ $c.fullName(entity) }}</strong> by scanning or entering the barcode number.</div>
      <div class="font-12 normal mt-15 text-positive"
           v-if="entity && !migrate && entity['Enrolled'] === 'Yes'">
        The balance on the old account will be moved to the new account. The new account will be enrolled automatically to ensure the balance can be loaded.
      </div>
      <div class="font-12 normal mt-15 text-negative"
           v-else-if="oldBarcodeNumber && !migrate">
        <template v-if="!entity || entity.currentProcessor !== 'BOTM'">
          The old account is not enrolled yet so the balance will NOT be moved to the new account.
        </template>
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <q-input float-label="Barcode Number"
               autocomplete="no"
               class="mt-20 mb-40"
               :error="$v.entity['Barcode Number'].$error"
               @blur="$v.entity['Barcode Number'].$touch"
               v-model="entity['Barcode Number']"></q-input>
      <q-input float-label="Retype the Barcode Number"
               autocomplete="no"
               class="mt-40 mb-10"
               :error="$v.entity['Barcode Number 2'].$error"
               @blur="$v.entity['Barcode Number 2'].$touch"
               v-model="entity['Barcode Number 2']"></q-input>
      <div v-if="entity && oldBarcodeNumber && entity['Enrolled'] !== 'Yes'"
           class="col-sm-12 text-left">
        <q-field helper="Add the old card to this member's legacy cards lists so that we are able to accept the new deposits to the old card and move it to the new card automatically. Don't assign the old card to others if this is checked on.">
          <q-checkbox v-model="addOldCardToLegacy"
                      label="Add old card to legacy cards" />
        </q-field>

      </div>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn :label="change ? 'Change Card' : 'Assign Card'"
               no-caps
               color="positive"
               @click="submit" />
        <q-btn label="Close"
               no-caps
               color="grey-3"
               class="ml-0 mt-8"
               text-color="tertiary"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { required } from 'vuelidate/lib/validators'
import { notifyForm, request, barcodeNumberValidator, isBotmAccountNumber } from '../../../common'

export default {
  name: 'mex-member-assign-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      oldBarcodeNumber: null
    }
  },
  computed: {
    change () {
      return this.origin && this.origin.change
    },
    migrate () {
      return this.origin && this.origin.migrate
    }
  },
  validations: {
    entity: {
      'Barcode Number': {
        required,
        barcodeNumberValidator
      },
      'Barcode Number 2': {
        required,
        same () {
          return this.entity['Barcode Number'] === this.entity['Barcode Number 2']
        }
      }
    }
  },
  methods: {
    show () {
      this.oldBarcodeNumber = null
      if (this.change) {
        this.oldBarcodeNumber = this.entity['Barcode Number']
        this.entity['Barcode Number'] = ''
        this.addOldCardToLegacy = true
      } else if (this.migrate) {
        this.entity['Barcode Number'] = ''
      }
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      const data = {
        barcodeNumber: this.entity['Barcode Number'],
        addToLegacy: this.addOldCardToLegacy
      }
      if (this.change) {
        data.change = true
        data.token = this.entity.token
      }
      if (this.migrate) {
        data.migrate = true
        if (!isBotmAccountNumber(data.barcodeNumber)) {
          return notifyForm('Invalid BOTM barcode number!')
        }
      }
      this.$q.loading.show()
      const resp = await request(`/admin/mex/members/${this.entity['Member ID']}/assign`, 'post', data)
      this.$q.loading.hide()
      if (resp.success) {
        this.$root.$emit('reload-mex-members')
        this.$root.$emit('reload-faas-members')
        this._hideAndEmit('show-mex-member-assigned-dialog', this.entity)
      }
    }
  }
}
</script>

<style lang="scss">
.mex-member-assign-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
