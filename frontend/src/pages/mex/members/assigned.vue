<template>
  <q-dialog
    class="mex-member-assigned-dialog"
    v-model="visible"
  >
    <template slot="title">
      <div class="mb-5">
        <q-icon name="mdi-check-circle" color="positive" class="font-35"></q-icon>
      </div>
      <div class="font-16 mb-2">Card has Successfully Been Assigned</div>
      <div class="font-12 normal"><strong notranslate="">{{ $c.fullName(entity) }}</strong>'s card has been assigned and is ready to use.</div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="buttons">
      <q-btn label="Done"
             no-caps
             color="positive"
             @click="_hide" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'

export default {
  name: 'mex-member-assigned-dialog',
  mixins: [
    Singleton
  ]
}
</script>

<style lang="scss">
.mex-member-assigned-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
