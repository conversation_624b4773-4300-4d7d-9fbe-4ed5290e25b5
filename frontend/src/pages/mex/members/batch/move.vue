<template>
  <q-dialog class="mex-member-batch-move-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Batch Moving Members</div>
      <div class="font-12 normal">Move <strong>{{ members.length }}</strong> members to another employer</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row flex-center">
        <div class="col-12">
          <q-field label="Move to the employer"
                   :label-width="12">
            <q-select :options="employers"
                      :filter="true"
                      v-model="employer"></q-select>
          </q-field>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn label="Move"
               no-caps
               color="positive"
               :disable="!employer"
               @click="submit" />
        <ul class="notes">
          <li>If the member is already under the selected employer, it will be skipped.</li>
        </ul>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notify, request } from '../../../../common'
import _ from 'lodash'

export default {
  name: 'mex-member-batch-move-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      members: [],
      employers: [],
      employer: null
    }
  },
  methods: {
    show () {
      const o = this.origin || {}
      this.employers = o.employers || []
      this.members = o.members || []
      this.employer = null
    },
    submit () {
      if (!this.employer || this.members.length <= 0) {
        notify('No members or employer selected...')
        return
      }
      const em = _.find(this.employers, { value: this.employer })
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to move the selected ${this.members.length} members to "${em.label}"?`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show({ message: 'Batch moving...' })
        const resp = await request(`/admin/mex/members/batch-move`, 'post', {
          employer: this.employer,
          members: this.members
        })
        this.$q.loading.hide()
        if (resp.success) {
          this.$root.$emit('reload-mex-members')
          this._hide()
          notify(resp.data)
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
.mex-member-batch-move-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
