<template>
  <q-dialog class="mex-member-batch-options-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Batch Action</div>
      <div v-if="type == 'setSplash'"
           class="font-12 normal">Set members use the Splash page</div>
      <div v-if="type == 'clearSplash'"
           class="font-12 normal">Clear the splash pag for the members</div>
      <div v-if="type == 'updateSeasonName'"
           class="font-12 normal">Set Season name for members</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row flex-center">
        <div class="col-7">
          <div class="upload-area"
               @click="select"
               :class="{selected: file}">
            <q-icon name="mdi-file-document-outline"></q-icon>
            <template v-if="file">
              <div class="mt-10">Selected file:</div>
              <div class="font-13 text-blue">
                <span>{{ file.name }}</span>
                <a href="javascript:"
                   class="ml-5 link"
                   @click.stop="file = null">
                  <q-icon name="mdi-close-circle-outline"
                          class="font-20"
                          color="negative"></q-icon>
                  <q-tooltip>Remove and reselect</q-tooltip>
                </a>
              </div>
            </template>
            <template v-else>
              <div class="mt-10">Upload .XLSX</div>
              <div class="font-13 text-dark">(Drop or click here)</div>
            </template>
          </div>
          <input type="file"
                 class="hide"
                 accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                 ref="file"
                 @change="selectedFile">
        </div>
        <div class="col-12">
          <q-field label="Action"
                   :label-width="12">
            <q-select :options="actionList"
                      :filter="true"
                      v-model="type"></q-select>
          </q-field>
        </div>
        <div v-if="type=='setSplash' || type=='clearSplash' "
             class="col-12">
          <q-field label="Select the Splash Page"
                   :label-width="12">
            <q-select :options="splashList"
                      :filter="true"
                      v-model="splash"></q-select>
          </q-field>
        </div>
        <div v-if="type=='updateSeasonName' "
             class="col-12">
          <q-field label="Season Name"
                   :label-width="12">
            <q-input v-model="seasonName"></q-input>
          </q-field>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn label="Confirm"
               no-caps
               color="positive"
               class="max-w-350"
               :disable="!file || !type"
               @click="submit" />
        <q-btn label="Download XLSX Template"
               no-caps
               color="blue"
               class="max-w-350"
               @click="downloadTemplate" />
        <ul class="notes">
          <li v-if="type == 'setSplash'">If the member is already use the selected splash page, it will be skipped.</li>
        </ul>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notify, request, uploadAttachment, notifyResponse } from '../../../../common'
import _ from 'lodash'
import $ from 'jquery'

export default {
  name: 'mex-member-batch-options-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      splashList: [],
      employers: [],
      splash: null,
      type: null,
      file: null,
      seasonName: null,
      actionList: [
        {
          label: 'Set Splash Page',
          value: 'setSplash'
        },
        {
          label: 'Clear Splash Page',
          value: 'clearSplash'
        },
        {
          label: 'Update Season Name',
          value: 'updateSeasonName'
        }
      ]
    }
  },
  methods: {
    show () {
      const o = this.origin || {}
      this.splashList = o.splashList || []
      this.employers = o.employers || []
      this.type = null
      this.splash = null
      this.file = null
      this.seasonName = null
      this.attachment = null
    },
    submit () {
      if (!this.file || !this.type) {
        return
      }
      let message = ''
      const em = _.find(this.splashList, { value: this.splash })
      if (this.type === 'setSplash') {
        if (!this.splash) {
          notify('No splash page selected...', 'negative')
          return
        }
        message = `Are you sure that you want to set the "${em.label}" splash page for the members?`
      } else if (this.type === 'clearSplash') {
        let title = em ? 'the ' + em.label : 'all the '
        message = `Are you sure that you want to clear ${title} splash page for the members?`
      } else if (this.type === 'updateSeasonName') {
        if (!this.seasonName) {
          notify('Please input the season name!', 'negative')
          return
        }
        message = `Are you sure that you want to update the season name for the members?`
      }
      this.$q.dialog({
        title: 'Confirm',
        message: message,
        color: 'negative',
        cancel: true
      }).then(async () => {
        if (!this.attachment) {
          this.$q.loading.show({ message: 'Uploading...' })
          const category = 'transferMexMemberBatchActions'
          const resp = await uploadAttachment(this.file, category)
          if (typeof resp === 'string') {
            this.$q.loading.hide()
            return notifyResponse(`Failed to upload: ${resp}`)
          }
          this.attachment = resp
        }
        this.$q.loading.show({ message: 'Batch doing...' })
        let resp = null
        if (this.type === 'setSplash') {
          resp = await request(`/admin/mex/members/batch-setting-splash`, 'post', {
            splash: this.splash,
            attachmentId: this.attachment.id
          })
        } else if (this.type === 'clearSplash') {
          let url = this.splash ? '/admin/mex/employers/batch-setting-splash' : '/admin/mex/members/batch-clear-splash'
          resp = await request(url, 'post', {
            splash: this.splash,
            attachmentId: this.attachment.id
          })
        } else if (this.type === 'updateSeasonName') {
          const url = '/admin/mex/members/batch-setting-seasonName'
          resp = await request(url, 'post', {
            seasonName: this.seasonName,
            attachmentId: this.attachment.id
          })
        }
        this.$q.loading.hide()
        if (resp.success) {
          if (this.type === 'member') {
            this.$root.$emit('reload-mex-members')
          } else {
            this.$root.$emit('reload-mex-employers')
          }
          this._hide()
          notify(resp.data)
        }
      }).catch(() => {})
    },
    downloadTemplate () {
      location.href = `/download?path=static/mex/template/members-for-batch.xlsx`
    },
    select () {
      if (this.file) {
        return
      }
      $(this.$refs.file).click()
    },
    selectedFile () {
      const input = this.$refs.file
      if (input.files.length) {
        this.file = input.files[0]
      }
      input.type = 'text'
      input.type = 'file'
    },
    getValidDragFile (e) {
      if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length) {
        const file = e.dataTransfer.files[0]
        console.log(file)
        if (file && file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          return file
        }
      }
      return null
    },
    setupDnd () {
      const area = $(this.$el).find('.upload-area'),
        dom = area.get(0)

      dom.addEventListener('dragover', e => {
        e.stopPropagation()
        e.preventDefault()
        area.addClass('dropping')
      }, false)

      dom.addEventListener('dragleave', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')
      }, false)

      dom.addEventListener('drop', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')

        const file = this.getValidDragFile(e)
        if (file) {
          this.file = file
        } else {
          notify('Please select a .xlsx file.', 'negative')
        }
      }, false)
    }
  },
  mounted () {
    this.setupDnd()
  }
}
</script>

<style lang="scss">
.mex-member-batch-options-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
