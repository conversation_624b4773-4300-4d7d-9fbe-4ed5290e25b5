<template>
  <q-dialog class="mex-member-batch-set-splash-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Batch Set Splash for {{type}}s</div>
      <div class="font-12 normal">Set <strong>{{ members.length }}</strong> {{type}}s use the Splash page</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row flex-center">
        <div class="col-12">
          <q-field label="Select the Splash Page"
                   :label-width="12">
            <q-select :options="splashList"
                      :filter="true"
                      v-model="splash"></q-select>
          </q-field>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn label="Set"
               no-caps
               color="positive"
               :disable="!splash"
               @click="submit" />
        <q-btn label="Clear"
               no-caps
               color="negative"
               :disable="!splash"
               @click="clear" />
        <ul class="notes">
          <li>If the {{type}} is already use the selected splash page, it will be skipped.</li>
        </ul>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notify, request } from '../../../../common'
import _ from 'lodash'

export default {
  name: 'mex-member-batch-set-splash-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      members: [],
      splashList: [],
      splash: null,
      type: 'member'
    }
  },
  methods: {
    show () {
      const o = this.origin || {}
      this.splashList = o.splashList || []
      this.members = o.members || []
      this.type = o.type || 'member'
      this.splash = null
    },
    clear () {
      const type = this.type
      if (!this.splash || this.members.length <= 0) {
        notify('No ' + type + 's or splash selected...')
        return
      }
      const em = _.find(this.splashList, { value: this.splash })
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to clear the "${em.label}" splash page for the selected ${this.members.length} ${this.type}s?`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show({ message: 'Batch clearing...' })
        let resp = null
        if (this.type === 'member') {
          resp = await request(`/admin/mex/members/batch-setting-splash`, 'post', {
            splash: this.splash,
            members: this.members,
            type: 'clear'
          })
        } else {
          resp = await request(`/admin/mex/employers/batch-setting-splash`, 'post', {
            splash: this.splash,
            employes: this.members,
            type: 'clear'
          })
        }
        this.$q.loading.hide()
        if (resp.success) {
          if (this.type === 'member') {
            this.$root.$emit('reload-mex-members')
          } else {
            this.$root.$emit('reload-mex-employers')
          }
          this._hide()
          notify(resp.data)
        }
      }).catch(() => {})
    },
    submit () {
      const type = this.type
      if (!this.splash || this.members.length <= 0) {
        notify('No ' + type + 's or splash selected...')
        return
      }
      const em = _.find(this.splashList, { value: this.splash })
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to set the "${em.label}" splash page for the selected ${this.members.length} ${this.type}s?`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show({ message: 'Batch setting...' })
        let resp = null
        if (this.type === 'member') {
          resp = await request(`/admin/mex/members/batch-setting-splash`, 'post', {
            splash: this.splash,
            members: this.members
          })
        } else {
          resp = await request(`/admin/mex/employers/batch-setting-splash`, 'post', {
            splash: this.splash,
            employes: this.members
          })
        }
        this.$q.loading.hide()
        if (resp.success) {
          if (this.type === 'member') {
            this.$root.$emit('reload-mex-members')
          } else {
            this.$root.$emit('reload-mex-employers')
          }
          this._hide()
          notify(resp.data)
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
.mex-member-batch-set-splash-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
