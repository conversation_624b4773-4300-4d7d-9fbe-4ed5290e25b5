<template>
  <q-dialog class="mex-member-batch-update-last-four-ssn-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Batch Update Last Four SSN</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row flex-center">
        <div class="col-7">
          <div class="upload-area"
               @click="select"
               :class="{selected: file}">
            <q-icon name="mdi-file-document-outline"></q-icon>
            <template v-if="file">
              <div class="mt-10">Selected file:</div>
              <div class="font-13 text-blue">
                <span>{{ file.name }}</span>
                <a href="javascript:"
                   class="ml-5 link"
                   @click.stop="file = null">
                  <q-icon name="mdi-close-circle-outline"
                          class="font-20"
                          color="negative"></q-icon>
                  <q-tooltip>Remove and reselect</q-tooltip>
                </a>
              </div>
            </template>
            <template v-else>
              <div class="mt-10">Upload .XLSX</div>
              <div class="font-13 text-dark">(Drop or click here)</div>
            </template>
          </div>
          <input type="file"
                 class="hide"
                 accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                 ref="file"
                 @change="selectedFile">
        </div>
      </div>
      <div class="row flex-center">
        <div class="col-12">
          <q-field label="Move to the employer"
                   :label-width="12">
            <q-select :options="employers"
                      :filter="true"
                      v-model="employer"></q-select>
          </q-field>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn label="Done"
               no-caps
               color="positive"
               :disable="!employer"
               @click="submit" />
        <ul class="notes">
          <li>If the ssn is same with the member's current ssn, it will be skipped.</li>
        </ul>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notify, request, notifyResponse, uploadAttachment } from '../../../../common'
import $ from 'jquery'

export default {
  name: 'mex-member-batch-update-last-four-ssn-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      file: null,
      attachment: null,
      notes: false,
      employers: [],
      employer: null
    }
  },
  watch: {
    file () {
      this.attachment = null
    }
  },
  methods: {
    show () {
      const o = this.origin || {}
      this.employers = o.employers || []
      this.file = null
      this.notes = false
      this.employer = null
    },
    async submit () {
      if (!this.file) {
        return
      }
      if (!this.employer) {
        return notifyResponse(`Please select the employer.`)
      }
      if (!this.attachment) {
        this.$q.loading.show({ message: 'Uploading...' })
        const category = 'transferMexMemberBatchUpdateLastFourSsn'
        const resp = await uploadAttachment(this.file, category)
        if (typeof resp === 'string') {
          this.$q.loading.hide()
          return notifyResponse(`Failed to upload: ${resp}`)
        }
        this.attachment = resp
      }
      this.$q.loading.show({ message: 'Importing...' })
      const resp = await request(`/admin/mex/members/batch-update-last-four-ssn`, 'post', {
        attachment: this.attachment.id,
        employer: this.employer
      }, true)
      this.$q.loading.hide()
      if (resp.success) {
        this.$root.$emit('reload-mex-members')
        this._hide()
        notify(resp.data)
      }
    },
    select () {
      if (this.file) {
        return
      }
      $(this.$refs.file).click()
    },
    selectedFile () {
      const input = this.$refs.file
      if (input.files.length) {
        this.file = input.files[0]
      }
      input.type = 'text'
      input.type = 'file'
    },
    getValidDragFile (e) {
      if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length) {
        const file = e.dataTransfer.files[0]
        console.log(file)
        if (file && file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          return file
        }
      }
      return null
    },
    setupDnd () {
      const area = $(this.$el).find('.upload-area'),
        dom = area.get(0)

      dom.addEventListener('dragover', e => {
        e.stopPropagation()
        e.preventDefault()
        area.addClass('dropping')
      }, false)

      dom.addEventListener('dragleave', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')
      }, false)

      dom.addEventListener('drop', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')

        const file = this.getValidDragFile(e)
        if (file) {
          this.file = file
        } else {
          notify('Please select a .xlsx file.', 'negative')
        }
      }, false)
    }
  },
  mounted () {
    this.setupDnd()
  }
}
</script>

<style lang="scss">
.mex-member-batch-move-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
