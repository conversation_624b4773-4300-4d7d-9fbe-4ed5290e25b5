<template>
  <q-dialog class="mex-member-batch-config-transfer-free-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Batch Config Transfer Free</div>
      <div class="font-12 normal text-dark">Please fill in the start and end date(UTC) and the transfer will be automatically free for the members.
      </div><q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row flex-center">
        <div class="col-7">
          <div class="upload-area"
               @click="select"
               :class="{selected: file}">
            <q-icon name="mdi-file-document-outline"></q-icon>
            <template v-if="file">
              <div class="mt-10">Selected file:</div>
              <div class="font-13 text-blue">
                <span>{{ file.name }}</span>
                <a href="javascript:"
                   class="ml-5 link"
                   @click.stop="file = null">
                  <q-icon name="mdi-close-circle-outline"
                          class="font-20"
                          color="negative"></q-icon>
                  <q-tooltip>Remove and reselect</q-tooltip>
                </a>
              </div>
            </template>
            <template v-else>
              <div class="mt-10">Upload .XLSX</div>
              <div class="font-13 text-dark">(Drop or click here)</div>
            </template>
          </div>
          <input type="file"
                 class="hide"
                 accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                 ref="file"
                 @change="selectedFile">
        </div>
      </div>
      <div class="row flex-center mt-30">
        <q-datetime class="col-10"
                    float-label="Start Date"
                    autocomplete="no"
                    type="datetime"
                    format="MM/DD/YYYY HH:mm"
                    :format24h="true"
                    v-model="startDate"></q-datetime>
      </div>
      <div class="row flex-center mt-30">
        <q-datetime class="col-10"
                    float-label="End Date"
                    autocomplete="no"
                    type="datetime"
                    :format24h="true"
                    format="MM/DD/YYYY HH:mm"
                    v-model="endDate"></q-datetime>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn label="Done"
               no-caps
               color="positive"
               class="max-w-350"
               :disable="!file || !startDate || !endDate"
               @click="submit" />
        <q-btn label="Download XLSX Template"
               no-caps
               color="blue"
               class="max-w-350"
               @click="downloadTemplate" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import $ from 'jquery'
import { notify, notifyResponse, request, uploadAttachment } from '../../../common'
import moment from 'moment'

export default {
  name: 'mex-member-batch-config-transfer-free-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      file: null,
      attachment: null,
      notes: false,
      startDate: null,
      endDate: null
    }
  },
  watch: {
    file () {
      this.attachment = null
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  methods: {
    show () {
      this.file = null
      this.notes = false
      this.startDate = null
      this.endDate = null
    },
    async submit () {
      if (!this.file) {
        return
      }
      if (!this.startDate || !this.endDate) {
        return notifyResponse(`Please select the start and the end date.`)
      }
      if (!this.attachment) {
        this.$q.loading.show({ message: 'Uploading...' })
        const category = 'transferMexMemberBatchConfigTransferFree'
        const resp = await uploadAttachment(this.file, category)
        if (typeof resp === 'string') {
          this.$q.loading.hide()
          return notifyResponse(`Failed to upload: ${resp}`)
        }
        this.attachment = resp
      }
      this.$q.loading.show({ message: 'Importing...' })
      const resp = await request(`/admin/mex/members/batch-config-transfer-free`, 'post', {
        attachment: this.attachment.id,
        startDate: moment(this.startDate).format('MM/DD/YYYY HH:mm:ss'),
        endDate: moment(this.endDate).format('MM/DD/YYYY HH:mm:ss')
      }, true)
      this.$q.loading.hide()
      if (resp.success) {
        this.$root.$emit('reload-mex-members')
        this._hide()
        notify(resp.data)
      }
    },
    downloadTemplate () {
      location.href = `/download?path=static/mex/template/members_batch_config_transfer_free.xlsx`
    },
    select () {
      if (this.file) {
        return
      }
      $(this.$refs.file).click()
    },
    selectedFile () {
      const input = this.$refs.file
      if (input.files.length) {
        this.file = input.files[0]
      }
      input.type = 'text'
      input.type = 'file'
    },
    getValidDragFile (e) {
      if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length) {
        const file = e.dataTransfer.files[0]
        console.log(file)
        if (file && file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          return file
        }
      }
      return null
    },
    setupDnd () {
      const area = $(this.$el).find('.upload-area'),
        dom = area.get(0)

      dom.addEventListener('dragover', e => {
        e.stopPropagation()
        e.preventDefault()
        area.addClass('dropping')
      }, false)

      dom.addEventListener('dragleave', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')
      }, false)

      dom.addEventListener('drop', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')

        const file = this.getValidDragFile(e)
        if (file) {
          this.file = file
        } else {
          notify('Please select a .xlsx file.', 'negative')
        }
      }, false)
    }
  },
  mounted () {
    this.setupDnd()
  }
}
</script>

<style lang="scss">
.mex-member-batch-config-transfer-free-dialog {
  .modal-content {
    width: 450px;
  }

  .notes {
    margin-top: 10px;
    font-size: 13px;
    color: var(--q-color-faded);

    i {
      font-style: normal;
      border: 1px solid #ddd;
      border-radius: 3px;
      padding: 0 5px;
    }
  }
}
</style>
