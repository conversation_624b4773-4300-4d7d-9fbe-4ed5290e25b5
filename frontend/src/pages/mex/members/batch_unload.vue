<template>
  <q-dialog class="mex-member-batch-unload-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Batch Unload</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row flex-center">
        <div class="col-7">
          <div class="upload-area"
               @click="select"
               :class="{selected: file}">
            <q-icon name="mdi-file-document-outline"></q-icon>
            <template v-if="file">
              <div class="mt-10">Selected file:</div>
              <div class="font-13 text-blue">
                <span>{{ file.name }}</span>
                <a href="javascript:"
                   class="ml-5 link"
                   @click.stop="file = null">
                  <q-icon name="mdi-close-circle-outline"
                          class="font-20"
                          color="negative"></q-icon>
                  <q-tooltip>Remove and reselect</q-tooltip>
                </a>
              </div>
            </template>
            <template v-else>
              <div class="mt-10">Upload .XLSX</div>
              <div class="font-13 text-dark">(Drop or click here)</div>
            </template>
          </div>
          <input type="file"
                 class="hide"
                 accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                 ref="file"
                 @change="selectedFile">
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn label="Done"
               no-caps
               color="positive"
               class="max-w-350"
               :disable="!file"
               @click="submit" />
        <q-btn label="Download XLSX Template"
               no-caps
               color="blue"
               class="max-w-350"
               @click="downloadTemplate" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import $ from 'jquery'
import { notify, notifyResponse, request, uploadAttachment } from '../../../common'

export default {
  name: 'mex-member-batch-unload-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      file: null,
      attachment: null,
      notes: false
    }
  },
  watch: {
    file () {
      this.attachment = null
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  methods: {
    show () {
      this.file = null
      this.notes = false
    },
    async submit () {
      if (!this.file) {
        return
      }
      if (!this.attachment) {
        this.$q.loading.show({ message: 'Uploading...' })
        const category = 'transferMexMemberBatchUnload'
        const resp = await uploadAttachment(this.file, category)
        if (typeof resp === 'string') {
          this.$q.loading.hide()
          return notifyResponse(`Failed to upload: ${resp}`)
        }
        this.attachment = resp
      }
      this.$q.loading.show({ message: 'Importing...' })
      const resp = await request(`/admin/mex/members/batch-unload`, 'post', {
        attachment: this.attachment.id
      }, true)
      this.$q.loading.hide()
      if (resp.success) {
        this.$root.$emit('reload-mex-members')
        this._hide()
        notify(resp.data)
      }
    },
    downloadTemplate () {
      location.href = `/download?path=static/mex/template/members-batch-unload.xlsx`
    },
    select () {
      if (this.file) {
        return
      }
      $(this.$refs.file).click()
    },
    selectedFile () {
      const input = this.$refs.file
      if (input.files.length) {
        this.file = input.files[0]
      }
      input.type = 'text'
      input.type = 'file'
    },
    getValidDragFile (e) {
      if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length) {
        const file = e.dataTransfer.files[0]
        console.log(file)
        if (file && file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          return file
        }
      }
      return null
    },
    setupDnd () {
      const area = $(this.$el).find('.upload-area'),
        dom = area.get(0)

      dom.addEventListener('dragover', e => {
        e.stopPropagation()
        e.preventDefault()
        area.addClass('dropping')
      }, false)

      dom.addEventListener('dragleave', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')
      }, false)

      dom.addEventListener('drop', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')

        const file = this.getValidDragFile(e)
        if (file) {
          this.file = file
        } else {
          notify('Please select a .xlsx file.', 'negative')
        }
      }, false)
    }
  },
  mounted () {
    this.setupDnd()
  }
}
</script>

<style lang="scss">
.mex-member-batch-unload-dialog {
  .modal-content {
    width: 450px;
  }

  .notes {
    margin-top: 10px;
    font-size: 13px;
    color: var(--q-color-faded);

    i {
      font-style: normal;
      border: 1px solid #ddd;
      border-radius: 3px;
      padding: 0 5px;
    }
  }
}
</style>
