<template>
  <q-dialog class="mex-member-load-dialog"
            v-model="visible">
    <template slot="title">
      <div class="mb-5">
        <div class="radius-box">
          <q-icon name="mdi-credit-card-wireless"
                  class="font-22"></q-icon>
        </div>
      </div>
      <div v-if="agentAdmin"
           class="font-16 mb-2">Unload Account</div>
      <div v-else
           class="font-16 mb-2">Load/Unload Account</div>
      <div class="font-12 normal">Enter the required info to load/unload <strong notranslate="">{{ $c.fullName(entity) }}</strong></div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <q-field label="Action"
               :label-width="12"
               class="mv-20 text-left">
        <q-radio v-if="!agentAdmin"
                 v-model="type"
                 val="load"
                 label="Load"
                 color="positive"
                 class="mr-20"></q-radio>
        <q-radio v-model="type"
                 val="unload"
                 label="Unload"
                 color="negative"></q-radio>
      </q-field>
      <q-field :label="type == 'load' ? 'Source' : 'Target'"
               :label-width="12"
               class="mv-20 text-left">
        <q-radio v-model="source"
                 v-for="(s, k) in sources"
                 :key="k"
                 :val="k"
                 :label="s"
                 color="primary"></q-radio>
      </q-field>
      <q-field label="Amount"
               :label-width="12"
               class="mv-20">
        <q-input type="number"
                 min="0.01"
                 max="3500"
                 autocomplete="no"
                 :error="$v.amount.$error"
                 @blur="$v.amount.$touch"
                 v-model="amount"></q-input>
      </q-field>
      <q-field label="Short Comment"
               :label-width="12"
               class="mv-20">
        <q-select autocomplete="no"
                  :options="commentTypeList"
                  filter
                  class="mt-20 mb-10"
                  :error="$v.commentType.$error"
                  @change="$v.commentType.$touch"
                  @input="changeLabel"
                  v-model="commentType"></q-select>
      </q-field>
      <q-field :label="labelStr"
               :label-width="12"
               class="mv-20">
        <q-input v-if="commentType"
                 autocomplete="no"
                 :error="$v.comment.$error"
                 @blur="$v.comment.$touch"
                 v-model="comment"></q-input>
      </q-field>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn label="Submit"
               no-caps
               color="positive"
               @click="submit" />
        <q-btn label="Close"
               no-caps
               color="grey-3"
               class="ml-0 mt-8"
               text-color="tertiary"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { maxValue, minValue, required } from 'vuelidate/lib/validators'
import { notifyForm, request, toSelectOptions } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'

const validations = {
  type: {
    required
  },
  comment: {
    required
  },
  commentType: {
    required
  },
  amount: {
    required,
    minValue: minValue(0.01),
    maxValue: maxValue(3500)
  }
}

export default {
  name: 'mex-member-load-dialog',
  mixins: [
    Singleton,
    MexPageMixin
  ],
  data () {
    return {
      source: 'employer',
      type: 'load',
      labelStr: '',
      amount: 0,
      comment: null,
      commentType: '',
      commentTypeList: toSelectOptions([
        'rapyd_transfer', 'rapyd_transfer_reverse', 'Change account from', 'Change account to', 'Other & No track', 'Other'
      ])
    }
  },
  computed: {
    sources () {
      if (this.type === 'load') {
        return {
          employer: 'Current employer\'s business account (if applicable)',
          enrolled: 'The employer when the card was enrolled (same as `employer` for BOTM cards)',
          base: 'Base business account'
        }
      }
      return {
        enrolled: 'The employer when the card was enrolled or the current employer',
        base: 'Base business account'
      }
    }
  },
  watch: {
    type () {
      if (this.type === 'unload') {
        this.source = 'enrolled'
      }
    }
  },
  validations,
  methods: {
    show () {
      if (this.agentAdmin) {
        this.type = 'unload'
      } else {
        this.type = 'load'
      }
      this.source = 'employer'
      this.amount = 0
      this.comment = null
    },
    changeLabel () {
      this.labelStr = ''
      if (this.commentType === 'rapyd_transfer' || this.commentType === 'rapyd_transfer_reverse') {
        this.labelStr = 'Transfer ID'
      } else if (this.commentType === 'Change account from' || this.commentType === 'Change account to') {
        this.labelStr = 'Account Number'
      }
    },
    async submit () {
      let comment = this.comment
      if (this.commentType === 'rapyd_transfer' || this.commentType === 'rapyd_transfer_reverse') {
        comment = this.commentType + ' - ' + this.comment
      } else if (this.commentType === 'Change account from' || this.commentType === 'Change account to') {
        comment = this.commentType + ' ' + this.comment
      }
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      const message = `Are you sure that you want to ${this.type} ${this.amount} dollars ${this.type === 'load' ? 'to' : 'from'} the account ${this.$c.fullName(this.entity)}?`
      this.$q.dialog({
        title: 'Confirm',
        message,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()

        const data = {
          source: this.source,
          type: this.type,
          amount: this.amount,
          comment: comment,
          isNoTrack: this.commentType === 'Other & No track' ? true : ''
        }
        let url = `members/${this.entity['Member ID']}`
        if (!this.entity['Member ID'] && this.entity['Employer ID']) {
          url = `employers/${this.entity['Employer ID']}`
        }
        const resp = await request(`/admin/mex/${url}/load-unload`, 'post', data)
        this.$q.loading.hide()
        if (resp.success) {
          this.$root.$emit('reload-mex-members')
          this.$root.$emit('reload-mex-employers')
          this.$root.$emit('reload-faas-members')
          this._hide()
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
.mex-member-load-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
