<template>
  <q-dialog class="mex-member-card-status-dialog"
            v-model="visible">
    <template slot="title">
      <div class="mb-5">
        <div class="radius-box">
          <q-icon name="mdi-credit-card"
                  class="font-22"></q-icon>
        </div>
      </div>
      <div class="font-16 mb-2">Change Card Status</div>
      <div class="font-12 normal">Change the card status of <strong notranslate="">{{ $c.fullName(entity) }}</strong></div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <q-input float-label="Barcode Number"
               v-if="entity['dba']"
               disable
               v-model="entity['Barcode Number']"
               class="mt-20 mb-40"></q-input>
      <q-input float-label="Card Number"
               v-if="entity['dba']"
               disable
               v-model="entity['Card Number']"
               class="mt-20 mb-40"></q-input>
      <q-select float-label="Card Status"
                autocomplete="no"
                class="mt-20 mb-40"
                :options="statuses"
                :error="$v.entity['Card Status'].$error"
                @blur="$v.entity['Card Status'].$touch"
                v-model="entity['Card Status']"></q-select>

      <div class="text-left">
        <p v-if="entity['Card Status'] === 'Suspended'">
          Suspended status will block transactions.
          This will not stop the cardholder from activating the card on their own though.
          The member can buy stuff and use an ATM with the pin.
        </p>
        <p v-else-if="entity['Card Status'] === 'On Hold' || entity['Card Status'] === 'Suspectedfraud'">
          {{entity['Card Status']}} status will block transactions, card activation, and ATM usage.
        </p>
        <template v-else-if="entity['Card Status'] === 'Paused'">
          <p>{{entity['Card Status']}} status will block transactions, and can be reactivated.</p>
          <p class="text-orange">If the account status is active, the user is still able to make transfers from the mobile app.</p>
        </template>
        <template v-else-if="entity['Card Status'] === 'Inactive'">
          <p class="text-negative">{{entity['Card Status']}} status will block transactions, close the card and cannot be reactivated.</p>
          <p class="text-orange">If the account status is active, the user is still able to make transfers from the mobile app.</p>
        </template>
      </div>

      <p class="mv-10 text-left font-12 text-faded"
         v-if="!entity['dba']">
        All the current card of the bound accounts will be updated.
      </p>
      <q-input float-label="Reason"
               autocomplete="no"
               v-model="reason"
               class="mt-20 mb-40"></q-input>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn label="Submit"
               no-caps
               color="positive"
               @click="submit" />
        <q-btn label="Close"
               no-caps
               color="grey-3"
               class="ml-0 mt-8"
               text-color="tertiary"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { required } from 'vuelidate/lib/validators'
import { cardStatusesByAccountNumber, notifyForm, request, toSelectOptions } from '../../../../common'

export default {
  name: 'mex-member-card-status-dialog',
  mixins: [
    Singleton
  ],
  computed: {
    statuses () {
      if (!this.entity) {
        return []
      }
      const all = cardStatusesByAccountNumber(this.entity['Barcode Number'] || '')
      if (!all) {
        return []
      }
      return toSelectOptions(all)
    }
  },
  validations: {
    entity: {
      'Card Status': {
        required
      }
    }
  },
  data () {
    return {
      reason: null
    }
  },
  methods: {
    show () {
      this.reason = null
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      const data = {
        cardStatus: this.entity['Card Status'],
        reason: this.reason
      }
      if (this.entity.dba) {
        data.accountNumber = this.entity['Barcode Number']
        data.dba = this.entity.dba
      }
      let url = `members/${this.entity['Member ID']}`
      if (!this.entity['Member ID'] && this.entity['Employer ID']) {
        url = `employers/${this.entity['Employer ID']}`
      }
      const resp = await request(`/admin/mex/${url}/change-card-status`, 'post', data)
      this.$q.loading.hide()
      if (resp.success) {
        this.$root.$emit('reload-mex-members-profile')
        this.$root.$emit('reload-mex-members-cards')
        this.$root.$emit('reload-mex-employers-profile')
        this.$root.$emit('reload-faas-members-profile')
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.mex-member-card-status-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
