<template>
  <q-dialog class="mex-create-uniteller-dialog"
            v-model="visible">
    <template slot="title">
      <div class="mb-5">
        <div class="radius-box">
          <q-icon name="mdi-credit-card"
                  class="font-22"></q-icon>
        </div>
      </div>
      <div class="font-16 mb-2">{{ 'Register UniTeller User' }}</div>
      <div class="font-12 normal">Please select a question for <strong notranslate="">{{ $c.fullName(entity) }}</strong> and input the answer.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <q-select float-label="Question"
                autocomplete="no"
                :options="questions"
                filter
                class="mt-20 mb-10"
                :error="$v.entity['questionId'].$error"
                @change="$v.entity['questionId'].$touch"
                v-model="entity['questionId']"></q-select>
      <q-input float-label="Answer"
               autocomplete="no"
               class="mt-40 mb-10"
               :error="$v.entity['answer'].$error"
               @blur="$v.entity['answer'].$touch"
               v-model="entity['answer']"></q-input>
      <q-input float-label="Answer Hint"
               autocomplete="no"
               class="mt-40 mb-10"
               :error="$v.entity['answerHint'].$error"
               @blur="$v.entity['answerHint'].$touch"
               v-model="entity['answerHint']"></q-input>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn :label="'Save'"
               no-caps
               color="positive"
               @click="submit" />
        <q-btn label="Close"
               no-caps
               color="grey-3"
               class="ml-0 mt-8"
               text-color="tertiary"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { required } from 'vuelidate/lib/validators'
import { notifyForm, request, notifyResponse } from '../../../common'
export default {
  name: 'mex-create-uniteller-dialog',
  mixins: [
    Singleton
  ],
  computed: {
    change () {
      return this.origin && this.origin.change
    }
  },
  validations: {
    entity: {
      'questionId': {
        required
      },
      'answer': {
        required
      },
      'answerHint': {
        required
      }
    }
  },
  data () {
    return {
      questions: []
    }
  },
  methods: {
    async show () {
      this.$q.loading.show()
      // this.entity['questionId'] = null
      this.entity['answer'] = ''
      this.entity['answerHint'] = ''
      const resp = await request(`/admin/mex/members/getQuestions`, 'get')
      if (resp.success) {
        this.questions = resp.data
        this.$q.loading.hide()
      } else {
        this.$q.loading.hide()
        notifyResponse(resp, () => {
          this._hide()
        })
      }
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      const data = {
        answer: this.entity.answer,
        questionId: this.entity.questionId,
        answerHint: this.entity.answerHint
      }
      const resp = await request(`/admin/mex/members/${this.entity['Member ID']}/createUniTiller`, 'post', data)
      this.$q.loading.hide()
      if (resp.success) {
        this.$root.$emit('reload-mex-members')
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.mex-create-uniteller-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
