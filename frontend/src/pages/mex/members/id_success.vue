<template>
  <q-dialog class="mex-member-id-success-dialog"
            v-model="visible">
    <template slot="title">
      <div class="mb-5">
        <q-icon name="mdi-check-circle"
                color="positive"
                class="font-35"></q-icon>
      </div>
      <div class="font-16 mb-2">KYC Approved</div>
      <div class="font-12 normal">
        The member <strong notranslate="">{{ $c.fullName(entity) }}</strong>’s account has been approved!
        <template v-if="!assigned">Please assign a card by scanning or entering the barcode number.</template>
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <q-input float-label="Barcode Number"
               autocomplete="no"
               class="mv-10"
               v-if="!assigned"
               :error="$v.entity['Barcode Number'].$error"
               @blur="$v.entity['Barcode Number'].$touch"
               v-model="entity['Barcode Number']"></q-input>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn label="Assign Card"
               no-caps
               color="positive"
               v-if="!assigned"
               @click="submit" />
        <q-btn label="Create New Member"
               no-caps
               color="grey-3"
               class="ml-0 mt-8"
               text-color="tertiary"
               v-if="!entity['Role']"
               @click="addNew" />
        <q-btn label="Close"
               no-caps
               color="blue"
               class="ml-0 mt-8"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { required } from 'vuelidate/lib/validators'
import { notifyForm, request, barcodeNumberValidator } from '../../../common'

export default {
  name: 'mex-member-id-success-dialog',
  mixins: [
    Singleton
  ],
  computed: {
    assigned () {
      return this.origin && this.origin['Barcode Number']
    }
  },
  validations: {
    entity: {
      'Barcode Number': {
        required,
        barcodeNumberValidator
      }
    }
  },
  methods: {
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      const resp = await request(`/admin/mex/members/${this.entity['Member ID']}/assign`, 'post', {
        barcodeNumber: this.entity['Barcode Number']
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.$root.$emit('reload-mex-members')
        this.$root.$emit('reload-mex-members-profile')
        this._hideAndEmit('show-mex-member-activated-dialog')
      }
    },
    addNew () {
      this._hideAndEmit('show-mex-member-detail-dialog')
    }
  }
}
</script>

<style lang="scss">
.mex-member-id-success-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
