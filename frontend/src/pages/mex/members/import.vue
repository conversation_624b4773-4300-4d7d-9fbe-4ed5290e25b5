<template>
  <q-dialog class="mex-member-import-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Create New Member</div>
      <div class="font-12 normal">Please select an option to create a member.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row flex-center">
        <div class="col-7">
          <div class="upload-area"
               @click="select"
               :class="{selected: file}">
            <q-icon name="mdi-file-document-outline"></q-icon>
            <template v-if="file">
              <div class="mt-10">Selected file:</div>
              <div class="font-13 text-blue">
                <span>{{ file.name }}</span>
                <a href="javascript:"
                   class="ml-5 link"
                   @click.stop="file = null">
                  <q-icon name="mdi-close-circle-outline"
                          class="font-20"
                          color="negative"></q-icon>
                  <q-tooltip>Remove and reselect</q-tooltip>
                </a>
              </div>
            </template>
            <template v-else>
              <div class="mt-10">Upload .XLSX</div>
              <div class="font-13 text-dark">(Drop or click here)</div>
            </template>
          </div>
          <input type="file"
                 class="hide"
                 accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                 ref="file"
                 @change="selectedFile">
        </div>
        <div class="col-7 mt-20"
             v-if="!file">
          <q-btn class="xl-icon"
                 no-caps
                 @click="manual">
            <q-icon name="mdi-plus-circle"></q-icon>
            <div class="mt-10">Manually Create Member</div>
          </q-btn>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn label="Continue"
               no-caps
               color="positive"
               class="max-w-350"
               :disable="!file"
               @click="submit" />
        <q-btn label="Download XLSX Template"
               no-caps
               color="blue"
               class="max-w-350"
               @click="downloadTemplate" />
        <a href="javascript:"
           class="font-13 mt-10 text-blue"
           @click="notes = !notes">{{ notes ? 'Hide' : 'View' }} notes</a>
        <ul class="notes"
            v-if="notes">
          <li>Please delete the first data row in the above template as it's just a sample.</li>
          <li>The <i>CURP ID #</i> and <i>Email Address</i> need to be unique in the system.</li>
          <li>The <i>CURP ID #</i> is the Gov ID (SSN/CURP).</li>
          <li>The <i>Country</i> needs to be one of: <code>United States</code> and <code>Mexico</code>.</li>
          <li>The <i>Employer ID</i> can be found in the employers list.</li>
          <li>The <i>Date of Birth</i> format: mm/dd/yyyy</li>
          <li>The <i>State/Province</i> and <i>Country</i> need to match the values contained in
            <a href="/static/mex/template/US_Mexico_states.xlsx"
               target="_blank">this spreadsheet</a>.
          </li>
          <li>The <i>Mobile Phone</i> is optional.</li>
          <li>The <i>External Employee ID</i> is optional. It's the ID in an external system, which is used to map the data there.</li>
          <li>The row number in the error message (if any) is counting from the title row, with the title row included, which is usually same as the row number in Microsoft Excel.</li>
          <li>More than 100 data will be imported asynchronously in the background</li>
          <li>Just import the first sheet by default, multiple sheets need to be split to multiple files and import separately.</li>
        </ul>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import $ from 'jquery'
import { notify, notifyResponse, request, uploadAttachment } from '../../../common'

export default {
  name: 'mex-member-import-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      file: null,
      attachment: null,
      notes: false
    }
  },
  watch: {
    file () {
      this.attachment = null
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  methods: {
    show () {
      this.file = null
      this.notes = false
    },
    async submit () {
      if (!this.file) {
        return
      }
      if (!this.attachment) {
        this.$q.loading.show({ message: 'Uploading...' })
        const category = this.user.cpKey === 'cp_faas' ? 'faasMembers' : 'transferMexMembers'
        const resp = await uploadAttachment(this.file, category)
        if (typeof resp === 'string') {
          this.$q.loading.hide()
          return notifyResponse(`Failed to upload: ${resp}`)
        }
        this.attachment = resp
      }
      this.$q.loading.show({ message: 'Importing and verifying OFAC...' })
      const resp = await request(`/admin/mex/members/import`, 'post', {
        attachment: this.attachment.id
      }, true)
      this.$q.loading.hide()
      if (resp.success) {
        this.$root.$emit('reload-mex-members')
        this._hide()
        notify(resp.data)
      } else {
        this.file = null
        this.$root.$emit('show-html-list-dialog', {
          title: 'Error',
          html: resp.message
        })
      }
    },
    manual () {
      this._hide()
      this.$root.$emit('show-mex-member-detail-dialog')
    },
    downloadTemplate () {
      location.href = `/download?path=static/mex/template/members.xlsx`
    },
    select () {
      if (this.file) {
        return
      }
      $(this.$refs.file).click()
    },
    selectedFile () {
      const input = this.$refs.file
      if (input.files.length) {
        this.file = input.files[0]
      }
      input.type = 'text'
      input.type = 'file'
    },
    getValidDragFile (e) {
      if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length) {
        const file = e.dataTransfer.files[0]
        console.log(file)
        if (file && file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          return file
        }
      }
      return null
    },
    setupDnd () {
      const area = $(this.$el).find('.upload-area'),
        dom = area.get(0)

      dom.addEventListener('dragover', e => {
        e.stopPropagation()
        e.preventDefault()
        area.addClass('dropping')
      }, false)

      dom.addEventListener('dragleave', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')
      }, false)

      dom.addEventListener('drop', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')

        const file = this.getValidDragFile(e)
        if (file) {
          this.file = file
        } else {
          notify('Please select a .xlsx file.', 'negative')
        }
      }, false)
    }
  },
  mounted () {
    this.setupDnd()
  }
}
</script>

<style lang="scss">
.mex-member-import-dialog {
  .modal-content {
    width: 450px;
  }

  .notes {
    margin-top: 10px;
    font-size: 13px;
    color: var(--q-color-faded);

    i {
      font-style: normal;
      border: 1px solid #ddd;
      border-radius: 3px;
      padding: 0 5px;
    }
  }
}
</style>
