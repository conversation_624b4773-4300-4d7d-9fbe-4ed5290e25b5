<template>
  <q-page id="mex__members__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div v-if="user.cpKey === 'cp_mex'"
           class="mex-statics row gutter-sm">
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.total || 0 }}</div>
                  <div class="description">Total Members</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.active || 0 }}</div>
                  <div class="description">Active Members</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6 col-xs-10">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-card-text-outline"
                        color="orange"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.balance || 0 | moneyFormat }}</div>
                  <div class="description">Total Balance</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6 col-xs-10">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-shield-airplane-outline"
                        color="warning"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.cost || 0 | moneyFormat }}</div>
                  <div class="description">
                    Total KYC Cost
                    <q-icon name="mdi-refresh"
                            color="positive"
                            @click.native="refreshCost"
                            class="ml-5 pointer">
                      <q-tooltip>Refresh</q-tooltip>
                    </q-icon>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <div v-else-if="user.cpKey === 'cp_faas'"
           class="faas-statics row gutter-sm">
        <div class="col-xs-12"
             :class="$store.state.User.KYCRequired ? 'col-sm-2-5' : (n.title !=='Invited' ? 'col-sm-3' : '') "
             v-for="(n, i) in numbers"
             :key="i">
          <q-card v-if="n.title !=='Invited' || (n.title ==='Invited' && $store.state.User.KYCRequired)"
                  class="top-statics">
            <!-- <q-card-title>
              <span>{{n.title}}</span>
            </q-card-title> -->
            <q-card-main>
              <div class="row">
                <q-icon class="summary-icon blue"
                        v-if="n.title ==='Invited'"><img src="/static/faas/img/member_invited.svg"></q-icon>
                <q-icon class="summary-icon positive"
                        v-else-if="n.title ==='Active'"><img src="/static/faas/img/member_active.svg"></q-icon>
                <q-icon class="summary-icon warning"
                        v-else-if="n.title ==='Inactive'"><img src="/static/faas/img/member_inactive.svg"></q-icon>
                <q-icon class="summary-icon on-hold"
                        v-else-if="n.title ==='On Hold'"><img src="/static/faas/img/member_on_hold.svg"></q-icon>
                <q-icon class="summary-icon kyc-failed"
                        v-else-if="n.title ==='OFAC/KYC Failed'"><img src="/static/faas/img/member_kyc_failed.svg"></q-icon>
                <div>
                  <div class="font-20">{{n.title}}</div>
                  <div class="item-value heavy">
                    {{ quick && quick[n.title] ? quick[n.title] : 0 }}
                    <q-chip dense
                            v-if="(quick['percent'] && quick['percent'][n.title] ? quick['percent'][n.title] : 0) != 0 "
                            :class="(quick['percent'] && quick['percent'][n.title] ? quick['percent'][n.title] : 0 ) > 0 ? 'positive' : 'negative'">
                      <q-icon v-if="(quick['percent'] && quick['percent'][n.title] ? quick['percent'][n.title] : 0 ) > 0"
                              class="mdi mdi-trending-up"></q-icon>
                      <q-icon v-if="(quick['percent'] && quick['percent'][n.title] ? quick['percent'][n.title] : 0 ) < 0"
                              class="mdi mdi-trending-down"></q-icon>{{ quick['percent'] && quick['percent'][n.title] ? quick['percent'][n.title] : 0  | percent(1, true) }}
                    </q-chip>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <p class="import-info"
         v-if="quick.lastImport && quick.lastImport.createdAt">
        Last Import Members: {{ quick.lastImport.createdAt}}
        <span class="success">Status: {{quick.lastImport.status }} </span>
        <span class="success">Success: {{ quick.lastImport.success}}</span>
        <span class="failed"> Failed: {{ quick.lastImport.fail}}</span>
        <a @click="viewErrors(quick.lastImport)"
           v-if="quick.lastImport.fail">Detail</a>
      </p>
      <p class="import-info"
         v-if="quick.lastMigrate && quick.lastMigrate.createdAt">
        Last Migrate Cards: {{ quick.lastMigrate.createdAt}}
        <span class="success">Status: {{quick.lastMigrate.status }} </span>
        <span class="success">Success: {{ quick.lastMigrate.success}}</span>
        <span class="failed"> Failed: {{ quick.lastMigrate.fail}}</span>
        <a @click="viewErrors(quick.lastMigrate, 'migrate')"
           v-if="quick.lastMigrate.fail">Detail</a>
      </p>
      <p class="import-info"
         v-if="quick.lastImportSsn && quick.lastImportSsn.createdAt">
        Last Upload SSN: {{ quick.lastImportSsn.createdAt}}
        <span class="success">Status: {{quick.lastImportSsn.status }} </span>
        <span class="success">Success: {{ quick.lastImportSsn.success}}</span>
        <span class="failed"> Failed: {{ quick.lastImportSsn.fail}}</span>
        <a @click="viewErrors(quick.lastImportSsn, 'importSsn')"
           v-if="quick.lastImportSsn.fail">Detail</a>
      </p>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[ 3, 5, 7, 10, 15, 20, 25, 50, 100]"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left"
                  v-if="user.cpKey === 'cp_faas'">
          <strong>Member Report</strong>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm ml-10 export-btn"
                 no-caps></q-btn>
          <q-btn icon="mdi-account-group-outline"
                 color="positive"
                 label="Import New Members"
                 @click="add"
                 class="btn-sm ml-10"
                 no-caps></q-btn>
        </template>
        <template slot="top-left"
                  v-else>
          <q-btn-dropdown class="btn-sm mr-10"
                          no-caps
                          v-if="multiSelActionLabel"
                          color="primary"
                          text-color="white"
                          :label="multiSelActionLabel">
            <q-list link>
              <q-item v-close-overlay
                      @click.native="resetMultiSel">
                <q-item-main>Unselect all</q-item-main>
              </q-item>
              <q-item>
                <q-item-main label="Change Card Status"></q-item-main>
                <q-item-side right>
                  <q-item-tile icon="mdi-menu-right"></q-item-tile>
                </q-item-side>
                <q-popover anchor="bottom left"
                           self="top right"
                           :offset="[0, -40]">
                  <q-list link>
                    <q-item v-close-overlay
                            v-for="(status, si) in allCardStatuses"
                            :key="si"
                            @click.native="changeCardStatusMulti(status.value)">
                      <q-item-main>{{ status.label }}</q-item-main>
                    </q-item>
                  </q-list>
                </q-popover>
              </q-item>
              <q-item v-close-overlay
                      @click.native="moveToAnotherEmployer()">
                <q-item-main>Move to another employer</q-item-main>
              </q-item>
              <q-item>
                <q-item-main label="Configure Splash Page"></q-item-main>
                <q-item-side right>
                  <q-item-tile icon="mdi-menu-right"></q-item-tile>
                </q-item-side>
                <q-popover anchor="bottom left"
                           self="top right"
                           :offset="[0, -40]">
                  <q-list link>
                    <q-item v-close-overlay
                            @click.native="setSplashPage()">
                      <q-item-main>Set Splash Page</q-item-main>
                    </q-item>
                    <q-item v-close-overlay
                            @click.native="clearSplashPage()">
                      <q-item-main>Clear Splash Page</q-item-main>
                    </q-item>
                  </q-list>
                </q-popover>
              </q-item>
              <!-- <q-item>
                <q-item-main label="Send message"></q-item-main>
                <q-item-side right>
                  <q-item-tile icon="mdi-menu-right"></q-item-tile>
                </q-item-side>
                <q-popover anchor="bottom left"
                           self="top right"
                           :offset="[0, -40]">
                  <q-list link>
                    <q-item v-close-overlay
                            @click.native="remindPinChange">
                      <q-item-main>Remind PIN change</q-item-main>
                    </q-item>
                    <q-item v-close-overlay
                            @click.native="sendCustomMessage()">
                      <q-item-main>Send other customized message</q-item-main>
                    </q-item>
                    <q-item v-close-overlay
                            @click.native="sendMessage()">
                      <q-item-main>Send messages from Message Center</q-item-main>
                    </q-item>
                  </q-list>
                </q-popover>
              </q-item> -->
              <q-item v-close-overlay
                      @click.native="sendMessage()">
                <q-item-main>Send messages from Message Center</q-item-main>
              </q-item>
              <q-item v-close-overlay
                      @click.native="saveToFavorites">
                <q-item-main>Save to favorites</q-item-main>
              </q-item>
            </q-list>
          </q-btn-dropdown>

          <q-btn-dropdown class="btn-sm mr-10"
                          no-caps
                          icon="mdi-star-outline"
                          color="primary"
                          text-color="white">
            <q-list link>
              <q-item v-close-overlay
                      v-for="(f, id) in favorites"
                      :key="id">
                <q-item-main @click.native="applyFavorite(f)">
                  <q-item-tile label>{{ f.name }}</q-item-tile>
                  <q-item-tile sublabel
                               class="min-w-150">{{ f.time | date('L LT') }}</q-item-tile>
                </q-item-main>
                <q-item-side right
                             @click.native="removeFavorite(id)">
                  <q-icon name="mdi-close"></q-icon>
                </q-item-side>
              </q-item>
            </q-list>
          </q-btn-dropdown>

          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <template v-if="user.cpKey === 'cp_mex'">
            <q-btn icon="mdi-account-group-outline"
                   color="positive"
                   label="Import New Members"
                   @click="add"
                   class="btn-sm mr-10"
                   no-caps></q-btn>
            <q-btn icon="mdi-eye-outline"
                   color="positive"
                   label="Precheck Card"
                   @click="precheckCard"
                   class="btn-sm mr-10"
                   no-caps></q-btn>
            <q-btn-dropdown icon="mdi-database-edit-outline"
                            color="orange"
                            label="Batch Operations"
                            class="btn-sm mr-8"
                            no-caps>
              <q-list link>
                <q-item v-close-overlay
                        @click.native="migrateCard">
                  <q-item-main>Migrate Cards</q-item-main>
                </q-item>
                <q-item v-close-overlay
                        @click.native="batchUnload">
                  <q-item-main>Batch Unload</q-item-main>
                </q-item>
                <q-item v-close-overlay
                        @click.native="batchUpdate">
                  <q-item-main>Batch Updating</q-item-main>
                </q-item>
                <q-item v-close-overlay
                        @click.native="batchTransferFree">
                  <q-item-main>Batch Config Transfer Free</q-item-main>
                </q-item>
                <q-item v-close-overlay
                        @click.native="batchUpdateLastFourSSN">
                  <q-item-main>Batch Update Last Four SSN</q-item-main>
                </q-item>
                <q-item v-close-overlay
                        @click.native="otherBatchActions">
                  <q-item-main>Other Actions</q-item-main>
                </q-item>
              </q-list>
            </q-btn-dropdown>
            <q-btn-dropdown icon="mdi-file-download-outline"
                            color="blue"
                            label="Export as XLSX"
                            class="btn-sm mr-8"
                            no-caps>
              <q-list link>
                <q-item v-close-overlay
                        @click.native="download">
                  <q-item-main>Export grid data</q-item-main>
                </q-item>
                <q-item v-close-overlay
                        @click.native="download('personal')">
                  <q-item-main>Export personal data</q-item-main>
                </q-item>
              </q-list>
            </q-btn-dropdown>
          </template>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 :class="user.cpKey === 'cp_faas' ? 'table-btn' : ''"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 :class="user.cpKey === 'cp_faas' ? 'table-btn' : ''"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :sort="true"
                    :readonly="true"
                    @change="reload"
                    @selectAll="onHeaderMultiSelectAll"
                    ref="stickyHead"
                    :label-replace-data="user.cpKey === 'cp_faas' ? labelReplaceData : {}"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'multi_check'">
              <div class="checkbox-expander"
                   @click="toggleMultiSelRow(props.row)">
                <q-checkbox v-model="multiSel.ids[props.row['Member ID']]"></q-checkbox>
              </div>
            </template>
            <template v-else-if="col.field === 'Tags'">
              <q-chip class="font-8"
                      style="margin-right: 5px;"
                      dense
                      v-for="name in props.row['Tags'].slice(0, 3)"
                      :key="name">
                {{ name }}
                <q-tooltip v-if="props.row['Tags'].length > 3"
                           content="Show all tags">
                  {{ props.row['Tags'].join(', ') }}
                </q-tooltip>
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Phone'">
              <a v-if="props.row[col.field]"
                 :href="dialLink(props.row)"
                 target="_blank">{{props.row[col.field]}}</a>
            </template>
            <template v-else-if="col.field === 'Card Status'">
              <span :class="props.row[col.field] === 'Active' ? '' : 'text-orange'">
                {{ props.row[col.field] }}
              </span>
            </template>
            <template v-else-if="col.field === 'CURP ID'">
              <div notranslate="">{{ curpMask(props.row[col.field]) }}</div>
            </template>
            <template v-else-if="col.field === 'Balance'">
              <div notranslate=""
                   class="mex-amount">{{ props.row[col.field] }}</div>
            </template>
            <template v-else-if="[
             'Legacy Balance',
             'Total Of Bank Transfers',
             'Total Of Cash Pickups',
             'Total Of Deposit',
             'Total Of Card Spend',
             'Total Of ATM W/Ds',
             'Avg Of ATM W/Ds',
             'Avg Of Bank Transfers',
             'Avg Of Cash Pickups',
             'Avg Of Card Spend',
             'Avg Of Deposit',
             'Count Of ATM W/Ds',
             'Count Of Bank Transfers',
             'Count Of Cash Pickups',
             'Count Of Deposit',
             'Count Of Card Spend',
             'Bank Transfer % of Deposit',
             'Cash Pickups % of Deposit',
             'Spend % of Deposit',
             'ATM W/Ds % of Deposit'].includes(col.field)">
              <div notranslate=""
                   class="mex-amount">{{ props.row[col.field] }}</div>
            </template>
            <template v-else-if="col.field === 'Barcode Number'">
              <span>{{ props.row[col.field] }}</span>
              <q-icon name="mdi-information-outline"
                      class="ml-5"
                      color="primary"
                      v-if="masterAdmin && props.row['useBaseRapidAgent'] === true">
                <q-tooltip>Using the base Rapid agent account</q-tooltip>
              </q-icon>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="view(props.row)">
                    <q-item-main>View</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="$c.loginAs(props.row['Member ID'])"
                          v-if="masterAdmin || canLoginAsMembers">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                  <!-- <q-item v-if="user.cpKey === 'cp_mex' && masterAdmin && !props.row['isCreatedUniTellerUser']"
                          v-close-overlay
                          @click.native="createUniTeller(props.row)">
                    <q-item-main>Create UniTeller</q-item-main>
                  </q-item> -->
                  <q-item v-close-overlay
                          @click.native="verifyKyc(props.row)"
                          v-if="['Initial', 'KYC Failed (OFAC)', 'KYC Failed (OFAC & Scan)'].includes(props.row['Status'])">
                    <q-item-main>Verify KYC</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="uploadKyc(props.row)"
                          v-if="['KYC Failed (OFAC)', 'KYC (Scan Pending)', 'KYC Failed (Scan)', 'KYC Failed (OFAC & Scan)'].includes(props.row['Status'])">
                    <q-item-main>Upload KYC Docs</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="uploadKyc(props.row)"
                          v-if="['Active', 'Onboarded'].includes(props.row['Status']) && props.row['kycStatuses']['ID Scan'] == null">
                    <q-item-main>Upload KYC Docs</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="approveKyc(props.row)"
                          v-if="['KYC Failed (OFAC)', 'KYC Failed (Scan)', 'KYC Failed (OFAC & Scan)'].includes(props.row['Status']) && props.row.kycStatuses && props.row.kycStatuses['ID Scan'] !== null">
                    <q-item-main>Manually Approve KYC</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="approveKyc(props.row)"
                          v-if="['Active', 'Onboarded'].includes(props.row['Status']) && props.row.kycStatuses && props.row.kycStatuses['ID Scan'] !== true && props.row.kycStatuses['ID Scan'] !== null">
                    <q-item-main>Manually Approve KYC</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="assignCard(props.row)"
                          v-if="props.row['Status'] === 'Onboarded'">
                    <q-item-main>Assign Card</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeCard(props.row)"
                          v-if="props.row['Status'] === 'Active' && props.row['Enrolled'] === 'No'">
                    <q-item-main>Change Card</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="migrateToBotm(props.row)"
                          v-if="props.row['currentProcessor'] === 'Rapid'">
                    <q-item-main>Migrate to BOTM card</q-item-main>
                  </q-item>

                  <q-item v-if="props.row['Status'] === 'Active' && props.row['Enrolled'] === 'Yes'">
                    <q-item-main label="Replace Card"></q-item-main>
                    <q-item-side right>
                      <q-item-tile icon="mdi-menu-right"></q-item-tile>
                    </q-item-side>
                    <q-popover anchor="bottom left"
                               self="top right"
                               :offset="[0, -40]">
                      <q-list link>
                        <q-item v-close-overlay
                                v-if="props.row['currentProcessor'] === 'Rapid'"
                                @click.native="replaceCard(props.row)">
                          <q-item-main>By Full Card # (preferred)</q-item-main>
                        </q-item>
                        <q-item v-close-overlay
                                @click.native="changeCard(props.row)">
                          <q-item-main>By Barcode Number</q-item-main>
                        </q-item>
                      </q-list>
                    </q-popover>
                  </q-item>

                  <q-item v-close-overlay
                          @click.native="enrollCard(props.row)"
                          v-if="props.row['Status'] === 'Active' && props.row['Enrolled'] === 'No'">
                    <q-item-main>Enroll/Activate Card</q-item-main>
                  </q-item>
                  <!--                  <q-item v-close-overlay @click.native="reassignCard(props.row)" v-if="props.row['Status'] === 'Active' && props.row['Enrolled'] === 'Yes'">-->
                  <!--                    <q-item-main>Reassign/Deactivate Card</q-item-main>-->
                  <!--                  </q-item>-->
                  <q-item>
                    <q-item-main label="Change Status"></q-item-main>
                    <q-item-side right>
                      <q-item-tile icon="mdi-menu-right"></q-item-tile>
                    </q-item-side>
                    <q-popover anchor="bottom left"
                               self="top right"
                               :offset="[0, -40]">
                      <q-list link>
                        <q-item v-close-overlay
                                @click.native="changeStatus(props.row, 'Active')"
                                v-if="['On Hold', 'Closed'].includes(props.row['Status'])">
                          <q-item-main>Active</q-item-main>
                        </q-item>
                        <q-item v-close-overlay
                                @click.native="changeStatus(props.row, 'On Hold')"
                                v-if="props.row['Status'] !== 'On Hold'">
                          <q-item-main>On Hold</q-item-main>
                        </q-item>
                        <q-item v-close-overlay
                                @click.native="changeStatus(props.row, 'Closed')"
                                v-if="['KYC Failed (OFAC)', 'KYC Failed (Scan)', 'KYC Failed (OFAC & Scan)'].includes(props.row['Status'])">
                          <q-item-main>Closed</q-item-main>
                        </q-item>
                      </q-list>
                    </q-popover>
                  </q-item>
                  <q-item v-if="props.row['Enrolled'] === 'Yes'">
                    <q-item-main label="Change Card Status"></q-item-main>
                    <q-item-side right>
                      <q-item-tile icon="mdi-menu-right"></q-item-tile>
                    </q-item-side>
                    <q-popover anchor="bottom left"
                               self="top right"
                               :offset="[0, -40]">
                      <q-list link>
                        <q-item v-close-overlay
                                v-for="(status, si) in c.cardStatuses(props.row.currentProcessor)"
                                :key="si"
                                @click.native="changeCardStatus(props.row, status)">
                          <q-item-main>{{ status === 'Inactive' ? 'Inactive(Terminated)' : status }}</q-item-main>
                        </q-item>
                      </q-list>
                    </q-popover>
                  </q-item>
                  <q-item v-if="['Active'].includes(props.row['Status']) && props.row['Enrolled'] === 'Yes'"
                          v-close-overlay
                          @click.native="changePin(props.row)">
                    <q-item-main>Change PIN</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['viewPan']"
                          v-close-overlay
                          @click.native="viewPan(props.row)">
                    <q-item-main>View PAN</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="del(props.row)"
                          v-if="!props.row['Barcode Number'] && !['Onboarded'].includes(props.row['Status'])">
                    <q-item-main>Delete</q-item-main>
                  </q-item>
                  <q-item v-if="masterAdmin && user.cpKey === 'cp_mex' && ['Active'].includes(props.row['Status'])"
                          v-close-overlay
                          @click.native="loadUnload(props.row)">
                    <q-item-main>Load/Unload</q-item-main>
                  </q-item>
                  <q-item v-if="agentAdmin && user.cpKey === 'cp_mex' && ['Active'].includes(props.row['Status'])"
                          v-close-overlay
                          @click.native="loadUnload(props.row)">
                    <q-item-main>Unload</q-item-main>
                  </q-item>
                  <q-item v-if="user.cpKey === 'cp_mex'"
                          v-close-overlay
                          @click.native="logOut(props.row)">
                    <q-item-main>Invalidate app session</q-item-main>
                  </q-item>
                  <q-item v-if="user.cpKey === 'cp_mex'"
                          v-close-overlay
                          @click.native="resetPasscode(props.row)">
                    <q-item-main>Clear app passcode</q-item-main>
                  </q-item>
                  <q-item v-if="user.cpKey === 'cp_mex' && ['Active'].includes(props.row['Status'])"
                          v-close-overlay
                          @click.native="viewDeposit(props.row)">
                    <q-item-main>View Direct Deposit Info</q-item-main>
                  </q-item>
                  <q-item v-if="user.cpKey === 'cp_mex' && masterAdmin && props.row['useBaseRapidAgent'] === true"
                          v-close-overlay
                          @click.native="useRapidAccount(props.row, 'employer')">
                    <q-item-main>Use Employer's Rapid Account</q-item-main>
                  </q-item>
                  <q-item v-if="masterAdmin && props.row['useBaseRapidAgent'] === false"
                          v-close-overlay
                          @click.native="useRapidAccount(props.row, 'base')">
                    <q-item-main>Use Base Rapid Account</q-item-main>
                  </q-item>
                  <q-item v-if="user.cpKey === 'cp_mex'"
                          v-close-overlay
                          @click.native="tags(props.row)">
                    <q-item-main>Tags</q-item-main>
                  </q-item>
                  <q-item v-if="user.cpKey === 'cp_mex'"
                          v-close-overlay
                          @click.native="viewSplash(props.row)">
                    <q-item-main>View Splash</q-item-main>
                  </q-item>
                  <q-item v-if="masterAdmin && props.row['useIntermex'] !== true"
                          v-close-overlay
                          @click.native="useIntermex(props.row, true)">
                    <q-item-main>Enable Intermex</q-item-main>
                  </q-item>
                  <q-item v-if="masterAdmin && props.row['useIntermex'] === true"
                          v-close-overlay
                          @click.native="useIntermex(props.row, false)">
                    <q-item-main>Disable Intermex</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

    <HtmlListDialog></HtmlListDialog>
    <PromptDialog></PromptDialog>

    <ImportDialog></ImportDialog>
    <BatchUpdateDialog></BatchUpdateDialog>
    <BatchMoveDialog></BatchMoveDialog>
    <BatchSetSplashDialog></BatchSetSplashDialog>

    <DetailDialog></DetailDialog>
    <DepositDialog></DepositDialog>

    <IdUploadDialog></IdUploadDialog>
    <IdInstructionDialog></IdInstructionDialog>
    <IdSuccessDialog></IdSuccessDialog>
    <IdManualDialog></IdManualDialog>

    <ActivatedDialog></ActivatedDialog>
    <AssignDialog></AssignDialog>
    <ReplaceDialog></ReplaceDialog>
    <AssignedDialog></AssignedDialog>
    <PinDialog></PinDialog>
    <LoadDialog></LoadDialog>
    <ImportMemberError></ImportMemberError>
    <SmsDialog :callback="verifiedSms"></SmsDialog>
    <CreateUniTeller></CreateUniTeller>
    <BatchMessageDialog></BatchMessageDialog>
    <ConfirmDialog></ConfirmDialog>
    <BatchUnloadDialog></BatchUnloadDialog>
    <TagsDialog></TagsDialog>
    <SplashDialog></SplashDialog>
    <BatchMigrateCardsDialog></BatchMigrateCardsDialog>
    <viewPanDialog></viewPanDialog>
    <BatchConfigTransferFreeDialog></BatchConfigTransferFreeDialog>
    <BatchOtherOptionsDialog></BatchOtherOptionsDialog>
    <UpdateSsnDialog></UpdateSsnDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import {
  allCardStatuses,
  EventHandlerMixin,
  fullName,
  generateColumns, isBotmAccountNumber,
  notify,
  notifyOptional,
  request,
  toSelectOptions, toSelectOptionsFromObject
} from '../../../common'
import MexPageMixin from '../../../mixins/mex/MexPageMixin'
import HtmlListDialog from '../../../components/HtmlListDialog'
import PromptDialog from '../../../components/PromptDialog'
import ImportDialog from './import'
import BatchUpdateDialog from './batch/update'
import BatchMoveDialog from './batch/move'
import BatchSetSplashDialog from './batch/setSplash'
import DetailDialog from './detail'
import DepositDialog from '../common/deposit_info'
import IdUploadDialog from './id_upload'
import IdInstructionDialog from './id_instruction'
import IdSuccessDialog from './id_success'
import IdManualDialog from './id_manual'
import ActivatedDialog from './activated'
import AssignDialog from './assign'
import ReplaceDialog from './replace'
import AssignedDialog from './assigned'
import PinDialog from './pin'
import SmsDialog from '../common/sms'
import TagsDialog from './tags'
import SplashDialog from './splash'
import LoadDialog from './card/load'
import _ from 'lodash'
import MexMemberMixin from '../../../mixins/mex/MexMemberMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import MultiSelectionListMixin from '../../../mixins/MultiSelectionListMixin'
import ImportMemberError from './../employerDashboard/employee/importMemberError'
import CreateUniTeller from './createUniTeller'
import BatchMessageDialog from './../message/batch/detail'
import ConfirmDialog from './../message/batch/confirm'
import BatchUnloadDialog from './batch_unload'
import BatchMigrateCardsDialog from './batch_migrate_card'
import viewPanDialog from './viewPan'
import BatchConfigTransferFreeDialog from './batch_config_transfer_free'
import BatchOtherOptionsDialog from './batch/otherOptions'
import UpdateSsnDialog from './batch/updateSsn'

export default {
  name: 'mex-members',
  mixins: [
    MexPageMixin,
    MexMemberMixin,
    ListPageMixin,
    FreezeColumnMixin,
    MultiSelectionListMixin,
    EventHandlerMixin('reload-mex-members'),
    EventHandlerMixin('reload-mex-members-search', 'searchMember')
  ],
  components: {
    HtmlListDialog,
    PromptDialog,
    ImportDialog,
    BatchUpdateDialog,
    BatchMoveDialog,
    BatchSetSplashDialog,
    DetailDialog,
    DepositDialog,
    IdUploadDialog,
    IdInstructionDialog,
    IdSuccessDialog,
    IdManualDialog,
    ActivatedDialog,
    AssignDialog,
    ReplaceDialog,
    AssignedDialog,
    SmsDialog,
    PinDialog,
    LoadDialog,
    ImportMemberError,
    CreateUniTeller,
    BatchMessageDialog,
    ConfirmDialog,
    BatchUnloadDialog,
    TagsDialog,
    BatchMigrateCardsDialog,
    viewPanDialog,
    SplashDialog,
    BatchConfigTransferFreeDialog,
    BatchOtherOptionsDialog,
    UpdateSsnDialog
  },
  data () {
    const cardStatuses = allCardStatuses()
    return {
      title: 'Members',
      labelReplaceData: {
        'Employer': 'Client',
        'Employer Funding Type': 'Client Funding Type'
      },
      filtersUrl: `/admin/mex/members/filters`,
      requestUrl: `/admin/mex/members/list`,
      downloadUrl: `/admin/mex/members/export`,
      columns: generateColumns([
        'multi_check',
        'Create Date',
        'Member ID', 'First Name', 'Last Name',
        'Email', 'Phone',
        'Employer', 'Employer Funding Type',
        'External Employee ID', 'Program', 'Address',
        'Gov ID (SSN/CURP)', 'Barcode Number', 'Balance',
        'Enrolled', 'Card Status', 'BOTM User ID', 'BOTM Account ID', 'Legacy Balance',
        'Tags',
        'Total Of Deposit', 'Count Of Deposit', 'Avg Of Deposit', 'Last Date Of Deposit',
        'Total Of Bank Transfers', 'Count Of Bank Transfers', 'Avg Of Bank Transfers', 'Bank Transfer % of Deposit', 'Last Date Of Bank Transfer',
        'Total Of Cash Pickups', 'Count Of Cash Pickups', 'Avg Of Cash Pickups', 'Cash Pickups % of Deposit', 'Last Date Of Cash Pickup',
        'Total Of ATM W/Ds', 'Count Of ATM W/Ds', 'Avg Of ATM W/Ds', 'ATM W/Ds % of Deposit', 'Last Date Of ATM W/Ds',
        'Total Of Card Spend', 'Count Of Card Spend', 'Avg Of Card Spend', 'Spend % of Deposit', 'Last Date Of Card Spend',
        'Last Login Time',
        'Android Device ID', 'Last Android Login Date',
        'Apple Device ID', 'Last Apple Login Date',
        'Last Web Login Date', 'Last Four SSN',
        'Status', 'Actions'
      ], ['Balance', 'Legacy Balance'], {
        'Create Date': 'u.createdAt',
        'Employer': 'g.name',
        'Member ID': 'u.id',
        'First Name': 'u.firstName',
        'Last Name': 'u.lastName',
        'Email': 'u.email',
        'Phone': 'u.mobilephone',
        'Address': 'CONCAT(u.address,u.addressline)',
        'Gov ID (SSN/CURP)': '',
        'Barcode Number': '',
        'Balance': '',
        'Enrolled': '',
        'Card Status': '',
        'Last Login Time': 'u.lastLogin'
      }),
      filterOptions: [
        {
          value: 'filter[u.id]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName]',
          label: 'Last Name'
        }, {
          value: 'filter[u.title]',
          label: 'External Employee ID'
        }, {
          value: 'filter[u.email]',
          label: 'Email'
        }, {
          value: 'filter[u.mobilephone]',
          label: 'Mobile Phone'
        }, {
          value: 'filter[u.register_step=]',
          label: 'Status',
          options: toSelectOptions([
            'Initial', 'KYC Failed (OFAC)', 'KYC (Scan Pending)',
            'KYC Failed (Scan)', 'KYC Failed (OFAC & Scan)',
            'Onboarded', 'Active', 'On Hold', 'Closed'
          ])
        }, {
          value: 'filter[e.id=]',
          label: 'Employer',
          options: [],
          source: 'employers'
        }, {
          value: 'balance',
          label: 'Current Balance',
          range: [
            {
              value: 'range[uc.balance][min]',
              type: 'localAmount'
            },
            {
              value: 'range[uc.balance][max]',
              type: 'localAmount'
            }
          ]
        }, {
          value: 'local_balance',
          label: 'Legacy Balance',
          range: [
            {
              value: 'range[uc.localBalance][min]',
              type: 'localAmount'
            },
            {
              value: 'range[uc.localBalance][max]',
              type: 'localAmount'
            }
          ]
        }, {
          value: 'is_botm',
          label: 'Is BOTM Card',
          options: toSelectOptions([
            'yes', 'no'
          ], true)
        }, {
          value: 'botm_linked',
          label: 'BOTM User linked',
          options: toSelectOptions([
            'yes', 'no'
          ], true)
        }, {
          value: 'filter[config.botmUserId=]',
          label: 'BOTM User ID'
        }, {
          value: 'filter[config.botmUserAccountId=]',
          label: 'BOTM Account ID'
        }, {
          value: 'filter[uc.status=]',
          label: 'Card Status',
          options: toSelectOptions([
            'active', 'inactive'
          ], true)
        }, {
          value: 'filter[uc.nativeStatus=]',
          label: 'Card Native Status',
          options: cardStatuses
        }, {
          value: 'filter[uc.accountNumber=]',
          label: 'Barcode Number'
        }, {
          value: 'filter[uc.issued=]',
          label: 'Card Enrolled',
          options: [
            { label: 'Yes', value: '1' },
            { label: 'No', value: '0' }
          ]
        },
        // Deposit
        {
          value: 'totalDeposit',
          label: 'Total Of Deposit',
          range: [
            {
              value: 'otherInfo[uo.totalDeposit][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.totalDeposit][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'countDeposit',
          label: 'Count Of Deposit',
          range: [
            {
              value: 'otherInfo[uo.countDeposit][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.countDeposit][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'avgDeposit',
          label: 'Avg Of Deposit',
          range: [
            {
              value: 'otherInfo[uo.avgDeposit][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.avgDeposit][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'lastDeposit',
          label: 'Last Date Of Deposit',
          range: [
            {
              value: 'otherInfo[uo.lastDepositAt][min]',
              type: 'date'
            },
            {
              value: 'otherInfo[uo.lastDepositAt][max]',
              type: 'date'
            }
          ]
        },
        // bank
        {
          value: 'totalBankTransfer',
          label: 'Total Of Bank Transfers',
          range: [
            {
              value: 'otherInfo[uo.totalBankTransfer][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.totalBankTransfer][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'countBankTransfer',
          label: 'Count Of Bank Transfers',
          range: [
            {
              value: 'otherInfo[uo.countBankTransfer][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.countBankTransfer][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'avgBankTransfer',
          label: 'Avg Of Bank Transfers',
          range: [
            {
              value: 'otherInfo[uo.avgBankTransfer][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.avgBankTransfer][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'bankTransferPercent',
          label: 'Bank Transfer % of Deposit',
          range: [
            {
              value: 'otherInfo[uo.bankTransferPercent][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.bankTransferPercent][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'lastBankAt',
          label: 'Last Date Of Bank Transfers',
          range: [
            {
              value: 'otherInfo[uo.lastBankAt][min]',
              type: 'date'
            },
            {
              value: 'otherInfo[uo.lastBankAt][max]',
              type: 'date'
            }
          ]
        },
        // cash pickup
        {
          value: 'totalCashPickup',
          label: 'Total Of Cash Pickups',
          range: [
            {
              value: 'otherInfo[uo.totalCashPickup][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.totalCashPickup][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'countCashPickup',
          label: 'Count Of Cash Pickups',
          range: [
            {
              value: 'otherInfo[uo.countCashPickup][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.countCashPickup][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'avgCashPickup',
          label: 'Avg Of Cash Pickups',
          range: [
            {
              value: 'otherInfo[uo.avgCashPickup][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.avgCashPickup][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'cashCickupPercent',
          label: 'Cash Pickups % of Deposit',
          range: [
            {
              value: 'otherInfo[uo.cashCickupPercent][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.cashCickupPercent][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'lastCashPickUpAt',
          label: 'Last Date Of Cash Pickups',
          range: [
            {
              value: 'otherInfo[uo.lastCashPickUpAt][min]',
              type: 'date'
            },
            {
              value: 'otherInfo[uo.lastCashPickUpAt][max]',
              type: 'date'
            }
          ]
        },
        // spend
        {
          value: 'totalSpend',
          label: 'Total Of Spend',
          range: [
            {
              value: 'otherInfo[uo.totalSpend][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.totalSpend][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'countSpend',
          label: 'Count Of Spend',
          range: [
            {
              value: 'otherInfo[uo.countSpend][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.countSpend][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'avgSpend',
          label: 'Avg Of Spend',
          range: [
            {
              value: 'otherInfo[uo.avgSpend][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.avgSpend][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'spendPercent',
          label: 'Spend % of Deposit',
          range: [
            {
              value: 'otherInfo[uo.spendPercent][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.spendPercent][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'lastSpendAt',
          label: 'Last Date Of Spend',
          range: [
            {
              value: 'otherInfo[uo.lastSpendAt][min]',
              type: 'date'
            },
            {
              value: 'otherInfo[uo.lastSpendAt][max]',
              type: 'date'
            }
          ]
        },
        // atm
        {
          value: 'totalAtm',
          label: 'Total Of ATM W/Ds',
          range: [
            {
              value: 'otherInfo[uo.totalAtm][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.totalAtm][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'countAtm',
          label: 'Count Of ATM W/Ds',
          range: [
            {
              value: 'otherInfo[uo.countAtm][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.countAtm][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'avgAtm',
          label: 'Avg Of ATM W/Ds',
          range: [
            {
              value: 'otherInfo[uo.avgAtm][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.avgAtm][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'atmPercent',
          label: 'ATM W/Ds % of Deposit',
          range: [
            {
              value: 'otherInfo[uo.atmPercent][min]',
              type: 'localAmount'
            },
            {
              value: 'otherInfo[uo.atmPercent][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'lastAtmAt',
          label: 'Last Date Of ATM W/Ds',
          range: [
            {
              value: 'otherInfo[uo.lastAtmAt][min]',
              type: 'date'
            },
            {
              value: 'otherInfo[uo.lastAtmAt][max]',
              type: 'date'
            }
          ]
        },
        // login date
        {
          value: 'lastAndroidLogin',
          label: 'Last Android Login Date',
          range: [
            {
              value: 'otherInfo[uo.lastAndroidLogin][min]',
              type: 'date'
            },
            {
              value: 'otherInfo[uo.lastAndroidLogin][max]',
              type: 'date'
            }
          ]
        },
        {
          value: 'lastIosLogin',
          label: 'Last Apple Login Date',
          range: [
            {
              value: 'otherInfo[uo.lastIosLogin][min]',
              type: 'date'
            },
            {
              value: 'otherInfo[uo.lastIosLogin][max]',
              type: 'date'
            }
          ]
        },
        {
          value: 'lastWebLogin',
          label: 'Last Web Login Date',
          range: [
            {
              value: 'otherInfo[uo.lastWebLogin][min]',
              type: 'date'
            },
            {
              value: 'otherInfo[uo.lastWebLogin][max]',
              type: 'date'
            }
          ]
        }
      ],
      freezeColumn: 0,
      freezeColumnRight: 2,
      numbers: [
        {
          title: 'Invited',
          value: 0,
          delta: 0
        },
        {
          title: 'Active',
          value: 0,
          delta: 0
        },
        {
          title: 'Inactive',
          value: 0,
          delta: 0
        },
        {
          title: 'On Hold',
          value: 0,
          delta: 0
        },
        {
          title: 'OFAC/KYC Failed',
          value: 0,
          delta: 0
        }
      ],
      allCardStatuses: cardStatuses,
      multiSelIdKey: 'Member ID',
      favorites: {},
      splashList: []
    }
  },
  mounted () {
    this.keyword = localStorage.getItem('mexQuickSearch') ? localStorage.getItem('mexQuickSearch') : ''
    localStorage.setItem('mexQuickSearch', '')

    this.loadFavorites()
  },
  methods: {
    dialLink (row) {
      return 'https://dialpad.com/launch?phone=' + row['Phone'].replace(/\s*/g, '')
    },
    searchMember (data) {
      this.keyword = data
      this.reload()
    },
    curpMask (id) {
      if (!id) {
        return ''
      }
      return '***-**-' + id.substring(id.length - 4)
    },
    add () {
      this.$root.$emit('show-mex-member-import-dialog')
    },
    precheckCard () {
      this.$q.dialog({
        title: 'Precheck Card',
        message: `Please enter the barcode below to check the card assignee and status:`,
        cancel: true,
        prompt: {
          model: ''
        }
      }).then(async content => {
        if (!content || !isBotmAccountNumber(content)) {
          return notify('Invalid barcode!', 'negative')
        }
        this.$q.loading.show()
        const resp = await this.c.request(`/admin/mex/members/precheck-card/${content}`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          this.$root.$emit('show-html-list-dialog', {
            title: 'Card Details',
            message: resp.message,
            list: {
              columns: [{
                field: 'label',
                label: 'Label',
                align: 'left'
              }, {
                field: 'value',
                label: 'Value',
                align: 'left'
              }],
              data: toSelectOptionsFromObject(resp.data),
              rowsPerPage: 100
            }
          })
        }
      }).catch(() => {})
    },
    batchUnload () {
      this.$root.$emit('show-mex-member-batch-unload-dialog')
    },
    migrateCard () {
      this.$root.$emit('show-mex-member-batch-migrate-cards-dialog')
    },
    batchTransferFree () {
      this.$root.$emit('show-mex-member-batch-config-transfer-free-dialog')
    },
    batchUpdateLastFourSSN () {
      const option = _.find(this.filterOptions, { label: 'Employer' })
      this.$root.$emit('show-mex-member-batch-update-last-four-ssn-dialog', {
        employers: option ? option['options'] : []
      })
    },
    otherBatchActions () {
      console.log('--------')
      const option = _.find(this.filterOptions, { label: 'Employer' })
      this.$root.$emit('show-mex-member-batch-options-dialog', {
        splashList: this.splashList,
        employers: option ? option['options'] : []
      })
    },
    view (row) {
      window.open(`/admin#/j/mex/members/${row['Member ID']}`, '_blank')
    },
    viewSplash (row) {
      this.$root.$emit('show-mex-member-splash-dialog', {
        splashList: row.splashList,
        id: row['Member ID'],
        type: 'member'
      })
    },
    edit (row) {
      this.$root.$emit('show-mex-member-detail-dialog', row)
    },
    verifyKyc (row) {
      row = _.cloneDeep(row)
      row.startKyc = true
      this.$root.$emit('show-mex-member-detail-dialog', row)
    },
    tags (row) {
      this.$root.$emit('show-mex-member-tags-dialog', row)
    },
    uploadKyc (row) {
      return this.$root.$emit('show-mex-member-id-upload-dialog', row)
    },
    approveKyc (row) {
      this.$root.$emit('show-mex-member-id-manual-dialog', row)
    },
    assignCard (row) {
      this.$root.$emit('show-mex-member-assign-dialog', row)
    },
    changeCard (row) {
      row.smsType = 'change'
      this.$root.$emit('show-mex-common-sms-dialog', row)
    },
    replaceCard (row) {
      row.smsType = 'replace'
      this.$root.$emit('show-mex-common-sms-dialog', row)
    },
    enrollCard (row) {
      row.smsType = 'enroll'
      this.$root.$emit('show-mex-common-sms-dialog', row)
    },
    migrateToBotm (row) {
      row.smsType = 'migrate'
      this.$root.$emit('show-mex-common-sms-dialog', row)
    },
    async doEnrollCard (row) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/members/${row['Member ID']}/enroll`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    },
    reassignCard (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to reassign this card?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        row.smsType = 'reassign'
        this.$root.$emit('show-mex-common-sms-dialog', row)
      }).catch(() => {})
    },
    async doReassignCard (row) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/members/${row['Member ID']}/reassign`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    },
    verifiedSms (row, token) {
      row.token = token
      row.change = false
      row.migrate = false
      if (row.smsType === 'change') {
        row.change = true
        this.$root.$emit('show-mex-member-assign-dialog', row)
      } else if (row.smsType === 'migrate') {
        row.migrate = true
        this.$root.$emit('show-mex-member-assign-dialog', row)
      } else if (row.smsType === 'replace') {
        this.$root.$emit('show-mex-member-replace-dialog', row)
      } else if (row.smsType === 'enroll') {
        this.doEnrollCard(row)
      } else if (row.smsType === 'reassign') {
        this.doReassignCard(row)
      } else if (row.smsType === 'viewDeposit') {
        this.$root.$emit('show-mex-common-deposit-dialog', row)
      } else if (row.smsType === 'changePin') {
        this.$root.$emit('show-mex-member-pin-dialog', row)
      } else if (row.smsType === 'loadUnload') {
        this.$root.$emit('show-mex-member-load-dialog', row)
      } else if (row.smsType === 'editDetail') {
        this.$root.$emit('save-mex-member-detail')
      }
    },
    changeStatus (row, status) {
      this.$q.dialog({
        title: 'Change Status',
        message: `Please enter the reason why you want to change ${fullName(row)}'s the status to "${status}":`,
        cancel: {
          color: 'negative',
          flat: true,
          noCaps: true
        },
        ok: {
          color: 'positive',
          label: 'Submit',
          noCaps: true
        },
        prompt: {
          model: ''
        }
      }).then(async reason => {
        if (!reason) {
          return notify('The reason is required!', 'negative')
        }
        this.$q.loading.show()
        const resp = await this.c.request(`/admin/mex/members/${row['Member ID']}/change-status`, 'post', {
          reason,
          status
        })
        this.$q.loading.hide()
        if (resp.success) {
          notifyOptional(resp.message)
          await this.reload()
        }
      }).catch(() => {})
    },
    async changeCardStatus (row, status) {
      this.$q.loading.show({
        message: `Changing card ${row['Barcode Number']}'s status to "${status}"...`
      })
      const resp = await this.c.request(`/admin/mex/members/${row['Member ID']}/change-card-status`, 'post', {
        cardStatus: status
      })
      this.$q.loading.hide()
      if (resp.success) {
        notifyOptional(resp.message)
        await this.reload()
      }
    },
    async changeCardStatusMulti (status) {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to change all these selected cards to "${status}"?`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show({
          message: `Changing all the selected cards to "${status}"...`
        })
        const resp = await this.c.request(`/admin/mex/members/0/multi/change-card-status`, 'post', {
          ids: this.getMultiSelKeys(),
          cardStatus: status
        })
        this.$q.loading.hide()
        if (resp.success) {
          notifyOptional(resp.message)
        }
      }).catch(() => {})
    },
    async remindPinChange () {
      return this.sendCustomMessage('Cambie su pin en la aplicación para que su tarjeta vuelva a estar activa')
    },
    async sendMessage () {
      this.$root.$emit('show-mex-message-detail-dialog', {
        members: this.getMultiSelKeys()
      })
    },
    async sendCustomMessage (message) {
      this.$q.dialog({
        title: 'Send customized message',
        message: `Please enter the message that you want to send to the selected members below:`,
        cancel: {
          color: 'negative',
          flat: true,
          noCaps: true
        },
        ok: {
          color: 'positive',
          label: 'Submit',
          noCaps: true
        },
        prompt: {
          model: message
        }
      }).then(async content => {
        if (!content) {
          return notify('The message content is required!', 'negative')
        }
        this.$q.loading.show()
        const resp = await this.c.request(`/admin/mex/members/0/multi/send-message`, 'post', {
          ids: this.getMultiSelKeys(),
          content
        })
        this.$q.loading.hide()
        if (resp.success) {
          notifyOptional(resp.message)
        }
      }).catch(() => {})
    },
    moveToAnotherEmployer () {
      const option = _.find(this.filterOptions, { label: 'Employer' })
      this.$root.$emit('show-mex-member-batch-move-dialog', {
        employers: option ? option['options'] : [],
        members: this.getMultiSelKeys()
      })
    },
    setSplashPage () {
      this.$root.$emit('show-mex-member-batch-set-splash-dialog', {
        splashList: this.splashList,
        members: this.getMultiSelKeys()
      })
    },
    clearSplashPage () {
      const members = this.getMultiSelKeys()
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to clear the splash page for the selected ${members.length} members?`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show({ message: 'Batch clearring...' })
        const resp = await request(`/admin/mex/members/batch-clear-splash`, 'post', {
          members: members
        })
        this.$q.loading.hide()
        if (resp.success) {
          this.$root.$emit('reload-mex-members')
          this._hide()
          notify(resp.data)
        }
      }).catch(() => {})
    },
    postInitFilters (res) {
      if (res.success) {
        this.splashList = res.data.splashList ? res.data.splashList : []
      }
    },
    async loadFavorites () {
      const resp = await this.c.request(`/admin/mex/members/favorites`)
      if (resp.success) {
        this.favorites = _.reverse(resp.data)
      }
    },
    removeFavorite (id) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to delete this favorite?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request(`/admin/mex/members/remove-favorite/${id}`, 'post')
        this.loading = false
        if (resp.success) {
          notify(resp.message)
          this.$delete(this.favorites, id)
        }
      }).catch(() => {})
    },
    saveToFavorites () {
      this.$q.dialog({
        title: 'Save to favorites',
        message: `Please enter the favorite name of the selected members below:`,
        cancel: true,
        prompt: {
          model: ''
        }
      }).then(async content => {
        if (!content) {
          return notify('The favorite name is required!', 'negative')
        }
        this.$q.loading.show()
        const resp = await this.c.request(`/admin/mex/members/save-favorites`, 'post', {
          ids: this.getMultiSelKeys(),
          name: content
        })
        this.$q.loading.hide()
        if (resp.success) {
          this.loadFavorites()
          notify(resp.message)
        }
      }).catch(() => {})
    },
    applyFavorite (item) {
      this.setMultiSelKeys(item.members)
      notify('Selected all the members in the favorite')
    },
    del (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to delete this member?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request(`/admin/mex/members/${row['Member ID']}/del`, 'post')
        this.loading = false
        if (resp.success) {
          this.reload()
        }
      }).catch(() => {})
    },
    viewDeposit (row) {
      if (this.masterAdmin) {
        this.$root.$emit('show-mex-common-deposit-dialog', row)
      } else {
        row.smsType = 'viewDeposit'
        this.$root.$emit('show-mex-common-sms-dialog', row)
      }
    },
    changePin (row) {
      row.smsType = 'changePin'
      this.$root.$emit('show-mex-common-sms-dialog', row)
    },
    loadUnload (row) {
      row.smsType = 'loadUnload'
      this.$root.$emit('show-mex-common-sms-dialog', row)
    },
    logOut (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to invalidate the mobile/web app session? The member has to login again to use the app.',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/members/${row['Member ID']}/invalidate-session`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp)
        }
      }).catch(() => {})
    },
    resetPasscode (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to clear this member\'s mobile/web app passcode? The member can set the passcode after restarting the app.',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/members/${row['Member ID']}/reset-passcode`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp)
        }
      }).catch(() => {})
    },
    async refreshCost () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/members/update-kyc-cost`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        _.assignIn(this.quick, resp.data)
        notify(resp)
      }
    },
    async useRapidAccount (row, type) {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/members/${row['Member ID']}/update-rapid-account`, 'post', {
        type
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
        this.reload()
      }
    },
    async useIntermex (row, type) {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/members/${row['Member ID']}/config-transfer-with-intermex`, 'post', {
        type: type
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
        this.reload()
      }
    },
    viewErrors (row, type) {
      row.type = type
      this.$root.$emit('show-faas-import-member-error-dialog', row)
    },
    batchUpdate () {
      this.$root.$emit('show-mex-member-batch-update-dialog')
    },
    createUniTeller (row) {
      this.$root.$emit('show-mex-create-uniteller-dialog', row)
    },
    viewPan (row) {
      this.$root.$emit('show-mex-member-view-pan-dialog', row)
    }
  }
}
</script>

<style lang="scss">
#mex__members__index_page {
  .summary-icon {
    height: 50px !important;
    width: 50px !important;
    font-size: 24px;
    margin-top: 15px !important;
    border-radius: 50px !important;
    margin-right: 10px;
  }
  .item-value {
    font-size: 30px;
  }
  .export-btn {
    background: #190644 !important;
  }
  .table-btn {
    border: 0.5px solid #5a5a89;
    border-radius: 10px;
  }
}
</style>
