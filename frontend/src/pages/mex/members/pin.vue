<template>
  <q-dialog
    class="mex-member-pin-dialog"
    v-model="visible"
  >
    <template slot="title">
      <div class="mb-5">
        <div class="radius-box">
          <q-icon name="mdi-key" class="font-22"></q-icon>
        </div>
      </div>
      <div class="font-16 mb-2">Change PIN</div>
      <div class="font-12 normal">Enter the new PIN to <strong notranslate="">{{ $c.fullName(entity) }}</strong></div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <q-field label="Barcode Number"
               label-width="12"
               class="mv-20">
        <q-input autocomplete="no"
                 readonly
                 v-model="entity['Barcode Number']"></q-input>
      </q-field>
      <q-field label="Full Card Number"
               label-width="12"
               class="mv-20" helper="Required. Please fill the middle 8 digits.">
        <q-input autocomplete="no"
                 type="number"
                 prefix="****"
                 :suffix="suffix"
                 align="center"
                 :error="$v.entity['Full Card Number'].$error"
                 @blur="$v.entity['Full Card Number'].$touch"
                 v-model="entity['Full Card Number']"></q-input>
      </q-field>
      <q-field label="New PIN"
               label-width="12"
               class="mv-20" helper="Format: 4 digits">
        <q-input autocomplete="no"
                 type="text"
                 :error="$v.entity['PIN'].$error"
                 @blur="$v.entity['PIN'].$touch"
                 v-model="entity['PIN']"></q-input>
      </q-field>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn label="Change PIN" no-caps
               color="positive"
               @click="submit" />
        <q-btn label="Close" no-caps
               color="grey-3" class="ml-0 mt-8"
               text-color="tertiary"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { maxLength, minLength, required } from 'vuelidate/lib/validators'
import { notifyForm, notify, request } from '../../../common'

export default {
  name: 'mex-member-pin-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      suffix: '****'
    }
  },
  validations: {
    entity: {
      'Full Card Number': {
        required,
        minLength: minLength(8),
        maxLength: maxLength(8)
      },
      'PIN': {
        required,
        digits () {
          const v = this.entity['PIN']
          return v && v.match(/^\d{4}$/) !== null
        }
      }
    }
  },
  methods: {
    async show () {
      this.$set(this.entity, 'Full Card Number', '')
      this.$set(this.entity, 'PIN', '')
      this.suffix = '****'

      const mc = this.entity.maskedCardNumber
      if (mc) {
        this.suffix = '**' + mc.substring(mc.length - 2)
      } else {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/members/${this.entity['Member ID']}/prepare-pin?accountNumber=${this.entity['Barcode Number']}`)
        this.$q.loading.hide()
        if (resp.success) {
          this.suffix = resp.data['Full Card Number']
        }
      }
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      const data = {
        accountNumber: this.entity['Barcode Number'],
        pin: this.entity['PIN'],
        fullCardNumber: this.entity['Full Card Number']
      }
      const resp = await request(`/admin/mex/members/${this.entity['Member ID']}/change-pin`, 'post', data)
      this.$q.loading.hide()
      if (resp.success) {
        this._hide()
        notify(resp)
      }
    }
  }
}
</script>

<style lang="scss">
.mex-member-pin-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
