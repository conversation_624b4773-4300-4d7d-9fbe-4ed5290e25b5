<template>
  <q-card class="wide-card">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="inner-table m--15"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">
            <span class="va-m">{{ title }}</span>
          </div>
        </template>
        <template slot="top-right">
          <q-search icon="search" v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat round dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating class="dot" v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat round dense
                 color="faded"
                 @click="reload"
                 icon="refresh"/>
        </template>
        <q-tr slot="body" slot-scope="props" :props="props">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="col.field === 'Amount'">
              <span :class="amountClass(props.row)">{{ props.row['Amount'] }}</span>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </q-card-main>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-card>
</template>

<script>
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns } from '../../../../common'

export default {
  name: 'mex-members-profile-balances',
  mixins: [
    MexPageMixin,
    ListPageMixin
  ],
  props: {
    uid: {
      type: String
    }
  },
  data () {
    return {
      title: 'Balance History',
      requestUrl: `/admin/mex/members/balances/list`,
      columns: generateColumns([
        'Date', 'Type', 'Comment', 'Previous Balance',
        'Current Balance', 'Amount'
      ], ['Previous Balance', 'Current Balance', 'Amount']),
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 5,
        page: 1
      },
      filterOptions: [
        {
          value: 'range[ucb.createdAt]',
          label: 'Balance Date',
          range: [
            {
              value: 'range[ucb.createdAt][start]',
              type: 'date'
            }, {
              value: 'range[ucb.createdAt][end]',
              type: 'date'
            }
          ]
        }
      ],
      autoLoad: true
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        member: this.uid
      }
    },
    amountClass (row) {
      let amount = row['Amount']
      if (!amount) {
        return
      }
      amount = '' + amount
      return amount.startsWith('-') ? 'text-negative' : 'text-positive'
    }
  }
}
</script>
