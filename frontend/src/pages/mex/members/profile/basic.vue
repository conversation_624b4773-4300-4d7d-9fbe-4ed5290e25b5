<template>
  <q-card id="mex__members__profile_basic"
          class="high-card">
    <q-card-main>
      <div class="radius-box bg-positive"
           v-if="entity['Status'] === 'Active'">
        <q-icon name="mdi-lock-open-outline"></q-icon>
      </div>
      <div class="radius-box bg-negative"
           v-else>
        <q-icon name="mdi-lock-outline"></q-icon>
      </div>

      <div class="text-center mb-20">
        <div class="avatar mt-5">
          <img :src="'/static/img/avatar.png'"
               alt="">
        </div>
        <div class="font-20 bold mt-5">{{ $c.fullName(entity) }}</div>
        <div class="text-faded">{{ entity['CURP ID'] }}</div>

        <div class="text-faded mt-15">Balance</div>
        <div class="font-16 heavy text-black mt--3">
          <q-icon name="mdi-refresh"
                  color="positive"
                  class="mr-5 hidden-only"></q-icon>
          <span class="va-m">{{ entity['Balance'] | moneyFormat }}</span>
          <q-icon name="mdi-refresh"
                  color="positive"
                  @click.native="$emit('reload')"
                  class="ml-5 pointer">
            <q-tooltip>Refresh</q-tooltip>
          </q-icon>
        </div>

        <q-chip class="font-13 mt-5"
                :class="statusClass(entity['Status'])">
          {{ entity['Status'] }}
        </q-chip>
      </div>
      <table class="fields-table">
        <tr v-for="(r, i) in fields"
            :key="i">
          <th>{{ r }} </th>
          <td v-if="r === 'Phone' && entity[r]">
            <a :href="dialLink(entity[r])"
               target="_blank">{{ entity[r] }}</a>
          </td>
          <td v-else-if="r === 'Support ID'">
            <a href="javascript:"
               @click="verifySupportID">Verify</a>
          </td>
          <td v-else-if="r === 'Free Period'">
            <p v-for="(config, index) in entity[r]"
               :key="index">
              {{config.type}}: {{config.start}} ~ {{config.end}}
            </p>
          </td>
          <td v-else-if="r === 'Employer Free Period'">
            <p v-for="(config, index) in entity[r]"
               :key="index">
              {{config.type}}: {{config.start}} ~ {{config.end}}
            </p>
          </td>
          <td v-else>{{ entity[r] }}</td>
        </tr>
      </table>
    </q-card-main>
  </q-card>
</template>

<script>
import MexMemberMixin from '../../../../mixins/mex/MexMemberMixin'
import { notify, request } from '../../../../common'

export default {
  name: 'mex-members-profile-basic',
  mixins: [
    MexMemberMixin
  ],
  props: {
    entity: {
      type: Object
    }
  },
  data () {
    return {
      fields: [
        'Employer Funding Type', 'Employer',
        'Email', 'Phone', 'Role', 'Support ID', 'Free-First Transfer', 'Free Promo', 'Free Period', 'Employer Free Period'
      ]
    }
  },
  methods: {
    dialLink (phone) {
      return 'https://dialpad.com/launch?phone=' + phone.replace(/\s*/g, '')
    },
    verifySupportID () {
      this.$q.dialog({
        title: 'Verify Support ID',
        message: 'Please ask the employee for the support ID and enter it below to verify:',
        prompt: {
          model: ''
        },
        cancel: true
      }).then(async code => {
        if (!code || code.length <= 0) {
          return
        }
        this.$q.loading.show('Verifying...')
        const resp = await request(`/admin/mex/members/${this.entity['Member ID']}/verify-support-id`, 'post', {
          code
        })
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp.message || 'Valid support ID.')
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
#mex__members__profile_basic {
  .avatar {
    width: 130px;
    height: 130px;
    background-color: #eee;
    border-radius: 20px;
    margin: 0 auto;
    overflow: hidden;
  }

  .radius-box {
    border: none;
    color: white;
    position: absolute;
    right: 16px;
    top: 16px;
    font-size: 18px;
    padding: 4px 5px;
    line-height: 1em;

    &.bg-positive {
      background-color: #3dd598 !important;
    }
  }

  .fields-table {
    th {
      min-width: 60px !important;
    }
  }
}
</style>
