<template>
  <q-card class="wide-card">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="inner-table m--15"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">Accounts and Cards</div>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field === 'Action' && props.row['isReal']">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row)">
                    <q-item-main>Change Status</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changePin(props.row)">
                    <q-item-main>Change PIN</q-item-main>
                  </q-item>
                  <q-item v-if="canResetSpendingLimit(props.row)"
                          v-close-overlay
                          @click.native="resetSpendingLimit(props.row)">
                    <q-item-main>Reset Spending Limit</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['canRevert']"
                          v-close-overlay
                          @click.native="revert(props.row)">
                    <q-item-main>Revert to this card</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ _.startCase(props.row['Status']) }}
              </q-chip>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>
      </q-table>
    </q-card-main>

    <PinDialog></PinDialog>
  </q-card>
</template>

<script>
import PinDialog from '../pin.vue'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, isBotmAccountNumber, notify, request } from '../../../../common'

export default {
  name: 'mex-members-profile-recipients',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    EventHandlerMixin('reload-mex-members-cards')
  ],
  components: {
    PinDialog
  },
  props: {
    uid: {
      type: String
    }
  },
  data () {
    return {
      title: 'Member Profile',
      requestUrl: `/admin/mex/members/cards/list`,
      columns: generateColumns([
        'Barcode Number', 'Card Number', 'Creation Date', 'Balance',
        'Status', 'Action'
      ]),
      autoLoad: true
    }
  },
  methods: {
    statusClass (status) {
      return {
        'Active': 'positive',
        'On Hold': 'warning'
      }[status] || 'negative'
    },
    getOtherQueryParams () {
      return {
        member: this.uid
      }
    },
    changeStatus (row) {
      this.$root.$emit('show-mex-member-card-status-dialog', row)
    },
    changePin (row) {
      this.$root.$emit('show-mex-member-pin-dialog', row)
    },
    async revert (row) {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/members/${row['Member ID']}/revert-to-old-card`, 'post', {
        'cardNumber': row['Barcode Number']
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
        this.$emit('reload')
      }
    },
    canResetSpendingLimit (row) {
      if (row.Status !== 'Active') {
        return false
      }
      return isBotmAccountNumber(row['Barcode Number'])
    },
    async resetSpendingLimit (row) {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/members/${row['Member ID']}/reset-spending-limit`, 'post', {
        'proxyValue': row['Barcode Number']
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
        this.reload()
      }
    }
  }
}
</script>
