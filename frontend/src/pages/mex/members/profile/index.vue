<template>
  <q-page id="mex__members__profile_page"
          class="common-profile-page">
    <div class="row gutter-sm mt-0">
      <div class="col-sm-12 col-md-4">
        <BasicCard :entity="entity"
                   @reload="reload(true, true)"></BasicCard>
      </div>
      <div class="col-sm-12 col-md-4">
        <DetailCard :entity="entity"
                    @reload="reload"></DetailCard>
      </div>
      <div class="col-sm-12 col-md-4">
        <NotesCard :uid="uid"></NotesCard>
      </div>
      <div class="col-12">
        <TransactionsCard :uid="uid"></TransactionsCard>
      </div>
      <div class="col-12">
        <BalancesCard :uid="uid"></BalancesCard>
      </div>
      <div class="col-12">
        <CardsCard :uid="uid"></CardsCard>
      </div>
      <div class="col-12">
        <RecipientsCard :uid="uid"></RecipientsCard>
      </div>
      <div class="col-12"
           v-if="user.cpKey === 'cp_mex'">
        <Dds :uid="uid"
             :url="`/admin/mex/members/${uid}/dds`"></Dds>
      </div>
      <div class="col-12"
           v-if="user.cpKey === 'cp_mex'">
        <Dds :uid="uid"
             :type="'w2s'"
             :url="`/admin/mex/members/${uid}/w2s`"></Dds>
      </div>
      <div class="col-12 mb-30">
        <PayrollCard :uid="uid"></PayrollCard>
      </div>
      <div class="col-12 mb-30">
        <CardHistory :uid="uid"></CardHistory>
      </div>
    </div>
    <UploadDirectDepositPdf></UploadDirectDepositPdf>

  </q-page>
</template>

<script>
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import BasicCard from './basic'
import DetailCard from './detail'
import NotesCard from './notes'
import TransactionsCard from './transactions'
import BalancesCard from './balances'
import CardsCard from './cards'
import RecipientsCard from './recipients'
import PayrollCard from './payroll'
import CardHistory from './cardHistory'
import Dds from '../../employerDashboard/employee/profile/dds'
import { EventHandlerMixin, notifySuccess, request } from '../../../../common'
import UploadDirectDepositPdf from '../../employerDashboard/employee/profile/upload_dd_pdf.vue'
export default {
  name: 'mex-members-profile',
  mixins: [
    MexPageMixin,
    EventHandlerMixin('reload-mex-members-profile')
  ],
  components: {
    BasicCard,
    DetailCard,
    NotesCard,
    TransactionsCard,
    BalancesCard,
    CardsCard,
    RecipientsCard,
    PayrollCard,
    UploadDirectDepositPdf,
    Dds,
    CardHistory
  },
  data () {
    return {
      title: 'Member Profile',
      entity: {}
    }
  },
  computed: {
    uid () {
      return this.$route.params.id
    }
  },
  watch: {
    uid () {
      this.reload()
    }
  },
  methods: {
    async reload (indicator = true, force = false) {
      indicator && this.$q.loading.show()
      const resp = await request(`/admin/mex/members/${this.uid}/profile`, 'get', {
        force
      })
      indicator && this.$q.loading.hide()
      if (resp.success) {
        this.entity = resp.data

        if (force) {
          notifySuccess(resp)
        }
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>
