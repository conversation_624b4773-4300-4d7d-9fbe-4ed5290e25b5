<template>
  <q-card class="wide-card">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="inner-table m--15"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">Recipients</div>
        </template>
        <template slot="top-right">
          <q-search icon="search" v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat round dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating class="dot" v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat round dense
                 color="faded"
                 @click="reload"
                 icon="refresh"/>
        </template>
        <q-tr slot="body" slot-scope="props" :props="props">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="col.field === 'Change History' && props.row[col.field]">
              <a href="javascript:" @click="viewChangeHistory(props.row)">View</a>
            </template>
            <template v-else-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ _.startCase(props.row['Status']) }}
              </q-chip>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </q-card-main>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-card>
</template>

<script>
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns, request } from '../../../../common'
import _ from 'lodash'

export default {
  name: 'mex-members-profile-recipients',
  mixins: [
    MexPageMixin,
    ListPageMixin
  ],
  props: {
    uid: {
      type: String
    }
  },
  data () {
    return {
      title: 'Member Profile',
      requestUrl: `/admin/mex/members/recipients/list`,
      columns: generateColumns([
        'ID', 'Full Name',
        'First Name', 'Last Name', 'Email', 'Mobile Phone',
        'Date of Birth',
        'Country', 'State', 'City', 'Address', 'Postal Code',
        'Currency', 'Payout Type', 'Payout Method',
        'Bank Account Type', 'Account Number', 'BIC Swift',
        'Status', 'Change History'
      ]),
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 5,
        page: 1
      },
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'Recipient ID'
        }
      ],
      autoLoad: true
    }
  },
  methods: {
    statusClass (status) {
      return {
        'active': 'positive',
        'closed': 'negative'
      }[status] || 'negative'
    },
    getOtherQueryParams () {
      return {
        member: this.uid
      }
    },
    async viewChangeHistory (origin) {
      const row = _.cloneDeep(origin)
      let all = row['Change History'] || null
      if (!all) {
        return
      }
      if (all === true) {
        all = []
        this.loading = true
        const resp = await request(`/admin/mex/members/recipients/${row['ID']}/history`)
        this.loading = false
        if (resp && resp.success) {
          all = resp.data || []
          origin['Change History'] = _.cloneDeep(all)
        }
      }

      const data = []
      _.forEach(all, (v, k) => {
        const diff = []
        _.forEach(v.diff, (d, k) => {
          d[0] = d[0] === null ? '' : d[0]
          d[1] = d[1] === null ? '' : d[1]
          diff.push('<b>' + _.startCase(k) + '</b>: from "<b>' + d[0] + '</b>" to "<b>' + d[1] + '</b>"')
        })
        v.diff = '<div class="text-left">' + diff.join('<br/>') + '</div>'
        v.action = _.startCase(v.action)
        data.push(v)
      })
      this.$root.$emit('show-html-list-dialog', {
        title: 'Change history',
        message: row['First Name'] + ' ' + row['Last Name'],
        list: {
          columns: [{
            field: 'action',
            label: 'Action',
            align: 'left'
          }, {
            field: 'diff',
            label: 'Changes',
            align: 'left'
          }, {
            field: 'when',
            label: 'Date time',
            align: 'left'
          }, {
            field: 'by',
            label: 'Operator',
            align: 'left'
          }],
          data,
          htmlColumns: ['diff']
        }
      })
    }
  }
}
</script>
