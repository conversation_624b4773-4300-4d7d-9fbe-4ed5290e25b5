<template>
  <q-dialog class="mex-member-retrun-balance-dialog"
            v-model="visible">
    <template slot="title">
      <div class="mb-5">
        <div class="radius-box">
          <q-icon name="mdi-credit-card-wireless"
                  class="font-22"></q-icon>
        </div>
      </div>
      <div class="font-16 mb-2">Return Balance</div>
      <div class="font-12 normal">Enter the required info to return balance to <strong notranslate="">{{ $c.fullName(entity) }}</strong></div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <q-field label="Amount"
               :label-width="12"
               class="mv-20">
        <q-input type="number"
                 min="0.01"
                 max="3500"
                 autocomplete="no"
                 :error="$v.amount.$error"
                 @blur="$v.amount.$touch"
                 v-model="amount"></q-input>
      </q-field>
      <q-field label="Reason"
               :label-width="12"
               class="mv-20 text-left">
        <q-radio v-model="type"
                 val="card"
                 label="Change Card"
                 color="positive"
                 class="mr-20"></q-radio>
        <q-radio v-model="type"
                 val="employer"
                 label="Change Empalyer"
                 color="negative"></q-radio>
      </q-field>
      <q-field v-if="type == 'employer'"
               label="Legacy employer"
               :label-width="12">
        <q-select :options="employers"
                  :filter="true"
                  v-model="employer"></q-select>
      </q-field>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn label="Submit"
               no-caps
               color="positive"
               @click="submit" />
        <q-btn label="Close"
               no-caps
               color="grey-3"
               class="ml-0 mt-8"
               text-color="tertiary"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { minValue, maxValue, required } from 'vuelidate/lib/validators'
import { notifyForm, request, notifyResponse } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
export default {
  name: 'mex-member-return-dialog',
  mixins: [
    Singleton,
    MexPageMixin
  ],
  data () {
    return {
      type: 'card',
      amount: 0,
      comment: null,
      employers: [],
      employer: null
    }
  },
  validations: {
    type: {
      required
    },
    amount: {
      required,
      minValue: minValue(0.01),
      maxValue: maxValue(3500)
    }
  },
  computed: {
  },
  methods: {
    show () {
      this.amount = 0
      this.employer = null
      this.employers = this.entity.employers || []
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      if (!this.employer && this.type === 'employer') {
        notifyResponse('Please input the comment!')
        return
      }

      const message = `Are you sure that you want to send an email to Rapid to load ${this.amount} dollars ${this.type === 'load' ? 'to' : 'from'} the account ${this.$c.fullName(this.entity)}?`
      this.$q.dialog({
        title: 'Confirm',
        message,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const data = {
          type: this.type,
          amount: this.amount,
          employer: this.employer
        }

        const resp = await request(`/admin/mex/members/${this.entity['Member ID']}/return-balance`, 'post', data)
        this.$q.loading.hide()
        if (resp.success) {
          this.$root.$emit('reload-mex-members-profile')
          this._hide()
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
.mex-member-retrun-balance-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
