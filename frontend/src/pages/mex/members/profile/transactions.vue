<template>
  <q-card class="wide-card">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="inner-table m--15"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">
            <span class="va-m">Transaction History</span>
            <q-icon name="mdi-refresh"
                    color="positive"
                    @click.native="forceReload(1)"
                    class="ml-10 font-18 pointer">
              <q-tooltip>Refresh recent 24h's transactions</q-tooltip>
            </q-icon>
            <q-icon name="mdi-update"
                    color="positive"
                    @click.native="forceReload(365)"
                    class="ml-10 font-18 pointer">
              <q-tooltip>Refresh 365 days' transactions (slower, has Slack notification)</q-tooltip>
            </q-icon>
          </div>
        </template>
        <template slot="top-right">
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field === 'Type'">
              <q-icon :name="iconName(props.row)"
                      :color="iconColor(props.row)"
                      size="16px"></q-icon>
              {{ props.row['Type'] }}
            </template>
            <template v-else-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row)">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Description'">
              <div class="wrap-desc-col">{{ props.row[col.field] }}</div>
            </template>
            <template v-else-if="['Merchant', 'Address', 'Result'].includes(col.field)">
              <div class="wrap-desc-col-sm">{{ props.row[col.field] }}</div>
            </template>
            <template v-else-if="col.field === 'Message'">
              <span>
                {{ props.row['Message'] }}
                <q-icon name="mdi-content-copy"
                        class="ml-3 pointer"
                        @click.native="copyMessage(props.row)"
                        v-if="props.row['Tooltip'] && (props.row['Rapyd ID'] || props.row['UniTeller ID'] || props.row['Intermex ID'] || props.row['Partner ID'])"
                        color="primary">
                  <q-tooltip>{{ props.row['Tooltip'] }}</q-tooltip>
                </q-icon>
                <q-icon name="mdi-alert-circle-outline"
                        class="ml-3 pointer"
                        v-else-if="props.row['Tooltip']"
                        color="negative">
                  <q-tooltip>{{ props.row['Tooltip'] }}</q-tooltip>
                </q-icon>
                <q-icon name="mdi-help"
                        class="ml-3 pointer"
                        @click.native="submitInquiry(props.row)"
                        v-if="['rapyd', 'intermex'].includes(props.row['partner']) && props.row['Tooltip'] && ['Created', 'Completed'].includes(props.row['Status'])"
                        color="primary">
                  <q-tooltip>Haven't reflected? Click to submit an inquiry</q-tooltip>
                </q-icon>
              </span>
            </template>
            <template v-else-if="col.field === 'Amount'">
              <span :class="amountClass(props.row)">{{ props.row['Amount'] }}</span>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              v-if="showActionButton(props.row)"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          v-if="props.row['Tooltip'] && (props.row['Rapyd ID'] || props.row['UniTeller ID'] || props.row['Intermex ID'])"
                          @click.native="copyMessage(props.row)">
                    <q-item-main>Copy Transfer IDs</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['Tooltip'] && props.row['Partner ID']"
                          @click.native="copyMessage(props.row)">
                    <q-item-main>Copy Partner IDs</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="(
                            ['rapyd', 'intermex'].includes(props.row['partner']) &&
                            (props.row['Rapyd ID'] || props.row['Intermex ID']) &&
                            props.row['Tooltip'] && ['Created', 'Completed', 'Hold'].includes(props.row['Status'])
                          ) ||
                          (props.row['partner'] === 'rapyd' && props.row['Under Review'])"
                          @click.native="submitInquiry(props.row)">
                    <q-item-main>Submit an inquiry</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['forceStatus'] && props.row['Rapyd ID'] && ['Created', 'Completed'].includes(props.row['Status'])"
                          @click.native="claimError(props.row)">
                    <q-item-main>Claim Transfer Failed</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="masterAdmin && props.row['KnownUnhandledRapydError'] && ['Error'].includes(props.row['Status'])"
                          @click.native="refund(props.row)">
                    <q-item-main>Refund</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="syncStatusDetails(props.row)">
                    <q-item-main>Sync Partner Status</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['Rapyd ID'] && masterAdmin"
                          @click.native="viewRapydDetails(props.row)">
                    <q-item-main>View Partner Details</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['UniTeller ID'] && masterAdmin"
                          @click.native="viewUniTellerDetails(props.row)">
                    <q-item-main>View Partner Details</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['UniTeller ID'] && masterAdmin"
                          @click.native="viewUniTellerReceipt(props.row)">
                    <q-item-main>View Partner Receipt</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['Intermex ID']"
                          @click.native="viewIntermexDetails(props.row)">
                    <q-item-main>View Partner Details</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['Intermex ID']"
                          @click.native="viewIntermexReceipt(props.row)">
                    <q-item-main>View Partner Receipt</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['canRefundAtmFee']"
                          @click.native="refundAtmFee(props.row)">
                    <q-item-main>Refund Approved ATM Fee</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['canRefundMaintenanceFee']"
                          @click.native="refundMaintenanceFee(props.row)">
                    <q-item-main>Refund Maintenance Fee</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['canRefundPayout'] && masterAgentAdmin"
                          @click.native="refundPayment(props.row)">
                    <q-item-main>Refund Payout</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['FailedToDeductBalance']"
                          @click.native="refundFailedTransfer(props.row)">
                    <q-item-main>Refund Failed Transfer</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['form1025']"
                          @click.native="downloadForm(props.row)">
                    <q-item-main>Download Form 1025</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>

              <!-- <q-btn-dropdown class="btn-sm"
                              v-if="props.row['partner'] === 'rapyd' && props.row['Under Review']"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="submitUnderReviewInquiry(props.row)">
                    <q-item-main>Submit an inquiry</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown> -->
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </q-card-main>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-card>
</template>

<script>
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { copyToClipboard, generateColumns, toSelectOptions, notify, request } from '../../../../common'

export default {
  name: 'mex-members-profile-transactions',
  mixins: [
    MexPageMixin,
    ListPageMixin
  ],
  props: {
    uid: {
      type: String
    }
  },
  data () {
    return {
      title: 'Member Profile',
      requestUrl: `/admin/mex/members/transactions/list`,
      columns: generateColumns([
        'Type', 'Date & Time', 'Transaction ID',
        'Merchant', 'MCC', 'Address', 'Result',
        'Description', 'Message', 'Account Number',
        'Amount', 'Status', 'Actions'
      ], ['Amount']),
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 5,
        page: 1
      },
      filterOptions: [
        {
          value: 'filter[uct.id=]',
          label: 'Transaction ID'
        },
        {
          value: 'filter[uct.accountStatus=]',
          label: 'Status',
          options: toSelectOptions([
            'Pending', 'Executed', 'Declined', 'Expired',
            'Confirmation', 'Created', 'Canceled', 'Error',
            'Returned', 'Completed'
          ])
        },
        {
          value: 'filter[uct.txnTime]',
          label: 'Transaction Date',
          range: [
            {
              value: 'start',
              type: 'date'
            }, {
              value: 'end',
              type: 'date'
            }
          ]
        }
      ],
      autoLoad: true,
      force: false,
      requestCb: () => {
        if (this.force) {
          notify('Submitted update request. Please refresh the list again several minutes later.', 'positive', 3000)
        }
        this.force = false
      }
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        member: this.uid,
        force: this.force
      }
    },
    statusClass (row) {
      const s = row['Status']
      return {
        Pending: 'blue',
        Approved: 'positive',
        Executed: 'positive',
        Declined: 'negative',
        Expired: 'orange',
        Created: 'blue',
        Completed: 'positive',
        Canceled: 'orange',
        Error: 'negative',
        Returned: 'negative'
      }[s] || s
    },
    iconName (row) {
      const group = row['Group']
      return {
        transfer: 'mdi-flash',
        payment: 'mdi-point-of-sale',
        debit: 'mdi-arrow-down',
        credit: 'mdi-arrow-up'
      }[group] || 'mdi-help-circle-outline'
    },
    iconColor (row) {
      const group = row['Group']
      return {
        transfer: 'orange',
        payment: 'secondary',
        debit: 'negative',
        credit: 'positive'
      }[group] || 'warning'
    },
    amountClass (row) {
      const status = row['Status']
      if (['Declined', 'Expired'].includes(status)) {
        return 'text-faded'
      }
      let amount = row['Amount']
      if (!amount) {
        return
      }
      amount = '' + amount
      return amount.startsWith('-') ? 'text-negative' : 'text-positive'
    },
    async forceReload (days = 1) {
      this.force = days
      return this.reload()
    },
    copyMessage (row) {
      const message = row['Message'] + ', ' + row['Tooltip']
      copyToClipboard(message)
    },
    viewRapydDetails (row) {
      window.open(`/admin/mex/rapyd/view-payout-details/${row['Rapyd ID']}`, '_blank')
    },
    viewUniTellerDetails (row) {
      window.open(`/admin/mex/uniteller/view-payout-details/${row['UniTeller ID']}`, '_blank')
    },
    viewUniTellerReceipt (row) {
      window.open(`/admin/mex/uniteller/view-payout-receipt/${row['UniTeller ID']}`, '_blank')
    },
    viewIntermexDetails (row) {
      window.open(`/admin/mex/intermex/view-payout-details/${row['Intermex ID']}`, '_blank')
    },
    viewIntermexReceipt (row) {
      window.open(`/admin/mex/intermex/view-payout-receipt/${row['Intermex ID']}`, '_blank')
    },
    showActionButton (row) {
      const fields = [
        'Rapyd ID', 'UniTeller ID', 'Intermex ID', 'Partner ID', 'KnownUnhandledRapydError',
        'canRefundAtmFee', 'canRefundMaintenanceFee', 'canRefundPayout',
        'FailedToDeductBalance', 'form1025'
      ]
      for (const f of fields) {
        if (row[f]) {
          return true
        }
      }
      return false
    },
    submitInquiry (row) {
      this.$q.dialog({
        title: 'Submit an inquiry for ' + row['Message'],
        message: 'Not reflected amount: ' +
          row['Amount'].replace('-', '') + '. Please enter an additional message below if you want.',
        prompt: {
          model: '',
          placeholder: 'Optional message to describe the situation.'
        },
        cancel: true
      }).then(async message => {
        this.loading = true
        const resp = await request(`/admin/mex/rapyd/submit-inquiry/${row['Transaction ID']}`, 'post', {
          message
        })
        this.loading = false
        if (resp.success) {
          notify(resp.message)
        }
      }).catch(() => {})
    },
    submitUnderReviewInquiry (row) {
      this.$q.dialog({
        title: 'Submit an inquiry for ' + row['Message'],
        message: 'The transfer is still under review by Rapyd. Continue to submit an inquiry to Rapyd.',
        cancel: true
      }).then(async message => {
        this.loading = true
        const resp = await request(`/admin/mex/rapyd/submit-under-review-inquiry/${row['Transaction ID']}`, 'post', {
          message
        })
        this.loading = false
        if (resp.success) {
          notify(resp.message)
        }
      }).catch(() => {})
    },
    claimError (row) {
      this.$q.dialog({
        title: 'Claim Transfer Failed',
        message: 'Are you sure that the member claims this transfer failed? We will need to recheck with our partners for: ' + row['Message'] + '.',
        color: 'negative',
        cancel: true
      }).then(async message => {
        this.loading = true
        const resp = await request(`/admin/mex/rapyd/claim-error/${row['Transaction ID']}`, 'post', {
          message
        })
        this.loading = false
        if (resp.success) {
          notify(resp.message)
        }
      }).catch(() => {})
    },
    refundFailedTransfer (row) {
      this.$q.dialog({
        title: 'Refund failed transfer',
        message: 'Are you sure that this transfer failed but the amount was deducted from the balance successfully?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request(`/admin/mex/members/transactions/refund-failed-transfer-with-deduction/${row['Transaction ID']}`, 'post')
        this.loading = false
        if (resp.success) {
          notify(resp.message)
        }
      }).catch(() => {})
    },
    refund (row) {
      this.$q.dialog({
        title: 'Refund',
        message: 'Are you sure that this payout failed and Rapyd has refunded it to our wallet?',
        color: 'negative',
        cancel: true
      }).then(async message => {
        this.loading = true
        const resp = await request(`/admin/mex/rapyd/refund-for-rapyd-error/${row['Transaction ID']}`, 'post', {
          message
        })
        this.loading = false
        if (resp.success) {
          notify(resp.message)
        }
      }).catch(() => {})
    },
    refundAtmFee (row) {
      this.$q.dialog({
        title: 'Refund Approved ATM Fee',
        message: 'Are you sure that you want to refund the approved ATM fee to this member?',
        color: 'negative',
        cancel: true
      }).then(async message => {
        this.loading = true
        const resp = await request(`/admin/mex/rapyd/refund-approved-atm-fee/${row['Transaction ID']}`, 'post', {
          message
        })
        this.loading = false
        if (resp.success) {
          notify(resp.message)
        }
      }).catch(() => {})
    },
    refundMaintenanceFee (row) {
      this.$q.dialog({
        title: 'Refund Maintenance Fee',
        message: 'Are you sure that you want to refund the maintenance fee to this member?',
        color: 'negative',
        cancel: true
      }).then(async message => {
        this.loading = true
        const resp = await request(`/admin/mex/rapyd/refund-maintenance-fee/${row['Transaction ID']}`, 'post', {
          message
        })
        this.loading = false
        if (resp.success) {
          notify(resp.message)
        }
      }).catch(() => {})
    },
    refundPayment (row) {
      this.$q.dialog({
        title: 'Refund Payment',
        message: 'Are you sure that you want to refund the payout for this member?',
        color: 'negative',
        cancel: true
      }).then(async message => {
        this.loading = true
        const resp = await request(`/admin/mex/rapyd/refund-payment/${row['Transaction ID']}`, 'post', {
          message
        })
        this.loading = false
        if (resp.success) {
          notify(resp.message)
        }
      }).catch(() => {})
    },
    async syncStatusDetails (row) {
      this.loading = true
      const resp = await request(`/admin/mex/rapyd/sync-partner-status/${row['Transaction ID']}`, 'post')
      this.loading = false
      if (resp.success) {
        notify(resp.message)
      }
    },
    downloadForm (row) {
      location.href = 'admin/mex/form1025/download?attachment=' + row.form1025
      notify('Downloading...')
    }
  }
}
</script>
