<template>
  <q-dialog
    class="mex-member-replace-dialog"
    v-model="visible"
  >
    <template slot="title">
      <div class="mb-5">
        <div class="radius-box">
          <q-icon name="mdi-credit-card" class="font-22"></q-icon>
        </div>
      </div>
      <div class="font-16 mb-2">Replace Card</div>
      <div class="font-12 normal">
        Please enter the full card number of the new card for <strong notranslate="">{{ $c.fullName(entity) }}</strong>
      </div>
      <div class="font-12 normal mt-15 text-positive" v-if="entity && entity['Enrolled'] === 'Yes'">
        The balance on the old card will be moved to the new card.
      </div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <q-input float-label="Full Card Number" autocomplete="no"
               class="mt-20 mb-40"
               :error="$v.entity['Card Number'].$error"
               @blur="$v.entity['Card Number'].$touch"
               v-model="entity['Card Number']"></q-input>
      <q-input float-label="Retype the Full Card Number" autocomplete="no"
               class="mt-40 mb-10"
               :error="$v.entity['Card Number 2'].$error"
               @blur="$v.entity['Card Number 2'].$touch"
               v-model="entity['Card Number 2']"></q-input>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn label="Replace Card" no-caps
               color="positive"
               @click="submit" />
        <q-btn label="Close" no-caps
               color="grey-3" class="ml-0 mt-8"
               text-color="tertiary"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { required } from 'vuelidate/lib/validators'
import { notifyForm, request } from '../../../common'

export default {
  name: 'mex-member-replace-dialog',
  mixins: [
    Singleton
  ],
  validations: {
    entity: {
      'Card Number': {
        required
      },
      'Card Number 2': {
        required,
        same () {
          return this.entity['Card Number'] === this.entity['Card Number 2']
        }
      }
    }
  },
  methods: {
    show () {
      this.$set(this.entity, 'Card Number', '')
      this.$set(this.entity, 'Card Number 2', '')
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      const data = {
        cardNumber: this.entity['Card Number']
      }
      data.token = this.entity.token
      const resp = await request(`/admin/mex/members/${this.entity['Member ID']}/replace`, 'post', data)
      this.$q.loading.hide()
      if (resp.success) {
        this.$root.$emit('reload-mex-members')
        this._hideAndEmit('show-mex-member-assigned-dialog', this.entity)
      }
    }
  }
}
</script>

<style lang="scss">
.mex-member-replace-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
