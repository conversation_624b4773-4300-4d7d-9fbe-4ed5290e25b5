<template>
  <q-dialog class="mex-member-splash-dialog"
            v-model="visible">
    <template slot="title">
      <div class="font-16 mb-2">Manage Splash</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="splash-list"
           v-if="splashList.length">
        <p class="splash-item"
           v-for="(splash, key) in splashList"
           :key="key">
          {{splash['title']}}
          <q-btn @click="submit(splash)"
                 color="negative"
                 label="Remove" />
        </p>
      </div>

    </template>
    <template slot="buttons">
      <div class="stacks">
        <!-- <q-btn label="Save Tags"
               no-caps
               color="positive"
               @click="submit" /> -->
        <q-btn label="Close"
               no-caps
               color="grey-3"
               class="ml-0 mt-8"
               text-color="tertiary"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
// import { maxLength, minLength, required } from 'vuelidate/lib/validators'
import { notify, request } from '../../../common'

export default {
  name: 'mex-member-splash-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      splashList: [],
      type: 'member'
    }
  },
  methods: {
    async show () {
      this.splashList = this.entity.splashList
      this.type = this.entity.type
    },
    async submit (item) {
      this.$q.loading.show()
      let list = this.entity.splashList
      const id = item['id']
      let resp = null
      if (this.type === 'member') {
        resp = await request(`/admin/mex/members/batch-setting-splash`, 'post', {
          splash: id,
          members: [this.entity.id],
          type: 'clear'
        })
      } else {
        resp = await request(`/admin/mex/employers/batch-setting-splash`, 'post', {
          splash: id,
          employes: [this.entity.id],
          type: 'clear'
        })
      }
      this.$q.loading.hide()
      if (resp.success) {
        this._hide()
        notify(resp)
        this.entity.splashList = list.filter(item => {
          return item['id'] !== id
        })
        this.$root.$emit('reload-mex-members')
      }
    }
  }
}
</script>

<style lang="scss">
.mex-member-splash-dialog {
  .modal-content {
    width: 450px;
  }
  .add-btn {
    cursor: pointer;
    margin-left: 10px;
  }
  .splash-list {
    height: 250px;
    overflow: scroll;
  }
  .splash-item {
    display: flex;
    margin: 0;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
  }
}
</style>
