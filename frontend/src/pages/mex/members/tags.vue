<template>
  <q-dialog class="mex-member-tags-dialog"
            v-model="visible">
    <template slot="title">
      <div class="font-16 mb-2">Manage Tags</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="tag-list"
           v-if="tags.length">
        <p class="tag-item"
           v-for="(tag, key) in tags"
           :key="key">
          <q-checkbox :label="tag"
                      :val="tag"
                      v-model="selectTags"></q-checkbox>
        </p>
      </div>
      <div class="row add-new">
        <div class="col-sm-8">
          <q-input float-label="Tag Name"
                   autocomplete="no"
                   v-model="name"></q-input>
        </div>
        <q-btn class="add-btn"
               color="positive"
               @click="addNew()">New Tag</q-btn>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn label="Save Tags"
               no-caps
               color="positive"
               @click="submit" />
        <q-btn label="Close"
               no-caps
               color="grey-3"
               class="ml-0 mt-8"
               text-color="tertiary"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
// import { maxLength, minLength, required } from 'vuelidate/lib/validators'
import { notify, request } from '../../../common'

export default {
  name: 'mex-member-tags-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      tags: [],
      selectTags: [],
      tag: '',
      name: ''
    }
  },
  methods: {
    async show () {
      this.$q.loading.show()
      // this.selectTags = this.entity.Tags
      this.tag = ''
      this.tags = []
      const resp = await request(`/admin/mex/member/${this.entity['Member ID']}/get-tags`)
      this.$q.loading.hide()
      if (resp.success) {
        this.tags = resp.data.tags
        this.selectTags = resp.data.selectTags
      }
    },
    async submit () {
      this.$q.loading.show()
      const data = {
        tags: this.selectTags
      }
      const resp = await request(`/admin/mex/member/${this.entity['Member ID']}/add-tags`, 'post', data)
      this.$q.loading.hide()
      if (resp.success) {
        this._hide()
        notify(resp)
        this.entity.tags = this.selectTags
        this.$root.$emit('reload-mex-members')
        this.$root.$emit('reload-mex-members-profile')
      }
    },
    async addNew () {
      this.$q.loading.show()
      const data = {
        name: this.name
      }
      const resp = await request(`/admin/mex/member/new-tag`, 'post', data)
      this.$q.loading.hide()
      if (resp.success) {
        this.tags.push(this.name)
        this.name = ''
      }
    }
  }
}
</script>

<style lang="scss">
.mex-member-tags-dialog {
  .modal-content {
    width: 450px;
  }
  .q-checkbox {
    margin-bottom: 10px;
  }
  .add-new {
    display: flex;
    align-items: center;
    margin-top: 5px;
  }
  .add-btn {
    cursor: pointer;
    margin-left: 10px;
  }
  .tag-list {
    height: 250px;
    overflow: scroll;
  }
  .tag-item {
    text-align: left;
    margin: 0;
  }
}
</style>
