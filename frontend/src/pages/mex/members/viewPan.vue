<template>
  <q-dialog class="mex-employer-viewPan-dialog"
            v-model="visible">
    <template slot="title">
      <div class="font-16 mb-2">BOTM Card's Pan</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="mb-25 text-blue font-24 heavy"
           notranslate="">{{ pan }}</div>
    </template>
    <template slot="buttons">
      <q-btn label="Close"
             no-caps
             color="blue"
             class="max-w-300"
             text-color="white"
             @click="_hide" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { request } from '../../../common'

export default {
  name: 'mex-member-view-pan-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      pan: null
    }
  },
  methods: {
    async show () {
      this.visible = false
      await this.init()
      if (this.pan) {
        this.visible = true
      }
    },
    async init () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/member/${this.entity['Member ID']}/viewpan`)
      this.$q.loading.hide()
      if (resp.success) {
        this.pan = resp.data
      }
    }
  }
}
</script>

<style lang="scss">
.mex-employer-view-pan-dialog {
  .modal-content {
    width: 500px;
  }
}
</style>
