<template>
  <q-dialog class="mex-message-confirm-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Confirm
      </div>
      <div class="font-12 normal text-dark">
        The following is the case where the selected user has the same type of promo.
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <q-table :data="promolist"
                 :columns="columns"
                 hide-bottom
                 :pagination.sync="paginitionFile"
                 class="main-table sticky-table mt-16"
                 separator="none">
          <q-tr slot="body"
                slot-scope="props"
                :props="props">
            <q-td v-for="col in props.cols"
                  :key="col.field">
              <template>
                <div notranslate="">{{ _.get(props.row, col.field) }}</div>
              </template>
            </q-td>
          </q-tr>
        </q-table>
      </div>

    </template>
    <template slot="buttons">
      <div class="stacks">
        <div class="row">
          <q-btn :label="'Cancel'"
                 no-caps
                 color="grey-3"
                 text-color="tertiary"
                 @click="cancel" />
          <q-btn :label="'Skip Existing'"
                 no-caps
                 color="positive"
                 class="main"
                 @click="skipSend" />
          <q-btn :label="'Send To All'"
                 no-caps
                 color="grey-3"
                 text-color="tertiary"
                 @click="sendToAll" />
        </div>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notify, request, EventHandlerMixin, generateColumns } from '../../../../common'

export default {
  name: 'mex-message-confirm-dialog',
  mixins: [
    Singleton,
    EventHandlerMixin('save-mex-message-detail', 'doSave')
  ],
  components: {
  },
  data () {
    return {
      paginitionFile: {
        page: 1,
        rowsPerPage: 10
      },
      defaultEntity: {},
      verifying: false,
      promolist: [],
      columns: generateColumns(['Status', 'Total'])
    }
  },
  methods: {
    show () {
      console.log(this.entity)
      this.promolist = [
        {
          Status: 'Not In Use',
          Total: this.entity.initPromo
        },
        {
          Status: 'Pending',
          Total: this.entity.pendingPromo
        },
        {
          Status: 'No Promo',
          Total: this.entity.noPromo
        }
      ]
      console.log(this.promolist)
    },
    onSuccess (resp) {
      notify(resp.message)
      this.$root.$emit('reload-mex-message-batch')
      this._hide()
    },
    async save () {
      this.entity['confirm'] = true
      const resp = await request(`/admin/mex/messageBatch/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.file = null
        this.onSuccess(resp)
        setTimeout(() => {
          this.$root.$emit('hide-mex-message-detail-dialog')
        }, 400)
      }
    },
    async sendToAll () {
      let message = 'Are you sure that you want to send message to all members and create promo for them?'
      this.$q.dialog({
        title: 'Confirm',
        message: message,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.entity['createPromo'] = true
        await this.save()
      }).catch(() => {})
    },
    async skipSend () {
      let message = 'Are you sure that you want to only send message to those members and skip create promo for those who already have the same promo?'
      this.$q.dialog({
        title: 'Confirm',
        message: message,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.entity['createPromo'] = false
        await this.save()
      }).catch(() => {})
    },
    cancel () {
      this.file = null
      this._hide()
    }
  },
  mounted () {
  }
}
</script>

<style lang="scss">
.mex-message-confirm-dialog {
  .modal-content {
    width: 480px;
  }

  .modal-scroll {
    max-height: none;
  }
  .main-table {
    width: 100%;
  }
}
</style>
