<template>
  <q-dialog class="mex-message-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">{{ edit ? 'Edit Message' : 'Create New Message' }}</div>
      <div class="font-12 normal text-dark">Please fill in the information below about the message.
        <p v-if="members"> That you want to send to the selected members below</p>
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-12">
          <q-select :float-label="'Message Type'"
                    autocomplete="no"
                    :options="messageTypeList"
                    :error="$v.entity['Message Type'].$error"
                    @change="$v.entity['Message Type'].$touch"
                    @input="changeMessage()"
                    :readonly="edit ? true : false"
                    v-model="entity['Message Type']"></q-select>
        </div>
        <div class="col-12">
          <q-select :float-label="'Message Channel'"
                    autocomplete="no"
                    :options="messageChannelList"
                    @input="changeMessage()"
                    :error="$v.entity['Message Channel'].$error"
                    @change="$v.entity['Message Channel'].$touch"
                    v-model="entity['Message Channel']"></q-select>
        </div>
        <div class="col-12">
          <q-input float-label="Title"
                   :error="$v.entity['Title'].$error"
                   rows="2"
                   @change="$v.entity['Title'].$touch"
                   v-model="entity['Title']"></q-input>
        </div>
        <div class="col-12">
          <q-input float-label="Title Spanish"
                   :error="$v.entity['Title Spanish'].$error"
                   rows="2"
                   @change="$v.entity['Title Spanish'].$touch"
                   v-model="entity['Title Spanish']"></q-input>
        </div>
        <div class="col-12">
          <q-input float-label="Message"
                   type="textarea"
                   :error="$v.entity['Message'].$error"
                   rows="2"
                   @change="$v.entity['Message'].$touch"
                   v-model="entity['Message']"></q-input>
        </div>
        <div class="col-12">
          <q-input float-label="Message Spanish"
                   type="textarea"
                   :error="$v.entity['Message Spanish'].$error"
                   rows="2"
                   @change="$v.entity['Message Spanish'].$touch"
                   v-model="entity['Message Spanish']"></q-input>
        </div>
        <div class="col-12">
          <q-datetime float-label="Schedule Date"
                      autocomplete="no"
                      type="datetime"
                      :format24h="true"
                      format="MM/DD/YYYY HH:mm"
                      v-model="entity['Date Scheduled']"></q-datetime>
        </div>
        <div class="col-12">
          <q-input float-label="Messaging Campaign Reason"
                   type="textarea"
                   filled
                   rows="2"
                   :error="$v.entity['Messaging Campaign Reason'].$error"
                   @change="$v.entity['Messaging Campaign Reason'].$touch"
                   v-model="entity['Messaging Campaign Reason']"></q-input>
        </div>
        <div v-if="!edit && !members.length"
             class="pt-20 col-12">
          <div class="upload-area"
               @click="select"
               :class="{selected: file}">
            <img src="/static/wilen/document-icon.svg">
            <template v-if="file">
              <div class="mt-10">Selected file:</div>
              <div class="font-13 text-blue">
                <span>{{ file.name }}</span>
                <a href="javascript:"
                   class="ml-5 link"
                   @click.stop="file = null">
                  <q-icon name="mdi-close-circle-outline"
                          class="font-20"
                          color="negative"></q-icon>
                  <q-tooltip>Remove and reselect</q-tooltip>
                </a>
              </div>
            </template>
            <template v-else>
              <div class="mt-10">Upload an XLS file by dragging the file here.</div>
            </template>
          </div>
          <input type="file"
                 class="hide"
                 accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,.csv"
                 ref="file"
                 @change="selectedFile">
        </div>
      </div>
      <q-btn v-if="!edit && !members.length"
             label="Download Template"
             no-caps
             color="blue"
             class="down-btn mt-10"
             @click="downloadTemplate"></q-btn>
      <div v-if="!isView"
           class="mt-10">
        <q-btn label="Send Test Now"
               no-caps
               color="negative"
               class="down-btn"
               @click="sendTest"></q-btn>
      </div>
    </template>
    <template slot="buttons">
      <div v-if="!isView"
           class="stacks">
        <div class="row">
          <q-btn :label="'Cancel'"
                 no-caps
                 color="grey-3"
                 text-color="tertiary"
                 @click="cancel" />
          <q-btn :label="edit ? 'Edit Batch Details' : 'Schedule Batch Send'"
                 no-caps
                 color="positive"
                 class="main"
                 @click="save" />
          <q-btn v-if="edit"
                 :label="'Clone Campaign'"
                 no-caps
                 color="blue"
                 class="main"
                 @click="clone" />
          <q-btn v-if="!entity['Date Scheduled'] && !edit"
                 :label="'Send Now'"
                 no-caps
                 color="positive"
                 class="main"
                 @click="sendNow" />
        </div>
      </div>
      <div v-else
           class="stacks">
        <div class="row">
          <q-btn :label="'Cancel'"
                 no-caps
                 color="grey-3"
                 text-color="tertiary"
                 @click="cancel" />
          <q-btn :label="'Clone Campaign'"
                 no-caps
                 color="blue"
                 class="main"
                 @click="clone" />
        </div>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notifyForm, notify, request, EventHandlerMixin, notifyResponse, uploadAttachment } from '../../../../common'
import { required } from 'vuelidate/lib/validators'
import $ from 'jquery'
import moment from 'moment'
import { date } from 'quasar'
const { addToDate } = date

const validations = {
  entity: {}
}
for (const field of [
  'Message Type', 'Message Channel', 'Title', 'Title Spanish', 'Message', 'Message Spanish',
  'Messaging Campaign Reason'
]) {
  validations.entity[field] = { required }
}

export default {
  name: 'mex-message-detail-dialog',
  mixins: [
    Singleton,
    EventHandlerMixin('save-mex-message-detail', 'doSave')
  ],
  components: {
  },
  watch: {
    file () {
      this.attachment = null
    }
  },
  data () {
    return {
      defaultEntity: {
        'Campaign ID': 0,
        'Message Type': 'call',
        'Message Channel': 'app',
        'Message': 'Hello, Please contact Customer Care at (855) 879-8344 for information regarding your TransferMex account, at your earliest convenience.',
        'Message Spanish': 'Hola, por favor a la mayor brevedad posible, ponte en contacto con nuestra área de Atención al Cliente al (855) 879-8344 para obtener información sobre tu cuenta TransferMex.',
        'Messaging Campaign Reason': ''
      },
      messageTypeList: [
        {
          value: 'call',
          label: 'Call Customer Service'
        },
        {
          value: 'general',
          label: 'Money Transfer (General)'
        },
        {
          value: 'cash_pickup',
          label: 'New Cash Pickup Feature'
        },
        // {
        //   value: 'free',
        //   label: 'Free Money Transfer (First of any method)'
        // },
        {
          value: 'free_transfer',
          label: 'Free Money Transfer'
        },
        {
          value: 'free_bank',
          label: 'Free Bank Transfer'
        },
        {
          value: 'free_cash_pickup',
          label: 'Free Cash Pickup'
        },
        {
          value: 'remind_pin_change',
          label: 'Remind PIN change'
        },
        {
          value: 'custom',
          label: 'Custom'
        }
      ],
      messageChannelList: [
        {
          value: 'app',
          label: 'in-App Push'
        },
        {
          value: 'email',
          label: 'Email'
        },
        {
          value: 'sms',
          label: 'SMS'
        },
        {
          value: 'both',
          label: 'One of SMS and Email'
        }
      ],
      verifying: false,
      file: null,
      messageType: 'call',
      messageChannel: 'app',
      minDate: null,
      members: []
    }
  },
  computed: {
    edit () {
      return this.entity['Campaign ID']
    },
    isView () {
      return this.entity['Campaign ID'] && this.entity['viewCampaign']
    }
  },
  validations,
  methods: {
    downloadTemplate () {
      location.href = `/download?path=static/mex/template/members_for_message.xlsx`
    },
    show () {
      console.log(this.entity)
      let today = new Date()
      const o = this.origin || {}
      this.members = o.members || []
      this.minDate = addToDate(today, { hours: 2 })
      this.entity['Message Type'] = this.entity['Type'] ? this.entity['Type'] : 'call'
      this.entity['Message Channel'] = this.entity['Channel'] ? this.entity['Channel'] : 'app'
      this.entity['Messaging Campaign Reason'] = this.entity['Campaign Reason']
      console.log(this.members)
    },
    sendTest () {
      let _this = this
      this.$q.dialog({
        title: 'Send Test Now',
        message: `Please enter the member Id that you want to send the message to test:`,
        cancel: {
          color: 'negative',
          flat: true,
          noCaps: true
        },
        ok: {
          color: 'positive',
          label: 'Submit',
          noCaps: true
        },
        prompt: {
          model: ''
        }
      }).then(async memberId => {
        if (!memberId) {
          return notify('The member Id is required!', 'negative')
        }
        this.$q.loading.show()
        const resp = await this.c.request(`/admin/mex/messageBatch/${memberId}/send-test-message`, 'post', _this.entity)
        if (resp.success) {
          notify(resp.message)
        }
        this.$q.loading.hide()
      }).catch(() => {})
    },
    changeMessage () {
      switch (this.entity['Message Type']) {
        case 'call':
          if (this.entity['Message Channel'] !== 'app') {
            this.entity['Message'] = 'Hello, Please contact Customer Care at (855) 879-8344 for information regarding your TransferMex account, at your earliest convenience.'
            this.entity['Message Spanish'] = 'Hola, por favor a la mayor brevedad posible, ponte en contacto con nuestra área de Atención al Cliente al (855) 879-8344 para obtener información sobre tu cuenta TransferMex.'
          } else {
            this.entity['Message'] = 'Please click here to contact Customer Care to discuss details regarding your TransferMex account.'
            this.entity['Message Spanish'] = 'Por favor haga clic aquí para ponerse en contacto con Atención al Cliente para discutir los detalles sobre su cuenta TransferMex.'
          }
          break
        case 'general':
          if (this.entity['Message Channel'] !== 'app') {
            this.entity['Message'] = 'Send money to friends and family with in Mexico with ease using TransferMex Bank Transfers or Cash Pickups! Give it a try, today. For more information visit.'
            this.entity['Message Spanish'] = '¡Envía dinero a amigos y familiares en México fácilmente al utilizar las transferencias bancarias o mandando efectivo! Dale una oportunidad, hoy mismo. Para más información visita'
          } else {
            this.entity['Message'] = 'Send money to friends and family with in Mexico with ease using TransferMex Bank Transfers or Cash Pickups! Give it a try, today.'
            this.entity['Message Spanish'] = '¡Envía dinero a amigos y familiares en México fácilmente al utilizar las transferencias bancarias o mandando efectivo! Dale una oportunidad, hoy mismo.'
          }
          break
        case 'cash_pickup':
          if (this.entity['Message Channel'] !== 'app') {
            this.entity['Message'] = 'The TransferMex App now allows you to send money  via Cash Pickup to recipients in Mexico! Give it a try, today. For more information visit.'
            this.entity['Message Spanish'] = 'La aplicación TransferMex ahora te permite enviar dinero a través de envío de dinero en efectivo a destinatarios en todo México! Dale una oportunidad, hoy mismo. Para más información visita.'
          } else {
            this.entity['Message'] = 'TransferMex App now allows you to send money  via Cash Pickup to recipients in Mexico! Give it a try, today.'
            this.entity['Message Spanish'] = 'La aplicación TransferMex ahora te permite enviar dinero a través de envío de dinero en efectivo a destinatarios en todo México! Dale una oportunidad, hoy mismo. hoy mismo.'
          }
          break
        case 'free_transfer':
          if (this.entity['Message Channel'] !== 'app') {
            this.entity['Message'] = 'We’d like to offer you a Free Money Transfer transaction to Mexico using TransferMex! Give it a try, today. For more information visit.'
            this.entity['Message Spanish'] = '¡Te ofrecemos una transferencia de dinero gratis a México utilizando TransferMex! Dale una oportunidad, hoy mismo. Para más información visita. '
          } else {
            this.entity['Message'] = 'We’d like to offer you a Free Money Transfer transaction to Mexico using TransferMex! Give it a try, today.'
            this.entity['Message Spanish'] = '¡Te ofrecemos una transferencia de dinero gratis a México utilizando TransferMex! Dale una oportunidad, hoy mismo.'
          }
          break
        case 'free_bank':
          if (this.entity['Message Channel'] !== 'app') {
            this.entity['Message'] = 'We’d like to offer you a Free Bank Transfer transaction to Mexico using TransferMex. Give it a try! For more information visit.'
            this.entity['Message Spanish'] = 'Te ofrecemos una transferencia bancaria gratis a México utilizando TransferMex. ¡Dale una oportunidad! Para más información visita.'
          } else {
            this.entity['Message'] = 'We’d like to offer you a Free Bank Transfer transaction to Mexico using TransferMex! Give it a try, today.'
            this.entity['Message Spanish'] = 'Te ofrecemos una transferencia bancaria gratis a México utilizando TransferMex. ¡Dale una oportunidad! hoy mismo.'
          }
          break
        case 'free_cash_pickup':
          if (this.entity['Message Channel'] !== 'app') {
            this.entity['Message'] = 'We’d like to offer you a Free Cash Pickup transaction to Mexico using TransferMex. Give it a try! For more information visit.'
            this.entity['Message Spanish'] = 'Nos gustaría ofrecerte una transacción de envío de dinero en efectivo gratis a México utilizando TransferMex. ¡Dale una oportunidad! Para más información visita.'
          } else {
            this.entity['Message'] = 'We’d like to offer you a Free Cash Pickup transaction to Mexico using TransferMex! Give it a try, today.'
            this.entity['Message Spanish'] = 'Nos gustaría ofrecerte una transacción de envío de dinero en efectivo gratis a México utilizando TransferMex. ¡Dale una oportunidad, hoy mismo.'
          }
          break
        case 'remind_pin_change':
          this.entity['Title'] = 'Change your pin in the app so that your card is active again'
          this.entity['Title Spanish'] = 'Por favor cambia tu pin en la aplicación para que tu tarjeta vuelva a estar activa.'
          this.entity['Message'] = 'Change your pin in the app so that your card is active again'
          this.entity['Message Spanish'] = 'Por favor cambia tu pin en la aplicación para que tu tarjeta vuelva a estar activa.'
          break
        default:
          this.entity['Message'] = ''
          this.entity['Message Spanish'] = ''
          break
      }
    },
    onSuccess (resp) {
      notify(resp.message)
      this.$root.$emit('reload-mex-message-batch')
      this._hide()
    },
    clone () {
      this.entity['Campaign ID'] = null
      this.entity['viewCampaign'] = false
      this.entity['Date Scheduled'] = null
      console.log(this.entity)
    },
    async sendNow () {
      this.entity['sendNow'] = true
      this.save()
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      if (this.edit) {
        await this.update()
      } else {
        if (!this.file && !this.members.length) {
          return notifyResponse(`Please upload a file of member to send messages to`)
        }
        let message = 'Are you sure that you want to send message to those members?'
        this.$q.dialog({
          title: 'Confirm',
          message: ['free_transfer', 'free_bank', 'free_cash'].includes(this.entity['Message Type']) ? message + ' and this promo does not cover the first transfer free promo.' : message,
          color: 'negative',
          cancel: true
        }).then(async () => {
          await this.doSave()
        }).catch(() => {})
      }
    },
    async update () {
      this.$q.loading.show({
        message: 'Saving ...'
      })
      this.entity['Schedule Date'] = this.entity['Schedule Date'] ? moment(this.entity['Schedule Date']).format('MM/DD/YYYY HH:mm') : null
      const resp = await request(`/admin/mex/messageBatch/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.file = null
        this.onSuccess(resp)
      }
    },
    async doSave () {
      if (!this.attachment && !this.members.length) {
        this.$q.loading.show({ message: 'Uploading...' })
        const category = 'messageaCenter'
        const resp = await uploadAttachment(this.file, category)
        if (typeof resp === 'string') {
          this.$q.loading.hide()
          return notifyResponse(`Failed to upload: ${resp}`)
        }
        this.attachment = resp
      }
      this.$q.loading.show({
        message: 'Saving ...'
      })
      if (!this.members.length) {
        this.entity['attachment'] = this.attachment.id
        this.entity['createType'] = 'file'
      } else {
        this.entity['members'] = this.members
        this.entity['createType'] = 'id'
      }
      console.log(this.entity)
      this.entity['Schedule Date'] = this.entity['Schedule Date'] ? moment(this.entity['Schedule Date']).format('MM/DD/YYYY hh:mm') : null
      const resp = await request(`/admin/mex/messageBatch/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        if (resp.message === 'confirm') {
          this.entity['initPromo'] = resp.data.initPromo
          this.entity['pendingPromo'] = resp.data.pendingPromo
          this.entity['noPromo'] = resp.data.noPromo
          this.$root.$emit('show-mex-message-confirm-dialog', this.entity)
        } else {
          this.file = null
          this.onSuccess(resp)
        }
      }
    },
    cancel () {
      this.file = null
      this._hide()
    },
    select () {
      if (this.file) {
        return
      }
      $(this.$refs.file).click()
    },
    selectedFile () {
      const input = this.$refs.file
      if (input.files.length) {
        this.file = input.files[0]
      }
      input.type = 'text'
      input.type = 'file'
    },
    getValidDragFile (e) {
      if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length) {
        const file = e.dataTransfer.files[0]
        if (file && file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          return file
        }
      }
      return null
    },
    setupDnd () {
      const area = $(this.$el).find('.upload-area'),
        dom = area.get(0)

      dom.addEventListener('dragover', e => {
        e.stopPropagation()
        e.preventDefault()
        area.addClass('dropping')
      }, false)

      dom.addEventListener('dragleave', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')
      }, false)

      dom.addEventListener('drop', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')

        const file = this.getValidDragFile(e)
        if (file) {
          this.file = file
        } else {
          notify('Please select a .xlsx file.', 'negative')
        }
      }, false)
    }
  },
  mounted () {
    this.setupDnd()
  }
}
</script>

<style lang="scss">
.mex-message-detail-dialog {
  .modal-content {
    width: 580px;
  }

  .modal-scroll {
    max-height: none;
  }
}
</style>
