<template>
  <q-page id="mex__message_center__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="mex-statics row gutter-sm">
        <div class="col-sm-3 col-6 col-sm-2-5">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-message-processing-outline"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.created || 0 }}</div>
                  <div class="description">Messages Created </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6 col-sm-2-5">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-message-processing-outline"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.scheduled || 0 }}</div>
                  <div class="description">Messages Scheduled</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6 col-sm-2-5">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-message-processing-outline"
                        color="negative"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.cancelled || 0 }}</div>
                  <div class="description">Messages Cancelled</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6 col-sm-2-5">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-message-processing-outline"
                        color="warning"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.error || 0 }}</div>
                  <div class="description">
                    Messages Error
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>

        <div class="col-sm-3 col-6 col-sm-2-5">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-message-processing-outline"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.sent || 0 }}</div>
                  <div class="description">
                    Messages Sent
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <div class="limit-desc">
        <p>SMS Daily Limit: {{quick.smsDailyUsed}}/{{quick.smsDailyLimit}}, Single Limit: {{quick.smsOnceLimit}}</p>
        <p>Email Daily Limit: {{quick.emailDailyUsed}}/{{quick.emailDailyLimit}}, Single Limit: {{quick.emailOnceLimit}}</p>
      </div>
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn icon="mdi-message-processing-outline"
                 color="positive"
                 label="Create New Message"
                 @click="add"
                 class="btn-sm mr-10"
                 no-caps></q-btn>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Message Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row['Message Status'])">
                {{ props.row['Message Status'] }}
              </q-chip>
            </template>
            <template v-else-if="['# of users', '% viewed', '% cta taken', '% error'].includes(col.field)">
              <div v-if="col.field == '# of users'"
                   class="mex-amount">{{ _.get(props.row, col.field) }}</div>
              <div class="mex-amount"
                   v-else>{{ _.get(props.row, col.field) | percent(1)}}</div>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)"
                          v-if="props.row['Message Status'] != 'Sent' && props.row['Message Status'] != 'Pending'">
                    <q-item-main>Edit Campaign Details</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="cancel(props.row)"
                          v-if="['Scheduled', 'Created'].includes(props.row['Message Status'])">
                    <q-item-main>Cancel Campaign Details</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="sendNow(props.row)"
                          v-if="['Scheduled', 'Cancelled', 'Error'].includes(props.row['Message Status'])">
                    <q-item-main>Send Campaign Now</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="updateStatus(props.row)"
                          v-if="['Pending'].includes(props.row['Message Status'])">
                    <q-item-main>Check & Update Status</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="viewCampaign(props.row)">
                    <q-item-main>View Campaign</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <DetailDialog></DetailDialog>
    <ConfirmDialog></ConfirmDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, toSelectOptions, request } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import DetailDialog from './detail'
import ConfirmDialog from './confirm'

export default {
  name: 'mex-message-batch',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-message-batch')
  ],
  components: {
    DetailDialog,
    ConfirmDialog
  },
  data () {
    return {
      title: 'Campaign Summary Report',
      requestUrl: `/admin/mex/message/batch/list`,
      downloadUrl: `/admin/mex/message/batch/export`,
      columns: generateColumns([
        'Campaign ID',
        'Campaign Reason',
        'Date Created',
        'Date Scheduled',
        'Date Sent',
        'Message Type',
        'Message Channel',
        'Sent By',
        '# of users',
        '% viewed',
        '% cta taken',
        '% error',
        'Title',
        'Title Spanish',
        'Message',
        'Message Spanish',
        'Message Status',
        'Action'
      ]),
      filterOptions: [
        {
          value: 'filter[mb.status=]',
          label: 'Message Status',
          options: toSelectOptions([
            'Pending', 'Created', 'Sent', 'Scheduled', 'Error', 'Canceled'
          ])
        },
        {
          value: 'filter[mb.channel=]',
          label: 'Message Channel',
          options: [
            {
              value: 'sms',
              label: 'SMS'
            },
            {
              value: 'email',
              label: 'Email'
            },
            {
              value: 'app',
              label: 'in-App Push'
            },
            {
              value: 'both',
              label: 'One of SMS and Email'
            }
          ]
        },
        {
          value: 'filter[mb.type=]',
          label: 'Message Type',
          options: [
            {
              value: 'call',
              label: 'Call Customer Service'
            },
            {
              value: 'general',
              label: 'Money Transfer (General)'
            },
            {
              value: 'cash_pickup',
              label: 'New Cash Pickup Feature'
            },
            // {
            //   value: 'free',
            //   label: 'Free Money Transfer (First of any method)'
            // },
            {
              value: 'free_transfer',
              label: 'Free Money Transfer'
            },
            {
              value: 'free_bank',
              label: 'Free Bank Transfer'
            },
            {
              value: 'free_cash_pickup',
              label: 'Free Cash Pickup'
            },
            {
              value: 'remind_pin_change',
              label: 'Remind PIN change'
            },
            {
              value: 'custom',
              label: 'Custom'
            }
          ]
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true,
      keyword: ''
    }
  },
  computed: {
    statColClass () {
      return this.masterAdmin ? 'col-sm-5' : 'col-sm-2-5'
    }
  },
  methods: {
    init () {
      if (this.$route.params.id) {
        this.filters = [
          {
            field: 'filter[t.id=]',
            predicate: '=',
            value: this.$route.params.id
          }
        ]
      }
    },
    statusClass (status) {
      return {
        'Created': 'blue',
        'Scheduled': 'purple',
        'Error': 'negative',
        'Sent': 'positive',
        'Cancelled': 'orange'
      }[status] || status
    },
    onSuccess () {
      this.$root.$emit('reload-mex-message-batch')
      this.$root.$emit('reload-mex-message-center-record')
    },
    add () {
      this.$root.$emit('show-mex-message-detail-dialog')
    },
    edit (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to edit the message?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        row['viewCampaign'] = false
        this.$root.$emit('show-mex-message-detail-dialog', row)
      }).catch(() => {})
    },
    viewCampaign (row) {
      row['viewCampaign'] = true
      this.$root.$emit('show-mex-message-detail-dialog', row)
    },
    async updateStatus (row) {
      this.$q.loading.show({
        message: 'Checking ...'
      })
      const resp = await request(`/admin/mex/message/batch/updateStatus`, 'post', {
        id: row['Campaign ID']
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    },
    async sendNow (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to send the message right now?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show({
          message: 'Sending ...'
        })
        const resp = await request(`/admin/mex/message/batch/action`, 'post', {
          type: 'sent',
          id: row['Campaign ID']
        })
        this.$q.loading.hide()
        if (resp.success) {
          this.file = null
          this.onSuccess(resp)
        }
      }).catch(() => {})
    },
    async cancel (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to cancel sending the message?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show({
          message: 'Cancelling ...'
        })
        const resp = await request(`/admin/mex/message/batch/action`, 'post', {
          type: 'cancel',
          id: row['Campaign ID']
        })
        this.$q.loading.hide()
        if (resp.success) {
          this.file = null
          this.onSuccess(resp)
        }
      }).catch(() => {})
    }
  }
}
</script>
<style lang="scss">
.limit-desc {
  p {
    margin-top: 4px;
    margin-bottom: 0;
  }
}
</style>
