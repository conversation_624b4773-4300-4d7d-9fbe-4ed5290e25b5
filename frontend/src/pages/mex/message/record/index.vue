<template>
  <q-page id="mex__message_center__record_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="mex-statics row gutter-sm">
        <div class="col-sm-3 col-6 col-sm-2-5">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-message-processing-outline"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.created || 0 }}</div>
                  <div class="description">Messages Created </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6 col-sm-2-5">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-message-processing-outline"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.scheduled || 0 }}</div>
                  <div class="description">Messages Scheduled</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6 col-sm-2-5">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-message-processing-outline"
                        color="negative"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.cancelled || 0 }}</div>
                  <div class="description">Messages Cancelled</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6 col-sm-2-5">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-message-processing-outline"
                        color="warning"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.error || 0 }}</div>
                  <div class="description">
                    Messages Error
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>

        <div class="col-sm-3 col-6 col-sm-2-5">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-message-processing-outline"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.sent || 0 }}</div>
                  <div class="description">
                    Messages Sent
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Message Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row['Message Status'])">
                {{ props.row['Message Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="cancel(props.row)"
                          v-if="['Scheduled', 'Created'].includes(props.row['Message Status'])">
                    <q-item-main>Cancel</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="sendNow(props.row)"
                          v-if="['Scheduled', 'Cancelled', 'Error', 'Created'].includes(props.row['Message Status'])">
                    <q-item-main>Send Now</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <DetailDialog></DetailDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, toSelectOptions, request } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import DetailDialog from '../batch/detail'

export default {
  name: 'mex-message-center-record',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-message-center-record')
  ],
  components: {
    DetailDialog
  },
  data () {
    return {
      title: 'Message Record',
      requestUrl: `/admin/mex/message/record/list`,
      downloadUrl: `/admin/mex/message/record/export`,
      columns: generateColumns([
        'Campaign ID',
        'Campaign Reason',
        'Message ID',
        'Date Created',
        'Date Scheduled',
        'Date Sent',
        'User ID',
        'Sent To',
        'Employer',
        'Message Type',
        'Message Channel',
        'Sent By',
        'Date Viewed By User',
        'Message Read Device',
        'User Perform CTA?',
        'Date CTA Taken',
        'Error Message',
        'Message Status',
        'Action'
      ]),
      filterOptions: [
        {
          value: 'filter[mr.status=]',
          label: 'Message Status',
          options: toSelectOptions([
            'Pending', 'Created', 'Sent', 'Scheduled', 'Error', 'Canceled'
          ])
        },
        {
          value: 'filter[mr.sentTo=]',
          label: 'Send To (Member ID)'
        },
        {
          value: 'filter[mr.channel=]',
          label: 'Message Channel',
          options: [
            {
              value: 'sms',
              label: 'SMS'
            },
            {
              value: 'email',
              label: 'Email'
            },
            {
              value: 'app',
              label: 'in-App Push'
            },
            {
              value: 'both',
              label: 'One of SMS and Email'
            }
          ]
        },
        {
          value: 'filter[mr.type=]',
          label: 'Message Type',
          options: [
            {
              value: 'call',
              label: 'Call Customer Service'
            },
            {
              value: 'general',
              label: 'Money Transfer (General)'
            },
            {
              value: 'cash_pickup',
              label: 'New Cash Pickup Feature'
            },
            // {
            //   value: 'free',
            //   label: 'Free Money Transfer (First of any method)'
            // },
            {
              value: 'free_transfer',
              label: 'Free Money Transfer'
            },
            {
              value: 'free_bank',
              label: 'Free Bank Transfer'
            },
            {
              value: 'free_cash_pickup',
              label: 'Free Cash Pickup'
            },
            {
              value: 'remind_pin_change',
              label: 'Remind PIN change'
            },
            {
              value: 'custom',
              label: 'Custom'
            }
          ]
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true
    }
  },
  computed: {
    statColClass () {
      return this.masterAdmin ? 'col-sm-5' : 'col-sm-2-5'
    }
  },
  methods: {
    init () {
      if (this.$route.params.id) {
        this.filters = [
          {
            field: 'filter[t.id=]',
            predicate: '=',
            value: this.$route.params.id
          }
        ]
      }
    },
    onSuccess () {
      this.$root.$emit('reload-mex-message-batch')
      this.$root.$emit('reload-mex-message-center-record')
    },
    statusClass (status) {
      return {
        'Created': 'blue',
        'Scheduled': 'purple',
        'Error': 'negative',
        'Sent': 'positive',
        'Cancelled': 'orange'
      }[status] || status
    },
    async sendNow (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to send the message right now?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show({
          message: 'Sending ...'
        })
        const resp = await request(`/admin/mex/message/record/action`, 'post', {
          type: 'sent',
          id: row['Campaign ID']
        })
        this.$q.loading.hide()
        if (resp.success) {
          this.file = null
          this.onSuccess(resp)
        }
      }).catch(() => {})
    },
    async cancel (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to cancel sending the message?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show({
          message: 'Cancelling ...'
        })
        const resp = await request(`/admin/mex/message/record/action`, 'post', {
          type: 'cancel',
          id: row['Campaign ID']
        })
        this.$q.loading.hide()
        if (resp.success) {
          this.file = null
          this.onSuccess(resp)
        }
      }).catch(() => {})
    }
  }
}
</script>
