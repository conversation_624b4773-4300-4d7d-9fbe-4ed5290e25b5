<template>
  <q-page id="mex__payroll_reports_batch_detail__index_page">
    <div class="page-header">
      <div class="title mt-15">{{ quick.title }} {{ title }}</div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3  col-xs-6"
             v-for="(n, i) in numbers"
             :key="i">
          <q-card>
            <q-card-title>
              <span>{{n.title}}</span>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy">
                <span v-if="n.title === 'Payments Total'">
                  {{quick['total'] ? quick['total']['total'] : 0}}</span>
                <span v-if="n.title === 'Payments Pending'">
                  {{quick['pending'] ? quick['pending']['total'] : 0}}</span>
                <span v-if="n.title === 'Payments Completed'">
                  {{quick['completed'] ? quick['completed']['total'] : 0}}</span>
                <span v-if="n.title === 'Payments Canceled'">
                  {{quick['canceled'] ? quick['canceled']['total'] : 0}}</span>
              </div>
              <div class="font-14 item-count mb-10">
                <span v-if="n.title === 'Payments Total'">
                  {{quick['total'] ? quick['total']['count'] : 0}}</span>
                <span v-if="n.title === 'Payments Pending'">
                  {{quick['pending'] ? quick['pending']['count'] : 0 }}</span>
                <span v-if="n.title === 'Payments Completed'">
                  {{quick['completed'] ? quick['completed']['count'] : 0 }}</span>
                <span v-if="n.title === 'Payments Canceled'">
                  {{quick['canceled'] ? quick['canceled']['count'] : 0 }}</span>
                {{n.sub}}
              </div>
              <div v-if="n.title !== 'Payments Total' "
                   class="font-14 item-count mb-10">
                <span v-if="n.title === 'Payments Pending'">
                  {{quick['pending'] ? quick['pending']['employees'] : 0}}</span>
                <span v-if="n.title === 'Payments Completed'">
                  {{quick['completed'] ? quick['completed']['employees'] : 0 }}</span>
                <span v-if="n.title === 'Payments Canceled'">
                  {{quick['canceled'] ? quick['canceled']['employees'] : 0}}</span>
                Employees
              </div>
              <div v-if="n.title !== 'Payments Total' "
                   class="font-14 item-count mb-10">
                <span v-if="n.title === 'Payments Pending'">
                  {{quick['pending'] ? quick['pending']['avgPayment'] : 0}}</span>
                <span v-if="n.title === 'Payments Completed'">
                  {{quick['completed'] ? quick['completed']['avgPayment'] : 0}}</span>
                <span v-if="n.title === 'Payments Canceled'">
                  {{quick['canceled'] ? quick['canceled']['avgPayment'] : 0}}</span> Avg payment
              </div>
            </q-card-main>
          </q-card>
        </div>
        <!-- <div class="col-sm-3">
          <q-card>
            <q-card-title>
              <span>Total Transactions</span>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy mb-10">{{ 0 | moneyFormat}}</div>
              <a href="/j/leaflink/payments"
                 class="link bold font-12">View Report <i class="mdi mdi-arrow-right"></i></a>
            </q-card-main>
          </q-card>
        </div> -->
      </div>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 label="Export as XLSX"
                 color="primary"
                 @click="download"
                 class="btn-sm mr-8 export-btn"
                 no-caps></q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10 font-18"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="font-18"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Payment Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Payment Status'])">
                {{ _.get(props.row, col.field) }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Member ID'">
              <span class="font-12 view-btn"
                    @click="view(props.row)"> {{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import ForceReloadMixin from '../../../../mixins/ForceReloadMixin'

export default {
  name: 'mex-payroll-batch-detail-summary-report',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    ForceReloadMixin
  ],
  computed: {
    uid () {
      return this.$route.params.id
    }
  },
  watch: {
    uid () {
      this.reload()
    }
  },
  data () {
    return {
      title: ' Batch File Detail',
      requestUrl: `/admin/mex/payroll/batch/detail/list`,
      downloadUrl: `/admin/mex/payroll/batch/detail/export`,
      columns: generateColumns([
        'Employer', 'Batch Create Date', 'Batch Send Date', 'Batch ID', 'File name', 'Member First Name',
        'Member Last Name', 'Member ID', 'External Employee ID', 'Load Account', 'Payment Status'
      ]),
      filterOptions: [
        {
          value: 'filter[u.id]',
          label: 'Employee ID'
        },
        {
          value: 'filter[u.firstName]',
          label: 'Employee First Name'
        },
        {
          value: 'filter[u.lastName]',
          label: 'Employee Last Name'
        },
        {
          value: 'filter[u.title]',
          label: 'External Employee ID'
        },
        {
          value: 'filter[ep.status]',
          label: 'Status',
          options: [
            { label: 'Canceled', value: 'canceled' },
            { label: 'Pending', value: 'pending' },
            { label: 'Completed', value: 'Completed' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      keyword: '',
      numbers: [
        { title: 'Payments Total', sub: 'count' },
        { title: 'Payments Pending', sub: 'count' },
        { title: 'Payments Completed', sub: 'count' },
        { title: 'Payments Canceled', sub: 'count' }
      ]
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        batchId: this.uid
      }
    },
    view (row) {
      window.open(`/admin#/j/mex/members/${row['Member ID']}`, '_blank')
    },
    statusClass (status) {
      const cls = []
      cls.push({
        'Canceld': 'Canceld',
        'Pending': 'pending',
        'Completed': 'positive'
      }[status] || status)
      return cls
    }
  }
}
</script>
<style lang="scss">
#mex__payroll_reports_batch_detail__index_page {
  .q-chip.pending {
    color: #ff9f00;
    background-color: rgba($color: #ff9f00, $alpha: 0.1);
  }
  .view-btn {
    color: #0062ff;
    cursor: pointer;
    text-decoration: underline;
  }
}
</style>
