<template>
  <q-page id="mex__payroll_reports_batch__index_page">
    <div class="page-header">
      <div class="title mt-15">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3  col-xs-6"
             v-for="(n, i) in numbers"
             :key="i">
          <q-card>
            <q-card-title>
              <span>{{n.title}}</span>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy font-mono">
                <span v-if="n.title === 'Batches Total'">
                  {{quick['total'] ? quick['total']['total'] : 0}}</span>
                <span v-if="n.title === 'Batches Pending'">
                  {{quick['pending'] ? quick['pending']['total'] : 0 }}</span>
                <span v-if="n.title === 'Batches Completed'">
                  {{quick['completed'] ? quick['completed']['total'] : 0}}</span>
                <span v-if="n.title === 'Batches Canceled'">
                  {{quick['canceled'] ? quick['canceled']['total'] : 0}}</span>
              </div>
              <div class="font-14 item-count mb-10">
                <span v-if="n.title === 'Batches Total'">
                  {{quick['total'] ? quick['total']['count'] : 0}}</span>
                <span v-if="n.title === 'Batches Pending'">
                  {{quick['pending'] ? quick['pending']['count'] : 0 }}</span>
                <span v-if="n.title === 'Batches Completed'">
                  {{quick['completed'] ? quick['completed']['count'] : 0 }}</span>
                <span v-if="n.title === 'Batches Canceled'">
                  {{quick['canceled'] ? quick['canceled']['count'] : 0 }}</span>
                {{n.sub}}
              </div>
              <div v-if="n.title !== 'Batches Total' "
                   class="font-14 item-count mb-10">
                <span v-if="n.title === 'Batches Pending'">
                  {{quick['pending'] ? quick['pending']['employees'] : 0}}</span>
                <span v-if="n.title === 'Batches Completed'">
                  {{quick['completed'] ? quick['completed']['employees'] : 0 }}</span>
                <span v-if="n.title === 'Batches Canceled'">
                  {{quick['canceled'] ? quick['canceled']['employees'] : 0}}</span>
                Employees
              </div>
              <div v-if="n.title !== 'Batches Total' "
                   class="font-14 item-count mb-10">
                <span class="font-mono"
                      v-if="n.title === 'Batches Pending'">
                  {{quick['pending'] ? quick['pending']['avgPayment'] : 0}}</span>
                <span class="font-mono"
                      v-if="n.title === 'Batches Completed'">
                  {{quick['completed'] ? quick['completed']['avgPayment'] : 0}}</span>
                <span class="font-mono"
                      v-if="n.title === 'Batches Canceled'">
                  {{quick['canceled'] ? quick['canceled']['avgPayment'] : 0}}</span> Avg payment
              </div>
            </q-card-main>
          </q-card>
        </div>
        <!-- <div class="col-sm-3">
          <q-card>
            <q-card-title>
              <span>Total Transactions</span>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy mb-10">{{ 0 | moneyFormat}}</div>
              <a href="/j/leaflink/payments"
                 class="link bold font-12">View Report <i class="mdi mdi-arrow-right"></i></a>
            </q-card-main>
          </q-card>
        </div> -->
      </div>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 label="Export as XLSX"
                 color="primary"
                 @click="download"
                 class="btn-sm mr-8 export-btn"
                 no-caps></q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10 font-18"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="font-18"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Batch Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Batch Status'])">
                {{ _.get(props.row, col.field) }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Batch ID'">
              <span class="font-12 view-btn"
                    @click="viewBatch(props.row)"> {{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else-if="col.field === 'File name'">
              <span class="font-12 view-btn"
                    @click="viewBatch(props.row)"> {{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else-if="['Member Load', 'Avg Load', 'Total Load'].includes(col.field)">
              <div class="mex-amount">{{ _.get(props.row, col.field)  }}</div>
            </template>
            <template v-else-if="['Member Count'].includes(col.field)">
              <div class="mex-amount">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-if="masterAdmin && props.row['hasDuplicatePayroll']"
                          v-close-overlay
                          @click.native="execute(props.row)">
                    <q-item-main>Execute Duplicated</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns, EventHandlerMixin, request, notify } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import ForceReloadMixin from '../../../../mixins/ForceReloadMixin'

export default {
  name: 'mex-payroll-batch-summary-report',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    ForceReloadMixin,
    EventHandlerMixin('reload-mex-payroll-batch-summary-report')
  ],
  components: {
  },
  data () {
    return {
      title: 'Employer Batch Payroll Summary Report',
      requestUrl: `/admin/mex/payroll/batch/list`,
      downloadUrl: `/admin/mex/payroll/batch/export`,
      filtersUrl: '/admin/mex/payroll/filters',
      columns: generateColumns([
        'Employer', 'Batch Create Date', 'Batch Send Date', 'Batch ID', 'File name', 'Uploaded By', 'Member Count',
        'Member Load', 'Avg Load', 'Total Load', 'Batch Status', 'Actions'
      ], ['Member Count', 'Member Load', 'Avg Load', 'Total Load']),
      filterOptions: [
        {
          value: 'filter[e.id=]',
          label: 'Employer',
          options: [],
          source: 'employers'
        },
        {
          value: 'filter[ipr.id]',
          label: 'Batch ID'
        }, {
          value: 'filter[ipr.fileName]',
          label: 'File Name'
        },
        {
          value: 'filter[ipr.postTime]',
          label: 'Batch Send Date',
          range: [{
            value: 'range[ipr.createdAt][start]',
            type: 'date'
          }, {
            value: 'range[ipr.createdAt][end]',
            type: 'date'
          }]
        },
        {
          value: 'filter[ipr.createdAt]',
          label: 'Batch Create Date',
          range: [{
            value: 'range[ipr.createdAt][start]',
            type: 'date'
          }, {
            value: 'range[ipr.createdAt][end]',
            type: 'date'
          }]
        },
        {
          value: 'filter[ipr.executeStatus]',
          label: 'Status',
          options: [
            { label: 'Canceled', value: 'canceled' },
            { label: 'Pending', value: 'pending' },
            { label: 'Completed', value: 'Completed' },
            { label: 'Inprogress', value: 'inprogress' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      keyword: '',
      numbers: [
        { title: 'Batches Total', sub: 'count', count: 0, value: 0, employee: 0, avg: 0 },
        { title: 'Batches Pending', sub: 'count', count: 0, value: 0, employee: 0, avg: 0 },
        { title: 'Batches Completed', sub: 'count', count: 0, value: 0, employee: 0, avg: 0 },
        { title: 'Batches Canceled', sub: 'count', count: 0, value: 0, employee: 0, avg: 0 }
      ]
    }
  },
  methods: {
    statusClass (status) {
      const cls = []
      cls.push({
        'Canceld': 'faded',
        'Inprogress': 'negative',
        'Pending': 'pending',
        'Completed': 'positive',
        'Importing': 'gray'
      }[status] || status)
      return cls
    },
    viewBatch (row) {
      window.open(`/admin#/j/mex/payroll_batch_report/detail/${row['Batch ID']}`, '_blank')
    },
    execute (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to execute the duplicated payments for the batch file?`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        // console.log('sadfgrewsdf')
        const resp = await request(`/admin/mex/payroll/batch/${row['Batch ID']}/execute`, 'post')
        // console.log('sadfgrewsdf12345421')
        this.$q.loading.hide()
        if (resp) {
          notify()
          this.reload()
        }
      }).catch(() => {})
    }
  }
}
</script>
<style lang="scss">
#mex__payroll_reports_batch__index_page {
  .q-chip.pending {
    color: #ff9f00;
    background-color: rgba($color: #ff9f00, $alpha: 0.1);
  }
  .view-btn {
    color: #0062ff;
    cursor: pointer;
    text-decoration: underline;
  }
}
</style>
