<template>
  <q-page id="mex__payroll_reports_detail__index_page">
    <div class="page-header">
      <div class="title mt-15">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3  col-xs-6"
             v-for="(n, i) in numbers"
             :key="i">
          <q-card>
            <q-card-title>
              <span>{{n.title}}</span>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy">
                <span class="font-mono" v-if="n.title === 'Total'">
                  {{quick['total'] ? quick['total']['total'] : 0}}</span>
                <span class="font-mono" v-if="n.title === 'Pending'">
                  {{quick['pending'] ? quick['pending']['total'] : 0 }}</span>
                <span class="font-mono" v-if="n.title === 'Completed'">
                  {{quick['completed'] ? quick['completed']['total'] : 0}}</span>
                <span class="font-mono" v-if="n.title === 'Canceled'">
                  {{quick['canceled'] ? quick['canceled']['total'] : 0}}</span>
              </div>
              <div class="font-14 item-count mb-10">
                <span class="font-mono" v-if="n.title === 'Total'">
                  {{quick['total'] ? quick['total']['count'] : 0}}</span>
                <span class="font-mono" v-if="n.title === 'Pending'">
                  {{quick['pending'] ? quick['pending']['count'] : 0 }}</span>
                <span class="font-mono" v-if="n.title === 'Completed'">
                  {{quick['completed'] ? quick['completed']['count'] : 0 }}</span>
                <span class="font-mono" v-if="n.title === 'Canceled'">
                  {{quick['canceled'] ? quick['canceled']['count'] : 0 }}</span>
                {{n.sub}}
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 label="Export as XLSX"
                 color="primary"
                 @click="download"
                 class="btn-sm mr-8 export-btn"
                 no-caps></q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10 font-18"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="font-18"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                <span v-if="props.row['Status'] === 'init'">Pending</span>
                <span v-else-if="props.row['Status'] === 'Executed'">Completed</span>
                <span v-else-if="props.row['Status'] === 'Reversed'">Reversed</span>
                <span v-else-if="props.row['Status'] === 'canceled'">Canceled</span>
                <span v-else-if="props.row['Status'] === 'Duplicated'">Duplicated</span>
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Member ID'">
              <span class="font-12 view-btn"
                    @click="view(props.row)"> {{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else-if="col.field === 'Load Amount'">
              <span class="mex-amount">{{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns, EventHandlerMixin } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import ForceReloadMixin from '../../../../mixins/ForceReloadMixin'

export default {
  name: 'mex-payroll-batch-summary-report',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    ForceReloadMixin,
    EventHandlerMixin('reload-mex-payroll-batch-summary-report')
  ],
  components: {
  },
  data () {
    return {
      title: 'Employer Payroll Detail Report',
      requestUrl: `/admin/mex/payroll_detail/list`,
      downloadUrl: `/admin/mex/payroll_detail/export`,
      filtersUrl: '/admin/mex/payroll/filters',
      columns: generateColumns([
        'Transaction ID', 'Employer', 'Create Date', 'Send Date', 'Member ID',
        'Email', 'First Name', 'Last Name', 'External Employee ID', 'Load Amount', 'Status'
      ], ['Load Amount']),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'Employer',
          options: [],
          source: 'employers'
        },
        {
          value: 'email',
          label: 'Email'
        },
        {
          value: 'firstName',
          label: 'First Name'
        },
        {
          value: 'lastName',
          label: 'Last Name'
        },
        {
          value: 'filter[uct.txnTime]',
          label: 'Send Date',
          range: [{
            value: 'range[uct.txnTime][start]',
            type: 'date'
          }, {
            value: 'range[uct.txnTime][end]',
            type: 'date'
          }]
        },
        {
          value: 'filter[uct.createdAt]',
          label: 'Create Date',
          range: [{
            value: 'range[uct.createdAt][start]',
            type: 'date'
          }, {
            value: 'range[uct.createdAt][end]',
            type: 'date'
          }]
        },
        {
          value: 'accountStatus',
          label: 'Status',
          options: [
            { label: 'Canceled', value: 'canceled' },
            { label: 'Pending', value: 'init' },
            { label: 'Completed', value: 'Executed' },
            { label: 'Reversed', value: 'Reversed' },
            { label: 'Duplicated', value: 'Duplicated' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      keyword: '',
      numbers: [
        { title: 'Total', sub: 'count', count: 0, value: 0, employee: 0, avg: 0 },
        { title: 'Pending', sub: 'count', count: 0, value: 0, employee: 0, avg: 0 },
        { title: 'Completed', sub: 'count', count: 0, value: 0, employee: 0, avg: 0 },
        { title: 'Canceled', sub: 'count', count: 0, value: 0, employee: 0, avg: 0 }
      ]
    }
  },
  methods: {
    statusClass (status) {
      const cls = []
      cls.push({
        'Reversed': 'negative',
        'init': 'pending',
        'Executed': 'positive',
        'Duplicated': 'negative'
      }[status] || status)
      return cls
    },
    view (row) {
      window.open(`/admin#/j/mex/members/${row['Member ID']}`, '_blank')
    }
  }
}
</script>
<style lang="scss">
#mex__payroll_reports_detail__index_page {
  .q-chip.pending {
    color: #ff9f00;
    background-color: rgba($color: #ff9f00, $alpha: 0.1);
  }
  .view-btn {
    color: #0062ff;
    cursor: pointer;
    text-decoration: underline;
  }
}
</style>
