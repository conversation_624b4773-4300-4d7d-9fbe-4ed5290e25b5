<template>
  <q-page id="mex__program__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row">
        <div class="col-sm-12">
          <ProgramBalance :viewReportFlag="false"
                          v-model="chartDateRange"></ProgramBalance>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Program History</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-refresh"
                 color="orange"
                 label="Update Now"
                 @click="forceReload"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field === 'Amount'">
              <span v-if="props.row['Amount'] >= 0"
                    class="amount-item mex-amount">{{ _.get(props.row, col.field) | moneyFormat}}</span>
              <span v-else
                    class="reduce-amount-item mex-amount">{{ _.get(props.row, col.field) | moneyFormat}}</span>
            </template>
            <template v-else-if="col.field === 'Running Balance'">
              <span class="mex-amount">{{ _.get(props.row, col.field) | moneyFormat}}</span>
            </template>
            <template v-else-if="col.field === 'Member ID'">
              <span @click="goToMember(props.row)"
                    class="account-id">{{ _.get(props.row, col.field) }}</span>
            </template>
            <template v-else-if="col.field === 'Status'">
              <q-chip v-if="props.row['Status'] === 'Completed'"
                      class="font-12 status-item positive">
                {{ props.row['Status'] }}
              </q-chip>
              <q-chip v-else
                      class="font-12 status-item blue">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import ProgramBalance from '../common/programBalance'
import MexPageMixin from '../../../mixins/mex/MexPageMixin'
import ForceReloadMixin from '../../../mixins/ForceReloadMixin'

export default {
  name: 'cow-funding',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    ForceReloadMixin,
    EventHandlerMixin('reload-cow-funding')
  ],
  components: {
    ProgramBalance
  },
  data () {
    return {
      title: 'Program',
      requestUrl: `/admin/mex/program/list`,
      downloadUrl: `/admin/mex/program/export`,
      keyword: '',
      dateRange: 'all',
      chartDateRange: 'all',
      columns: generateColumns([
        'Transaction Type', 'Date & time', 'First Name', 'Last Name', 'Member ID', 'Transaction ID',
        'Amount', 'Running Balance', 'Status'
      ], ['Amount', 'Running Balance']),
      filterOptions: [
        {
          value: 'tranCode',
          label: 'Transaction Type',
          options: [
            { label: 'Credit', value: 'CREDIT' },
            { label: 'Debit', value: 'DEBIT' },
            { label: 'Transfer', value: 'transfer' }
          ]
        },
        {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        },
        {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        },
        {
          value: 'filter[u.id=]',
          label: 'Memeber ID'
        },
        {
          value: 'date_range',
          label: 'Date & Time',
          range: [{
            value: 'range[uct.txnTime][start]',
            type: 'date'
          }, {
            value: 'range[uct.txnTime][end]',
            type: 'date'
          }]
        }
      ],
      autoLoad: true
    }
  },
  methods: {
    async reload () {
      let data = {}
      if (this.$refs && this.$refs.dateRangeFilter) {
        data = this.$refs.dateRangeFilter.params()
      }
      this.chartDateRange = data.period
      await this.request({
        pagination: this.pagination
      })
      this.everLoaded = true
    },
    goToMember (row) {
      const { href } = this.$router.resolve({
        path: `/j/mex/members/${row['Member Id']}`
      })
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss">
#mex__program__index_page {
  .account-id {
    color: #7ac142;
    text-decoration-color: #7ac142;
    text-decoration-line: underline;
    cursor: pointer;
  }
}
</style>
