<template>
  <q-dialog
    class="mex-revenue-config-dialog"
    v-model="visible"
  >
    <template slot="title">
      <div class="font-16 mb-2">Configuration</div>
      <div class="font-12 normal">Ask Rapid for the details of these parameters</div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <q-field label="Account Number (Core)" class="mt-0" :label-width="4">
        <q-input v-model="entity.AccountNumberCore"></q-input>
      </q-field>
      <q-field label="User ID" class="mt-15" :label-width="4">
        <q-input v-model="entity.UserID"></q-input>
      </q-field>
      <q-field label="Card ID" class="mt-15" :label-width="4">
        <q-input v-model="entity.CardID"></q-input>
      </q-field>
      <q-field label="Account Number" class="mt-15" :label-width="4">
        <q-input v-model="entity.AccountNumber"></q-input>
      </q-field>
      <q-field label="Bank Account ID" class="mt-15" :label-width="4">
        <q-input v-model="entity.BankAccountID"></q-input>
      </q-field>
      <div class="flex justify-center mt-15">
        <q-btn label="Save" no-caps
               class="min-w-300"
               @click="submit"
               color="primary"></q-btn>
      </div>

      <div class="q-body-1 text-left mb-10 text-grey mt-30">Changelog:</div>
      <q-table :data="logs"
               :columns="columns"
               notranslate=""
               class="clean">
        <q-tr slot="body" slot-scope="props" :props="props">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="col.field === 'time'">
              {{ props.row[col.field] | date('L LT') }}
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>
      </q-table>
    </template>
    <template slot="buttons">
      <q-btn label="Close" no-caps
             color="blue" class="max-w-300"
             text-color="white"
             @click="_hide" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, request } from '../../../common'

export default {
  name: 'mex-revenue-config-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      type: null,
      entity: {
        AccountNumberCore: null,
        UserID: null,
        CardID: null,
        AccountNumber: null,
        BankAccountID: null
      },
      logs: [],
      columns: [
        { field: 'time', label: 'Date & Time', align: 'left' },
        { field: 'uname', label: 'User', align: 'left' },
        { field: 'detail', label: 'Details', align: 'left' }
      ]
    }
  },
  methods: {
    async show (arg) {
      this.type = arg
      this.visible = false
      await this.init()
      this.visible = true
    },
    async init () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/revenue/${this.type}/config`, 'get', {
        token: this.entity.token
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.logs = resp.data.logs
        delete resp.data.logs

        this.entity = resp.data
      }
    },
    async submit () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/revenue/${this.type}/save-config`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this._hide()
        notify(resp)
      }
    }
  }
}
</script>

<style lang="scss">
.mex-revenue-config-dialog {
  .modal-content {
    width: 600px;
  }
}
</style>
