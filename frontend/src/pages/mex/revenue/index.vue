<template>
  <q-page id="mex__transfer_revenue__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row">
        <div class="col" v-if="masterAdmin">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-alpha-r-box" color="orange"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super nowrap">
                        Amount
                      </small>
                      <div class="value font-mono">{{ quick.rapidBalance | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">
                    Rapid Program Balance
                    <q-icon name="mdi-refresh" color="positive" @click.native="refreshRapid"
                            class="ml-5 pointer">
                      <q-tooltip>Refresh</q-tooltip>
                    </q-icon>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col" v-if="masterAdmin">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-alpha-r-box" color="orange"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super nowrap">
                        Amount
                      </small>
                      <div class="value font-mono">{{ quick.botmBalance | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">
                    BOTM Program Balance
                    <q-icon name="mdi-refresh" color="positive" @click.native="refreshBotm"
                            class="ml-5 pointer">
                      <q-tooltip>Refresh</q-tooltip>
                    </q-icon>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col" v-if="masterAdmin && type === 'total'">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="blue"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super">Amount</small>
                      <div class="value font-mono">{{ quick.revenue || 0 | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">
                    <span>Partner Transfer Revenues</span>
                    <template v-if="masterAdmin">
                      <q-icon name="mdi-help-circle-outline" class="ml-2 help"></q-icon>
                      <q-tooltip>Total amount of <code>Partner Gross Revenue</code> in all the periods</q-tooltip>
                    </template>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col" v-if="platformAccess">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="blue"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super">Amount</small>
                      <div class="value font-mono">{{ quick.ternRevenue || 0 | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">
                    <span>Platform Transfer Revenues</span>
                    <template v-if="masterAdmin">
                      <q-icon name="mdi-help-circle-outline" class="ml-2 help"></q-icon>
                      <q-tooltip>Total amount of <code>Platform Gross Revenue</code> in all the periods</q-tooltip>
                    </template>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col" v-if="platformAccess">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super">Amount</small>
                      <div class="value font-mono">{{ quick.platformRevenue || 0 | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">
                    <span>Total FX Processing Fee</span>
                    <template v-if="masterAdmin">
                      <q-icon name="mdi-help-circle-outline" class="ml-2 help"></q-icon>
                      <q-tooltip>Total amount of <code>FX Income</code> in all the periods</q-tooltip>
                    </template>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super">Cost Amount</small>
                      <div class="value font-mono">{{ quick.cost || 0 | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">
                    <span>Cost (KYC, Fee Credits, etc.)</span>
                    <template v-if="masterAdmin">
                      <q-icon name="mdi-help-circle-outline" class="ml-2 help"></q-icon>
                      <q-tooltip><code>Fee Credits</code> + <code>KYC Costs</code> + <code>Other Cost</code></q-tooltip>
                    </template>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col" v-if="masterAdmin">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="orange"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super">Amount</small>
                      <div class="value font-mono">{{ quick.totalProfit || 0 | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">
                    <span>Total Net Revenue</span>
                    <template v-if="masterAdmin">
                      <q-icon name="mdi-help-circle-outline" class="ml-2 help"></q-icon>
                      <q-tooltip><code>Partner Transfer Revenues</code> + <code>Platform Transfer Revenues</code> + <code>Total FX Processing Fee</code> - <code>Cost</code></q-tooltip>
                    </template>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col" v-if="masterAdmin">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="purple"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super">Amount</small>
                      <div class="value font-mono">{{ quick.revenueToSettle || 0 | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">
                    <span>Available Revenue can settle</span>
                    <template v-if="masterAdmin">
                      <q-icon name="mdi-help-circle-outline" class="ml-2 help"></q-icon>
                      <q-tooltip><code>Total Net Revenue</code> - <code>Completed Deposit Total</code></q-tooltip>
                    </template>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-comment-arrow-right-outline"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-12">
                      <small class="super">Count</small>
                      <div class="value font-mono">{{ quick.cnt || 0 }}</div>
                    </div>
                  </div>
                  <div class="description">
                    <span>Completed Deposits</span>
                    <template v-if="masterAdmin">
                      <q-icon name="mdi-help-circle-outline" class="ml-2 help"></q-icon>
                      <q-tooltip>Total amount of the completed settlements</q-tooltip>
                    </template>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col">
          <q-card class="top-statics columns">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-credit-card-multiple" color="positive"></q-icon>
                <div class="column col">
                  <div class="row values">
                    <div class="col-4">
                      <small class="super">Amount</small>
                      <div class="value font-mono">{{ quick.amount | moneyFormat }}</div>
                    </div>
                  </div>
                  <div class="description">Completed Deposit Total</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn icon="mdi-flash"
                 color="primary"
                 label="Add Settlement"
                 v-if="platformAccess"
                 @click="createSettlement"
                 class="btn-sm mr-8" no-caps></q-btn>
          <q-btn icon="mdi-tune" color="warning"
                 label="Config"
                 @click="configure"
                 v-if="platformAccess && type !== 'total' && false"
                 class="btn-sm mr-8" no-caps></q-btn>
          <q-btn icon="mdi-file-download-outline" color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8" no-caps></q-btn>

          <q-search icon="search" v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat round dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating class="dot" v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat round dense
                 color="faded"
                 class="mr-10"
                 @click="reload"
                 icon="refresh"/>
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body" slot-scope="props" :props="props" :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols" :key="col.field" :align="col.align"
                :style="columnStyles[i]" :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Amount'">
              <div class="mex-amount">{{ props.row[col.field] }}</div>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm" no-caps
                              v-if="masterAdmin"
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay @click.native="send(props.row)" v-if="['Created', 'Funding', 'Transferring'].includes(props.row['Status'])">
                    <q-item-main>Send</q-item-main>
                  </q-item>
                  <q-item v-close-overlay @click.native="updateStatus(props.row, 'Canceled')" v-if="['Created'].includes(props.row['Status'])">
                    <q-item-main>Cancel</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <PromptDialog></PromptDialog>
    <ConfigDialog v-if="masterAdmin"></ConfigDialog>
    <SendDialog v-if="masterAdmin"></SendDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notify, request, toSelectOptions } from '../../../common'
import MexPageMixin from '../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import MexProcessorMixin from '../../../mixins/mex/MexProcessorMixin'
import PromptDialog from '../../../components/PromptDialog'
import ConfigDialog from './config'
import SendDialog from './send'
import _ from 'lodash'

export default {
  name: 'mex-revenue',
  mixins: [
    MexPageMixin,
    MexProcessorMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-revenue')
  ],
  components: {
    PromptDialog,
    ConfigDialog,
    SendDialog
  },
  data () {
    const type = this.$route.params.type
    return {
      type,
      title: 'Revenue',
      requestUrl: `/admin/mex/revenue/${type}/list`,
      downloadUrl: `/admin/mex/revenue/${type}/export`,
      columns: generateColumns([
        'ID',
        'Date Sent',
        'Amount',
        'Target',
        'Notes',
        'User',
        'Status',
        'Action'
      ], ['Amount']),
      filterOptions: [
        {
          value: 'filter[t.id=]',
          label: 'ID'
        }, {
          value: 'depositDate',
          label: 'Date Sent',
          range: [
            {
              value: 'range[t.depositDate][start]',
              type: 'date'
            }, {
              value: 'range[t.depositDate][end]',
              type: 'date'
            }
          ]
        }, {
          value: 'filter[t.status=]',
          label: 'Status',
          options: toSelectOptions([
            'Pending', 'Completed'
          ])
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true
    }
  },
  computed: {
    statColClass () {
      return this.masterAdmin ? 'col-sm-2' : 'col-sm-2-5'
    },
    platformAccess () {
      return this.masterAdmin && ['total', 'platform'].includes(this.type)
    }
  },
  watch: {
    type () {
      this.reload()
    }
  },
  methods: {
    init () {
      this.title = _.startCase(this.type) + ' Revenue'
    },
    initData (route) {
      this.type = route.params.type
      this.requestUrl = `/admin/mex/revenue/${this.type}/list`
      this.downloadUrl = `/admin/mex/revenue/${this.type}/export`
      this.init()
    },
    statusClass (status) {
      return {
        'Created': 'dark',
        'Sent': 'blue',
        'Updated': 'orange',
        'Confirmed': 'positive',
        'Completed Orphan': 'positive',
        'Canceled': 'purple'
      }[status] || status
    },
    async send (row) {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/revenue/${this.type}/send/${row['ID']}`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
        notify(resp)
      }
    },
    async updateStatus (row, status, other = {}) {
      this.$q.dialog({
        title: `Update status`,
        message: `Are you sure that you want to change this settlement's status to "${status}"?`,
        cancel: true,
        color: 'negative'
      }).then(async () => {
        this.$q.loading.show()
        other.status = status
        const resp = await request(`/admin/mex/revenue/${this.type}/update-status/${row['ID']}`, 'post', other)
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
          notify(resp)
        }
      }).catch(() => {})
    },
    createSettlement () {
      this.$root.$emit('show-mex-revenue-send-dialog', {
        type: this.type,
        max: this.quick.revenueToSettle
      })
    },
    configure () {
      this.$root.$emit('show-mex-revenue-config-dialog', this.type)
    }
  },
  beforeRouteUpdate (to, from, next) {
    this.initData(to)
    next()
  },
  mounted () {
    this.initData(this.$route)
  }
}
</script>
