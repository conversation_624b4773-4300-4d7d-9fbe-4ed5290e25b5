<template>
  <q-dialog
    class="mex-revenue-send-dialog"
    v-model="visible"
  >
    <template slot="title">
      <div class="font-16 mb-2">Add Settlement</div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <q-field label="Settled Amount"
               class="mb-15"
               :helper="'Not greater than the available revenue ' + c.moneyFormat(max)"
               :label-width="12">
        <q-input type="number" v-model="amount"
                 prefix="$" :min="0.01" :max="max"></q-input>
      </q-field>
      <q-field label="Note"
               class="mb-15"
               :label-width="12">
        <q-input v-model="note" placeholder="Optional"></q-input>
      </q-field>
    </template>
    <template slot="buttons">
      <q-btn label="Send" no-caps
             @click="submit"
             color="primary"></q-btn>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { moneyMinorAmount, notify, notifyResponse, request } from '../../../common'

export default {
  name: 'mex-revenue-send-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      type: null,
      max: 0,
      amount: null,
      note: null
    }
  },
  computed: {
    targetName () {
      if (this.type === 'partner') {
        return 'Partner (TransferMex)'
      }
      if (this.type === 'platform' || this.type === 'total') {
        return 'Platform (Tern)'
      }
      return 'Unknown'
    }
  },
  methods: {
    show (arg) {
      this.type = arg.type
      this.max = arg.max
      this.amount = null
      this.note = null
    },
    submit () {
      let amount = this.amount
      if (!amount || amount <= 0) {
        notifyResponse('Please enter the amount!')
        return
      }
      amount = moneyMinorAmount(amount)
      if (amount > this.max) {
        notifyResponse('The amount cannot be greater than the available revenue!')
        return
      }

      let msg = `Are you sure that you want to add a settlement record to "${this.targetName}" with the amount "$${this.amount}"`
      if (this.note) {
        msg += ` and the note "${this.note}"`
      }
      msg += `?`

      this.$q.dialog({
        title: 'Confirm',
        message: msg,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/revenue/${this.type}/add`, 'post', {
          amount: this.amount,
          note: this.note
        })
        this.$q.loading.hide()
        if (resp.success) {
          this._hide()
          notify(resp)
          this.$root.$emit('reload-mex-revenue')
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
.mex-revenue-send-dialog {
  .modal-content {
    width: 500px;
  }
}
</style>
