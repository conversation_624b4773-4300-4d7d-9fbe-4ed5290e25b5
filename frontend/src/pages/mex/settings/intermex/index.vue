<template>
  <q-page id="mex__intermex_payers__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3">
          <q-card class="top-statics col-12">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-comment-arrow-right-outline"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.total || 0 }}</div>
                  <div class="description">Number of Payers</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Intermex Branch Report</strong>
        </template>
        <template slot="top-right">
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-if="props.row['Status'] == 'Inactive'"
                          v-close-overlay
                          @click.native="changeStatus(props.row)">
                    <q-item-main>Enable</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Status'] == 'Active'"
                          v-close-overlay
                          @click.native="changeStatus(props.row)">
                    <q-item-main>Disable</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notifySuccess, request, toSelectOptions } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'

export default {
  name: 'mex-settings-intermex-payers',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-settings-intermex-payers')
  ],
  components: {
  },
  data () {
    return {
      title: 'Intermex Payers',
      requestUrl: `/admin/mex/settings/branches/list`,
      columns: generateColumns([
        'ID', 'Partner', 'Payer Name', 'Payer ID', 'Type', 'Branch ID', 'Branch Name', 'Country', 'State', 'City', 'Address', 'Status', 'Action'
      ]),
      filterOptions: [
        {
          value: 'filter[b.id=]',
          label: 'ID'
        }, {
          value: 'filter[b.payerName]',
          label: 'Payer Name'
        }, {
          value: 'filter[b.city]',
          label: 'City'
        }, {
          value: 'filter[b.type=]',
          label: 'Type',
          options: [
            {
              label: 'Bank Deposit',
              value: 3
            },
            {
              label: 'Cash Pickup',
              value: 1
            }
          ]
        }, {
          value: 'filter[p.status=]',
          label: 'Status',
          options: toSelectOptions([
            'active', 'inactive'
          ], true)
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true
    }
  },
  methods: {
    changeStatus (row) {
      let type = 'enable'
      if (row['Status'] === 'Active') {
        type = 'disable'
      }
      this.$q.dialog({
        title: `Confirm`,
        message: 'Are you sure that you want to ' + type + ' this payer? ',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/settings/branch/${row['ID']}/${type}`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
          notifySuccess(resp)
        }
      }).catch(() => {})
    },
    statusClass (status) {
      return {
        'Active': 'positive',
        'Inactive': 'negative'
      }[status] || status
    }
  }
}
</script>
