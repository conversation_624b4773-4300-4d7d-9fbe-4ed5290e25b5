<template>
  <q-dialog class="mex-location-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">{{ edit ? 'Edit Location' : 'Create New Location' }}</div>
      <div class="font-12 normal text-dark">Please fill in the information below about the location.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-input float-label="Payout Business Name"
                   autocomplete="no"
                   :error="$v.entity['Payout Business Name'].$error"
                   @blur="$v.entity['Payout Business Name'].$touch"
                   v-model="entity['Payout Business Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Street"
                   autocomplete="no"
                   :error="$v.entity['Street'].$error"
                   @blur="$v.entity['Street'].$touch"
                   v-model="entity['Street']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="City"
                   autocomplete="no"
                   :error="$v.entity['City'].$error"
                   @blur="$v.entity['City'].$touch"
                   v-model="entity['City']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-select float-label="Country"
                    autocomplete="no"
                    readonly
                    :options="countries"
                    filter
                    autofocus-filter
                    :error="$v.entity['CountryId'].$error"
                    @blur="$v.entity['CountryId'].$touch"
                    v-model="entity['CountryId']"></q-select>
        </div>
        <div class="col-sm-6">
          <q-select float-label="State / Province"
                    autocomplete="no"
                    :options="states"
                    filter
                    autofocus-filter
                    :error="$v.entity['StateId'].$error"
                    :before="stateBefore"
                    @blur="$v.entity['StateId'].$touch"
                    v-model="entity['StateId']"></q-select>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Zip Code"
                   autocomplete="no"
                   :error="$v.entity['Zip Code'].$error"
                   @blur="$v.entity['Zip Code'].$touch"
                   v-model="entity['Zip Code']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-select float-label="Payer"
                    autocomplete="no"
                    :options="payers"
                    filter
                    autofocus-filter
                    :error="$v.entity['Payer'].$error"
                    @blur="$v.entity['Payer'].$touch"
                    v-model="entity['Payer']"></q-select>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Latitude"
                   autocomplete="no"
                   type="number"
                   :error="$v.entity['Latitude'].$error"
                   @blur="$v.entity['Latitude'].$touch"
                   v-model="entity['Latitude']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Longitude"
                   autocomplete="no"
                   type="number"
                   :error="$v.entity['Longitude'].$error"
                   @blur="$v.entity['Longitude'].$touch"
                   v-model="entity['Longitude']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Exterior #"
                   autocomplete="no"
                   v-model="entity['Exterior #']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Interior #"
                   autocomplete="no"
                   v-model="entity['Interior #']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Colonia"
                   autocomplete="no"
                   v-model="entity['Colonia']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Phone Number"
                   autocomplete="no"
                   v-model="entity['Phone Number']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Business Hours"
                   autocomplete="no"
                   v-model="entity['Business Hours']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Business Hours Saturday & Sunday"
                   autocomplete="no"
                   v-model="entity['Business Hours Saturday & Sunday']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Payment Limit MXN"
                   autocomplete="no"
                   type="number"
                   prefix="$"
                   v-model="entity['Payment Limit MXN Number']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Payment Limit USD"
                   autocomplete="no"
                   type="number"
                   prefix="$"
                   v-model="entity['Payment Limit USD Number']"></q-input>
        </div>
        <div class="col-sm-12 pt-20"
             v-show="edit">
          <div class="bold">Status</div>
          <div class="mt-8">
            <q-radio v-model="entity['Status']"
                     color="blue"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Active"
                     label="Active"></q-radio>
            <q-radio v-model="entity['Status']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Inactive"
                     label="Inactive"></q-radio>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel"
             no-caps
             color="grey-3"
             text-color="tertiary"
             @click="_hide" />
      <q-btn :label="edit ? 'Save Changes' : 'Create Location'"
             no-caps
             color="positive"
             class="main"
             @click="save" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notifyForm, notify, request, toSelectOptions } from '../../../../common'
import { required } from 'vuelidate/lib/validators'
import _ from 'lodash'
import MexStateListMixin from '../../../../mixins/mex/MexStateListMixin'

const validations = {
  entity: {}
}
for (const field of [
  'Payout Business Name', 'City', 'CountryId', 'StateId',
  'Zip Code', 'Street', 'Payer',
  'Latitude', 'Longitude', 'Status'
]) {
  validations.entity[field] = { required }
}

export default {
  name: 'mex-settings-locations-detail-dialog',
  mixins: [
    Singleton,
    MexStateListMixin
  ],
  data () {
    return {
      defaultEntity: {
        'CountryId': null,
        'Status': 'Active'
      },
      payers: toSelectOptions([
        'CHEDRAUI',
        'ENVIOS DELGADO',
        'FARMAPRONTO',
        'INPAMEX',
        'ISSEG',
        'MERZA',
        'WILLYS'
      ])
    }
  },
  computed: {
    edit () {
      return this.entity['ID']
    }
  },
  validations,
  methods: {
    _preShow () {
      let countryId = null
      if (this.countries && this.countries.length) {
        countryId = this.countries[0].value
      }
      this.defaultEntity.CountryId = countryId
    },
    async show () {
      const resp = await request(`/admin/mex/locations/payers`, 'get', this.entity)
      if (resp.success) {
        this.payers = resp.data
      }
    },
    onSuccess (resp) {
      notify(resp.message)

      if (this.origin && this.origin['ID']) {
        _.assignIn(this.origin, resp.data)
      }

      this.$root.$emit('reload-mex-settings-locations')
      this._hide()
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      const resp = await request(`/admin/mex/settings/locations/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    }
  }
}
</script>

<style lang="scss">
.mex-location-detail-dialog {
  .modal-content {
    width: 580px;
  }
}
</style>
