<template>
  <q-page id="mex__locations__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3">
          <q-card class="top-statics col-12">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-comment-arrow-right-outline"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.total || 0 }}</div>
                  <div class="description">Number of Locations</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <p class="import-info"
         v-if="quick.lastImport && quick.lastImport.createdAt">
        Last Import Locatons: {{ quick.lastImport.createdAt}}
        <span class="success">Status: {{quick.lastImport.status }} </span>
        <span class="success">Success: {{ quick.lastImport.success}}</span>
        <span class="failed"> Failed: {{ quick.lastImport.fail}}</span>
        <a @click="viewErrors(quick.lastImport)"
           v-if="quick.lastImport.fail">Detail</a>
      </p>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Locations Report</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-account-plus-outline"
                 color="positive"
                 label="Create New Location"
                 @click="add"
                 class="btn-sm mr-10"
                 no-caps></q-btn>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-if="props.row['Status'] == 'Inactive'"
                          v-close-overlay
                          @click.native="changeStatus(props.row)">
                    <q-item-main>Enable</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Status'] == 'Active'"
                          v-close-overlay
                          @click.native="changeStatus(props.row)">
                    <q-item-main>Disable</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="remove(props.row)">
                    <q-item-main>Delete</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else-if="['Payment Limit USD', 'Payment Limit MXN'].includes(col.field)">
              <div class="mex-amount">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

    <DetailDialog></DetailDialog>
    <ImportDialog></ImportDialog>
    <ImportLocationError></ImportLocationError>
    <UpdateDialog></UpdateDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notifySuccess, request, toSelectOptions } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import DetailDialog from './detail'
import ImportDialog from './import'
import ImportLocationError from './importLocationError'
import UpdateDialog from './updateLocation'

export default {
  name: 'mex-settings-locations',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-settings-locations')
  ],
  components: {
    DetailDialog,
    ImportDialog,
    ImportLocationError,
    UpdateDialog
  },
  data () {
    return {
      title: 'Locations',
      filtersUrl: `/admin/mex/settings/locations/filters`,
      requestUrl: `/admin/mex/settings/locations/list`,
      downloadUrl: `/admin/mex/settings/locations/export`,
      columns: generateColumns([
        'ID', 'Partner', 'Payout Business Name', 'City', 'State', 'Country',
        'Zip Code', 'Street', 'Exterior #', 'Interior #',
        'Colonia', 'Phone Number', 'Business Hours', 'Business Hours Saturday & Sunday',
        'Payment Limit MXN', 'Payment Limit USD', 'Payer',
        'Latitude', 'Longitude', 'Status', 'Action'
      ], ['Payment Limit USD', 'Payment Limit MXN']),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'ID'
        }, {
          value: 'filter[u.partner]',
          label: 'Partner',
          options: [
            { label: 'Rapyd', value: 'Rapyd' },
            { label: 'UniTeller', value: 'UniTeller' }
          ]
        }, {
          value: 'filter[u.name]',
          label: 'Payout Business Name'
        }, {
          value: 'filter[u.city]',
          label: 'City'
        }, {
          value: 'filter[u.zip]',
          label: 'Zip Code'
        }, {
          value: 'filter[u.street]',
          label: 'Street'
        }, {
          value: 'filter[u.exterior]',
          label: 'Exterior #'
        }, {
          value: 'filter[u.interior]',
          label: 'Interior #'
        }, {
          value: 'filter[u.suburb]',
          label: 'Colonia'
        }, {
          value: 'filter[u.phone]',
          label: 'Phone Number'
        }, {
          value: 'filter[u.payer]',
          label: 'Payer'
        }, {
          value: 'filter[u.status=]',
          label: 'Status',
          options: toSelectOptions([
            'active', 'inactive'
          ], true)
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    changeStatus (row) {
      let type = 'enable'
      if (row['Status'] === 'Active') {
        type = 'disable'
      }
      this.$q.dialog({
        title: `Confirm`,
        message: 'Are you sure that you want to ' + type + ' this location? ',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/settings/locations/${row['ID']}/${type}`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
          notifySuccess(resp)
        }
      }).catch(() => {})
    },
    edit (row) {
      this.$root.$emit('show-mex-settings-locations-detail-dialog', row)
    },
    add () {
      this.$root.$emit('show-mex-location-import-dialog')
    },
    update () {
      console.log(this.$store.state.User)
      this.$root.$emit('show-mex-location-update-dialog')
    },
    viewErrors (row) {
      this.$root.$emit('show-mex-import-location-error-dialog', row)
    },
    statusClass (status) {
      return {
        'Active': 'positive',
        'Inactive': 'negative'
      }[status] || status
    },
    async remove (row) {
      this.$q.dialog({
        title: `Confirm`,
        message: 'Are you sure that you want to delete this location? ',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/settings/locations/${row['ID']}/del`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
          notifySuccess(resp)
        }
      }).catch(() => {})
    }
  }
}
</script>
