<template>
  <q-dialog class="mex-notify-email-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">{{ 'Add Notification Email Recipient' }}</div>
      <div class="font-12 normal text-dark">Please fill in the email you want to notify.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-input float-label="Name"
                   autocomplete="no"
                   v-model="entity['Name']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Email"
                   autocomplete="no"
                   v-model="entity['Email']"></q-input>
        </div>
        <div class="col-sm-12 pt-20" v-show="edit">
          <div class="bold">Status</div>
          <div class="mt-8">
            <q-radio v-model="entity['Status']" color="blue"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Active" label="Active"></q-radio>
            <q-radio v-model="entity['Status']" color="blue" class="ml-15"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Inactive" label="Inactive"></q-radio>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel"
             no-caps
             color="grey-3"
             text-color="tertiary"
             @click="_hide" />
      <q-btn :label="'Save'"
             no-caps
             color="positive"
             class="main"
             @click="save" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notifyForm, notify, request } from '../../../../common'
import { required, email } from 'vuelidate/lib/validators'
import _ from 'lodash'
import MexStateListMixin from '../../../../mixins/mex/MexStateListMixin'

const validations = {
  entity: {
    'Name': { required },
    'Email': { required, email },
    'Status': { required }
  }
}

export default {
  name: 'mex-settings-notify-email-detail-dialog',
  mixins: [
    Singleton,
    MexStateListMixin
  ],
  data () {
    return {
      defaultEntity: {
        'Name': null,
        'Email': null,
        'Status': 'Active'
      }
    }
  },
  computed: {
    edit () {
      return this.entity['ID']
    }
  },
  validations,
  methods: {
    onSuccess (resp) {
      notify(resp.message)

      if (this.origin && this.origin['ID']) {
        _.assignIn(this.origin, resp.data)
      }

      this.$root.$emit('reload-mex-settings-notify-email')
      this._hide()
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      const resp = await request(`/admin/mex/settings/notify-email/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    }
  }
}
</script>

<style lang="scss">
.mex-notify-email-detail-dialog {
  .modal-content {
    width: 580px;
  }
}
</style>
