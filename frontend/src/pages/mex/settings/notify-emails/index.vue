<template>
  <q-page id="mex__locations__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3">
          <q-card class="top-statics col-12">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-comment-arrow-right-outline"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.total || 0 }}</div>
                  <div class="description">Number of Notify Emails</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Recipients Report</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-account-plus-outline"
                 color="positive"
                 label="Add New Email"
                 @click="add"
                 class="btn-sm mr-10"
                 no-caps></q-btn>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="remove(props.row)">
                    <q-item-main>Delete</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

    <DetailDialog></DetailDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notifySuccess, request } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import DetailDialog from './detail'

export default {
  name: 'mex-settings-notify-email',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-settings-notify-email')
  ],
  components: {
    DetailDialog
  },
  data () {
    return {
      title: 'Notification Email Recipients',
      requestUrl: `/admin/mex/settings/notify-email/list`,
      downloadUrl: `/admin/mex/settings/notify-email/export`,
      columns: generateColumns([
        'ID', 'Name', 'Email', 'Status', 'Action'
      ]),
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    statusClass (status) {
      return {
        'Active': 'positive',
        'Inactive': 'negative'
      }[status] || status
    },
    add () {
      this.$root.$emit('show-mex-settings-notify-email-detail-dialog')
    },
    edit (row) {
      this.$root.$emit('show-mex-settings-notify-email-detail-dialog', row)
    },
    async remove (row) {
      this.$q.dialog({
        title: `Confirm`,
        message: 'Are you sure that you want to delete this email? ',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/settings/notify-email/${row['ID']}/del`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
          notifySuccess(resp)
        }
      }).catch(() => {})
    }
  }
}
</script>
