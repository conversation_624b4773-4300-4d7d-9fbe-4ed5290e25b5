<template>
  <q-dialog class="mex-uniteller-payer-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">{{ edit ? 'Edit UniTeller Payer' : 'Add New UniTeller Payer' }}</div>
      <div class="font-12 normal text-dark">Please fill in the information below about the UniTeller Payer.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-input float-label="Name"
                   autocomplete="no"
                   :error="$v.entity['Name'].$error"
                   @input="$v.entity['Name'].$touch"
                   v-model="entity['Name']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Payer Code"
                   autocomplete="no"
                   filled
                   :error="$v.entity['Payer Code'].$error"
                   @input="$v.entity['Payer Code'].$touch"
                   v-model="entity['Payer Code']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-select float-label="Country"
                    autocomplete="no"
                    :options="countries"
                    filter
                    autofocus-filter
                    :error="$v.entity['CountryId'].$error"
                    @change="$v.entity['CountryId'].$touch"
                    v-model="entity['CountryId']"></q-select>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <div class="row">
          <q-btn label="Cancel"
                 no-caps
                 color="grey-3"
                 text-color="tertiary"
                 @click="cancel" />
          <q-btn label="Save"
                 no-caps
                 color="positive"
                 class="main"
                 @click="save" />
        </div>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notifyForm, notify, request, toSelectOptions } from '../../../../common'
import { required } from 'vuelidate/lib/validators'

const validations = {
  entity: {}
}
for (const field of [
  'Name', 'Payer Code', 'CountryId'
]) {
  validations.entity[field] = { required }
}

export default {
  name: 'mex-uniteller-payer-detail-dialog',
  mixins: [
    Singleton
  ],
  components: {
  },
  data () {
    return {
      defaultEntity: {
        'ID': 0,
        'Name': null,
        'Payer Code': null,
        'CountryId': null
      },
      countries: []
    }
  },
  computed: {
    edit () {
      return this.entity['ID']
    }
  },
  validations,
  methods: {
    async show () {
      this.$q.loading.show({
        message: 'Loading...'
      })
      const resp = await request(`/admin/uniteller/countries`, 'get', {})
      if (resp.success) {
        this.countries = toSelectOptions(resp.data)
        console.log(this.countries)
      }
      this.$q.loading.hide()
    },
    onSuccess (resp) {
      notify(resp.message)
      this.$root.$emit('reload-mex-settings-payers')
      this._hide()
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }

      await this.doSave()
    },
    async doSave () {
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/mex/settings/payers/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    },
    cancel () {
      this._hide()
    }
  }
}
</script>

<style lang="scss">
.mex-uniteller-payer-detail-dialog {
  .modal-content {
    width: 580px;
  }

  .modal-scroll {
    max-height: none;
  }
}
</style>
