<template>
  <q-dialog class="mex-splash-page-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">{{ edit ? 'Edit Splash Page' : 'Create New Splash Page' }}</div>
      <div class="font-12 normal text-dark">Please fill in the information below about the Splash Page.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-input float-label="Title"
                   autocomplete="no"
                   :error="$v.entity['Title'].$error"
                   @input="$v.entity['Title'].$touch"
                   v-model="entity['Title']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Description"
                   autocomplete="no"
                   type="textarea"
                   filled
                   :error="$v.entity['Page Desc'].$error"
                   @input="$v.entity['Page Desc'].$touch"
                   v-model="entity['Page Desc']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-datetime float-label="Start Date"
                      autocomplete="no"
                      type="date"
                      format="MM/DD/YYYY"
                      :error="$v.entity['Start Date'].$error"
                      @change="$v.entity['Start Date'].$touch"
                      v-model="entity['Start Date']"></q-datetime>
        </div>
        <div class="col-sm-6">
          <q-datetime float-label="End Date"
                      autocomplete="no"
                      type="date"
                      format="MM/DD/YYYY"
                      :error="$v.entity['End Date'].$error"
                      @change="$v.entity['End Date'].$touch"
                      v-model="entity['End Date']"></q-datetime>
        </div>
        <div class="col-sm-12">
          <q-input float-label="External Url (Click the button to jump)"
                   autocomplete="no"
                   v-model="entity['External Url']"></q-input>
        </div>
        <div class="col-sm-6 flex">
          <q-checkbox label="Is WebView"
                      autocomplete="no"
                      v-model="entity['IsWebView']"></q-checkbox>
        </div>
        <div v-if="entity['IsWebView']"
             class="col-sm-12">
          <q-input float-label="WebView Url"
                   autocomplete="no"
                   v-model="entity['WebView Url']"></q-input>
        </div>
      </div>
      <div v-if="!entity['IsWebView']"
           class="row">
        <div class="col-sm text-center file-section both-side"
             v-for="(side, index) in ['English', 'Spanish']"
             :key="index">
          <input type="file"
                 class="drop-file-area-input hide"
                 accept="image/*"
                 @change="selectedFile($event.target)"
                 name="file" />
          <input type="hidden"
                 class="file-attachment-id"
                 :name="'attachment_id' + (index ? '2' : '')">
          <div class="drop-file-area"
               @click="chooseFile($event.target)">
            <q-icon name="mdi-file-image"></q-icon>
            <div class="tip pe-none">
              Click here or drag a file here to upload a photo of the <strong>{{ side }}</strong> Splash page.<br />
              <small>(Accepted file formats: .JPG, .PNG)</small>
            </div>
          </div>
          <div class="selected-file-area mv-20 hide">
            <img class="preview"
                 :src="''"
                 alt="" />
            <div class="elements">
              <a href="javascript:"
                 @click="uploadFile($event.target)"
                 class="bold">
                <q-icon name="mdi-cloud-upload-outline"></q-icon> Click to Upload
              </a>
              <span class="tip"></span>
              <a href="javascript:"
                 @click="rotatePicture($event.target, -90)"
                 class="ml-auto mr-10">
                <q-icon name="mdi-rotate-left"></q-icon>
                <q-tooltip>Rotate left</q-tooltip>
              </a>
              <a href="javascript:"
                 @click="rotatePicture($event.target, 90)"
                 class="mr-10">
                <q-icon name="mdi-rotate-right"></q-icon>
                <q-tooltip>Rotate right</q-tooltip>
              </a>
              <a href="javascript:"
                 @click="removeFile($event.target)"
                 class="red">
                <q-icon name="mdi-close"></q-icon> Remove
              </a>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <div class="row">
          <q-btn label="Cancel"
                 no-caps
                 color="grey-3"
                 text-color="tertiary"
                 @click="cancel" />
          <q-btn label="Save"
                 no-caps
                 color="positive"
                 class="main"
                 @click="save" />
        </div>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notifyForm, notify, request, notifyResponse } from '../../../../common'
import { required } from 'vuelidate/lib/validators'
import $ from 'jquery'

const validations = {
  entity: {}
}
for (const field of [
  'Title', 'Page Desc', 'Start Date',
  'End Date', 'IsWebView'
]) {
  validations.entity[field] = { required }
}

export default {
  name: 'mex-splash-page-detail-dialog',
  mixins: [
    Singleton
  ],
  components: {
  },
  data () {
    return {
      defaultEntity: {
        'ID': 0,
        'EsImageId': null,
        'ImageId': null,
        'IsWebView': false
      },
      employers: [],
      attachment_id: null,
      attachment_id2: null
    }
  },
  computed: {
    edit () {
      return this.entity['ID']
    }
  },
  validations,
  methods: {
    onSuccess (resp) {
      notify(resp.message)
      this.$root.$emit('reload-mex-splash-page')
      this._hide()
    },
    async save () {
      if (!this.validateForm(this.$el)) {
        return
      }
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }

      this.entity.ImageId = $(this.$el).find('.file-attachment-id:eq(0)').val()
      this.entity.EsImageId = $(this.$el).find('.file-attachment-id:eq(1)').val()
      await this.doSave()
    },
    validateForm (form) {
      if (this.entity['IsWebView']) {
        if (!this.entity['WebView Url']) {
          notifyResponse('Please enter the webview URL.')
          return false
        }
      } else {
        if (!$(form).find('.file-attachment-id:eq(0)').val()) {
          if ($(form).find('.drop-file-area-input:eq(0)').val()) {
            notifyResponse('Please upload the image or waiting for the uploading to be done.')
          } else {
            notifyResponse('Please select the image for English Splash Page to upload.')
          }
          return false
        }
        if ($('.file-section:visible:eq(1)').length && !$(form).find('.file-attachment-id:eq(1)').val()) {
          if ($(form).find('.drop-file-area-input:eq(1)').val()) {
            notifyResponse('Please upload the image or waiting for the uploading to be done.')
          } else {
            notifyResponse('Please select the image for Spanish Splash Page to upload.')
          }
          return false
        }
      }
      return true
    },
    async doSave () {
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/mex/setting/splash-page/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    },
    cancel () {
      this._hide()
    },
    hide () {
      const self = this
      console.log('asdfghytre')
      $('.file-section').each(function () {
        self.removeFile($(this).find('.selected-file-area a'))
      })
      // this._hide()
    },
    chooseFile ($a) {
      var $parent = $($a).parent()
      var $file = $parent.find('.drop-file-area-input')
      $file.click()
    },
    resetFile ($file) {
      $file.type = 'text'
      $file.type = 'file'
    },
    selectedFile ($file, file) {
      if (!file && $file.files.length <= 0) {
        return
      }
      if (!file) {
        file = $file.files[0]
      }

      var name = file.name.toLowerCase()
      if (!name.endsWith('.jpg') && !name.endsWith('.jpeg') && !name.endsWith('.png')) {
        this.resetFile($file)
        notifyResponse('Only JPG and PNG formats are supported!')
        return
      }
      if (file.size > 5242880) { // 5MB = 5 * 1024 * 1024
        this.resetFile($file)
        notifyResponse('The file is too large. Please compress it to under 5MB first!')
        return
      }

      var $parent = $($file).parent()
      var $selected = $parent.find('.selected-file-area')
      $selected.removeClass('hide failed')
      $parent.find('.drop-file-area').addClass('hide')

      this.displayPicture($selected.find('img.preview')[0], file)
    },
    doUploadFile ($a, file) {
      var $parent = $($a).parents('.file-section')
      var $selected = $parent.find('.selected-file-area')

      var $tip = $selected.find('.tip')
      $tip.text('Uploading file...')

      var formData = new FormData()
      formData.append('category', 'splash_page_file')
      formData.append('file', file)

      var oReq = new XMLHttpRequest()
      oReq.open('POST', '/attachments', true)
      oReq.setRequestHeader('X-Requested-With', 'XMLHttpRequest')
      oReq.onreadystatechange = function () {
        if (oReq.readyState === 4 && oReq.status === 200) {
          var resp = JSON.parse(oReq.responseText)
          if (!resp.success) {
            notifyResponse(resp.message)
            this.removeFile($selected.find('a'))
            return
          }
          $tip.text('Uploaded. Ready to verify.')
          $selected.addClass('success')
          $parent.find('.file-attachment-id').val(resp.data.file.id)

          this.resetFile($parent.find('input[type=file]')[0])
        }
      }.bind(this)
      oReq.upload.onprogress = function (e) {
        $tip.text('Uploading file... ' + Math.round(e.loaded / e.total * 100) + '%')
      }
      oReq.send(formData)

      $parent.data('req', oReq)
    },
    removeFile ($a) {
      var $parent = $($a).parents('.file-section').eq(0)
      var req = $parent.data('req')
      if (req) {
        req.abort()
      }

      var $selected = $parent.find('.selected-file-area')
      $selected.addClass('hide').removeClass('success failed')
      $selected.find('.elements a').not('.red').show()
      $selected.find('.tip').text('')
      $selected.find('img.preview').attr('src', '')

      $parent.find('.drop-file-area').removeClass('hide')

      var $file = $parent.find('.drop-file-area-input')
      if ($file.length) {
        $file[0].type = 'text'
        $file[0].type = 'file'
      }

      $parent.find('.file-attachment-id').val('')
    },
    setupDnd () {
      const self = this
      $('.drop-file-area').each(function () {
        var ifForm = $(this),
          dom = ifForm.get(0)
        ifForm.off('dragover dragleave drop')

        dom.addEventListener('dragover', function (e) {
          e.stopPropagation()
          e.preventDefault()

          ifForm.addClass('dropping')
        }, false)
        dom.addEventListener('dragleave', function (e) {
          e.stopPropagation()
          e.preventDefault()

          ifForm.removeClass('dropping')
        }, false)
        dom.addEventListener('drop', function (e) {
          e.stopPropagation()
          e.preventDefault()

          ifForm.removeClass('dropping')

          if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length) {
            self.selectedFile($(this).siblings('.drop-file-area-input'), e.dataTransfer.files[0])
          }
        }, false)
      })
    },
    displayPicture (img, file) {
      var $img = $(img)
      $img.data('file', file)

      var cache = new Image()
      cache.onload = function () {
        $img.data('width', cache.width)
        $img.data('height', cache.height)
      }

      var fileReader = new FileReader()
      fileReader.onload = function (e) {
        img.src = e.target.result
        cache.src = e.target.result
      }
      fileReader.readAsDataURL(file)
    },
    rotatePicture ($a, degrees) {
      var $parent = $($a).parents('.file-section')
      var img = $parent.find('img.preview')[0]
      var $img = $(img)
      var width = $img.data('width')
      var height = $img.data('height')

      var canvas = document.createElement('canvas')
      canvas.width = height
      canvas.height = width

      var context = canvas.getContext('2d')
      context.translate(canvas.width / 2, canvas.height / 2)
      context.rotate(degrees * Math.PI / 180)
      context.drawImage(img, -width / 2, -height / 2)

      canvas.toBlob(function (blob) {
        this.displayPicture(img, blob)
      }.bind(this), 'image/jpeg', 0.93)
    },
    uploadFile ($a) {
      var $parent = $($a).parents('.file-section')
      var img = $parent.find('img.preview')[0]
      var $img = $(img)
      var width = $img.data('width')
      var height = $img.data('height')

      var $selected = $parent.find('.selected-file-area')
      $selected.find('.elements a').not('.red').hide()

      var $tip = $selected.find('.tip')
      $tip.text('Processing...')

      var maxWidth = 4288
      var maxHeight = 3216
      var nWidth = width
      var nHeight = height

      // Resize the image if needed
      if (width > maxWidth || height > maxHeight) {
        var ratio = width / height
        var maxRatio = maxWidth / maxHeight
        if (ratio >= maxRatio) {
          nWidth = Math.round(maxWidth * 0.8)
          nHeight = Math.round(nWidth / ratio)
        } else {
          nHeight = Math.round(maxHeight * 0.8)
          nWidth = Math.round(ratio * nHeight)
        }
      } else {
        return this.doUploadFile($a, $img.data('file'))
      }

      var canvas = document.createElement('canvas')
      canvas.width = nWidth
      canvas.height = nHeight

      var context = canvas.getContext('2d')
      context.drawImage(img, 0, 0, width, height, 0, 0, nWidth, nHeight)

      canvas.toBlob(function (blob) {
        this.doUploadFile($a, blob)
      }.bind(this), 'image/jpeg', 0.93)
    }
  }
}
</script>

<style lang="scss">
.mex-splash-page-detail-dialog {
  .modal-content {
    width: 580px;
  }

  .modal-scroll {
    max-height: none;
  }
}
</style>
