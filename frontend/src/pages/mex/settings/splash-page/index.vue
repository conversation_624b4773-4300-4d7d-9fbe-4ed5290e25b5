<template>
  <q-page id="mex__splash__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3">
          <q-card class="top-statics col-12">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-comment-arrow-right-outline"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total of Splash Page</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Splash Page Report</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-account-group-outline"
                 color="positive"
                 label="Set Display Count"
                 @click="setDisplayCount"
                 class="btn-sm mr-10"
                 no-caps></q-btn>
          <q-btn icon="mdi-account-group-outline"
                 color="positive"
                 label="Create New Splash Page"
                 @click="add"
                 class="btn-sm mr-10"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'English Image'">
              <img class="splash-image"
                   v-if="props.row['English Image']"
                   :src="props.row['English Image']" />
            </template>
            <template v-else-if="col.field === 'Spanish Image'">
              <img class="splash-image"
                   v-if="props.row['Spanish Image']"
                   :src="props.row['Spanish Image']" />
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-if="props.row['Status'] == 'Inactive'"
                          v-close-overlay
                          @click.native="changeStatus(props.row)">
                    <q-item-main>Enable</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Status'] == 'Active'"
                          v-close-overlay
                          @click.native="changeStatus(props.row)">
                    <q-item-main>Disable</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
    <DetailDialog></DetailDialog>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notifySuccess, request, toSelectOptions } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import DetailDialog from './detail'
import _ from 'lodash'

export default {
  name: 'mex-settings-payers',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-splash-page')
  ],
  components: {
    DetailDialog
  },
  data () {
    return {
      title: 'Splash Page ',
      requestUrl: `/admin/mex/settings/splash-page/list`,
      columns: generateColumns([
        'ID', 'Title', 'Page Desc', 'External Url', 'WebView Url', 'Start Date', 'End Date', 'English Image', 'Spanish Image', 'Status', 'Action'
      ]),
      filterOptions: [
        {
          value: 'filter[p.id=]',
          label: 'ID'
        }, {
          value: 'filter[p.title]',
          label: 'Splash Page Name'
        }, {
          value: 'filter[p.status=]',
          label: 'Status',
          options: toSelectOptions([
            'active', 'inactive'
          ], true)
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true
    }
  },
  methods: {
    add () {
      this.$root.$emit('show-mex-splash-page-detail-dialog')
    },
    changeStatus (row) {
      let type = 'enable'
      if (row['Status'] === 'Active') {
        type = 'disable'
      }
      this.$q.dialog({
        title: `Confirm`,
        message: 'Are you sure that you want to ' + type + ' this splash page? ',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/settings/splash-page/${row['ID']}/${type}`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
          notifySuccess(resp)
        }
      }).catch(() => {})
    },
    statusClass (status) {
      return {
        'Active': 'positive',
        'Inactive': 'negative'
      }[status] || status
    },
    setDisplayCount () {
      this.$q.dialog({
        title: `Set Display Count`,
        message: `Please enter the number of times the splash page is displayed, the default value is 5 and 0 means that it is not display and -1 indicates that the display times are not limited `,
        prompt: {
          model: this.quick.splashDisplayCount,
          type: 'number'
        },
        cancel: true
      }).then(async number => {
        if (_.trim(number) === '') {
          return this.$q.notify(`Invalid number!`)
        }
        this.$q.loading.show()
        const resp = await request(`/admin/mex/settings/splash-page/setDisplayCount`, 'post', {
          number
        })
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
        }
      }).catch(() => {})
    }
  }
}

</script>

<style lang="scss">
.splash-image {
  width: 100px;
}
</style>
