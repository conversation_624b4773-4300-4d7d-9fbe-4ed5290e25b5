<template>
  <q-dialog class="mex-transaction-group-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">{{ edit ? 'Edit Transaction Group' : 'Create New Transaction Group' }}</div>
      <div class="font-12 normal text-dark">Please fill in the information below about the Transaction Group.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-input float-label="English Name"
                   autocomplete="no"
                   :error="$v.entity['English Name'].$error"
                   @input="$v.entity['English Name'].$touch"
                   v-model="entity['English Name']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Spanish Name"
                   autocomplete="no"
                   :error="$v.entity['Spanish Name'].$error"
                   @input="$v.entity['Spanish Name'].$touch"
                   v-model="entity['Spanish Name']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="English Description"
                   autocomplete="no"
                   type="textarea"
                   rows="5"
                   v-model="entity['English Description']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Spanish Description"
                   autocomplete="no"
                   type="textarea"
                   rows="5"
                   v-model="entity['Spanish Description']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-select multiple
                    filter
                    v-model="multipleMccs"
                    :options="mccsOptions" />
        </div>
        <div class="col-sm-12 text-left pt-20"
             v-show="edit">
          <div class="bold">Default Status</div>
          <div class="mt-8">
            <q-radio v-model="entity['Default Status']"
                     color="positive"
                     :error="$v.entity['Default Status'].$error"
                     @change="$v.entity['Default Status'].$touch"
                     val="Allowed"
                     label="Allowed"></q-radio>
            <q-radio v-model="entity['Default Status']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Default Status'].$error"
                     @change="$v.entity['Default Status'].$touch"
                     val="Blocked"
                     label="Blocked"></q-radio>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <div class="row">
          <q-btn label="Cancel"
                 no-caps
                 color="grey-3"
                 text-color="tertiary"
                 @click="cancel" />
          <q-btn label="Save"
                 no-caps
                 color="positive"
                 class="main"
                 @click="save" />
        </div>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notifyForm, notify, request } from '../../../../common'
import { required } from 'vuelidate/lib/validators'

const validations = {
  entity: {}
}
for (const field of [
  'English Name', 'Spanish Name', 'Default Status'
]) {
  validations.entity[field] = { required }
}

export default {
  name: 'mex-transaction-group-detail-dialog',
  mixins: [
    Singleton
  ],
  components: {
  },
  data () {
    return {
      defaultEntity: {
        'ID': 0,
        'English Name': null,
        'Spanish Name': null,
        'English Description': null,
        'Spanish Description': null,
        'Default Status': 'Allowed'
      },
      multipleMccs: [],
      mccsOptions: []
    }
  },
  computed: {
    edit () {
      return this.entity['ID']
    }
  },
  validations,
  methods: {
    onSuccess (resp) {
      notify(resp.message)
      this.$root.$emit('reload-mex-transaction-groups')
      this._hide()
    },
    async show () {
      this.multipleMccs = this.entity.MccsList ? this.entity.MccsList : []
      this.$q.loading.show()
      const resp = await request(`/admin/mex/settings/transaction-groups/mccs`)
      this.$q.loading.hide()
      if (resp.success) {
        this.mccsOptions = resp.data
      }
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      if (!this.multipleMccs.length) {
        return notifyForm('Please select at least one Mcc')
      }
      await this.doSave()
    },
    async doSave () {
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      this.entity.MCCS = this.multipleMccs
      const resp = await request(`/admin/mex/settings/transaction-groups/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    },
    cancel () {
      this._hide()
    }
  }
}
</script>

<style lang="scss">
.mex-transaction-group-detail-dialog {
  .modal-content {
    width: 580px;
  }

  .modal-scroll {
    max-height: none;
  }
}
</style>
