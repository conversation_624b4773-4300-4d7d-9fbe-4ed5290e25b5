<template>
  <q-page id="mex__transaction_groups__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-6">
          <q-card class="top-statics col-12">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-comment-arrow-right-outline"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total of Transaction Groups</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Transaction Groups Report</strong>
        </template>
        <template slot="top-right">
          <q-btn color="positive"
                 label="Import Mccs"
                 @click="importMcc"
                 class="btn-sm mr-10"
                 no-caps></q-btn>
          <q-btn icon="mdi-account-group-outline"
                 color="positive"
                 label="Create Transaction Group"
                 @click="add"
                 class="btn-sm mr-10"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Default Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row['Default Status'])">
                {{ props.row['Default Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'MCCS'">
              <p v-for="(mcc, i ) in props.row['MCCS']"
                 :key="i">
                {{mcc['mcc']}} - {{mcc['name']}}
              </p>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Default Status'] == 'Blocked'"
                          v-close-overlay
                          @click.native="changeStatus(props.row)">
                    <q-item-main>Allow</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Default Status'] == 'Allowed'"
                          v-close-overlay
                          @click.native="changeStatus(props.row)">
                    <q-item-main>Blocked</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else-if="['English Description', 'Spanish Description'].includes(col.field)">
              <div notranslate="" class="desc-field">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
    <DetailDialog></DetailDialog>
    <UploadMccDialog></UploadMccDialog>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notifySuccess, request, toSelectOptions } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import DetailDialog from './detail'
import UploadMccDialog from './upload_mcc'
export default {
  name: 'mex-transaction-groups',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-transaction-groups')
  ],
  components: {
    DetailDialog,
    UploadMccDialog
  },
  data () {
    return {
      title: 'Transaction Groups ',
      requestUrl: `/admin/mex/settings/transaction-groups/list`,
      columns: generateColumns([
        'ID', 'English Name', 'Spanish Name', 'English Description', 'Spanish Description', 'MCCS', 'Default Status', 'Action'
      ]),
      filterOptions: [
        {
          value: 'filter[tg.id=]',
          label: 'ID'
        }, {
          value: 'filter[tg.ename]',
          label: 'English Name'
        }, {
          value: 'filter[tg.sname]',
          label: 'English Name'
        }, {
          value: 'filter[tg.status=]',
          label: 'Default Status',
          options: toSelectOptions([
            'allowed', 'blocked'
          ], true)
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true
    }
  },
  methods: {
    add () {
      this.$root.$emit('show-mex-transaction-group-detail-dialog')
    },
    importMcc () {
      this.$root.$emit('show-mex-mccs-dialog')
    },
    edit (row) {
      this.$root.$emit('show-mex-transaction-group-detail-dialog', row)
    },
    changeStatus (row) {
      let type = 'allow'
      if (row['Default Status'] === 'Allowed') {
        type = 'block'
      }
      this.$q.dialog({
        title: `Confirm`,
        message: 'Are you sure that you want to ' + type + ' this transaction group? ',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/settings/transaction-groups/change-status`, 'post',
          {
            status: type === 'block' ? 'Blocked' : 'Allowed',
            id: row['ID']
          }
        )
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
          notifySuccess(resp)
        }
      }).catch(() => {})
    },
    statusClass (status) {
      return {
        'Allowed': 'positive',
        'Blocked': 'negative'
      }[status] || status
    }
  }
}
</script>

<style lang="scss">
#mex__transaction_groups__index_page {
  .desc-field {
    max-width: 300px;
    white-space: normal;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
