<template>
  <q-dialog class="mex-mccs-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Upload Mccs</div>
      <div class="font-12 normal">Please select/drag a excel file to upload.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row flex-center">
        <div class="col-7">
          <div class="upload-area"
               @click="selectFile"
               :class="{selected: file}">
            <q-icon name="mdi-file-document-outline"></q-icon>
            <template v-if="file">
              <div class="mt-10">Selected file:</div>
              <div class="font-13 text-blue">
                <span>{{ file.name }}</span>
                <a href="javascript:"
                   class="ml-5 link"
                   @click.stop="file = null">
                  <q-icon name="mdi-close-circle-outline"
                          class="font-20"
                          color="negative"></q-icon>
                  <q-tooltip>Remove and reselect</q-tooltip>
                </a>
              </div>
            </template>
            <template v-else>
              <div class="mt-10">Upload .excel/.csv</div>
              <div class="font-13 text-dark">(Drop or click here)</div>
            </template>
          </div>
          <input type="file"
                 class="hide"
                 :accept="fileAccept"
                 ref="file"
                 @change="selectedFile">
        </div>
      </div>
      <q-field class="mt-50 mb-15"
               helper="Fill Category Name.">
        <q-input float-label="Category Name"
                 v-model="name"></q-input>
      </q-field>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn label="Upload"
               no-caps
               color="positive"
               :disable="!file"
               @click="submit" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notify, notifyResponse, request, uploadAttachment } from '../../../../common'
import DndUploadFileMixin from '../../../../mixins/DndUploadFileMixin'

export default {
  name: 'mex-mccs-dialog',
  mixins: [
    Singleton,
    DndUploadFileMixin
  ],
  data () {
    return {
      file: null,
      attachment: null,
      name: null,
      acceptFileTypes: [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.csv'
      ]
    }
  },
  watch: {
    file () {
      this.attachment = null
    }
  },
  methods: {
    show () {
      this.file = null
      this.attachment = null
      this.name = null
    },
    isFileUploaded () {
      return this.attachment
    },
    async submit () {
      if (!this.file) {
        return
      }
      // if (!this.name) {
      //   return notifyResponse('Please input the category name!')
      // }
      if (!this.attachment) {
        this.$q.loading.show({ message: 'Uploading...' })
        const resp = await uploadAttachment(this.file, 'mex_mcc')
        if (typeof resp === 'string') {
          this.$q.loading.hide()
          return notifyResponse(`Failed to upload: ${resp}`)
        }
        this.attachment = resp
      }
      this.$q.loading.show({ message: 'Processing...' })
      let resp = null
      resp = await request(`/admin/mex/settings/transaction-groups/upload-mcc`, 'post', {
        attachment: this.attachment.id,
        category: this.name
      }, true)

      this.$q.loading.hide()
      if (resp.success) {
        this.$root.$emit('reload-mex-transaction-groups')
        this._hide()
        notify(resp.data)
      }
    }
  }
}
</script>

<style lang="scss">
.mex-mccs-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
