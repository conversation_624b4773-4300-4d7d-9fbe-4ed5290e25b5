<template>
  <q-page id="mex__webview__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <TransferConfig></TransferConfig>

      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="loadData"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">"Others" menu (Web Pages)</div>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-account-plus-outline"
                 color="positive"
                 label="Create New Page"
                 @click="add"
                 class="btn-sm mr-10"
                 no-caps></q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="loadData"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="remove(props.row)">
                    <q-item-main>Delete</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else-if="col.field === 'SPANISH TITLE'">{{props.row['es_title']}}</template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, _.lowerCase(col.field)) }}</div>
            </template>
          </q-td>
        </q-tr>
      </q-table>
    </div>
    <q-dialog class="mex-others-menu-dialog"
              prevent-close
              v-model="dialogModal">
      <template slot="title">
        <div class="title">{{ (!_.isNull(form.id) && !_.isUndefined(form.id)) ? 'Edit' : 'Create' }}</div>
      </template>
      <template slot="body">
        <q-input v-model="form.title"
                 label="TITLE"
                 placeholder="Enter Title"
                 @blur="$v.form.title.$touch()"
                 :error="$v.form.title.$error"
                 class="mt-10"></q-input>
       <q-input v-model="form.es_title"
                 label="SPANISH TITLE"
                 placeholder="Enter Spanish Title"
                 @blur="$v.form.es_title.$touch()"
                 :error="$v.form.es_title.$error"
                 class="mt-10"></q-input>
        <q-input v-model="form.url"
                 @blur="$v.form.url.$touch()"
                 label="URL"
                 placeholder="Enter URL"
                 :error="$v.form.url.$error"
                 class="mt-10"></q-input>
        <q-radio class="mt-10"
                 v-model="form.status"
                 color="blue"
                 val="Active"
                 label="Active"></q-radio>
        <q-radio class="mt-10 ml-15"
                 v-model="form.status"
                 color="blue"
                 val="Inactive"
                 label="Inactive"></q-radio>
      </template>
      <template slot="buttons">
        <q-btn flat
               color="negative"
               label="Cancel"
               @click="dialogModal = false"
               class="mr-8"></q-btn>
        <q-btn flat
               color="primary"
               label="Save"
               @click="submit"
               class="mr-8"></q-btn>
      </template>
    </q-dialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import {
  EventHandlerMixin,
  generateColumns,
  notifySuccess,
  request
} from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import TransferConfig from './transfer'
import { required } from 'vuelidate/lib/validators'

export default {
  name: 'mex-settings-mobile-app',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-settings-mobile-app')
  ],
  components: {
    TransferConfig
  },
  data () {
    return {
      title: 'Mobile/Web App',
      requestUrl: `/admin/mex/settings/webview/url/list`,
      columns: generateColumns(['TITLE', 'SPANISH TITLE', 'URL', 'STATUS', 'Action']),
      freezeColumn: -1,
      freezeColumnRight: 2,
      loading: true,
      form: {
        title: '',
        es_title: '',
        url: '',
        status: 'Active'
      },
      isEdit: false,
      dialogModal: false
    }
  },
  validations: {
    form: {
      title: {
        required
      },
      es_title: {
        required
      },
      url: {
        required
      }
    }
  },
  mounted () {
    this.loadData()
  },
  methods: {
    async loadData () {
      this.loading = true
      const resp = await request(this.requestUrl)
      this.loading = false
      if (resp.success) {
        this.data = resp.data
      }
    },
    add () {
      this.form = {
        title: '',
        es_title: '',
        url: '',
        status: 'Active'
      }
      this.$v.form.$reset()
      this.dialogModal = true
    },
    edit (row) {
      this.form = {
        title: row.title,
        es_title: row.es_title,
        url: row.url,
        id: row.id,
        status: row.status
      }
      this.dialogModal = true
    },
    statusClass (status) {
      return (
        {
          Active: 'positive',
          Inactive: 'negative'
        }[status] || status
      )
    },
    async remove (row) {
      this.$q
        .dialog({
          title: `Confirm`,
          message: 'Are you sure that you want to delete this page? ',
          cancel: true
        })
        .then(async () => {
          this.$q.loading.show()
          const resp = await request(`/admin/mex/settings/webview/url/del`, 'post', {
            id: row.id
          })
          this.$q.loading.hide()
          if (resp.success) {
            notifySuccess(resp.message)
            this.loadData()
          }
        })
        .catch(() => {})
    },
    async submit () {
      this.$v.form.$touch()
      if (this.$v.form.$error) {
        this.$q.notify({
          color: 'negative',
          textColor: 'white',
          message: 'Please fill all required fields'
        })
      } else {
        const params = {
          title: this.form.title,
          es_title: this.form.es_title,
          url: this.form.url,
          status: this.form.status
        }
        const isEdit = !this._.isNull(this.form.id) && !this._.isUndefined(this.form.id)
        if (isEdit) {
          params.id = this.form.id
        }
        this.$q.loading.show()
        let resp = isEdit ? await request(`/admin/mex/settings/webview/url/edit`, 'put', params) : await request(
          `/admin/mex/settings/webview/url/create`, 'post', params)
        this.$q.loading.hide()
        if (resp.success) {
          this.dialogModal = false
          notifySuccess(resp.message)
          await this.loadData()
        }
      }
    }
  }
}

</script>

<style lang="scss">
.mex-others-menu-dialog {
  .modal-content {
    width: 580px;
  }
}
</style>
