<template>
  <q-card>
    <q-card-title>
      <div class="font-16 bold mb-15">Transfer settings</div>
    </q-card-title>
    <q-card-main class="relative">
      <div class="row gutter-sm">
        <div class="col">
          <q-input float-label="Minimal Amount" autocomplete="no"
                   class="mt-20" prefix="US$" type="number" min="0.01"
                   :error="$v.entity['Minimal Amount'].$error"
                   @blur="$v.entity['Minimal Amount'].$touch"
                   v-model="entity['Minimal Amount']"></q-input>
        </div>
        <div class="col">
          <q-input float-label="Maximal Amount" autocomplete="no"
                   class="mt-20" prefix="US$" type="number" min="0.01"
                   :error="$v.entity['Maximal Amount'].$error"
                   @blur="$v.entity['Maximal Amount'].$touch"
                   v-model="entity['Maximal Amount']"></q-input>
        </div>
        <div class="col">
          <q-input float-label="UniTeller Maximal Amount" autocomplete="no"
                   class="mt-20" prefix="US$" type="number" min="0.01"
                   :error="$v.entity['UniTeller Maximal Amount'].$error"
                   @blur="$v.entity['UniTeller Maximal Amount'].$touch"
                   v-model="entity['UniTeller Maximal Amount']"></q-input>
        </div>
      </div>

      <q-inner-loading :visible="loading"></q-inner-loading>
    </q-card-main>
    <q-card-separator />
    <q-card-actions>
      <q-btn color="primary" class="ml-10 ph-15"
             icon="mdi-content-save-outline"
             :disable="loading || $v.$error" @click="submit"
             label="Save" />
    </q-card-actions>
  </q-card>
</template>

<script>
import { required, minValue } from 'vuelidate/lib/validators'
import { notifySuccess, request } from '../../../../common'
import _ from 'lodash'

function lowerValidator () {
  return parseFloat(this.entity['Minimal Amount']) <= parseFloat(this.entity['Maximal Amount'])
}

export default {
  name: 'mex-settings-mobile-app-transfer',
  data () {
    return {
      loading: false,
      entity: {
        'Minimal Amount': 25,
        'Maximal Amount': 3500,
        'UniTeller Maximal Amount': 2500
      }
    }
  },
  validations: {
    entity: {
      'Minimal Amount': {
        required,
        minValue: minValue(0.01),
        lower: lowerValidator
      },
      'Maximal Amount': {
        required,
        minValue: minValue(0.01),
        lower: lowerValidator
      },
      'UniTeller Maximal Amount': {
        required,
        minValue: minValue(0.01),
        lower: lowerValidator
      }
    }
  },
  methods: {
    async init () {
      this.loading = true
      const resp = await request(`/admin/mex/settings/app/transfer`)
      this.loading = false
      if (resp.success) {
        _.assignIn(this.entity, resp.data)
      }
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return
      }
      this.loading = true
      const resp = await request(`/admin/mex/settings/app/transfer`, 'post', this.entity)
      this.loading = false
      if (resp.success) {
        notifySuccess(resp)
      }
    }
  },
  mounted () {
    this.init()
  }
}
</script>
