<template>
  <q-page id="mex__employer_ach_report__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
          <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-3 col-xs-10 col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-briefcase-account-outline"
                        color="purple"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.employee || 0 }}</div>
                  <div class="description">Total Employees</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-3 col-xs-10 col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-group-outline"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.count || 0 }}</div>
                  <div class="description">Total Count of Deposits</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-3 col-xs-10 col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.total || 0 }}</div>
                  <div class="description">Total Employee Deposits</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-3 col-xs-10 col-sm-3">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="negative"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.avg || 0 }}</div>
                  <div class="description">Avg Deposits</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="['Avg. Deposits per Employee $', 'Amount of Deposits', 'Employee Deposits'].includes(col.field)">
              <div class="mex-amount">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else-if="['Employee Count', 'Count of Deposits', 'Avg. Deposits per Employee #'].includes(col.field)">
              <div class="mex-amount">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import ForceReloadMixin from '../../../../mixins/ForceReloadMixin'

export default {
  name: 'mex-employer-ach-report',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    ForceReloadMixin
  ],
  data () {
    return {
      title: 'Employer to Employee ACH Direct Report',
      requestUrl: `/admin/mex/summary/employer-ach/list`,
      downloadUrl: `/admin/mex/summary/employer-ach/export`,
      columns: generateColumns([
        'Employer Name',
        'Employee Count',
        'Employee Deposits',
        'Count of Deposits',
        'Amount of Deposits',
        'Avg. Deposits per Employee #',
        'Avg. Deposits per Employee $'
      ], ['Employee Count', 'Count of Deposits', 'Avg. Deposits per Employee #', 'Avg. Deposits per Employee $', 'Amount of Deposits', 'Employee Deposits']),
      filterOptions: [],
      freezeColumn: -1,
      freezeColumnRight: 0,
      autoLoad: true,
      keyword: ''
    }
  }
}
</script>
<style lang="scss">
#mex__employer_ach_report__index_page {
}
</style>
