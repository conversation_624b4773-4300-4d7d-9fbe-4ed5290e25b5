<template>
  <q-page id="mex__transfers__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row">
        <div :class="summaryCol">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-comment-arrow-right-outline"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.total || 0 }}</div>
                  <div class="description">Number of Completed Transfers</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="summaryCol">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-usd"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.amount || 0 | moneyFormat }}</div>
                  <div class="description">Total Amount Transferred</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="summaryCol">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-chart-bar"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.average || 0 | moneyFormat }}</div>
                  <div class="description">Average Transfer Amount</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="summaryCol">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-shield-airplane-outline"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.revenue || 0 | moneyFormat }}</div>
                  <div class="description">Partner Transfer Revenue</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="summaryCol"
             v-if="masterAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-shield-airplane-outline"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.ternRevenue | moneyFormat }}</div>
                  <div class="description">Platform Transfer Revenue</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div :class="summaryCol"
             v-if="masterAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-shield-airplane-outline"
                        color="warning"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.platformRevenue || 0 | moneyFormat }}</div>
                  <div class="description">FX Processing Fee</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50, 100, 200, 500, 1000]"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Transfer Report</strong>
        </template>
        <template slot="top-right">
          <q-btn v-if="masterAdmin"
                 icon="mdi-cog-outline"
                 color="orange"
                 label="Transfer Methods Config"
                 @click="$root.$emit('show-mex-transfer-methods-dialog')"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn color="negative"
                 label="Set Maintenance Time"
                 @click="addMaintenance"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn icon="mdi-refresh"
                 color="orange"
                 label="Update UniTeller Transfers"
                 @click="forceReload"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn icon="mdi-refresh"
                 color="orange"
                 label="Update Cancel Transfer"
                 @click="updateCancelTransfer"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn v-if="masterAdmin"
                 icon="mdi-lightning-bolt"
                 color="orange"
                 label="Execute Queued Transfers"
                 @click="execute"
                 class="btn-sm mr-8"
                 no-caps>
          </q-btn>
          <q-btn v-if="$store.state.User.teams.indexOf('MasterAdmin') !== -1 || $store.state.User.teams.indexOf('TransferMex Admin') !== -1 "
                 icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-13"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="['FX Processing Fee', 'Transfer Fee', 'Transfer Cost', 'Transfer Exchange Rate',
                'Settlement Amount',
                'Fee Revenue', 'Shared Revenue', 'Partner Revenue', 'Program Revenue',
                 'Transfer Amount', 'Transfer Amount Received'].includes(col.field)">
              <div class="mex-amount">{{ _.get(props.row, col.field) }}</div>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          tag="a"
                          link
                          :href="'/admin#/j/mex/members/' + props.row['Member ID']"
                          target="_blank">
                    <q-item-main>View Member</q-item-main>
                  </q-item>
                  <q-item v-if="masterAdmin || agentAdmin"
                          v-close-overlay
                          @click.native="loginAs(props.row)">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['Partner ID']"
                          @click.native="syncStatusDetails(props.row)">
                    <q-item-main>Sync Partner Status</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['Partner ID']"
                          @click.native="viewPartnerDetails(props.row)">
                    <q-item-main>View Partner Details</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Status'] == 'Canceled' && props.row['Processor'] == 'Uniteller'">
                    <q-item-main label="Update Transaction Status"></q-item-main>
                    <q-item-side right>
                      <q-item-tile icon="mdi-menu-right"></q-item-tile>
                    </q-item-side>
                    <q-popover anchor="bottom left"
                               self="top right"
                               :offset="[0, -40]">
                      <q-list link>
                        <q-item v-close-overlay
                                @click.native="changeStatus(props.row, 'REFUND')">
                          <q-item-main>Refund</q-item-main>
                        </q-item>
                        <q-item v-close-overlay
                                @click.native="changeStatus(props.row, 'VOID')">
                          <q-item-main>Void</q-item-main>
                        </q-item>
                      </q-list>
                    </q-popover>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="['Queued', 'Processing'].includes(props.row['Status'])"
                          @click.native="transfer(props.row)">
                    <q-item-main>Transfer Now</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['Sandbox'] && props.row['canSimulateCashPickup']"
                          @click.native="cashPickup(props.row, 1)">
                    <q-item-main>Simulate: Cash Pickup</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['Sandbox'] && ['Confirmation'].includes(props.row['Status'])"
                          @click.native="confirm(props.row, 1)">
                    <q-item-main>Simulate: Confirm</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['Sandbox'] && ['Created'].includes(props.row['Status'])"
                          @click.native="complete(props.row, 1)">
                    <q-item-main>Simulate: Complete</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['Processor'] === 'Rapyd' && props.row['Sandbox'] && ['Created'].includes(props.row['Status'])"
                          @click.native="complete(props.row, 0.8)">
                    <q-item-main>Simulate: Complete 80%</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['isHoldForCashPickup']"
                          @click.native="removeHoldStatue(props.row)">
                    <q-item-main>Remove On Hold Status For View Cash Pickup Code</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="(['Pending', 'Queued', 'Processing', 'Confirmation', 'Created'].includes(props.row['Status']) && props.row['Processor'] !== 'Intermex') ||
                          (['Confirmation', 'Pending'].includes(props.row['Status']) && props.row['Processor'] === 'Intermex' && masterAgentAdmin)"
                          @click.native="cancel(props.row)">
                    <q-item-main>Cancel</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

    <SmsDialog :callback="verifiedSms"></SmsDialog>
    <MaintenanceDialog></MaintenanceDialog>
    <MethodsDialog></MethodsDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notify, notifySuccess, request, toSelectOptions } from '../../../common'
import MexPageMixin from '../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import ForceReloadMixin from '../../../mixins/ForceReloadMixin'
import SmsDialog from '../common/sms'
import MaintenanceDialog from './maintenance'
import MethodsDialog from './method'

export default {
  name: 'mex-transfers',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    ForceReloadMixin,
    EventHandlerMixin('reload-mex-transfers')
  ],
  components: {
    SmsDialog,
    MaintenanceDialog,
    MethodsDialog
  },
  data () {
    const columns = [
      'Create Date', 'Recipient Seen Code Date', 'Complete Date', 'Cancel Date', 'Employer Name',
      'Member ID', 'First Name', 'Last Name',
      'Transfer ID', 'Processor', 'Recipient Full Name', 'Recipient Country',
      'Transfer Amount', 'Transfer Amount Received',
      'Transfer Exchange Rate', 'Transfer Method', 'Transfer Method Type',
      'Partner ID', 'Promo ID', 'Promo Type',
      'Settlement ID', 'Settlement Amount', 'Transfer Fee', 'Transfer Cost',
      'Fee Revenue', 'Shared Revenue', 'Partner Revenue', 'Program Revenue'
    ]
    if (this.isMasterAdmin()) {
      columns.push('FX Processing Fee')
    }
    for (const col of ['Status', 'Action']) {
      columns.push(col)
    }
    return {
      title: 'Transfers',
      filtersUrl: `/admin/mex/transfers/filters`,
      requestUrl: `/admin/mex/transfers/list`,
      downloadUrl: `/admin/mex/transfers/export`,
      columns: generateColumns(columns, [
        'FX Processing Fee', 'Transfer Fee', 'Transfer Cost', 'Transfer Exchange Rate',
        'Fee Revenue', 'Shared Revenue', 'Partner Revenue', 'Program Revenue',
        'Transfer Amount', 'Transfer Amount Received'
      ], {
        'Create Date': 't.createdAt',
        'Member ID': 's.id',
        'First Name': 's.firstName',
        'Last Name': 's.lastName',
        'Transfer ID': 't.id',
        'Recipient Full Name': 'CONCAT(u.firstName,u.lastName)'
      }),
      filterOptions: [
        {
          value: 'filter[t.id=]',
          label: 'Transfer ID'
        }, {
          value: 'filter[s.id=]',
          label: 'Member ID'
        }, {
          value: 'filter[t.partnerId=]',
          label: 'Partner ID'
        }, {
          value: 'filter[s.firstName=]',
          label: 'Member First Name'
        }, {
          value: 'filter[s.lastName=]',
          label: 'Member Last Name'
        }, {
          value: 'filter[r.firstName=]',
          label: 'Recipient First Name'
        }, {
          value: 'filter[r.lastName=]',
          label: 'Recipient Last Name'
        }, {
          value: 'filter[r.country=]',
          label: 'Recipient Country',
          options: [],
          source: 'countries'
        }, {
          value: 'employerId',
          label: 'Employer',
          options: [],
          source: 'employers'
        },
        {
          value: 'filter[t.payoutType=]',
          label: 'Transfer Type',
          options: [
            {
              label: 'Bank Transfer',
              value: 'bank'
            },
            {
              label: 'Cash Pickup',
              value: 'cash'
            }
          ]
        }, {
          value: 'filter[t.partner=]',
          label: 'Processor',
          options: [
            {
              label: 'Intermex',
              value: 'intermex'
            },
            {
              label: 'UniTeller',
              value: 'uniteller'
            },
            {
              label: 'Rapyd',
              value: 'rapyd'
            }
          ]
        }, {
          value: 'filter[t.payoutMethodType=]',
          label: 'Transfer Method Type',
          options: [],
          source: 'payoutMethodTypes'
        }, {
          value: 'date_range',
          label: 'Create Date',
          range: [{
            value: 'range[t.createdAt][start]',
            type: 'date'
          }, {
            value: 'range[t.createdAt][end]',
            type: 'date'
          }]
        }, {
          value: 'filter[t.status=]',
          label: 'Status',
          options: toSelectOptions([
            'Pending', 'Confirmation', 'Created',
            'Queued', 'Processing', 'Completed', 'Canceled',
            'Error', 'Expired', 'Hold', 'Returned',
            'Decline', 'Declined' // , 'Sent sms, email, or called'
          ])
        },
        {
          value: 'cashPickCodeAt',
          label: 'Recipient Seen Code Date',
          range: [{
            value: 'range[t.cashPickCodeAt][start]',
            type: 'date'
          }, {
            value: 'range[t.cashPickCodeAt][end]',
            type: 'date'
          }]
        }, {
          value: 'receiveAt',
          label: 'Complete Date',
          range: [{
            value: 'range[t.receiveAt][start]',
            type: 'date'
          }, {
            value: 'range[t.receiveAt][end]',
            type: 'date'
          }]
        }, {
          value: 'cancelAt',
          label: 'Cancel Date',
          range: [{
            value: 'range[t.cancelAt][start]',
            type: 'date'
          }, {
            value: 'range[t.cancelAt][end]',
            type: 'date'
          }]
        }, {
          value: 'filter[t.transferFee=]',
          label: 'Transfer Fee',
          options: [],
          source: 'feeList'
        }, {
          value: 'promoType',
          label: 'Promo Type',
          options: [
            {
              label: 'First Transfer',
              value: 'st_transfer'
            },
            {
              label: 'Free Money Transfer',
              value: 'free_transfer'
            },
            {
              label: 'Free Bank Transfer',
              value: 'free_bank'
            },
            {
              label: 'Free Cash Pickup',
              value: 'free_cash_pickup'
            },
            {
              label: 'Other',
              value: 'other'
            }
          ]
        }, {
          value: 'filter[t.cost=]',
          label: 'Transfer Cost',
          options: [],
          source: 'costList'
        }, {
          value: 'filter[t.settlement=]',
          label: 'Settlement ID'
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  computed: {
    summaryCol () {
      return this.masterAdmin ? 'col-sm-2 col-6 col-xs-10' : 'col-sm-3 col-6 col-xs-10'
    }
  },
  methods: {
    init () {
      if (this.$route.params.id) {
        this.filters = [
          {
            field: 'filter[t.id=]',
            predicate: '=',
            value: this.$route.params.id
          }
        ]
      }
    },
    statusClass (status) {
      return {
        'Pending': 'dark',
        'Confirmation': 'warning',
        'Queued': 'purple',
        'Processing': 'purple',
        'Created': 'blue',
        'Completed': 'positive',
        'Canceled': 'orange',
        'Error': 'negative',
        'Expired': 'pansy'
      }[status] || status
    },
    async complete (row, percent) {
      this.$q.dialog({
        title: `Simulate: Complete`,
        message: 'Are you sure that you want to complete this transfer? ',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/transfers/simulate-complete/${row['Transfer ID']}`, 'post', {
          percent
        })
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
          notifySuccess(resp)
        }
      }).catch(() => {})
    },
    async cashPickup (row) {
      this.$q.dialog({
        title: `Confirm`,
        message: 'Are you sure that you want to set this transfer to available to Pick Up? ',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/transfers/simulate-cash/${row['Transfer ID']}`, 'post', {})

        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
          notifySuccess(resp)
        }
      }).catch(() => {})
    },
    async confirm (row) {
      this.$q.dialog({
        title: `Confirm`,
        message: 'Are you sure that you want to confirm this transfer? ',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/members/${row['Member ID']}/transfer/confirm`, 'post', {
          transferId: row['Transfer ID']
        })

        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
          notifySuccess(resp)
        }
      }).catch(() => {})
    },
    async cancel (row) {
      this.$q.dialog({
        title: `Cancel`,
        message: 'Are you sure that you want to cancel this transfer? ',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/transfers/cancel/${row['Transfer ID']}`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
          notifySuccess(resp)
        }
      }).catch(() => {})
    },
    async removeHoldStatue (row) {
      this.$q.dialog({
        title: `Cancel`,
        message: 'Are you sure that you want to remove on hold status on view the cash pickup code for this transfer? ',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/transfers/removeHoldStatue/${row['Transfer ID']}`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
          notifySuccess(resp)
        }
      }).catch(() => {})
    },
    async execute () {
      this.$q.dialog({
        title: `Execute Queued Transfers`,
        message: 'Are you sure that you want to execute all the queued transfers now? ',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/mex/transfers/process-all-queued`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          notifySuccess(resp)
        }
      }).catch(() => {})
    },
    verifiedSms (row, token) {
      row.token = token
      if (row.smsType === 'loginAs') {
        this.$c.loginAs(row['Member ID'])
      }
    },
    loginAs (row) {
      if (this.masterAdmin) {
        this.$c.loginAs(row['Member ID'])
      } else if (this.agentAdmin) {
        row.smsType = 'loginAs'
        this.$root.$emit('show-mex-common-sms-dialog', row)
      }
    },
    async transfer (row) {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/transfers/process-queue/${row['Transfer ID']}`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
        notifySuccess(resp)
      }
    },
    changeStatus (row, status) {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to change the UniTeller transaction to "${status}"?`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show({
          message: `Changing the transaction to "${status}"...`
        })
        const resp = await this.c.request(`/admin/mex/transfers/update/${row['Transfer ID']}`, 'post', {
          status: status
        })
        this.$q.loading.hide()
        if (resp.success) {
          notifySuccess(resp)
        }
      }).catch(() => {})
    },
    async updateCancelTransfer () {
      await this.c.request(`/admin/mex/transfers/updateCancel`, 'get')
    },
    addMaintenance () {
      this.$root.$emit('show-mex-maintenance-dialog')
    },
    async syncStatusDetails (row) {
      this.loading = true
      const resp = await request(`/admin/mex/transfers/sync-partner-status/${row['Transfer ID']}`, 'post')
      this.loading = false
      if (resp.success) {
        notify(resp.message)
        this.reload()
      }
    },
    viewPartnerDetails (row) {
      window.open(`/admin/mex/transfers/view-payout-details/${row['Transfer ID']}`, '_blank')
    }
  }
}
</script>
