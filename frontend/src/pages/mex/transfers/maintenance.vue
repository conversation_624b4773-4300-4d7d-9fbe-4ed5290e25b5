<template>
  <q-dialog class="mex-maintenance-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Set Maintenance Time</div>
      <div class="font-12 normal text-dark">Please fill in the start and end date of the maintenance (UTC) and the transfer queue will be automatically executed after the maintenance period.
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="gutter-form mt--10 mb-5">
        <div class="maintenance-item row"
             v-for="(maintenance, index) in maintenances"
             :key="index">
          <div class="col-5">
            <q-datetime float-label="Start Date"
                        autocomplete="no"
                        type="datetime"
                        format="MM/DD/YYYY HH:mm"
                        :format24h="true"
                        v-model="maintenance['Start Date']"></q-datetime>
          </div>
          <div class="col-5">
            <q-datetime float-label="End Date"
                        autocomplete="no"
                        type="datetime"
                        :format24h="true"
                        format="MM/DD/YYYY HH:mm"
                        v-model="maintenance['End Date']"></q-datetime>
          </div>
          <q-btn flat
                 round
                 dense
                 icon="mdi-delete-outline"
                 class="ml-10 mt-5"
                 @click="remove(index)" />
        </div>
        <q-btn class="mt-10 add-filter"
               flat
               icon="add"
               @click="add">ADD</q-btn>
        <div class="col-12">
          <q-radio v-model="entity['Type']"
                   color="blue"
                   :error="$v.entity['Type'].$error"
                   @change="$v.entity['Type'].$touch"
                   val="rapyd"
                   @input="changeType"
                   label="Rapyd"></q-radio>
          <q-radio v-model="entity['Type']"
                   color="blue"
                   class="ml-15"
                   :error="$v.entity['Type'].$error"
                   @change="$v.entity['Type'].$touch"
                   @input="changeType"
                   val="uniteller"
                   label="UniTeller"></q-radio>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <div class="row">
          <q-btn :label="'Cancel'"
                 no-caps
                 color="grey-3"
                 text-color="tertiary"
                 @click="cancel" />
          <q-btn v-if="(rapydSettingFlag && entity['Type'] == 'rapyd') || (uniTellerSettingFlag && entity['Type'] == 'uniteller')"
                 :label="'Clear setup'"
                 no-caps
                 color="grey-3"
                 text-color="tertiary"
                 @click="clear" />
          <q-btn :label="'Save'"
                 no-caps
                 color="blue"
                 class="main"
                 @click="save" />
        </div>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify } from '../../../common'
import { required } from 'vuelidate/lib/validators'
import moment from 'moment'
import _ from 'lodash'

const validations = {
  entity: {}
}
for (const field of ['Type']) {
  validations.entity[field] = { required }
}

export default {
  name: 'mex-maintenance-dialog',
  mixins: [
    Singleton
  ],
  components: {
  },
  data () {
    return {
      defaultEntity: {
        'maintenances': [],
        'Type': 'rapyd'
      },
      rapydSetting: [],
      uniTellerSetting: [],
      rapydSettingFlag: false,
      uniTellerSettingFlag: false,
      maintenances: []
    }
  },
  validations,
  methods: {
    onSuccess (resp) {
      notify(resp.message)
      this._hide()
    },
    remove (index) {
      this.maintenances.splice(index, 1)
    },
    add () {
      this.maintenances.push({
        'Start Date': null,
        'End Date': null
      })
    },
    changeType () {
      console.log(this.entity)
      this.maintenances = []
      if (this.entity['Type'] === 'rapyd') {
        _.forEach(this.rapydSetting, (item) => {
          if (item['Start Date'] && item['End Date']) {
            this.maintenances.push({
              'Start Date': item['Start Date'] ? moment(item['Start Date']).format('MM/DD/YYYY HH:mm') : null,
              'End Date': item['End Date'] ? moment(item['End Date']).format('MM/DD/YYYY HH:mm') : null
            })
          }
        })
      } else {
        _.forEach(this.uniTellerSetting, (item) => {
          if (item['Start Date'] && item['End Date']) {
            this.maintenances.push({
              'Start Date': item['Start Date'] ? moment(item['Start Date']).format('MM/DD/YYYY HH:mm') : null,
              'End Date': item['End Date'] ? moment(item['End Date']).format('MM/DD/YYYY HH:mm') : null
            })
          }
        })
      }
    },
    async show () {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/transfers/getMaintenance`, 'post')
      console.log(resp)
      if (resp.success) {
        this.rapydSetting = resp.data.rapydSetting
        this.uniTellerSetting = resp.data.uniTellerSetting
        this.rapydSettingFlag = resp.data.rapydSettingFlag
        this.uniTellerSettingFlag = resp.data.uniTellerSettingFlag
        this.changeType()
        console.log(resp)
      }
      this.$q.loading.hide()
    },
    async clear () {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to clear the maintenance time for ' + this.entity['Type'] + '?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await this.c.request(`/admin/mex/transfers/clearMaintenance`, 'post', this.entity)
        if (resp.success) {
          this.onSuccess(resp)
        }
        this.$q.loading.hide()
      }).catch(() => {})
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to set maintenance time for ' + this.entity['Type'] + '?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        this.entity.maintenances = []
        _.forEach(this.maintenances, (item) => {
          if (item['Start Date'] && item['End Date']) {
            this.entity.maintenances.push({
              'Start Date': item['Start Date'] ? moment(item['Start Date']).format('MM/DD/YYYY HH:mm') : null,
              'End Date': item['End Date'] ? moment(item['End Date']).format('MM/DD/YYYY HH:mm') : null
            })
          }
        })
        const resp = await this.c.request(`/admin/mex/transfers/setMaintenance`, 'post', this.entity)
        if (resp.success) {
          this.onSuccess(resp)
        }
        this.$q.loading.hide()
      }).catch(() => {})
    },
    cancel () {
      this._hide()
    }
  }
}
</script>

<style lang="scss">
.mex-maintenance-dialog {
  .modal-content {
    width: 580px;
  }

  .modal-scroll {
    max-height: none;
  }
  .maintenance-item {
    justify-content: space-around;
  }
}
</style>
