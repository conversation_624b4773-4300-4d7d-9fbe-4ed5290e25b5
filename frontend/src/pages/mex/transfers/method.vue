<template>
  <q-dialog class="mex-employer-global-dialog"
            v-model="visible">
    <template slot="title">
      <div class="font-16 mb-2">Transfer Methods Config</div>
      <div class="font-12 normal text-dark ph-40">
        Transfer methods configurations for all the members. Please be careful to change them.
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <q-list no-border>
        <q-item>
          <q-item-main>
            <q-field helper="When disabled, the members cannot use bank transfer by Intermex">
              <q-toggle v-model="form.enableIntermexBank"
                        label="Enable Intermex(Bank Transfer)"></q-toggle>
            </q-field>
          </q-item-main>
        </q-item>
        <q-item>
          <q-item-main>
            <q-field helper="When disabled, the members cannot use cash pickup by Intermex">
              <q-toggle v-model="form.enableIntermexCashPickup"
                        label="Enable Intermex(Cash Pickup)"></q-toggle>
            </q-field>
          </q-item-main>
        </q-item>
        <q-item>
          <q-item-main>
            <q-field helper="When disabled, the members cannot use cash pickup by UniTeller">
              <q-toggle v-model="form.enableUniTellerCashPickup"
                        label="Enable UniTeller(Cash Pickup)"></q-toggle>
            </q-field>
          </q-item-main>
        </q-item>
        <q-item>
          <q-item-main>
            <q-field helper="When disabled, the members cannot use Bank Transfer by UniTeller">
              <q-toggle v-model="form.enableUniTellerBank"
                        label="Enable UniTeller(Bank Transfer)"></q-toggle>
            </q-field>
          </q-item-main>
        </q-item>
        <q-item>
          <q-item-main>
            <q-field helper="When disabled, the members cannot use bank transfer by Rapyd">
              <q-toggle v-model="form.enableRapydBank"
                        label="Enable Rapyd(Bank Transfer)"></q-toggle>
            </q-field>
          </q-item-main>
        </q-item>
        <q-item>
          <q-item-main>
            <q-field helper="The reason for this change">
              <q-input v-model="form.comment"
                       type="textarea"
                       label="Comment" />
            </q-field>
          </q-item-main>
        </q-item>
      </q-list>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel"
             color="grey"
             no-caps
             @click="_hide" />
      <q-btn label="Save"
             no-caps
             color="blue"
             text-color="white"
             @click="save" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, request } from '../../../common'
import _ from 'lodash'
export default {
  name: 'mex-transfer-methods-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      form: {
        enableIntermexBank: null,
        enableIntermexCashPickup: null,
        enableUniTellerCashPickup: null,
        enableUniTellerBank: null,
        enableRapydBank: null,
        comment: null
      }
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    },
    masterAdmin () {
      const user = this.user
      return user && user.currentRole === 'MasterAdmin'
    }
  },
  methods: {
    async show () {
      this.visible = false
      await this.init()
      this.visible = true
    },
    async init () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/transfer/0/method-config`)
      this.$q.loading.hide()
      if (resp.success) {
        this.form = resp.data
      }
    },
    async save () {
      if (!_.trim(this.form.comment)) {
        notify('Please enter the reason why you want to change the transfer methods config.', 'negative')
        return
      }
      this.$q.loading.show()
      const resp = await request(`/admin/mex/transfer/0/method-config`, 'post', this.form)
      this.$q.loading.hide()
      if (resp.success) {
        this.form = resp.data
        notify(resp)
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.mex-transfer-methods-dialog {
  .modal-content {
    width: 500px;
  }
}
</style>
