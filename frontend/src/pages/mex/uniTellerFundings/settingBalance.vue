<template>
  <q-dialog class="mex-setting-uniteller-balance-dialog"
            v-model="visible">
    <template slot="title">
      <div class="mb-5">
        <div class="radius-box">
          <q-icon name="mdi-credit-card-wireless"
                  class="font-22"></q-icon>
        </div>
      </div>
      <div class="font-16 mb-2">Set UniTeller Balance</div>
      <div class="font-12 normal">Enter the required info to set UniTeller Balance</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <q-field label="Action"
               :label-width="12"
               class="mv-20 text-left">
        <q-radio v-model="type"
                 val="credit"
                 label="Credit"
                 color="positive"
                 class="mr-20"></q-radio>
        <q-radio v-model="type"
                 val="debit"
                 label="Debit"
                 color="negative"></q-radio>
      </q-field>
      <q-field label="Amount"
               :label-width="12"
               class="mv-20">
        <q-input type="number"
                 min="0.01"
                 max="3500"
                 autocomplete="no"
                 :error="$v.amount.$error"
                 @blur="$v.amount.$touch"
                 v-model="amount"></q-input>
      </q-field>
      <q-field label="Short Comment"
               :label-width="12"
               class="mv-20">
        <q-input autocomplete="no"
                 :error="$v.amount.$error"
                 @blur="$v.amount.$touch"
                 v-model="comment"></q-input>
      </q-field>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn label="Submit"
               no-caps
               color="positive"
               @click="submit" />
        <q-btn label="Close"
               no-caps
               color="grey-3"
               class="ml-0 mt-8"
               text-color="tertiary"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { minValue, required } from 'vuelidate/lib/validators'
import { notifyForm, request } from '../../../common'

export default {
  name: 'mex-setting-uniteller-balance-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      type: 'credit',
      amount: 0,
      comment: null
    }
  },
  validations: {
    type: {
      required
    },
    amount: {
      required,
      minValue: minValue(0.01)
    },
    comment: {
      required
    }
  },
  computed: {},
  methods: {
    show () {
      this.type = 'credit'
      this.amount = 0
      this.comment = null
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      const message = `Are you sure that you want to ${this.type} ${this.amount} dollars to UniTeller balance?`
      this.$q.dialog({
        title: 'Confirm',
        message,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const data = {
          source: this.source,
          type: this.type,
          amount: this.amount,
          comment: this.comment
        }
        const resp = await request(`/admin/mex/uniteller-fundings/set-balance`, 'post', data)
        this.$q.loading.hide()
        if (resp.success) {
          this.$root.$emit('reload-mex-uniteller-fundings')
          this._hide()
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
.mex-setting-uniteller-balance-dialog {
  .modal-content {
    width: 450px;
  }
}
</style>
