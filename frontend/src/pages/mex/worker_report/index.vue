<template>
  <q-page id="mex__worker_report__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="columns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }} Report</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Worker ID'">
              <span class="font-12 view-btn"
                    @click="view(props.row)">
                {{ props.row['Worker ID'] }}
              </span>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import MexPageMixin from '../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'mex-atm-transactions',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-atm-transactions')
  ],
  data () {
    return {
      title: 'Workers',
      requestUrl: `/admin/mex/members/atm-transactions/list`,
      downloadUrl: `/admin/mex/members/atm-transactions/export`,
      columns: generateColumns([
        'Date & Time',
        'Worker Name',
        'Worker ID',
        'Worker Phone',
        'Amount Withdrawn'
      ]),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'Worker ID'
        }, {
          value: 'filter[u.name=]',
          label: 'Worker Name'
        }, {
          value: 'filter[u.mobilephone=]',
          label: 'Worker Phone'
        },
        {
          value: 'date_range',
          label: 'Date & Time',
          range: [{
            value: 'range[uct.txnTime][start]',
            type: 'date'
          }, {
            value: 'range[uct.txnTime][end]',
            type: 'date'
          }]
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 1,
      autoLoad: true,
      keyword: ''
    }
  },
  methods: {
    view (row) {
      window.open(`/admin#/j/mex/members/${row['Worker ID']}`, '_blank')
    }
  }
}
</script>
<style lang="scss">
#mex__worker_report__index_page {
  .view-btn {
    color: #0062ff;
    cursor: pointer;
    text-decoration: underline;
  }
}
</style>
