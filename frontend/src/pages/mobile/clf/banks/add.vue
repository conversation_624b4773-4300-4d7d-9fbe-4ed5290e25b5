<template>
  <q-modal class="flex items-center column modal-page center-box-modal-page"
           maximized
           no-esc-dismiss
           no-backdrop-dismiss
           no-route-dismiss
           :value="visible">
    <q-modal-layout id="mobile-clf-banks-add-modal">
      <q-toolbar slot="header">
        <q-btn flat round
               class="btn-back"
               @click="_hide"
               @touchend.native="_hide"
               icon="mdi-chevron-left"></q-btn>
        <q-toolbar-title>Connect Your Bank</q-toolbar-title>
        <q-btn flat round disable></q-btn>
      </q-toolbar>

      <div class="center-box">
        <div class="center-box-inner">
          <div class="text-center pt-40 pb-20">
            <img class="icon"
                 v-if="entity && (entity.icon || entity.banner)"
                 :src="entity.icon || entity.banner" alt="">
            <img v-else
                 class="icon" src="../../../../statics/mobile/clf/bank.png" alt="">
          </div>

          <div class="modal-desc">
            <div>Use your bank login to link your bank.</div>
            <div>We don't save this information.</div>
          </div>

          <div class="form">
            <q-field class="mb-20"
                     icon="mdi-account-outline flaticon-avatar"
                     :error="$v.username.$error">
              <q-input v-model="username"
                       placeholder="Username"
                       @input="$v.username.$touch"></q-input>
            </q-field>
            <q-field icon="mdi-lock-outline flaticon-padlock"
                     :error="$v.password.$error">
              <q-input v-model="password"
                       type="password"
                       placeholder="Password"
                       @input="$v.password.$touch"></q-input>
            </q-field>
          </div>

          <q-btn class="full-width btn-md mt-20"
                 color="black"
                 label="Connect"
                 :disable="$v.$invalid"
                 @click="$c.click(submit)"
                 @touchend.native="submit"></q-btn>
        </div>
      </div>
    </q-modal-layout>
  </q-modal>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { request } from '../../../../common'
import { required } from 'vuelidate/lib/validators'

export default {
  name: 'mobile-clf-banks-add-modal',
  mixins: [
    Singleton
  ],
  data () {
    return {
      username: '',
      password: ''
    }
  },
  validations: {
    username: {
      required
    },
    password: {
      required
    }
  },
  methods: {
    show () {
      this.username = this.password = ''
      this.$v.$reset()
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return
      }
      this.$q.loading.show()
      const resp = await request(`/api/clf/banks/${this.entity.id}/add`, 'post', {
        username: this.username,
        password: this.password
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.$store.dispatch('Bank/init')
        this.$root.$emit('hide-mobile-clf-banks-select-modal')
        this._hide()

        setTimeout(() => {
          this.$root.$emit('show-mobile-clf-banks-verify-modal', resp.data)
        }, 500)
      }
    }
  }
}
</script>

<style lang="scss">
  #mobile-clf-banks-add-modal {
    .center-box {
      .center-box-inner {
        min-height: 450px;
      }

      .icon {
        max-width: 65vw;
        max-height: 30vw;
        margin-left: auto;
        margin-right: auto;
      }

      .modal-desc {
        font-size: 15px;
        text-align: center;
        line-height: 1.8em;
      }
    }
  }
</style>
