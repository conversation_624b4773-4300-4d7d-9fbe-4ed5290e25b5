<template>
  <q-modal class="flex items-center column modal-page center-box-modal-page"
           maximized
           no-esc-dismiss
           no-backdrop-dismiss
           no-route-dismiss
           :value="visible">
    <q-modal-layout id="mobile-clf-banks-connect-modal">
      <q-toolbar slot="header">
        <q-btn flat round
               class="btn-back"
               @click="_hide"
               @touchend.native="_hide"
               icon="mdi-chevron-left"></q-btn>
        <q-toolbar-title>Connect Your Bank</q-toolbar-title>
        <q-btn flat round disable></q-btn>
      </q-toolbar>

      <div class="center-box">
        <div class="center-box-inner">
          <h3>Connect your bank account</h3>

          <div class="types">
            <img src="../../../../statics/mobile/clf/card-type.png" alt="">

            <div class="row">
              <div class="col">
                <q-radio v-model="type"
                         checked-icon="mdi-checkbox-marked-circle"
                         unchecked-icon="mdi-checkbox-blank-circle-outline"
                         val="checking"
                         label="Checking"></q-radio>
              </div>
              <div class="col">
                <q-radio v-model="type"
                         checked-icon="mdi-checkbox-marked-circle"
                         unchecked-icon="mdi-checkbox-blank-circle-outline"
                         val="savings"
                         label="Savings"></q-radio>
              </div>
            </div>
          </div>

          <q-field icon="mdi-bank flaticon-bank" :error="$v.name.$error">
            <q-input v-model="name"
                     placeholder="Enter Bank Name"
                     @input="$v.name.$touch"></q-input>
          </q-field>

          <q-field icon="mdi-card-bulleted-outline mdi-flip-h flaticon-bank-1"
                   :error="$v.routingNumber.$error">
            <q-input v-model="routingNumber"
                     placeholder="Enter Bank Routing Number"
                     @input="$v.routingNumber.$touch"></q-input>
          </q-field>

          <q-field icon="mdi-card-bulleted-settings-outline flaticon-bank-1"
                   :error="$v.accountNumber.$error">
            <q-input v-model="accountNumber"
                     placeholder="Enter Bank Account Number"
                     @input="$v.accountNumber.$touch"></q-input>
          </q-field>

          <div class="bottom-tip">
            By continuing, you agree to let UNIVERSAL TRANSACTION COMPLIANCE send 2 small deposit amounts (less than $1.00 USD) and retrieve them in 1 withdrawal.
          </div>

          <q-btn class="full-width btn-md"
                 color="black"
                 label="Connect"
                 :disable="$v.$invalid"
                 @click="$c.click(submit)"
                 @touchend.native="submit"></q-btn>
        </div>
      </div>
    </q-modal-layout>
  </q-modal>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { request } from '../../../../common'
import { required } from 'vuelidate/lib/validators'

export default {
  name: 'mobile-clf-banks-connect-modal',
  mixins: [
    Singleton
  ],
  data () {
    return {
      type: 'checking',
      name: '',
      routingNumber: '',
      accountNumber: ''
    }
  },
  validations: {
    name: {
      required
    },
    routingNumber: {
      required
    },
    accountNumber: {
      required
    }
  },
  methods: {
    show () {
      this.name = this.routingNumber = this.accountNumber = ''
      this.$v.$reset()
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return
      }
      this.$q.loading.show()
      const resp = await request(`/api/clf/banks/connect`, 'post', {
        type: this.type,
        name: this.name,
        routingNumber: this.routingNumber,
        accountNumber: this.accountNumber
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.$store.dispatch('Bank/init')
        this.$root.$emit('hide-mobile-clf-banks-select-modal')
        this._hide()

        setTimeout(() => {
          this.$root.$emit('show-mobile-clf-banks-verify-modal', resp.data)
        }, 500)
      }
    }
  }
}
</script>

<style lang="scss">
  #mobile-clf-banks-connect-modal {
    .center-box {
      .center-box-inner {
        min-height: 530px;
      }

      h3 {
        margin: 0;
        text-align: center;
        font-size: 20px;
      }

      .types {
        text-align: center;
        max-width: 223px;
        margin: 0 auto 10px;

        img {
          margin-bottom: 5px;
        }
      }

      .bottom-tip {
        font-size: 13px;
        color: #777;
        line-height: 1.5em;
        margin: 10px auto;
      }
    }
  }
</style>
