<template>
  <q-modal class="flex items-center column modal-page modal-transparent"
           maximized
           no-esc-dismiss
           no-backdrop-dismiss
           no-route-dismiss
           :value="visible">
    <q-modal-layout id="mobile-clf-bank-detail-modal">
      <q-toolbar slot="header">
        <q-btn flat round disable></q-btn>
        <q-toolbar-title>Remove Your Bank</q-toolbar-title>
        <q-btn flat round disable></q-btn>
      </q-toolbar>

      <div class="center-box">
        <q-btn round
               class="btn-close"
               text-color="white"
               @click="_hide"
               @touchend.native="_hide"
               color="red-2"
               icon="mdi-close"></q-btn>

        <h4 class="modal-title" v-if="entity.bank && (entity.bank.icon || entity.bank.banner)">
          <img class="icon" :src="entity.bank.icon || entity.bank.banner" alt="">
        </h4>
        <h4 class="modal-title" v-else>
          <img src="../../../../statics/mobile/clf/bank.png" alt="">
        </h4>
        <div class="modal-desc">
          Are you sure that you want to remove this bank account?
        </div>

        <div class="modal-body pt-0">
          <table class="table table-form table-form-xlg">
            <tr v-if="entity.bank">
              <th>Name:</th>
              <td v-text="entity.bank.name + ' - ' + entity.type"></td>
            </tr>
            <tr>
              <th>Type:</th>
              <td v-text="entity.type"></td>
            </tr>
            <tr>
              <th>Account:</th>
              <td v-text="entity.number"></td>
            </tr>
            <tr>
              <th>Status:</th>
              <td v-text="statusText(entity.status)"></td>
            </tr>
          </table>
        </div>

        <div class="row gutter-sm mt-5 full-width">
          <div class="col pl-0">
            <q-btn label="Yes"
                   outline
                   color="primary"
                   class="full-width btn-green-light"
                   @click="$c.click(remove)"
                   @touchend.native="remove"></q-btn>
          </div>
          <div class="col">
            <q-btn label="No"
                   outline
                   color="negative"
                   class="full-width btn-red-light"
                   @click="_hide"
                   @touchend.native="_hide"></q-btn>
          </div>
        </div>
      </div>
    </q-modal-layout>
  </q-modal>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import _ from 'lodash'
import { request } from '../../../../common'

export default {
  name: 'mobile-clf-banks-detail-modal',
  mixins: [
    Singleton
  ],
  methods: {
    statusText (s) {
      if (s === 'verifying') {
        return 'Verification Pending'
      }
      return _.capitalize(s)
    },
    async remove () {
      this.$q.loading.show()
      const resp = await request(`/api/clf/banks/${this.entity.id}/delete`, 'delete')
      this.$q.loading.hide()
      if (resp.success) {
        this.$store.dispatch('Bank/init')
        this.$root.$emit('hide-mobile-clf-banks-verify-modal')
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
#mobile-clf-bank-detail-modal {
  .q-modal-layout-content {
    .center-box {
      width: 88vw;
      min-height: 100px;

      .modal-title {
        margin: 10px;
        text-align: center;

        img {
          max-width: 65vw;
          max-height: 25vw;
        }
      }

      .modal-desc {
        text-align: center;
        font-size: 20px;
        color: #333;
        margin-bottom: 15px;
      }
    }
  }
}
</style>
