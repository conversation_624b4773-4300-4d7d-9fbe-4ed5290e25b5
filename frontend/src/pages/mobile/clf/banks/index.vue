<template>
  <q-page class="mobile_clf_banks_index_page center-box-page">
    <div class="center-box">
      <div class="mt-10 mb-20 font-15 text-center">Select your bank account provider.</div>

      <div class="row gutter-sm">
        <div class="col-6"
             v-for="(item, index) in data"
             :key="index">
          <div class="bank-box"
               v-ripple
               :class="item.status"
               @click="$c.click(() => detail(item))"
               @touchend="$c.touch(() => detail(item))">
            <div class="logo" :style="logoBoxStyle(item)"></div>
            <div class="title">(ending in {{ item.end }})</div>
            <q-icon name="mdi-check-circle" class="status-icon"></q-icon>
          </div>
        </div>

        <div class="col-6">
          <div class="bank-box bank-box-new"
               v-ripple
               @click="$c.click(select)"
               @touchend="$c.touch(select)"
               :class="{inactive: !canAddNewCard}">
            <q-icon name="mdi-plus-circle"></q-icon>
            <div class="title">Link a new bank</div>
          </div>
        </div>
      </div>
    </div>

    <q-inner-loading :visible="loading">
      <q-spinner size="40"></q-spinner>
    </q-inner-loading>

    <DetailModal></DetailModal>
  </q-page>
</template>

<script>
import PageMixin from '../../../../mixins/PageMixin'
import DetailModal from './detail'
import { bindStateReadonly, EventHandlerMixin } from '../../../../common'
import { origin } from '../../../../const'

export default {
  name: 'MobileClfBanksIndex',
  components: {
    DetailModal
  },
  mixins: [
    PageMixin,
    EventHandlerMixin('mobile-clf-banks-index-reload')
  ],
  data () {
    return {
      title: 'Banking Setup'
    }
  },
  computed: {
    ...bindStateReadonly('Bank', 'data'),
    ...bindStateReadonly('Bank', 'canAddNewCard')
  },
  methods: {
    async reload () {
      if (!this.data.length) {
        this.loading = true
      }
      await this.$store.dispatch('Bank/init')
      this.loading = false
    },
    logoBoxStyle (item) {
      const s = {
        borderColor: item.border,
        backgroundImage: `url('${origin}/static/img/clf/bank.png')`,
        backgroundSize: '55%'
      }
      if (item.bank) {
        if (item.bank.banner) {
          s.backgroundImage = `url('${item.bank.banner}')`
          s.backgroundSize = 'contain'
        }
        if (item.bank.icon) {
          s.backgroundImage = `url('${item.bank.icon}')`
          s.backgroundSize = '90%'
        }
        if (item.bank.name === 'Wells Fargo') {
          s.backgroundSize = 'cover'
          s.borderColor = '#C30229'
        }
      }
      return s
    },
    detail (item) {
      if (item.status === 'Confirmed') {
        this.$root.$emit('show-mobile-clf-banks-detail-modal', item)
      } else {
        this.$root.$emit('show-mobile-clf-banks-verify-modal', item)
      }
    },
    select () {
      if (!this.canAddNewCard) {
        try {
          this.$store.dispatch('Bank/initAll')
        } catch (e) {}
        return
      }
      this.$root.$emit('show-mobile-clf-banks-select-modal')
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
  .mobile_clf_banks_index_page {
    .center-box {
      padding: 15px !important;
      justify-content: flex-start !important;
    }

    .bank-box {
      border-radius: 6px;
      border: 1px solid #DDD;
      text-align: center;
      padding: 6px;
      position: relative;

      &.Confirmed {
        border-color: #388E3C;

        .status-icon {
          display: flex;
        }
      }

      &.bank-box-new {
        border-color: #666;
        background: #eee;
        height: calc(25vw + 32px);
        display: flex;
        flex-direction: column;
        justify-content: center;

        .mdi-plus-circle {
          font-size: 40px;
        }

        .title {
          color: #333;
          margin-top: 12px;
        }

        &.inactive {
          opacity: 0.2;
        }
      }

      &:active {
        background: #ddd;
      }

      .logo {
        height: 25vw;
        background-size: contain;
        background-position: center center;
        background-repeat: no-repeat;
      }

      .title {
        color: #999;
      }

      .status-icon {
        position: absolute;
        right: 8px;
        top: 8px;
        color: #388E3C;
        font-size: 20px;
        display: none;
      }
    }
  }
</style>
