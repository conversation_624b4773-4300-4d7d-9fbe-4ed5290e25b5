<template>
  <q-modal class="flex items-center column modal-page center-box-modal-page"
           maximized
           no-esc-dismiss
           no-backdrop-dismiss
           no-route-dismiss
           :value="visible">
    <q-modal-layout id="mobile-clf-banks-select-modal">
      <q-toolbar slot="header">
        <q-btn flat round
               class="btn-back"
               @click="_hide"
               @touchend.native="_hide"
               icon="mdi-chevron-left"></q-btn>
        <q-toolbar-title>Select Your Bank</q-toolbar-title>
        <q-btn flat round disable></q-btn>
      </q-toolbar>

      <div class="center-box">
        <div class="mv-15 font-15 text-center">Select your bank account provider.</div>

        <div class="row gutter-xs">
          <div class="col-4" v-for="(item, i) in all" :key="i">
            <div class="bank-box"
                 v-ripple
                 :style="logoBoxStyle(item)"
                 @click="$c.click(() => select(item))"
                 @touchend="$c.touch(() => select(item))"></div>
          </div>

          <div class="col-4">
            <div class="bank-box bank-box-new"
                 v-ripple
                 @click="$c.click(link)"
                 @touchend="$c.touch(link)">
              I have a different bank account
            </div>
          </div>
        </div>
      </div>

      <q-inner-loading :visible="loading">
        <q-spinner size="40"></q-spinner>
      </q-inner-loading>
    </q-modal-layout>
  </q-modal>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { bindStateReadonly } from '../../../../common'

export default {
  name: 'mobile-clf-banks-select-modal',
  mixins: [
    Singleton
  ],
  computed: {
    ...bindStateReadonly('Bank', 'all')
  },
  methods: {
    logoBoxStyle (item) {
      return {
        backgroundImage: `url('${item.banner}')`
      }
    },
    async show () {
      this.loading = true
      try {
        await this.$store.dispatch('Bank/initAll')
      } catch (e) {
        this._hide()
      }
      this.loading = false
    },
    select (item) {
      this.$root.$emit('show-mobile-clf-banks-add-modal', item)
    },
    link () {
      this.$root.$emit('show-mobile-clf-banks-connect-modal')
    }
  }
}
</script>

<style lang="scss">
  #mobile-clf-banks-select-modal {
    .center-box {
      justify-content: flex-start;

      .bank-box {
        position: relative;
        height: 19vw;
        padding: 4px;
        border: 1px solid #ddd;
        border-radius: 6px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;

        &:active {
          border-color: var(--q-color-primary);
        }

        &.bank-box-new {
          display: flex;
          align-items: center;
          text-align: center;
          justify-content: center;
          color: #09619c;
          font-size: 12px;
          font-weight: 600;
          padding: 0;

          &:active {
            border-color: #09619c;
            background: rgba(9, 97, 156, 0.17);
          }
        }
      }
    }
  }
</style>
