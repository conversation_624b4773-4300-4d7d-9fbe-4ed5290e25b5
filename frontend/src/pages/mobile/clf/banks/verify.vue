<template>
  <q-modal class="flex items-center column modal-page center-box-modal-page"
           maximized
           no-esc-dismiss
           no-backdrop-dismiss
           no-route-dismiss
           :value="visible">
    <q-modal-layout id="mobile-clf-banks-verify-modal">
      <q-toolbar slot="header">
        <q-btn flat round
               class="btn-back"
               @click="_hide"
               @touchend.native="_hide"
               icon="mdi-chevron-left"></q-btn>
        <q-toolbar-title>Verify Your Bank</q-toolbar-title>
        <q-btn flat round disable></q-btn>
      </q-toolbar>

      <div class="center-box">
        <div class="center-box-inner">
          <h3>
            <q-icon name="mdi-alert flaticon-attention" class="mr-5"></q-icon>
            Verification Required
          </h3>

          <p class="mb-10">In order to begin using your account, you must first verify that you are the bank account holder. Two small deposit amounts will be made into the below bank account:</p>

          <table class="table-form table-form-lg">
            <tr>
              <th width="100">Bank Account:</th>
              <td>{{ entity.name }}</td>
            </tr>
            <tr>
              <th>Date Range:</th>
              <td>{{ `sent between ${entity.verify_start_at} thru ${entity.verify_end_at}` }}</td>
            </tr>
          </table>

          <p>Please provide the <strong>2 small deposit amounts</strong> sent from <strong>UNIVERSAL TRANSACTION COMPLIANCE</strong> in your banks online banking transaction activity:</p>

          <q-field icon="mdi-cash-usd flaticon-statement"
                   :error="$v.deposit_1.$error">
            <q-input v-model="deposit_1"
                     placeholder="Deposit #1"
                     type="number"
                     prefix="$"
                     @input="$v.deposit_1.$touch"></q-input>
          </q-field>
          <q-field icon="mdi-cash-usd flaticon-statement"
                   :error="$v.deposit_2.$error">
            <q-input v-model="deposit_2"
                     placeholder="Deposit #2"
                     type="number"
                     prefix="$"
                     @input="$v.deposit_2.$touch"></q-input>
          </q-field>

          <q-btn class="full-width btn-md mt-20"
                 color="black"
                 label="Verify"
                 :disable="$v.$invalid"
                 @click="$c.click(submit)"
                 @touchend.native="submit"></q-btn>

          <q-btn flat
                 class="full-width"
                 text-color="blue-10"
                 no-caps
                 @click="$c.click(remove)"
                 @touchend.native="remove"
                 label="Need to start over?"></q-btn>
        </div>
      </div>
    </q-modal-layout>
  </q-modal>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { request } from '../../../../common'
import { required } from 'vuelidate/lib/validators'

export default {
  name: 'mobile-clf-banks-verify-modal',
  mixins: [
    Singleton
  ],
  data () {
    return {
      deposit_1: '',
      deposit_2: ''
    }
  },
  validations: {
    deposit_1: {
      required
    },
    deposit_2: {
      required
    }
  },
  methods: {
    show () {
      this.deposit_1 = this.deposit_2 = ''
      this.$v.$reset()
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return
      }
      this.$q.loading.show()
      const resp = await request(`/api/clf/banks/${this.entity.id}/verify`, 'post', {
        deposit_1: this.deposit_1,
        deposit_2: this.deposit_2
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.$store.dispatch('Bank/init')
        this.$root.$emit('hide-mobile-clf-banks-select-modal')
        this._hide()

        const balance = this.$store.state.User.account.balance
        if (!balance || !parseInt(balance)) {
          setTimeout(() => {
            this.$root.$emit('show-mobile-clf-wallet-load-modal', { type: 'Load' })
          }, 500)
        }
      }
    },
    remove () {
      this.$root.$emit('show-mobile-clf-banks-detail-modal', this.entity)
    }
  }
}
</script>

<style lang="scss">
  #mobile-clf-banks-verify-modal {
    .center-box {
      .center-box-inner {
        min-height: 530px;
      }

      h3 {
        margin: 10px auto 0;
        text-align: center;
        font-size: 20px;
        color: #BC162C;

        .q-icon {
          color: #BC162C !important;

          &:before {
            margin-left: 0;
            margin-top: -4px;
          }
        }
      }

      p {
        color: #444;
        font-weight: 300;

        strong {
          font-weight: 600;
        }
      }

      .table-form {
        th {
          font-weight: 600 !important;
        }

        td {
          font-weight: 300 !important;
        }
      }
    }
  }
</style>
