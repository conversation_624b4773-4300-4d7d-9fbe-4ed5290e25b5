<template>
  <q-modal class="flex items-center column modal-page modal-transparent"
           maximized
           no-esc-dismiss
           no-backdrop-dismiss
           no-route-dismiss
           :value="visible">
    <q-modal-layout id="mobile-clf-dispensary-locator-detail-modal">
      <q-toolbar slot="header">
        <q-btn flat round
               class="btn-back"
               @click="_hide"
               @touchend.native="_hide"
               icon="mdi-chevron-left"></q-btn>
        <q-toolbar-title>Store Locator</q-toolbar-title>
        <q-btn flat round disable></q-btn>
      </q-toolbar>

      <div class="center-box">
        <q-btn round
               class="btn-close"
               text-color="white"
               @click="_hide"
               @touchend.native="_hide"
               color="red-2"
               icon="mdi-close"></q-btn>

        <div class="meta-row">
          <div class="icon">
            <img :src="entity.icon" alt="">
          </div>
          <div class="info">
            <div class="title">{{ entity.name }}</div>
            <div class="desc mb-5"
                 v-if="entity.hours && entity.hours[0]"
                 v-text="entity.openUntilText"></div>
            <div class="desc" v-if="entity.address">
              <span v-text="entity.address.address1"></span>
              <span v-if="entity.address.address2" v-text="', ' + entity.address.address2"></span>
            </div>
            <div class="desc" v-text="addressCityLine"></div>
            <div class="distance" v-text="entity.distance + ' away from you'"></div>
          </div>
        </div>

        <div class="row actions" v-if="entity.address">
          <a class="col-6" :href="'tel:' + entity.address.phone"
            @click="$c.click(callPhone)"
            @touchend="callPhone">
            <img src="../../../../statics/mobile/clf/phone-symbol-of-an-auricular-inside-a-circle.svg" alt="">
            <span v-text="entity.address.phone"></span>
          </a>
          <a class="col-6" :href="'geo:' + entity.address.latitude + ',' + entity.address.longitude"
            @click="$c.click(openMap)"
            @touchend="openMap">
            <img src="../../../../statics/mobile/clf/sports-car.svg" alt="">
            <span>Get Directions</span>
          </a>
        </div>

        <h4 class="modal-body-sub-title">
          Hours
          <span class="text-light ml-3"
                v-if="entity.address && entity.address.timezoneAbbr"
                v-text="'(' + entity.address.timezoneAbbr + ')'"></span>
        </h4>
        <div class="row hours-row" v-for="(hour, day) in entity.hours" :key="day">
          <div class="col-xs-5" v-text="hour.format.weekday"></div>
          <div class="col-xs-7" v-text="hour.format.description"></div>
        </div>

      </div>
    </q-modal-layout>
  </q-modal>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { PhoneNumberUtil, PhoneNumberFormat } from 'google-libphonenumber'

export default {
  name: 'mobile-clf-dispensary-locator-detail-modal',
  mixins: [
    Singleton
  ],
  computed: {
    addressCityLine () {
      const entity = this.entity
      if (!entity.address) {
        return ''
      }
      return entity.address.city + ', ' +
        (entity.address.state ? entity.address.state.name : '') + ' ' +
        entity.address.postalCode
    },
    address () {
      return this.entity.address
    }
  },
  methods: {
    callPhone () {
      if (window.cordova) {
        const phoneUtil = PhoneNumberUtil.getInstance()
        const number = phoneUtil.parseAndKeepRawInput(this.address.phone, 'US')
        const format = phoneUtil.format(number, PhoneNumberFormat.E164)
        window.cordova.InAppBrowser.open(`tel:${format}`, '_system')
      }
    },
    openMap () {
      if (window.cordova) {
        const geo = `${this.address.latitude},${this.address.longitude}`
        let url = `geo:${geo}`
        if (window.device.platform === 'iOS') {
          let address = `${this.address.address1}`
          if (this.address.address2) {
            address += `, ${this.address.address2}`
          }
          address += `, ${this.address.city}, ${this.address.state.name}`
          // url = `maps://?daddr=${address}`
          url = `maps://?ll=${geo}&address=${address}`
        } else if (window.device.platform === 'Android') {
          url = `geo:0,0?q=${geo}(${this.entity.name})`
        }
        console.log(url)
        window.cordova.InAppBrowser.open(url, '_system')
      }
    }
  }
}
</script>

<style lang="scss">
#mobile-clf-dispensary-locator-detail-modal {
  .q-modal-layout-content {
    .center-box {
      width: 90vw;
      min-height: 100px;
      padding: 15px;

      .meta-row {
        display: flex;
        margin-top: 10px;

        .icon {
          width: 70px;
          padding-top: 8px;
        }

        .info {
          margin-left: 10px;

          .title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 5px;
          }

          .desc {
            color: #888;
          }

          .distance {
            margin-top: 5px;
            font-size: 12px;
            color: #bbb;
          }
        }
      }

      .actions {
        text-align: center;
        font-size: 18px;
        margin: 15px auto;

        a {
          color: #0D47A1 !important;
          text-decoration: none;
          padding: 6px 0;
          border-radius: 8px;
          display: flex;
          justify-content: flex-start;
          align-items: center;

          img {
            color: #888;
            fill: #888;
            stroke: #888;
            width: 22px;
            height: 22px;
            margin-right: 3px;
            vertical-align: middle;
          }

          &:active {
            color: rgb(2, 47, 114) !important;
            background: #eee;
          }
        }
      }

      .modal-body-sub-title {
        color: #666;
        margin: 0 0 8px;
        border-bottom: 1px solid #ddd;
      }

      .hours-row {
        color: #616161;
        line-height: 1.7em;
        font-weight: bold;

        + .hours-row {
          font-weight: normal;
        }
      }
    }
  }
}
</style>
