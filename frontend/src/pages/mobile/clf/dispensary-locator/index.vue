<template>
  <q-page class="mobile_clf_dispensary_locator_index_page center-box-page">
    <div class="center-box">
      <q-input v-model="keyword"
               :before="[{icon: 'mdi-magnify', handler: () => reload()}]"
               :after="searchAfter"
               @keydown.enter="reload()"
               @clear="reload()"
               clearable
               class="top-search-bar"
               placeholder="Find a dispensary"></q-input>

      <div id="dispensary-locator-map"></div>

      <PullToRefresh :loading="loading"
                     @reload="reload(1, null)">
        <q-infinite-scroll ref="infiniteScroll"
                           inline
                           v-show="items.length || loading"
                           :handler="reload">
          <q-list no-border separator>
            <q-item v-for="(t, i) in items"
                    link
                    @click.native="select(t)"
                    :key="i">
              <q-item-side>
                <img :src="t.icon || '/static/img/clf/dispensary.png'" alt="">
              </q-item-side>
              <q-item-main>
                <q-item-tile label>{{ t.name }}</q-item-tile>
                <q-item-tile sublabel>{{ t.openUntilText }}</q-item-tile>
                <q-item-tile sublabel>{{ t.distance + ' miles away' }}</q-item-tile>
              </q-item-main>
              <q-item-side right>
                <q-btn round flat
                       @click.stop="showDetail(t)"
                       icon="mdi-information-outline"></q-btn>
              </q-item-side>
            </q-item>
          </q-list>

          <q-spinner :size="30" slot="message"></q-spinner>
        </q-infinite-scroll>

        <div class="list-msg" v-if="position && typeof(position) === 'object' && !loading && items.length <= 0">
          <div>No data matches.</div>
        </div>
      </PullToRefresh>

      <q-inner-loading :visible="listening" class="listening">
        <q-spinner size="50"></q-spinner>
        <div class="mt-20 font-20">
          <q-icon name="mdi-microphone" class="mr-6 font-30"></q-icon>
          Listening...
        </div>
        <q-btn class="mt-30 rd-6"
               @click="stopListening"
               :color="ios ? 'primary': 'white'"
               :label="ios ? 'Done' : 'Cancel'"></q-btn>
      </q-inner-loading>
    </div>

    <detail></detail>
  </q-page>
</template>

<script>
import PageMixin from '../../../../mixins/PageMixin'
import Detail from './detail'
import GoogleMapsLoader from 'google-maps'
import moment from 'moment'
import PullToRefresh from '../../../../components/PullToRefresh'
import { request } from '../../../../common'

GoogleMapsLoader.KEY = 'AIzaSyCtjFRds8BqTZtZjeULsaQkDKRaIW16aCw'

export default {
  name: 'MobileClfDispensaryLocatorIndex',
  components: {
    Detail,
    PullToRefresh
  },
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'Store Locator',
      all: [],
      items: [],
      keyword: null,
      recognition: null,
      listening: false,
      map: null,
      marker: null,
      position: null,
      ios: false,
      page: 1,
      pageSize: 10,
      selected: null
    }
  },
  computed: {
    searchAfter () {
      const all = []
      if (this.recognition) {
        all.push({
          icon: 'mdi-microphone',
          handler: this.startListening
        })
      }
      return all
    }
  },
  watch: {
    selected () {
      this.moveMap(this.selected)
    }
  },
  methods: {
    async reload (page = 1, done = null) {
      if (page === 1) {
        this.loading = true
        const resp = await request(`/api/clf/dispensary-locator/search`, 'get', {
          keyword: this.keyword,
          timezone: moment.tz.guess(),
          position: this.position
        })
        this.loading = false
        if (resp.success) {
          this.all = resp.data
          this.items = []
        }
        this.$refs.infiniteScroll.resume()
        this.$refs.infiniteScroll.index = 1
      }
      this.loadData(page)
      if (done) {
        done(this.items.length >= this.all.length)
      }
    },
    loadData (page = 1) {
      if (page > Math.ceil(this.all.length / this.pageSize)) {
        return
      }
      this.page = page

      const start = (this.page - 1) * this.pageSize
      this.items = this.all.slice(start, start + this.pageSize)

      if (this.items.length) {
        this.selected = this.items[0]
      } else {
        this.selected = null
      }
    },
    select (item) {
      this.selected = item
    },
    moveMap (item) {
      if (!item) {
        item = {
          name: null,
          address: {
            latitude: 40.7549104,
            longitude: -120.033127
          }
        }
      }
      if (this.map) {
        const center = {
          lat: item.address.latitude,
          lng: item.address.longitude
        }
        this.map.setCenter(center)

        if (this.marker) {
          if (item.name) {
            this.marker.setTitle(item.name)
            this.marker.setPosition(center)
            this.marker.setMap(this.map)
          } else {
            this.marker.setMap(null)
          }
        }
      }
    },
    showDetail (item) {
      this.$root.$emit('show-mobile-clf-dispensary-locator-detail-modal', item)
    },
    cordovaStartListening () {
      this.recognition.startListening(keywords => {
        console.log(keywords)
        if (keywords && keywords.length) {
          this.stopListening()

          if (!this.keyword) {
            this.keyword = keywords[0]
          } else {
            this.keyword = `${this.keyword} ${keywords[0]}`
          }

          this.reload()
        }
      }, e => {
        if (!this.ios && e && (e === '0' || e.indexOf('No Activity found to handle Intent') >= 0)) {
          this.$q.dialog({
            title: 'Error',
            message: 'The store locator can use Google Speech Recognition Services to help you find navigation directions to nearest dispensary faster. Do you wish to install support for this capability? ',
            cancel: true
          }).then(() => {
            window.cordova.InAppBrowser.open(' https://market.android.com/details?id=com.google.android.googlequicksearchbox', '_system')
          }).catch(() => {})
        }
        this.stopListening()
      })
    },
    startListening () {
      this.listening = true
      if (!this.recognition) {
        this.$q.notify('Sorry that your device does not support speech recognition.')
        return
      }
      if (window.cordova) {
        this.recognition.hasPermission(has => {
          if (has) {
            this.cordovaStartListening()
          } else {
            this.requestPermission()
          }
        }, () => {
          this.requestPermission()
        })
        return
      }
      this.recognition.start()
    },
    requestPermission () {
      this.recognition.requestPermission(() => {
        this.cordovaStartListening()
      }, () => {
        this.$q.notify('Sorry that you have to grant the permission to microphone to recognize speech.')
      })
    },
    stopListening () {
      this.listening = false
      if (!this.recognition) {
        return
      }
      if (window.cordova) {
        this.recognition.stopListening()
        return
      }
      this.recognition.stop()
    },
    initMap () {
      const el = document.querySelector('#dispensary-locator-map')
      this.map = new window.google.maps.Map(el, {
        zoom: 10,
        mapTypeControl: false,
        streetViewControl: false
      })
      this.moveMap()

      this.marker = new window.google.maps.Marker({
        animation: window.google.maps.Animation.DROP
      })

      this.marker.addListener('click', () => {
        this.showDetail(this.selected)
      })
    },
    initPosition () {
      window.navigator.geolocation.getCurrentPosition(pos => {
        this.position = pos.coords
        this.reload()
      }, err => {
        this.position = `${err.message || err}`
        this.reload()
      })
    },
    initSpeechRecognition () {
      if (window.cordova) {
        window.plugins.speechRecognition.isRecognitionAvailable(available => {
          if (available) {
            this.recognition = window.plugins.speechRecognition
          }
        })
        return
      }

      try {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
        const recognition = new SpeechRecognition()

        recognition.onstart = () => {
          this.listening = true
        }

        recognition.onspeechend = () => {
          this.listening = false
        }

        recognition.onerror = (event) => {
          this.listening = false
          this.$q.notify(event.error === 'no-speech' ? 'No speech was detected' : event.error)
        }

        recognition.onresult = (event) => {
          this.listening = false

          var current = event.resultIndex
          var transcript = event.results[current][0].transcript
          if (!this.keyword) {
            this.keyword = transcript
          } else {
            this.keyword = `${this.keyword} ${transcript}`
          }

          this.reload()
        }

        this.recognition = recognition
      } catch (e) {
        console.log(`Your device doesn't support speech recognition: ${e.message}`)
      }
    }
  },
  mounted () {
    if (window.cordova && window.device.platform === 'iOS') {
      this.ios = true
    }

    GoogleMapsLoader.load(google => {
      window.google = google
      this.initMap()
    })

    this.initPosition()
    this.initSpeechRecognition()
  }
}
</script>

<style lang="scss">
  .mobile_clf_dispensary_locator_index_page {
    .top-search-bar {
      z-index: 1;
      background: #eee;
      border-radius: 8px;
      padding: 8px;

      &:before, &:after {
        display: none;
      }
    }

    #dispensary-locator-map {
      height: 55vw;
      z-index: 1;
      background: white;
      margin-top: 10px;
    }

    .tc_pull_refresh {
      flex: 1;
      margin-top: 10px;
      height: calc(100vh - 180px - 55vw - var(--safe-area-inset-top));

      .q-infinite-scroll, .list-msg {
        height: calc(100vh - 165px - 55vw - var(--safe-area-inset-top));
      }

      .list-msg {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 18px;
        flex-direction: column;
        color: #888;
        margin-top: 0;

        .q-icon {
          font-size: 40px;
          display: block;
          margin-top: -10px;
          margin-bottom: 6px;
        }
      }

      .q-list {
        padding: 0;

        .q-item {
          padding-right: 0;
        }

        .q-item-side-left {
          width: 60px;
        }

        .q-item-main {
          .q-item-label {
            font-size: 17px;
            font-weight: 600;
            margin-top: -5px;
          }
        }

        .q-item-side-right {
          .q-btn {
            width: auto;
            height: auto;
            padding: 10px;

            .q-icon {
              font-size: 28px;
              color: #aaa;
            }
          }
        }
      }
    }

    .q-inner-loading.listening {
      z-index: 999;
      background: rgba(255, 255, 255, 0.85);
    }
  }
</style>
