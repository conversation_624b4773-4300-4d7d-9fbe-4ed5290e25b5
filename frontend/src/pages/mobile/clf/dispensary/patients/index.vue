<template>
  <q-page id="mobile__clf__patients__index_page" class="clf_page clf_mobile_list_page clf_mobile_list_page_index">
    <div class="page-header" :class="{visible: filters}">
      <div class="fun-group">
        <q-field label="Keyword" class="search_field">
          <q-input v-model="keyword"
                   clearable
                   placeholder="Name, Patient ID, City, State"
                   autocomplete="off"></q-input>
        </q-field>
        <DateRangeFilter class="mr-20"
                         default="all"
                         ref="dateRangeFilter"></DateRangeFilter>

        <q-btn color="primary"
               class="mt-10"
               label="Reload"
               @click="filters = false; reload(1, null)"></q-btn>
      </div>
    </div>

    <PullToRefresh :loading="loading"
                   @reload="reload(1, null)">
      <q-infinite-scroll ref="infiniteScroll"
                         inline
                         :handler="reload">
        <q-list no-border separator>
          <q-item v-for="(t, i) in patient.data"
                  link
                  @click.native="viewDetail(t)"
                  :key="i">
            <q-item-main>
              <q-item-tile class="item-left">
                <div class="title">{{ t.account.name }}</div>
                <div class="desc">{{ t.user.id }}</div>
                <div class="desc italic">Since: {{ t.since | date }}</div>
              </q-item-tile>
              <q-item-tile class="item-right">
                <div class="title">Total: {{ t.totalSpend | moneyFormat }}</div>
                <div class="desc">Last Purchase: {{ t.lastSpend | moneyFormat }}</div>
                <div class="desc italic">At: {{ t.lastPurchase | date }}</div>
              </q-item-tile>
            </q-item-main>
            <q-item-side right icon="mdi-chevron-right"></q-item-side>
          </q-item>
        </q-list>

        <q-spinner :size="30" slot="message"></q-spinner>

        <div class="list-msg" v-if="!loading && patient.data.length <= 0">
          <div>No data matches.</div>
        </div>
      </q-infinite-scroll>
    </PullToRefresh>

    <PatientDetail></PatientDetail>
  </q-page>
</template>

<script>
import PageMixin from '../../../../../mixins/PageMixin'
import DateRangeFilter from '../../../../../components/DateRangeFilter'
import PatientDetail from '../../../../clf/patients/detail-dialog'
import PullToRefresh from '../../../../../components/PullToRefresh'
import { EventHandlerMixin } from '../../../../../common'

export default {
  name: 'MobileClfPatientIndex',
  mixins: [
    PageMixin,
    EventHandlerMixin('reload-dispensary-patients'),
    EventHandlerMixin('show-mobile-clf-dispensary-patients-filter', 'showFilters')
  ],
  components: {
    DateRangeFilter,
    PatientDetail,
    PullToRefresh
  },
  data () {
    return {
      title: 'Patients',
      keyword: null,
      filters: false
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    },
    patient () {
      return this.$store.state.Patient
    }
  },
  methods: {
    showFilters () {
      this.filters = !this.filters
    },

    getQueryParams () {
      const params = this.$refs.dateRangeFilter.params()
      params.keyword = this.keyword
      return params
    },

    viewDetail (item) {
      this.$root.$emit('show-dispensary-patient-detail-dialog', item)
    },

    async reload (page = 1, done = null) {
      if (page === 1) {
        this.loading = true
      }
      const params = this.getQueryParams()
      params.page = page
      await this.$store.dispatch('Patient/init', params)
      this.loading = false
      if (page === 1) {
        try {
          this.$refs.infiniteScroll.resume()
          this.$refs.infiniteScroll.index = 1
        } catch (e) {
          console.log(e)
        }
      }
      if (done) {
        done(this.patient.data.length >= this.patient.count)
      }
    }
  }
}
</script>

<style lang="scss">
  #mobile__clf__patients__index_page {
  }
</style>
