<template>
  <q-page class="mobile_clf_funds_index_page">
    <PullToRefresh :loading="loading"
                   :check="pullCheck"
                   @reload="reload(1, null)">
      <div class="row balance-row">
        <div class="col-3 text-right">
          <q-btn flat round
                 class="btn-load-unload"
                 @click="$root.$emit('show-mobile-clf-wallet-load-modal', {type: 'Unload'})"
                 icon="mdi-minus-circle"
                 color="negative"></q-btn>
        </div>
        <div class="col balance"
             v-html="c.moneyFormatHtml(user.account.balance)"></div>
        <div class="col-3">
          <q-btn flat round
                 class="btn-load-unload"
                 @click="$root.$emit('show-mobile-clf-wallet-load-modal', {type: 'Load'})"
                 icon="mdi-plus-circle"
                 color="positive"></q-btn>
        </div>
      </div>

      <div class="list-cards-wrap">
        <div class="dummy-header">0 deposits pending - $0.00</div>
        <div class="list-cards" @scroll="onScroll">
          <ListCard type="pending"
                    ref="pending"
                    @loaded="onScroll"></ListCard>
          <ListCard type="withdrawal"
                    ref="withdrawal"
                    @loaded="onScroll"></ListCard>
        </div>
      </div>
    </PullToRefresh>
  </q-page>
</template>

<script>
import PageMixin from '../../../../mixins/PageMixin'
import PullToRefresh from '../../../../components/PullToRefresh'
import { EventHandlerMixin } from '../../../../common'
import ListCard from './list'
import $ from 'jquery'

export default {
  name: 'MobileClfFundsIndex',
  mixins: [
    PageMixin,
    EventHandlerMixin('mobile-clf-funds-index-reload', 'reload')
  ],
  components: {
    ListCard,
    PullToRefresh
  },
  data () {
    return {
      title: 'Funds Transfer'
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  methods: {
    async reload () {
      this.loading = true
      await this.$refs.pending.reload()
      await this.$refs.withdrawal.reload()
      this.loading = false
      this.onScroll()
    },
    onScroll () {
      const wrap = $(this.$el)
      const cards = wrap.find('.mobile_clf_funds_list_card')
      let hideDummy = false
      let title = ''
      cards.each((i, card) => {
        card = $(card)
        const top = card.position().top
        if (top < -card.height()) {
          card.addClass('animated')
        } else {
          card.removeClass('animated')
        }
        if (top >= 0 && top < 28) {
          hideDummy = true
        }
        if (top <= 0) {
          title = card.find('> .q-card-primary').text()
        }
      })

      const dummy = wrap.find('.dummy-header')
      if (hideDummy) {
        dummy.addClass('hidden')
      } else {
        dummy.removeClass('hidden')
      }
      dummy.text(title)
    },
    pullCheck ($event, comp) {
      if ($($event.target).parents('.list-cards').length) {
        if ($event.touches[0].pageY >= comp.currentY) {
          const is = this.$el.querySelector('.list-cards')
          if (is && is.scrollTop > 0) {
            return false
          }
        }
      }
      return true
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
  @import '../../../../css/mobile/common';

  .mobile_clf_funds_index_page {
    box-shadow: none !important;

    .list-cards-wrap {
      position: relative;

      .list-cards {
        max-height: calc(100vh - 127px - var(--safe-area-inset-top)) !important;
        overflow: auto;
        position: absolute;
        width: 100%;
        -webkit-overflow-scrolling: touch;
      }

      .dummy-header {
        z-index: 9;
        width: 100vw;
        height: 28px;
        font-weight: 400;
        line-height: 2rem;
        position: absolute;
        text-transform: uppercase;
        text-align: center;
        background: #E3E3E3;
        font-size: 15px;
        letter-spacing: 3px;
      }
    }
  }

  @media (max-width: 320px) {
    .mobile_clf_funds_index_page {
      .q-card-container {
        .q-card-title {
          letter-spacing: 2px !important;
        }
      }

      .list-cards-wrap {
        .dummy-header {
          letter-spacing: 2px !important;
        }
      }
    }
  }
</style>
