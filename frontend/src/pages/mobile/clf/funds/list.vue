<template>
  <q-card class="mobile_clf_funds_list_card">
    <q-card-title>{{ title }}</q-card-title>
    <q-card-main>
      <div class="text-center">
        <q-spinner :size="30"
                   class="mv-10"
                   v-if="page === 1 && loading"></q-spinner>
      </div>

      <q-list no-border separator>
        <q-item v-for="(t, i) in transaction"
                link
                @click.native="viewDetail(t)"
                :key="i">
          <q-item-side>
            <q-icon class="replaced-icon"
                    :class="transactionClass(t)"
                    :name="transactionIcon(t)"></q-icon>
          </q-item-side>
          <q-item-main>
            <q-item-tile class="item-left">
              <div class="title">{{ t.description }}</div>
              <div class="desc">{{ t.date }}</div>
              <div class="desc italic">{{ t.subDescription }}</div>
            </q-item-tile>
            <q-item-tile class="item-right flex items-center" v-if="t.status === 'Completed'">
              <div class="amount" :class="transactionClass(t)">{{ transactionAmount(t) }}</div>
            </q-item-tile>
            <q-item-tile class="item-right" v-else>
              <div class="amount" :class="transactionClass(t)">{{ transactionAmount(t) }}</div>
              <div class="status italic">{{ t.status }}</div>
            </q-item-tile>
          </q-item-main>
          <q-item-side right icon="mdi-chevron-right"></q-item-side>
        </q-item>
      </q-list>

      <div class="text-center">
        <q-spinner :size="30"
                   class="mv-10"
                   v-if="page > 1 && loading"></q-spinner>
        <q-btn flat
               size="sm"
               icon="mdi-refresh"
               v-else-if="type === 'withdrawal' && transaction.length < fund.count"
               label="Load more"
               @touchend.native="reload(false)"
               @click="$c.click(() => reload(false))"></q-btn>
        <template v-else-if="type === 'pending' && fund.count > 3">
          <q-btn flat
                 size="sm"
                 icon="mdi-chevron-down"
                 v-if="!showAll"
                 label="Show all"
                 @click="showAll = true"></q-btn>
          <q-btn flat
                 size="sm"
                 icon="mdi-chevron-up"
                 v-else
                 label="Show less"
                 @click="showAll = false"></q-btn>
        </template>
      </div>

      <div class="list-msg mb-20" v-if="!loading && transaction.length <= 0">
        <div>No data matches.</div>
      </div>
    </q-card-main>
  </q-card>
</template>

<script>
import TransactionMixin from '../../../../mixins/mobile/clf/TransactionMixin'
import { moneyFormat } from '../../../../common'
import _ from 'lodash'

export default {
  name: 'MobileClfFundsList',
  mixins: [
    TransactionMixin
  ],
  props: {
    type: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      loading: false,
      page: 1,
      size: 10,
      showAll: false
    }
  },
  computed: {
    fund () {
      return this.$store.state.Fund[this.type]
    },
    transaction () {
      if (this.type === 'pending') {
        if (!this.showAll) {
          return this.fund.data.slice(0, 3)
        }
      }
      return this.fund.data
    },
    title () {
      if (this.type === 'pending') {
        const sum = _.reduce(this.fund.data, (sum, t) => sum + t.amountValue, 0)
        return `${this.fund.count} deposits pending - ${moneyFormat(sum)}`
      }
      if (this.type === 'withdrawal') {
        return 'Recent Withdrawals'
      }
      return 'Transactions'
    }
  },
  methods: {
    viewDetail (t) {
      this.$root.$emit('show-mobile-clf-wallet-detail-modal', t)
    },
    async reload (reset = true) {
      if (reset) {
        this.page = 1
      } else {
        this.page++
      }
      this.loading = true
      await this.$store.dispatch(`Fund/init${_.upperFirst(this.type)}`, {
        page: this.page,
        size: this.size
      })
      this.loading = false
      this.$emit('loaded', this.title)
    }
  }
}
</script>

<style lang="scss">
  .mobile_clf_funds_list_card {
    margin-top: 0 !important;
    position: relative;
    padding-top: 28px;

    > .q-card-primary {
      position: absolute;
      width: 100%;
      z-index: 8;
      top: 0;
    }

    &.animated {
      > .q-card-primary {
        top: auto;
        bottom: 0;
      }
    }
  }
</style>
