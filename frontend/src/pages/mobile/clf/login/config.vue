<template>
  <q-page class="mobile_clf_login_page">
    <div class="form">
      <img class="logo" src="../../../../statics/mobile/clf/company_logo.png" alt="">
      <h3>{{ title }}</h3>
      <q-field icon="mdi-network"
               label="Server URL"
               :error="$v.url.$error">
        <q-input v-model="url"
                 autocapitalize="off"
                 @blur="$v.url.$touch"></q-input>

        <div class="q-field-label mt-10">Input or choose one from below:</div>

        <q-select v-model="de"
                  @input="selectedDefault"
                  :options="defaultUrls"></q-select>
      </q-field>
      <q-btn color="black" label="Save" @click="submit"></q-btn>
      <q-btn color="faded" flat @click="$router.go(-1)" label="Back"></q-btn>
    </div>
  </q-page>
</template>

<script>
import PageMixin from '../../../../mixins/PageMixin'
import { required } from 'vuelidate/lib/validators'
import { origin, updateOrigin } from '../../../../const'

export default {
  name: 'MobileClfLoginConfig',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'Configuration',
      url: origin,
      de: null,
      defaultUrls: ['https://utc.virtualcards.us', 'https://utc.staging.virtualcards.us', 'https://clf.virtualcards.us', 'https://clf.staging.virtualcards.us', 'http://localhost:8080'].map(v => ({
        label: v,
        value: v
      }))
    }
  },
  validations: {
    url: {
      required
    }
  },
  methods: {
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return
      }
      this.save(this.url)
    },

    save (url) {
      if (!url) {
        return
      }
      this.url = url
      window.origin = url
      window.localStorage.setItem('utc_server', url)
      updateOrigin(url)
      this.$q.notify({
        message: 'Configuration saved!',
        color: 'positive'
      })
      this.$router.replace('/clf/login')
    },

    selectedDefault () {
      if (!this.de) {
        return
      }
      this.url = this.de
      this.de = null
    }
  }
}
</script>

<style lang="scss">
  @import '../../../../css/mobile/clf';
</style>
