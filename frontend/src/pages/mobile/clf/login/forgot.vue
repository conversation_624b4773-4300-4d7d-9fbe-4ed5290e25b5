<template>
  <q-page class="mobile_clf_login_page">
    <div class="form">
      <img class="logo" src="../../../../statics/mobile/clf/company_logo.png" alt="">
      <h3>Reset your password</h3>
      <p class="tip">Enter your username or email address to reset the password.</p>
      <q-field icon="mdi-account-outline flaticon-avatar" :error="$v.email.$error">
        <q-input v-model="email"
                 autocapitalize="off"
                 @blur="$v.email.$touch"
                 placeholder="Username or email address"></q-input>
      </q-field>
      <q-btn color="black" label="Reset Password" @click="submit"></q-btn>
      <q-btn color="faded" flat @click="$router.go(-1)" label="Back"></q-btn>
    </div>
  </q-page>
</template>

<script>
import PageMixin from '../../../../mixins/PageMixin'
import { required } from 'vuelidate/lib/validators'
import { request, notifySuccess, isDebugging } from '../../../../common'

export default {
  name: 'MobileClfLoginForgot',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'Forgot Password',
      email: null
    }
  },
  validations: {
    email: {
      required
    }
  },
  methods: {
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return
      }
      this.$q.loading.show()
      const resp = await request(`/api/auth/forgot-password`, 'post', {
        username: this.email
      })
      this.$q.loading.hide()
      if (resp.success) {
        notifySuccess('An email has been sent. It contains a link you must click to reset your password.')
        this.$router.replace('/clf/login')
      }
    }
  },
  mounted () {
    if (isDebugging()) {
      this.email = '<EMAIL>'
    }
  }
}
</script>

<style lang="scss">
  @import '../../../../css/mobile/clf';
</style>
