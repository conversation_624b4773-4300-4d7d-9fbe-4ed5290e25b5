<template>
  <q-page class="mobile_clf_login_page">
    <div class="form">
      <img class="logo" src="../../../../statics/mobile/clf/company_logo.png" alt="">
      <h2 class="mt-10">Welcome</h2>
      <p class="tip">Please enter your login and password to access your account. Remember, your password is case sensitive.</p>
      <q-field icon="mdi-account-outline flaticon-avatar" :error="$v.email.$error">
        <q-input v-model="email"
                 autocapitalize="off"
                 @blur="$v.email.$touch"
                 placeholder="Email / Username"></q-input>
      </q-field>
      <q-field icon="mdi-lock-outline flaticon-padlock" :error="$v.password.$error">
        <q-input type="password"
                 autocapitalize="off"
                 v-model="password"
                 @blur="$v.password.$touch"
                 placeholder="Password"></q-input>
      </q-field>
      <div class="q-field text-right">
        <a href="#/clf/login/forgot-password" class="link">Forgot Password?</a>
      </div>
      <q-btn color="black" label="LOG IN" @click="submit"></q-btn>

      <q-btn flat
             size="sm"
             @click="configure"
             class="version">v{{ version }}</q-btn>
    </div>
  </q-page>
</template>

<script>
import PageMixin from '../../../../mixins/PageMixin'
import VersionMixin from '../../../../mixins/VersionMixin'
import { required } from 'vuelidate/lib/validators'
import { request, isDebugging } from '../../../../common'

export default {
  name: 'MobileClfLoginIndex',
  mixins: [
    PageMixin,
    VersionMixin
  ],
  data () {
    return {
      title: 'Login',
      email: null,
      password: null,
      tapped: 0
    }
  },
  validations: {
    email: {
      required
    },
    password: {
      required
    }
  },
  methods: {
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return
      }
      this.$q.loading.show()
      const resp = await request(`/api/auth/login`, 'post', {
        username: this.email,
        password: this.password
      })
      if (resp.success) {
        this.$store.commit('Config/update', resp.data)

        try {
          await this.$store.dispatch('User/init')
          this.$router.replace('/clf')
          this.$q.loading.hide()

          await this.$store.dispatch('Bank/init')
          if (this.$store.state.Bank.data.length <= 0) {
            this.$root.$emit('show-mobile-clf-banks-select-modal')
          }
        } catch (e) {
          this.$q.loading.hide()
        }
      } else {
        this.$q.loading.hide()
      }
    },
    debugInit () {
      // this.email = '<EMAIL>'
      this.email = '<EMAIL>'
      // this.email = '<EMAIL>'
      // this.email = '<EMAIL>'
      // this.email = '<EMAIL>'
      // this.password = 'Span1234'
      // this.password = 'Clf54321'
      this.password = 'Clf12345'
    },
    configure () {
      if (isDebugging() || this.tapped > 10) {
        this.$router.push('/clf/login/config')
      } else {
        this.tapped++
      }
    }
  },
  mounted () {
    this.tapped = 0

    if (window.cordova) {
      document.addEventListener('deviceready', () => {
        if (isDebugging()) {
          this.debugInit()
        }
      }, false)
    } else {
      this.debugInit()
    }
  }
}
</script>

<style lang="scss">
  @import '../../../../css/mobile/clf';

  .mobile_clf_login_page {
    .version {
      margin-top: 8px !important;
      color: #aaa;
      width: 100px !important;
      font-size: 14px !important;
    }
  }
</style>
