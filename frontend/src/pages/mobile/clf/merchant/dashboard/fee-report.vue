<template>
  <q-page class="clf_page clf_mobile_list_page clf_mobile_list_page_index">
    <div class="page-header" :class="{visible: filters}">
      <div class="fun-group">
        <DateRangeFilter class="mr-20"
                         default="all"
                         ref="dateRangeFilter"></DateRangeFilter>

        <q-btn color="primary"
               class="mt-10"
               label="Reload"
               @click="filters = false; reload(1, null)"></q-btn>
      </div>
    </div>

    <PullToRefresh :loading="loading"
                   @reload="reload(1, null)">
      <q-infinite-scroll ref="infiniteScroll"
                         inline
                         :handler="reload">
        <q-list no-border separator>
          <q-item v-for="(t, i) in source.data"
                  link
                  :key="i">
            <q-item-main>
              <q-item-tile class="item-left">
                <div class="title">{{ t.date | date }}</div>
                <div class="desc"># {{ t.tranNumber }}</div>
              </q-item-tile>
              <q-item-tile class="item-right">
                <div class="title">{{ t.totalDeposits | moneyFormat }}</div>
                <div class="desc text-negative">Tran Fee: +{{ t.tranFeeAmount | moneyFormat }}</div>
                <div class="desc text-negative">Tax: +{{ t.taxAmount | moneyFormat }}</div>
              </q-item-tile>
            </q-item-main>
          </q-item>
        </q-list>

        <q-spinner :size="30" slot="message"></q-spinner>

        <div class="list-msg" v-if="!loading && source.data.length <= 0">
          <div>No data matches.</div>
        </div>
      </q-infinite-scroll>
    </PullToRefresh>
  </q-page>
</template>

<script>
import PageMixin from '../../../../../mixins/PageMixin'
import DateRangeFilter from '../../../../../components/DateRangeFilter'
import PullToRefresh from '../../../../../components/PullToRefresh'
import { EventHandlerMixin } from '../../../../../common'

export default {
  name: 'mobile-clf-dashboard-fee-report',
  mixins: [
    PageMixin,
    EventHandlerMixin('show-mobile-clf-merchant-fee-report-filter', 'showFilters')
  ],
  components: {
    DateRangeFilter,
    PullToRefresh
  },
  data () {
    return {
      title: 'Taxes & Fees',
      filters: false
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    },
    source () {
      return this.$store.state.DashboardFee
    }
  },
  methods: {
    showFilters () {
      this.filters = !this.filters
    },

    getQueryParams () {
      return this.$refs.dateRangeFilter.params()
    },

    async reload (page = 1, done = null) {
      if (page === 1) {
        this.loading = true
      }
      const params = this.getQueryParams()
      params.page = page
      await this.$store.dispatch('DashboardFee/init', params)
      this.loading = false
      if (page === 1) {
        try {
          this.$refs.infiniteScroll.resume()
          this.$refs.infiniteScroll.index = 1
        } catch (e) {
          console.log(e)
        }
      }
      if (done) {
        done(this.source.data.length >= this.source.count)
      }
    }
  }
}
</script>
