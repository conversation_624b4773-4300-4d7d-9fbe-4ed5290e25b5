<template>
  <q-page id="mobile__clf__dues__index_page" class="clf_page clf_mobile_list_page clf_mobile_list_page_index">
    <div class="page-header" :class="{visible: filters}">
      <div class="fun-group">
        <DateRangeFilter class="mr-20"
                         default="all"
                         ref="dateRangeFilter"></DateRangeFilter>

        <q-field label="Status" class="search_field">
          <q-select v-model="status" :options="statuses"></q-select>
        </q-field>

        <q-btn color="primary"
               class="mt-10"
               label="Reload"
               @click="filters = false; reload(1, null)"></q-btn>
      </div>
    </div>

    <PullToRefresh :loading="loading"
                   @reload="reload(1, null)">
      <q-infinite-scroll ref="infiniteScroll"
                         inline
                         :handler="reload">
        <q-list no-border separator>
          <q-item v-for="(t, i) in due.data"
                  link
                  @click.native="viewDetail(t)"
                  :key="i">
            <q-item-main>
              <q-item-tile class="item-left">
                <div class="title">{{ t.sender.name }}</div>
                <div class="desc">Received: {{ t.sendAt | date }}</div>
                <div class="desc">Due date: {{ t.dueAt | date }}</div>
              </q-item-tile>
              <q-item-tile class="item-right">
                <div class="title">{{ t.amount | moneyFormat }}</div>
                <div class="desc red" v-if="t.overdue">
                  <q-icon name="warning" class="mr-3"></q-icon> Overdue
                </div>
                <div class="desc" v-else-if="t.status === 'paid'">
                  <div class="font-12 text-primary">{{ t.statusText }}: {{ t.payAt | date('L LT') }}</div>
                </div>
                <div class="desc normal text-dark" v-else>{{ t.statusText }}</div>
                <div class="desc"># {{ t.id }}</div>
              </q-item-tile>
            </q-item-main>
            <q-item-side right icon="mdi-chevron-right"></q-item-side>
          </q-item>
        </q-list>

        <q-spinner :size="30" slot="message"></q-spinner>

        <div class="list-msg" v-if="!loading && due.data.length <= 0">
          <div>No data matches.</div>
        </div>
      </q-infinite-scroll>
    </PullToRefresh>

    <DueDetail></DueDetail>
  </q-page>
</template>

<script>
import PageMixin from '../../../../../mixins/PageMixin'
import DueDetail from '../../../../clf/bills/due-dialog'
import DateRangeFilter from '../../../../../components/DateRangeFilter'
import PullToRefresh from '../../../../../components/PullToRefresh'
import { EventHandlerMixin } from '../../../../../common'

export default {
  name: 'MobileClfDueIndex',
  mixins: [
    PageMixin,
    EventHandlerMixin('reload-merchant-dues'),
    EventHandlerMixin('show-mobile-clf-merchant-dues-filter', 'showFilters')
  ],
  components: {
    DateRangeFilter,
    DueDetail,
    PullToRefresh
  },
  data () {
    return {
      title: 'Incoming Bills',
      status: 'unpaid',
      filters: false
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    },
    due () {
      return this.$store.state.Due
    },
    statuses () {
      return this.cc.clf.billStatuses.filter(v => {
        return v.label !== 'Draft'
      })
    }
  },
  methods: {
    showFilters () {
      this.filters = !this.filters
    },

    getQueryParams () {
      const params = this.$refs.dateRangeFilter.params()
      params.status = this.status
      return params
    },

    viewDetail (item) {
      this.$root.$emit('show-dispensary-bill-due-dialog', item)
    },

    async reload (page = 1, done = null) {
      if (page === 1) {
        this.loading = true
      }
      const params = this.getQueryParams()
      params.page = page
      await this.$store.dispatch('Due/init', params)
      this.loading = false
      if (page === 1) {
        try {
          this.$refs.infiniteScroll.resume()
          this.$refs.infiniteScroll.index = 1
        } catch (e) {
          console.log(e)
        }
      }
      if (done) {
        done(this.due.data.length >= this.due.count)
      }
    }
  }
}
</script>

<style lang="scss">
  #mobile__clf__dues__index_page {
    .item-left {
      .title {
        font-size: 14px;
      }
    }
  }
</style>
