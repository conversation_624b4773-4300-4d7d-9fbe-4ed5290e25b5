<template>
  <q-page id="mobile__clf__employees__index_page" class="clf_page clf_mobile_list_page clf_mobile_list_page_index">
    <div class="page-header" :class="{visible: filters}">
      <div class="fun-group">
        <q-field label="Keyword" class="search_field">
          <q-input v-model="keyword"
                   clearable
                   placeholder="Name, Title, Address"
                   autocomplete="off"></q-input>
        </q-field>

        <q-field label="Status" class="search_field">
          <q-select v-model="status" :options="statuses"></q-select>
        </q-field>

        <q-btn color="primary"
               class="mt-10"
               label="Reload"
               @click="filters = false; reload(1, null)"></q-btn>
      </div>
    </div>

    <PullToRefresh :loading="loading"
                   @reload="reload(1, null)">
      <q-infinite-scroll ref="infiniteScroll"
                         inline
                         :handler="reload">
        <q-list no-border separator>
          <q-item v-for="(t, i) in employee.data"
                  link
                  @click.native="viewDetail(t)"
                  :key="i">
            <q-item-main>
              <q-item-tile class="item-left">
                <div class="title">{{ t.name }}</div>
                <div class="desc">{{ t.address.email }}</div>
              </q-item-tile>
              <q-item-tile class="item-right">
                <div class="title">{{ t.title }}</div>
                <div class="desc">{{ t.address.phone }}</div>
              </q-item-tile>
            </q-item-main>
            <q-item-side right icon="mdi-chevron-right"></q-item-side>
          </q-item>
        </q-list>

        <q-spinner :size="30" slot="message"></q-spinner>

        <div class="list-msg" v-if="!loading && employee.data.length <= 0">
          <div>No data matches.</div>
        </div>
      </q-infinite-scroll>
    </PullToRefresh>

    <EmployeeDetail></EmployeeDetail>
  </q-page>
</template>

<script>
import PageMixin from '../../../../../mixins/PageMixin'
import EmployeeDetail from '../../../../clf/employees/detail-dialog'
import PullToRefresh from '../../../../../components/PullToRefresh'
import { EventHandlerMixin } from '../../../../../common'

export default {
  name: 'MobileClfEmployeeIndex',
  mixins: [
    PageMixin,
    EventHandlerMixin('reload-merchant-employees'),
    EventHandlerMixin('show-mobile-clf-merchant-employees-filter', 'showFilters')
  ],
  components: {
    EmployeeDetail,
    PullToRefresh
  },
  data () {
    return {
      title: 'Employees',
      keyword: null,
      status: 'active',
      statuses: ['Active', 'Inactive', 'All'].map(v => ({
        label: v,
        value: this._.lowerCase(v)
      })),
      filters: false
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    },
    employee () {
      return this.$store.state.Employee
    }
  },
  methods: {
    showFilters () {
      this.filters = !this.filters
    },

    getQueryParams () {
      return {
        keyword: this.keyword,
        status: this.status
      }
    },

    viewDetail (item) {
      this.$root.$emit('show-dispensary-employee-detail-dialog', item)
    },

    async reload (page = 1, done = null) {
      if (page === 1) {
        this.loading = true
      }
      const params = this.getQueryParams()
      params.page = page
      await this.$store.dispatch('Employee/init', params)
      this.loading = false
      if (page === 1) {
        try {
          this.$refs.infiniteScroll.resume()
          this.$refs.infiniteScroll.index = 1
        } catch (e) {
          console.log(e)
        }
      }
      if (done) {
        done(this.employee.data.length >= this.employee.count)
      }
    }
  }
}
</script>

<style lang="scss">
  #mobile__clf__employees__index_page {
    .q-item-main {
      .item-right {
        .title {
          font-size: 14px !important;
        }
      }
    }
  }
</style>
