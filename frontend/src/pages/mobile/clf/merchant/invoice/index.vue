<template>
  <q-page id="mobile__clf__invoices__index_page" class="clf_page clf_mobile_list_page clf_mobile_list_page_index">
    <div class="page-header" :class="{visible: filters}">
      <div class="fun-group">
        <DateRangeFilter class="mr-20"
                         default="all"
                         ref="dateRangeFilter"></DateRangeFilter>

        <q-field label="Status" class="search_field">
          <q-select v-model="status" :options="statuses"></q-select>
        </q-field>

        <q-btn color="primary"
               class="mt-10"
               label="Reload"
               @click="filters = false; reload(1, null)"></q-btn>
      </div>
    </div>

    <PullToRefresh :loading="loading"
                   @reload="reload(1, null)">
      <q-infinite-scroll ref="infiniteScroll"
                         inline
                         :handler="reload">
        <q-list no-border separator>
          <q-item v-for="(t, i) in invoice.data"
                  link
                  @click.native="viewDetail(t)"
                  :key="i">
            <q-item-main>
              <q-item-tile class="item-left">
                <div class="title">{{ t.recipient.name }}</div>
                <div class="desc">Created: {{ t.createdAt | date }}</div>
                <div class="desc">Due date: {{ t.dueAt | date }}</div>
              </q-item-tile>
              <q-item-tile class="item-right ml-auto">
                <div class="title">{{ t.amount | moneyFormat }}</div>
                <div class="desc red" v-if="t.overdue">
                  <q-icon name="warning" class="mr-3"></q-icon> Overdue
                </div>
                <div class="desc" v-else-if="t.status === 'paid'">
                  <div class="font-12 text-primary">{{ t.statusText }}: {{ t.payAt | date('L LT') }}</div>
                </div>
                <div class="desc normal text-dark" v-else>{{ t.statusText }}</div>
                <div class="desc"># {{ t.id }}</div>
              </q-item-tile>

              <q-btn outline
                     v-if="t.status !== 'paid'"
                     @click.stop="send(t)"
                     class="btn-send item-right"
                     size="sm"
                     :class="t.status ? 'btn-gray-light' : 'btn-green-light'"
                     :label="t.status ? 'RESEND' : 'SEND'"></q-btn>
            </q-item-main>
            <q-item-side right icon="mdi-chevron-right"></q-item-side>
          </q-item>
        </q-list>

        <q-spinner :size="30" slot="message"></q-spinner>

        <div class="list-msg" v-if="!loading && invoice.data.length <= 0">
          <div>No data matches.</div>
        </div>
      </q-infinite-scroll>
    </PullToRefresh>

    <InvoiceDetail></InvoiceDetail>
    <DueDetail></DueDetail>
  </q-page>
</template>

<script>
import PageMixin from '../../../../../mixins/PageMixin'
import InvoiceDetail from '../../../../clf/bills/invoice-dialog'
import DueDetail from '../../../../clf/bills/due-dialog'
import DateRangeFilter from '../../../../../components/DateRangeFilter'
import PullToRefresh from '../../../../../components/PullToRefresh'
import { EventHandlerMixin } from '../../../../../common'

export default {
  name: 'MobileClfInvoiceIndex',
  mixins: [
    PageMixin,
    EventHandlerMixin('reload-merchant-invoices'),
    EventHandlerMixin('show-mobile-clf-merchant-invoices-filter', 'showFilters')
  ],
  components: {
    DateRangeFilter,
    InvoiceDetail,
    DueDetail,
    PullToRefresh
  },
  data () {
    return {
      title: 'Outgoing Invoices',
      status: 'unpaid',
      filters: false
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    },
    invoice () {
      return this.$store.state.Invoice
    },
    statuses () {
      return this.cc.clf.billStatuses
    }
  },
  methods: {
    showFilters () {
      this.filters = !this.filters
    },

    getQueryParams () {
      const params = this.$refs.dateRangeFilter.params()
      params.status = this.status
      return params
    },

    viewDetail (item) {
      if (!item.status) {
        return this.$root.$emit('show-dispensary-bill-invoice-dialog', item)
      }
      item.invoiceView = true
      this.$root.$emit('show-dispensary-bill-due-dialog', item)
    },

    send (item) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to send this invoice?',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await this.c.request(`${this.c.clf.apiPrefix()}/invoices/send`, 'post', item)
        this.$q.loading.hide()
        if (resp.success) {
          this.$root.$emit('reload-merchant-invoices')
        }
      }).catch(() => {})
    },

    async reload (page = 1, done = null) {
      if (page === 1) {
        this.loading = true
      }
      const params = this.getQueryParams()
      params.page = page
      await this.$store.dispatch('Invoice/init', params)
      this.loading = false
      if (page === 1) {
        try {
          this.$refs.infiniteScroll.resume()
          this.$refs.infiniteScroll.index = 1
        } catch (e) {
          console.log(e)
        }
      }
      if (done) {
        done(this.invoice.data.length >= this.invoice.count)
      }
    }
  }
}
</script>

<style lang="scss">
  #mobile__clf__invoices__index_page {
    .item-left {
      .title {
        font-size: 14px;
      }
    }

    .item-right.btn-send {
      font-size: 11px;
      padding: 4px 8px;
      width: 60px !important;
      height: 35px;
      min-height: 35px;
      align-self: center;
      margin-left: 5px;
    }

    .q-item-side-right {
      min-width: 24px;
      margin-left: 3px;
    }
  }

  @media (min-width: 375px) {
    #mobile__clf__invoices__index_page {
      .item-left {
        .title {
          font-size: 14px;
        }
      }

      .item-right.btn-send {
        margin-left: 12px;
      }
    }
  }
</style>
