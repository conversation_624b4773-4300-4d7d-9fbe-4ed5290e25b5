<template>
  <q-modal class="flex items-center column modal-page center-box-modal-page"
           maximized
           no-esc-dismiss
           no-backdrop-dismiss
           no-route-dismiss
           :value="visible">
    <q-modal-layout id="mobile-clf-pay-confirm-modal">
      <q-toolbar slot="header">
        <q-btn flat round
               @click="_hide"
               @touchend.native="_hide"
               icon="mdi-chevron-left"></q-btn>
        <q-toolbar-title>Confirm Payment</q-toolbar-title>
        <q-btn flat round disable></q-btn>
      </q-toolbar>

      <div class="center-box" v-if="transaction">
        <div class="merchant-bar">
          <div class="icon">
            <img :src="merchant.icon || '/static/img/clf/dispensary.png'" alt="">
          </div>
          <div class="info">
            <div class="title">{{ merchant.name }}</div>
            <div class="description">
              {{ [address.address1, address.address2].join(', ') }}
            </div>
            <div class="description">
              {{ [address.city, address.state && address.state.abbr].join(', ') }} {{ address.postalCode }}
            </div>
          </div>
        </div>

        <div class="tip">is {{ refund ? 'sending' : 'requesting' }} a</div>

        <div class="amount" v-html="$c.moneyFormatHtml(transaction.amount)"></div>

        <div class="tip tip2">{{ refund ? 'refund' : 'payment' }}.</div>

        <div class="question">Do you wish to accept this {{ refund ? 'refund' : 'payment' }}?</div>

        <q-btn label="Yes, I accept."
               color="primary"
               class="full-width btn-md mt-30"
               @click="submit('accept')"></q-btn>
        <q-btn label="No, I decline."
               color="negative"
               class="full-width btn-md mt-15"
               @click="submit('decline')"></q-btn>
      </div>

      <q-inner-loading :visible="loading">
        <q-spinner size="40"></q-spinner>
      </q-inner-loading>
    </q-modal-layout>
  </q-modal>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { request, notifySuccess } from '../../../../common'

export default {
  name: 'mobile-clf-pay-confirm-modal',
  mixins: [
    Singleton
  ],
  data () {
    return {
      transaction: null
    }
  },
  computed: {
    merchant () {
      return this.transaction.merchant || {}
    },
    address () {
      return this.merchant.address || {}
    },
    refund () {
      return this.transaction.type.toLowerCase().indexOf('refund') >= 0
    }
  },
  methods: {
    async show () {
      this.transaction = null
      this.loading = true
      const resp = await request(`/api/clf/transactions/confirm?token=${this.origin}`)
      this.loading = false
      if (resp.success) {
        this.transaction = resp.data
      } else {
        this._hide()
      }
    },
    async submit (operation) {
      this.loading = true
      const resp = await request(`/api/clf/transactions/operate`, 'post', {
        token: this.origin,
        operation
      })
      this.loading = false
      if (resp.success) {
        const type = this.refund ? 'refund' : 'payment'
        if (operation === 'accept') {
          notifySuccess(`The ${type} has been accepted!`, 'Accepted')
        } else if (operation === 'decline') {
          notifySuccess(`The ${type} has been declined!`, 'Declined', 'negative')
        }
        this.$store.dispatch('User/init')
        this.$store.dispatch('Transaction/init')
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
  #mobile-clf-pay-confirm-modal {
    .center-box {
      text-align: center;
      height: auto !important;

      .merchant-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        margin-top: 15px;

        .icon {
          width: 26vw;
          height: 26vw;
          display: flex;
          padding: 10px;
          align-items: center;
        }

        .info {
          text-align: left;

          .title {
            font-size: 24px;
            font-weight: 500;
          }

          .description {
            font-size: 14px;
            color: #666;
          }
        }
      }

      > .tip {
        font-size: 22px;
        font-weight: lighter;
        margin: 30px auto 5px;

        &.tip2 {
          margin: 5px auto 30px;
        }
      }

      > .amount {
        font-size: 50px;
        font-weight: bold;

        sup {
          font-size: 16px;
          font-weight: normal;
        }
      }

      > .question {
        font-size: 18px;
        font-weight: 500;
      }
    }
  }
</style>
