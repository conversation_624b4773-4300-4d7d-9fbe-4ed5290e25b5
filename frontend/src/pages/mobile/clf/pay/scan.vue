<template>
  <q-modal class="flex items-center column modal-page modal-transparent"
           maximized
           no-esc-dismiss
           no-backdrop-dismiss
           no-route-dismiss
           :value="visible">
    <q-modal-layout id="mobile-clf-pay-scan-modal">
      <q-toolbar slot="header">
        <q-btn flat round
               @click="_hide"
               @touchend.native="_hide"
               icon="mdi-chevron-left"></q-btn>
        <q-toolbar-title>Make A Purchase</q-toolbar-title>
        <q-btn flat round disable></q-btn>
      </q-toolbar>

      <div class="tip">Place the QR code inside area</div>
      <div class="scan-area-top scan-area-fix"></div>
      <div class="scan-area-left scan-area-fix"></div>
      <div class="scan-area" @click="test"></div>
      <div class="scan-area-right scan-area-fix"></div>
      <div class="scan-area-bottom scan-area-fix"></div>
    </q-modal-layout>
  </q-modal>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { isDebugging } from '../../../../common'

export default {
  name: 'mobile-clf-pay-scan-modal',
  mixins: [
    Singleton
  ],
  methods: {
    show () {
      const body = document.querySelector('body')
      body.classList.add('transparent')

      if (window.cordova) {
        window.QRScanner.scan((err, text) => {
          if (!err) {
            this.done(text)
          }
        })
        window.QRScanner.show()
      }
    },
    hide () {
      setTimeout(() => {
        const body = document.querySelector('body')
        body.classList.remove('transparent')

        if (window.cordova) {
          window.QRScanner.destroy()
        }
      }, 150)
    },
    done (text) {
      this._hide()
      this.$root.$emit('show-mobile-clf-pay-confirm-modal', text)
    },
    test () {
      if (isDebugging()) {
        this.done('3F4D9C0F-C87F-07E3-EC43-D30211FE8C3D')
      }
    }
  },
  mounted () {
    window.document.addEventListener('backbutton', this._hide, false)
  },
  beforeDestroy () {
    window.document.removeEventListener('backbutton', this._hide, false)
  }
}
</script>

<style lang="scss">
#mobile-clf-pay-scan-modal {
  .q-layout-header {
    background-color: rgba(0, 0, 0, 0.3) !important;

    .q-toolbar {
      .q-toolbar-title {
        font-weight: bold;
      }
    }
  }

  .q-modal-layout-content {
    .tip {
      position: absolute;
      font-size: 16px;
      color: white;
      z-index: 1;
      top: 35vw;
      width: 100vw;
      text-align: center;
    }

    .scan-area {
      width: 60vw;
      height: 60vw;
      background-image: url("../../../../statics/mobile/clf/scan.png");
      background-repeat: no-repeat;
      background-size: contain;
    }

    .scan-area-fix {
      position: absolute;
      background-color: rgba(0, 0, 0, 0.3) !important;
      left: 0;
      top: 0;
      width: 100vw;
      height: calc(50% - 30vw);

      &.scan-area-left {
        top: calc(50% - 30vw);
        width: calc(50% - 30vw);
        height: 60vw;
      }

      &.scan-area-right {
        left: auto;
        right: 0;
        top: calc(50% - 30vw);
        width: calc(50% - 30vw);
        height: 60vw;
      }

      &.scan-area-bottom {
        top: auto;
        bottom: 0;
        height: calc(50% - 30vw);
      }
    }
  }
}
</style>
