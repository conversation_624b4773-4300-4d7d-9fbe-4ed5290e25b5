<template>
  <q-page class="mobile_clf_settings_index_page center-box-page" :class="{ sa }">
    <div class="center-box">
      <div class="form-section">
        <h4 class="mt-0 mb-30">
          <template v-if="sa && saId">Edit Patient {{ saId }}</template>
          <template v-else-if="sa">Add Patient</template>
          <template v-else>Account Details</template>
        </h4>
        <div class="row gutter-sm">
          <div class="col-5">
            <div class="avatar-container">
              <div class="avatar" v-if="entity.avatar"
                   :style="{backgroundImage: `url(${entity.avatar})`}"></div>
              <div class="avatar avatar-default" v-else></div>

              <q-btn flat
                     color="primary"
                     class="btn-change-picture"
                     size="sm"
                     no-caps
                     label="Change">
                <input type="file"
                  class="icon_file_input"
                  @change="selectedFile"
                  accept="image/*"/>
              </q-btn>
            </div>
          </div>
          <div class="col-7">
            <q-field :error="$v.entity.firstName.$error">
              <q-input v-model="entity.firstName"
                       placeholder="First Name"
                       @blur="$v.entity.firstName.$touch">
                <i class="q-icon q-icon-square flaticon-avatar" slot="before"></i>
              </q-input>
            </q-field>
            <q-field :error="$v.entity.lastName.$error" class="field-offset">
              <q-input v-model="entity.lastName"
                       placeholder="Last Name"
                       @blur="$v.entity.lastName.$touch"></q-input>
            </q-field>
          </div>
        </div>

        <q-field :error="$v.entity.email.$error">
          <q-input v-model="entity.email"
                   placeholder="Email"
                   @blur="$v.entity.email.$touch">
            <i class="q-icon q-icon-square flaticon-envelope" slot="before"></i>
          </q-input>
        </q-field>

        <q-field :error="$v.entity.birthday.$error">
          <q-datetime type="date"
                      v-model="entity.birthday"
                      placeholder="Date of Birth"
                      :before="[{icon: 'mdi-blank q-icon-square flaticon-calendar'}]"
                      @blur="$v.entity.birthday.$touch">
          </q-datetime>
        </q-field>

        <q-field :error="$v.entity.address.$error">
          <q-input v-model="entity.address"
                   placeholder="Address"
                   @blur="$v.entity.address.$touch">
            <i class="q-icon q-icon-square flaticon-placeholder" slot="before"></i>
          </q-input>
        </q-field>

        <div class="row gutter-sm">
          <div class="col">
            <q-field :error="$v.entity.city.$error">
              <q-input v-model="entity.city"
                       placeholder="City"
                       @blur="$v.entity.city.$touch">
                <i class="q-icon q-icon-square flaticon-target" slot="before"></i>
              </q-input>
            </q-field>
          </div>
          <div class="col">
            <q-field :error="$v.entity.state.$error">
              <q-select v-model="entity.state"
                        :options="$store.state.Config.usStates"
                        placeholder="State"
                        filter
                        :before="[{icon: 'mdi-blank q-icon-square flaticon-skyline'}]"
                        @blur="$v.entity.state.$touch">
              </q-select>
            </q-field>
          </div>
        </div>
        <div class="row gutter-sm">
          <div class="col">
            <q-field :error="$v.entity.zip.$error">
              <q-input type="number"
                       v-model="entity.zip"
                       placeholder="ZIP Code"
                       @blur="$v.entity.zip.$touch">
                <i class="q-icon q-icon-square flaticon-placeholder-1" slot="before"></i>
              </q-input>
            </q-field>
          </div>
          <div class="col"></div>
        </div>

        <q-field label="Home Phone" label-width="12"
                 :error="$v.entity.homePhone.$error">
          <q-input v-model="entity.homePhone"
                   @blur="$v.entity.homePhone.$touch">
            <i class="q-icon q-icon-square flaticon-telephone" slot="before"></i>
          </q-input>
        </q-field>

        <q-field label="Mobile Phone" label-width="12"
                 :error="$v.entity.mobilePhone.$error">
          <q-input v-model="entity.mobilePhone"
                   @blur="$v.entity.mobilePhone.$touch">
            <i class="q-icon q-icon-square flaticon-telephone" slot="before"></i>
          </q-input>
        </q-field>

        <q-field label="Work Phone" label-width="12"
                 :error="$v.entity.workPhone.$error">
          <q-input v-model="entity.workPhone"
                   @blur="$v.entity.workPhone.$touch">
            <i class="q-icon q-icon-square flaticon-telephone" slot="before"></i>
          </q-input>
        </q-field>
      </div>

      <div class="form-section">
        <h4 v-if="sa && !saId">Set Password</h4>
        <h4 v-else>Change Password</h4>
        <div class="q-field">
          <div class="q-field-label items-center flex">
            New Password
            <span class="text-gray thin ml-5">(optional)</span>
          </div>
          <q-input type="password" v-model="entity.password"
                   :error="$v.entity.password.$error || $v.entity.confirmPassword.$error"
                   @blur="$v.entity.password.$touch() && $v.entity.confirmPassword.$touch()">
            <i class="q-icon q-icon-square flaticon-padlock" slot="before"></i>
          </q-input>
        </div>
        <div class="q-field">
          <div class="q-field-label items-center flex">
            Confirm New Password
            <span class="text-gray thin ml-5">(optional)</span>
          </div>
          <q-input type="password" v-model="entity.confirmPassword"
                   :error="$v.entity.confirmPassword.$error"
                   @blur="$v.entity.confirmPassword.$touch">
            <i class="q-icon q-icon-square flaticon-padlock" slot="before"></i>
          </q-input>
        </div>
        <div class="row gutter-sm" v-if="$store.getters['Config/current'] && $store.getters['Config/current'].passwordExpiry">
          <div class="col">
            <q-checkbox v-model="entity.passwordExpiryDisabled"
                        label="Turn off password 90 days expiry."></q-checkbox>
          </div>
        </div>
      </div>

      <div class="form-section" v-if="!sa">
        <h4>Please Enter Your Current Password</h4>
        <div class="q-field">
          <div class="q-field-label items-center flex">
            Current Password
            <span class="text-orange thin ml-5">(required)</span>
          </div>
          <q-input type="password"
                   v-model="entity.old_password"
                   :error="$v.entity.old_password.$error"
                   @blur="$v.entity.old_password.$touch">
            <i class="q-icon q-icon-square flaticon-padlock" slot="before"></i>
          </q-input>
        </div>
      </div>

      <q-btn color="dark"
             class="btn-submit"
             @click="submit"
             label="Save"></q-btn>
    </div>
  </q-page>
</template>

<script>
import PageMixin from '../../../../mixins/PageMixin'
import { required } from 'vuelidate/lib/validators'
import { request } from '../../../../common'
import _ from 'lodash'
import fixOrientation from 'fix-orientation'

export default {
  name: 'MobileClfSettingsIndex',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'My Settings',
      entity: {}
    }
  },
  computed: {
    sa () {
      return this.$route.path.startsWith('/a/clf/sa/patients')
    },
    saId () {
      if (!this.sa) {
        return ''
      }
      return this.$route.params.id || ''
    }
  },
  validations () {
    const all = {
      entity: {
        firstName: {
          required
        },
        lastName: {
          required
        },
        email: {
          required
        },
        birthday: {
          required
        },
        address: {},
        city: {},
        state: {},
        zip: {},
        workPhone: {},
        mobilePhone: {},
        homePhone: {},
        password: {},
        old_password: {},
        confirmPassword: {
          confirmPassword: (value, vm) => {
            return !vm ||
              (!vm.password && !value) ||
              vm.password === value
          }
        }
      }
    }
    if (!this.sa) {
      all.entity.old_password = {
        required
      }
    }
    if (this.sa && !this.saId) {
      all.entity.password = {
        required
      }
    }
    return all
  },
  methods: {
    async reload () {
      if (this.loading) {
        return
      }
      if (this.sa) {
        this.entity = {}

        if (this.saId) {
          this.loading = true
          this.$q.loading.show()
          const resp = await request(`/clf/sa/patients/settings/action/data/${this.saId}`)
          this.$q.loading.hide()
          this.loading = false
          if (resp.success) {
            this.sync(resp.data)
          }
        }
        return
      }
      this.loading = true
      this.sync()
      await this.$store.dispatch('User/init')
      this.sync()
      this.loading = false
    },
    sync (obj = undefined) {
      if (obj === undefined) {
        obj = this.$store.state.User
      }
      this.entity = _.assignIn({}, obj)
      if (this.entity.state) {
        this.entity.state = this.entity.state.id
      }
    },
    async selectedFile () {
      const $input = this.$el.querySelector('.icon_file_input')
      if ($input.files.length) {
        const reader = new FileReader()
        const self = this
        reader.onload = function () {
          fixOrientation(this.result, fixed => {
            self.$set(self.entity, 'avatar', fixed)
            self.entity.avatar = fixed
          })
        }
        reader.readAsDataURL($input.files[0])
      }
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$q.notify('Please fix the highlighted fields.')
        return this.$v.$touch()
      }
      this.$q.loading.show()
      const params = _.cloneDeep(this.entity)
      params.emailAddress = params.email
      params.phone = params.homePhone || ''
      params.mobilephone = params.mobilePhone || ''
      params.workphone = params.workPhone || ''
      params.zip = params.zip || ''
      params.passwordExpiresDisabled = params.passwordExpiryDisabled
      const $input = this.$el.querySelector('.icon_file_input')
      if ($input.files.length) {
        params.picture = $input.files[0]
      }
      delete params.avatar

      let url = `/api/clf/settings`
      if (this.sa) {
        url = `/clf/sa/patients/settings/action/save`
      }
      const resp = await request(url, 'file', params)
      this.$q.loading.hide()
      if (resp.success) {
        this.$q.notify({
          type: 'positive',
          message: 'Changes have been saved!'
        })
        this.$v.$reset()

        if (this.sa) {
          this.$router.push(`/a/clf/sa/patients`)
        } else {
          this.reload()
        }
      }
    }
  },
  mounted () {
    this.reload()

    if (this.sa) {
      this.title = 'Add Patient'

      if (this.saId) {
        this.title = 'Edit Patient'
      }
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.reload()
    })
  }
}
</script>

<style lang="scss">
  .mobile_clf_settings_index_page {
    overflow: hidden;
    height: calc(100vh - 51px - var(--safe-area-inset-top));

    .center-box {
      display: block !important;
      padding: 20px !important;
      -webkit-overflow-scrolling: touch;
    }

    .avatar-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-bottom: 10px;

      .avatar {
        width: 80px;
        height: 80px;
        background-size: cover;
        background-position: 50%;
        background-repeat: no-repeat;
        border-radius: 50%;
        margin-bottom: 3px;
        box-shadow: 0 0 10px silver;

        &.avatar-default {
          background-image: url('../../../../statics/mobile/avatar.png');
        }
      }
    }

    .btn-change-picture {
      padding: 0;
      width: 65px;
      height: 34px;

      .q-btn-inner {
        position: relative;
      }
    }

    .icon_file_input {
      width: 65px;
      height: 34px;
      position: absolute;
      opacity: 0;
    }

    .form-section {
      margin-bottom: 25px;

      h4 {
        margin: 0 auto 25px;
        text-align: center;
        font-size: 18px;
        line-height: 28px;
        color: black;
      }

      .q-field {
        margin-bottom: 15px;

        .q-field-label {
          font-size: 14px;
          color: #333;
        }

        &.field-offset {
          margin-left: 27px;
        }
      }
    }

    .q-datetime-input, .q-select {
      .q-if-inner {
        margin-left: 0;
      }
    }

    .btn-submit {
      width: 100%;
      border-radius: 10px;
    }
  }

  #admin .mobile_clf_settings_index_page.sa {
    > .center-box {
      max-width: 600px;

      [class^="flaticon-"]:before,
      [class*=" flaticon-"]:before,
      [class^="flaticon-"]:after,
      [class*=" flaticon-"]:after {
        margin-left: 2px;
        margin-right: 8px;
        color: #888;
      }

      .form-section {
        h4 {
          text-align: left;
          font-size: 24px;
        }

        .q-field.field-offset {
          margin-left: 31px;
        }

        .q-if {
          padding: 9px 12px !important;
        }
      }

      .btn-submit {
        height: 44px;
      }
    }
  }
</style>
