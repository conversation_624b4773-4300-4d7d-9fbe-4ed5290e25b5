<template>
  <q-modal class="flex items-center column modal-page modal-transparent"
           maximized
           no-esc-dismiss
           no-backdrop-dismiss
           no-route-dismiss
           :value="visible">
    <q-modal-layout id="mobile-clf-wallet-detail-modal">
      <q-toolbar slot="header">
        <q-btn flat round
               class="btn-back"
               @click="_hide"
               @touchend.native="_hide"
               icon="mdi-chevron-left"></q-btn>
        <q-toolbar-title>Transaction Details</q-toolbar-title>
        <q-btn flat round disable></q-btn>
      </q-toolbar>

      <div class="center-box">
        <q-btn round
               class="btn-close"
               text-color="white"
               @click="_hide"
               @touchend.native="_hide"
               color="red-2"
               icon="mdi-close"></q-btn>

        <div class="head-icon" v-if="entity.status">
          <q-icon class="replaced-icon"
                  :class="transactionClass(entity)"
                  :name="transactionIcon(entity)"></q-icon>
        </div>

        <table class="table table-form table-form-light" v-if="entity.status">
          <tr>
            <th width="100">Date:</th>
            <td>{{ entity.date }}</td>
          </tr>
          <tr>
            <th>Tran ID:</th>
            <td>{{ entity.id }}</td>
          </tr>
          <tr v-if="loadType">
            <th>Bank:</th>
            <td>{{ entity.card }}</td>
          </tr>
          <tr v-else>
            <th>Merchant:</th>
            <td>{{ entity.description }}</td>
          </tr>
          <tr>
            <th>Tran Type:</th>
            <td v-if="loadType">{{ entity.description }}</td>
            <td v-else>{{ entity.subDescription }}</td>
          </tr>
          <tr v-if="loadType && entity.status === 'Pending'">
            <th>Est. Arrival:</th>
            <td class="normal black">{{ entity.estArrival }}</td>
          </tr>
        </table>

        <loading-box :loading="loading" class="compact" v-if="!loadType">
          <template v-if="detail">
            <table class="table table-form table-form-left table-form-light table-details">
              <thead>
              <tr>
                <th class="dimgrey">Item</th>
                <th class="dimgrey right">Cost</th>
              </tr>
              </thead>
              <tbody>
              <tr v-for="(item, i) in detail.items" :key="i">
                <td>{{ item.name }}</td>
                <td class="right">{{ item.amount }}</td>
              </tr>
              </tbody>
            </table>

            <table class="table table-form table-summary table-form-light">
              <tr>
                <th class="text-label">Taxes:</th>
                <td class="right">{{ detail.tax }}</td>
              </tr>
              <tr>
                <th class="text-label">Fees:</th>
                <td class="right">{{ detail.fee }}</td>
              </tr>
              <tr class="black">
                <th class="va-m">Total:</th>
                <td class="right heavy font-16">{{ detail.total }}</td>
              </tr>
            </table>
          </template>
        </loading-box>
        <div v-else>
          <table class="table table-form table-summary mt-10">
            <tr>
              <td class="va-m">Amount:</td>
              <th class="font-16">{{ transactionAmount(entity) }}</th>
            </tr>
            <tr v-if="entity.status === 'Pending'">
              <td colspan="2"
                  class="text-center uc red">{{ entity.status }}</td>
            </tr>
          </table>
        </div>
      </div>
    </q-modal-layout>
  </q-modal>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { request } from '../../../../common'
import LoadingBox from '../../../../components/LoadingBox'
import TransactionMixin from '../../../../mixins/mobile/clf/TransactionMixin'
import _ from 'lodash'

export default {
  name: 'mobile-clf-wallet-detail-modal',
  mixins: [
    Singleton,
    TransactionMixin
  ],
  components: {
    LoadingBox
  },
  props: {
    reverse: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data () {
    return {
      detail: null
    }
  },
  computed: {
    loadType () {
      return ['Account Load', 'Account Unload'].includes(this.entity.type)
    }
  },
  methods: {
    async show () {
      this.detail = null
      if (!this.loadType) {
        this.loading = true
      }
      const resp = await request(`/api/clf/transactions/detail/${this.entity.id}`)
      this.loading = false
      if (resp.success) {
        this.detail = resp.data
        _.assignIn(this.entity, resp.data)
      }
    }
  }
}
</script>

<style lang="scss">
#mobile-clf-wallet-detail-modal {
  .q-modal-layout-content {
    .center-box {
      width: 88vw;
      min-height: 100px;

      .head-icon {
        text-align: center;
        padding: 0 10px 10px;

        .q-icon {
          position: static;
          width: 60px;
          height: 60px;
          top: 0;
        }
      }

      .table-summary {
        width: auto;
        margin-left: auto;
      }

      .table-details {
        margin: 15px 5px;

        th {
          border-bottom: 1px solid #eee;
          color: #888;
          font-weight: bold;
        }
      }
    }
  }
}

body.device-mode-web {
  #mobile-clf-wallet-detail-modal {
    .q-layout-header {
      display: none;
    }

    .center-box {
      max-width: 500px;
      padding: 15px 25px;
      font-size: 14px;

      .common-loading-comp-box {
        margin: 30px auto;
      }
    }
  }
}
</style>
