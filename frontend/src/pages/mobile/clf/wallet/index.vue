<template>
  <q-page class="mobile_clf_wallet_index_page">
    <PullToRefresh :loading="loading"
                   @reload="reload(1, null)">
      <div class="row balance-row">
        <div class="col-3 text-right">
          <q-btn flat round
                 class="btn-load-unload"
                 @click="$root.$emit('show-mobile-clf-wallet-load-modal', {type: 'Unload'})"
                 icon="mdi-minus-circle"
                 color="negative"></q-btn>
        </div>
        <div class="col balance"
             v-html="c.moneyFormatHtml(user.account.balance)"></div>
        <div class="col-3">
          <q-btn flat round
                 class="btn-load-unload"
                 @click="$root.$emit('show-mobile-clf-wallet-load-modal', {type: 'Load'})"
                 icon="mdi-plus-circle"
                 color="positive"></q-btn>
        </div>
      </div>

      <div class="row pay-row justify-center">
        <q-btn rounded
               color="dark"
               class="btn-pay"
               @click="$root.$emit('show-mobile-clf-pay-scan-modal')"
               icon="mdi-qrcode-scan"
               label="PAY"></q-btn>
      </div>

      <q-card class="transaction-card">
        <q-card-title>Transactions</q-card-title>
        <q-card-main>
          <q-infinite-scroll ref="infiniteScroll"
                             inline
                             :handler="reload">
            <q-list no-border separator>
              <q-item v-for="(t, i) in transaction.data"
                      link
                      @click.native="viewDetail(t)"
                      :key="i">
                <q-item-side>
                  <q-icon class="replaced-icon"
                          :class="transactionClass(t)"
                          :name="transactionIcon(t)"></q-icon>
                </q-item-side>
                <q-item-main>
                  <q-item-tile class="item-left">
                    <div class="title">{{ t.description }}</div>
                    <div class="desc">{{ t.date }}</div>
                    <div class="desc italic">{{ t.subDescription }}</div>
                  </q-item-tile>
                  <q-item-tile class="item-right">
                    <div class="amount" :class="transactionClass(t)">{{ transactionAmount(t) }}</div>
                    <div class="balance" v-if="t.status === 'Completed'">{{ t.balance }}</div>
                    <div class="status italic" v-else>{{ t.status }}</div>
                  </q-item-tile>
                </q-item-main>
                <q-item-side right icon="mdi-chevron-right"></q-item-side>
              </q-item>
            </q-list>

            <q-spinner :size="30" slot="message"></q-spinner>

            <div class="list-msg" v-if="!loading && transaction.data.length <= 0">
              <div>No data matches.</div>
            </div>
          </q-infinite-scroll>
        </q-card-main>
      </q-card>
    </PullToRefresh>

    <ScanModal></ScanModal>
    <PayModal></PayModal>
  </q-page>
</template>

<script>
import PageMixin from '../../../../mixins/PageMixin'
import TransactionMixin from '../../../../mixins/mobile/clf/TransactionMixin'
import ScanModal from '../pay/scan'
import PayModal from '../pay/confirm'
import PullToRefresh from '../../../../components/PullToRefresh'
import { EventHandlerMixin } from '../../../../common'

export default {
  name: 'MobileClfWalletIndex',
  components: {
    ScanModal,
    PayModal,
    PullToRefresh
  },
  mixins: [
    PageMixin,
    TransactionMixin,
    EventHandlerMixin('mobile-clf-wallet-index-reload', 'reload')
  ],
  data () {
    return {
      title: 'My Wallet'
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    },
    transaction () {
      return this.$store.state.Transaction
    }
  },
  methods: {
    async reload (page = 1, done = null) {
      if (page === 1) {
        this.loading = true
        this.$store.dispatch('User/init')
        this.$store.dispatch('Bank/init')
      }
      await this.$store.dispatch('Transaction/init', { page })
      this.loading = false
      if (page === 1) {
        try {
          this.$refs.infiniteScroll.resume()
          this.$refs.infiniteScroll.index = 1
        } catch (e) {
          console.log(e)
        }
      }
      if (done) {
        done(this.transaction.data.length >= this.transaction.count)
      }
    },
    viewDetail (t) {
      this.$root.$emit('show-mobile-clf-wallet-detail-modal', t)
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
  @import '../../../../css/mobile/common';

  .mobile_clf_wallet_index_page {
    overflow: hidden;

    .pay-row {
      .btn-pay {
        width: 99px;
        height: 34px;
        min-height: 34px;

        @extend %q-svg-icon;
        background-image: url("../../../../statics/mobile/clf/pay.png") !important;
        background-color: white;
        background-size: cover !important;

        .q-btn-inner {
          display: none;
        }
      }
    }

    .q-card-main {
      .q-infinite-scroll {
        height: calc(100vh - 203px - var(--safe-area-inset-top));
      }
    }
  }
</style>
