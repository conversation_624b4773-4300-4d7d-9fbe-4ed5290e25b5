<template>
  <q-modal class="flex items-center column modal-page center-box-modal-page"
           maximized
           no-esc-dismiss
           no-backdrop-dismiss
           no-route-dismiss
           :value="visible">
    <q-modal-layout id="mobile-clf-wallet-load-modal">
      <q-toolbar slot="header">
        <q-btn flat round
               class="btn-back"
               @click="_hide"
               @touchend.native="_hide"
               icon="mdi-chevron-left"></q-btn>
        <q-toolbar-title>{{ title }}</q-toolbar-title>
        <q-btn flat round disable></q-btn>
      </q-toolbar>

      <div class="center-box" :class="`center-box-${type}`">
        <q-btn round
               class="btn-close"
               text-color="white"
               @click="_hide"
               @touchend.native="_hide"
               color="red-2"
               icon="mdi-close"></q-btn>

        <div class="amount-row">
          <sup>$</sup>{{ amount }}
        </div>
        <div class="tip-row">
          <template v-if="load">To be deposited from</template>
          <template v-else>Will be deposited to</template>
        </div>
        <div class="banks-row gutter-xs row">
          <div class="col" v-for="c in cards" :key="c.id">
            <a href="javascript:"
               v-ripple
               @click="card = c"
               @touchend="card = c"
               :class="{active: (card && card.id === c.id)}">
              <q-icon name="mdi-check-circle"></q-icon>
              <img v-if="c.bank.icon" :src="c.bank.icon" alt="">
              <img v-else src="../../../../statics/mobile/clf/bank.png" alt="">
              <div class="title">(ending in {{ c.end }})</div>
            </a>
          </div>
          <div class="col" v-if="cards.length < 3">
            <a href="javascript:"
               class="new"
               @click="$c.click(link)"
               @touchend="$c.touch(link)"
               v-ripple>
              <q-icon name="mdi-plus-circle"></q-icon>
              <div class="title">Link a new bank</div>
            </a>
          </div>
        </div>
        <div class="keyboard gutter-xs row">
          <div class="col-4" v-for="k in keys" :key="k">
            <a href="javascript:"
              @click="$c.click(() => tap(k))"
              @touchend="tap(k)">
              <q-icon name="mdi-backspace-outline" v-if="k === '<'"></q-icon>
              <template v-else>{{ k }}</template>
            </a>
          </div>
        </div>
        <q-btn class="btn-submit"
               :label="type"
               :disable="!card || amount === '0'"
               @click="confirming = true"
               @touchend.native="confirming = true"
               color="primary"></q-btn>
      </div>

      <div class="confirm-box-container" :class="{'active': confirming && card}">
        <div class="confirm-box">
          <q-btn round
                 class="btn-close"
                 text-color="white"
                 @click="confirming = false"
                 @touchend.native="confirming = false"
                 color="red-2"
                 icon="mdi-close"></q-btn>

          <h3>{{ type }} Confirmation</h3>
          <div class="title-light">Are you sure that you wish to complete the following {{ _.lowerCase(type) }} to your account?</div>

          <table class="table table-form table-form-lg" v-if="card">
            <tr>
              <th v-if="type === 'Load'">From Bank:</th>
              <th v-else>To Bank:</th>
              <td v-text="`${card.bank.name} - ${card.type}`"></td>
            </tr>
            <tr>
              <th>Account #:</th>
              <td v-text="card.number"></td>
            </tr>
            <tr>
              <th width="102">Amount:</th>
              <td v-text="amountFormat"></td>
            </tr>
          </table>

          <div class="row gutter-sm mt-20 full-width">
            <div class="col pl-0">
              <q-btn label="Yes"
                     outline
                     color="primary"
                     class="full-width btn-green-light"
                     @click="submit"></q-btn>
            </div>
            <div class="col">
              <q-btn label="No"
                     outline
                     color="negative"
                     class="full-width btn-red-light"
                     @click="confirming = false"></q-btn>
            </div>
          </div>
        </div>
      </div>
    </q-modal-layout>
  </q-modal>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { moneyMajorAmount, moneyMinorAmount, moneyFormat, request } from '../../../../common'

export default {
  name: 'mobile-clf-wallet-load-modal',
  mixins: [
    Singleton
  ],
  data () {
    return {
      keys: [1, 2, 3, 4, 5, 6, 7, 8, 9, '.', 0, '<'],
      amount: '0',
      card: null,
      confirming: false
    }
  },
  computed: {
    type () {
      return this.entity.type || 'Load'
    },
    load () {
      return this.type === 'Load'
    },
    title () {
      return `${this.type} Your Wallet`
    },
    cards () {
      return this.$store.state.Bank.data.filter(c => c.status === 'Confirmed')
    },
    amountValue () {
      return this.castAmountValue(this.amount)
    },
    amountFormat () {
      return moneyFormat(this.amountValue)
    },
    account () {
      const user = this.$store.state.User
      return `${user.account.name} (${user.account.number})`
    }
  },
  methods: {
    castAmountValue (str = '0') {
      let amount = str.trim('.')
      amount = parseFloat(amount)
      if (Number.isNaN(amount)) {
        amount = 0
      }
      return moneyMinorAmount(amount, 'USD')
    },
    async submit () {
      this.$q.loading.show()
      const resp = await request(`/api/clf/load`, 'form', {
        type: this.type.toLowerCase(),
        amount: this.amountValue,
        card: this.card.id
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.$store.dispatch('Transaction/init')
        this.$root.$emit('mobile-clf-funds-index-reload')
        this._hide()
      }
    },
    show () {
      this.amount = '0'
      this.confirming = false
      if (!this.card && this.cards.length) {
        this.card = this.cards[0]
      }
    },
    tapExceedAlert () {
      this.$q.notify({
        color: 'warning',
        message: 'You can only load or unload at most $9999.99 each time.',
        timeout: 2000
      })
    },
    tapExceedBalance () {
      const balance = moneyFormat(this.$store.state.User.account.balance || 0)
      this.$q.notify({
        color: 'warning',
        message: `Unload amount can not exceed your current account balance ${balance}.`,
        timeout: 2000
      })
    },
    tap (key) {
      const max = 9999.99
      const value = moneyMajorAmount(this.amountValue, 'USD')
      if (value < 0 || value > max) {
        return this.tapExceedAlert()
      }

      const balance = moneyMajorAmount(this.$store.state.User.account.balance || 0, 'USD')
      if (!this.load && value > balance) {
        return this.tapExceedBalance()
      }

      key = key.toString()
      let amount = this.amount

      const fixFloat = () => {
        const pt = amount.split('.')
        if (pt.length > 1 && pt[1].length >= 2) {
          amount = (parseFloat(amount) * 10).toFixed(1)
        }
      }

      if (key === '0') {
        if (amount === '0') {
          return
        }
        fixFloat()
        amount += key
      } else if (key === '.') {
        if (amount.endsWith('.')) {
          return
        }
        amount += key
      } else if (key === '<') {
        if (amount.length > 1) {
          amount = amount.substring(0, amount.length - 1)
        } else {
          amount = '0'
        }
      } else {
        if (amount === '0') {
          amount = key
        } else {
          fixFloat()
          amount += key
        }
      }
      const amountValue = moneyMajorAmount(this.castAmountValue(amount), 'USD')
      if (amountValue < 0 || amountValue > max) {
        return this.tapExceedAlert()
      }
      if (!this.load && amountValue > balance) {
        return this.tapExceedBalance()
      }
      this.amount = amount
    },
    link () {
      this.$root.$emit('show-mobile-clf-banks-select-modal')
    }
  }
}
</script>

<style lang="scss">
  #mobile-clf-wallet-load-modal {
    .q-modal-layout-content {
      position: relative;

      .center-box {
        &.center-box-Load {
          color: #388E3C;
        }

        &.center-box-Unload {
          color: #BC162C;

          .banks-row {
            a {
              .mdi-check-circle {
                color: #BC162C;
              }

              &.active {
                border-color: #BC162C;
              }
            }
          }

          .keyboard {
            a {
              color: #BC162C !important;
              background-color: #F8E7E9;
              border: 1px solid #BC162C;

              &:active {
                background-color: #BC162C;
                color: white !important;
              }
            }
          }

          .btn-submit {
            background-color: #BC162C !important;
          }
        }

        .amount-row {
          text-align: center;
          font-size: 5rem;
          margin-top: auto;

          sup {
            font-size: 2rem;
          }
        }

        .tip-row {
          text-align: center;
          text-transform: uppercase;
          margin-top: -4px;
          font-size: 12px;
        }

        .banks-row {
          margin-left: -5px;
          margin-top: 6px;

          .col {
            padding-left: 5px;
          }

          a {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            text-decoration: none;
            color: #888 !important;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            height: 90px;
            padding-top: 10px;

            > img {
              max-width: calc(100% - 10px);
              max-height: 55px;
              margin: auto;
            }

            .title {
              font-size: 12px;
              text-align: center;
              padding: 4px 0;
            }

            .mdi-check-circle {
              position: absolute;
              right: 3px;
              top: 3px;
              color: #388E3C;
              z-index: 1;
              font-size: 17px;
              opacity: 0;
              transition: opacity 0.3s;
            }

            &:hover, &:active {
              text-decoration: none;
            }

            &.active {
              border-color: #388E3C;

              .mdi-check-circle {
                opacity: 1;
              }
            }

            &.new {
              border-color: #4B4B4B;
              background-color: #E9E9E9;

              .mdi-plus-circle {
                color: #696969;
                font-size: 34px;
                margin: auto;
              }

              .title {
                color: #333;
                margin: -3px auto auto;
              }
            }
          }
        }

        .keyboard {
          margin: auto auto 15px;
          padding: 0 10px;

          .col-4 {
            display: flex;
            justify-content: center;
            align-items: center;
          }

          a {
            display: flex;
            justify-content: center;
            align-items: center;
            text-decoration: none;
            color: #388E3C !important;
            background-color: #EBF3EB;
            border: 1px solid #388E3C;
            border-radius: 50%;
            width: 18vw;
            height: 18vw;
            font-size: 28px;

            .q-icon {
              font-size: 22px;
            }

            &:active {
              background-color: #388E3C;
              color: white !important;
            }
          }
        }

        .banks-row, .keyboard {
          margin-left: -5px;

          .col, .col-4 {
            padding-left: 5px;
          }
        }

        .btn-submit {
          border-radius: 8px;
          font-size: 18px;
        }
      }

      .confirm-box-container {
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        right: 0;
        z-index: 9;
        visibility: hidden;
        opacity: 0;
        transition: opacity 0.3s;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        &.active {
          visibility: visible;
          opacity: 1;
        }
      }

      .confirm-box {
        padding: 20px;
        width: 88vw;
        height: auto;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: white;
        border-radius: 12px;
        position: relative;

        .q-btn.btn-close {
          width: 34px;
          height: 34px;
          padding: 0;
          position: absolute;
          right: 8px;
          top: 8px;
          border-radius: 50%;
        }

        h3 {
          font-size: 19px;
          font-weight: 600;
          margin: -10px auto 0;
        }

        .title-light {
          text-align: center;
          font-weight: lighter;
          font-size: 14px;
          margin: 0 auto 20px;
          line-height: 1.3em;
        }

        .table-form.table-form-lg {
          td, th {
            font-size: 16px;
            padding: 3px 6px;
          }
        }
      }
    }
  }

  @media (max-width: 340px) {
    #mobile-clf-wallet-load-modal {
      .q-modal-layout-content {
        .center-box {
          padding: 15px 10px;

          .banks-row, .keyboard {
            margin-left: -3px;

            .col, .col-4 {
              padding-left: 3px;
            }
          }
        }
      }
    }
  }

  @media (max-height: 666px) {
    #mobile-clf-wallet-load-modal {
      .q-modal-layout-content {
        .center-box {
          .amount-row {
            text-align: center;
            font-size: 4rem;
            margin-top: -15px;

            sup {
              font-size: 1.5rem;
            }
          }

          .banks-row {
            margin-top: 0;

            .col {
              padding-top: 3px;
            }

            a {
              height: 80px;
              padding-top: 8px;

              > img {
                max-height: 45px;
              }
            }
          }

          .keyboard {
            a {
              width: 16vw;
              height: 16vw;
              font-size: 24px;

              .q-icon {
                font-size: 18px;
              }
            }
          }

          .btn-submit {
            font-size: 14px;
          }
        }
      }
    }
  }
</style>
