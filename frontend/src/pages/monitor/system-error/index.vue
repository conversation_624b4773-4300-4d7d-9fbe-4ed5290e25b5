<template>
  <q-page id="monitor__system_error_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="visibleColumns"
               :loading="loading"
               :rows-per-page-options="[5000]"
               :pagination="pagination"
               hide-bottom
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-checkbox label="Auto refresh"
                      class="font-14"
                      @input="toggleRefresh"
                      v-model="autoRefresh"></q-checkbox>
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props"
              :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols"
                :key="col.field">
            <template v-if="col.field === 'time'">
              <a href="javascript:" class="font-mono"
                 @click="view(props.row)">{{ props.row[col.field]}}</a>
            </template>
            <template v-else-if="col.field === 'action'">
              <q-btn icon="mdi-text-box-search-outline"
                     round
                     @click="view(props.row)"></q-btn>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <PreDialog></PreDialog>
  </q-page>
</template>

<script>
import { request, toSelectOptions } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import PreDialog from '../../../components/PreDialog.vue'

export default {
  name: 'monitor-system-log',
  components: { PreDialog },
  mixins: [
    ListPageMixin
  ],
  data () {
    return {
      title: 'System Log',
      filtersUrl: '/admin/monitor/system-error-filters',
      autoLoad: false,
      autoRefresh: false,
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 5000,
        page: 1
      },
      columns: [
        {
          field: 'platform',
          label: 'Platform',
          align: 'left'
        },
        {
          field: 'time',
          label: 'Time',
          align: 'left'
        },
        {
          field: 'level',
          label: 'Level',
          align: 'left'
        },
        {
          field: 'user',
          label: 'User',
          align: 'left'
        },
        {
          field: 'detail',
          label: 'Detail',
          align: 'left'
        },
        {
          field: 'action',
          label: 'Action',
          align: 'left'
        }
      ],
      filterOptions: [
        {
          value: 'platform',
          label: 'Platform',
          options: [],
          source: 'platform'
        },
        {
          value: 'date',
          label: 'Date (UTC)',
          type: 'date'
        },
        {
          value: 'time',
          label: 'Time range (UTC)',
          range: [{
            value: 'time[start]',
            type: 'time'
          }, {
            value: 'time[end]',
            type: 'time'
          }]
        },
        {
          value: 'user',
          label: 'User'
        },
        {
          value: 'detail',
          label: 'Detail'
        },
        {
          value: 'level',
          label: 'Level',
          options: toSelectOptions([
            'DEBUG',
            'INFO',
            'NOTICE',
            'WARNING',
            'WARNING+',
            'ERROR',
            'CRITICAL',
            'ALERT',
            'EMERGENCY'
          ])
        }
      ],
      timeout: null
    }
  },
  methods: {
    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.getQueryParams()
      const resp = await request(`/admin/monitor/system-error`, 'form', data, true)
      this.loading = false
      if (resp.success) {
        this.data = resp.data.data
        this.pagination.rowsNumber = this.data.length
      } else {
        this.$q.notify(resp.message)
      }
      this.setRefresh()
    },
    clearRefresh () {
      if (this.timeout) {
        clearTimeout(this.timeout)
        this.timeout = null
      }
    },
    setRefresh () {
      this.clearRefresh()
      if (this.autoRefresh) {
        this.timeout = setTimeout(() => {
          this.reload()
        }, 60 * 1000)
      }
    },
    toggleRefresh () {
      if (this.autoRefresh) {
        this.reload()
      } else {
        this.clearRefresh()
      }
    },
    view (row) {
      const context = JSON.parse(row.context || '{}')
      this.$root.$emit('show-pre-dialog', {
        title: 'Log Detail',
        content: JSON.stringify(context, null, 4)
      })
    }
  },
  mounted () {
    this.filters = [
      {
        field: 'level',
        predicate: '=',
        value: 'WARNING+'
      }
    ]
  },
  beforeDestroy () {
    if (this.timeout) {
      clearTimeout(this.timeout)
      this.timeout = null
    }
  }
}
</script>
