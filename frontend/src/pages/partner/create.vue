<template>
  <q-page id="partner_detail_page">
    <div class="page-header">
      <div v-if="uid"
           class="title">{{ editTitle }}</div>
      <div v-else
           class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="gutter-lg">
        <template>
          <p class="col-12 item-title">PARTNER INFORMATION</p>
          <div class="row">
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Partner Name*</p>
              <div class="input-item">
                <q-input v-model="entity.name"
                         @blur="$v.entity.name.$touch"
                         :disabled="uid"
                         :error="$v.entity.name.$error"></q-input>
              </div>
            </div>
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Upload Partner Logo*</p>
              <div class="input-item">
                <q-uploader url="/attachments"
                            name="logo"
                            auto-expand
                            @blur="$v.entity.logo.$touch"
                            :error="$v.entity.logo.$error"
                            ref="uploader_logo"
                            @add="$refs.uploader_logo && $refs.uploader_logo.upload()"
                            @uploaded="(file, xhr) => uploadedAttachment('logo', xhr)"
                            extensions=".gif,.jpg,.jpeg,.png"
                            :additional-fields="[{name: 'category', value: 'public'}]"
                            clearable></q-uploader>
              </div>
              100px Max Width
              <div class="input-item"
                   v-if="entity.logo">
                <img :src="entity.logo"
                     alt="">
                <div class="text-center">
                  <div class="text-right q-mb-lg">
                    <q-btn label="Remove"
                           flat
                           @click="removeAttachment('logo')"
                           icon="mdi-close"
                           color="negative"
                           size="sm"></q-btn>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <template>
          <p class="col-12 item-title ">PARTNER ADMINS</p>
          <div class="row">
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Partner Admin First Name*</p>
              <div class="input-item">
                <q-input v-model="entity.firstName"
                         @blur="$v.entity.firstName.$touch"
                         :disabled="uid"
                         :error="$v.entity.firstName.$error"></q-input>
              </div>
            </div>
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Partner Admin Last Name*</p>
              <div class="input-item">
                <q-input v-model="entity.lastName"
                         @blur="$v.entity.lastName.$touch"
                         :disabled="uid"
                         :error="$v.entity.lastName.$error"></q-input>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Partner Admin Email*</p>
              <div class="input-item">
                <q-input v-model="entity.email"
                         type="email"
                         @blur="$v.entity.email.$touch"
                         :disabled="uid"
                         :error="$v.entity.email.$error"></q-input>
              </div>
            </div>
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Partner Admin Phone Number*</p>
              <div class="input-item">
                <q-input v-model="entity.phone"
                         type="tel"
                         @blur="$v.entity.phone.$touch"
                         :disabled="uid"
                         :error="$v.entity.phone.$error"></q-input>
              </div>
            </div>
          </div>
        </template>
        <template>
          <p class="col-12 item-title ">PARTNER PLATFORM SETTINGS</p>
          <div class="row">
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Subdomain Name*</p>
              <div class="input-item">
                <q-input v-model="entity.subDomain"
                         @blur="$v.entity.subDomain.$touch"
                         :error="$v.entity.subDomain.$error"
                         :prefix="`${entity.schema || 'https'}://`"
                         :suffix="`.${entity.host || ''}`"></q-input>
              </div>
            </div>
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Partner Custom Domain</p>
              <div class="input-item">
                <q-input v-model="entity.domain"
                         :prefix="`https://`"></q-input>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Use freight forwarding?*</p>
              <div class="input-item">
                <q-radio v-model="entity.forwarding"
                         :val="true"
                         label="Yes" />
                <q-radio v-model="entity.forwarding"
                         :val="false"
                         label="No" />
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Store Locator?*</p>
              <div class="input-item">
                <q-radio v-model="entity.locator"
                         :val="true"
                         label="Yes" />
                <q-radio v-model="entity.locator"
                         :val="false"
                         label="No" />
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Store Locator URL</p>
              <div class="input-item">
                <q-input v-model="entity.locatorUrl"
                         :prefix="`https://`"></q-input>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Partner Primary Color*</p>
              <div class="input-item">
                <q-color v-model="entity.cuscolor"
                         format-model="hex"
                         @blur="$v.entity.cuscolor.$touch"
                         :error="$v.entity.cuscolor.$error"></q-color>
                <q-icon name="mdi-square"
                        :style="{color: entity.cuscolor}"></q-icon>
              </div>
            </div>
          </div>
        </template>
        <template>
          <p class="col-12 item-title ">Card Program Branding Settings & Configuration</p>
          <div class="row">
            <div class="col-md-6 col-sm-12">
              <div class="row">
                <div class="col-md-6 col-sm-12">
                  <p class="label-item">Upload Partner Icon*</p>
                  <div class="input-item">
                    <div class="input-item">
                      <q-uploader url="/attachments"
                                  name="logo"
                                  auto-expand
                                  ref="uploader_icon"
                                  @blur="$v.entity.icon.$touch"
                                  :error="$v.entity.icon.$error"
                                  @add="$refs.uploader_icon && $refs.uploader_icon.upload()"
                                  @uploaded="(file, xhr) => uploadedAttachment('icon', xhr)"
                                  extensions=".gif,.jpg,.jpeg,.png"
                                  :additional-fields="[{name: 'category', value: 'public'}]"
                                  clearable></q-uploader>
                    </div>
                    <div class="input-item"
                         v-if="entity.icon">
                      <img :src="entity.icon"
                           alt="">
                      <div class="text-center">
                        <div class="text-right q-mb-lg">
                          <q-btn label="Remove"
                                 flat
                                 @click="removeAttachment('icon')"
                                 icon="mdi-close"
                                 color="negative"
                                 size="sm"></q-btn>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6 col-sm-12">
                  <p class="label-item">One Time Use Card Art*</p>
                  <div class="input-item">
                    <div class="input-item">
                      <q-uploader url="/attachments"
                                  name="logo"
                                  auto-expand
                                  @blur="$v.entity.oneCardArt.$touch"
                                  :error="$v.entity.oneCardArt.$error"
                                  ref="uploader_oneCardArt"
                                  @add="$refs.uploader_oneCardArt && $refs.uploader_oneCardArt.upload()"
                                  @uploaded="(file, xhr) => uploadedAttachment('oneCardArt', xhr)"
                                  extensions=".gif,.jpg,.jpeg,.png"
                                  :additional-fields="[{name: 'category', value: 'public'}]"
                                  clearable></q-uploader>
                    </div>
                    <div class="input-item"
                         v-if="entity.oneCardArt">
                      <img :src="entity.oneCardArt"
                           alt="">
                      <div class="text-center">
                        <div class="text-right q-mb-lg">
                          <q-btn label="Remove"
                                 flat
                                 @click="removeAttachment('oneCardArt')"
                                 icon="mdi-close"
                                 color="negative"
                                 size="sm"></q-btn>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-6 col-sm-12">
                  <p class="label-item">On-boarding image #1</p>
                  <div class="input-item">
                    <div class="input-item">
                      <q-uploader url="/attachments"
                                  name="logo"
                                  auto-expand
                                  @blur="$v.entity.stepOne.$touch"
                                  :error="$v.entity.stepOne.$error"
                                  ref="uploader_stepOne"
                                  @add="$refs.uploader_stepOne && $refs.uploader_stepOne.upload()"
                                  @uploaded="(file, xhr) => uploadedAttachment('stepOne', xhr)"
                                  extensions=".gif,.jpg,.jpeg,.png"
                                  :additional-fields="[{name: 'category', value: 'public'}]"
                                  clearable></q-uploader>
                    </div>
                    <div class="input-item"
                         v-if="entity.stepOne">
                      <img :src="entity.stepOne"
                           alt="">
                      <div class="text-center">
                        <div class="text-right q-mb-lg">
                          <q-btn label="Remove"
                                 flat
                                 @click="removeAttachment('stepOne')"
                                 icon="mdi-close"
                                 color="negative"
                                 size="sm"></q-btn>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6 col-sm-12">
                  <p class="label-item">On-boarding image #2*</p>
                  <div class="input-item">
                    <div class="input-item">
                      <q-uploader url="/attachments"
                                  name="logo"
                                  auto-expand
                                  @blur="$v.entity.stepThree.$touch"
                                  :error="$v.entity.stepThree.$error"
                                  ref="uploader_stepThree"
                                  @add="$refs.uploader_stepThree && $refs.uploader_stepThree.upload()"
                                  @uploaded="(file, xhr) => uploadedAttachment('stepThree', xhr)"
                                  extensions=".gif,.jpg,.jpeg,.png"
                                  :additional-fields="[{name: 'category', value: 'public'}]"
                                  clearable></q-uploader>
                    </div>
                    <div class="input-item"
                         v-if="entity.stepThree">
                      <img :src="entity.stepThree"
                           alt="">
                      <div class="text-center">
                        <div class="text-right q-mb-lg">
                          <q-btn label="Remove"
                                 flat
                                 @click="removeAttachment('stepThree')"
                                 icon="mdi-close"
                                 color="negative"
                                 size="sm"></q-btn>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-6 col-sm-12">
                  <p class="label-item">One-time use info*</p>
                  <div class="input-item">
                    <div class="input-item">
                      <q-uploader url="/attachments"
                                  name="logo"
                                  auto-expand
                                  ref="uploader_useInfo"
                                  @blur="$v.entity.useInfo.$touch"
                                  :error="$v.entity.useInfo.$error"
                                  @add="$refs.uploader_useInfo && $refs.uploader_useInfo.upload()"
                                  @uploaded="(file, xhr) => uploadedAttachment('useInfo', xhr)"
                                  extensions=".gif,.jpg,.jpeg,.png"
                                  :additional-fields="[{name: 'category', value: 'public'}]"
                                  clearable></q-uploader>
                    </div>
                    <div class="input-item"
                         v-if="entity.useInfo">
                      <img :src="entity.useInfo"
                           alt="">
                      <div class="text-center">
                        <div class="text-right q-mb-lg">
                          <q-btn label="Remove"
                                 flat
                                 @click="removeAttachment('useInfo')"
                                 icon="mdi-close"
                                 color="negative"
                                 size="sm"></q-btn>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6 col-sm-12">
                  <p class="label-item">Upload Partner Transaction Icon*</p>
                  <div class="input-item">
                    <div class="input-item">
                      <q-uploader url="/attachments"
                                  name="logo"
                                  auto-expand
                                  ref="uploader_tranIcon"
                                  @blur="$v.entity.tranIcon.$touch"
                                  :error="$v.entity.tranIcon.$error"
                                  @add="$refs.uploader_tranIcon && $refs.uploader_tranIcon.upload()"
                                  @uploaded="(file, xhr) => uploadedAttachment('tranIcon', xhr)"
                                  extensions=".gif,.jpg,.jpeg,.png"
                                  :additional-fields="[{name: 'category', value: 'public'}]"
                                  clearable></q-uploader>
                    </div>
                    <div class="input-item"
                         v-if="entity.tranIcon">
                      <img :src="entity.tranIcon"
                           alt="">
                      <div class="text-center">
                        <div class="text-right q-mb-lg">
                          <q-btn label="Remove"
                                 flat
                                 @click="removeAttachment('tranIcon')"
                                 icon="mdi-close"
                                 color="negative"
                                 size="sm"></q-btn>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-6 col-sm-12">
                  <p class="label-item">Terms & Conditions URL</p>
                  <div class="input-item">
                    <q-input v-model="entity.url"></q-input>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6 col-sm-12">
              <div class="row">
                <div class="col-md-6 col-sm-12">
                  <div class="img-preview">
                    <p class="label-item">Card Preview</p>
                    <div class="input-item">
                      <div class="px-2 mt-10">
                        <div class="card-box virtual-card"
                             :style="{background: entity.cuscolor}">
                          <div class="card-box-inner">
                            <a href="javascript:"
                               class="btn-circle status status-active">
                              <i class="mdi mdi-pause"></i>
                            </a>
                            <a href="javascript:"
                               class="btn-circle refresh"
                               data-toggle="tooltip"
                               title="Refresh the card status and create a new card if this one has been used or declined because of unauthorized merchant.">
                              <i class="mdi mdi-settings"></i>
                            </a>

                            <div class="icon">
                              <img :src="entity.oneCardArt"
                                   alt="">
                            </div>
                            <div class="card-number touch-tip-less"
                                 v-text="'**** **** **** ****'"
                                 data-toggle="tooltip"
                                 title="Click to show/hide"
                                 data-placement="top"></div>
                            <div class="expire touch-tip-less"
                                 v-text="'**/**'"
                                 data-toggle="tooltip"
                                 title="Click to show/hide"></div>
                            <div class="holder touch-tip-less"
                                 v-text="'***'"
                                 data-toggle="tooltip"
                                 title="Click to show/hide"></div>
                          </div>

                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6 col-sm-12">
                  <div class="img-preview transaction-preview">
                    <p class="label-item">Transaction Icon Preview</p>
                    <div class="input-item">
                      <div class="transaction-desc">
                        <p>
                          <span class="text-item">May</span>
                          <span class="text-title">10</span>
                        </p>
                        <img :src="entity.tranIcon">
                        <div>
                          <p class="title-item">{{entity.name}}</p>
                          <p class="text-item">Fee</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <template>
          <p class="col-12 item-title ">PARTNER FEE CONFIGURATION</p>
          <div class="row">
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Customer Membership Fee*</p>
              <div class="input-item">
                <q-input v-model="entity.memberShipFee"
                         type="number"
                         :decimals="2"
                         @blur="$v.entity.memberShipFee.$touch"
                         :error="$v.entity.memberShipFee.$error"></q-input>
              </div>
            </div>
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Customer Monthly Fee*</p>
              <div class="input-item">
                <q-input v-model="entity.monthlyFee"
                         type="number"
                         @blur="$v.entity.monthlyFee.$touch"
                         :error="$v.entity.monthlyFee.$error"></q-input>
              </div>
            </div>
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Customer Transaction Fee*</p>
              <div class="input-item">
                <q-input v-model="entity.transactionFee"
                         type="number"
                         @blur="$v.entity.transactionFee.$touch"
                         :error="$v.entity.transactionFee.$error"></q-input>
              </div>
            </div>
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Customer Unload Fee*</p>
              <div class="input-item">
                <q-input v-model="entity.unloadFee"
                         type="number"
                         @blur="$v.entity.unloadFee.$touch"
                         :error="$v.entity.unloadFee.$error"></q-input>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Tern Membership Fee*</p>
              <div class="input-item">
                <q-input v-model="entity.tmemberShipFee"
                         type="number"
                         @blur="$v.entity.tmemberShipFee.$touch"
                         :error="$v.entity.tmemberShipFee.$error"></q-input>
              </div>
            </div>
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Tern Monthly Fee*</p>
              <div class="input-item">
                <q-input v-model="entity.tmonthlyFee"
                         type="number"
                         @blur="$v.entity.tmonthlyFee.$touch"
                         :error="$v.entity.tmonthlyFee.$error"></q-input>
              </div>
            </div>
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Tern Transaction Fee*</p>
              <div class="input-item">
                <q-input v-model="entity.ttransactionFee"
                         type="number"
                         @blur="$v.entity.ttransactionFee.$touch"
                         :error="$v.entity.ttransactionFee.$error"></q-input>
              </div>
            </div>
            <div class="col-md-3 col-sm-6">
              <p class="label-item">Tern Unload Fee*</p>
              <div class="input-item">
                <q-input v-model="entity.tunloadFee"
                         type="number"
                         @blur="$v.entity.tunloadFee.$touch"
                         :error="$v.entity.tunloadFee.$error"></q-input>
              </div>
            </div>
          </div>
          <div class="btn-list">
            <q-btn class="update-item"
                   @click.native="save">
              <q-icon left
                      size="1.5em"
                      name="mdi-plus-circle-outline" />
              <div v-if="uid">Update Partner</div>
              <div v-else>Create New Partner</div>
            </q-btn>
            <q-btn class="test-item"
                   @click.native="testLoad">
              <q-icon left
                      size="1.5em"
                      name="mdi-play-circle-outline" />
              <div>Preview Partner Platform</div>
            </q-btn>
          </div>
        </template>
      </div>
    </div>
  </q-page>
</template>
<script>
import { request, notifyForm } from '../../common'
import { required } from 'vuelidate/lib/validators'
import ListPageMixin from '../../mixins/ListPageMixin'

export default {
  mixins: [
    ListPageMixin
  ],
  components: {},
  data () {
    return {
      title: 'Partner Creation',
      editTitle: 'Partner Edit',
      entity: {
        name: '',
        logo: null,
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        subDomain: '',
        locator: false,
        forwarding: false,
        locatorUrl: null,
        oneCardArt: null,
        stepOne: null,
        stepThree: null,
        useInfo: null,
        icon: null,
        tranIcon: null,
        memberShipFee: null,
        monthlyFee: null,
        transactionFee: null,
        unloadFee: null,
        tmemberShipFee: null,
        tmonthlyFee: null,
        ttransactionFee: null,
        tunloadFee: null,
        cuscolor: ''
      },
      autoLoad: true
    }
  },
  validations: {
    entity: {
      name: { required },
      firstName: { required },
      lastName: { required },
      logo: { required },
      email: { required },
      phone: { required },
      subDomain: { required },
      cuscolor: { required },
      icon: { required },
      oneCardArt: { required },
      stepOne: { required },
      stepThree: { required },
      useInfo: { required },
      tranIcon: { required },
      memberShipFee: { required },
      monthlyFee: { required },
      transactionFee: { required },
      unloadFee: { required },
      tmemberShipFee: { required },
      tmonthlyFee: { required },
      ttransactionFee: { required },
      tunloadFee: { required }
    }
  },
  computed: {
    uid () {
      return this.$route.params.id
    }
  },
  watch: {
    uid () {
      this.reload()
    }
  },
  methods: {
    async reload () {
      if (this.uid) {
        this.$q.loading.show()
        const resp = await request(`/admin/partner/${this.uid}/detail`)
        this.$q.loading.hide()
        if (resp.success) {
          this.entity = resp.data
          if (resp.data.admin) {
            this.entity.firstName = resp.data.admin.firstName
            this.entity.lastName = resp.data.admin.lastName
            this.entity.email = resp.data.admin.email
            this.entity.phone = resp.data.admin.phone
          }
        }
      }
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      if (!this.entity.forwarding && !this.entity.locator && !this.entity.locatorUrl) {
        return notifyForm('Please enter the locator url or select store locator')
      }
      if (this.entity.memberShipFee < this.entity.tmemberShipFee ||
      this.entity.monthlyFee < this.entity.tmonthlyFee ||
      this.entity.unloadFee < this.entity.tunloadFee ||
      this.entity.transactionFee < this.entity.ttransactionFee
      ) {
        return notifyForm('Please enter the right fee')
      }
      let resp = null
      this.$q.loading.show()
      if (this.uid) {
        resp = await request(`/admin/partners/update`, 'post', this.entity)
      } else {
        resp = await request(`/admin/partners/save`, 'post', this.entity)
      }
      if (resp.success) {
        this.$router.push(`/a/partners`)
      }
      this.$q.loading.hide()
    },
    uploadedAttachment (field, xhr) {
      const resp = JSON.parse(xhr.response)
      const file = resp.data['logo']
      this.entity[field] = file.url
      this.entity[`${field}_id`] = file.id
      delete this.entity[`${field}_delete`]

      this.$refs[`uploader_${field}`].reset()
      console.log(this.entity)
    },
    removeAttachment (field) {
      this.entity[field] = null
      this.entity[`${field}_delete`] = true
      console.log(this.entity)
    }
  }
}
</script>
<style lang="scss">
@import "../../css/variable";

#partner_detail_page {
  .item-title {
    letter-spacing: 2px;
    color: #696975;
    font-weight: 600;
    padding-left: 48px;
    font-size: 16px;
    margin-top: 52px;
  }
  .item-title:first-child {
    margin-top: 0;
  }
  .label-item {
    font-size: 16px;
    font-weight: 600;
    color: #171726;
    line-height: 12px;
    margin-top: 20px;
    margin-bottom: 15px;
  }
  .page-content {
    padding-top: 80px;
  }
  .img-preview {
    background: #fff;
    padding: 10px;
    border-radius: 10px;
    margin-right: 10px;
  }
  .input-item {
    margin-right: 10px;
    .btn-circle {
      width: 28px;
      height: 28px;
      background: #fbfbfb;
      border-radius: 8px;
      text-decoration: none;
      color: #92929d;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      border: 1px solid white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
      i {
        font-size: 20px;
        margin-top: 4px;
      }
    }

    a.btn-circle {
      &:hover,
      &:focus,
      &:active {
        color: white;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.7);
        text-decoration: none;
      }
    }

    .btn-circle {
      &.status {
        &.status-active {
          background: #fbfbfb;
          font-size: 12px;
          padding-left: 2px;
          color: #fc5150;
        }
      }

      &.refresh {
        .fa {
          font-size: 13px;
        }
      }

      &.setting,
      &.refresh {
        background: #fbfbfb;
        color: #92929d;
        padding-top: 1px;
      }
    }

    a.btn-circle.setting,
    a.btn-circle.refresh {
      &:hover,
      &:focus,
      &:active {
        color: #555;
      }
    }

    .card-body > .card-body-container {
      overflow-y: auto;
      overflow-x: visible;
      margin-left: -24px;
      margin-right: -24px;
      padding: 10px 30px 0;
    }
    .card-box {
      width: 100%;
      padding-top: 60%;
      background: #f5f5f5;
      border-radius: 10px;
      margin-bottom: 20px;
      position: relative;
      box-shadow: 3px 3px 8px rgba(0, 0, 0, 0.3);
    }

    .card-box-name {
      text-align: center;
      font-weight: 500;
      margin-bottom: 3px;
    }

    .card-box-inner {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .card-box.virtual-card {
      //  background: rgba(93, 192, 52, 1);
      border-radius: 24.6px;
      .flaticon-settings:before {
        margin: 0;
      }
      .btn-circle {
        &.status {
          position: absolute;
          right: 20px;
          top: 20px;
        }

        &.setting,
        &.refresh {
          position: absolute;
          right: 20px;
          bottom: 20px;
        }
      }

      .icon {
        position: absolute;
        left: 7%;
        top: 10%;
        max-height: 20%;
        max-width: 40%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }

      .card-status {
        position: absolute;
        right: 10%;
        top: 15%;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        padding: 0 10px;
        color: white;
        text-transform: uppercase;
        font-size: 12px;
        box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);
      }

      .card-number,
      .expire,
      .holder {
        font-family: "OCR A Extended", "Rubik", "Helvetica Neue", Helvetica,
          Arial, sans-serif;
        font-size: 21px;
        position: absolute;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        cursor: pointer;
      }

      .card-number:hover,
      .expire:hover,
      .holder:hover {
        text-shadow: 0 3px 6px rgba(0, 0, 0, 0.8);
      }

      .card-number {
        width: 82%;
        text-align: justify;
        top: 50%;
        transform: translateY(-25%);

        &:after {
          content: "";
          display: inline-block;
          width: 100%;
        }
      }

      .mc-icon {
        position: absolute;
        width: 50px;
        height: 50px;
        right: 7%;
        bottom: 9%;
        cursor: pointer;
      }

      .expire {
        left: 9%;
        bottom: 12%;
      }

      .holder {
        left: 45%;
        bottom: 12%;
      }
    }
    img {
      margin-top: 10px;
    }
    .q-if,
    .q-if.q-if-focused {
      border: solid 1px #e2e2eb;
      border-radius: 10px;
      color: #939493;
    }
  }
  .editor-item {
    max-width: 500px;
  }
  .gutter-lg > div {
    padding-top: 0;
  }
  .remove-icon {
    margin: 10px 0;
    color: #fff;
    border-radius: 5px;
    background: #f10012;
    text-transform: none;
  }
  .btn-list {
    margin-top: 59px;
    color: #fff;
    .test-item {
      border-radius: 5px;
      background-color: #ff974a;
      margin-left: 10px;
      text-transform: none;
      padding-left: 5px;
    }
    .update-item {
      border-radius: 5px;
      background-color: #3dd598;
      text-transform: none;
      padding-left: 5px;
    }
  }
  .status-select {
    .q-toggle-base {
      background: #fff;
      border: 1px solid #4cd964;
      height: 15px;
    }
    .q-toggle-handle {
      width: 22px;
      height: 22px;
      top: 1px;
    }
    .active .q-toggle-base,
    .q-toggle-handle {
      background: #4cd964;
      line-height: 15px;
    }
    .q-option-label {
      font-weight: 600;
      color: #171726;
    }
  }
  .transaction-preview {
    height: 100%;
    .input-item {
      height: calc(100% - 50px);
    }
    .transaction-desc {
      margin: 10px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      p {
        margin: 0;
      }
      span {
        display: block;
      }
      img {
        max-width: 56px;
      }
      .text-item {
        color: #8d95a6;
        font-size: 12px;
      }
      .text-title {
        font-size: 14px;
        font-weight: 600;
      }
      .title-item {
        color: #202d4a;
        font-size: 14px;
        font-weight: 600;
      }
    }
  }
}
</style>
