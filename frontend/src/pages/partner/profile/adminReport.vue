<template>
  <q-card class="wide-card">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :hide-bottom="true"
               :loading="loading"
               class="inner-table"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">Admin Report</div>
        </template>
        <template slot="top-right">
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="props.row['Status'] === 'active' ? 'positive' : 'negative'">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              label="Status">
                <q-list v-if="props.row['Status'] === 'active'"
                        link>
                  <q-item v-close-overlay
                          @click.native="status(props.row)">
                    <q-item-main>Inaction</q-item-main>
                  </q-item>
                </q-list>
                <q-list v-if="props.row['Status'] !== 'active'"
                        link>
                  <q-item v-close-overlay
                          @click.native="status(props.row)">
                    <q-item-main>Active</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>
      </q-table>
      <span v-if="data.length && entity['admin']"
            @click="$c.loginAs(entity['admin']['id'])"
            class="auto-login">View all admin</span>

    </q-card-main>

  </q-card>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { generateColumns, request } from '../../../common'

export default {
  name: 'admin-report',
  mixins: [
    ListPageMixin
  ],
  props: {
    uid: {
      type: String
    }
  },
  data () {
    return {
      title: 'Member Profile',
      requestUrl: `/admin/partners-account-search`,
      columns: generateColumns([
        'Admin Name', 'Admin ID', 'Date & time Assigned',
        'Admin Email', 'Admn Phone Number', 'Status', 'Action'
      ]),
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 5,
        page: 1
      },
      keyword: '',
      autoLoad: true
    }
  },
  methods: {
    async status (item) {
      console.log(item['Status'] !== 'active')
      this.loading = true
      const data = {
        status: item['Status'] !== 'active'
      }
      const resp = await request(`/admin/partner/${item['Admin ID']}/updateStatus`, 'post', data)
      this.loading = false
      if (resp.success) {
        this.reload()
      }
    }
  }
}
</script>
<style lang="scss">
.auto-login {
  display: block;
  margin-top: 15px;
  text-align: center;
  cursor: pointer;
  text-transform: uppercase;
  color: #f10012;
  &:hover {
    color: #d80412;
  }
}
</style>
