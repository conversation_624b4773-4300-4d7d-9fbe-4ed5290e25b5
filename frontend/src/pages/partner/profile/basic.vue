<template>
  <q-card id="partner__profile_basic"
          class="high-card">
    <q-card-main>
      <div class="radius-box bg-positive"
           v-if="entity['admin']">
        <q-icon name="mdi-lock-open-outline"></q-icon>
      </div>
      <div class="radius-box bg-negative"
           v-else>
        <q-icon name="mdi-lock-outline"></q-icon>
      </div>

      <div class="text-center">
        <div class="avatar mt-5">
          <img :src="'/static/img/avatar.png'"
               alt="">
        </div>
        <div class="font-20 bold mt-5 partner-name">{{ entity['name'] }}</div>
        <div class="text-faded">{{ entity['id'] }}</div>

        <div class="font-16 heavy text-black mt--3 balance-item justify-center">
          <div>
            <strong v-if="entity['admin']">
              {{entity['admin']['balance'] | moneyFormat}}
            </strong>
            <strong v-else>{{ 0 | moneyFormat}}</strong>

            <span>Partner Balance</span>
          </div>
          <div class="left-item">
            <strong v-if="entity['admin']">
              {{entity['admin']['programBalance'] | moneyFormat}}
            </strong>
            <strong v-else>{{ 0 | moneyFormat}}</strong>
            <span>Program Balance</span>
          </div>
        </div>

      </div>
      <table class="fields-table">
        <tr v-for="(r, i) in fields"
            :key="i">
          <th>{{ i }}</th>
          <td><span class="font-partner"
                  v-if="entity['admin']">{{ entity['admin'][r] }}</span></td>
        </tr>
        <tr>
          <th>Role</th>
          <td>Partner</td>
        </tr>
      </table>
      <span v-if="entity['admin']"
            @click="$c.loginAs(entity['admin']['id'])"
            class="login-btn"><i class="mdi mdi-view-dashboard"></i>Login to Partner Dashboard</span>
    </q-card-main>
  </q-card>
</template>

<script>
export default {
  name: 'partner-profile-basic',
  props: {
    entity: {
      type: Object
    }
  },
  data () {
    return {
      fields: {
        'Location': 'address',
        'Email': 'email',
        'Phone': 'phone'
      }
    }
  },
  methods: {
  }
}
</script>

<style lang="scss">
#partner__profile_basic {
  .avatar {
    width: 130px;
    height: 130px;
    background-color: #eee;
    border-radius: 20px;
    margin: 0 auto;
    overflow: hidden;
  }

  .partner-name {
    color: #171726;
    margin: 5px 0;
  }
  .font-partner {
    color: #171726;
  }
  .radius-box {
    border: none;
    color: white;
    position: absolute;
    right: 16px;
    top: 16px;
    font-size: 18px;
    padding: 4px 5px;
    line-height: 1em;

    &.bg-positive {
      background-color: #3dd598 !important;
    }
  }

  .fields-table {
    th {
      min-width: 60px !important;
    }
  }
  .q-chip.warning {
    background-color: rgba($color: #ffc300, $alpha: 0.1);
    color: #ffc300 !important;
  }
  .q-chip.positive {
    background-color: rgba($color: #00d993, $alpha: 0.1);
    color: #00d993 !important;
  }
  .q-chip.negative {
    background-color: rgba($color: #fc5a5a, $alpha: 0.1);
    color: #fc5a5a !important;
  }
  .q-chip.orange {
    background-color: rgba($color: #ff974a, $alpha: 0.1);
    color: #ff974a !important;
  }
  .balance-item {
    display: flex;
    align-items: center;
    div {
      padding: 0 10px;
      margin: 10px 0;
      span {
        color: #92929e;
        font-size: 16px;
      }
      strong {
        font-size: 28px;
        display: block;
      }
    }
    .left-item {
      border-left: 1px solid #d3d3d8;
    }
  }
  .login-btn {
    display: block;
    display: flex;
    justify-content: center;
    width: 247px;
    margin: 0 auto;
    margin-top: 10px;
    margin-bottom: 10px;
    text-align: center;
    background: #f10012;
    color: #fafafb;
    line-height: 50px;
    border-radius: 20px;
    box-shadow: 0 0 20px 0 rgba(28, 20, 70, 0.1);
    font-size: 14px;
    cursor: pointer;
    i {
      font-size: 24px;
      margin-right: 5px;
    }
  }
}
</style>
