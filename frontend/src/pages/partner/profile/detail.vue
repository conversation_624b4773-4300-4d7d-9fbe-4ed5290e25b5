<template>
  <q-card id="partner__profile_detail"
          class="high-card">
    <q-card-title>
      <div class="row justify-between">
        <div class="font-18 bold">Account Details</div>
        <q-btn icon="mdi-pencil-outline"
               outline
               @click="edit"
               class="btn-mini ml-5 edit-partner"></q-btn>
      </div>
    </q-card-title>
    <q-card-main>
      <table class="fields-table mt-10 mb-auto partner-detail">
        <tr>
          <th>Partner Name</th>
          <td class="font-partner">{{ entity['name'] }}</td>
        </tr>
        <tr>
          <th>Partner Type</th>
          <td class="font-partner">US Unlocked</td>
        </tr>
        <tr>
          <th>Freight Forwarding</th>
          <td><span class="font-partner"
                  v-if="entity['forwarding']">Yes</span><span v-else>No</span></td>
        </tr>
        <tr>
          <th>Store Locator</th>
          <td><span class="font-partner"
                  v-if="entity['locator']">Yes</span><span v-else>No</span></td>
        </tr>
        <tr>
          <th>Home Address</th>
          <td><span class="font-partner"
                  v-if="entity['admin']">{{ entity['admin']['name'] }}</span></td>
        </tr>
        <tr>
          <th>City</th>
          <td><span class="font-partner"
                  v-if="entity['admin']">{{ entity['admin']['city'] }}</span></td>
        </tr>
        <tr>
          <th>Country</th>
          <td><span class="font-partner"
                  v-if="entity['admin']">{{ entity['admin']['country'] }}</span></td>
        </tr>
        <tr>
          <th>State</th>
          <td><span class="font-partner"
                  v-if="entity['admin']">{{ entity['admin']['state'] }}</span></td>
        </tr>
        <tr>
          <th>Zip</th>
          <td><span class="font-partner"
                  v-if="entity['admin']">{{ entity['admin']['zip'] }}</span></td>
        </tr>
      </table>
    </q-card-main>
  </q-card>
</template>

<script>

export default {
  name: 'partner-profile-detail',
  props: {
    entity: {
      type: Object
    }
  },
  data () {
    return {
      fields: [
        'First Name', 'Last Name', 'Date of Birth', 'Mailing Address',
        'City', 'Country', 'State / Province', 'Postal Code'
      ]
    }
  },
  methods: {
    edit () {
      this.$router.push(`/a/partners/edit/${this.entity['id']}`)
    }
  }
}
</script>

<style lang="scss">
#partner__profile_detail {
  .q-card-main {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }
  .font-partner {
    color: #171726;
  }
  .partner-detail {
    th {
      padding-bottom: 20px !important;
    }
  }
  .edit-partner {
    padding: 6px;
  }
}
</style>
