<template>
  <q-card class="wide-card">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :loading="loading"
               :hide-bottom="true"
               class="inner-table"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">Program Fee History</div>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field !== 'Fee Date & time'">
              {{ _.get(props.row, col.field) | moneyFormat}}
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>
      </q-table>
      <span v-if="data.length && entity['admin']"
            @click="$c.loginAs(entity['admin']['id'])"
            class="auto-login">View all transactions</span>

    </q-card-main>

  </q-card>
</template>

<script>
import MexPageMixin from '../../../mixins/mex/MexPageMixin'
import ListPageMixin from '../../../mixins/ListPageMixin'
import { generateColumns } from '../../../common'

export default {
  name: 'fee-history',
  mixins: [
    MexPageMixin,
    ListPageMixin
  ],
  props: {
    uid: {
      type: String
    }
  },
  data () {
    return {
      title: 'Fee History',
      requestUrl: `/admin/partner/fee/list`,
      columns: generateColumns([
        'Fee Date & time', 'MemberShip Fee', 'Load Fee', 'Transaction Fee', 'Monthly Fee',
        'Unload Fee', 'Total Fee', 'Partner Revenue', 'Platform Revenue'
      ]),
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 5,
        page: 1
      },
      filterOptions: [
        {
          value: 'filter[t.id=]',
          label: 'Transaction ID'
        }
      ],
      autoLoad: true
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        member: this.uid
      }
    }
  }
}
</script>
<style lang="scss">
.auto-login {
  display: block;
  margin-top: 15px;
  text-align: center;
  cursor: pointer;
  text-transform: uppercase;
  color: #f10012;
  &:hover {
    color: #d80412;
  }
}
</style>
