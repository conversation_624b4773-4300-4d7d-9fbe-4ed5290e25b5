<template>
  <q-page id="partner__profile_page">
    <div class="row gutter-sm mt-0">
      <div class="col-sm-12 col-md-4">
        <BasicCard :entity="entity"></BasicCard>
      </div>
      <div class="col-sm-12 col-md-4">
        <DetailCard :entity="entity"
                    @reload="reload"></DetailCard>
      </div>
      <div class="col-sm-12 col-md-4">
        <NotesCard :uid="uid"></NotesCard>
      </div>
      <div class="col-12">
        <FeeHistory :uid="uid"></FeeHistory>
      </div>
      <div class="col-12 mb-30">
        <AdminReport :uid="uid"></AdminReport>
      </div>
    </div>
  </q-page>
</template>

<script>
import BasicCard from './basic'
import DetailCard from './detail'
import NotesCard from './notes'
import FeeHistory from './feeHistory'
import AdminReport from './adminReport'
import { EventHandlerMixin, request } from '../../../common'

export default {
  name: 'partner-profile',
  mixins: [
    EventHandlerMixin('reload-partner-profile')
  ],
  components: {
    BasicCard,
    DetailCard,
    NotesCard,
    FeeHistory,
    AdminReport
  },
  data () {
    return {
      title: 'Partner Profile',
      entity: {}
    }
  },
  computed: {
    uid () {
      return this.$route.params.id
    }
  },
  watch: {
    uid () {
      this.reload()
    }
  },
  methods: {
    async reload (indicator = true) {
      indicator && this.$q.loading.show()
      const resp = await request(`/admin/partner/${this.uid}/detail`)
      indicator && this.$q.loading.hide()
      if (resp.success) {
        this.entity = resp.data
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
#partner__profile_page {
  .q-card {
    &.high-card {
      height: 473px;
    }

    &.wide-card {
      min-height: 300px;

      > .q-card-main {
        position: relative;
        height: 100%;
      }
    }

    overflow: auto;

    > .q-card-primary {
      padding-top: 16px;
    }

    > .q-card-main {
      position: relative;
      height: calc(100% - 67px);
    }
  }

  .q-card-title .font-18.bold {
    height: 33px;
    padding-top: 3px;
  }

  .fields-table {
    width: 100%;

    th {
      padding: 5px 5px 5px 0;
      font-weight: normal;
      font-size: 13px;
      color: #888;
      text-align: left;
      min-width: 100px;
      vertical-align: top;
    }

    td {
      padding: 5px 0 5px 5px;
      text-align: right;
      word-break: break-word;
    }
  }

  .empty-tip {
    height: 100%;
    color: #666;

    .q-icon {
      margin-bottom: 5px;
      font-size: 20px;
    }
  }
  .profile-btn {
    background: #9c85ca;
    color: #ffffff;
  }
}
</style>
