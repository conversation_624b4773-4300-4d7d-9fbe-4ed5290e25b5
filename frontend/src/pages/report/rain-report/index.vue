<template>
  <q-page id="report_privacy_card_creation__list_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div>
          <h5>{{ quick.total }}</h5>
          <div class="description">Total Member</div>
        </div>
        <div>
          <h5>{{ quick.migrated  }}</h5>
          <div class="description">Migrated Count</div>
        </div>
        <div>
          <h5>{{ quick.amount  }}</h5>
          <div class="description">Total Migrated Amount</div>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               class="main-table sticky-table"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>
         <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"
                       :max="999999"></BatchExportDialog>
  </q-page>
</template>

<script>
import { generateColumns, request } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  mixins: [
    ListPageMixin,
    FreezeColumnMixin
  ],
  data () {
    return {
      requestUrl: '/admin/report/rain-report/list',
      filtersUrl: '/admin/report/rain-report/filters',
      title: 'Rain Migrated Report',
      autoReloadWhenUrlChanges: true,
      columns: generateColumns([
        'Member Id', 'First Name', 'Last Name', 'Rain User Id', 'Current Balance', 'Privacy Balance'
      ], [
        'Member Id', 'First Name', 'Last Name', 'Rain User Id', 'Current Balance', 'Privacy Balance'
      ], [],
      {
        'Member Id': { 'hidden': false },
        'First Name': { 'hidden': false },
        'Last Name': { 'hidden': false },
        'Current Balance': { 'hidden': false },
        'Privacy Balance': { 'hidden': false }
      }
      ),
      quick: {},
      filterOptions: [
        {
          value: 'isMigrated',
          label: 'Migrated',
          options: [
            { label: 'Yes', value: 1 },
            { label: 'No', value: 0 }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: 0
    }
  },
  methods: {
    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.$refs.filtersDialog.getFilters()
      const resp = await request(`/admin/report/rain-report/list/${pagination.page}/${pagination.rowsPerPage}`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.count
        this.quick = resp.data.quick
      }
    }
  }
}
</script>
