<template>
  <q-page id="report_privacy_card_creation__list_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-btn class="mr-10"
               color="black"
               @click="download"
               label="Export"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table"
               @request="request"
               separator="none"
               :rows-per-page-options="[5, 10, 20, 50]"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props">
          <template slot-scope="{ col }">
            {{ col.label }}
            <q-icon name="mdi-help-circle-outline" class="ml-3" v-if="tips[col.label]">
              <q-tooltip>{{ tips[col.label] }}</q-tooltip>
            </q-icon>
          </template>
        </StickyHead>

        <q-tr slot="body"
              slot-scope="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="moneyColumns.includes(col.field)">
              {{ col.value | moneyFormat }}
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>
        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"
                       :max="999999"></BatchExportDialog>
  </q-page>
</template>

<script>
import { generateColumns, isSuperAdmin } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  mixins: [
    ListPageMixin,
    FreezeColumnMixin
  ],
  data () {
    return {
      requestUrl: '/admin/report/total-account-balance/list',
      downloadUrl: '/admin/report/total-account-balance/export',
      title: 'Daily Data Report',
      cardProgramField: 'filter[cp.id=]',
      autoReloadWhenUrlChanges: true,
      columns: generateColumns([
        'Date',
        'Positive cards', 'Positive users',
        '30-day active users', '90-day active users',
        '180-day active users', 'Inactive cards', 'New users',
        'Churn', 'Reactivated users', 'Loads count',
        'Loads amount', 'Avg load', 'Spend count',
        'Spend amount', 'Avg spend',
        'Issued Card In This Month', 'Total Issued Cards',
        'Signups(verified email) In This Month',
        'Total Account Balance', 'Usable Spending Limit'
      ]),
      moneyColumns: [
        'Loads amount', 'Avg load',
        'Spend amount', 'Avg spend',
        'Total Account Balance',
        'Usable Spending Limit'
      ],
      tips: {
        'Positive cards': 'All cards with a positive balance',
        'Positive users': 'All users with a positive balance',
        '30-day active users': 'All users with a spend transaction in the last 30 days',
        '90-day active users': 'All users with a spend transaction in the last 90 days',
        '180-day active users': 'All users with a spend transaction in the last 180 days',
        'Inactive cards': 'Total cards that once had a balance but are now at zero',
        'New users': 'New users that have successfully loaded their card',
        'Churn': 'Users whose balance reached zero',
        'Reactivated users': 'Churned users who successfully loaded their card again',
        'Loads count': 'Number of successful loads',
        'Loads amount': 'Total successful load amount',
        'Avg load': 'Loads amount /Loads count',
        'Spend count': 'Number of spend transactions',
        'Spend amount': 'Total spend',
        'Avg spend': 'Spend amount / spend count'
      },
      quick: {},
      filterOptions: [
        {
          value: 'time',
          label: 'Date',
          range: [
            {
              value: 'range[usd.date][start]',
              type: 'date'
            }, {
              value: 'range[usd.date][end]',
              type: 'date'
            }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: 0
    }
  },
  mounted () {
    if (!isSuperAdmin()) {
      const excepts = ['Total Account Balance', 'Usable Spending Limit']
      this.columns = this.columns.filter(v => !excepts.includes(v.field))
    }
  }
}
</script>
