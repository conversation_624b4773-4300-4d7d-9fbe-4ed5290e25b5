<template>
  <q-page id="report_privacy_card_creation__list_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div>
          <h5>{{ quick.total }}</h5>
          <div class="description">Total Count</div>
        </div>
        <div>
          <h5>{{ quick.voice }}</h5>
          <div class="description"> Voice Count</div>
        </div>
        <div>
          <h5>{{ quick.sms }}</h5>
          <div class="description">SMS Count</div>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="no-pagination main-table sticky-table"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"
                       :max="999999"></BatchExportDialog>
  </q-page>
</template>

<script>
import { generateColumns } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  mixins: [
    ListPageMixin,
    FreezeColumnMixin
  ],
  data () {
    return {
      requestUrl: '/admin/report/twilio-report/list',
      filtersUrl: '/admin/report/twilio-report/filters',
      title: 'TwilioReport',
      autoReloadWhenUrlChanges: true,
      columns: generateColumns([
        'Month', 'Platform Name', 'Voice Count', 'Voice Domestic Count', 'Voice International Count',
        'SMS Count', 'SMS Domestic Count', 'SMS International Count', 'Total Count'
      ], [
        'Month', 'Voice Count', 'Voice Domestic Count', 'Voice International Count', 'SMS Count', 'SMS Domestic Count', 'SMS International Count', 'Total Count'
      ], [],
      {
        'Platform Name': { 'hidden': false },
        'Voice Count': { 'hidden': false },
        'Voice Domestic Count': { 'hidden': false },
        'Voice International Count': { 'hidden': false },
        'SMS Count': { 'hidden': false },
        'SMS Domestic Count': { 'hidden': false },
        'SMS International Count': { 'hidden': false },
        'Total Count': { 'hidden': false }
      }
      ),
      quick: {},
      filterOptions: [
        {
          value: 'month',
          label: 'Month',
          options: [],
          source: 'monthList'
        }
      ],
      autoLoad: true,
      freezeColumn: 0
    }
  }
}
</script>
