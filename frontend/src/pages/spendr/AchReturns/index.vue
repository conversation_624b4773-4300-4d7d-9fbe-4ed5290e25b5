<template>
  <q-page id="spendr_ach_returns_transactions__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :iso-week="true"
                         :default="dateRange"
                         :ranges="datesWithDaily"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content top-statics-style">
      <div class="row gutter-sm">
        <div class="col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="fuschia"></q-icon>
                <div class="column">
                  <div class="description">Total Returns</div>
                  <div class="value">{{ quick.totalAmount | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
    </div>
    <q-table :data="data"
             :columns="filteredVisibleColumns"
             :pagination.sync="pagination"
             :loading="loading"
             class="main-table sticky-table mt-16"
             @request="request"
             separator="none"
             row-key="id">
      <template slot="top-left">
        <strong>{{ title }} Report</strong>
      </template>
      <template slot="top-right">
        <q-btn v-if="isMasterAdmin() || agentAdmin || spendrCompliance"
          icon="mdi-file-download-outline"
               color="blue"
               label="Upload Returns File"
               @click="upload"
               class="btn-sm mr-8"
               no-caps></q-btn>
        <q-btn v-if="!bankAdmin" icon="mdi-file-download-outline"
               color="blue"
               label="Export as XLSX"
               @click="download"
               class="btn-sm mr-8"
               no-caps></q-btn>
        <q-search icon="search"
                  v-model="keyword"
                  type="text"
                  class="mr-10 w-200 dense"
                  @keydown.13="delayedReload"
                  @clear="delayedReload"
                  clearable
                  placeholder="Search...">
        </q-search>
        <q-btn flat
               round
               dense
               color="faded"
               class="mr-10"
               icon="mdi-filter-outline"
               @click="filtersDialog = true">
          <q-chip floating
                  class="dot"
                  v-if="validFilters.length"></q-chip>
        </q-btn>
        <q-btn flat
               round
               dense
               color="faded"
               @click="reload"
               icon="refresh" />
      </template>
      <StickyHead slot="header"
                  slot-scope="props"
                  :freeze-column.sync="freezeColumn"
                  :column-styles="columnStyles"
                  :column-classes="columnClasses"
                  :readonly="true"
                  :sort="true"
                  @change="reload"
                  ref="stickyHead"
                  :props="props"></StickyHead>
      <q-tr slot="body"
            slot-scope="props"
            :props="props"
            class="receipt-item">
        <q-td v-for="(col, i) in props.cols"
              :key="col.field"
              :align="col.align"
              :style="columnStyles[i]"
              :class="columnClasses[i]">
          <template v-if="col.field === 'Payment Amount'">
            <span>{{ _.get(props.row, col.field) }}</span>
          </template>
          <template v-else>
            <div notranslate="">{{ _.get(props.row, col.field) }}</div>
          </template>
        </q-td>
      </q-tr>
      <div slot="pagination"
           slot-scope="props">
        <Pagination :props="props"
                    :pagination="pagination"
                    @reload="reload"></Pagination>
      </div>
    </q-table>
    <UploadReturn></UploadReturn>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import UploadReturn from './upload'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'

export default {
  name: 'spendr-ach-returns',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-ach-returns')
  ],
  components: {
    UploadReturn
  },
  data () {
    return {
      title: 'Nacha Returns',
      url: '',
      requestUrl: `/admin/spendr/ach-returns/list`,
      downloadUrl: `/admin/spendr/ach-returns/export`,
      columns: generateColumns([
        'Bank ID', 'Institution',
        'ACH Batch ID', 'Load ID', 'Created Date', 'Send Date',
        'Trx Type', 'Payment Amount', 'Return Date', 'Status'
      ]),
      filterOptions: [
        {
          value: 'filter[ach.batchId=]',
          label: 'ACH Batch ID'
        }
      ],
      keyword: '',
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true
    }
  },
  methods: {
    statusClass (status) {
      return {
        'Batched': 'blue',
        'unloaded': 'positive',
        'Error': 'negative'
      }[status] || status
    },
    upload () {
      this.$root.$emit('show-spendr-upload-returns-dialog')
    }
  }
}
</script>

<style lang="scss">
#spendr_ach_returns_transactions__index_page {
}
</style>
