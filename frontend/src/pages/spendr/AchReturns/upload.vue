<template>
  <q-dialog id="spendr-upload-returns-dialog"
            v-model="visible">
    <template slot="title">
      <template>
        <div class="font-14 normal">
          Please upload a NACHA payroll file.
        </div>
      </template>
      <q-btn class="close"
             round
             flat
             @click="cancel"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row">
        <div class="pt-20 col-12">
          <div class="upload-area"
               @click="select"
               :class="{selected: file}">
            <img src="/static/wilen/document-icon.svg">
            <template v-if="file">
              <div class="mt-10">Selected file:</div>
              <div class="font-13 text-blue">
                <span>{{ file.name }}</span>
                <a href="javascript:"
                   class="ml-5 link"
                   @click.stop="file = null">
                  <q-icon name="mdi-close-circle-outline"
                          class="font-20"
                          color="negative"></q-icon>
                  <q-tooltip>Remove and reselect</q-tooltip>
                </a>
              </div>
            </template>
            <template v-else>
              <div class="mt-10">Upload a NACHA file by dragging the file here.</div>
            </template>
          </div>
          <input type="file"
                 class="hide"
                 accept=".ach"
                 ref="file"
                 @change="selectedFile">
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn label="Done"
               no-caps
               color="positive"
               class="continue-btn"
               @click="confirm"></q-btn>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { EventHandlerMixin, uploadAttachment, notifyResponse, notify, request } from '../../../common'
import $ from 'jquery'

export default {
  name: 'spendr-upload-returns-dialog',
  mixins: [
    Singleton,
    EventHandlerMixin('spendr-upload-returns-dialog', 'show')
  ],
  data () {
    return {
      showing: false,
      file: null,
      attachment: null
    }
  },
  watch: {
    file () {
      this.attachment = null
    }
  },
  created () { },
  components: {},
  computed: {},
  methods: {
    show () {
      this.file = null
    },
    cancel () {
      this.file = null
      this._hide()
    },
    async confirm () {
      if (this.file) {
        if (!this.attachment) {
          this.$q.loading.show({ message: 'Uploading...' })
          const category = 'Spendr ACH Returns'
          const resp = await uploadAttachment(this.file, category)
          if (typeof resp === 'string') {
            this.$q.loading.hide()
            return notifyResponse(`Failed to upload: ${resp}`)
          }
          this.attachment = resp
        }
        this.$q.loading.show({ message: 'Importing and verifying ...' })
        const resp = await request(`/admin/spendr/ach-returns/upload`, 'post', {
          attachment: this.attachment.id
        }, true)
        this.$q.loading.hide()
        if (resp.success) {
          this.cancel()
          notify('Operation finished successfully!')
          setTimeout(() => {
            this.$root.$emit('reload-ach-returns')
          }, 1000)
        } else {
          notify(resp.message, 'negative')
        }
      }
    },
    select () {
      if (this.file) {
        return
      }
      $(this.$refs.file).click()
    },
    selectedFile () {
      const input = this.$refs.file
      if (input.files.length) {
        this.file = input.files[0]
      }
      input.type = 'text'
      input.type = 'file'
    },
    getValidDragFile (e) {
      if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length) {
        const file = e.dataTransfer.files[0]
        if (file && (file.name.indexOf('.ach') !== -1 || file.name.indexOf('.ACH') !== -1)) {
          return file
        }
      }
      return null
    },
    setupDnd () {
      const area = $(this.$el).find('.upload-area'),
        dom = area.get(0)

      dom.addEventListener('dragover', e => {
        e.stopPropagation()
        e.preventDefault()
        area.addClass('dropping')
      }, false)

      dom.addEventListener('dragleave', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')
      }, false)

      dom.addEventListener('drop', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')

        const file = this.getValidDragFile(e)
        if (file) {
          this.file = file
        } else {
          notify('Please select a NACHA file.', 'negative')
        }
      }, false)
    }
  },
  mounted () {
    this.setupDnd()
  }
}
</script>

<style lang="scss">
#spendr-upload-returns-dialog {
  .modal-content {
    width: 450px;
  }
  .upload-area {
    border: 2px dashed #e2e2e2;
    border-radius: 14px;
    padding: 25px 15px;
    cursor: pointer;
    display: block;
  }
  .continue-btn {
    background-color: #7ac142;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
    .q-btn-inner {
      line-height: 28px;
      color: #fafafb;
    }
  }

  .text-blod {
    font-weight: bold;
  }
}
</style>
