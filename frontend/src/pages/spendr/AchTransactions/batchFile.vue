<template>
  <q-dialog
    class="spendr-admin-batch-dialog"
    v-model="visible"
    prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">ACH Processing Type</div>
      <div class="font-12 normal text-dark">Please fill in the information below.</div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="pt-20" :class="'col-sm-12'">
<!--          <div class="bold">Process Type</div>-->
          <div class="mt-8">
            <q-radio v-model="entity['Type']" color="blue"
                     :error="$v.entity['Type'].$error"
                     @change="$v.entity['Type'].$touch"
                     val="Same day" label="Same day" class="mr-15 mb-5"></q-radio>
            <q-radio v-model="entity['Type']" color="blue" class="mr-15 mb-5"
                     :error="$v.entity['Type'].$error"
                     @change="$v.entity['Type'].$touch"
                     val="Standard" label="Standard"></q-radio>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Cancel" no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
        <q-btn :label="'Continue'"
               no-caps
               color="positive" class="main"
               @click="batch" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, notifyForm, request } from '../../../common'
import { required } from 'vuelidate/lib/validators'

export default {
  name: 'spendr-admin-batch-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        'Type': '',
        'batchForTest': false
      }
    }
  },
  validations: {
    entity: {
      Type: {
        required
      }
    }
  },
  methods: {
    async batch () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Creating file...'
      })
      const resp = await request(`/admin/spendr/ach-files/batch`, 'post', this.entity)
      this.$q.loading.hide()
      let that = this
      if (resp.success) {
        notify(resp.message)
        setTimeout(function () {
          that.$root.$emit('reload-ach-file')
          that._hide()
        }, 1000)
      }
    }
  }
}
</script>

<style lang="scss">
.spendr-admin-batch-dialog {
  .modal-content {
    width: 580px;
  }

  .modal-scroll {
    max-height: none;
  }
}
</style>
