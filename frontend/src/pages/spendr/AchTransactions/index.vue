<template>
  <q-page id="spendr_ach_transaction__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalNoBatch || 0 }}</div>
                  <div class="description">Total No Batch</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.pendingTotal | moneyFormat }}</div>
                  <div class="description">Pending Transactions</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalCredits | moneyFormat }}</div>
                  <div class="description">Total Credits</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="fuschia"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalDebits | moneyFormat }}</div>
                  <div class="description">Total Debits</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }} Report</strong>
        </template>
        <template slot="top-right">
          <template v-if="isMasterAdmin()">
            <q-btn color="red"
                   label="Batch File For Test"
                   @click="batchFile(true)"
                   icon="mdi-file-multiple-outline"
                   class="btn-sm mr-8"
                   no-caps></q-btn>
          </template>
          <template v-if="isMasterAdmin() || agentAdmin || spendrCompliance">
            <q-btn color="positive"
                   label="Batch File"
                   @click="batchFile"
                   icon="mdi-file-multiple-outline"
                   class="btn-sm mr-8"
                   no-caps></q-btn>
            <q-btn color="negative"
                   label="Reset Failed Batch"
                   @click="resetFailedBatch"
                   class="btn-sm mr-8"
                   no-caps></q-btn>
          </template>
          <q-btn icon="mdi-file-download-outline"
                 v-if="!bankAdmin"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              class="receipt-item">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Actions' && !bankAdmin">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          v-if="!props.row['Last Download'] && props.row['canDownload'] && !spendrAccountant"
                          @click.native="downloadFile(props.row)">
                    <q-item-main>Download</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Last Download'] && props.row['canDownload'] && !spendrAccountant && !props.row['batchForTest']"
                          @click.native="redownLoad(props.row)"
                          v-close-overlay>
                    <q-item-main>Re-Download</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="viewFile(props.row)">
                    <q-item-main>View TRX</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else-if="col.field === 'NACHA Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['NACHA Status'].toLowerCase())">
                {{ props.row['NACHA Status'] }}
              </q-chip>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>
        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <ReDownload></ReDownload>
    <BatchFile></BatchFile>
    <form ref="download"
          method="post"
          action="/admin/download">
      <input type="hidden"
             name="path"
             v-model="url">
    </form>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notifySuccess, request } from '../../../common'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import ReDownload from './redownload'
import BatchFile from './batchFile'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'

export default {
  name: 'spendr-ach-file',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-ach-file')
  ],
  components: {
    ReDownload,
    BatchFile
  },
  data () {
    let columns = [
      'Batch ID', 'File Name', 'Create Date', 'Last Download', 'TRX Count',
      'TRX Amount', 'Process Type', 'NACHA Status', 'Actions'
    ]
    if (this.isBankAdmin()) {
      columns.splice(columns.indexOf('Actions'), 1)
    }
    return {
      title: 'Nacha Activity',
      url: '',
      requestUrl: `/admin/spendr/ach-files/list`,
      downloadUrl: `/admin/spendr/ach-files/export`,
      columns: generateColumns(columns),
      filterOptions: [],
      keyword: '',
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true
    }
  },
  methods: {
    // async batchFile () {
    //   this.$q.loading.show({
    //     message: 'Creating file...'
    //   })
    //   const resp = await request(`/admin/spendr/ach-files/batch`, 'post')
    //   this.$q.loading.hide()
    //   let that = this
    //   if (resp.success) {
    //     notify(resp.message)
    //     setTimeout(function () {
    //       that.$root.$emit('reload-ach-file')
    //     }, 2000)
    //   }
    // },
    async downloadFile (row) {
      this.$q.loading.show({
        message: 'Downloading file...'
      })
      const resp = await request(`/admin/spendr/ach-file/${row['Batch ID']}/download`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        this.downloadTheFile(resp.data)
        // notify(resp.message)
      }
    },
    downloadTheFile (url) {
      if (!url) {
        return
      }
      this.url = url

      this.$nextTick(() => {
        this.$q.loading.show({
          message: 'Downloading file...'
        })
        this.$refs.download.submit()

        setTimeout(() => {
          this.$q.loading.hide()
          this.$root.$emit('reload-ach-file')
        }, 1000)
      })
    },
    redownLoad (row) {
      this.$root.$emit('show-spendr-admin-redownload-dialog', row)
    },
    batchFile (batchForTest = false) {
      let data = {}
      if (batchForTest === true) {
        data = {
          'batchForTest': batchForTest
        }
      }
      this.$root.$emit('show-spendr-admin-batch-dialog', data)
    },
    resetFailedBatch () {
      this.$q.dialog({
        title: `Reset Failed Batch`,
        message: 'Are you sure that you want to reset the failed batch? ',
        cancel: {
          color: 'faded',
          flat: true
        },
        ok: {
          label: 'Yes',
          color: 'negative',
          flat: true
        }
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/spendr/ach-files/reset-failed`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          notifySuccess('Submitted the request to reset failed batch. Please wait and notice the Slack alerts.')
        }
      }).catch(() => {})
    },
    statusClass (status) {
      return {
        'Batched': 'blue',
        'unloaded': 'positive',
        'Error': 'negative'
      }[status] || status
    },
    viewFile (row) {
      window.open(`/admin#/h/spendr/ach-file/${row['Batch ID']}`, '_blank')
    }
  }
}
</script>
