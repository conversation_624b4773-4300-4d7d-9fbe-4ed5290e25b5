<template>
  <q-dialog class="spendr-admin-redownload-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">File has already been downloaded</div>
      <div class="font-12 normal text-dark">This file has already been downloaded. Please verify within your banking portal that this NACHA file has not already been processed. Processing the same file twice will result in duplicated transacations. </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <form ref="download"
            method="post"
            action="/admin/download">
        <input type="hidden"
               name="path"
               v-model="url">
      </form>
      <div class="row alet-message">
        <q-icon name="mdi-alert-circle-outline"
                class="redownload-item"></q-icon>
        By clicking “I Understand” you agree that <PERSON><PERSON> is not responsible for any duplicated transactions that may occur from the same file being processed twice.
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="I Understand"
               no-caps
               color="blue"
               @click="downloadFile" />
        <q-btn label="Cancel"
               no-caps
               color="blue"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { request } from '../../../common'

export default {
  name: 'spendr-admin-redownload-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      url: ''
    }
  },
  methods: {
    show () {
      this.visible = true
      this.url = ''
    },
    cancel () {
      this.visible = false
    },
    async downloadFile () {
      this.$q.loading.show({
        message: 'Downloading file...'
      })
      const resp = await request(`/admin/spendr/ach-file/${this.entity['Batch ID']}/download`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        this.download(resp.data)
      }
    },
    download (url) {
      if (!this.visible || !url) {
        return
      }
      this.url = url

      this.$nextTick(() => {
        this.$q.loading.show({
          message: 'Downloading file...'
        })
        this.$refs.download.submit()

        setTimeout(() => {
          this.visible = false
          this.$q.loading.hide()
          this.$root.$emit('reload-ach-file')
        }, 1000)
      })
    }
  }
}
</script>

<style lang="scss">
.spendr-admin-redownload-dialog {
  .modal-content {
    width: 580px;
  }

  .alet-message {
    font-size: 8px;
    color: #ff0000;
  }
  .redownload-item {
    font-size: 64px;
    color: #d97706;
    margin: 44px auto;
  }
  .modal-scroll {
    max-height: none;
  }
}
</style>
