<template>
  <q-dialog
    class="spendr-admin-detail-dialog"
    v-model="visible"
    prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">{{ edit ? 'Edit Admin' : 'Create New Admin' }}</div>
      <div class="font-12 normal text-dark">Please fill in the information below about the admin.</div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-6">
          <q-input float-label="First Name" autocomplete="no"
                   :error="$v.entity['First Name'].$error"
                   @input="$v.entity['First Name'].$touch"
                   v-model="entity['First Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Last Name" autocomplete="no"
                   :error="$v.entity['Last Name'].$error"
                   @input="$v.entity['Last Name'].$touch"
                   v-model="entity['Last Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Email" autocomplete="no"
                   :error="$v.entity['Email'].$error"
                   @input="$v.entity['Email'].$touch"
                   v-model="entity['Email']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Phone Number" autocomplete="no"
                   :error="$v.entity['Phone'].$error"
                   @input="$v.entity['Phone'].$touch"
                   v-model="entity['Phone']"></q-input>
        </div>
        <div class="pt-20" :class="'col-sm-12'">
          <div class="bold">Role</div>
          <div class="mt-8">
            <q-radio v-model="entity['User Type']" color="blue"
                     :error="$v.entity['User Type'].$error"
                     @change="$v.entity['User Type'].$touch"
                     val="Program Owner" label="Program Owner" class="mr-15 mb-5"></q-radio>
            <q-radio v-model="entity['User Type']" color="blue" class="mr-15 mb-5"
                     :error="$v.entity['User Type'].$error"
                     @change="$v.entity['User Type'].$touch"
                     val="Bank" label="Bank"></q-radio>
            <q-radio v-model="entity['User Type']" color="blue" class="mr-15 mb-5"
                     :error="$v.entity['User Type'].$error"
                     @change="$v.entity['User Type'].$touch"
                     val="Customer Support" label="Customer Support"></q-radio>
            <q-radio v-model="entity['User Type']" color="blue"
                     :error="$v.entity['User Type'].$error"
                     @change="$v.entity['User Type'].$touch"
                     val="Compliance" label="Compliance"></q-radio>
            <q-radio v-model="entity['User Type']" color="blue"
                     :error="$v.entity['User Type'].$error"
                     @change="$v.entity['User Type'].$touch"
                     val="Accountant" label="Accountant"></q-radio>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Cancel" no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
        <q-btn :label="edit ? 'Save Changes' : 'Create Admin'"
               no-caps
               color="positive" class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request, commonUserEntityValidator } from '../../../common'
import _ from 'lodash'

export default {
  name: 'spendr-admin-detail-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        'User ID': 0
      }
    }
  },
  computed: {
    edit () {
      return this.entity['User ID']
    },
    user () {
      return this.$store.state.User
    }
  },
  validations: commonUserEntityValidator([
    'First Name', 'Last Name', 'User Type'
  ]),
  methods: {
    async show () {
      if (!this.entity || !this.entity['User ID']) {
        return
      }
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/admins/${this.entity['User ID']}/details`, 'get')
      this.$q.loading.hide()
      if (resp.success) {
        this.entity = _.assignIn({}, this.entity, resp.data)
      }
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/admins/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.$root.$emit('reload-spendr-admins')
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.spendr-admin-detail-dialog {
  .modal-content {
    width: 580px;
  }

  .modal-scroll {
    max-height: none;
  }
}
</style>
