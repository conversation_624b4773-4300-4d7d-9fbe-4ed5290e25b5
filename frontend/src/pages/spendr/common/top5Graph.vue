<template>
  <q-card class="top5Balance mt-16">
    <q-card-title>
      <div class="row">
        <div class="chart-title">
          <p>Top 5 {{ title }}</p>
        </div>
      </div>
    </q-card-title>
    <q-card-main class="chart-item">
      <div :id="chartId"
           class="chart-item"></div>
      <div v-if="visible"
           class="loading">
        <q-spinner-hourglass color="primary"
                             size="3em"
                             :thickness="2" />
      </div>
    </q-card-main>
  </q-card>
</template>

<script>
import SpendrChartMixin from '../../../mixins/spendr/SpendrChartMixin'
import $ from 'jquery'
import { EventHandlerMixin } from '../../../common'

export default {
  mixins: [
    SpendrChartMixin,
    EventHandlerMixin('reload-top5-graph', 'getData')
  ],
  model: {
    prop: 'value',
    event: 'change'
  },
  watch: {
    value () {
      this.getData()
    }
  },
  data () {
    return {
    }
  },
  async mounted () {
    this.getData()
  },
  methods: {
    resize () {
      this.chart.resize()
    },
    async getData () {
      this.visible = true
      this.initChart()
      await this.reloadData()
      this.visible = false
      $(window).on('resize', this.resize)
    }
  }
}
</script>
<style lang="scss">
.top5Balance {
  width: 100%;
  .chart-title {
    p {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
    span {
      font-size: 28px;
      font-weight: 600;
    }
  }
  .chart-item {
    height: 400px;
  }
  .chart-item {
    position: relative;
  }
  .loading {
    height: 400px;
    line-height: 400px;
    width: calc(100% - 32px);
    text-align: center;
    position: absolute;
    top: 0;
    z-index: 100;
    background: rgba($color: #000000, $alpha: 0.1);
  }
  .tooltip-area-left :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    left: 40px;
  }
  .tooltip-area-right :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    right: 40px;
  }
}
</style>
