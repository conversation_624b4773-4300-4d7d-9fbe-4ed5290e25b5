<template>
  <q-page id="spendr_consumers__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content top-statics-style">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="description">Total Consumers</div>
                  <div class="value">{{ quick.total || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="purple"></q-icon>
                <div class="column">
                  <div class="description">Total Consumer Spend</div>
                  <div class="value">{{ quick.totalSpend || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="orange"></q-icon>
                <div class="column">
                  <div class="description">Average TRX Size</div>
                  <div class="value">{{ quick.averageTRx || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"
                        color="orange"></q-icon>
                <div class="column">
                  <div class="description">Average Consumer Age</div>
                  <div class="value">{{ quick.averageConsumerAge || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }} Report</strong>
        </template>
        <template slot="top-right">
          <q-btn v-if="!spendrCustomerSupportLogin && !merchantOtherAdmin"
                 icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <div notranslate="">
              <template v-if="col.field === 'User ID'">
                <span class="click-text">{{ _.get(props.row, col.field) }}</span>
              </template>
              <template v-else-if="col.field === 'Member Status'">
                <q-chip class="font-12"
                        :class="props.row['Member Status'] === 'Active' ? 'positive' : 'negative'">
                  {{ props.row['Member Status'] }}
                </q-chip>
              </template>
              <template v-else-if="col.field === 'Actions'">
                <q-btn-dropdown class="btn-sm"
                                no-caps
                                color="grey-2"
                                text-color="dark"
                                :label="col.label">
                  <q-list link>
                    <q-item v-close-overlay
                            @click.native="viewConsumers(props.row)">
                      <q-item-main>View</q-item-main>
                    </q-item>
                  </q-list>
                </q-btn-dropdown>
              </template>
              <template v-else>{{ _.get(props.row, col.field) }}</template>
            </div>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'spendr-consumers',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-consumers')
  ],
  components: {
  },
  data () {
    const columns = [
      'User ID', 'First Name', 'Last Name', 'Email',
      'Phone', 'Spend', 'Member Status', 'Actions'
    ]
    return {
      title: 'Consumers',
      requestUrl: `/admin/spendr/merchant/consumers/list`,
      downloadUrl: `/admin/spendr/merchant/consumers/export`,
      columns: generateColumns(columns),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        },
        {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        },
        {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        },
        {
          value: 'filter[u.email=]',
          label: 'Email'
        }
      ],
      keyword: '',
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true
    }
  },
  methods: {
    viewConsumers (row) {
      window.open(`/admin#/h/spendr/consumers/${row['User ID']}`, '_blank')
    }
  }
}
</script>
