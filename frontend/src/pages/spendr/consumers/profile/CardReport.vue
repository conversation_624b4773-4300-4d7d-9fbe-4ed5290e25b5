<template>
  <q-card class="wide-card mb-30">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="inner-table m--15"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">
            <span class="va-m">{{ title }}</span>
          </div>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <div notranslate="">
              <template v-if="col.field === 'Type'">
                <div class="font-12 uppercase">
                  {{ props.row['Type'] }}
                </div>
              </template>
              <template v-if="(isMasterAdmin() || agentAdmin || spendrCompliance) && col.field === 'Actions'">
                <q-btn-dropdown class="btn-sm"
                                no-caps
                                color="grey-2"
                                text-color="dark"
                                :label="col.label">
                  <q-item v-close-overlay
                          class="cursor-pointer"
                          @click.native="add(props.row)">
                    <q-item-main>Add to blacklist</q-item-main>
                  </q-item>
                </q-btn-dropdown>
              </template>
              <template v-else>{{ _.get(props.row, col.field) }}</template>
            </div>
          </q-td>
        </q-tr>
        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </q-card-main>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-card>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns, notify, request } from '../../../../common'
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'

export default {
  name: 'spendr-consumer-profile-card-report',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin
  ],
  props: {
    uid: {
      type: String
    }
  },
  data () {
    const columns = [
      'Card ID', 'Date & Time', 'Bank Name', 'Last 4 Digits', 'Type', 'Actions'
    ]
    return {
      title: 'Card Report',
      columns: generateColumns(columns),
      requestUrl: `/admin/spendr/member-cards-report/list`,
      filterOptions: [
      ],
      autoLoad: true
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        memberID: this.uid
      }
    },
    async add (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure to add the bank card to blacklist?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/spendr/bank-card-blacklist/save`, 'post', { 'cardId': row['Card ID'] })
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp)
          this.reload()
        }
      })
    }
  }
}
</script>
