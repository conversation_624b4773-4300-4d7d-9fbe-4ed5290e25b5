<template>
  <q-card class="wide-card mb-30">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="inner-table m--15"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">
            <span class="va-m">{{ title }}</span>
          </div>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <div notranslate="">
              <template v-if="col.field === 'Type'">
                <div class="font-12 uppercase">
                  {{ props.row['Type'] }}
                </div>
              </template>
              <template v-else>{{ _.get(props.row, col.field) }}</template>
            </div>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </q-card-main>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-card>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns } from '../../../../common'
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'

export default {
  name: 'spendr-consumer-profile-basic-balance-history',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin
  ],
  props: {
    uid: {
      type: String
    }
  },
  data () {
    const columns = [
      'History ID', 'Date & Time', 'Type', 'Full Name', 'Account ID', 'Previous Balance',
      'Current Balance', 'Comment'
    ]
    return {
      title: 'Balance History Report',
      columns: generateColumns(columns),
      requestUrl: `/admin/spendr/balance-history/list`,
      downloadUrl: `/admin/spendr/balance-history/export`,
      filterOptions: [
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        'filter[u.id=]': this.uid
      }
    },
    statusClass (status) {
      return {
        'pending': 'blue',
        'initiated': 'dark',
        'loaded': 'positive',
        'unloaded': 'positive',
        'cancelled': 'orange',
        'error': 'negative',
        'expired': 'pansy'
      }[status] || status
    }
  }
}
</script>
<style lang="scss">
.load-status {
  text-transform: capitalize;
}
</style>
