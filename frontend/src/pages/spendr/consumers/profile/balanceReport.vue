<template>
  <q-card class="wide-card mb-30">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="inner-table m--15"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">
            <span class="va-m">{{ title }}</span>
            <!-- <q-icon name="mdi-refresh"
                    color="positive"
                    @click.native="forceReload"
                    class="ml-5 pointer">
              <q-tooltip>Refresh</q-tooltip>
            </q-icon> -->
          </div>
        </template>
        <template slot="top-right">
          <!-- <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn> -->
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Account ID'">
              <span class="click-text">{{ _.get(props.row, col.field) }}</span>
            </template>
<!--            <template v-else-if="col.field === 'Amount'">-->
<!--              <span v-if="props.row['Amount Value']" class="text-red"></span>-->
<!--            </template>-->
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </q-card-main>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-card>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns } from '../../../../common'
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'

export default {
  name: 'spendr-consumer-profile-balance-report',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin
  ],
  props: {
    uid: {
      type: String
    }
  },
  data () {
    const columns = [
      'Transaction', 'Date & Time', 'Transaction ID',
      'Account ID', 'Amount', 'Running Balance'
    ]
    return {
      title: 'Running Balance Report',
      columns: generateColumns(columns),
      requestUrl: `/admin/spendr/members/balance-report/list`,
      filterOptions: [],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        memberID: this.uid
      }
    }
  }
}
</script>
