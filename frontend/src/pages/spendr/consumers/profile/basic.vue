<template>
  <q-card id="spendr__consumers__profile_basic"
          class="high-card">
    <q-card-main>
      <div class="text-center mb-20">
        <div class="base-info">
          <div class="avatar mt-5">
            <img :src="'/static/img/avatar.png'"
                 alt="">
          </div>
          <div class="name-item font-24 bold mt-5">
            <p>{{ $c.fullName(entity) }}
              <q-btn icon="mdi-refresh"
                     outline
                     @click="$emit('reload')"
                     class="btn-mini ml-auto"></q-btn>
            </p>
            <p class="user-type">Consumer</p>
          </div>
        </div>
      </div>
      <div class="base-item-list">
        <div class="base-item"
             v-for="(r, i) in fields"
             :key="i">
          <div class="desc-item">
            <q-icon :name="iconName(r)"></q-icon>
            {{ r }}
          </div>
          <p>
            {{ entity[r] }}
          </p>
        </div>
      </div>
    </q-card-main>
  </q-card>
</template>

<script>
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'

export default {
  name: 'spendr-consumers-profile-basic',
  mixins: [
    SpendrPageMixin
  ],
  props: {
    entity: {
      type: Object
    }
  },
  data () {
    let fields = [
      'Puchases', 'Average Order', 'Total Spend', 'Current Balance',
      '# of days in negative balance', '# of returns', 'User ID', 'Email',
      'Phone', 'DOB', 'Home Address', 'Created Date'
    ]
    if (this.isMerchantAdmin()) {
      fields.splice(fields.indexOf('Current Balance'), 1)
      fields.splice(fields.indexOf('# of returns'), 1)
      fields.splice(fields.indexOf('# of days in negative balance'), 1)
      fields.splice(fields.indexOf('DOB'), 1)
      fields.splice(fields.indexOf('Home Address'), 1)
      fields.splice(fields.indexOf('Created Date'), 1)
    }
    return {
      fields: fields
    }
  },
  methods: {
    iconName (name) {
      return {
        'Email': 'mdi-email-outline',
        'Phone': 'mdi-cellphone-iphone',
        'Total Spend': 'mdi-cash-usd',
        'Average Order': 'mdi-chart-box-outline',
        'Puchases': 'mdi-shopify',
        'Current Balance': 'mdi-cash',
        'User ID': 'mdi-account',
        'Created Date': 'mdi-clock-outline',
        'DOB': 'mdi-clock-outline',
        'Home Address': 'mdi-home-map-marker',
        '# of days in negative balance': 'mdi-counter',
        '# of returns': 'mdi-counter'
      }[name] || 'mdi-help-circle-outline'
    },
    iconColor (row) {
      const group = row['Group']
      return {
        transfer: 'orange',
        payment: 'secondary',
        debit: 'negative',
        credit: 'positive'
      }[group] || 'warning'
    }
  }
}
</script>

<style lang="scss">
#spendr__consumers__profile_basic {
  .avatar {
    width: 100px;
    height: 100px;
    background-color: #eee;
    border-radius: 50px;
    margin-right: 15px;
    overflow: hidden;
  }
  .base-info {
    display: flex;
    align-items: center;
    .name-item {
      width: calc(100% - 120px);
      text-align: left;
      p {
        margin: 0;
      }
      .user-type {
        font-size: 14px;
        color: #8e8e93;
      }
    }
  }
  .base-item-list {
    max-height: 320px;
    overflow-y: scroll;
  }
  .base-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #cccccc;
    padding: 10px 5px;
    &:nth-last-child(1) {
      border-bottom: none;
    }
    p {
      margin: 0;
      font-size: 16px;
      color: #1c1c1e;
    }
    .desc-item {
      font-size: 14px;
      color: #8e8e93;
      .q-icon {
        font-size: 18px;
      }
    }
  }
  .fields-table {
    th {
      min-width: 60px !important;
    }
  }
}
</style>
