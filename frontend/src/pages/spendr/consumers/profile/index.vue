<template>
  <q-page id="spendr__consumers__profile_page">
    <div class="row gutter-sm mt-0">
      <div class="col-sm-12 col-md-6">
        <BasicCard :entity="entity"
                   @reload="reload()"></BasicCard>
      </div>
      <div class="col-sm-12 col-md-6">
        <NotesCard :uid="uid"
                   :url="url"
                   :from="'spendr'"
        ></NotesCard>
      </div>
      <div class="col-12" v-if="!merchantAdmin">
        <AppVersion :uid="uid"></AppVersion>
      </div>
      <div class="col-12" v-if="!merchantAdmin">
        <CardReport :uid="uid"
                    :url="url"></CardReport>
      </div>
      <div class="col-12" v-if="!merchantAdmin">
        <IdentityReport :uid="uid"
                    :url="url"></IdentityReport>
      </div>
      <div class="col-12" v-if="!merchantAdmin">
        <LoadReport :uid="uid"
                    :url="url"></LoadReport>
      </div>
      <div class="col-12" v-if="!merchantAdmin">
        <PromoCodeHistory :uid="uid"
                     :url="url"></PromoCodeHistory>
      </div>
      <div class="col-12" v-if="!merchantAdmin">
        <EarnHistory :uid="uid"
                    :url="url"></EarnHistory>
      </div>
      <div class="col-12">
        <CustomerReport :uid="uid"
                        :url="url"></CustomerReport>
      </div>
      <div class="col-12" v-if="!merchantAdmin">
        <BalanceHistory :uid="uid"
                        :url="url"></BalanceHistory>
      </div>
      <div class="col-12" v-if="!merchantAdmin">
        <BalanceReport :uid="uid"
                        :url="url"></BalanceReport>
      </div>
    </div>
  </q-page>
</template>

<script>
import { EventHandlerMixin, request } from '../../../../common'
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'
import NotesCard from '../../../faas/components/card/profile/notes'
import BasicCard from './basic'
import CustomerReport from './report'
import PromoCodeHistory from './PromoCodeHistory'
import LoadReport from './loadHistory'
import EarnHistory from './EarnHistory'
import BalanceReport from './balanceReport'
import BalanceHistory from './balanceHistory'
import CardReport from './CardReport'
import IdentityReport from './IdentityReport'
import AppVersion from './AppVersion'

export default {
  name: 'spendr-consumers-profile',
  mixins: [
    SpendrPageMixin,
    EventHandlerMixin('reload-spendr-consumers-profile')
  ],
  components: {
    BasicCard,
    NotesCard,
    CustomerReport,
    LoadReport,
    PromoCodeHistory,
    EarnHistory,
    BalanceReport,
    BalanceHistory,
    CardReport,
    IdentityReport,
    AppVersion
  },
  data () {
    return {
      title: 'Customer Profile',
      entity: {},
      url: 'spendr/merchant/consumer',
      balances: []
    }
  },
  computed: {
    uid () {
      return this.$route.params.id
    }
  },
  watch: {
    uid () {
      this.reload()
    }
  },
  methods: {
    async reload () {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/merchant/consumer/${this.uid}/profile`, 'get')
      this.$q.loading.hide()
      if (resp.success) {
        this.entity = resp.data
        this.balances = this.entity.balances
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
 .bank_row {
   display: flex;
   .bank_item {
     flex: 1;
     padding-top: 10px;
     padding-bottom: 10px;
   }
 }
</style>
