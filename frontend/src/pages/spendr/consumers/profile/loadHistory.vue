<template>
  <q-card class="wide-card mb-30">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="inner-table sticky-table m--15"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">
            <span class="va-m">{{ title }}</span>
            <!-- <q-icon name="mdi-refresh"
                    color="positive"
                    @click.native="forceReload"
                    class="ml-5 pointer">
              <q-tooltip>Refresh</q-tooltip>
            </q-icon> -->
          </div>
        </template>
        <template slot="top-right">
          <!-- <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn> -->
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Amount'">
              <span class="click-text">{{ _.get(props.row, col.field) | moneyFormat }}</span>
            </template>
            <template v-else-if="col.field === 'Status'">
              <q-chip class="font-12 load-status"
                      :class="statusClass(props.row['Status'].toLowerCase())">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="
            (isMasterAdmin() || agentAdmin || spendrCompliance) &&
            col.field === 'Actions' &&
            props.row['Allowed Cancel']">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-item v-close-overlay
                        class="cursor-pointer"
                        @click.native="manualCancel(props.row)">
                  <q-item-main>Cancel</q-item-main>
                </q-item>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </q-card-main>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <CancelLoad></CancelLoad>
  </q-card>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../../common'
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import CancelLoad from '../../merchants/loads/cancel_load'

export default {
  name: 'spendr-consumer-profile-basic-load-history',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-consumers-profile-loads')
  ],
  components: {
    CancelLoad
  },
  props: {
    uid: {
      type: String
    }
  },
  data () {
    const columns = [
      'ID', 'Date & Time', 'Amount'
    ]
    if (this.isMasterAdmin() || this.isSpendrAdmin()) {
      columns.push('Bank Fee')
      columns.push('Tern Fee')
    }
    for (const col of [
      'Loaded', 'Is Promotion', 'Custom Message', 'Loaded By', 'Error Reason', 'Note', 'Type', 'Load Type', 'Status'
    ]) {
      columns.push(col)
    }
    if (this.isMasterAdmin() || this.isAgentAdmin() || this.isSpendrCompliance()) {
      columns.push('Actions')
    }
    return {
      title: 'Load Report',
      columns: generateColumns(columns),
      requestUrl: `/admin/spendr/merchant/loads/list`,
      downloadUrl: `/admin/spendr/merchant/loads/export`,
      filterOptions: [
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 4
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        'filter[u.id=]': this.uid
      }
    },
    statusClass (status) {
      return {
        'pending': 'blue',
        'initiated': 'dark',
        'loaded': 'positive',
        'unloaded': 'positive',
        'cancelled': 'orange',
        'error': 'negative',
        'expired': 'pansy'
      }[status] || status
    },
    manualCancel (row) {
      this.$root.$emit('show-spendr-cancel-load-dialog', row)
    }
  }
}
</script>
<style lang="scss">
.load-status {
  text-transform: capitalize;
}
</style>
