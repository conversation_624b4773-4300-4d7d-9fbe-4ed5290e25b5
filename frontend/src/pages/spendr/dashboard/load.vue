<template>
  <q-dialog class="spendr-load-dialog"
            v-model="visible"
            prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">
        Load/Unload
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-radio v-model="entity['type']"
                   color="blue"
                   :error="$v.entity['type'].$error"
                   @change="$v.entity['type'].$touch"
                   val="Load"
                   label="Load"></q-radio>
          <q-radio v-model="entity['type']"
                   color="blue"
                   class="ml-15"
                   :error="$v.entity['type'].$error"
                   @change="$v.entity['type'].$touch"
                   val="Unload"
                   label="Unload"></q-radio>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Amount"
                   autocomplete="no"
                   type="number"
                   :error="$v.entity['amount'].$error"
                   @input="$v.entity['amount'].$touch"
                   v-model="entity['amount']"></q-input>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Continue"
               no-caps
               color="primary"
               class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, notifyForm, request } from '../../../common'
import { required } from 'vuelidate/lib/validators'

export default {
  name: 'spendr-load-dialog',
  mixins: [
    Singleton
  ],
  props: {
    partnerBoundCard: {
      type: Object,
      required: false
    }
  },
  data () {
    return {
      defaultEntity: {
        amount: null,
        type: null
      },
      partner: null
    }
  },
  validations: {
    entity: {
      amount: {
        required
      },
      type: {
        required
      }
    }
  },
  methods: {
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/dashboard/partner-load-unload`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this._hide()
        this.$root.$emit('reload-spendr-dashboard')
      }
    }
  }
}
</script>
<style lang="scss">
  .spendr-load-dialog {
    .modal-content {
      width: 500px;
    }
    .modal-scroll {
      max-height: none;
    }
  }
</style>
