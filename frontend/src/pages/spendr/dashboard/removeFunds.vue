<template>
  <q-dialog
    class="spendr-remove-funds-dialog"
    v-model="visible"
    prevent-close
  >
    <template slot="title">
      <div class="mb-5">
        <div class="avatar radius-box">
          <q-icon name="mdi-arrow-up-circle-outline"></q-icon>
        </div>
      </div>
      <div class="font-16 mb-2" v-if="step === 'response'">Your Funds Are On The Way</div>
      <div class="font-16 mb-2" v-else>Remove Funds</div>
      <div class="font-12 normal text-dark">
        <span v-if="step !== 'response'">How much do you wish to send to your operating account?</span>
        <br v-if="step !== 'response'">
        <span v-if="
        step === 'input' && (
          ($store.state.User.isLocationHasDummyMerchant && merchantBalanceAdmin) ||
          !$store.state.User.isLocationHasDummyMerchant
        )
        ">
          Total Merchant Balance: <span class="font-14 bold text-black">{{ totalMerchantBalance | moneyFormat }}</span>
        </span>
        <br v-if="totalLocationBalance && step === 'input'"/>
        <span v-if="step === 'input' && totalLocationBalance && needSelectLocation">
          Total Location Balance: <span class="font-14 bold text-black">{{ totalLocationBalance | moneyFormat }}</span>
        </span>
        <span v-if="
        (step === 'confirm' || step === 'response') && (
          ($store.state.User.isLocationHasDummyMerchant && merchantBalanceAdmin) ||
          !$store.state.User.isLocationHasDummyMerchant
        )
        ">
          New Merchant Balance: <span class="font-14 bold text-black">{{ newMerchantBalance | moneyFormat }}</span>
        </span>
        <br v-if="newLocationBalance"/>
        <span v-if="(step === 'confirm' || step === 'response') && newLocationBalance && needSelectLocation">
          New location Balance: <span class="font-14 bold text-black">{{ newLocationBalance | moneyFormat }}</span>
        </span>
      </div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12" v-if="needSelectLocation && step === 'input'">
          <q-select
            float-label="Location"
            v-model="entity['locationId']"
            :options="locationList"
            filter
            autofocus-filter
            autocomplete="no"
            @input="onLocationChange"
          />
        </div>
        <div class="col-sm-12" v-if="step === 'input'">
          <q-input float-label="Amount" autocomplete="no"
                   :error="$v.entity['Amount'].$error"
                   @input="$v.entity['Amount'].$touch"
                   v-model="entity['Amount']"></q-input>
        </div>
        <div class="col-sm-12" v-if="step === 'input'">
          <q-select float-label="Bank Account"
                    :options="bankOptions"
                    :error="$v.entity['bankId'].$error"
                    @input="$v.entity['bankId'].$touch"
                    v-model="entity['bankId']"></q-select>
        </div>
      </div>
      <div class="row" v-if="step === 'confirm' || step === 'response'">
        <div class="new-balance">
          <div class="dollar">$</div>
          <div class="value">{{ newBalanceWithoutDollar }}</div>
        </div>
        <table class="common-fields-table">
          <tr v-if="step === 'response'">
            <th>
              Payout Status
            </th>
            <td>
              <q-chip class="font-12 status" :class="styleClass(unloadStatus)">{{ unloadStatus }}</q-chip>
            </td>
          </tr>
          <tr>
            <th>
              Arrival Date
            </th>
            <td>
              2-3 Business Days
            </td>
          </tr>
          <tr>
            <th>
              Payment Total
            </th>
            <td>
              {{entity['Amount'] * 100 | moneyFormat}}
            </td>
          </tr>
          <tr v-if="currentLocationName">
            <th>
              Location
            </th>
            <td>
              {{ currentLocationName }}
            </td>
          </tr>
          <tr>
            <th>
              Linked Bank Account
            </th>
            <td>
              {{ bankInfo['bankName'] }} {{ bankInfo['accountNum'] }}
            </td>
          </tr>
        </table>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100 mb-10" v-if="step === 'input' && !merchantManagerAdmin && !merchantAccountantAdmin">
        <q-btn label="Add Bank Account"
               no-caps
               color="primary"
               class="main"
               @click="addBankAccount" />
      </div>
      <div class="row wp-100" v-if="step === 'input'">
        <q-btn label="Continue"
               no-caps
               color="primary"
               class="main"
               @click="confirm" />
      </div>
      <div class="row wp-100 mb-10" v-if="step === 'confirm'">
        <q-btn label="Remove Funds"
               no-caps
               color="primary"
               class="main"
               @click="save" />
      </div>
      <div class="row wp-100" v-if="step === 'confirm'">
        <q-btn label="Cancel"
               no-caps
               color="grey"
               class="main"
               @click="_hide" />
      </div>
      <div class="row wp-100" v-if="step === 'response'">
        <q-btn label="Close"
               no-caps
               color="grey"
               class="main"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import Singleton from '../../../mixins/Singleton'
import { moneyFormat, request, notifyResponse, notifyForm, notify } from '../../../common'
import { minValue, required } from 'vuelidate/lib/validators'
import _ from 'lodash'

export default {
  name: 'spendr-remove-funds-dialog',
  mixins: [
    Singleton,
    SpendrPageMixin
  ],
  data () {
    return {
      defaultEntity: {
        locationId: null,
        Amount: null,
        bankId: null
      },
      totalMerchantBalance: 0,
      bankList: [],
      bankOptions: [],
      step: 'input', // input, confirm, response
      bankInfo: [],
      unloadStatus: null,
      message: null,
      locationList: [],
      totalLocationBalance: null,
      needSelectLocation: false,
      currentLocationName: null
    }
  },
  computed: {
    newMerchantBalance () {
      return this.totalMerchantBalance - (this.entity['Amount'] * 100)
    },
    newLocationBalance () {
      return this.totalLocationBalance - (this.entity['Amount'] * 100)
    },
    newBalanceWithoutDollar () {
      let balance = moneyFormat(this.entity['Amount'] * 100)
      balance = balance.substr(1)
      return balance
    }
  },
  validations: {
    entity: {
      Amount: {
        required,
        minValue: minValue(0.01)
      },
      bankId: {
        required
      }
    }
  },
  methods: {
    async show () {
      this.step = 'input'
      this.entity['Amount'] = null
      this.totalMerchantBalance = 0
      this.totalLocationBalance = null
      this.needSelectLocation = false
      this.currentLocationName = null
      this.locationList = []

      this.$q.loading.show()
      await this.getActiveCards()
      const resp = await request(`/admin/spendr/merchant/withdraw/prepare`)
      this.$q.loading.hide()
      if (resp.success) {
        if (resp.data && resp.data.balance <= 0 && this.isMerchantBalanceAdmin()) {
          let balance = moneyFormat(resp.data.balance)
          notifyResponse('The current balance is: ' + balance + ', cannot be withdrawn.', () => {
            this._hide()
          })
        } else {
          this.totalMerchantBalance = resp.data.balance
          this.locationList = resp.data.locations
          this.needSelectLocation = resp.data.needSelectLocation
        }
      }
    },
    async getActiveCards () {
      const resp = await request(`/admin/spendr/merchant/active-cards`, 'get', {
        'merchantId': this.$store.state.User.merchantId
      })
      if (resp.success) {
        this.bankOptions = []
        this.bankList = resp.data
        resp.data.forEach(element => {
          this.bankOptions.push({
            label: element.bankName + ': ' + element.accountNum + (element.location ? ` (${element.location.name})` : ''),
            value: element.id
          })
        })
      }
    },
    addBankAccount () {
      window.open(`/admin#/h/spendr/merchant/${this.$store.state.User.merchantId}/onboard?step=bank`, '_blank')
      this._hide()
    },
    async confirm () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }

      if (this.needSelectLocation) {
        if (!this.entity.locationId) {
          return notify('Please select a location.', 'negative')
        }

        if (this.totalLocationBalance <= 0) {
          return notify('The location balance is insufficient and cannot be withdrawn', 'negative')
        }

        if (this.entity.Amount > (this.totalLocationBalance / 100)) {
          return notify('The amount cannot be greater than the current balance of location.', 'negative')
        }
      }

      this.bankInfo = _.find(this.bankList, { id: this.entity.bankId })
      this.step = 'confirm'
      // this.$q.loading.show()
      // const resp = await request(`/spendr/merchant/${this.$store.state.User.merchantId}/onboard/getBankinfo`)
      // this.$q.loading.hide()
      // if (resp.success) {
      //   if (resp.data.accountNum && resp.data.routing) {
      //     this.bankInfo = resp.data
      //     this.step = 'confirm'
      //   } else {
      //     notifyResponse('Please add a bank account first.')
      //   }
      // }
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/merchant/withdraw`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.step = 'response'
        this.unloadStatus = resp.data.status
        this.message = resp.message
        this.$root.$emit('reload-spendr-dashboard')
      }
    },
    styleClass (status) {
      const cls = []
      cls.push({
        'pending': 'positive',
        'received': 'blue',
        'loaded': 'blue'
      }[status] || status)
      return cls
    },
    onLocationChange (newValue) {
      this.locationList.map((v) => {
        if (v.value === newValue) {
          this.totalLocationBalance = v.balance
          this.currentLocationName = v.label
        }
      })
      this.bankList.map((i) => {
        if (i.bindLocationIds) {
          i.bindLocationIds.map((id) => {
            if (id === this.entity.locationId) {
              this.entity.bankId = i.id
            }
          })
        }
      })
      if (this.totalLocationBalance <= 0) {
        notify('The location balance is insufficient and cannot be withdrawn', 'negative')
      }
    }
  }
}
</script>

<style lang="scss">
.spendr-remove-funds-dialog {
  .modal-content {
    width: 500px;
  }

  .modal-scroll {
    max-height: none;
  }

  .avatar .q-icon {
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
  }

  .common-fields-table {
    width: 100%;
    th {
      padding-left: 20px !important;
    }
    td {
      padding-right: 20px !important;
    }
  }

  .new-balance {
    color: var(--q-color-primary);
    font-weight: bold;
    display: flex;
    justify-content: center;
    margin: 10px auto;
    .dollar {
      font-size: 20px;
    }
    .value {
      font-size: 80px;
      letter-spacing: -5px;
      line-height: 77px;
    }
  }

  .q-chip {
    border-radius: 20px;
    &.status {
      text-transform: capitalize;
    }
  }
}
</style>
