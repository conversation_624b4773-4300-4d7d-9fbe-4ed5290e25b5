<template>
  <q-dialog class="spendr-employee-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="avatar mt-5">
        <img :src="'/static/img/avatar.png'"
             alt="">
      </div>
      <div class="font-16 mb-2">{{ !edit ? 'Create New Employee' : 'Edit Employee' }}</div>
      <!-- <div v-if="edit"
           class="font-16 mb-2">{{ $c.fullName(entity) }}</div> -->
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-input float-label="First Name"
                   autocomplete="no"
                   :error="$v.entity['First Name'].$error"
                   @input="$v.entity['First Name'].$touch"
                   v-model="entity['First Name']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Last Name"
                   autocomplete="no"
                   :error="$v.entity['Last Name'].$error"
                   @input="$v.entity['Last Name'].$touch"
                   v-model="entity['Last Name']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Email"
                   autocomplete="no"
                   :error="$v.entity['Email'].$error"
                   @input="$v.entity['Email'].$touch"
                   v-model="entity['Email']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Phone"
                   autocomplete="no"
                   :error="$v.entity['Phone'].$error"
                   @input="$v.entity['Phone'].$touch"
                   v-model="entity['Phone']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Pin (Must be 4 digits)"
                   autocomplete="no"
                   :error="$v.entity['Pin'].$error"
                   @input="$v.entity['Pin'].$touch"
                   v-model="entity['Pin']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-select float-label="Location"
                    autocomplete="no"
                    :options="locationList"
                    filter
                    chips
                    autofocus-filter
                    multiple
                    v-model="entity['LocationIds']"></q-select>
          <!-- <q-select v-if="entity['Employee Type'] !== 'Merchant Manager'"
                    float-label="Location"
                    autocomplete="no"
                    :options="locationList"
                    filter
                    autofocus-filter
                    v-model="entity['LocationId']"></q-select> -->
        </div>
        <div class="pt-20"
             :class="edit ? 'col-sm-12 text-left' : 'col-sm-12'">
          <div class="bold">Role</div>
          <div class="mt-8">
            <q-radio v-model="entity['Employee Type']"
                     color="blue"
                     :error="$v.entity['Employee Type'].$error"
                     @change="$v.entity['Employee Type'].$touch"
                     val="Merchant Manager"
                     label="Manager"></q-radio>
            <q-radio v-model="entity['Employee Type']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Employee Type'].$error"
                     @change="$v.entity['Employee Type'].$touch"
                     val="Assistant Manager"
                     label="Assistant Manager"></q-radio>
            <q-radio v-model="entity['Employee Type']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Employee Type'].$error"
                     @change="$v.entity['Employee Type'].$touch"
                     val="Clerks"
                     label="Clerk"></q-radio>
          </div>
        </div>
        <div class="pt-20"
             :class="edit ? 'col-sm-12 text-left' : 'col-sm-12'">
          <div class="bold">Status</div>
          <div class="mt-8">
            <q-radio v-model="entity['Status']"
                     color="blue"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Active"
                     label="Active"></q-radio>
            <q-radio v-model="entity['Status']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Inactive"
                     label="Inactive"></q-radio>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn :label="'Save'"
               no-caps
               color="positive"
               class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, notifyForm, commonUserEntityValidator, request } from '../../../common'
import MexStateListMixin from '../../../mixins/mex/MexStateListMixin'
import _ from 'lodash'
// import { required, email, helpers } from 'vuelidate/lib/validators'
// const pin = helpers.regex('Must be 4 digits', /^[0-9]{4}$/)
export default {
  name: 'spendr-employee-detail-dialog',
  mixins: [
    Singleton,
    MexStateListMixin
  ],
  data () {
    return {
      defaultEntity: {
        'User ID': 0,
        'Employee Type': 'Merchant Manager',
        'LocationIds': [],
        'Status': 'Active'
      },
      locationList: []
    }
  },
  computed: {
    edit () {
      return this.entity['User ID']
    },
    user () {
      return this.$store.state.User
    }
  },
  validations: commonUserEntityValidator([
    'First Name', 'Last Name', 'Phone', 'Email', 'Employee Type', 'Status', 'Pin'
  ]),
  methods: {
    async show () {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/merchant/employee/${this.entity['User ID']}/detail`)
      this.$q.loading.hide()
      if (resp.success) {
        this.entity = _.assignIn({}, this.entity, resp.data.entity)
        this.locationList = resp.data.locations || []
      }
      // this.$q.loading.hide()
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        console.log(this.$v)
        return notifyForm()
      }
      if (!this.entity.LocationIds.length) {
        notify('Please select location for employee', 'negative')
        return
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/merchant/employees/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.$root.$emit('reload-spendr-employees')
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.spendr-employee-detail-dialog {
  .modal-content {
    width: 500px;
  }
  .avatar {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    overflow: hidden;
    margin: 0 auto;
  }
  .modal-scroll {
    max-height: none;
  }
  .q-chip {
    border-radius: 2rem;
  }
}
</style>
