<template>
  <q-page id="spendr-group-api-config">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <q-input v-model="maskedKey"
               class="mv-40"
               readonly
               :after="[{icon: 'mdi-clipboard-text-multiple-outline', handler: copyKey}]"
               float-label="Access Key"></q-input>
      <q-input v-model="maskedSecret"
               class="mv-40"
               readonly
               :after="[{icon: 'mdi-clipboard-text-multiple-outline', handler: copySecret}]"
               float-label="Secret"></q-input>
      <div class="mt-20 mb-30 text-faded" v-if="entity.expiringKey">
        The previous access key <b>{{ maskContent(entity.expiringKey) }}</b> will be revoked after <b>{{ entity.expiringTime | date('L LT') }}</b>.
        <a href="javascript:" class="text-orange" @click="revokeExpiring">Revoke now</a>
      </div>
      <div>
        <q-btn :label="submitLabel"
               no-caps
               class="mr-10"
               color="primary"
               @click="submit"
               icon="mdi-auto-fix"></q-btn>
        <q-btn label="Revoke and disable"
               no-caps
               v-if="entity.key"
               @click="disable"
               icon="mdi-close"
               color="orange"></q-btn>
      </div>

      <hr class="light mb-30" />

      <q-field label="Webhook"
               label-width="12"
               class="mb-20"
               helper="The URL in your system to receive transaction status update. Starts with 'https://'.">
        <q-input v-model="entity.webhook" placeholder="https://"></q-input>
      </q-field>

      <q-field label="IP Whitelist"
               label-width="12"
               class="mb-20"
               helper="Add one IP (v4 or v6) in each line. Keep empty to enable all IPs.">
        <q-input type="textarea"
                 rows="3"
                 v-model="entity.ips"></q-input>
      </q-field>
      <div>
        <q-btn label="Save"
               no-caps
               class="mr-10"
               color="primary"
               @click="saveLimit"
               icon="mdi-content-save"></q-btn>
      </div>

      <q-inner-loading :visible="loading"></q-inner-loading>
    </div>
  </q-page>
</template>

<script>
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'
import { copyToClipboard, notify, request } from '../../../../common'

export default {
  name: 'spendr-group-api-config',
  mixins: [
    SpendrPageMixin
  ],
  data () {
    return {
      title: 'API Config',
      entity: {
        key: '',
        secret: '',
        webhook: '',
        ips: '',
        expiringKey: '',
        expiringTime: null
      }
    }
  },
  computed: {
    submitLabel () {
      if (this.entity.key) {
        return 'Revoke and generate a new one'
      }
      return 'Generate to enable API access'
    },
    maskedKey () {
      return this.maskContent(this.entity.key)
    },
    maskedSecret () {
      return this.maskContent(this.entity.secret, 0)
    }
  },
  methods: {
    async reload () {
      this.loading = true
      const resp = await request('/admin/spendr/group/api/config', 'get')
      this.loading = false
      if (resp.success) {
        this.entity = resp.data
      }
    },
    submit () {
      const message = this.entity.key ? `Are you sure that you want to generate a new access key/secret pair? The previous one will still be valid for 24 hours so you have enough time to cut over.` : `Are you sure that you want to enable the API access?`
      this.$q.dialog({
        title: 'Confirm',
        message,
        color: 'positive',
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request('/admin/spendr/group/api/config', 'post')
        this.loading = false
        if (resp.success) {
          this.entity = resp.data
        }
      }).catch(() => {})
    },
    async disable () {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to disable the API access and revoke all key/secret pairs immediately?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request('/admin/spendr/group/api/config', 'delete')
        this.loading = false
        if (resp.success) {
          this.entity = resp.data
        }
      }).catch(() => {})
    },
    revokeExpiring () {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to revoke the previous access key immediately?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request('/admin/spendr/group/api/config/expiring', 'delete')
        this.loading = false
        if (resp.success) {
          this.entity = resp.data
        }
      }).catch(() => {})
    },
    copyKey () {
      copyToClipboard(this.entity.key)
    },
    copySecret () {
      copyToClipboard(this.entity.secret)
    },
    maskContent (s, visible = 4) {
      if (!s) {
        return ''
      }
      const left = s.substring(0, visible)
      const right = s.substring(s.length - visible)
      const middle = '*********************************'
      return left + middle + right
    },
    async saveLimit () {
      this.loading = true
      const resp = await request('/admin/spendr/group/api/config/limit', 'post', {
        ip_whitelist: this.entity.ips,
        webhook: this.entity.webhook
      })
      this.loading = false
      if (resp.success) {
        notify()
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>
