<template>
  <q-page id="spendr-group-api-doc">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <iframe :src="url" v-if="url" frameborder="0"></iframe>
    </div>
  </q-page>
</template>

<script>
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'
import { request } from '../../../../common'

export default {
  name: 'spendr-group-api-doc',
  mixins: [
    SpendrPageMixin
  ],
  data () {
    return {
      title: 'API Doc',
      url: null
    }
  },
  methods: {
    async init () {
      this.$q.loading.show()
      const resp = await request('/admin/spendr/group/api/doc', 'get')
      this.$q.loading.hide()
      if (resp.success) {
        this.url = resp.data.url
      }
    }
  },
  mounted () {
    this.init()
  }
}
</script>

<style lang="scss">
  #spendr-group-api-doc {
    .page-content {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
    }

    iframe {
      background: #F5F5F5;
      width: 100%;
      flex-grow: 1;
    }
  }
</style>
