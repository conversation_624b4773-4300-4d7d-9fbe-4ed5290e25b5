<template>
  <q-page id="spendr_groups__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total Groups</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn v-if="!spendrEmployee"
                 icon="mdi-account-group-outline"
                 color="positive"
                 label="Create group"
                 @click="add"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn v-if="!bankAdmin && !spendrCustomerSupport"
                 icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <!-- template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Display Status'])">
                {{ props.row['Display Status'] }}
              </q-chip>
            </template -->
            <template v-if="col.field === 'Is Partner'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Is Partner'])">
                {{ props.row['Is Partner'] }}
              </q-chip>
            </template>
            <!-- <template v-else-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template> -->
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          v-if="!spendrAccountant && !spendrCustomerSupport"
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <!-- <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'active')"
                          v-if="!spendrEmployee && props.row['Status'] === 'Inactive'"
                  >
                    <q-item-main>Change to Active</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'inactive')"
                          v-if="!spendrEmployee && props.row['Status'] === 'Active'">
                    <q-item-main>Change to Inactive</q-item-main>
                  </q-item> -->
                  <q-item v-if="!spendrEmployee && !props.row['Last Login']"
                          v-close-overlay
                          @click.native="resendInvitation(props.row)">
                    <q-item-main>Resend Invitation Email</q-item-main>
                  </q-item>
                  <!-- q-item v-close-overlay
                          v-if="!spendrAccountant"
                          @click.native="viewOnboardApplication(props.row)">
                    <q-item-main>View Onboard Application</q-item-main>
                  </q-item -->
                  <q-item v-close-overlay
                          v-if="masterAdmin || agentAdmin || spendrCompliance || spendrCustomerSupport"
                          @click.native="$c.loginAs(props.row['Admin ID'])">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <DetailDialog></DetailDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notify
  // , toSelectOptions
} from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import DetailDialog from './detail'

export default {
  name: 'spendr-groups',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-groups')
  ],
  components: {
    DetailDialog
  },
  data () {
    const columns = [
      'Date & Time', 'ID', 'Name', 'Admin ID',
      // 'Locations',
      'Merchants',
      'Contact Name', 'Contact Email', 'Contact Phone',
      // 'Status',
      'Actions'
    ]
    return {
      title: 'Groups',
      requestUrl: `/admin/spendr/groups/list`,
      downloadUrl: `/admin/spendr/groups/export`,
      columns: generateColumns(columns),
      filterOptions: [
        {
          value: 'filter[g.id=]',
          label: 'ID'
        },
        {
          value: 'filter[g.name=]',
          label: 'Name'
        },
        {
          value: 'filter[u.email=]',
          label: 'Email'
        }
        // {
        //   value: 'filter[g.status=]',
        //   label: 'Status',
        //   options: toSelectOptions([
        //     'Active', 'Inactive'
        //   ])
        // }
      ],
      freezeColumn: -1,
      freezeColumnRight: 1,
      autoLoad: true,
      showEncrypt: false
    }
  },
  methods: {
    add () {
      this.$root.$emit('show-spendr-group-detail-dialog')
    },
    edit (row) {
      this.$root.$emit('show-spendr-group-detail-dialog', row)
    },
    async changeStatus (row, status) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure you want to change status? It will also change the status of the locations that belong to it.',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await this.c.request(`/admin/spendr/groups/${row['Group ID']}/toggle-status`, 'post', {
          Status: status
        })
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
        }
      }).catch(() => {})
    },
    async resendInvitation (row) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/spendr/groups/${row['Admin ID']}/resend-invitation`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
      }
    },
    viewOnboardApplication (row) {
      window.open(`/admin#/h/spendr/group/${row['Group ID']}/onboard`, '_blank')
    }
  }
}
</script>
