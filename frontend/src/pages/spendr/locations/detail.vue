<template>
  <q-dialog class="spendr-location-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <q-icon class="font-30"
              name="mdi-map-marker"></q-icon>
      <div class="font-16 mb-2">{{ edit ? 'Edit Location' : 'Create a Location' }}</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-input float-label="Name"
                   autocomplete="no"
                   :error="$v.entity['Name'].$error"
                   @input="$v.entity['Name'].$touch"
                   v-model="entity['Name']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Phone"
                   autocomplete="no"
                   :error="$v.entity['Phone'].$error"
                   @input="$v.entity['Phone'].$touch"
                   v-model="entity['Phone']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Address"
                   autocomplete="no"
                   :error="$v.entity['Address'].$error"
                   @input="$v.entity['Address'].$touch"
                   v-model="entity['Address']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-select float-label="Country"
                    autocomplete="no"
                    :options="countries"
                    filter
                    autofocus-filter
                    :error="$v.entity['CountryId'].$error"
                    @blur="$v.entity['CountryId'].$touch"
                    v-model="entity['CountryId']"></q-select>
        </div>
        <div class="col-sm-12">
          <q-select float-label="State / Province"
                    autocomplete="no"
                    :options="states"
                    filter
                    autofocus-filter
                    :error="$v.entity['StateId'].$error"
                    :before="stateBefore"
                    @blur="$v.entity['StateId'].$touch"
                    v-model="entity['StateId']"></q-select>
        </div>
        <div class="col-sm-12">
          <q-input float-label="City"
                   autocomplete="no"
                   :error="$v.entity['City'].$error"
                   @input="$v.entity['City'].$touch"
                   v-model="entity['City']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Zip"
                   autocomplete="no"
                   :error="$v.entity['Zip'].$error"
                   @input="$v.entity['Zip'].$touch"
                   v-model="entity['Zip']"></q-input>
        </div>
        <div class="col-sm-12" v-if="entity['Location ID']">
          <q-input float-label="Latitude"
                   autocomplete="no"
                   v-model="entity['Latitude']" readonly></q-input>
        </div>
        <div class="col-sm-12" v-if="entity['Location ID']">
          <q-input float-label="Longitude"
                   autocomplete="no"
                   v-model="entity['Longitude']" readonly></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Business Hours"
                   autocomplete="no"
                   v-model="entity['Business Hours']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Business Hours Saturday & Sunday"
                   autocomplete="no"
                   v-model="entity['Business Hours Saturday & Sunday']"></q-input>
        </div>
        <div class="col-sm-12 pt-20"
             v-show="edit">
          <div class="bold">Status</div>
          <div class="mt-8">
            <q-radio v-model="entity['Status']"
                     color="blue"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Active"
                     label="Active"></q-radio>
            <q-radio v-model="entity['Status']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Inactive"
                     label="Inactive"></q-radio>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn :label="'Save'"
               no-caps
               color="positive"
               class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, commonUserEntityValidator, request, notify } from '../../../common'
import MexStateListMixin from '../../../mixins/mex/MexStateListMixin'

export default {
  name: 'spendr-location-detail-dialog',
  mixins: [
    Singleton,
    MexStateListMixin
  ],
  data () {
    return {
      defaultEntity: {
        'Location ID': 0,
        'CountryId': null,
        'StateId': null,
        'Status': 'Active'
      }
    }
  },
  computed: {
    edit () {
      return this.entity['Location ID']
    },
    user () {
      return this.$store.state.User
    }
  },
  validations: commonUserEntityValidator([
    'Name', 'Address', 'City', 'Zip', 'StateId', 'CountryId', 'Phone', 'Status'
  ], false),
  methods: {
    async show () {
      if (!this.entity || !this.entity['Location ID']) {
      }
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/merchant/locations/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.$root.$emit('reload-spendr-locations')
        this._hide()
      }
      this.$q.loading.hide()
    }
  }
}
</script>

<style lang="scss">
.spendr-location-detail-dialog {
  .modal-content {
    width: 500px;
  }

  .modal-scroll {
    max-height: none;
  }
}
</style>
