<template>
  <q-dialog class="spendr-change-manual-card-status-dialog"
            v-model="visible"
            prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">
        Change Manual Card Status
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-radio v-model="Prefund"
                   color="blue"
                   val="Prefund"
                   label="Prefund"></q-radio>
          <q-radio v-model="Prefund"
                   color="blue"
                   class="ml-15"
                   val="Instant"
                   label="Instant($200)"></q-radio>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Continue"
               no-caps
               color="primary"
               class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { EventHandlerMixin, notify, request } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'

export default {
  name: 'spendr-change-manual-card-status-dialog',
  mixins: [
    Singleton,
    SpendrPageMixin,
    EventHandlerMixin('spendr-change-manual-card-status-dialog', 'show')
  ],
  data () {
    return {
      consumerId: null,
      Prefund: null
    }
  },
  methods: {
    async show () {
      this.consumerId = this.entity['User ID']
      this.Prefund = (this.entity['Manual Prefund'] ? 'Instant' : 'Prefund')
    },
    async save () {
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(
        `/admin/spendr/members/${this.consumerId}/manual-prefund`,
        'post',
        { Prefund: this.Prefund }
      )
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this._hide()
        this.$root.$emit('reload-spendr-members')
      }
    }
  }
}
</script>

<style lang="scss">
  .spendr-change-manual-card-status-dialog {
    .modal-content {
      width: 500px;
    }
    .modal-scroll {
      max-height: none;
    }
  }
</style>
