<template>
  <q-dialog class="spendr-close-account-dialog"
            v-model="visible"
            prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">
        Handle the delete account apply
      </div>
      <div class="font-14 normal">
        This member has applied to delete account at {{ entity['Delete Apply At'] }} with the reason for {{ entity['Delete Reason'] }}.
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-radio v-model="type"
                   color="blue"
                   val="cancel"
                   label="Cancel"></q-radio>
          <q-radio v-model="type"
                   color="red"
                   class="ml-15"
                   val="delete"
                   label="Delete"></q-radio>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Confirm"
               no-caps
               color="primary"
               class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { EventHandlerMixin, notify, request } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'

export default {
  name: 'spendr-close-account-dialog',
  mixins: [
    Singleton,
    SpendrPageMixin,
    EventHandlerMixin('spendr-close-account-dialog', 'show')
  ],
  data () {
    return {
      consumerId: null,
      type: null
    }
  },
  methods: {
    async show () {
      this.consumerId = this.entity['User ID']
    },
    async save () {
      this.$q.loading.show({
        message: 'Submitting...'
      })
      const resp = await request(
        `/admin/spendr/members/${this.consumerId}/handle-delete`,
        'post',
        { type: this.type }
      )
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this._hide()
        this.$root.$emit('reload-spendr-members')
      }
    }
  }
}
</script>
<style lang="scss">
  .spendr-close-account-dialog {
    .modal-content {
      width: 500px;
    }
    .modal-scroll {
      max-height: none;
    }
  }
</style>
