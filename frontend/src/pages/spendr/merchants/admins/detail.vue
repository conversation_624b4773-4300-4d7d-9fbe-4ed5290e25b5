<template>
  <q-dialog
    class="spendr-merchant-admin-detail-dialog"
    v-model="visible"
    prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">{{ edit ? 'Edit Admin' : 'Create New Admin' }}</div>
      <div class="font-12 normal text-dark">Please fill in the information below about the admin.</div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-6">
          <q-input float-label="First Name" autocomplete="no"
                   :error="$v.entity['First Name'].$error"
                   @input="$v.entity['First Name'].$touch"
                   v-model="entity['First Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Last Name" autocomplete="no"
                   :error="$v.entity['Last Name'].$error"
                   @input="$v.entity['Last Name'].$touch"
                   v-model="entity['Last Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Email" autocomplete="no"
                   :error="$v.entity['Email'].$error"
                   @input="$v.entity['Email'].$touch"
                   v-model="entity['Email']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Phone Number" autocomplete="no"
                   :error="$v.entity['Phone'].$error"
                   @input="$v.entity['Phone'].$touch"
                   v-model="entity['Phone']"></q-input>
        </div>
        <div class="col-sm-12" v-if="!merchantAccountantAdmin && !merchantOtherAdmin">
          <q-select
            multiple
            chips
            float-label="Location"
            v-model="entity['LocationIds']"
            :options="locationList"
            filter
            autofocus-filter
          />
        </div>
        <div class="pt-20 col-sm-12" v-if="!entity['isMyself']">
          <div class="bold">Role</div>
          <div class="mt-8">
            <q-radio v-if="(merchantBalanceAdmin || merchantMasterAdmin) && !spendrCustomerSupportLogin"
                     v-model="entity['User Type']"
                     color="blue"
                     val="Master Admin"
                     label="Master Admin"
                     class="mr-15 mb-5">
              <q-tooltip>Full permissions to view/edit/export data and information, create and edit ALL admin access, and remove funds/change bank information</q-tooltip>
            </q-radio>
            <q-radio v-if="(merchantBalanceAdmin || merchantMasterAdmin || merchantOperatorAdmin) && !spendrCustomerSupportLogin"
                     v-model="entity['User Type']"
                     color="blue"
                     class="mr-15 mb-5"
                     val="Operator Admin"
                     label="Operator Admin">
              <q-tooltip>All permissions to view/edit/export data and information, create and edit LOWER admin access, and remove funds/change bank information</q-tooltip>
            </q-radio>
            <q-radio v-model="entity['User Type']"
                     color="blue"
                     class="mr-15 mb-5"
                     val="Manager Admin"
                     label="Manager Admin">
              <q-tooltip>All permissions to view/edit/export data and information, create and edit LOWER admin access, and remove funds</q-tooltip>
            </q-radio>
            <q-radio v-model="entity['User Type']"
                     color="blue"
                     class="mr-15 mb-5"
                     val="Accountant Admin"
                     label="Accountant Admin">
              <q-tooltip>View only with permissions to export data and remove funds</q-tooltip>
            </q-radio>
            <q-radio v-model="entity['User Type']"
                     color="blue"
                     val="Other Admin"
                     label="Other Admin">
              <q-tooltip>View only</q-tooltip>
            </q-radio>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Cancel" no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
        <q-btn :label="edit ? 'Save Changes' : 'Create Admin'"
               no-caps
               color="positive" class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notifyForm, notify, request, commonUserEntityValidator } from '../../../../common'
import _ from 'lodash'
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'

export default {
  name: 'spendr-merchantAdmin-detail-dialog',
  mixins: [
    Singleton,
    SpendrPageMixin
  ],
  data () {
    return {
      defaultEntity: {
        'User ID': 0,
        'LocationIds': []
      },
      locationList: []
    }
  },
  computed: {
    edit () {
      return this.entity['User ID']
    },
    user () {
      return this.$store.state.User
    }
  },
  validations: commonUserEntityValidator([
    'First Name', 'Last Name'
  ]),
  methods: {
    async show () {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/merchant/admins/${this.entity['User ID']}/details`, 'get')
      this.$q.loading.hide()
      if (resp.success) {
        this.entity = _.assignIn({}, this.entity, resp.data.entity)
        this.locationList = resp.data.locations || []
      }
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      if (!this.isMerchantAccountantAdmin() && !this.isMerchantOtherAdmin() && !this.entity.LocationIds.length) {
        notify('Please select at least one location for this admin.', 'negative')
        return
      }
      if (!this.entity.isMyself && !this.entity['User Type']) {
        notify('Please select a role for admin.', 'negative')
        return
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/merchant/admins/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.$root.$emit('reload-spendr-merchant-admins')
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.spendr-merchant-admin-detail-dialog {
  .modal-content {
    width: 580px;
  }

  .modal-scroll {
    max-height: none;
  }

  .q-chip {
    border-radius: 2rem;
  }
}
</style>
