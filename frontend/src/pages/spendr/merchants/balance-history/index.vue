<template>
  <q-page id="spendr_balance_history__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :iso-week="true"
                         :default="dateRange"
                         :ranges="datesWithDaily"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content top-statics-style">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="positive"></q-icon>
                <div class="column">
                  <div class="description">Total</div>
                  <div class="value">{{ quick.total || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id"
      >
        <template slot="top-left">
          <strong>{{ title }} Report</strong>
        </template>
        <template slot="top-right">
          <q-btn v-if="!spendrCustomerSupportLogin && !merchantOtherAdmin"
                 icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-select v-if="isMasterAdmin() || agentAdmin || spendrEmployee"
                    v-model="balanceAdminType"
                    :options="balanceAdminTypeOptions"
                    @input="reload"
                    class="balance-type"
          ></q-select>
<!--          <q-search icon="search"-->
<!--                    v-model="keyword"-->
<!--                    type="text"-->
<!--                    class="mr-10 w-200 dense"-->
<!--                    @keydown.13="delayedReload"-->
<!--                    @clear="delayedReload"-->
<!--                    clearable-->
<!--                    placeholder="Search...">-->
<!--          </q-search>-->
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)"
        >
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]"
          >
            <div notranslate="">
              <template v-if="col.field === 'Type'">
                <div class="font-12 uppercase">
                  {{ props.row['Type'] }}
                </div>
              </template>
              <template v-else>{{ _.get(props.row, col.field) }}</template>
            </div>
          </q-td>
        </q-tr>
        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../../common'
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'

export default {
  name: 'spendr-balance-history',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-balance-history')
  ],
  components: {
  },
  data () {
    const columns = [
      'History ID', 'Date & Time', 'Type', 'Full Name', 'Account ID', 'Previous Balance',
      'Current Balance', 'Comment'
    ]
    if (this.isMerchantAdmin() && !this.isMerchantBalanceAdmin()) {
      columns.splice(columns.indexOf('Previous Balance'), 1)
      columns.splice(columns.indexOf('Current Balance'), 1)
    }
    return {
      title: 'Balance History',
      requestUrl: `/admin/spendr/balance-history/list`,
      downloadUrl: `/admin/spendr/balance-history/export`,
      filtersUrl: `/admin/spendr/balance-history/filters`,
      columns: generateColumns(columns),
      filterOptions: [
        {
          value: 'filter[ucb.id=]',
          label: 'History ID'
        },
        {
          value: 'filter[u.id]',
          label: 'Account ID'
        },
        {
          value: 'filter[ucb.type=]',
          label: 'Type',
          options: [],
          source: 'types'
        }
      ],
      keyword: '',
      freezeColumn: -1,
      freezeColumnRight: 0,
      autoLoad: true,
      balanceAdminType: 'all',
      balanceAdminTypeOptions: [
        { label: 'All', value: 'all' },
        { label: 'Partner', value: 'spendr' },
        { label: 'Bank', value: 'bank' },
        { label: 'Tern', value: 'tern' }
      ]
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        balanceAdminType: this.balanceAdminType
      }
    },
    statusClass (status) {
      return {
        'Pending': 'dark',
        'Completed': 'positive',
        'Cancelled': 'orange',
        'Error': 'negative',
        'Expired': 'pansy'
      }[status] || status
    }
  }
}
</script>

<style lang="scss">
  #spendr_balance_history__index_page {
    .balance-type {
      min-width: 100px;
    }
  }
</style>
