<template>
  <q-dialog class="spendr-merchant-braze-pushed-dialog"
            v-model="visible"
            prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">
        Config braze pushed
      </div>
      <div class="font-14 normal">
        Unselected roles will not get braze email messages.
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-select float-label="Pushed Roles"
                    autocomplete="no"
                    :options="roles"
                    filter
                    autofocus-filter
                    multiple
                    v-model="entity['Subscribed']"></q-select>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Continue"
               no-caps
               color="primary"
               class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, notifyResponse, request } from '../../../common'

export default {
  name: 'spendr-merchant-braze-pushed-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      roles: [],
      defaultEntity: {
        'Merchant ID': null,
        'Subscribed': [],
        'Roles': []
      }
    }
  },
  methods: {
    async show (data) {
      this.entity = data
      this.roles = []
      for (const role of data['Roles']) {
        this.roles.push({ label: role, value: role })
      }
    },
    async save () {
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/merchants/config-braze-pushed`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this._hide()
      } else {
        notifyResponse(resp.message)
      }
    }
  }
}
</script>
<style lang="scss">
.spendr-merchant-braze-pushed-dialog {
  .modal-content {
    width: 500px;
  }
  .modal-scroll {
    max-height: none;
  }
}
</style>
