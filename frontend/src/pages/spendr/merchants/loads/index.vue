<template>
  <q-page id="spendr_loads__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :iso-week="true"
                         :default="dateRange"
                         :ranges="datesWithDaily"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total Loads</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6" v-if="!merchantAdmin && !bankAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalConsumerLoads || 0 }}</div>
                  <div class="description">Total Consumer Loads</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6" v-if="!merchantAdmin && !bankAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalConsumerLoadAmount | moneyFormat }}</div>
                  <div class="description">Total Consumer Load Amount</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6" v-if="!merchantAdmin && !bankAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalPromoLoads || 0 }}</div>
                  <div class="description">Total Promotion Loads</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6" v-if="!merchantAdmin && !bankAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalPromoLoadAmount | moneyFormat }}</div>
                  <div class="description">Total Promotion Load Amount</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6" v-if="!merchantAdmin && !bankAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.estimatedBankFees | moneyFormat }}</div>
                  <div class="description">Estimated Bank Fees</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="orange"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalUnloadAmount | moneyFormat }}</div>
                  <div class="description">Total Unload Amount</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }} Report</strong>
        </template>
        <template slot="top-right">
<!--          <q-btn color="blue"-->
<!--                 v-if="!$c.isLive()"-->
<!--                 label="Process Prefund Load Funds"-->
<!--                 @click="processPrefundLoadFunds"-->
<!--                 class="btn-sm mr-8"-->
<!--                 no-caps></q-btn>-->
          <q-btn v-if="!spendrCustomerSupportLogin && !merchantOtherAdmin"
                 icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-select v-if="isMasterAdmin() || agentAdmin || spendrEmployee"
                    v-model="loadType"
                    :options="loadTypeOptions"
                    @input="reload"
                    class="load-type"
          ></q-select>
          <!--          <q-search icon="search"-->
          <!--                    v-model="keyword"-->
          <!--                    type="text"-->
          <!--                    class="mr-10 w-200 dense"-->
          <!--                    @keydown.13="delayedReload"-->
          <!--                    @clear="delayedReload"-->
          <!--                    clearable-->
          <!--                    placeholder="Search...">-->
          <!--          </q-search>-->
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              class="receipt-item">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Amount'">
              <span v-if="(props.row['Amount'] >= 0) && (props.row['Type'] === 'Deposit')"
                    class="amount-item">+{{ _.get(props.row, col.field) | moneyFormat}}</span>
              <span v-else>{{ _.get(props.row, col.field) | moneyFormat}}</span>
            </template>
            <template v-else-if="col.field === 'Status'">
              <q-chip class="font-12 load-status"
                      :class="statusClass(props.row['Status'].toLowerCase())">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="(isMasterAdmin() || agentAdmin || spendrCompliance) &&
            col.field === 'Actions' &&
            props.row['Allowed Cancel']">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-item v-close-overlay
                        class="cursor-pointer"
                        @click.native="manualCancel(props.row)">
                  <q-item-main>Cancel</q-item-main>
                </q-item>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>
        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <CancelLoad></CancelLoad>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../../common'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'
import CancelLoad from './cancel_load'

export default {
  name: 'spendr-loads',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-loads')
  ],
  components: {
    CancelLoad
  },
  data () {
    const columns = [
      'ID', 'Date & Time', 'Full Name', 'Email', 'Member ID', 'Amount'
    ]
    if (this.isMasterAdmin() || this.isSpendrAdmin()) {
      columns.push('Estimated Bank Fee')
      columns.push('Estimated Tern Fee')
      columns.push('Spendr ACH Return Fee')
      columns.push('Estimated Bank ACH Return Fee')
      columns.push('Estimated Merchant Withdraw Fee')
      columns.push('Estimated Partner Withdraw Fee')
    }
    for (const col of [
      'Loaded', 'Is Promotion', 'Custom Message', 'Loaded By', 'Error Reason', 'Note', 'Type', 'Load Type', 'Status'
    ]) {
      columns.push(col)
    }
    if (this.isMasterAdmin() || this.isAgentAdmin() || this.isSpendrCompliance()) {
      columns.push('Actions')
    }
    return {
      title: 'Loads',
      requestUrl: `/admin/spendr/merchant/loads/list`,
      downloadUrl: `/admin/spendr/merchant/loads/export`,
      filtersUrl: `/admin/spendr/merchant/loads/filters`,
      columns: generateColumns(columns,
        [],
        {
          'Date & Time': 'ucl.createdAt',
          'Type': 'ucl.type',
          'Full Name': 'u.firstName',
          'Email': 'u.email',
          'ID': 'u.id',
          'Amount': 'ucl.initialAmount'
        }),
      filterOptions: [
        {
          value: 'filter[ucl.id=]',
          label: 'ID'
        }, {
          value: 'filter[ucl.loadStatus=]',
          label: 'Status',
          options: [],
          source: 'loadStatuses'
        }, {
          value: 'filter[u.id]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        }
      ],
      keyword: '',
      freezeColumn: -1,
      freezeColumnRight: 4,
      autoLoad: true,
      loadTypeOptions: [
        { label: 'All', value: 'all' },
        { label: 'Promotion', value: 'promotion' },
        { label: 'Normal', value: 'normal' }
      ],
      loadType: 'all'
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        'loadType': this.loadType
      }
    },
    statusClass (status) {
      return {
        'pending': 'blue',
        'initiated': 'dark',
        'loaded': 'positive',
        'unloaded': 'positive',
        'cancelled': 'orange',
        'error': 'negative',
        'expired': 'pansy'
      }[status] || status
    },
    manualCancel (row) {
      this.$root.$emit('show-spendr-cancel-load-dialog', row)
    }
    // async processPrefundLoadFunds () {
    //   this.$q.loading.show()
    //   const resp = await request(`/admin/spendr/merchant/loads/handle-prefund-funds`)
    //   if (resp.success) {
    //     notifySuccess()
    //   }
    //   this.$q.loading.hide()
    // }
  }
}
</script>

<style lang="scss">
#spendr_loads__index_page {
  .load-status {
    text-transform: capitalize;
  }
  .load-type {
    min-width: 100px;
  }
}
</style>
