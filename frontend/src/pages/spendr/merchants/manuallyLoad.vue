<template>
  <q-dialog class="spendr-merchant-manually-load-dialog"
            v-model="visible"
            prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">
        Manually Load
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-input float-label="Amount"
                   autocomplete="no"
                   type="number"
                   :error="$v.entity['Amount'].$error"
                   @input="$v.entity['Amount'].$touch"
                   v-model="entity['Amount']"></q-input>
        </div>
        <div class="col-sm-12">
            <q-input autocomplete="no" float-label="Custom Message"
                     type="text"
                     :error="$v.entity['customMessage'].$error"
                     @input="$v.entity['customMessage'].$touch"
                     v-model="entity['customMessage']"></q-input>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Continue"
               no-caps
               color="primary"
               class="main"
               @click="load" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, notifyForm, notifyResponse, request } from '../../../common'
import { required } from 'vuelidate/lib/validators'

export default {
  name: 'spendr-merchant-manually-load-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      merchant: null,
      defaultEntity: {
        'Merchant ID': null,
        Amount: null,
        customMessage: null
      },
      bankList: [],
      bankOptions: []
    }
  },
  validations: {
    entity: {
      Amount: {
        required
      },
      customMessage: {
        required
      }
    }
  },
  methods: {
    async show (merchant) {
      this.merchant = merchant
      this.entity['Merchant ID'] = merchant.id
    },
    async load () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Saving...'
      })
      const resp = await request(`/admin/spendr/merchants/${this.merchant.id}/manually-load`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this._hide()
      } else {
        notifyResponse(resp.message)
      }
    }
  }
}
</script>
<style lang="scss">
  .spendr-merchant-manually-load-dialog {
    .modal-content {
      width: 500px;
    }
    .modal-scroll {
      max-height: none;
    }
  }
</style>
