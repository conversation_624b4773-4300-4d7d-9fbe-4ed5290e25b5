<template>
  <q-card class="spendr-merchant-onboard-email q-card-lg shadow-lg">
    <q-card-title>
      <div class="font-18 mb-2">Email Verification</div>
      <div class="font-12 normal text-dark">An email verification link has been sent to <span class="text-primary">{{ merchant.email }}</span>.</div>
    </q-card-title>
    <q-card-main>
      <p>Click on the link to verify your email and login to continue with the next step in the registration process.</p>

      <q-btn label="Resend Verification Link"
             no-caps class="mv-20 wp-100"
             color="primary"
             @click="resend" />

      <q-btn label="Change Email"
             no-caps class="wp-100"
             color="primary" outline
             @click="change" />
    </q-card-main>
  </q-card>
</template>

<script>
import { notify, request } from '../../../../common'

export default {
  name: 'spendr-merchant-onboard-email',
  props: {
    merchant: {
      type: Object,
      required: true
    },
    profilePage: {
      type: Boolean,
      default: false
    },
    recaptchaKey: {
      type: String,
      default: ''
    }
  },
  methods: {
    resend () {
      if (this.profilePage) {
        this.resendAction(this)
      } else {
        const _this = this
        window.grecaptcha.ready(function () {
          window.grecaptcha.execute(
            this.recaptchaKey,
            { action: 'spendr_merchant_onboard_resend_email' }
          ).then(function (token) {
            _this.resendAction(_this, token)
          }).catch(function (e) {
            console.log('reCAPTCHA Error', 'Failed to load reCAPTCHA. Please refresh the page and try again.')
          })
        })
      }
    },
    async resendAction (_this, token) {
      let data = token ? { recaptcha: token } : null
      _this.$q.loading.show({
        message: 'Resending...'
      })
      const resp = await request(
        `/spendr/merchant/onboard/resend-invitation/${_this.merchant.id}`,
        'post',
        data
      )
      _this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
      }
    },
    change () {
      this.$q.dialog({
        title: 'Change Email',
        message: `Please enter the new email address:`,
        cancel: true,
        prompt: {
          model: this.merchant.email,
          type: 'text'
        }
      }).then(async email => {
        if (!email || !email.trim()) {
          notify('Operation cancelled.', 'negative')
          return
        }
        if (this.profilePage) {
          this.changeAction(this, email)
        } else {
          const _this = this
          window.grecaptcha.ready(function () {
            window.grecaptcha.execute(
              this.recaptchaKey,
              { action: 'spendr_merchant_onboard_change_email' }
            ).then(function (token) {
              _this.changeAction(_this, email, token)
            }).catch(function (e) {
              console.log('reCAPTCHA Error', 'Failed to load reCAPTCHA. Please refresh the page and try again.')
            })
          })
        }
      }).catch(() => {})
    },
    async changeAction (_this, email, token) {
      let data = { email }
      if (token) {
        data.recaptcha = token
      }
      _this.$q.loading.show()
      const resp = await request(
        `/spendr/merchant/onboard/change-email/${_this.merchant.id}`,
        'post',
        data
      )
      _this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
        _this.$emit('update', resp.data)
      }
    }
  }
}
</script>

<style lang="scss">
.spendr-merchant-onboard-email {
  max-width: 450px;
}
</style>
