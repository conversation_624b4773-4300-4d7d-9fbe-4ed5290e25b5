<template>
  <q-dialog class="spendr-bank-enter-manually-dialog"
            v-model="visible"
            prevent-close
  >
    <template slot="title">
<!--      <div class="font-16 mb-2">-->
<!--        Enter Manually-->
<!--      </div>-->
      <div>
        <img src="/static/spendr/img/envestnet_yodlee_logo.svg" alt="">
      </div>
      <div class="desc text-center">
        <PERSON><PERSON><PERSON> uses Yodlee to link your bank account.
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-input float-label="Routing Number"
                   autocomplete="no"
                   :error="$v.entity['routingNumber'].$error"
                   @input="$v.entity['routingNumber'].$touch"
                   v-model="entity['routingNumber']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Account Number"
                   autocomplete="no"
                   :error="$v.entity['accountNumber'].$error"
                   @input="$v.entity['accountNumber'].$touch"
                   v-model="entity['accountNumber']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Confirm Account Number"
                   autocomplete="no"
                   :error="$v.entity['confirmAccountNumber'].$error"
                   @input="$v.entity['confirmAccountNumber'].$touch"
                   v-model="entity['confirmAccountNumber']"></q-input>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Continue"
               no-caps
               color="primary"
               class="main"
               @click="Save" />
      </div>
      <div class="desc text-center m-10">
        By providing your credentials,
        we verify in real time that you own the account you want to link.
        We then use this information to establish a secure connection with your financial institution.
      </div>
      <div class="desc footer text-center m-10">
        <img src="/static/spendr/img/security_grey.svg" class="img-security mr-10"/>
        Transfer of your information is encrypted end-to-end
      </div>
      <div class="desc footer text-center mb-30">
        <img class="img-locked mr-10" src="/static/spendr/img/locked_grey.svg"/>
        Your credentials will never be shared with Spendr
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import { notify, notifyForm, request } from '../../../../common'
import { required } from 'vuelidate/lib/validators'

export default {
  name: 'spendr-enter-manually-dialog',
  mixins: [
    Singleton
  ],
  props: {
    merchant: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      defaultEntity: {
        routingNumber: null,
        accountNumber: null,
        confirmAccountNumber: null
      }
    }
  },
  validations: {
    entity: {
      routingNumber: {
        required
        // numeric
      },
      accountNumber: {
        required
        // numeric
      },
      confirmAccountNumber: {
        required,
        // numeric,
        confirmAccountNumber: (value, vm) => {
          return !vm ||
            (!vm.accountNumber && !value) ||
            vm.accountNumber === value
        }
      }
    }
  },
  methods: {
    async Save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        console.log(this.$v)
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/spendr/merchant/${this.merchant.id}/onboard/enter-manually`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.$emit('done')
        this._hide()
      }
    }
  }
}
</script>
<style lang="scss">
  .spendr-bank-enter-manually-dialog {
    .modal-content {
      width: 500px;
    }
    /*.avatar {*/
    /*  width: 50px;*/
    /*  height: 50px;*/
    /*  border-radius: 25px;*/
    /*  overflow: hidden;*/
    /*  margin: 0 auto;*/
    /*}*/
    .modal-scroll {
      max-height: none;
    }
    .desc {
      color: #8E8E93;
      font-size: 12px;
      font-weight: normal;
      &.footer {
        display: flex;
        .img-locked {
          width: 14px;
        }
        .img-security {
          width: 18px;
        }
      }
    }
  }
</style>
