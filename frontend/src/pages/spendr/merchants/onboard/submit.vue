<template>
  <q-card class="spendr-merchant-onboard-review q-card-lg shadow-lg">
    <template v-if="merchant.Status === 'Initial'">
      <q-card-title>
        <div class="font-18 mb-2">Submit and Review</div>
        <div class="font-12 normal text-dark">
          Your application needs to be approved before you can begin processing payments with <PERSON><PERSON>dr.
        </div>
      </q-card-title>
      <q-card-main class="relative">
        <template v-if="canSubmit">
          <p class="mv-10">By clicking "Submit for review", you attest that all information provided is true and accurate.</p>
          <q-btn label="Submit for review"
                 no-caps class="mt-20 wp-100"
                 color="primary"
                 @click="submit" />
        </template>
        <template v-else>
          <p class="mv-10">
            Please complete all the previous steps (the "Bank Account" is optional) before submitting for review.
          </p>
          <q-btn label="Submit for review"
                 no-caps class="mt-20 wp-100"
                 color="primary"
                 disable />
        </template>
      </q-card-main>
    </template>
    <template v-else-if="merchant.Status === 'Pending'">
      <q-card-title>
        <q-icon name="mdi-file-upload" color="primary" class="font-40 mb-15"></q-icon>
        <div class="font-18 mb-15">Application Submitted</div>
        <div class="font-12 normal text-dark">Your application is in review and being processed.</div>
      </q-card-title>
      <q-card-main class="relative">
        <q-btn label="Done"
               no-caps class="mt-20 wp-100"
               color="primary"
               @click="done" />
        <q-btn label="Repeal"
               v-if="masterAdmin || agentAdmin"
               no-caps class="mt-20 wp-100"
               color="negative"
               @click="repeal" />
      </q-card-main>
    </template>
    <template v-else-if="merchant.Status === 'Approved'">
      <q-card-title>
        <q-icon name="mdi-check-circle" color="positive" class="font-40 mb-15"></q-icon>
        <div class="font-18 mb-15">Application Approved</div>
        <div class="font-12 normal text-dark">Congrats! Your application has been approved.</div>
      </q-card-title>
      <q-card-main class="relative">
        <q-btn label="Done"
               no-caps class="mt-20 wp-100"
               color="primary"
               @click="done" />
        <q-btn label="Repeal"
               v-if="masterAdmin || agentAdmin"
               no-caps class="mt-20 wp-100"
               color="negative"
               @click="repeal" />
      </q-card-main>
    </template>
    <template v-else-if="merchant.Status === 'Denied'">
      <q-card-title>
        <q-icon name="mdi-close-circle" color="negative" class="font-40 mb-15"></q-icon>
        <div class="font-18 mb-15">Application Denied</div>
        <div class="font-12 normal text-dark">Please check the below issues, fix them and resubmit the application.</div>
      </q-card-title>
      <q-card-main class="relative">
        <div class="mv-10" v-html="merchant.deniedMessage"></div>
        <q-btn label="Resubmit for review"
               no-caps class="mt-20 wp-100"
               color="primary"
               @click="submit" />
      </q-card-main>
    </template>
    <template v-else>
      <q-card-title>
        <div class="font-18 mb-2">Submit and Review</div>
        <div class="font-12 normal text-dark">Your application needs to be reviewed and approved to continue.</div>
      </q-card-title>
      <q-card-main class="relative">
        <div class="mv-10 text-negative">Your account needs to be active to be able to submit & being reviewed.</div>
      </q-card-main>
    </template>
  </q-card>
</template>

<script>
import { notifySuccess, request } from '../../../../common'
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'

export default {
  name: 'spendr-merchant-onboard-review',
  mixins: [
    SpendrPageMixin
  ],
  props: {
    merchant: {
      type: Object,
      required: true
    },
    canSubmit: {
      type: Boolean,
      required: true
    }
  },
  methods: {
    async submit () {
      this.$q.loading.show()
      const resp = await request(`/spendr/merchant/${this.merchant.id}/onboard/submit`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        notifySuccess(resp.message)
        this.$emit('done', resp.data)
        this.$store.dispatch('User/init', {
          refresh: true
        })
      }
    },
    async repeal () {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to repeal this application?`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/spendr/merchant/${this.merchant.id}/onboard/repeal`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          notifySuccess(resp.message)
          this.$emit('done', resp.data)
        }
      }).catch(() => { })
    },
    done () {
      this.$router.push('/h/spendr/dashboard')
      this.$root.$emit('admin-show-drawer')
    }
  }
}
</script>

<style lang="scss">
.spendr-merchant-onboard-review {
  max-width: 450px;
}
</style>
