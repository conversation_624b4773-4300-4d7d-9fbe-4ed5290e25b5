<template>
  <q-dialog class="spendr-card-bind-location-dialog"
            v-model="visible"
            prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">
        Bind Location
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-select float-label="Related Locations"
                    multiple
                    chips
                    :options="allLocations"
                    v-model="bindLocationIds"></q-select>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Save"
               no-caps
               color="primary"
               class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../../mixins/Singleton'
import { notify, request } from '../../../../../common'

export default {
  name: 'spendr-card-bind-location-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      merchantId: null,
      cardId: null,
      bindLocationIds: [],
      allLocations: []
    }
  },
  methods: {
    async show (param) {
      this.merchantId = param.merchantId
      this.cardId = param.cardId
      this.bindLocationIds = param.bindLocationIds
      await this.getLocations()
    },
    async getLocations () {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/merchant/all-locations/${this.merchantId}`, 'get')
      this.$q.loading.hide()
      if (resp.success) {
        this.allLocations = resp.data
      }
    },
    async save () {
      // if (!this.bindLocationIds.length) {
      //   return notify('Please select the location')
      // }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/merchant/card-bind-location/${this.merchantId}`, 'post', {
        cardId: this.cardId,
        locationIds: this.bindLocationIds
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.$emit('done')
        this._hide()
      } else {
        notify(resp.message)
      }
    }
  }
}
</script>
<style lang="scss">
.spendr-card-bind-location-dialog {
  .modal-content {
    width: 500px;
  }
  .modal-scroll {
    max-height: none;
  }
  .q-chip {
    border-radius: 2rem;
  }
}
</style>
