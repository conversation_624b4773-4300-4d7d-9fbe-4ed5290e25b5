<template>
  <q-page id="spendr_merchant_auto_withdrawal_page">
    <div class="page-header mb-20">
      <div class="title">Auto Withdrawal</div>
      <div class="subtitle">The system will automatically make regular withdrawals for you according to the frequency you have set. If the withdrawal date falls on a non-business day, it will be processed on the next business day. Auto withdrawals will be processed at 3pm EST.</div>
    </div>
    <div class="page-content">
      <div class="row mb-20 gutter-xs">
        <div class="col-sm-3">
          <q-select float-label="Frequency"
                    autocomplete="no"
                    :options="frequencyTypes"
                    v-model="entity['Frequency']"></q-select>
        </div>
        <div class="col-sm-3" v-if="entity['Frequency'] === 'Monthly'">
          <q-select float-label="Day"
                    autocomplete="no"
                    :options="days"
                    v-model="entity['Day']"></q-select>
        </div>
        <div class="col-sm-3" v-if="entity['Frequency'] === 'Weekly'">
          <q-select float-label="Week Day"
                    autocomplete="no"
                    :options="weekDays"
                    v-model="entity['Week Day']"></q-select>
        </div>
        <!-- <div class="col-sm-3">
          <q-select float-label="Time"
                    autocomplete="no"
                    :options="times"
                    v-model="entity['Time']"></q-select>
        </div>
        <div class="col-sm-3">
          <q-select float-label="Time Period"
                    autocomplete="no"
                    :options="timePeriods"
                    v-model="entity['Time Period']"></q-select>
        </div> -->
      </div>
      <div class="row mb-30 gutter-xs">
        <div class="col-sm-6">
          <q-select float-label="Amount"
                    autocomplete="no"
                    :options="amountTypes"
                    v-model="entity['Amount']"></q-select>
        </div>
        <div class="col-sm-4 include-tip" v-if="showTipSetting && entity['Amount'] === 'completed_txns'">
          <q-checkbox v-model="entity['Include Tip']" label="Include tip amounts in withdrawal" />
        </div>
      </div>
      <div class="row mb-30 gutter-xs bind-card" v-if="locations.length > 0 && bankCards.length > 0">
        <div class="col-sm-12">
          <div class="bold">Bind bank account to location</div>
          <div class="subtitle mb-8">Locations without a binded bank account cannot withdraw automatically.</div>
          <div class="locations-cards">
            <div class="row mb-16 gutter-xs location-card" v-for="(location, key) in locations" :key="key">
              <div class="col-sm-6 location">
                {{ location.name }}
              </div>
              <div class="col-sm-6 card">
                <q-select float-label="Bank Account"
                      autocomplete="no"
                      :options="bankCards"
                      v-model="locations[key]['bankCardId']"
                      ></q-select>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-sm-5">
          <div class="bold">Status</div>
          <div class="mt-8">
            <q-radio v-model="entity['Status']"
                     color="blue"
                     val="Enabled"
                     label="Enabled"></q-radio>
            <q-radio v-model="entity['Status']"
                     color="blue"
                     class="ml-15"
                     val="Disabled"
                     label="Disabled"></q-radio>
          </div>
        </div>
      </div>
      <div class="row mt-40 empty-location-or-card-warning" v-if="emptyLocationOrCardWarning">
        <i>
          You have not added any location or bank account yet. This feature is not available.
        </i>
      </div>
      <div class="row wp-100 mv-30">
        <q-btn label="Save"
               no-caps
               v-if="!spendrLogin && !merchantOtherAdmin"
               :disable="emptyLocationOrCardWarning"
               color="primary"
               @click="save" />
      </div>
    </div>
  </q-page>
</template>

<script>
import { request, notify } from '../../../../common'
import Singleton from '../../../../mixins/Singleton'
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'

let days = []
for (let i = 1; i < 29; i++) {
  let j = i.toString()
  if (i < 10) {
    j = '0' + j
  }
  days.push({
    label: j, value: i
  })
}

let times = []
for (let i = 0; i < 12; i++) {
  let j = i.toString()
  if (i < 10) {
    j = '0' + j
  }
  let label = j + ':00'
  times.push({
    label: label, value: label
  })
}

export default {
  name: 'spendr-merchant-auto-withdrawal',
  mixins: [
    Singleton,
    SpendrPageMixin
  ],
  data () {
    return {
      defaultEntity: {
        'Frequency': null,
        'Amount': null,
        'Status': 'Disabled',
        'Day': null,
        'Week Day': null,
        // 'Time': null,
        // 'Time Period': null,
        'Include Tip': false
      },
      locations: [],
      bankCards: [],
      frequencyTypes: [
        { label: 'Monthly', value: 'Monthly' },
        { label: 'Weekly', value: 'Weekly' },
        { label: 'Daily', value: 'Daily' }
      ],
      amountTypes: [
        { label: 'Full Available Balance', value: 'all' },
        { label: 'All Completed Transactions Since Last Unload', value: 'completed_txns' }
      ],
      days: days,
      weekDays: [
        { label: 'Monday', value: 'Monday' },
        { label: 'Tuesday', value: 'Tuesday' },
        { label: 'Wednesday', value: 'Wednesday' },
        { label: 'Thursday', value: 'Thursday' },
        { label: 'Friday', value: 'Friday' }
      ],
      // times: times,
      // timePeriods: [
      //   { label: 'AM', value: 'AM' },
      //   { label: 'PM', value: 'PM' }
      // ],
      emptyLocationOrCardWarning: false,
      showTipSetting: false
    }
  },
  mounted () {
    this.getSetting()
  },
  methods: {
    async getSetting () {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/merchant/setting/get-auto-unload`, 'get')
      this.$q.loading.hide()
      if (resp.success) {
        this.entity = resp.data.config
        this.locations = resp.data.locations
        this.bankCards = resp.data.bankCards
        if (this.locations.length <= 0 || this.bankCards.length <= 0) {
          this.emptyLocationOrCardWarning = true
        }
        this.showTipSetting = resp.data.showTipSetting
      }
    },
    verify () {
      if (this.emptyLocationOrCardWarning) {
        notify('You have not added any location or bank account yet. This feature is not available.', 'negative')
        return false
      }
      let frequency = this.entity['Frequency']
      if (
        !this.entity['Frequency'] ||
        // !this.entity['Time'] ||
        // !this.entity['Time Period'] ||
        !this.entity['Amount'] ||
        !this.entity['Status']
      ) {
        notify('Please complete the form before submitting.', 'negative')
        return false
      }
      if (frequency === 'Monthly' && !this.entity['Day']) {
        notify('"Day" cannot be empty.', 'negative')
        return false
      } else if (frequency === 'Weekly' && !this.entity['Week Day']) {
        notify('"Week Day" cannot be empty.', 'negative')
        return false
      }
      return true
    },
    async save () {
      const verifyRes = this.verify()
      if (!verifyRes) {
        return false
      }
      let formData = this.entity
      formData.locations = this.locations
      this.$q.loading.show({
        message: 'Saving...'
      })
      const resp = await request('/admin/spendr/merchant/setting/set-auto-unload', 'post', formData)
      this.$q.loading.hide()
      if (resp.success) {
        notify()
        await this.getSetting()
      }
    }
  }
}
</script>

<style lang="scss">
#spendr_merchant_auto_withdrawal_page {
  max-width: 840px !important;
  .subtitle {
    font-size: 12px;
    color: #8C8C8C;
  }
  .bind-card .subtitle, .empty-location-or-card-warning {
    color: #ff7a00;
  }
  .locations-cards {
    padding: 32px 24px 8px;
    border: 1px dashed lightgrey;
    border-radius: 8px;
    max-height: 330px;
    overflow-y: scroll;
    .location-card {
      margin-left: 0;
      background-color: #f5f5f5;
      padding: 16px 20px 24px;
      border-radius: 8px;
    }
    .location {
      align-self: center;
      padding-left: 0;
    }
    .card {
      align-self: center;
    }
  }
  .empty-location-or-card-warning {
    margin-bottom: -24px;
  }
  .include-tip {
    align-self: center;
  }
}
</style>
