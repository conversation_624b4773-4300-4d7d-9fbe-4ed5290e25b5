<template>
  <div id="spendr-merchant-setting">
    <div class="logo">
      <img :src="logo"
           alt="">
    </div>
    <div class="steppers">
      <div class="stepper"
           v-for="s in steps"
           :class="{active: s === step}"
           @click="step = s"
           :key="s">
        <div class="text">{{ s }}</div>
        <div class="indicator"></div>
      </div>
    </div>
    <q-card v-show="step === 'Tipping Setting'"
            class="setting-card q-card-lg shadow-lg">
      <q-card-title>
        <div class="font-18 mb-2">{{ step }}</div>
        <div class="font-12 normal text-dark">
          If you disable tipping, consumers will not be able to add tips for any transactions.
        </div>
      </q-card-title>
      <q-card-main>
        <div class="row gutter-form mt--10 mb-5">
          <div class="col-sm-12">
            <q-select float-label="Tipping Enable"
                      autocomplete="no"
                      :options="tipping_enables"
                      :disable="!tipping['Change Enable']"
                      v-model="tipping['Tipping Enable']"></q-select>
          </div>
        </div>
        <q-btn label="Save Tipping"
               no-caps class="mt-20 wp-100"
               color="positive"
               @click="saveTipping" />
      </q-card-main>
    </q-card>
  </div>
</template>

<script>
import { notify, request } from '../../../../common'
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'

export default {
  name: 'SpendrSetting',
  mixins: [
    SpendrPageMixin
  ],
  data () {
    return {
      tipping: {
        'Change Enable': false,
        'Tipping Enable': null
      },
      tipping_enables: [
        { label: 'Enabled', value: true },
        { label: 'Disabled', value: false }
      ],
      step: 'Tipping Setting',
      steps: ['Tipping Setting']
    }
  },
  methods: {
    async getTipSetting () {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/merchants/${this.$store.state.User.merchantId}/tipping`)
      this.$q.loading.hide()
      if (resp.success) {
        this.tipping = resp.data
      }
    },
    async saveTipping () {
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/merchants/${this.$store.state.User.merchantId}/tipping`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
      }
    }
  },
  mounted () {
    this.getTipSetting()
  }
}
</script>

<style lang="scss">
  #spendr-merchant-setting {
    margin: 80px auto;
    padding-bottom: 40px;
    text-align: center;
    .steppers {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      margin: 0 auto 20px;

      .stepper {
        display: flex;
        flex-direction: column;
        padding: 0 10px;
        margin-bottom: 10px;
        cursor: pointer;

        .text {
          color: #999;
          font-size: 13px;
        }

        .indicator {
          margin-top: 4px;
          width: 100%;
          height: 6px;
          border-radius: 6px;
          background: var(--span-color-primary);
          opacity: 0.25;
        }
        &.active {
          .text {
            color: #333;
          }
          .indicator {
            opacity: 1;
          }
        }
        &:hover {
          .text {
            color: #555;
          }
          .indicator {
            opacity: 0.7;
          }
        }
      }
    }
    .setting-card {
      max-width: 500px;
    }
    > div {
      margin-left: auto;
      margin-right: auto;
    }
  }
</style>
