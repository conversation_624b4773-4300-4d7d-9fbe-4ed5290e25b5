<template>
  <q-dialog class="spendr-merchant-tipping-dialog"
            v-model="visible"
            prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">
        Tipping Config
      </div>
      <div class="font-12 normal">
        If Tipping Enable for merchants is disabled, the merchants will not be able to enable or set their own tipping.
        They can only see and set their own tipping when you set the Tipping Enable to enabled.
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-20">
        <div class="col-sm-12">
          <q-select float-label="Tipping Enable"
                    autocomplete="no"
                    :options="tipping_enables"
                    v-model="entity['Tipping Enable']"></q-select>
        </div>
        <div class="col-sm-12">
          <q-select float-label="Charge Fees on Tips"
                    autocomplete="no"
                    :options="charge_fees"
                    v-model="entity['Charge Fees']"></q-select>
        </div>
        <div class="col-sm-12">
          <q-select float-label="Enable Locations"
                    autocomplete="no"
                    :options="all_locations"
                    filter
                    autofocus-filter
                    multiple
                    v-model="entity['Locations']"></q-select>
          <div class="font-12 normal">Select none location means tipping is for all locations</div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Save"
               no-caps
               color="primary"
               class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, notifyResponse, request } from '../../../common'
import _ from 'lodash'

export default {
  name: 'spendr-merchant-tipping-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      merchantId: null,
      defaultEntity: {
        'Change Enable': false,
        'Tipping Enable': null,
        'Charge Fees': null,
        'Tipping Type': null,
        'Locations': []
      },
      tipping_enables: [
        { label: 'Enabled', value: true },
        { label: 'Disabled', value: false }
      ],
      charge_fees: [
        { label: 'Yes', value: true },
        { label: 'No', value: false }
      ],
      tipping_types: [
        { label: 'Per Clerk', value: 'Per Clerk' },
        { label: 'Combined', value: 'Combined' }
      ],
      all_locations: []
    }
  },
  methods: {
    async show (data) {
      this.merchantId = data['Merchant ID']
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/merchants/${this.merchantId}/tipping`)
      this.$q.loading.hide()
      if (resp.success) {
        this.tipping_types = resp.data.tipping_types
        this.all_locations = resp.data['All Locations']
        this.entity = _.assignIn({}, this.entity, resp.data)
      }
    },
    async save () {
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/merchants/${this.merchantId}/tipping`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this._hide()
      } else {
        notifyResponse(resp.message)
      }
    }
  }
}
</script>
<style lang="scss">
.spendr-merchant-tipping-dialog {
  .modal-content {
    width: 500px;
  }
  .modal-scroll {
    max-height: none;
  }
}
</style>
