<template>
  <q-dialog class="spendr-transaction-detail-dialog"
            v-model="visible"
            prevent-close
  >
    <template slot="title">
      <q-icon class="font-30"
              name="mdi-format-list-bulleted"></q-icon>
      <div class="font-16 mb-2">{{ 'Transaction Details' }}</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5 text-left">
        <div class="col-sm-12">Transaction ID: {{ entity['Transaction ID'] }}</div>
        <div class="col-sm-12">Location Name: {{ entity['Location Name'] }}</div>
        <div class="col-sm-12">Clerk Name: {{ entity['Clerk Name'] }}</div>
        <div class="col-sm-12">Terminal: {{ entity['Terminal'] }}</div>
        <div class="col-sm-12">Consumer: {{ entity['Consumer'] }}</div>
        <div class="col-sm-12">Amount: {{ entity['Amount'] }}</div>
        <div class="col-sm-12">Spendr Fee: {{ entity['Spendr Fee'] }}</div>
        <div class="col-sm-12">Tern Fee: {{ entity['Tern Fee'] }}</div>
        <div class="col-sm-12">Time: {{ entity['Time'] }}</div>
        <div class="col-sm-12">Type: {{ entity['Type'] }}</div>
        <div class="col-sm-12">Status: {{ entity['Status'] }}</div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn :label="'Ok'"
               no-caps
               color="positive"
               class="main"
               @click="_hide" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import MexStateListMixin from '../../../../mixins/mex/MexStateListMixin'

export default {
  name: 'spendr-transaction-detail-dialog',
  mixins: [
    Singleton,
    MexStateListMixin
  ],
  data () {
    return {
    }
  },
  computed: {},
  methods: {
    async show () {
      if (!this.entity || !this.entity['Transaction ID']) {
      }
    }
  }
}
</script>

<style lang="scss">
  .spendr-transaction-detail-dialog {
    .modal-content {
      width: 500px;
    }
    .modal-scroll {
      max-height: none;
    }
  }
</style>
