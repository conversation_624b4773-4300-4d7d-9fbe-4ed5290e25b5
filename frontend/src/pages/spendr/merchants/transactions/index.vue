<template>
  <q-page id="spendr_merchant_transactions__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :iso-week="true"
                         :default="dateRange"
                         :ranges="datesWithDaily"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total Transactions</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalPurchase || 0 }}</div>
                  <div class="description">Total Payment</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="negative"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalRefund || 0 }}</div>
                  <div class="description">Total Refund</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalPurchaseAmount || 0 }}</div>
                  <div class="description">Total Payment Amount</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="negative"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalRefundAmount || 0 }}</div>
                  <div class="description">Total Refund Amount</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6" v-if="!merchantAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.spendrRevenue || 0 }}</div>
                  <div class="description">Spendr Revenue</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6" v-if="quick.showTip">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalTips || 0 }}</div>
                  <div class="description">Total # of Tips</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6" v-if="quick.showTip">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalTipsAmount || 0 }}</div>
                  <div class="description">Total $ of Tips</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6" v-if="isMasterAdmin() || spendrAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.ternFee || 0 }}</div>
                  <div class="description">Tern Fee</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.spendrFee || 0 }}</div>
                  <div class="description">Spendr Fee</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6" v-if="quick.showTip">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalTipsSpendrFee || 0 }}</div>
                  <div class="description">Tip Spendr Fee</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id"
      >
        <template slot="top-left">
          <strong>{{ title }} Report</strong>
        </template>
        <template slot="top-right">
          <q-btn v-if="!spendrCustomerSupportLogin && !merchantOtherAdmin"
                 icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
<!--          <q-search icon="search"-->
<!--                    v-model="keyword"-->
<!--                    type="text"-->
<!--                    class="mr-10 w-200 dense"-->
<!--                    @keydown.13="delayedReload"-->
<!--                    @clear="delayedReload"-->
<!--                    clearable-->
<!--                    placeholder="Search...">-->
<!--          </q-search>-->
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)"
        >
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]"
          >
            <div notranslate="">
              <template v-if="col.field === 'Status'">
                <q-chip class="font-12"
                        :class="statusClass(props.row['Status'])">
                  {{ props.row['Status'] }}
                </q-chip>
              </template>
              <template v-else-if="col.field === 'Type'">
                <div class="font-12 uppercase">
                  {{ props.row['Type'] }}
                </div>
              </template>
<!--              <template v-else-if="col.field === 'Actions'">-->
<!--                <q-btn-dropdown class="btn-sm"-->
<!--                                no-caps-->
<!--                                color="grey-2"-->
<!--                                text-color="dark"-->
<!--                                :label="col.label">-->
<!--                  <q-list link>-->
<!--                    <q-item v-close-overlay-->
<!--                            @click.native="view(props.row)">-->
<!--                      <q-item-main>View</q-item-main>-->
<!--                    </q-item>-->
<!--                  </q-list>-->
<!--                </q-btn-dropdown>-->
<!--              </template>-->
              <template v-else>{{ _.get(props.row, col.field) }}</template>
            </div>
          </q-td>
        </q-tr>
        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
<!--    <DetailDialog>-->
<!--    </DetailDialog>-->
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../../common'
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import _ from 'lodash'
// import DetailDialog from './detail'

export default {
  name: 'spendr-merchant-transactions',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-merchant-transactions')
  ],
  components: {
    // DetailDialog
  },
  data () {
    const columns = [
      'Transaction ID', 'Date & Time', 'First Name', 'Last Name', 'Consumer ID', 'Payment Amount',
      'Spendr Fee', 'Tip Amount', 'Tip Spendr Fee'
    ]
    if (this.isMasterAdmin() || this.isSpendrAdmin()) {
      columns.push('Estimated Tern Fee')
    }
    for (const col of [
      'Refund Fee', 'Merchant Balance Before', 'Merchant Balance After', 'Merchant Name', 'Location Name', 'Clerk Name',
      'Terminal', 'Refund From ID', 'Transaction Type', 'Status'
    ]) {
      columns.push(col)
    }
    return {
      title: 'Transactions',
      requestUrl: `/admin/spendr/merchant/transactions/list`,
      downloadUrl: `/admin/spendr/merchant/transactions/export`,
      filtersUrl: `/admin/spendr/merchant/transactions/filters`,
      columns: generateColumns(columns),
      filterOptions: [
        {
          value: 'filter[t.id=]',
          label: 'Transaction ID'
        },
        {
          value: 'filter[c.id]',
          label: 'Consumer ID'
        }, {
          value: 'filter[l.id=]',
          label: 'Location Name',
          options: [],
          source: 'locations'
        }, {
          value: 'txn_type',
          label: 'Transaction Type',
          options: [],
          source: 'types'
        }
      ],
      keyword: '',
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true
    }
  },
  computed: {
    visibleColumns () {
      return _.filter(this.columns, c => {
        if (this.quick && ['Tip Amount', 'Tip Spendr Fee'].indexOf(c.label) > -1) {
          c.hidden = !this.quick.showTip
        }
        return !c.hidden
      })
    }
  },
  methods: {
    // view (row) {
    //   this.$root.$emit('show-spendr-transaction-detail-dialog', row)
    // },
    statusClass (status) {
      return {
        'Pending': 'dark',
        'Completed': 'positive',
        'Cancelled': 'orange',
        'Error': 'negative',
        'Expired': 'pansy'
      }[status] || status
    }
  }
}
</script>

<style lang="scss">
  #spendr_merchant_transactions__index_page {
  }
</style>
