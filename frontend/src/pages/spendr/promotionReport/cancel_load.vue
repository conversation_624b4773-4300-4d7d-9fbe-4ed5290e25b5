<template>
  <q-dialog class="spendr-earn-revoke-dialog"
            v-model="visible">
    <template slot="title">
      <div class="font-18 mb-2">Manual Cancel Load/Unload</div>
      <div class="font-14 normal">
        Please fill in the reason for cancellation.
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="col-sm-12 col-md-4 refund-info">
        <q-input autocomplete="no"
                 v-model="form.reason"></q-input>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <div class="row">
          <q-btn label="Cancel"
                 no-caps
                 class="refund-btn"
                 color="grey-3"
                 text-color="tertiary"
                 @click="cancel" />
          <q-btn label="Submit"
                 no-caps
                 color="positive"
                 class="main refund-btn"
                 @click="submit" />
        </div>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { EventHandlerMixin, request, notify } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'

export default {
  name: 'spendr-rewards-revoke-dialog',
  mixins: [
    Singleton,
    SpendrPageMixin,
    EventHandlerMixin('spendr-rewards-revoke-dialog', 'show')
  ],
  data () {
    return {
      form: {
        id: null,
        reason: null
      }
    }
  },
  methods: {
    async show () {
      this.$q.loading.show()
      this.form.id = this.entity['ID']
      this.form.reason = null
      this.$q.loading.hide()
    },
    async submit () {
      if (!this.form.reason || !this.form.reason.trim()) {
        this.form.reason = null
        notify('Please input the reason.', 'negative')
        return
      }
      this.form.reason = this.form.reason.trim()
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/promo-load/${this.form.id}/manual-revoke`, 'post', this.form)
      if (resp.success) {
        this.$emit('cancelled')
        this._hide()
      }
      this.$q.loading.hide()
    },
    cancel () {
      this._hide()
    }
  }
}
</script>

<style lang="scss">
.spendr-earn-revoke-dialog {
  .modal-content {
    width: 450px;
  }
  .refund-btn {
    font-size: 16px;
    .q-btn-inner {
      line-height: 28px;
    }
  }
  .close-btn {
    background-color: #7ac142;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
  }
  .refund-info {
    .q-list {
      border-top: none;
      border-right: none;
      border-left: none;
      // border-color: #e2e2ea;
      font-size: 15px;
      max-width: 325px;
      margin: 0 auto;
    }
    .q-item {
      padding: 15px 0;
    }
    .q-item-side {
      color: #92929e;
    }
    .q-item-division {
      border-color: #e2e2ea;
    }
    .q-item-main {
      text-align: right;
      color: #171725;
      font-size: 16px;
    }
    .refund-amount {
      max-width: 350px;
      margin: 0 auto;
      .input-group {
        color: #231f20;
        font-weight: 600;
        font-size: 22px;
        display: flex;
        justify-content: center;
        width: 150px;
        margin: 20px auto;
        border-radius: 10px;
        border: solid 1px #e2e2ea;
        padding-left: 20px;
        span {
          line-height: 53px;
        }
        input {
          display: inline-block;
          padding: 10px 5px;
          border-radius: 10px;
          text-align: center;
          border: none;
          outline: none;
          &:active,
          &:hover,
          &:focus {
            border: none;
          }
        }
      }
    }
  }
  .amount-header {
    font-weight: 600;
  }
  .confirm-amount {
    .header-title {
      font-size: 20px;
      font-weight: 600;
      color: #7ac142;
      margin: 0;
    }
    .sub-title {
      font-size: 14px;
      text-align: center;
      color: #92929e;
      margin: 0;
    }
  }
  .refund-status {
    color: #00d993;
    background: rgba($color: #00d993, $alpha: 0.1);
    border-radius: 5px;
    padding: 5px;
  }
}
</style>
