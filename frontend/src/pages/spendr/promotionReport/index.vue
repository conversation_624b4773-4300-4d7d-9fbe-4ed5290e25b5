<template>
  <q-page id="spendr_promotion_report__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :iso-week="true"
                         :default="dateRange"
                         :ranges="datesWithDaily"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total Promo Code Claimed</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-multiple"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.redeem_total || 0 | moneyFormat }}</div>
                  <div class="description">Total Claimed Amount</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-multiple"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.avg_redeem || 0 | moneyFormat }}</div>
                  <div class="description">Average Claimed Amount</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-multiple"
                        color="orange"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.revoke_total || 0 | moneyFormat }}</div>
                  <div class="description">Total Revoked Amount</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }}</strong>
        </template>
        <template slot="top-right">
          <q-btn v-if="!bankAdmin"
                 icon="mdi-file-download-outline"
                 color="orange"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
<!--          <q-search icon="search"-->
<!--                    v-model="keyword"-->
<!--                    type="text"-->
<!--                    class="mr-10 w-200 dense"-->
<!--                    @keydown.13="delayedReload"-->
<!--                    @clear="delayedReload"-->
<!--                    clearable-->
<!--                    placeholder="Search...">-->
<!--          </q-search>-->
          <q-select v-model="loadType"
                    :options="loadTypeOptions"
                    @input="reload"
                    class="load-type"></q-select>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Reward Status'">
              <q-chip class="font-12 load-status"
                      :class="statusClass(props.row['Reward Status'].toLowerCase())">
                {{ props.row['Reward Status'] }}
              </q-chip>
            </template>
            <template v-else-if="(isMasterAdmin() || agentAdmin)
              && col.field === 'Actions'
              && props.row['Reward Type'] === 'Promo Code'
              && props.row['Reward Status'] === 'loaded'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-item
                  v-close-overlay
                  class="cursor-pointer"
                  @click.native="manualCancel(props.row)">
                  <q-item-main>Revoke</q-item-main>
                </q-item>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
<!--            <template>-->
<!--              <div notranslate="">{{ _.get(props.row, col.field) }}</div>-->
<!--            </template>-->
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <CancelLoad @cancelled="reload"></CancelLoad>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { generateColumns } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import MexMemberMixin from '../../../mixins/mex/MexMemberMixin'
import CancelLoad from './cancel_load'

export default {
  name: 'spendr-promotion-report',
  mixins: [
    SpendrPageMixin,
    MexMemberMixin,
    ListPageMixin,
    FreezeColumnMixin
  ],
  components: {
    CancelLoad
  },
  data () {
    const columns = [
      'ID', 'User ID', 'First Name', 'Last Name', 'Email',
      'Reward Type', 'Rewards Earned', 'Total Spend', 'Redemption Date',
      'Reward Status'
    ]
    if (this.isMasterAdmin() || this.isAgentAdmin()) {
      columns.push('Actions')
    }
    return {
      title: 'Promo Code Report',
      requestUrl: `/admin/spendr/promotion-report/list`,
      downloadUrl: `/admin/spendr/promotion-report/export`,
      columns: generateColumns(columns),
      filterOptions: [
        {
          value: 'filter[u.id]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 1,
      autoLoad: true,
      loadTypeOptions: [
        { label: 'All', value: 'All' },
        { label: 'Promo Code', value: 'load_card' },
        { label: 'Revoke', value: 'unload' }
      ],
      loadType: 'All'
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        'loadType': this.loadType
      }
    },
    statusClass (status) {
      return {
        'pending': 'blue',
        'initiated': 'dark',
        'loaded': 'positive',
        'unloaded': 'positive',
        'cancelled': 'orange',
        'error': 'negative',
        'expired': 'pansy'
      }[status] || status
    },
    manualCancel (row) {
      this.$root.$emit('show-spendr-rewards-revoke-dialog', row)
    }
  }
}
</script>

<style lang="scss">
.spendr_promotion_report__index_page {
  .capitalize {
    text-transform: capitalize;
  }
}
.modal.minimized .modal-content {
  max-width: 600px;
}
.load-type {
  min-width: 100px;
}
</style>
