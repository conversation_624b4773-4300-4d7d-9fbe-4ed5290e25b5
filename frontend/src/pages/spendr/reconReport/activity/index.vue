<template>
  <q-page id="spendr_activity_report__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :iso-week="true"
                         :default="dateRange"
                         :ranges="datesWithDaily"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="description">Spendr Revenue</div>
                  <div class="value">{{ quick.spendrRevenue || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="description">Total Bank Fees</div>
                  <div class="value">{{ quick.bankFees || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="description">Total Estimated Bank Fees</div>
                  <div class="value">{{ quick.estimatedBankFees || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account" color="negative"></q-icon>
                <div class="column">
                  <div class="description"># of Negative Members</div>
                  <div class="value">{{ quick.totalNegativeMembers || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="negative"></q-icon>
                <div class="column">
                  <div class="description">$ of total Negative Member</div>
                  <div class="value">{{ quick.totalNegativeMemberBalance || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="negative"></q-icon>
                <div class="column">
                  <div class="description">
                    $ of Pending Balance
                    <q-icon name="mdi-open-in-new" color="positive" @click.native="viewPendingBalanceReport"
                            class="ml-5 pointer">
                      <q-tooltip>Pending balance report</q-tooltip>
                    </q-icon>
                  </div>
                  <div class="value">{{ quick.totalPendingBalance || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <h6 class="sub-title">Transaction</h6>
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="positive"></q-icon>
                <div class="column">
                  <div class="description"># of TRX</div>
                  <div class="value">{{ quick.totalTransactions || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="description">Spendr Fee</div>
                  <div class="value">{{ quick.totalTransactionSpendrFee || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="positive"></q-icon>
                <div class="column">
                  <div class="description"># of In-store Purchase</div>
                  <div class="value">{{ quick.totalInStorePayment || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="description">$ of total In-store Purchase</div>
                  <div class="value">{{ quick.totalInStorePaymentAmount || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="negative"></q-icon>
                <div class="column">
                  <div class="description"># of In-store Refund</div>
                  <div class="value">{{ quick.totalInStoreRefund || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="negative"></q-icon>
                <div class="column">
                  <div class="description">$ of total In-store Refund</div>
                  <div class="value">{{ quick.totalInStoreRefundAmount || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <h6 class="sub-title">Load/Unload</h6>
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="positive"></q-icon>
                <div class="column">
                  <div class="description"># of Load/Unload</div>
                  <div class="value">{{ quick.totalLoadUnload || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="description">$ of ACH Return Fee to Spendr</div>
                  <div class="value">{{ quick.totalLoadAchReturnFee || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="positive"></q-icon>
                <div class="column">
                  <div class="description"># of Loads</div>
                  <div class="value">{{ quick.totalLoads || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="description">$ of total Load</div>
                  <div class="value">{{ quick.totalLoadAmount || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="positive"></q-icon>
                <div class="column">
                  <div class="description"># of Consumer Loads</div>
                  <div class="value">{{ quick.totalConsumerLoads || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="description">$ of total Consumer Load</div>
                  <div class="value">{{ quick.totalConsumerLoadAmount || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="positive"></q-icon>
                <div class="column">
                  <div class="description"># of Partner Loads</div>
                  <div class="value">{{ quick.totalPartnerLoads || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="description">$ of total Partner Load</div>
                  <div class="value">{{ quick.totalPartnerLoadAmount || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="positive"></q-icon>
                <div class="column">
                  <div class="description"># of Consumer Normal Loads</div>
                  <div class="value">{{ quick.totalConsumerNormalLoads || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="description">$ of total Consumer Normal Load</div>
                  <div class="value">{{ quick.totalConsumerNormalLoadAmount || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="positive"></q-icon>
                <div class="column">
                  <div class="description"># of Manually Loads</div>
                  <div class="value">{{ quick.totalManuallyLoads || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="description">$ of total Manually Load</div>
                  <div class="value">{{ quick.totalManuallyLoadAmount || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="positive"></q-icon>
                <div class="column">
                  <div class="description"># of Earn Loads</div>
                  <div class="value">{{ quick.totalEarnLoads || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="description">$ of total Earn Load</div>
                  <div class="value">{{ quick.totalEarnLoadAmount || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="positive"></q-icon>
                <div class="column">
                  <div class="description"># of Promo Code Loads</div>
                  <div class="value">{{ quick.totalPromoCodeLoads || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="description">$ of total Promo Code Load</div>
                  <div class="value">{{ quick.totalPromoCodeLoadAmount || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="purple"></q-icon>
                <div class="column">
                  <div class="description"># of Unloads</div>
                  <div class="value">{{ quick.totalUnloads || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="purple"></q-icon>
                <div class="column">
                  <div class="description">$ of total Unload</div>
                  <div class="value">{{ quick.totalUnloadAmount || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="purple"></q-icon>
                <div class="column">
                  <div class="description"># of Consumer Unloads</div>
                  <div class="value">{{ quick.totalConsumerUnloads || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="purple"></q-icon>
                <div class="column">
                  <div class="description">$ of total Consumer Unload</div>
                  <div class="value">{{ quick.totalConsumerUnloadAmount || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="purple"></q-icon>
                <div class="column">
                  <div class="description"># of Partner Unloads</div>
                  <div class="value">{{ quick.totalPartnerUnloads || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="purple"></q-icon>
                <div class="column">
                  <div class="description">$ of total Partner Unload</div>
                  <div class="value">{{ quick.totalPartnerUnloadAmount || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="purple"></q-icon>
                <div class="column">
                  <div class="description"># of Merchant Unloads</div>
                  <div class="value">{{ quick.totalMerchantUnloads || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="purple"></q-icon>
                <div class="column">
                  <div class="description">$ of total Merchant Unload</div>
                  <div class="value">{{ quick.totalMerchantUnloadAmount || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
    </div>
<!--    <BatchExportDialog ref="exportDialog"></BatchExportDialog>-->
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin } from '../../../../common'
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import MexMemberMixin from '../../../../mixins/mex/MexMemberMixin'

export default {
  name: 'spendr-activity-report',
  mixins: [
    SpendrPageMixin,
    MexMemberMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-activity-report')
  ],
  components: {},
  data () {
    return {
      title: 'Activity Report',
      requestUrl: `/admin/spendr/recon/activity-report/list`,
      autoLoad: true
    }
  },
  methods: {
    // view () {
    //   this.$root.$emit('show-spendr-activity-detail-dialog')
    // }
    viewPendingBalanceReport () {
      window.open(`/admin#/h/spendr/recon-report/activity/pending-balance`, '_blank')
    }
  }
}
</script>
<style lang="scss">
#spendr_activity_report__index_page {
  .sub-title {
    line-height: 0;
  }
  .page-content .top-statics .description {
    font-size: 14px;
  }
}
</style>
