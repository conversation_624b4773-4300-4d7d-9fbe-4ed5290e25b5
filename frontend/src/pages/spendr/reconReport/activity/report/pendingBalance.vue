<template>
  <q-page class="spendr_activity_report__pending_balance_page">
    <div class="page-content top-statics-style">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="description">Total Users</div>
                  <div class="value">{{ quick.total || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="orange"></q-icon>
                <div class="column">
                  <div class="description">Total Pending</div>
                  <div class="value">{{ quick.totalPending || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="orange"></q-icon>
                <div class="column">
                  <div class="description">Total Pending Balance</div>
                  <div class="value">{{ quick.totalPendingBalance || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="orange"></q-icon>
                <div class="column">
                  <div class="description">Total Pending ACH return fee</div>
                  <div class="value">{{ quick.totalPendingAchReturnFee || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
                :columns="visibleColumns"
                :pagination.sync="pagination"
                :loading="loading"
                class="main-table sticky-table mt-16"
                @request="request"
                separator="none"
                row-key="id">
          <template slot="top-left">
            <div class="font-16 bold">
              <span class="va-m">{{ title }}</span>
            </div>
          </template>
          <template slot="top-right">
            <q-btn v-if="!spendrCustomerSupportLogin && !merchantOtherAdmin"
                  icon="mdi-file-download-outline"
                  color="blue"
                  label="Export as XLSX"
                  @click="download"
                  class="btn-sm mr-8"
                  no-caps></q-btn>
            <q-search icon="search"
                      v-model="keyword"
                      type="text"
                      class="mr-10 w-200 dense"
                      @keydown.13="delayedReload"
                      @clear="delayedReload"
                      clearable
                      placeholder="Search...">
            </q-search>
            <q-btn flat
                  round
                  dense
                  color="faded"
                  @click="reload"
                  icon="refresh" />
          </template>
          <StickyHead slot="header"
                      slot-scope="props"
                      :freeze-column.sync="freezeColumn"
                      :column-styles="columnStyles"
                      :column-classes="columnClasses"
                      :readonly="true"
                      :sort="true"
                      @change="reload"
                      ref="stickyHead"
                      :props="props"></StickyHead>
          <q-tr slot="body"
                slot-scope="props"
                :props="props"
                :class="rowClass(props.row)">
            <q-td v-for="(col, i) in props.cols"
                  :key="col.field"
                  :align="col.align"
                  :style="columnStyles[i]"
                  :class="columnClasses[i]">
              <div notranslate="">
                <template v-if="col.field === 'Details'">
                  <div v-for="(val, key) in props.row['Details']" :key="key">
                    {{ val['Load ID'] + ': ' + val['Amount'] }}
                  </div>
                </template>
                <template v-else>{{ _.get(props.row, col.field) }}</template>
              </div>
            </q-td>
          </q-tr>

          <div slot="pagination"
              slot-scope="props">
            <Pagination :props="props"
                        :pagination="pagination"
                        @reload="reload"></Pagination>
          </div>
        </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                          :visible.sync="filtersDialog"
                          :filters.sync="filters"
                          :options="filterOptions"
                          @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../../../common'
import SpendrPageMixin from '../../../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../../../mixins/FreezeColumnMixin'

export default {
  name: 'spendr-activity-pending-balance-report',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-activity-pending-balance')
  ],
  data () {
    const columns = [
      'User ID', 'First Name', 'Last Name', 'Email', 'Current Balance', 'Pending Balance', 'Details'
    ]
    return {
      title: 'Pending Balance Report',
      columns: generateColumns(columns),
      requestUrl: `/admin/spendr/recon/activity-report/pending-balance/list`,
      downloadUrl: `/admin/spendr/recon/activity-report/pending-balance/export`,
      filterOptions: [
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 0
    }
  },
  methods: {}
}
</script>
<style lang="scss">
</style>
