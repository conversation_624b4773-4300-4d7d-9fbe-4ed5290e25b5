<template>
  <q-page id="spendr_ledger_report__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="balanceType"
                  :options="balanceTypeOptions"
                  @input="reload"
                  class="mr-8"
        ></q-select>
        <q-datetime float-label="Date"
                    class="limit-date"
                    autocomplete="no"
                    type="date"
                    format="MM/DD/YYYY"
                    :max="maxDate"
                    :clearable="true"
                    @change="reload"
                    v-model="date">
        </q-datetime>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalProgramBalance || 0 }}</div>
                  <div class="description">Total Program Balance</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalMemberBalance || 0 }}</div>
                  <div class="description">Total Member Balance</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalMerchantBalance || 0 }}</div>
                  <div class="description">Total Merchant Balance</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalPartnerBalance || 0 }}</div>
                  <div class="description">Total Partner Balance</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="negative"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalNegativeMemberBalance || 0 }}</div>
                  <div class="description">Total Negative Member Balance</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalNetMemberBalance || 0 }}</div>
                  <div class="description">Net Member Balance</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalNetCustomerLiability || 0 }}</div>
                  <div class="description">Net Customer Liability</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 v-if="!bankAdmin"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-select v-model="role"
                    :options="roleOptions"
                    @input="reload"
                    class="mr-8 dense"
          ></q-select>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <div notranslate="">{{ _.get(props.row, col.field) }}</div>
          </q-td>
        </q-tr>
        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../../common'
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import MexMemberMixin from '../../../../mixins/mex/MexMemberMixin'

export default {
  name: 'spendr-ledger-report',
  mixins: [
    SpendrPageMixin,
    MexMemberMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-ledger-report')
  ],
  components: {},
  data () {
    const columns = [
      'Role', 'User ID', 'Email', 'Balance History ID',
      'Balance Change Time', 'Active Balance'
    ]
    return {
      title: 'Ledger Report',
      requestUrl: `/admin/spendr/recon/ledger-report/list`,
      downloadUrl: `/admin/spendr/recon/ledger-report/export`,
      columns: generateColumns(columns),
      filterOptions: [],
      freezeColumn: -1,
      freezeColumnRight: 1,
      autoLoad: true,
      role: 'consumer',
      roleOptions: [
        { label: 'Consumer', value: 'consumer' },
        { label: 'Merchant', value: 'merchant' },
        { label: 'Partner', value: 'partner' }
      ],
      balanceType: 'all',
      balanceTypeOptions: [
        { label: 'All Balance', value: 'all' },
        { label: 'Positive Balance', value: 'positive' },
        { label: 'Negative Balance', value: 'negative' }
      ],
      date: null,
      maxDate: new Date()
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        role: this.role,
        date: this.date,
        balanceType: this.balanceType
      }
    }
  }
}
</script>
<style lang="scss">
#spendr_ledger_report__index_page {
  .limit-date {
    min-width: 150px;
  }
}
</style>
