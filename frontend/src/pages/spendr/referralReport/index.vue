<template>
  <q-page id="spendr_referral_report__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :iso-week="true"
                         :default="dateRange"
                         :ranges="datesWithDaily"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total # of Referral</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-multiple"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.finished }}</div>
                  <div class="description">Total # of Finished</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }}</strong>
        </template>
        <template slot="top-right">
          <q-btn v-if="!bankAdmin"
                 icon="mdi-file-download-outline"
                 color="orange"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
<!--          <q-search icon="search"-->
<!--                    v-model="keyword"-->
<!--                    type="text"-->
<!--                    class="mr-10 w-200 dense"-->
<!--                    @keydown.13="delayedReload"-->
<!--                    @clear="delayedReload"-->
<!--                    clearable-->
<!--                    placeholder="Search...">-->
<!--          </q-search>-->
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12 load-status"
                      :class="statusClass(props.row['Status'].toLowerCase())">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { generateColumns } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import MexMemberMixin from '../../../mixins/mex/MexMemberMixin'

export default {
  name: 'spendr-rewards-report',
  mixins: [
    SpendrPageMixin,
    MexMemberMixin,
    ListPageMixin,
    FreezeColumnMixin
  ],
  data () {
    const columns = [
      'ID', 'Inviter ID', 'Inviter Name', 'Invitee ID',
      'Invitee Name', 'Status', 'Referred At', 'Finished At'
    ]
    return {
      title: 'Referral Report',
      requestUrl: `/admin/spendr/referral-report/list`,
      downloadUrl: `/admin/spendr/referral-report/export`,
      columns: generateColumns(columns),
      filterOptions: [
        {
          value: 'filter[r.id]',
          label: 'ID'
        },
        {
          value: 'filter[r.status=]',
          label: 'Status',
          options: [
            { label: 'Referred', value: 'referred' },
            { label: 'Finished', value: 'finished' }
          ]
        },
        {
          value: 'filter[f.id]',
          label: 'Inviter ID'
        },
        {
          value: 'filter[f.firstName=]',
          label: 'Inviter First Name'
        },
        {
          value: 'filter[f.lastName=]',
          label: 'Inviter Last Name'
        },
        {
          value: 'filter[t.id]',
          label: 'Invitee ID'
        },
        {
          value: 'filter[t.firstName=]',
          label: 'Invitee First Name'
        },
        {
          value: 'filter[t.lastName=]',
          label: 'Invitee Last Name'
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 0,
      autoLoad: true
    }
  },
  methods: {
    statusClass (status) {
      return {
        'referred': 'dark',
        'finished': 'positive'
      }[status] || status
    }
  }
}
</script>

<style lang="scss">
.spendr_rewards_report__index_page {
  .capitalize {
    text-transform: capitalize;
  }
}
.modal.minimized .modal-content {
  max-width: 600px;
}
.earn-type {
  min-width: 100px;
}
</style>
