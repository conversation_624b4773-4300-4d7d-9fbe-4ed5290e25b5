<template>
  <q-page id="spendr_running_balance_report__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id"
      >
        <template slot="top-left">
          <strong>{{ title }}</strong>
        </template>
        <template slot="top-right">
          <q-select v-model="balanceAdminType"
                    :options="balanceAdminTypeOptions"
                    @input="reload"
                    class="balance-type"
          ></q-select>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)"
        >
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]"
          >
            <div notranslate="">
              <template>{{ _.get(props.row, col.field) }}</template>
            </div>
          </q-td>
        </q-tr>
        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'spendr-running-balance-report',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-running-balance-report')
  ],
  components: {
  },
  data () {
    const columns = [
      'Transaction', 'Date & Time', 'Transaction ID',
      'Account ID', 'Amount', 'Running Balance'
    ]
    return {
      title: 'Running Balance Report',
      requestUrl: `/admin/spendr/members/balance-report/list`,
      columns: generateColumns(columns),
      keyword: '',
      freezeColumn: -1,
      freezeColumnRight: 0,
      autoLoad: true,
      balanceAdminType: 'spendr',
      balanceAdminTypeOptions: [
        { label: 'Partner', value: 'spendr' },
        { label: 'Bank', value: 'bank' }
      ]
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        balanceAdminType: this.balanceAdminType
      }
    }
  }
}
</script>
