<template>
  <q-page id="spendr_versions__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="text-faded mt-10">{{ subtitle }}</div>
    <div class="page-content">
      <div class="row gutter-form">
        <template v-for="(version, idx) in appVersions">
          <div class="col-sm-3 mt-20" :key="idx">
            <q-input float-label="OS" autocomplete="no" readonly
                     v-model="appVersions[idx]['os']"></q-input>
          </div>
          <div class="col-sm-3 offset-sm-1 mt-20" :key="idx">
            <q-input float-label="Build" autocomplete="no"
                     v-model="appVersions[idx]['build']"></q-input>
          </div>
          <div class="col-sm-3 offset-sm-1 mt-20" :key="idx">
            <q-input float-label="Version" autocomplete="no"
                     v-model="appVersions[idx]['version']"></q-input>
          </div>
          <div class="col-sm-12 mv-20" :key="idx">
            <q-input float-label="Description" autocomplete="no"
                     type="textarea"
                     v-model="appVersions[idx]['description']"></q-input>
          </div>
        </template>
      </div>
      <div class="row wp-100 mv-40">
        <q-btn label="Save"
               no-caps
               color="primary"
               @click="save" />
      </div>
    </div>
  </q-page>
</template>

<script>
import { notify, request } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'

export default {
  mixins: [
    SpendrPageMixin
  ],
  data () {
    return {
      title: 'App Version',
      subtitle: 'When you modify the version, it means that users who are lower than this version need to force the update version.' +
        ' The app update prompt information is consistent with the description content.',
      appVersions: []
    }
  },
  methods: {
    async getSetting () {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/setting`, 'get', {
        'type': 'App Versions'
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.appVersions = resp.data['App Versions']
      }
    },
    async save () {
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request('/admin/spendr/update-setting', 'post', {
        'type': 'App Versions',
        'versions': this.appVersions
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify('Save successfully')
        // this.appVersions = resp.data['App Versions']
      }
    }
  },
  created () {
    this.getSetting()
  }
}
</script>

<style lang="scss">
#spendr_versions__index_page {
  max-width: 1000px !important;
  .tipping-select {
    min-width: 100px;
  }
  .q-if-label-above {
    padding-bottom: 5px;
    font-size: 16px !important;
  }
}
</style>
