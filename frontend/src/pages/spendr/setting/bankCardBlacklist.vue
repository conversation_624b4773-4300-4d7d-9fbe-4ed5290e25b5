<template>
  <q-page id="spendr_bankcard_blacklist__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">
            <span class="va-m">{{ title }}</span>
          </div>
        </template>
        <template slot="top-right">
          <q-btn v-if="!bankAdmin && !spendrEmployee"
                 icon="mdi-account-group-outline"
                 color="positive"
                 label="Add Bankcard Blacklist"
                 @click="add"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-300 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search account/routing mask">
          </q-search>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="(isMasterAdmin() || agentAdmin || spendrCompliance) &&
            col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-item v-close-overlay
                        class="cursor-pointer"
                        @click.native="remove(props.row)">
                  <q-item-main>Remove</q-item-main>
                </q-item>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>
        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
    <BankcardBlacklistSetting/>
  </q-page>
</template>

<script>
import { EventHandlerMixin, generateColumns, notify, request } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import BankcardBlacklistSetting from './bankcardBlacklistSetting'

export default {
  name: 'spendr-bankcard-blacklist',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-bankcard-blacklist')
  ],
  components: {
    BankcardBlacklistSetting
  },
  data () {
    return {
      title: 'Bankcard Blacklist',
      requestUrl: `/admin/spendr/bank-card-blacklist/list`,
      columns: generateColumns(['ID', 'Account Mask', 'Routing Mask', 'Created At', 'Actions']),
      filterOptions: [],
      freezeColumn: -1,
      freezeColumnRight: 1,
      autoLoad: true
    }
  },
  methods: {
    add () {
      this.$root.$emit('show-spendr-bankcard-blacklist-dialog')
    },
    async remove (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure to remove the bank card?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/spendr/bank-card-blacklist/${row['ID']}/remove`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp)
          this.reload()
        }
      })
    }
  }
}
</script>

<style lang="scss">
#spendr_bankcard_blacklist__index_page {
  .capitalize {
    text-transform: capitalize;
  }
}
</style>
