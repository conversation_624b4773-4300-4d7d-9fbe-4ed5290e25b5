<template>
  <q-dialog
      class="spendr-bankcard-blacklist-dialog"
      v-model="visible"
      prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">Add Bankcard Blacklist</div>
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-input stack-label="Routing Number" autocomplete="no" placeholder="Routing Number"
                   :error="$v.entity['routingNumber'].$error"
                   @blur="$v.entity['routingNumber'].$touch"
                   v-model="entity['routingNumber']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input stack-label="Account Number" autocomplete="no" placeholder="Account Number"
                   v-model="entity['accountNumber']"></q-input>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Cancel" no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
        <q-btn label="Save"
               no-caps
               color="positive" class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, notifyForm, request } from '../../../common'
import { required } from 'vuelidate/lib/validators'

export default {
  name: 'spendr-bankcard-blacklist-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      entity: {
        'accountNumber': null,
        'routingNumber': null
      }
    }
  },
  validations: function () {
    return {
      entity: {
        'routingNumber': { required }
      }
    }
  },
  methods: {
    async show () {
      //
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/bank-card-blacklist/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.$root.$emit('reload-spendr-bankcard-blacklist')
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.spendr-bankcard-blacklist-dialog {
  .modal-content {
    width: 600px;
  }
  .modal-scroll {
    max-height: none;
  }
  .gutter-form>div {
    padding-top: 40px !important;
  }
  .q-if-label-above {
    padding-bottom: 5px;
    font-size: 16px !important;
  }
  .rewards-select {
    min-width: 100px;
  }
}
</style>
