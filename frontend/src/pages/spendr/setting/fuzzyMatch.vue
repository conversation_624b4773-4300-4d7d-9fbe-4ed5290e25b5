<template>
  <q-page id="spendr_fuzzy_match__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="text-faded mt-10">{{ subtitle }}</div>
    <div class="page-content">
      <div class="row gutter-form">
        <div class="col-sm-4 mt-50">
          <q-input stack-label="Fuzzy Match Score"
                   autocomplete="no"
                   type="number"
                   :min="0.5"
                   :max="1"
                   v-model="config.score"></q-input>
        </div>
        <div class="col-sm-4 offset-sm-2 mt-50">
          <q-input stack-label="Last Name Min Treshold"
                   autocomplete="no"
                   type="number"
                   :min="0.5"
                   :max="1"
                   v-model="config.lastNameMinTreshold"></q-input>
        </div>
      </div>
      <div class="row wp-100 mv-40">
        <q-btn label="Save"
               no-caps
               color="primary"
               @click="save" />
      </div>
    </div>
  </q-page>
</template>

<script>
import { notify, request } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'

export default {
  mixins: [
    SpendrPageMixin
  ],
  data () {
    return {
      title: 'Fuzzy Match',
      subtitle: 'The name fuzzy match averageScore needs to be higher than the modified score to pass verification.' +
          'And if the match score is less than the last name min treshold, the success of match result will be false',
      config: {
        score: 0.5,
        lastNameMinTreshold: 0.6
      }
    }
  },
  methods: {
    async getSetting () {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/setting`, 'get', {
        'type': 'Fuzzy Match'
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.config = resp.data['Fuzzy Match']
      }
    },
    async save () {
      this.$q.loading.show({
        message: 'Saving...'
      })
      const resp = await request('/admin/spendr/update-setting', 'post', {
        'type': 'Fuzzy Match',
        'config': this.config
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify('Save successfully')
      }
    }
  },
  created () {
    this.getSetting()
  }
}
</script>

<style lang="scss">
#spendr_fuzzy_match__index_page {
  max-width: 1000px !important;
  .q-if-label-above {
    padding-bottom: 5px;
    font-size: 16px !important;
  }
}
</style>
