<template>
  <div id="spendr-setting">
    <div class="logo">
      <img :src="logo"
           alt="">
    </div>
    <div class="steppers">
      <div class="stepper"
           v-for="s in steps"
           :class="{active: s === step}"
           @click="step = s"
           :key="s">
        <div class="text">{{ s }}</div>
        <div class="indicator"></div>
      </div>
    </div>
    <q-card v-show="step === 'Plaid Setting'"
            class="setting-card q-card-lg shadow-lg">
      <q-card-title>
        <div class="font-18 mb-2">{{ step }}</div>
        <div class="font-12 normal text-dark">
          Normally, this needn't to change.
          If Plaid add new ip, we should sync to avoid webhook error.
          And if you want to test some function, you can change Plaid Env in Staging.
        </div>
      </q-card-title>
      <q-card-main>
        <div class="row gutter-form mt--10 mb-5">
          <template v-if="staging">
            <div class="col-sm-6">
              <q-select float-label="Plaid Env"
                        autocomplete="no"
                        :options="envs"
                        v-model="plaidEnv">
              </q-select>
            </div>
            <div class="col-sm-6">
              <q-btn label="Change Env" outline
                     no-caps class="wp-100"
                     color="primary"
                     @click="saveEnv" />
            </div>
          </template>
          <template v-for="(ip, idx) in plaidIps">
            <div :key="ip" class="col-sm-6">
              <q-input float-label="Plaid IP" autocomplete="no"
                       v-model="plaidIps[idx]"></q-input>
            </div>
            <div :key="idx" class="col-sm-6">
              <q-btn label="Remove IP" outline
                     no-caps class="wp-100"
                     color="red"
                     @click="plaidIps.splice(idx, 1)" />
            </div>
          </template>
          <div class="col-sm-6">
            <q-input float-label="Plaid IP" autocomplete="no"
                     v-model="addIp"></q-input>
          </div>
          <div class="col-sm-6">
            <q-btn label="Add IP" outline
                   no-caps class="wp-100"
                   color="primary"
                   @click="addAction" />
          </div>
        </div>
        <q-btn label="Save Plaid IPs"
               no-caps class="mt-20 wp-100"
               color="positive"
               @click="saveIps" />
      </q-card-main>
    </q-card>
    <q-card v-show="step === 'App Version'"
            class="setting-card q-card-lg shadow-lg">
      <q-card-title>
        <div class="font-18 mb-2">{{ step }}</div>
        <div class="font-12 normal text-dark">
          When you modify the version, it means that users who are lower than this version need to force the update version.
          The app update prompt information is consistent with the description content.
        </div>
      </q-card-title>
      <q-card-main>
        <div class="row gutter-form mt--10 mb-5">
          <template v-for="(version, idx) in appVersions">
            <div class="col-sm-6" :key="idx">
              <q-input float-label="OS" autocomplete="no" readonly
                       v-model="appVersions[idx]['os']"></q-input>
            </div>
            <div class="col-sm-6" :key="idx">
              <q-input float-label="Build" autocomplete="no"
                       v-model="appVersions[idx]['build']"></q-input>
            </div>
            <div class="col-sm-6" :key="idx">
              <q-input float-label="Version" autocomplete="no"
                       v-model="appVersions[idx]['version']"></q-input>
            </div>
            <div class="col-sm-12 mb-20" :key="idx">
              <q-input float-label="Description" autocomplete="no"
                       type="textarea"
                       v-model="appVersions[idx]['description']"></q-input>
            </div>
          </template>
        </div>
        <q-btn label="Save Versions"
               no-caps class="mt-20 wp-100"
               color="positive"
               @click="saveVersions" />
      </q-card-main>
    </q-card>
    <q-card v-show="step === 'Earn Rewards'"
            class="setting-card q-card-lg shadow-lg">
      <q-card-title>
        <div class="font-18 mb-2">{{ step }}</div>
        <div class="font-12 normal text-dark">
          Bank Link Reward means when consumers firstly link bank card, they will earn the reward amount(There are quotas).<br/>
          First $100 Spend Reward means when consumers' total spend amount over $100, they will earn the reward amount.<br/>
          Spend Reward means when consumers' total spend amount monthly over the total amount, they will earn the reward amount.
        </div>
      </q-card-title>
      <q-card-main>
        <div class="row gutter-form mt--10 mb-5">
          <div class="font-16 col-sm-12 item-title">Bank Link Reward</div>
          <div class="col-sm-6">
            <q-input float-label="Reward Amount" autocomplete="no"
                     type="number" placeholder="Bank link reward amount" prefix="$"
                     v-model="rewards['Bank Link']['Amount']"></q-input>
          </div>
          <div class="col-sm-6">
            <q-input float-label="Reward Count Limit" autocomplete="no"
                     type="number" placeholder=""
                     v-model="rewards['Bank Link']['Count Limit']"></q-input>
          </div>
          <div class="font-16 col-sm-12 item-title">First $100 Spend Reward</div>
          <div class="col-sm-6">
            <q-input float-label="Reward Amount" autocomplete="no"
                     type="number" placeholder="First $100 Spend reward amount" prefix="$"
                     v-model="rewards['First $100 Spend']['Amount']"></q-input>
          </div>
          <div class="col-sm-6">
            <q-input float-label="Total Amount" autocomplete="no" readonly
                     type="number" placeholder="Spend Over Total Amount" prefix="$"
                     v-model="rewards['First $100 Spend']['Total Amount']"></q-input>
          </div>
          <div class="font-16 col-sm-12 item-title">Spend Reward</div>
          <div class="col-sm-6">
            <q-input float-label="Reward Amount" autocomplete="no"
                     type="number" placeholder="Spend reward amount" prefix="$"
                     v-model="rewards['Spend Reward']['Amount']"></q-input>
          </div>
          <div class="col-sm-6">
            <q-input float-label="Total Amount" autocomplete="no"
                     type="number" placeholder="Spend Total Amount monthly" prefix="$"
                     v-model="rewards['Spend Reward']['Total Amount']"></q-input>
          </div>
        </div>
        <q-btn label="Save Rewards"
               no-caps class="mt-20 wp-100"
               color="positive"
               @click="saveRewards" />
      </q-card-main>
    </q-card>
  </div>
</template>

<script>
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import { request, isStaging, notify } from '../../../common'

export default {
  name: 'SpendrSetting',
  mixins: [
    SpendrPageMixin
  ],
  data () {
    return {
      plaidEnv: '',
      envs: [
        { label: 'sandbox', value: 'sandbox' },
        { label: 'development', value: 'development' }
      ],
      addIp: '',
      plaidIps: [],
      appVersions: [],
      rewards: {
        'Bank Link': {},
        'First $100 Spend': {},
        'Spend Reward': {}
      },
      step: 'Plaid Setting',
      steps: ['Plaid Setting', 'App Version', 'Earn Rewards']
    }
  },
  computed: {
    staging () {
      return isStaging()
    }
  },
  methods: {
    async getSettings () {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/setting`)
      this.$q.loading.hide()
      if (resp.success) {
        this.plaidEnv = resp.data['Plaid Env']
        this.plaidIps = resp.data['Plaid Ips']
        this.appVersions = resp.data['App Versions']
        this.rewards = resp.data['Rewards']
      }
    },
    async saveEnv () {
      this.$q.loading.show({
        message: 'Saving...'
      })
      const resp = await request('/admin/spendr/update-setting', 'post', {
        'type': 'Plaid Env',
        'plaid_env': this.plaidEnv
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify('Save successfully')
      }
    },
    addAction () {
      this.plaidIps.push(this.addIp)
      this.addIp = ''
    },
    async saveIps () {
      this.$q.loading.show({
        message: 'Saving...'
      })
      const resp = await request('/admin/spendr/update-setting', 'post', {
        'type': 'Plaid Ips',
        'ips': this.plaidIps
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify('Save successfully')
      }
    },
    async saveVersions () {
      this.$q.loading.show({
        message: 'Saving...'
      })
      const resp = await request('/admin/spendr/update-setting', 'post', {
        'type': 'App Versions',
        'versions': this.appVersions
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify('Save successfully')
      }
    },
    async saveRewards () {
      const decimal = /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/
      if ((this.rewards['Bank Link']['Amount'] && !decimal.test(this.rewards['Bank Link']['Amount'])) ||
          (this.rewards['First $100 Spend']['Amount'] && !decimal.test(this.rewards['First $100 Spend']['Amount'])) ||
          (this.rewards['First $100 Spend']['Amount'] && !decimal.test(this.rewards['First $100 Spend']['Total Amount'])) ||
          (this.rewards['Spend Reward']['Amount'] && !decimal.test(this.rewards['Spend Reward']['Amount'])) ||
          (this.rewards['Spend Reward']['Amount'] && !decimal.test(this.rewards['Spend Reward']['Total Amount']))
      ) {
        notify('Invalid amount', 'negative')
        return
      }
      this.$q.loading.show({
        message: 'Saving...'
      })
      const resp = await request('/admin/spendr/update-setting', 'post', {
        'type': 'Rewards',
        'value': this.rewards
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify('Save successfully')
      }
    }
  },
  mounted () {
    this.getSettings()
  }
}
</script>

<style lang="scss">
  #spendr-setting {
    margin: 80px auto;
    padding-bottom: 40px;
    text-align: center;
    .steppers {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      margin: 0 auto 20px;

      .stepper {
        display: flex;
        flex-direction: column;
        padding: 0 10px;
        margin-bottom: 10px;
        cursor: pointer;

        .text {
          color: #999;
          font-size: 13px;
        }

        .indicator {
          margin-top: 4px;
          width: 100%;
          height: 6px;
          border-radius: 6px;
          background: var(--span-color-primary);
          opacity: 0.25;
        }
        &.active {
          .text {
            color: #333;
          }
          .indicator {
            opacity: 1;
          }
        }
        &:hover {
          .text {
            color: #555;
          }
          .indicator {
            opacity: 0.7;
          }
        }
      }
    }
    .setting-card {
      max-width: 500px;
    }
    > div {
      margin-left: auto;
      margin-right: auto;
    }
    .item-title {
      text-align: left;
    }
  }
</style>
