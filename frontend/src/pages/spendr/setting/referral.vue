<template>
  <q-page id="spendr_referral__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="font-22 text-faded mt-10">{{ subtitle }}</div>
    <div class="page-content">
      <div class="row gutter-form">
        <div class="col-sm-12 font-18">Give Config</div>
        <div class="col-sm-4 mt-20">
          <q-input stack-label="Reward Amount" autocomplete="no"
                   type="number" placeholder="Reward amount" prefix="$"
                   :error="$v.entity['Give Reward'].$error"
                   @blur="$v.entity['Give Reward'].$touch"
                   v-model="entity['Give Reward']"></q-input>
        </div>
        <div class="col-sm-4 offset-sm-2 mt-20">
          <q-input stack-label="Spent Amount" autocomplete="no"
                   type="number" placeholder="Spent amount" prefix="$"
                   :error="$v.entity['Spent Amount'].$error"
                   @blur="$v.entity['Spent Amount'].$touch"
                   v-model="entity['Spent Amount']"></q-input>
        </div>
        <div class="col-sm-12 font-18">Get Config</div>
        <div class="col-sm-4 mt-20">
          <q-input stack-label="Reward Amount" autocomplete="no"
                   type="number" placeholder="reward amount" prefix="$"
                   :error="$v.entity['Get Reward'].$error"
                   @blur="$v.entity['Get Reward'].$touch"
                   v-model="entity['Get Reward']"></q-input>
        </div>
        <div class="col-sm-4 offset-sm-2 mt-20">
          <q-input stack-label="Max Number of Referrals" autocomplete="no"
                   type="number" placeholder="Max number of referrals"
                   :error="$v.entity['Max Referrals'].$error"
                   @blur="$v.entity['Max Referrals'].$touch"
                   v-model="entity['Max Referrals']"></q-input>
        </div>
      </div>
      <div class="row wp-100 mt-40">
        <q-btn label="Save"
               no-caps
               color="primary"
               :disable="spendrAccountant"
               @click="save" />
      </div>
    </div>
  </q-page>
</template>

<script>
import { helpers, minValue, maxValue, integer } from 'vuelidate/lib/validators'
import { notify, notifyForm, request } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import _ from 'lodash'

export default {
  mixins: [
    SpendrPageMixin
  ],
  data () {
    return {
      title: 'User Referral Config',
      subtitle: '',
      entity: {
        'ID': 0,
        'Give Reward': null,
        'Spent Amount': null,
        'Get Reward': null,
        'Max Referrals': null
      }
    }
  },
  validations: function () {
    const amountValidator = helpers.regex('Up to two decimal places', /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/)
    return {
      entity: {
        'Give Reward': {
          required: true,
          minValue: minValue(0),
          maxValue: maxValue(100),
          amountValidator
        },
        'Spent Amount': {
          required: true,
          minValue: minValue(0),
          maxValue: maxValue(1000),
          amountValidator
        },
        'Get Reward': {
          required: true,
          minValue: minValue(0),
          maxValue: maxValue(100),
          amountValidator
        },
        'Max Referrals': {
          integer,
          minValue: minValue(0),
          maxValue: maxValue(10000)
        }
      }
    }
  },
  methods: {
    async getSetting () {
      this.$q.loading.show()
      const resp = await request('/admin/spendr/referral-setting')
      this.$q.loading.hide()
      if (resp.success && resp.data) {
        this.entity = _.assignIn({}, this.entity, resp.data)
      }
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request('/admin/spendr/referral-setting', 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.entity = _.assignIn({}, this.entity, resp.data)
      }
    }
  },
  created () {
    this.getSetting()
  }
}
</script>

<style lang="scss">
  #spendr_referral__index_page {
    padding: 0 100px;
    .tipping-select {
      min-width: 100px;
    }
    .q-if-label-above {
      padding-bottom: 5px;
      font-size: 16px !important;
    }
  }
</style>
