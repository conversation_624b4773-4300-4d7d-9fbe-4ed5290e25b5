<template>
  <q-dialog
      class="spendr-restrict-setting-dialog"
      v-model="visible"
      prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">Add Restrict State</div>
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-select :disable="entity['ID']" stack-label="Restrict State" placeholder="Please select the Restrict State"
                    v-model="state"
                    :options="states"
                    class="rewards-select"></q-select>
        </div>
        <div class="col-sm-12">
          <q-select stack-label="Restricted Features" placeholder="Please select the Restricted Features"
                    multiple
                    chips
                    v-model="features"
                    :options="featureList"
                    class="rewards-select"></q-select>
        </div>
        <div class="col-sm-12">
          <q-select stack-label="Load Type" placeholder="Please select the Load Type"
                    v-model="loadType"
                    :options="loadTypes"
                    class="rewards-select"></q-select>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Cancel" no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
        <q-btn label="Save"
               no-caps
               color="positive" class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, request } from '../../../common'

export default {
  name: 'spendr-restrict-setting-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        'ID': 0,
        'State ID': null,
        'Features': [],
        'Load Type': null
      },
      state: null,
      features: [],
      loadType: null,
      states: [],
      featureList: [],
      loadTypes: []
    }
  },
  methods: {
    async show () {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/restrict/filters`, 'get', {
        remain_states: true
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.states = resp.data.states
        this.featureList = resp.data.features
        this.loadTypes = resp.data.loadTypes
        if (this.entity['ID']) {
          this.state = this.entity['State ID']
          const type = this.loadTypes.find((item) => item.label === this.entity['Load Type'])
          this.loadType = type ? type.value : null
          this.features = this.entity['Features']
        } else {
          this.features = this.featureList.map((item) => item.value)
          this.loadType = this.loadTypes[0].value
        }
      }
    },
    async save () {
      if (!this.state) {
        return notify('Please select the state', 'negative')
      }
      if (!this.features) {
        return notify('Please select the restricted features', 'negative')
      }
      if (!this.loadType) {
        return notify('Please select the load type', 'negative')
      }
      if (
        this.state === this.entity['State ID'] &&
        this.loadType === this.entity['Load Type'] &&
        this.features === this.entity['Features']
      ) {
        return notify('You don\'t change any parameter.', 'negative')
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/restrict/save`, 'post', {
        id: this.entity['ID'],
        state: this.state,
        features: this.features,
        loadType: this.loadType
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.state = null
        notify(resp.message)
        this.$root.$emit('reload-spendr-restrict')
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.spendr-restrict-setting-dialog {
  .modal-content {
    width: 600px;
  }
  .modal-scroll {
    max-height: none;
  }
  .gutter-form>div {
    padding-top: 40px !important;
  }
  .q-if-label-above {
    padding-bottom: 5px;
    font-size: 16px !important;
  }
  .rewards-select {
    min-width: 100px;
  }
}
</style>
