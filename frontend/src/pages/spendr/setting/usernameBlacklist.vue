<template>
  <q-page id="spendr_username_blacklist__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <q-card id="username_blacklist_card">
        <q-card-title>
          <div class="row gutter-form">
            <div class="col-sm-6">
              <q-search icon="search"
                        v-model="keyword"
                        type="text"
                        class="mr-10 dense"
                        style="width: 100%; height: 42px"
                        clearable
                        placeholder="Search name">
              </q-search>
            </div>
            <div class="col-sm-6" style="text-align: right">
              <q-btn label="Add to blacklist" outline
                     no-caps class="wp-200 mr-10"
                     color="primary"
                     @click="addAction" />
              <q-btn label="Save List"
                     no-caps
                     color="primary"
                     :disable="spendrAccountant"
                     @click="save" />
            </div>
          </div>
        </q-card-title>
        <q-card-main>
          <div class="base-item-list">
            <template v-for="(r, i) in list">
              <div v-show="!keyword || r.includes(keyword)" class="base-item" :key="i">
                <div class="desc-item">{{ r }}</div>
                <q-btn flat round size="xs" @click="list.splice(i, 1)" icon="mdi-close"></q-btn>
              </div>
            </template>
          </div>
        </q-card-main>
      </q-card>
    </div>
  </q-page>
</template>

<script>
import { notify, request } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'

export default {
  mixins: [
    SpendrPageMixin
  ],
  data () {
    return {
      title: 'Username Blacklist',
      keyword: null,
      list: []
    }
  },
  methods: {
    addAction () {
      this.$q.dialog({
        title: 'Add username',
        message: 'Please input the username',
        cancel: true,
        prompt: {
          model: ''
        }
      }).then(async input => {
        if (!input) return notify('Please input the full name', 'negative')
        this.list.push(input)
      }).catch(() => {})
    },
    async getSetting () {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/setting`, 'get', {
        'type': 'Username Blacklist'
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.list = resp.data['Username Blacklist']
      }
    },
    async save () {
      this.$q.loading.show({
        message: 'Saving...'
      })
      const resp = await request('/admin/spendr/update-setting', 'post', {
        'type': 'Username Blacklist',
        'config': this.list
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify('Save successfully')
      }
    }
  },
  created () {
    this.getSetting()
  }
}
</script>

<style lang="scss">
  #spendr_username_blacklist__index_page {
    padding: 0 100px;
    .btn-small {
      padding: 5px 8px;
    }
    .base-item-list {
      max-height: 800px;
      overflow-y: scroll;
    }
    .base-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #cccccc;
      padding: 10px 5px;
      &:nth-last-child(1) {
        border-bottom: none;
      }
    }
  }
</style>
