<template>
  <q-page id="spendr_tern_fee_report__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :iso-week="true"
                         :default="dateRange"
                         :ranges="datesWithDaily"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="description">Total</div>
                  <div class="value">{{ quick.total || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="description">Total Amount</div>
                  <div class="value">{{ quick.totalAmount | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="description">Total Transaction Fee</div>
                  <div class="value">{{ quick.totalTxnFee | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="description">Total Load Fee</div>
                  <div class="value">{{ quick.totalLoadFee | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="description">Total Monthly On File Fee</div>
                  <div class="value">{{ quick.totalMonthlyOnFileFee | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="description">Total Withdraw Fee</div>
                  <div class="value">{{ quick.totalWithdrawFee | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id"
      >
        <template slot="top-left">
          <strong>{{ title }}</strong>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)"
        >
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]"
          >
            <div notranslate="">
              <template>{{ _.get(props.row, col.field) }}</template>
            </div>
          </q-td>
        </q-tr>
        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'spendr-tern-fee-report',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-tern-fee-report')
  ],
  components: {
  },
  data () {
    const columns = [
      'Transaction', 'Date & Time', 'Transaction ID', 'Amount'
    ]
    return {
      title: 'Tern Fee Report',
      requestUrl: `/admin/spendr/fee-report/tern`,
      columns: generateColumns(columns),
      keyword: '',
      freezeColumn: -1,
      freezeColumnRight: 0,
      autoLoad: true
    }
  },
  methods: {}
}
</script>
