<template>
  <q-page id="spendr_tipping__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="font-22 text-faded mt-10">{{ subtitle }}</div>
    <div class="font-16 text-warning mv-10" v-if="disable">The global setting is disabled or no permission, you can't edit this now.</div>
    <div class="font-16 text-warning mv-10" v-if="merchantAdmin">Select none location means tipping is for all locations.</div>
    <div class="page-content">
      <div class="row gutter-form">
        <div class="col-sm-4 mt-20">
          <q-select stack-label="Clerk Tipping"
                    :disable="spendrAccountant || disable"
                    placeholder="Enabled/Disabled"
                    v-model="entity['Clerk Tipping']"
                    :options="tipping_enables"
                    class="tipping-select"></q-select>
        </div>
        <div class="col-sm-4 offset-sm-2 mt-20">
          <q-select v-if="!merchantAdmin" stack-label="Charge Fees on Tips"
                    :disable="spendrAccountant"
                    placeholder="Yes/No"
                    :error="$v.entity['Charge Fees'].$error"
                    @blur="$v.entity['Charge Fees'].$touch"
                    v-model="entity['Charge Fees']"
                    :options="charge_fees"
                    class="tipping-select"></q-select>
          <q-select v-else stack-label="Enable Locations"
                    :disable="spendrAccountant"
                    autocomplete="no"
                    :options="all_locations"
                    filter
                    autofocus-filter
                    multiple
                    v-model="entity['Locations']"></q-select>
        </div>
        <div class="col-sm-4 mt-20">
          <q-select stack-label="Payout Type"
                    :disable="spendrAccountant || disable"
                    :error="$v.entity['Payout Type'].$error"
                    @blur="$v.entity['Payout Type'].$touch"
                    v-model="entity['Payout Type']"
                    :options="payout_types"
                    class="tipping-select"></q-select>
        </div>
        <div class="col-sm-4 offset-sm-2 mt-20">
          <q-select stack-label="Tipping Type"
                    :disable="spendrAccountant || disable"
                    placeholder="Per Clerk/Combined"
                    :error="$v.entity['Tipping Type'].$error"
                    @blur="$v.entity['Tipping Type'].$touch"
                    v-model="entity['Tipping Type']"
                    :options="tipping_types"
                    class="tipping-select"></q-select>
        </div>
        <div class="col-sm-4 mt-20">
          <q-select stack-label="Suggested Tips Format"
                    :disable="spendrAccountant || disable"
                    placeholder="Dollars/Percentages"
                    :error="$v.entity['Tips Format'].$error"
                    @blur="$v.entity['Tips Format'].$touch"
                    v-model="entity['Tips Format']"
                    :options="tips_formats"
                    class="tipping-select"></q-select>
        </div>
        <div class="col-sm-4 offset-sm-2 mt-20">
          <q-input stack-label="Suggested Tip Amounts" autocomplete="no"
                   :disable="spendrAccountant || disable"
                   type="number"
                   placeholder="$ / %"
                   :prefix="entity['Tips Format'] === 'Dollars' ? '$' : ''"
                   :suffix="entity['Tips Format'] === 'Percentages' ? '%' : ''"
                   v-model="entity['Tip Amounts'][0]"></q-input>
          <q-input class="mt-10" autocomplete="no"
                   :disable="spendrAccountant || disable"
                   type="number"
                   placeholder="$ / %"
                   :prefix="entity['Tips Format'] === 'Dollars' ? '$' : ''"
                   :suffix="entity['Tips Format'] === 'Percentages' ? '%' : ''"
                   v-model="entity['Tip Amounts'][1]"></q-input>
          <q-input class="mt-10" autocomplete="no"
                   :disable="spendrAccountant || disable"
                   type="number"
                   placeholder="$ / %"
                   :prefix="entity['Tips Format'] === 'Dollars' ? '$' : ''"
                   :suffix="entity['Tips Format'] === 'Percentages' ? '%' : ''"
                   v-model="entity['Tip Amounts'][2]"></q-input>
        </div>
      </div>
      <div class="row wp-100 mt-40">
        <q-btn label="Save"
               no-caps
               color="primary"
               :v-if="!spendrAccountant && !disable"
               @click="save" />
      </div>
    </div>
  </q-page>
</template>

<script>
import { helpers, minValue, maxValue, requiredIf } from 'vuelidate/lib/validators'
import { notify, notifyForm, request } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import _ from 'lodash'

export default {
  mixins: [
    SpendrPageMixin
  ],
  data () {
    return {
      title: 'Tipping Structure Wizard',
      subtitle: 'DETAILS',
      entity: {
        'Clerk Tipping': false,
        'Charge Fees': null,
        'Locations': [],
        'Payout Type': 'Cash',
        'Tipping Type': null,
        'Tips Format': null,
        'Tip Amounts': [ null, null, null ]
      },
      disable: true,
      tipping_enables: [
        { label: 'Enabled', value: true },
        { label: 'Disabled', value: false }
      ],
      charge_fees: [
        { label: 'Yes', value: true },
        { label: 'No', value: false }
      ],
      payout_types: [
        { label: 'Cash', value: 'Cash' }
      ],
      tipping_types: [
        { label: 'Per Clerk', value: 'Per Clerk' },
        { label: 'Combined', value: 'Combined' }
      ],
      tips_formats: [
        { label: 'Dollars', value: 'Dollars' },
        { label: 'Percentages', value: 'Percentages' }
      ],
      all_locations: []
    }
  },
  computed: {
    tippingEnable () {
      return !!this.entity['Clerk Tipping']
    }
  },
  validations: function () {
    const amountValidator = helpers.regex('Up to two decimal places', /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/)
    return {
      entity: {
        'Charge Fees': {
          required: requiredIf(function () {
            return this.tippingEnable
          })
        },
        'Payout Type': {
          required: requiredIf(function () {
            return this.tippingEnable
          })
        },
        'Tipping Type': {
          required: requiredIf(function () {
            return this.tippingEnable
          })
        },
        'Tips Format': {
          required: requiredIf(function () {
            return this.tippingEnable
          })
        },
        'Tip Amounts': {
          $each: {
            required: requiredIf(function () {
              return this.tippingEnable
            }),
            minValue: minValue(0.01),
            maxValue: maxValue(1000),
            amountValidator
          }
        }
      }
    }
  },
  methods: {
    async getSetting () {
      this.$q.loading.show()
      const resp = await request('/admin/merchant/tipping')
      this.$q.loading.hide()
      if (resp.success) {
        this.payout_types = resp.data.payout_types
        this.tipping_types = resp.data.tipping_types
        this.tips_formats = resp.data.tips_formats
        this.all_locations = resp.data.all_locations
        this.entity = _.assignIn({}, this.entity, resp.data.entity)
        this.disable = this.merchantAdmin ? resp.data.disable : false
      }
    },
    async save () {
      for (const i in this.entity['Tip Amounts']) {
        const amount = this.entity['Tip Amounts'][i]
        if (this.entity['Clerk Tipping'] && !amount) {
          notifyForm('Please input the tip amounts')
          return
        } else if (amount <= 0 || amount > 1000) {
          notifyForm('Please input the tip amounts between $0.01 and $1000')
          return
        } else if (!/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/.test(amount)) {
          notifyForm('Tip amounts format error')
          return
        }
      }
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request('/admin/merchant/tipping', 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.entity = _.assignIn({}, this.entity, resp.data)
      }
    }
  },
  created () {
    this.getSetting()
  }
}
</script>

<style lang="scss">
  #spendr_tipping__index_page {
    padding: 0 100px;
    .tipping-select {
      min-width: 100px;
    }
    .q-if-label-above {
      padding-bottom: 5px;
      font-size: 16px !important;
    }
  }
</style>
