<template>
  <q-page id="spendr_tip_report__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :iso-week="true"
                         :default="dateRange"
                         :ranges="datesWithDaily"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total # of Tip</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-multiple"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalTips || 0 | moneyFormat }}</div>
                  <div class="description">Total $ of Tip</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-multiple"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.spendrFee || 0 | moneyFormat }}</div>
                  <div class="description">Total $ of Spendr Fee</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }}</strong>
        </template>
        <template slot="top-right">
          <q-btn v-if="!bankAdmin"
                 icon="mdi-file-download-outline"
                 color="orange"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <!--          <q-search icon="search"-->
          <!--                    v-model="keyword"-->
          <!--                    type="text"-->
          <!--                    class="mr-10 w-200 dense"-->
          <!--                    @keydown.13="delayedReload"-->
          <!--                    @clear="delayedReload"-->
          <!--                    clearable-->
          <!--                    placeholder="Search...">-->
          <!--          </q-search>-->
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12 load-status"
                      :class="statusClass(props.row['Status'].toLowerCase())">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { generateColumns } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import MexMemberMixin from '../../../mixins/mex/MexMemberMixin'

export default {
  name: 'spendr-tip-report',
  mixins: [
    SpendrPageMixin,
    MexMemberMixin,
    ListPageMixin,
    FreezeColumnMixin
  ],
  data () {
    const columns = [
      'ID', 'Transaction ID', 'Consumer ID', 'Consumer Name',
      'Tip Amount', 'Tip Type', 'Tip Format', 'Spendr Fee',
      'Merchant Name', 'Location Name', 'Clerk Name',
      'Date & Time', 'Status'
    ]
    return {
      title: 'Tip Report',
      requestUrl: `/admin/spendr/merchant/tip-report/list`,
      downloadUrl: `/admin/spendr/merchant/tip-report/export`,
      filtersUrl: `/admin/spendr/merchant/tip-report/filters`,
      columns: generateColumns(columns),
      filterOptions: [
        {
          value: 'filter[st.id]',
          label: 'ID'
        },
        {
          value: 'filter[c.id]',
          label: 'Member ID'
        }, {
          value: 'filter[c.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[c.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[c.email=]',
          label: 'Email'
        },
        {
          value: 'filter[e.id=]',
          label: 'Clerk ID'
        },
        {
          value: 'filter[t.type=]',
          label: 'Tip Type',
          options: [
            { label: 'Per Clerk', value: 'Per Clerk' },
            { label: 'Combined', value: 'Combined' }
          ]
        },
        {
          value: 'filter[l.id=]',
          label: 'Location Name',
          options: [],
          source: 'locations'
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 1,
      autoLoad: true
    }
  },
  methods: {
    statusClass (status) {
      return {
        'created': 'blue',
        'paid': 'positive',
        'refunded': 'orange'
      }[status] || status
    }
  },
  created () {
    if (this.isMasterAdmin() || this.isSpendrAdmin()) {
      this.filterOptions.push({
        value: 'filter[m.id=]',
        label: 'Merchant Name',
        options: [],
        source: 'merchants'
      })
    }
  }
}
</script>

<style lang="scss">
.spendr_tip_report__index_page {
  .capitalize {
    text-transform: capitalize;
  }
}
.modal.minimized .modal-content {
  max-width: 600px;
}
.earn-type {
  min-width: 100px;
}
</style>
