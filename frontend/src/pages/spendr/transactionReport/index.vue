<template>
  <q-page id="spendr_transaction_report__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :iso-week="true"
                         :default="dateRange"
                         :ranges="datesWithDaily"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total Transactions</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6" v-if="!merchantAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.spendrRevenue || 0 }}</div>
                  <div class="description">Spendr Revenue</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6" v-if="isMasterAdmin()">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.ternFee || 0 }}</div>
                  <div class="description">Tern Fee</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.spendrFee || 0 }}</div>
                  <div class="description">Spendr Fee</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id"
      >
        <template slot="top-left">
          <strong>{{ title }}</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
<!--          <q-search icon="search"-->
<!--                    v-model="keyword"-->
<!--                    type="text"-->
<!--                    class="mr-10 w-200 dense"-->
<!--                    @keydown.13="delayedReload"-->
<!--                    @clear="delayedReload"-->
<!--                    clearable-->
<!--                    placeholder="Search...">-->
<!--          </q-search>-->
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)"
        >
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]"
          >
            <div notranslate="">
              <template v-if="col.field === 'Status'">
                <q-chip class="font-12"
                        :class="statusClass(props.row['Status'])">
                  {{ props.row['Status'] }}
                </q-chip>
              </template>
              <template v-else-if="col.field === 'Type'">
                <div class="font-12 uppercase">
                  {{ props.row['Type'] }}
                </div>
              </template>
              <template v-else-if="col.field === 'Ucb - Txn Amount'">
                <div>Consumer: {{ props.row['Ucb - Txn Amount']['actual']['consumer']['amountText'] }}</div>
                <div>Merchant: {{ props.row['Ucb - Txn Amount']['actual']['merchant']['amountText'] }}</div>
              </template>
              <template v-else-if="col.field === 'Ucb - Spendr Fee'">
                <div>Amount: {{props.row['Ucb - Spendr Fee']['should'] | moneyFormat }}</div>
                <div>Merchant: {{ props.row['Ucb - Spendr Fee']['actual']['merchant']['amountText'] }}</div>
                <div>Partner: {{ props.row['Ucb - Spendr Fee']['actual']['partner']['amountText'] }}</div>
              </template>
              <template v-else-if="col.field === 'Ucb - Tern Fee'">
                <div>Amount: {{props.row['Ucb - Tern Fee']['should'] | moneyFormat }}</div>
                <div>Partner: {{ props.row['Ucb - Tern Fee']['actual']['partner']['amountText'] }}</div>
                <div>Tern: {{ props.row['Ucb - Tern Fee']['actual']['tern']['amountText'] }}</div>
              </template>
              <template v-else-if="col.field === 'Ucb - Refund Fee'">
                <div>Amount: {{props.row['Ucb - Refund Fee']['should'] | moneyFormat }}</div>
                <div>Partner: {{ props.row['Ucb - Refund Fee']['actual']['partner']['amountText'] }}</div>
                <div>Merchant: {{ props.row['Ucb - Refund Fee']['actual']['merchant']['amountText'] }}</div>
              </template>
              <template v-else>{{ _.get(props.row, col.field) }}</template>
            </div>
          </q-td>
        </q-tr>
        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'spendr-transaction-report',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-transaction-report')
  ],
  components: {
  },
  data () {
    const columns = [
      'Transaction ID', 'Date & Time', 'First Name', 'Last Name', 'Consumer ID', 'Payment Amount', 'Ucb - Txn Amount',
      'Spendr Fee', 'Ucb - Spendr Fee', 'Estimated Tern Fee', 'Ucb - Tern Fee', 'Refund Fee', 'Ucb - Refund Fee',
      'Merchant Balance Before', 'Merchant Balance After', 'Merchant Name', 'Location Name', 'Clerk Name',
      'Terminal', 'Refund From ID', 'Transaction Type', 'Status'
    ]
    return {
      title: 'Transaction Report',
      requestUrl: `/admin/spendr/transaction-report/list`,
      downloadUrl: `/admin/spendr/transaction-report/export`,
      columns: generateColumns(columns),
      filterOptions: [
        {
          value: 'filter[t.id=]',
          label: 'Transaction ID'
        },
        {
          value: 'filter[c.id]',
          label: 'Consumer ID'
        }
      ],
      keyword: '',
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true
    }
  },
  methods: {
    statusClass (status) {
      return {
        'Pending': 'dark',
        'Completed': 'positive',
        'Cancelled': 'orange',
        'Error': 'negative',
        'Expired': 'pansy'
      }[status] || status
    }
  }
}
</script>
