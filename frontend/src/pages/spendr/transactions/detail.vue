<template>
  <q-dialog
    class="spendr-bank-ledge-detail-dialog"
    v-model="visible"
    prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">Create Ledger</div>
      <div class="font-12 normal text-dark">Please fill in the information below.</div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-6">
          <q-input float-label="Account"
                   autocomplete="no"
                   v-model="entity['Account']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Chk Ref"
                   autocomplete="no"
                   v-model="entity['Chk Ref']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Debit"
                   autocomplete="no"
                   v-model="entity['Debit']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Credit"
                   autocomplete="no"
                   v-model="entity['Credit']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Balance"
                   autocomplete="no"
                   v-model="entity['Balance']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Date"
                   autocomplete="no"
                   placeholder="eg. 01/22/2022"
                   v-model="entity['Date']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Description"
                   autocomplete="no"
                   :error="$v.entity['Description'].$error"
                   @input="$v.entity['Description'].$touch"
                   v-model="entity['Description']"></q-input>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Save"
               no-caps
               color="positive" class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request } from '../../../common'
import { required } from 'vuelidate/lib/validators'

export default {
  name: 'spendr-bank-ledge-detail-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        'Account': null,
        'Chk Ref': null,
        'Debit': null,
        'Credit': null,
        'Balance': null,
        'Date': null,
        'Description': null
      }
    }
  },
  validations: {
    entity: {
      Description: {
        required
      }
    }
  },
  methods: {
    async show () {
      // if (!this.entity || !this.entity['User ID']) {
      //   return
      // }
      // this.$q.loading.show()
      // const resp = await request(`/admin/spendr/members/${this.entity['User ID']}/detail`)
      // this.$q.loading.hide()
      // if (resp.success) {
      //   this.entity = _.assignIn({}, this.entity, resp.data)
      // }
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/transactions/create`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.$root.$emit('reload-spendr-transactions')
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.spendr-bank-ledge-detail-dialog {
  .modal-content {
    width: 580px!important;
  }

  .modal-scroll {
    max-height: none;
  }
}
</style>
