<template>
  <q-page id="spendr_transactions__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :iso-week="true"
                         :default="dateRange"
                         :ranges="datesWithDaily"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-history" color="blue"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.last | date('L LT') }}</div>
                  <div class="description">Last File Import</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.credit || 0 | moneyFormat }}</div>
                  <div class="description">Total Credits</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash" color="negative"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.debit || 0 | moneyFormat }}</div>
                  <div class="description">Total Debits</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
<!--          <q-btn icon="mdi-plus"-->
<!--                 color="positive"-->
<!--                 label="Create"-->
<!--                 @click="create"-->
<!--                 v-if="!bankAdmin"-->
<!--                 class="btn-sm mr-8"-->
<!--                 no-caps></q-btn>-->
          <q-btn icon="mdi-account-group-outline"
                 color="positive"
                 label="Import Daily File"
                 @click="importData"
                 v-if="isMasterAdmin() || agentAdmin || spendrCompliance"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
<!--          <q-btn icon="mdi-cash"-->
<!--                 color="blue"-->
<!--                 label="Process Partner Balance"-->
<!--                 @click="processPartnerBalance"-->
<!--                 v-if="!bankAdmin"-->
<!--                 class="btn-sm mr-8"-->
<!--                 no-caps></q-btn>-->
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <div notranslate="">
              <template v-if="col.field === 'Credit'">
                <span class="text-positive">{{ _.get(props.row, col.field) }}</span>
              </template>
              <template v-else-if="col.field === 'Debit'">
                <span class="text-negative">{{ props.row['Debit'] ? '-' + props.row['Debit'] : null }}</span>
              </template>
              <template v-else-if="col.field === 'Status'">
                <q-chip class="font-12"
                        :class="styleClass(props.row['Status'])">
                  {{ props.row['Status'] | capitalize }}
                </q-chip>
              </template>
<!--              <template v-else-if="!bankAdmin && !spendrEmployee && col.field === 'Actions'">-->
<!--                <q-btn-dropdown v-if="props.row['Status'] === null || props.row['Status'] === 'pending'"-->
<!--                                class="btn-sm"-->
<!--                                no-caps-->
<!--                                color="grey-2"-->
<!--                                text-color="dark"-->
<!--                                :label="col.label">-->
<!--                  <q-list link>-->
<!--                    <q-item v-if="props.row['Status'] === 'completed'"-->
<!--                            v-close-overlay-->
<!--                            @click.native="rollback(props.row)">-->
<!--                      <q-item-main>Rollback</q-item-main>-->
<!--                    </q-item>-->
<!--                    <q-item v-close-overlay-->
<!--                            @click.native="remove(props.row)">-->
<!--                      <q-item-main>Delete</q-item-main>-->
<!--                    </q-item>-->
<!--                  </q-list>-->
<!--                </q-btn-dropdown>-->
<!--              </template>-->
              <template v-else>{{ _.get(props.row, col.field) }}</template>
            </div>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <ImportDialog></ImportDialog>
<!--    <DetailDialog></DetailDialog>-->
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import ImportDialog from './import'
// import DetailDialog from './detail'

export default {
  name: 'spendr-transactions',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-transactions')
  ],
  components: {
    ImportDialog
    // DetailDialog
  },
  data () {
    const columns = [
      'Date', 'ID', 'Account', 'Chk Ref',
      'Debit', 'Credit', 'Balance', 'Description', 'Import Date', 'Status'
    ]
    if (this.isBankAdmin() || this.isSpendrEmployee()) {
      columns.splice(columns.indexOf('Actions'), 1)
    }
    return {
      title: 'Bank Ledger',
      requestUrl: `/admin/spendr/transactions/list`,
      downloadUrl: `/admin/spendr/transactions/export`,
      columns: generateColumns(columns),
      filterOptions: [
        {
          value: 'filter[t.account=]',
          label: 'Account'
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 1,
      autoLoad: true
    }
  },
  methods: {
    styleClass (status) {
      if (status === 'completed(combine)') {
        return 'blue'
      } else if (status === 'completed') {
        return 'positive'
      } else if (status === 'pending') {
        return 'gray'
      } else {
        return 'negative'
      }
    },
    importData () {
      this.$root.$emit('show-spendr-transactions-import-dialog')
    }
    // create () {
    //   this.$root.$emit('show-spendr-bank-ledge-detail-dialog')
    // }
    // async processPartnerBalance () {
    //   this.$q.loading.show({
    //     message: 'Processing partner balance...'
    //   })
    //   const resp = await request(`/admin/spendr/transactions/process-balance`)
    //   this.$q.loading.hide()
    //   if (resp.success) {
    //     notify(resp.message)
    //     this.reload()
    //   }
    // },
    // rollback (item) {
    //   this.$q.dialog({
    //     title: `Rollback`,
    //     message: `Are you sure you want to rollback this transaction?
    //      If the balance changes when the data was created or imported before,
    //      we will perform a rollback operation.`,
    //     cancel: true,
    //     color: 'negative'
    //   }).then(async () => {
    //     this.$q.loading.show()
    //     const resp = await request(`/admin/spendr/transactions/rollback/${item.ID}`)
    //     this.$q.loading.hide()
    //     if (resp.success) {
    //       notifySuccess(resp)
    //       this.reload()
    //     }
    //   }).catch(() => {})
    // },
    // remove (item) {
    //   this.$q.dialog({
    //     title: `Delete`,
    //     message: `Are you sure you want to delete this transaction?`,
    //     cancel: true,
    //     color: 'negative'
    //   }).then(async () => {
    //     this.$q.loading.show()
    //     const resp = await request(`/admin/spendr/transactions/delete/${item.ID}`)
    //     this.$q.loading.hide()
    //     if (resp.success) {
    //       notifySuccess(resp)
    //       this.reload()
    //     }
    //   }).catch(() => {})
    // }
  }
}
</script>
<style lang="scss">
  .modal.minimized .modal-content {
    max-width: 600px;
  }
</style>
