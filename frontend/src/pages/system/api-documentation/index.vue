<template>
  <q-page id="system__api_documentation_index_page">
    <div class="page-header column">
      <div class="title mb-15">{{ title }}</div>
      <div class="subtitle-line font-14">{{ entity.name || ' ' }}</div>
    </div>
    <div class="page-content">
      <div class="group-header">Abstract</div>
      <q-editor v-model="abstract"
                min-height="200px"
                max-height="300px"
                :toolbar="toolbar"
                :fonts="fonts"></q-editor>

      <div class="group-header">APIs</div>
      <div class="row mt-20">
        <div class="col-3">
          <q-toolbar color="white" class="api-toolbar">
            <q-btn flat
                   :disable="!selectedApiId"
                   @click="moveUp"
                   round
                   color="primary"
                   icon="mdi-arrow-up"></q-btn>
            <q-btn flat
                   :disable="!selectedApiId"
                   @click="moveDown"
                   round
                   color="primary"
                   icon="mdi-arrow-down"></q-btn>
          </q-toolbar>
          <q-tree :nodes="apis"
                  ref="apiTree"
                  default-expand-all
                  :selected.sync="selectedApiId"
                  class="api-tree"
                  node-key="id"></q-tree>
        </div>
        <div class="col-9 api-form">
          <template v-if="selectedApi">
            <q-field :label="selectedFolder ? 'Section Name' : 'API Name'" label-width="2">
              <q-input type="text" v-model="selectedApi.label"></q-input>
            </q-field>
            <q-field label="Description" label-width="2" v-if="selectedApi.description !== undefined">
              <q-editor v-model="selectedApi.description"
                        min-height="250px"
                        :toolbar="toolbar"
                        :fonts="fonts"></q-editor>
            </q-field>
            <q-field label="Visibility" label-width="2">
              <q-checkbox v-model="selectedApi.hidden" label="Hidden"></q-checkbox>
            </q-field>
          </template>
          <template v-else>
            <q-alert color="white" text-color="dark" icon="mdi-alert-circle-outline">
              Choose an API from left side to configure the name, description or visibility.
            </q-alert>
          </template>
        </div>
      </div>

      <div class="row mt-20">
        <div class="col">
          <q-btn label="Submit"
                 @click="submit"
                 color="primary"></q-btn>
          <q-btn label="Reload"
                 icon="mdi-refresh"
                 class="ml-20"
                 flat
                 @click="reload()"
                 color="grey"></q-btn>
        </div>
      </div>

      <q-inner-loading :visible="loading">
        <q-spinner size="40"></q-spinner>
      </q-inner-loading>
    </div>
  </q-page>
</template>

<script>
import PageMixin from '../../../mixins/PageMixin'
import { notifySuccess, request } from '../../../common'

export default {
  name: 'PlatformBrandingAPI',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'Customize API Documentation',
      entity: {},
      loading: false,
      abstract: '',
      apis: [],
      selectedApiId: null,
      toolbar: [],
      fonts: {
        arial: 'Arial',
        arial_black: 'Arial Black',
        comic_sans: 'Comic Sans MS',
        courier_new: 'Courier New',
        impact: 'Impact',
        lucida_grande: 'Lucida Grande',
        times_new_roman: 'Times New Roman',
        verdana: 'Verdana'
      }
    }
  },
  computed: {
    selectedApi () {
      if (!this.selectedApiId) {
        return null
      }
      for (const tag of this.apis) {
        if (tag.id === this.selectedApiId) {
          return tag
        }
        for (const api of tag.children) {
          if (api.id === this.selectedApiId) {
            return api
          }
        }
      }
      return null
    },
    selectedFolder () {
      if (!this.selectedApiId) {
        return null
      }
      for (const tag of this.apis) {
        if (tag.id === this.selectedApiId) {
          return tag
        }
      }
      return null
    }
  },
  methods: {
    async reload () {
      this.loading = true
      const resp = await request(`/admin/system/api-documentation/data/${this.$route.params.id}`)
      this.loading = false
      if (resp.success) {
        this.entity = resp.data.entity
        this.abstract = resp.data.abstract
        this.apis = resp.data.apis

        this.$nextTick(() => {
          this.$refs.apiTree.expandAll()
        })
      }
    },

    async submit () {
      this.loading = true
      const resp = await request(`/admin/system/api-documentation/data/${this.$route.params.id}`, 'post', {
        abstract: this.abstract,
        apis: this.apis
      })
      this.loading = false
      notifySuccess(resp)
    },

    moveUp () {
      const [group, index] = this.findGroup()
      if (index === 0) {
        return
      }
      const temp = group[index - 1]
      this.$set(group, index - 1, group[index])
      this.$set(group, index, temp)

      this.$forceUpdate()
    },

    moveDown () {
      const [group, index] = this.findGroup()
      if (index === group.length - 1) {
        return
      }
      const temp = group[index + 1]
      this.$set(group, index + 1, group[index])
      this.$set(group, index, temp)

      this.$forceUpdate()
    },

    findGroup () {
      const id = this.selectedApiId
      for (let i = 0; i < this.apis.length; i++) {
        const section = this.apis[i]
        if (section.id === id) {
          return [this.apis, i]
        }
        for (let j = 0; j < section.children.length; j++) {
          const api = section.children[j]
          if (api.id === id) {
            return [section.children, j]
          }
        }
      }
    }
  },
  mounted () {
    this.toolbar = [
      ['bold', 'italic', 'strike', 'underline', 'subscript', 'superscript'],
      ['token', 'hr', 'link', 'custom_btn'],
      [
        {
          label: this.$q.i18n.editor.align,
          icon: this.$q.icon.editor.align,
          fixedLabel: true,
          list: 'only-icons',
          options: ['left', 'center', 'right', 'justify']
        }
      ],
      [
        {
          label: this.$q.i18n.editor.formatting,
          icon: this.$q.icon.editor.formatting,
          list: 'no-icons',
          options: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'code']
        },
        {
          label: this.$q.i18n.editor.fontSize,
          icon: this.$q.icon.editor.fontSize,
          fixedLabel: true,
          fixedIcon: true,
          list: 'no-icons',
          options: ['size-1', 'size-2', 'size-3', 'size-4', 'size-5', 'size-6', 'size-7']
        },
        {
          label: this.$q.i18n.editor.defaultFont,
          icon: this.$q.icon.editor.font,
          fixedIcon: true,
          list: 'no-icons',
          options: ['default_font', 'arial', 'arial_black', 'comic_sans', 'courier_new', 'impact', 'lucida_grande', 'times_new_roman', 'verdana']
        },
        'removeFormat'
      ],
      ['quote', 'unordered', 'ordered', 'outdent', 'indent'],
      ['undo', 'redo'],
      ['print', 'fullscreen']
    ]

    this.reload()
  }
}
</script>

<style lang="scss">
#system__api_documentation_index_page {
  .api-tree {
    padding-right: 20px;
    max-height: 350px;
    overflow-y: auto;
    margin-top: 10px;
  }

  .api-form {
    padding-left: 20px;
    border-left: 1px solid rgba(0, 0, 0, 0.1);
  }

  .q-editor-content {
    h3 {
      margin: 10px 0;
      font-size: 20px;
    }

    p, ul, & {
      font-size: 14px;
      color: #333;
    }
  }
}
</style>
