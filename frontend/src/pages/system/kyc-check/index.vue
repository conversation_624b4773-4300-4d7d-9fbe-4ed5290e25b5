<template>
  <q-page id="system_kyc_check__list_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-btn @click="uploadCheckFile"
               class="mr-10"
               color="primary"
               no-caps>
          Upload Check File
        </q-btn>
        <q-btn class="mr-10"
               color="black"
               @click="download"
               label="Export"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <p class="import-info"
           v-if="quick.lastImport && quick.lastImport.createdAt">
          Last Import Check: {{ quick.lastImport.createdAt}}
          <span class="success">Status: {{quick.lastImport.status }} </span>
        </p>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               @request="request"
               separator="none"
               :rows-per-page-options="[5, 10, 20, 50]"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :align="col.align">
            <template v-if="col.field === 'Actions'">
              <q-btn-dropdown v-if="props.row['OFAC']&& !props.row['KYC']"
                              class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="uploadKyc(props.row)">
                    <q-item-main>Upload KYC Docs</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>
        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"
                       :max="999999"></BatchExportDialog>
    <UploadCheckFile></UploadCheckFile>
    <IdUploadDialog></IdUploadDialog>
    <IdInstructionDialog></IdInstructionDialog>
  </q-page>
</template>

<script>
import { generateColumns, EventHandlerMixin } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import UploadCheckFile from './upload_file'
import IdUploadDialog from './id_upload'
import IdInstructionDialog from './id_instruction'

export default {
  name: 'check-kyc',
  mixins: [
    ListPageMixin,
    EventHandlerMixin('reload-check-kyc')
  ],
  components: {
    UploadCheckFile,
    IdUploadDialog,
    IdInstructionDialog
  },
  data () {
    return {
      requestUrl: '/admin/system/kyc-check/list',
      downloadUrl: '/admin/system/kyc-check/export',
      title: 'KYC Check',
      autoReloadWhenUrlChanges: true,
      columns: generateColumns([
        'First Name', 'Last Name', 'Email/Mg contact/Tel', 'DOB', 'PASS#', 'OFAC', 'KYC', 'Status', 'Reason', 'Actions'
      ]),
      quick: {},
      filterOptions: [
        {
          value: 'fileName',
          label: 'File Name'
        }
      ],
      autoLoad: true
    }
  },
  methods: {
    uploadCheckFile () {
      this.$root.$emit('show-system-kyc-check-dialog')
    },
    uploadKyc (row) {
      return this.$root.$emit('show-system-check-user-id-upload-dialog', row)
    }
  }
}
</script>
<style lang="scss">
#system_kyc_check__list_page {
  .import-info {
    text-align: right;
    margin: 15px !important;
    width: 100%;
    span {
      margin: 0 5px;
    }
    .success {
      color: green;
    }
  }
}
</style>
