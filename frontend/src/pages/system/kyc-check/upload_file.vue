<template>
  <q-dialog class="system-kyc-check-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Upload Check File</div>
      <div class="font-12 normal">Please select a XLS file to upload.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row flex-center">
        <div class="col-7">
          <div class="upload-area"
               @click="selectFile"
               :class="{selected: file}">
            <q-icon name="mdi-file-document-outline"></q-icon>
            <template v-if="file">
              <div class="mt-10">Selected file:</div>
              <div class="font-13 text-blue">
                <span>{{ file.name }}</span>
                <a href="javascript:"
                   class="ml-5 link"
                   @click.stop="file = null">
                  <q-icon name="mdi-close-circle-outline"
                          class="font-20"
                          color="negative"></q-icon>
                  <q-tooltip>Remove and reselect</q-tooltip>
                </a>
              </div>
            </template>
            <template v-else>
              <div class="mt-10">Upload .xls</div>
              <div class="font-13 text-dark">(Drop or click here)</div>
            </template>
          </div>
          <input type="file"
                 class="hide"
                 :accept="fileAccept"
                 ref="file"
                 @change="selectedFile">
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks single">
        <q-btn label="Continue"
               no-caps
               color="positive"
               :disable="!file"
               @click="submit" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, notifyResponse, request, uploadAttachment } from '../../../common'
import DndUploadFileMixin from '../../../mixins/DndUploadFileMixin'

export default {
  name: 'system-kyc-check-dialog',
  mixins: [
    Singleton,
    DndUploadFileMixin
  ],
  data () {
    return {
      file: null,
      attachment: null,
      notes: false,
      acceptFileTypes: [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ],
      prefix: ''
    }
  },
  watch: {
    file () {
      this.attachment = null
    }
  },
  methods: {
    show () {
      this.file = null
      this.attachment = null
    },
    isFileUploaded () {
      return this.attachment
    },
    async submit () {
      if (!this.file) {
        return
      }
      if (!this.attachment) {
        this.$q.loading.show({ message: 'Uploading...' })
        const resp = await uploadAttachment(this.file, 'system_kyc_check')
        if (typeof resp === 'string') {
          this.$q.loading.hide()
          return notifyResponse(`Failed to upload: ${resp}`)
        }
        this.attachment = resp
      }
      this.$q.loading.show({ message: 'Processing...' })
      const resp = await request(`/admin/system/kyc-check/upload-file`, 'post', {
        attachment: this.attachment.id
      }, true)
      this.$q.loading.hide()
      // console.log(resp)
      if (resp.success) {
        this._hide()
        notify(resp.data)
      } else {
        notify(resp.message)
      }
      this.$root.$emit('reload-check-kyc')
    }
  }
}
</script>

<style lang="scss">
.system-kyc-check-dialog {
  .modal-content {
    position: relative;
    background: #fff;
    width: 450px;
    border-radius: 16px;
    overflow-y: auto;
    will-change: scroll-position;
    min-width: 280px;
    .modal-header {
      text-align: center;
      font-size: 18px;
      font-weight: 500;
      .close {
        position: absolute;
        right: 10px;
        top: 10px;
      }
    }
    .modal-body {
      text-align: center;
      color: #171726;
    }
    .upload-area {
      border: 2px dashed #e2e2e2;
      border-radius: 14px;
      padding: 25px 15px;
      cursor: pointer;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -ms-flex-direction: column;
      flex-direction: column;
      position: relative;
    }
    .upload-area > .q-icon {
      font-size: 45px;
      color: #92929d;
    }
    .modal-buttons > .stacks {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -ms-flex-direction: column;
      flex-direction: column;
      width: 100%;
    }
    .modal-buttons {
      padding: 10px 24px 18px;
      .stacks.single {
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        .q-btn {
          margin-left: 0;
          width: 100%;
        }
      }
    }
    .q-btn.q-btn-rectangle {
      border-radius: 10px;
    }
  }
}
</style>
