<template>
  <div class="platform-branding-commons">
    <template v-if="type === 'default'">
      <div class="group-header">Meta</div>
      <div class="row gutter-sm">
        <div class="col-6">
          <q-field label="Domain" label-width="4">
            <q-input v-model="entity.subDomain"
                     :prefix="`${entity.schema || 'https'}://`"
                     :suffix="`.${entity.host || ''}`"></q-input>
          </q-field>

          <q-field label="Custom domain" label-width="4">
            <q-input v-model="entity.domain"
                     :prefix="`https://`"></q-input>
          </q-field>

          <q-field label="Alternative domains" label-width="4"
                   helper="One domain in each line. For instance, 'sub.example.com'.">
            <q-input v-model="entity.otherDomains"
                     rows="3"
                     type="textarea"></q-input>
          </q-field>

          <q-field label="Description" label-width="4">
            <q-input v-model="entity.description"
                     rows="3"
                     type="textarea"></q-input>
          </q-field>

          <q-field label="Default language" label-width="4">
            <q-select v-model="entity.language"
                      :options="entity.languages || []"></q-select>
          </q-field>
        </div>
      </div>
    </template>

    <div class="group-header">Logo</div>
    <div class="row gutter-sm">
      <div class="col-12">
        <q-field label="Logo"
                 label-width="2"
                 helper="The logo that will show in the login page and other similar pages with light background. Click on '+' button to select the file to upload. Please ensure that the file has been uploaded before submitting the form.">
          <q-uploader url="/attachments"
                      name="logo"
                      auto-expand
                      ref="uploader_logo"
                      @add="$refs.uploader_logo && $refs.uploader_logo.upload()"
                      @uploaded="(file, xhr) => uploadedAttachment('logo', xhr)"
                      extensions=".gif,.jpg,.jpeg,.png"
                      :additional-fields="[{name: 'category', value: 'public'}]"
                      clearable></q-uploader>
        </q-field>
        <div class="row gutter-sm" v-if="entity.logo">
          <div class="offset-2 col text-left">
            <img :src="entity.logo" alt="">
            <div class="q-my-lg">
              <q-btn label="Remove"
                     flat
                     @click="removeAttachment('logo')"
                     icon="mdi-close"
                     color="negative"
                     size="sm"></q-btn>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row gutter-sm">
      <div class="col-12">
        <q-field label="Light Logo"
                 label-width="2"
                 helper="The logo that will show in the admin portal and other similar pages with dark background. Click on '+' button to select the file to upload. Please ensure that the file has been uploaded before submitting the form.">
          <q-uploader url="/attachments"
                      name="lightLogo"
                      auto-expand
                      ref="uploader_lightLogo"
                      @add="$refs.uploader_lightLogo && $refs.uploader_lightLogo.upload()"
                      @uploaded="(file, xhr) => uploadedAttachment('lightLogo', xhr)"
                      extensions=".gif,.jpg,.jpeg,.png"
                      :additional-fields="[{name: 'category', value: 'public'}]"
                      clearable></q-uploader>
        </q-field>
        <div class="row gutter-sm" v-if="entity.lightLogo">
          <div class="offset-2 col text-left">
            <img :src="entity.lightLogo" alt="">
            <div class="q-my-lg">
              <q-btn label="Remove"
                     flat
                     @click="removeAttachment('lightLogo')"
                     icon="mdi-close"
                     color="negative"
                     size="sm"></q-btn>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row gutter-sm">
      <div class="col-12">
        <q-field label="Square icon"
                 label-width="2"
                 helper="PNG format. Show in browser header, bookmark and webapp home screen.">
          <q-uploader url="/attachments"
                      name="icon"
                      auto-expand
                      ref="uploader_icon"
                      @add="$refs.uploader_icon && $refs.uploader_icon.upload()"
                      @uploaded="(file, xhr) => uploadedAttachment('icon', xhr)"
                      :additional-fields="[{name: 'category', value: 'public'}]"
                      extensions=".png"
                      clearable></q-uploader>
        </q-field>
        <div class="row gutter-sm" v-if="entity.icon">
          <div class="offset-2 col text-left">
            <img :src="entity.icon" alt="">
            <div class="q-my-lg">
              <q-btn label="Remove"
                     flat
                     @click="removeAttachment('icon')"
                     icon="mdi-close"
                     color="negative"
                     size="sm"></q-btn>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="group-header">Colors</div>
    <div class="row gutter-sm" v-if="entity.customize">
      <div class="col-6 colors">
        <q-field label="Link color" label-width="4">
          <q-color v-model="entity.customize.linkColor"></q-color>
          <q-icon name="mdi-square" :style="{color: entity.customize.linkColor}"></q-icon>
        </q-field>

        <q-field label="Link active color" label-width="4">
          <q-color v-model="entity.customize.linkActiveColor"></q-color>
          <q-icon name="mdi-square" :style="{color: entity.customize.linkActiveColor}"></q-icon>
        </q-field>

        <q-field label="Button color" label-width="4">
          <q-color v-model="entity.customize.buttonColor"></q-color>
          <q-icon name="mdi-square" :style="{color: entity.customize.buttonColor}"></q-icon>
        </q-field>

        <q-field label="Button active color" label-width="4">
          <q-color v-model="entity.customize.buttonActiveColor"></q-color>
          <q-icon name="mdi-square" :style="{color: entity.customize.buttonActiveColor}"></q-icon>
        </q-field>
      </div>
    </div>

    <div class="group-header">Font</div>
    <div class="row gutter-sm" v-if="entity.customize">
      <div class="col-12">
        <q-field label="Font" label-width="2">
          <font-picker :value="entity.customize.font"
                       @change="font => $set(entity.customize, 'font', font.family)"></font-picker>
        </q-field>
      </div>
    </div>

    <div class="group-header">Other</div>
    <div class="row gutter-sm">
      <div class="col-12">
        <q-field label="Customize CSS" label-width="2">
          <q-input type="textarea"
                   :max-height="100"
                   rows="7"
                   v-model="entity.css"></q-input>
        </q-field>
      </div>
      <div class="col-12">
        <q-field label="Customize JavaScript" label-width="2">
          <q-input type="textarea"
                   :max-height="100"
                   rows="7"
                   v-model="entity.js"></q-input>
        </q-field>
      </div>
    </div>
  </div>
</template>

<script>
import FontPicker from '../../../components/FontPicker'

export default {
  name: 'PlatformBrandingCommon',
  components: {
    FontPicker
  },
  props: {
    type: {
      type: String
    }
  },
  data () {
    return {
      entity: {
        customize: {},
        otherDomains: ''
      }
    }
  },
  methods: {
    removeAttachment (field) {
      this.entity[field] = null
      this.entity[`${field}_delete`] = true
    },

    uploadedAttachment (field, xhr) {
      const resp = JSON.parse(xhr.response)
      const file = resp.data[field]
      this.entity[field] = file.url
      this.entity[`${field}_id`] = file.id
      delete this.entity[`${field}_delete`]

      this.$refs[`uploader_${field}`].reset()
    }
  }
}
</script>
