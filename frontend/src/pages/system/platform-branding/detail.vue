<template>
  <q-page id="system__platform_branding_detail_page">
    <div class="page-header column">
      <div class="title mb-15">{{ title }}</div>
      <div class="subtitle-line font-14">{{ entity.name || ' ' }}</div>
    </div>
    <div class="page-content">
      <q-tabs inverted v-model="tab">
        <q-tab default slot="title" name="default" label="Default Branding"/>
        <q-tab slot="title" name="admin" label="Admin Dashboard"/>
        <q-tab slot="title" name="developer" label="Developer Portal"/>
        <q-tab slot="title" name="customer" label="Customer Portal"/>
        <q-tab slot="title" name="mobile" label="Mobile Application"/>

        <q-tab-pane name="default" keep-alive>
          <PlatformBrandingCommon type="default"
                                  ref="default"></PlatformBrandingCommon>

          <div class="row mt-20">
            <div class="col offset-2">
              <q-btn label="Submit"
                     @click="submit"
                     color="primary"></q-btn>
              <q-btn label="Reload"
                     icon="mdi-refresh"
                     class="ml-20"
                     flat
                     @click="reload()"
                     color="grey"></q-btn>
            </div>
          </div>
        </q-tab-pane>

        <q-tab-pane name="admin" keep-alive>

          <div class="group-header">Colors</div>
          <div class="row gutter-sm">
            <div class="col-6 colors">
              <q-field label="Admin primary color" label-width="6">
                <q-color v-model="entity.customize.adminPrimaryColor"></q-color>
                <q-icon name="mdi-square" :style="{color: entity.customize.adminPrimaryColor}"></q-icon>
              </q-field>

              <q-field label="Admin primary positive color" label-width="6">
                <q-color v-model="entity.customize.adminPrimaryPositiveColor"></q-color>
                <q-icon name="mdi-square" :style="{color: entity.customize.adminPrimaryPositiveColor}"></q-icon>
              </q-field>

              <q-field label="Admin primary negative color" label-width="6">
                <q-color v-model="entity.customize.adminPrimaryNegativeColor"></q-color>
                <q-icon name="mdi-square" :style="{color: entity.customize.adminPrimaryNegativeColor}"></q-icon>
              </q-field>

              <q-field label="Admin secondary color" label-width="6">
                <q-color v-model="entity.customize.adminSecondaryColor"></q-color>
                <q-icon name="mdi-square" :style="{color: entity.customize.adminSecondaryColor}"></q-icon>
              </q-field>

              <q-field label="Admin secondary positive color" label-width="6">
                <q-color v-model="entity.customize.adminSecondaryPositiveColor"></q-color>
                <q-icon name="mdi-square" :style="{color: entity.customize.adminSecondaryPositiveColor}"></q-icon>
              </q-field>

              <q-field label="Admin secondary negative color" label-width="6">
                <q-color v-model="entity.customize.adminSecondaryNegativeColor"></q-color>
                <q-icon name="mdi-square" :style="{color: entity.customize.adminSecondaryNegativeColor}"></q-icon>
              </q-field>
            </div>
          </div>

          <PlatformBrandingOverwrite ref="admin"
                                     type="admin"></PlatformBrandingOverwrite>

          <div class="row mt-20">
            <div class="col offset-3">
              <q-btn label="Submit"
                     @click="submit"
                     color="primary"></q-btn>
            </div>
          </div>
        </q-tab-pane>

        <q-tab-pane name="developer" keep-alive>
          <PlatformBrandingOverwrite ref="developer"
                                     type="developer"></PlatformBrandingOverwrite>

          <div class="row mt-20">
            <div class="col offset-3">
              <q-btn label="Submit"
                     @click="submit"
                     color="primary"></q-btn>
            </div>
          </div>
        </q-tab-pane>

        <q-tab-pane name="customer" keep-alive>
          <PlatformBrandingOverwrite ref="customer"
                                     type="customer"></PlatformBrandingOverwrite>

          <div class="row mt-20">
            <div class="col offset-3">
              <q-btn label="Submit"
                     @click="submit"
                     color="primary"></q-btn>
            </div>
          </div>
        </q-tab-pane>

        <q-tab-pane name="mobile" keep-alive>
          <PlatformBrandingOverwrite ref="mobile"
                                     type="mobile"></PlatformBrandingOverwrite>

          <div class="row mt-20">
            <div class="col offset-3">
              <q-btn label="Submit"
                     @click="submit"
                     color="primary"></q-btn>
            </div>
          </div>
        </q-tab-pane>
      </q-tabs>

      <q-inner-loading :visible="loading">
        <q-spinner size="40"></q-spinner>
      </q-inner-loading>
    </div>
  </q-page>
</template>

<script>
import PageMixin from '../../../mixins/PageMixin'
import { notifySuccess, request } from '../../../common'
import _ from 'lodash'
import PlatformBrandingOverwrite from './overwrite'
import PlatformBrandingCommon from './common'

export default {
  name: 'PlatformBrandingDetail',
  mixins: [
    PageMixin
  ],
  components: {
    PlatformBrandingOverwrite,
    PlatformBrandingCommon
  },
  data () {
    return {
      title: 'Platform Branding',
      tab: 'default',
      entity: {
        customize: {}
      }
    }
  },
  watch: {
    tab () {
      this.initOverwrite()
    }
  },
  methods: {
    async reload () {
      this.loading = true
      const resp = await request(`/admin/system/platform-branding/${this.$route.params.id}/get/detail`, 'get')
      this.loading = false
      if (resp.success) {
        this.entity = _.defaultsDeep(resp.data, {
          customize: {
            font: 'Lato'
          }
        })
        this.initOverwrite()
      }
    },

    initOverwrite () {
      this.$nextTick(() => {
        for (const type of ['default', 'admin', 'developer', 'customer', 'mobile']) {
          if (this.$refs[type] && !this.$refs[type].initialized) {
            const comp = this.$refs[type]
            if (comp.setEntity) {
              comp.setEntity(_.cloneDeep(this.entity))
            } else {
              comp.entity = _.cloneDeep(this.entity)
              comp.initialized = true
            }
          }
        }
      })
    },

    async submit () {
      const defaults = this.$refs['default'].entity
      for (const key of [
        'logo', 'logo_id', 'logo_delete',
        'lightLogo', 'lightLogo_id', 'lightLogo_delete',
        'icon', 'icon_id', 'icon_delete',
        'css', 'js', 'subDomain', 'domain', 'otherDomains',
        'description', 'language'
      ]) {
        this.entity[key] = defaults[key]
      }

      _.forEach(defaults.customize, (v, key) => {
        if (!key.startsWith('admin')) {
          this.entity.customize[key] = v
        }
      })

      delete this.entity.customize['disabledAPIs']

      for (const type of ['admin', 'developer', 'customer', 'mobile']) {
        if (this.$refs[type]) {
          this.entity.customize[`_${type}`] = this.$refs[type].entity.customize[`_${type}`]
        }
      }

      this.$q.loading.show()
      const resp = await request(`/admin/system/platform-branding/${this.$route.params.id}/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notifySuccess(resp)
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #system__platform_branding_detail_page {
    .q-tabs-inverted .q-tabs-head {
      background-color: transparent;

      .q-tab {
        text-transform: none;
      }
    }

    .q-tabs-panes {
      .q-field {
        margin-bottom: 10px;

        .q-field-label-inner {
          font-size: 13px;
          font-weight: normal;
        }
      }

      textarea.q-input-target {
        height: auto !important;
      }

      .colors {
        .q-field-content {
          display: flex;

          > .q-if {
            flex-grow: 1;
          }

          > .q-icon {
            margin-left: 15px;
            font-size: 18px;
          }
        }
      }
    }
  }
</style>
