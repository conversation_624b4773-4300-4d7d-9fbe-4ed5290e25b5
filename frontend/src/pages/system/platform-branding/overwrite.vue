<template>
  <q-list class="mt-20 pv-0 platform-branding-overwrite">
    <q-collapsible ref="panel">
      <template slot="header">
        <q-checkbox v-model="overwritten"
                    class="mr-auto"
                    label="Overwrite default settings"></q-checkbox>
      </template>

      <PlatformBrandingCommon v-if="overwritten"
                              ref="common"></PlatformBrandingCommon>
      <ol v-else>
        <li>Check the "Overwrite default settings".</li>
        <li>Settings from the tab "Default Branding" will be copied.</li>
        <li>Change the settings to the values you want.</li>
        <li>Submit the form in this tab.</li>
      </ol>
    </q-collapsible>
  </q-list>
</template>

<script>
import PlatformBrandingCommon from './common'
import _ from 'lodash'

export default {
  name: 'PlatformBrandingOverwrite',
  components: {
    PlatformBrandingCommon
  },
  props: {
    type: {
      type: String
    }
  },
  data () {
    return {
      entity: {
        customize: {}
      },
      initialized: false
    }
  },
  computed: {
    key () {
      return `_${this.type}`
    },

    overwritten: {
      get () {
        return this.entity.customize[this.key] !== undefined
      },
      set (value) {
        if (value && !this.entity.customize[this.key]) {
          const customize = _.cloneDeep(this.entity.customize)

          for (const type of ['admin', 'developer', 'customer', 'mobile']) {
            delete customize[`_${type}`]
          }

          _.forEach(customize, (v, k) => {
            if (k.startsWith('admin')) {
              delete customize[k]
            }
          })

          this.$set(this.entity.customize, this.key, {
            logo: this.entity.logo,
            icon: this.entity.icon,
            css: this.entity.css,
            customize
          })
        } else if (!value) {
          this.$delete(this.entity.customize, this.key)
        }

        this.$nextTick(() => {
          if (value) {
            this.$refs.panel.show()
            this.$refs.common.entity = this.entity.customize[this.key]
          } else {
            this.$refs.panel.hide()
          }
        })
      }
    }
  },
  methods: {
    setEntity (entity) {
      this.entity = entity
      this.initialized = true

      this.$nextTick(() => {
        if (this.overwritten) {
          this.$refs.panel.show()

          if (this.$refs.common) {
            this.$refs.common.entity = this.entity.customize[this.key]
          }
        }
      })
    }
  }
}
</script>

<style lang="scss">
.platform-branding-overwrite {
  .q-collapsible-sub-item {
    background: #fcfcfc;

    ol {
      li {
        line-height: 1.5em;
        font-size: 13px;
      }
    }
  }
}
</style>
