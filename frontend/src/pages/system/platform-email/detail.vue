<template>
  <q-page id="system__platform_email_detail_page">
    <div class="page-header column">
      <div class="title mb-15">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="group-header">{{ entity.name }}</div>
      <q-field label="Support Email" label-width="2">
        <q-input v-model="entity.supportEmail"></q-input>
      </q-field>
      <q-field label="Live Chat URL" label-width="2">
        <q-input v-model="entity.liveChatUrl"></q-input>
      </q-field>
      <q-field label="Sender Name" label-width="2">
        <q-input v-model="entity.senderName"></q-input>
      </q-field>
      <q-field label="Product Name" label-width="2">
        <q-input v-model="entity.productName"></q-input>
      </q-field>
      <q-field label="Company Name" label-width="2">
        <q-input v-model="entity.companyName"></q-input>
      </q-field>
      <q-field label="Logo HTML" label-width="2">
        <q-input v-model="entity.logoHtml" type="textarea"></q-input>
      </q-field>
      <q-field label="Address HTML" label-width="2">
        <q-input v-model="entity.addressHtml" type="textarea"></q-input>
      </q-field>
      <q-field label="Address Text" label-width="2">
        <q-input v-model="entity.addressText" type="textarea"></q-input>
      </q-field>
      <div class="row mt-30">
        <q-btn label="Submit"
               color="primary"
               @click="submit"
               icon="mdi-send"></q-btn>
      </div>
    </div>

    <q-inner-loading :visible="loading">
      <q-spinner size="40"></q-spinner>
    </q-inner-loading>
  </q-page>
</template>

<script>
import PageMixin from '../../../mixins/PageMixin'
import { notifySuccess, request } from '../../../common'

export default {
  name: 'PlatformEmailDetail',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'Platform Email Template Values',
      entity: {}
    }
  },
  methods: {
    async reload () {
      this.loading = true
      const resp = await request(`/admin/system/platform-email/${this.$route.params.id}/get/detail`, 'get')
      this.loading = false
      if (resp.success) {
        this.entity = resp.data
      }
    },

    async submit () {
      this.$q.loading.show()
      const resp = await request(`/admin/system/platform-email/${this.$route.params.id}/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notifySuccess(resp)
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #system__platform_email_detail_page {
    .page-content {
      .q-field {
        margin-bottom: 15px;
      }
    }
  }
</style>
