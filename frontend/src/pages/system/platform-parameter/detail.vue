<template>
  <q-page id="system__platform_parameter_detail_page">
    <div class="page-header column">
      <div class="title mb-15">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="group-header">{{ entity.name }}</div>
      <q-field label-width="2"
               v-for="f in fields"
               :key="f.value"
               :label="f.label">
        <q-checkbox v-if="f.type === 'checkbox'"
                    v-model="values[f.value]"></q-checkbox>
        <q-select v-else-if="f.type === 'select'"
                  :options="f.options"
                  v-model="values[f.value]"></q-select>
        <q-input v-else-if="f.type === 'number'"
                 type="number"
                 v-model="values[f.value]"
                 :numeric-keyboard-toggle="true"
        ></q-input>
        <q-input v-else
                 v-model="values[f.value]"></q-input>
      </q-field>
      <div class="row mt-30">
        <q-btn label="Submit"
               color="primary"
               @click="submit"
               icon="mdi-send"></q-btn>
      </div>
    </div>

    <q-inner-loading :visible="loading">
      <q-spinner size="40"></q-spinner>
    </q-inner-loading>
  </q-page>
</template>

<script>
import PageMixin from '../../../mixins/PageMixin'
import { notifySuccess, request } from '../../../common'

export default {
  name: 'PlatformParametersDetail',
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'Platform Parameters',
      entity: {},
      values: {},
      fields: []
    }
  },
  methods: {
    async reload () {
      this.loading = true
      const resp = await request(`/admin/system/platform-parameter/${this.$route.params.id}/get/detail`, 'get')
      this.loading = false
      if (resp.success) {
        this.fields = resp.data.fields
        this.entity = resp.data.entity
        this.values = resp.data.values
      }
    },

    async submit () {
      this.$q.loading.show()
      const resp = await request(`/admin/system/platform-parameter/${this.$route.params.id}/save`, 'post', this.values)
      this.$q.loading.hide()
      if (resp.success) {
        notifySuccess(resp)
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
@import "../../../css/variable";

#system__platform_parameter_detail_page {
  .page-content {
    .q-field {
      margin-bottom: 15px;
    }
  }
}
</style>
