<template>
  <q-page id="declined_transactions__declined_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-field class="input-button-group inline-flex mr-20">
          <div class="row no-wrap">
            <q-input v-model="keyword"
                     clearable
                     class="w-250"
                     @clear="reload"
                     placeholder="Txn ID, User ID"></q-input>
            <q-btn label="Search"
                   color="grey-4"
                   class="no-shadow"
                   @click="reload"
                   text-color="black"></q-btn>
          </div>
        </q-field>

        <q-btn class="mr-10"
               color="black"
               @click="download"
               label="Export"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div class="max-w-300">
          <h5>{{ quick.totalCount | number }}</h5>
          <div class="description">Total # of declines</div>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat round dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true"/>
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props" :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field">
            <template v-if="!col.field">
              <q-btn-dropdown size="xs" :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          v-if="isPrivacyTxn(props.row)"
                          @click.native="viewDetails(props.row)">
                    <q-item-main>View Details</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else-if="col.field === 'userId' && p('user')">
              <a :href="`/admin#/a/i/admin__user_modify__modify?user_id=${col.value}`"
                 target="_blank">{{ col.value }}</a>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import { request } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'

export default {
  mixins: [
    ListPageMixin
  ],
  data () {
    return {
      filtersUrl: '/admin/declined_transaction/filters',
      downloadUrl: '/admin/card_decline_report',
      title: 'Card Decline List',
      autoReloadWhenUrlChanges: true,
      baseColumns: [{
        field: 'tranId',
        label: 'Transaction Id',
        align: 'left'
      }, {
        field: 'txnTime',
        label: 'Txn Time',
        align: 'left'
      }, {
        field: 'userId',
        label: 'User ID',
        align: 'left'
      }, {
        field: 'fullName',
        label: 'User name',
        align: 'left'
      }, {
        field: 'email',
        label: 'Email',
        align: 'left'
      }, {
        field: 'accountNumber',
        label: 'Account Number',
        align: 'left'
      }, {
        field: 'cardType',
        label: 'Card Type',
        align: 'left'
      }, {
        field: 'privacyCardType',
        label: 'Privacy Card Type',
        align: 'left'
      }, {
        field: 'txnAmount',
        label: 'Amount',
        align: 'left'
      }, {
        field: 'declineReason',
        label: 'Decline Reason',
        align: 'left'
      }, {
        field: 'receivedAddressData',
        label: 'ReceivedAddress',
        align: 'left'
      }, {
        field: 'actualAddressData',
        label: 'ActualAddress',
        align: 'left'
      }, {
        field: 'receivedZipCode',
        label: 'ReceivedZip',
        align: 'left'
      }, {
        field: 'actualZipCode',
        label: 'ActualZip',
        align: 'left'
      }, {
        field: 'avsResult',
        label: 'AVSResult',
        align: 'left'
      }, {
        field: 'billingAddressType',
        label: 'Billing Address Type',
        align: 'left'
      }, {
        field: 'defaultAddress',
        label: 'Addr1',
        align: 'left'
      }, {
        field: 'secondAddress',
        label: 'Addr2',
        align: 'left'
      }, {
        field: 'city',
        label: 'City',
        align: 'left'
      }, {
        field: 'addressState',
        label: 'State',
        align: 'left'
      }, {
        field: 'countryRegion',
        label: 'Region',
        align: 'left'
      }, {
        field: 'addressCountry',
        label: 'Country',
        align: 'left'
      }, {
        field: 'merchant',
        label: 'MerchantCustomName',
        align: 'left'
      }, {
        field: 'cardProgramName',
        label: 'Program',
        align: 'left'
      }, {
        field: '',
        label: 'Action',
        align: 'left'
      }],
      quick: {
        totalCount: 0
      },
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'filter[uc.accountNumber=]',
          label: 'Account Number'
        }, {
          value: 'filter[ucl.declineReason=]',
          label: 'Decline reason',
          options: [],
          source: 'declineReasons'
        }, {
          value: 'filter[ucl.merchant]',
          label: 'Merchant Cust Name'
        }, {
          value: 'filter[ucl.txnId=]',
          label: 'Transaction Id'
        }, {
          value: 'filter[cp.id=]',
          label: 'Card Program',
          options: [],
          source: 'cardPrograms'
        }, {
          value: 'filter[ct.id=]',
          label: 'Card Type',
          options: [],
          source: 'cardTypes'
        }, {
          value: 'filter[uc.type=]',
          label: 'Privacy Card Type',
          options: [
            {
              label: 'Single Use',
              value: 'SINGLE_USE'
            },
            {
              label: 'Merchant Locked',
              value: 'MERCHANT_LOCKED'
            }
          ]
        }, {
          value: 'filter[c.id=]',
          label: 'Card nick name',
          options: [],
          source: 'cpCardTypes'
        }, {
          value: 'filter[pm.id=]',
          label: 'Program manager',
          options: [],
          source: 'programManagers'
        }, {
          value: 'filter[bp.id=]',
          label: 'Brand Partner',
          options: [],
          source: 'brandPartners'
        }, {
          value: 'filter[mp.id=]',
          label: 'Marketing partner',
          options: [],
          source: 'marketingPartners'
        }, {
          value: 'filter[p.id=]',
          label: 'Processor',
          options: [],
          source: 'processors'
        }, {
          value: 'filter[ib.id=]',
          label: 'Issuing bank',
          options: [],
          source: 'issuingBanks'
        }, {
          value: 'filter[ba.reshipper=]',
          label: 'Billing address type',
          options: [],
          source: 'reshippers'
        }, {
          value: 'filter[country.region=]',
          label: 'Region',
          options: [],
          source: 'regions'
        }, {
          value: 'filter[country.id=]',
          label: 'Country',
          options: [],
          search: true,
          source: 'countries'
        }, {
          value: 'filter[state.id=]',
          label: 'State',
          options: [],
          search: true,
          source: 'states',
          map: {
            country: 'filter[country.id=]'
          }
        }, {
          value: 'filter[u.city]',
          label: 'City'
        }, {
          value: 'filter[aff.affType=]',
          label: 'Affiliate Type',
          options: [],
          source: 'affiliateTypes'
        }, {
          value: 'filter[u.affiliate=]',
          label: 'Affiliate',
          options: [],
          search: true,
          source: 'affiliates',
          map: {
            type: 'filter[aff.affType=]'
          }
        }, {
          value: 'transactionDateFrom',
          label: 'Decline date',
          range: [
            {
              value: 'range[ucl.txnTime][start]',
              type: 'date'
            }, {
              value: 'range[ucl.txnTime][end]',
              type: 'date'
            }
          ]
        }, {
          value: 'txn_amount',
          label: 'Transaction Amount',
          range: [{
            value: 'range[ucl.txnAmount][min]',
            type: 'amount'
          }, {
            value: 'range[ucl.txnAmount][max]',
            type: 'amount'
          }]
        }
      ]
    }
  },
  methods: {
    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.$refs.filtersDialog.getFilters()
      data.keyword = this.keyword
      const resp = await request(`/admin/card_declines_list/${pagination.page}/${pagination.rowsPerPage}`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.total
        this.quick = resp.data.quick
      }
    },
    init () {
      this.data = []
      this.quick = {}
      this.filters = []
      this.keyword = ''

      this.columns = this.baseColumns
    },
    isPrivacyTxn (row) {
      if (!row || !row.tranId) {
        return false
      }
      if (row.cardProgramName !== 'US Unlocked') {
        return false
      }
      if (row.tranId.indexOf('-') < 0) {
        return false
      }
      return true
    },
    viewDetails (row) {
      window.open(`/admin/card-transaction/view-details/${row.tranId}`, '_blank')
    }
  }
}
</script>
