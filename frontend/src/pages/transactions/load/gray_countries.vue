<template>
  <q-dialog class="gray-countries-management-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Gray Countries Management</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="gray-country-option">
        <q-select float-label="Choose a country"
                  autocomplete="no"
                  :options="countries"
                  v-model="country"></q-select>
        <span class="add-icon"
              @click="addGrayCountry"><i class="mdi mdi-plus"></i></span>
      </div>
      <q-table :data="grayCountries"
               :columns="columns"
               hide-bottom
               :pagination.sync="paginitionFile"
               class="mt-16"
               separator="none">
        <q-tr slot="body"
              slot-scope="props"
              :props="props">
          <q-td v-for="col in props.cols"
                :key="col.field">
            <template v-if="col.field ==='Action'">
              <div notranslate=""><span @click="deleteSelect(props.row)"
                      class="delete-icon"><i class="mdi mdi-delete-outline"></i></span></div>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

      </q-table>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { generateColumns, request, notifySuccess, toSelectOptions, notifyResponse } from '../../../common'

export default {
  name: 'gray-countries-management-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      countries: [],
      country: null,
      paginitionFile: {
        page: 1,
        rowsPerPage: 10
      },
      grayCountries: [],
      columns: generateColumns(['Name', 'ISO', 'ISO3', 'Action'], [], [])
    }
  },
  methods: {
    async show () {
      this.$q.loading.show({
        message: 'Loading...'
      })
      const resp = await request(`/admin/gray_countries/search`, 'get', {})
      if (resp.success) {
        this.grayCountries = resp.data.grayCountries
        this.countries = toSelectOptions(resp.data.countries)
      }
      this.$q.loading.hide()
    },
    async addGrayCountry () {
      if (!this.country) {
        return
      }
      this.$q.loading.show()
      const resp = await request(`/admin/gray_countries/add`, 'post', {
        id: this.country
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.country = null
        this.show()
        notifySuccess()
      } else {
        notifyResponse(resp)
      }
    },
    async deleteSelect (row) {
      this.$q.loading.show()
      const resp = await request(`/admin/gray_countries/remove`, 'post', {
        id: row['ID']
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.show()
        notifySuccess()
      } else {
        notifyResponse(resp)
      }
    }
  }
}
</script>

<style lang="scss">
.gray-countries-management-dialog {
  .modal-content {
    position: relative;
    background: #fff;
    width: 450px;
    border-radius: 16px;
    overflow-y: auto;
    will-change: scroll-position;
    min-width: 280px;
    .modal-header {
      text-align: center;
      font-size: 18px;
      font-weight: 500;
      .close {
        position: absolute;
        right: 10px;
        top: 10px;
      }
    }
    .modal-body {
      color: #171726;
      margin-bottom: 20px;
      .gray-country-option {
        display: flex;
        align-items: baseline;
        justify-content: space-between;
        .q-select {
          width: calc(100% - 20px);
        }
        i {
          font-size: 18px;
          cursor: pointer;
        }
      }
      .delete-icon,
      .add-icon {
        display: flex;
        align-items: center;
        cursor: pointer;
        // justify-content: center;
        padding: 5px;
        font-size: 16px;
        border-radius: 10px;
        border: 1px solid #f1f1f5;
        i {
          margin: 0 auto;
        }
      }
    }
    .modal-buttons {
      display: none;
    }
  }
}
</style>
