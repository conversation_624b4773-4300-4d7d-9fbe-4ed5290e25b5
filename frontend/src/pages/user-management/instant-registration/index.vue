<template>
  <q-page id="user_management__instant_registration_page" class="form-page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="group-header">Basic Info</div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Card Program" label-width="4" class="required">
            <q-select v-model="entity.cardProgramId"
                      @blur="$v.entity.cardProgramId.$touch"
                      :error="$v.entity.cardProgramId.$error"
                      :options="cardPrograms"></q-select>
          </q-field>
        </div>
        <div class="col-6">
          <q-field label="KYC Update" label-width="4">
            <q-toggle v-model="entity.isKycUpdate"></q-toggle>
          </q-field>
        </div>
      </div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Card Processor Unique Id" label-width="4" class="required">
            <q-input v-model="entity.cardProcessorUniqueId"></q-input>
          </q-field>
        </div>
        <div class="col-6" v-if="shipping">
          <q-field label="Card Shipping Method" label-width="4" class="required">
            <q-select v-model="entity.cardShippingMethod"
                      :options="$c.esSolo.cardShippingMethods"
                      @blur="$v.entity.cardShippingMethod.$touch"
                      :error="$v.entity.cardShippingMethod.$error"></q-select>
          </q-field>
        </div>
      </div>
      <div class="group-header">User Info</div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="First Name" label-width="4" class="required">
            <q-input v-model="entity.firstName"
                     @blur="$v.entity.firstName.$touch"
                     :error="$v.entity.firstName.$error"></q-input>
          </q-field>
        </div>
        <div class="col-6">
          <q-field label="Last Name" label-width="4" class="required">
            <q-input v-model="entity.lastName"
                     @blur="$v.entity.lastName.$touch"
                     :error="$v.entity.lastName.$error"></q-input>
          </q-field>
        </div>
      </div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Birthday" label-width="4" class="required">
            <q-datetime v-model="entity.birthday"
                        @blur="$v.entity.birthday.$touch"
                        :error="$v.entity.birthday.$error"></q-datetime>
          </q-field>
        </div>
        <div class="col-6">
          <q-field label="Gender" label-width="4" class="required">
            <q-option-group
              v-model="entity.gender"
              :options="$c.genders"
              @blur="$v.entity.gender.$touch"
              :error="$v.entity.gender.$error"
              color="primary"
              inline
              dense
            />
          </q-field>
        </div>
      </div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Email Address" label-width="4" class="required">
            <q-input v-model="entity.emailAddress"
                     @blur="$v.entity.emailAddress.$touch"
                     :error="$v.entity.emailAddress.$error"></q-input>
          </q-field>
        </div>
        <div class="col-6">
          <q-field label="Home Phone" label-width="4" class="required">
            <q-input v-model="entity.homePhone"
                     @blur="$v.entity.homePhone.$touch"
                     :error="$v.entity.homePhone.$error"></q-input>
          </q-field>
        </div>
      </div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Residence Address" label-width="4" class="required">
            <q-input v-model="entity.residenceAddress"
                     @blur="$v.entity.residenceAddress.$touch"
                     :error="$v.entity.residenceAddress.$error"></q-input>
          </q-field>
        </div>
        <div class="col-6">
          <q-field label="Residence Address 2" label-width="4">
            <q-input v-model="entity.residenceAddress2"></q-input>
          </q-field>
        </div>
      </div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Residence Country" label-width="4" class="required">
            <q-select v-model="entity.residenceCountryId"
                      :options="countries"
                      filter
                      @blur="$v.entity.residenceCountryId.$touch"
                      :error="$v.entity.residenceCountryId.$error"></q-select>
          </q-field>
        </div>
        <div class="col-6">
          <q-field label="Residence State Province" label-width="4" class="required">
            <q-select v-model="entity.residenceStateProvinceId"
                      :options="residenceStates"
                      filter
                      @blur="$v.entity.residenceStateProvinceId.$touch"
                      :error="$v.entity.residenceStateProvinceId.$error"></q-select>
          </q-field>
        </div>
      </div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Residence City" label-width="4" class="required">
            <q-input v-model="entity.residenceCity"
                     @blur="$v.entity.residenceCity.$touch"
                     :error="$v.entity.residenceCity.$error"></q-input>
          </q-field>
        </div>
        <div class="col-6">
          <q-field label="Residence Postal Code" label-width="4" class="required">
            <q-input v-model="entity.residencePostalCode"
                     @blur="$v.entity.residencePostalCode.$touch"
                     :error="$v.entity.residencePostalCode.$error"></q-input>
          </q-field>
        </div>
      </div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Mobile Phone" label-width="4">
            <q-input v-model="entity.mobilePhone"></q-input>
          </q-field>
        </div>
        <div class="col-6">
          <q-field label="Work Phone" label-width="4">
            <q-input v-model="entity.workPhone"></q-input>
          </q-field>
        </div>
      </div>
      <div class="group-header">Id Verification Info</div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Address Proof Type" label-width="4" class="required">
            <q-select v-model="entity.proofAddressTypeId"
                      :options="$c.esSolo.addressProofTypes"
                      @blur="$v.entity.proofAddressTypeId.$touch"
                      :error="$v.entity.proofAddressTypeId.$error"></q-select>
          </q-field>
        </div>
        <div class="col-6">
          <q-field label="Address Proof" label-width="4" class="required">
            <input type="file"
                   ref="proofOfAddress"
                   @change="changedFile('proofOfAddress', $event)"
                   accept=".jpg, .png, .pdf"/>
          </q-field>
        </div>
      </div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Shipping Address" label-width="4" :class="{required: shipping}">
            <q-input v-model="entity.shippingAddress1"
                     @blur="$v.entity.shippingAddress1.$touch"
                     :error="$v.entity.shippingAddress1.$error"></q-input>
          </q-field>
        </div>
        <div class="col-6">
          <q-field label="Shipping Address 2" label-width="4">
            <q-input v-model="entity.shippingAddress2"></q-input>
          </q-field>
        </div>
      </div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Shipping Country" label-width="4" :class="{required: shipping}">
            <q-select v-model="entity.shippingCountryId"
                      filter
                      @blur="$v.entity.shippingCountryId.$touch"
                      :error="$v.entity.shippingCountryId.$error"
                      :options="countries"></q-select>
          </q-field>
        </div>
        <div class="col-6">
          <q-field label="Shipping State Province" label-width="4" :class="{required: shipping}">
            <q-select v-model="entity.shippingStateProvinceId"
                      filter
                      @blur="$v.entity.shippingStateProvinceId.$touch"
                      :error="$v.entity.shippingStateProvinceId.$error"
                      :options="shippingStates"></q-select>
          </q-field>
        </div>
      </div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Shipping City" label-width="4" :class="{required: shipping}">
            <q-input v-model="entity.shippingCity"
                     @blur="$v.entity.shippingCity.$touch"
                     :error="$v.entity.shippingCity.$error"></q-input>
          </q-field>
        </div>
        <div class="col-6">
          <q-field label="Shipping Postal Code" label-width="4" :class="{required: shipping}">
            <q-input v-model="entity.shippingPostalCode"
                     @blur="$v.entity.shippingPostalCode.$touch"
                     :error="$v.entity.shippingPostalCode.$error"></q-input>
          </q-field>
        </div>
      </div>
      <div class="row gutter-lg">
        <div class="col-6">
          <q-field label="Id Type" label-width="4" class="required">
            <q-select v-model="entity.idTypeId"
                      :options="$c.esSolo.idTypes"
                      @blur="$v.entity.idTypeId.$touch"
                      :error="$v.entity.idTypeId.$error"></q-select>
          </q-field>
        </div>
        <div class="col-6">
          <q-field label="Id File" label-width="4" class="required">
            <input type="file"
                   ref="idUpload"
                   @change="changedFile('idUpload', $event)"
                   accept=".jpg, .png, .pdf"/>
          </q-field>
        </div>
      </div>
      <div class="row gutter-lg flow">
        <div class="col-6" v-if="usa">
          <q-field label="SSN" label-width="4" class="required">
            <q-input v-model="entity.ssn"
                     @blur="$v.entity.ssn.$touch"
                     :error="$v.entity.ssn.$error"></q-input>
          </q-field>
        </div>
        <div class="col-6" v-if="entity.idTypeId === 1">
          <q-field label="Driver License No" label-width="4" class="required">
            <q-input v-model="entity.driverLicenseNo"
                     @blur="$v.entity.driverLicenseNo.$touch"
                     :error="$v.entity.driverLicenseNo.$error"></q-input>
          </q-field>
        </div>
        <div class="col-6" v-if="entity.idTypeId === 3">
          <q-field label="Passport Number" label-width="4" class="required">
            <q-input v-model="entity.passportNumber"
                     @blur="$v.entity.passportNumber.$touch"
                     :error="$v.entity.passportNumber.$error"></q-input>
          </q-field>
        </div>
        <div class="col-6" v-if="entity.idTypeId === 3">
          <q-field label="Country of Passport" label-width="4" class="required">
            <q-select v-model="entity.countryOfPassport"
                      :options="countries"
                      @blur="$v.entity.countryOfPassport.$touch"
                      :error="$v.entity.countryOfPassport.$error"></q-select>
          </q-field>
        </div>
        <div class="col-6" v-if="!([1, 3].includes(entity.idTypeId))">
          <q-field label="National Id" label-width="4" class="required">
            <q-input v-model="entity.nationalId"
                     @blur="$v.entity.nationalId.$touch"
                     :error="$v.entity.nationalId.$error"></q-input>
          </q-field>
        </div>
      </div>
      <div class="row mt-30">
        <q-btn label="Submit"
               color="primary"
               @click="submit"
               icon="mdi-send"></q-btn>
      </div>
    </div>

    <q-inner-loading :visible="loading">
      <q-spinner :size="50"></q-spinner>
    </q-inner-loading>
  </q-page>
</template>

<script>
import PageMixin from '../../../mixins/PageMixin'
import { request, toSelectOptions } from '../../../common'
import { required, requiredIf } from 'vuelidate/lib/validators'
import _ from 'lodash'
import StateListMixin from '../../../mixins/StateListMixin'

export default {
  name: 'UserInstantRegistrationIndex',
  mixins: [
    PageMixin,
    StateListMixin
  ],
  data () {
    return {
      title: 'Instant Registration',
      entity: {
        isKycUpdate: false,
        cardShippingMethod: 1
      },
      dataSource: {},
      cardPrograms: [],
      countries: [],
      residenceStates: [],
      shippingStates: []
    }
  },
  computed: {
    deliveryMethod () {
      if (this.entity.cardProgramId && this.dataSource.cardPrograms) {
        const cp = _.find(this.dataSource.cardPrograms, {
          id: this.entity.cardProgramId
        })
        if (cp) {
          return cp.deliveryMethod
        }
      }
      return null
    },
    shipping () {
      return this.deliveryMethod === 'mail'
    },
    usa () {
      const countryId = this.entity.residenceCountryId
      if (countryId && this.dataSource.countries) {
        const country = _.find(this.dataSource.countries, {
          id: countryId
        })
        if (country) {
          return country.iso3 === 'USA'
        }
      }
      return false
    }
  },
  watch: {
    async 'entity.residenceCountryId' (v) {
      this.residenceStates = await this.loadStates(v)
      this.$set(this.entity, 'residenceStateProvinceId', null)
    },
    async 'entity.shippingCountryId' (v) {
      this.shippingStates = await this.loadStates(v)
      this.$set(this.entity, 'shippingStateProvinceId', null)
    }
  },
  validations: {
    entity: {
      cardProgramId: { required },
      cardShippingMethod: {
        required: requiredIf(function (n) {
          return this.shipping
        })
      },
      firstName: { required },
      lastName: { required },
      birthday: { required },
      gender: { required },
      emailAddress: { required },
      residenceAddress: { required },
      residenceCountryId: { required },
      residenceStateProvinceId: { required },
      residenceCity: { required },
      residencePostalCode: { required },
      homePhone: { required },
      proofAddressTypeId: { required },
      proofOfAddress: { required },
      shippingAddress1: {
        required: requiredIf(function (n) {
          return this.shipping
        })
      },
      shippingCountryId: {
        required: requiredIf(function (n) {
          return this.shipping
        })
      },
      shippingStateProvinceId: {
        required: requiredIf(function (n) {
          return this.shipping
        })
      },
      shippingCity: {
        required: requiredIf(function (n) {
          return this.shipping
        })
      },
      shippingPostalCode: {
        required: requiredIf(function (n) {
          return this.shipping
        })
      },
      idTypeId: { required },
      idUpload: { required },
      ssn: {
        required: requiredIf(function () {
          return this.usa
        })
      },
      driverLicenseNo: {
        required: requiredIf(function () {
          return this.entity.idTypeId === 1
        })
      },
      passportNumber: {
        required: requiredIf(function () {
          return this.entity.idTypeId === 3
        })
      },
      countryOfPassport: {
        required: requiredIf(function () {
          return this.entity.idTypeId === 3
        })
      },
      nationalId: {
        required: requiredIf(function () {
          return !([1, 3].includes(this.entity.idTypeId))
        })
      }
    }
  },
  methods: {
    async reload () {
      this.loading = true
      const resp = await request(`/admin/essolo/instant-registration/data`)
      this.loading = false
      if (resp.success) {
        this.dataSource = resp.data
        this.cardPrograms = toSelectOptions(resp.data.cardPrograms)
        this.countries = toSelectOptions(resp.data.countries)
      }
    },
    async submit () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return
      }
      this.loading = true
      const resp = await request(`/admin/essolo/instant-registration/submit`, 'file', this.entity)
      this.loading = false
      if (resp.success) {
        this.$q.dialog({
          title: 'Message',
          message: `${resp.message} Would you like to view the user details now?`,
          ok: 'Yes',
          cancel: 'No'
        }).then(() => {
          this.resetForm()
          this.$router.push(`/a/i/admin__user_modify__modify?user_id=${resp.data.userId}`)
        }).catch(() => {
          this.resetForm()
        })
      }
    },
    resetForm () {
      this.entity = {
        isKycUpdate: false,
        cardShippingMethod: 1
      }

      for (const ref of ['proofOfAddress', 'idUpload']) {
        if (this.$refs[ref]) {
          this.$refs[ref].type = 'text'
          this.$refs[ref].type = 'file'
        }
      }

      this.$v.$reset()
    },
    changedFile (field, $event) {
      if (!$event.target.files.length) {
        return
      }
      this.entity[field] = $event.target.files[0]
    }
  },
  mounted () {
    this.reload()
  }
}
</script>

<style lang="scss">
  #user_management__instant_registration_page {
  }
</style>
