<template>
  <q-page id="user_management__kyc_exceptions_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat round dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true"/>
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <StickyHead slot="header"
                    slot-scope="props"
                    :freezeColumn.sync="freezeColumn"
                    :columnStyles="columnStyles"
                    :props="props"></StickyHead>

        <q-tr slot="body" slot-scope="props" :props="props"
              :class="{even: props.row.__index % 2}">
          <q-td v-for="(col, i) in props.cols" :key="col.field" :align="col.align"
                :style="columnStyle(i)"
                :class="{freeze: i <= freezeColumn, 'last-freeze': i === freezeColumn}">
            <template v-if="['id', 'firstName', 'lastName', 'email', 'phone', 'country', 'state', 'city'].includes(col.field)">
              <span v-html="$options.filters.searchMatch(_.get(props.row, col.field), keyword)"></span>
            </template>
            <template v-else-if="col.field === 'userId' && p('user')">
              <a :href="`/admin#/a/i/admin__user_modify__modify?user_id=${col.value}`"
                 target="_blank">{{ col.value }}</a>
            </template>
            <template v-else-if="col.field === 'frontImage' || col.field === 'backImage'">
              <a :href="`${col.value}`" v-if="col.value" target="_blank">View</a>
            </template>
            <template v-else-if="col.field === 'result'">
              <a href="javascript:" @click="viewResult(col.value)" v-if="col.value">View</a>
            </template>
            <template v-else-if="!col.field">
              <q-btn-dropdown size="xs"
                              v-if="props.row._editable"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'ineligible')">
                    <q-item-main>Change to "Not Eligible for Card"</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'manual_accepted')">
                    <q-item-main>Change to "Manual Approved"</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <PreDialog></PreDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, request, notifySuccess, notify } from '../../../common'
import PreDialog from '../../../components/PreDialog'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import _ from 'lodash'

export default {
  name: 'UserKycExceptionsIndex',
  mixins: [
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('admin_user_kyc_exceptions_search', 'search')
  ],
  components: {
    PreDialog
  },
  data () {
    return {
      title: 'KYC Exception Pool',
      requestUrl: '/admin/user_management/kyc_exceptions/list',
      filtersUrl: '/admin/user_management/kyc_exceptions/filters',
      autoLoad: true,
      autoReloadWhenUrlChanges: true,
      columns: [],
      quick: {
        count: 0
      },
      filterOptions: [
        {
          value: 'filter[ei.requestKey=]',
          label: 'Registration Request ID'
        },
        {
          value: 'filter[p.id=]',
          label: 'KYC Provider',
          options: [],
          source: 'kycProviders'
        },
        {
          value: 'filter[u.id=]',
          label: 'User Id'
        },
        {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        },
        {
          value: 'filter[u.lastName=]',
          label: 'First Name'
        },
        {
          value: 'filter[u.email=]',
          label: 'Email'
        },
        {
          value: 'filter[u.phone=]',
          label: 'Home Phone'
        },
        {
          value: 'filter[u.workphone=]',
          label: 'Work Phone'
        },
        {
          value: 'filter[u.mobilephone=]',
          label: 'Mobile Phone'
        },
        {
          value: 'filter[u.country=]',
          label: 'Country',
          options: [],
          source: 'countries',
          search: true
        },
        {
          value: 'filter[u.state=]',
          label: 'State',
          options: [],
          search: true,
          source: 'states',
          map: {
            country: 'filter[u.country=]'
          }
        },
        {
          value: 'filter[b.status=]',
          label: 'KYC Exception Status',
          options: [
            {
              label: 'All',
              value: ''
            }, {
              label: 'KYC Failed',
              value: 'invalid'
            }, {
              label: 'Not Eligible for Card',
              value: 'ineligible'
            }, {
              label: 'Manually Approved',
              value: 'manual_accepted'
            }
          ]
        }
      ]
    }
  },
  methods: {
    async changeStatus (item, status) {
      const statusText = _.startCase(status)
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to change the status to "${statusText}"? Please leave a note before continue.`,
        cancel: true,
        prompt: {
          model: '',
          type: 'text'
        }
      }).then(async note => {
        if (!note || !note.trim()) {
          notify('Operation cancelled.', 'negative')
          return
        }
        this.$q.loading.show()
        const resp = await request(`/admin/user_management/kyc_exceptions/change_status/${item.id}/${status}`, 'post', {
          note
        })
        this.$q.loading.hide()
        if (resp.success) {
          notifySuccess(resp)
          this.reload()
        }
      }).catch(() => {})
    },
    viewResult (result) {
      result = JSON.parse(result)
      this.$root.$emit('show-pre-dialog', {
        title: 'Result Detail',
        content: JSON.stringify(result, null, 4)
      })
    }
  },
  mounted () {
    const esSolo = this.$store.state.User.cpKey === 'cp_es_solo'
    if (esSolo) {
      this.columns = [
        {
          field: 'requestId',
          label: 'Reg. Request ID',
          align: 'left'
        }, {
          field: 'date',
          label: 'Date',
          align: 'left'
        }, {
          field: 'programOwner',
          label: 'Program Owner',
          align: 'left'
        }, {
          field: 'cardProgram',
          label: 'Card Program Name',
          align: 'left'
        }, {
          field: 'cardProgramId',
          label: 'Card Program Id',
          align: 'left'
        }, {
          field: 'provider',
          label: 'KYC Provider',
          align: 'left'
        }, {
          field: 'providerId',
          label: 'KYC Provider Program ID',
          align: 'left'
        }, {
          field: 'issuingBank',
          label: 'Issuing Bank',
          align: 'left'
        }, {
          field: 'processor',
          label: 'Processor',
          align: 'left'
        }, {
          field: 'userId',
          label: 'User ID',
          align: 'left'
        }, {
          field: 'firstName',
          label: 'First Name',
          align: 'left'
        }, {
          field: 'lastName',
          label: 'Last Name',
          align: 'left'
        }, {
          field: 'email',
          label: 'Email',
          align: 'left'
        }, {
          field: 'phone',
          label: 'Phone Number',
          align: 'left'
        }, {
          field: 'country',
          label: 'Country',
          align: 'left'
        }, {
          field: 'state',
          label: 'State/Province',
          align: 'left'
        }, {
          field: 'city',
          label: 'City',
          align: 'left'
        }, {
          field: 'statusText',
          label: 'KYC Exception Status',
          align: 'left'
        },
        {
          field: '',
          label: 'Action',
          align: 'left'
        }
      ]
    } else {
      this.columns = [
        {
          field: 'userId',
          label: 'User ID',
          align: 'left'
        }, {
          field: 'firstName',
          label: 'First Name',
          align: 'left'
        }, {
          field: 'lastName',
          label: 'Last Name',
          align: 'left'
        }, {
          field: 'email',
          label: 'Email',
          align: 'left'
        }, {
          field: 'phone',
          label: 'Phone Number',
          align: 'left'
        }, {
          field: 'country',
          label: 'Country',
          align: 'left'
        }, {
          field: 'state',
          label: 'State/Province',
          align: 'left'
        }, {
          field: 'city',
          label: 'City',
          align: 'left'
        }, {
          field: 'programOwner',
          label: 'Program Owner',
          align: 'left'
        }, {
          field: 'cardProgram',
          label: 'Card Program Name',
          align: 'left'
        }, {
          field: 'cardProgramId',
          label: 'Card Program Id',
          align: 'left'
        }, {
          field: 'provider',
          label: 'KYC Provider',
          align: 'left'
        }, {
          field: 'issuingBank',
          label: 'Issuing Bank',
          align: 'left'
        }, {
          field: 'processor',
          label: 'Processor',
          align: 'left'
        }, {
          field: 'requestId',
          label: 'Reg. Request ID',
          align: 'left'
        }, {
          field: 'date',
          label: 'Date',
          align: 'left'
        }, {
          field: 'kycCountry',
          label: 'KYC Country',
          align: 'left'
        },
        {
          field: 'type',
          label: 'KYC Type',
          align: 'left'
        },
        {
          field: 'frontImage',
          label: 'Front Image',
          align: 'left'
        },
        {
          field: 'backImage',
          label: 'Back Image',
          align: 'left'
        },
        {
          field: 'reason',
          label: 'Reason',
          align: 'left'
        },
        {
          field: 'result',
          label: 'Result Detail',
          align: 'left'
        }, {
          field: 'note',
          label: 'Note',
          align: 'left'
        }, {
          field: 'statusText',
          label: 'KYC Exception Status',
          align: 'left'
        },
        {
          field: '',
          label: 'Action',
          align: 'left'
        }
      ]
    }

    this.filters = [{
      field: 'filter[b.status=]',
      predicate: '=',
      value: 'invalid'
    }]
  }
}
</script>
