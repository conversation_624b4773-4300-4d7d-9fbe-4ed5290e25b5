<template>
  <q-page id="user_management__user_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-btn flat
               round
               @click="download('min')"
               icon="mdi-content-save">
          <q-tooltip>Export minimal fields</q-tooltip>
        </q-btn>
        <q-btn flat
               round
               class="ml-5"
               @click="download"
               icon="mdi-content-save-all">
          <q-tooltip>Export all the fields</q-tooltip>
        </q-btn>
        <div class="divider"></div>
        <q-btn flat
               v-if="c.isSuperAdmin(user) || p('user_managment__user__add_user')"
               @click="$router.push('/a/i/admin__user_new__new')"
               icon="mdi-plus"
               label="Add User"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div class="max-w-300">
          <h5>{{ quick.count | number }}</h5>
          <div class="description">Total # of users</div>
        </div>
      </div>
      <TopChart :chart-id="chartId"
                v-if="chartData">
      </TopChart>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props"
              :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :class="`text-${col.align || 'left'}`">
            <template v-if="col.field === 'status'">
              {{ col.value | capitalize }}
            </template>
            <template v-else-if="col.field === 'username' && props.row.emailInactive">
              <span class="text-negative">{{ col.value }}</span>
            </template>
            <template v-else-if="col.field === 'lastLoginAt'">
              {{ col.value | date('L LT') }}
            </template>
            <template v-else-if="['accountBalance', 'legacyBalance'].includes(col.field)">
              <span v-if="col.value">{{ col.value | moneyFormat }}</span>
            </template>
            <template v-else-if="col.field === 'teams'">
              {{ col.value | join(', ') }}
            </template>
            <template v-else-if="!col.field">
              <q-btn-dropdown size="xs"
                              v-if="props.row._own"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          :to="`/a/i/admin__user_view__userview?user_id=${props.row.id}`">
                    <q-item-main>View</q-item-main>
                  </q-item>
                   <q-item v-if="!props.row.paypalSubscription"
                          v-close-overlay
                          @click.native="waiveSubscriptionFee(props.row)">
                    <q-item-main>Waive Subscription Fee</q-item-main>
                  </q-item>
                  <q-item v-if="!props.row.paypalSubscription"
                          v-close-overlay
                          @click.native="addPaypalSubscript(props.row)">
                    <q-item-main>Add Paypal Subscription</q-item-main>
                  </q-item>
<!--                  <q-item v-close-overlay-->
<!--                          v-if="!props.row.internalTester"-->
<!--                          @click.native="addAsInternalTester(props.row, 'add')">-->
<!--                    <q-item-main>Add as an Internal Tester</q-item-main>-->
<!--                  </q-item>-->
<!--                  <q-item v-close-overlay-->
<!--                          v-else-if="props.row.internalTester === true"-->
<!--                          @click.native="addAsInternalTester(props.row, 'remove')">-->
<!--                    <q-item-main>Remove from Internal Testers</q-item-main>-->
<!--                  </q-item>-->
                  <q-item v-if="props.row.emailInactive"
                          v-close-overlay
                          @click.native="removeBlackList(props.row)">
                    <q-item-main>Remove from blacklist</q-item-main>
                  </q-item>
                  <template v-if="e('user')">
                    <q-item v-close-overlay
                            @click.native="editUser(props.row)">
                      <q-item-main>Edit</q-item-main>
                    </q-item>
                    <template v-if="esSoloMenu">
                      <q-item v-close-overlay
                              v-if="false">
                        <q-item-main>Card Details</q-item-main>
                      </q-item>
                      <q-item v-close-overlay
                              v-if="false">
                        <q-item-main>Card Transactions</q-item-main>
                      </q-item>
                      <q-item v-close-overlay
                              @click.native="showLoadUnloadDialog(props.row)">
                        <q-item-main>Load/Unload Card</q-item-main>
                      </q-item>
                      <q-item v-close-overlay
                              @click.native="updateGtpPhone(props.row)">
                        <q-item-main>GTP Phone Number</q-item-main>
                      </q-item>
                      <q-item v-close-overlay
                              v-if="false">
                        <q-item-main>Card to Card Transfer</q-item-main>
                      </q-item>
                    </template>
                    <template v-else>
                      <q-item v-if="c.isConsumer(props.row)"
                              v-close-overlay
                              :to="`/a/i/admin__user-discount?user_id=${props.row.id}`">
                        <q-item-main>Discount Settings</q-item-main>
                      </q-item>
                      <q-item v-if="p('user_managment__user__login_at') && !isGranted && esSoloLoginAs(props.row)"
                              v-close-overlay
                              @click.native="$c.loginAs(props.row.id)">
                        <q-item-main>Login As</q-item-main>
                      </q-item>
                      <q-item v-close-overlay
                              v-if="props.row.usuDummy"
                              :to="`/a/i/admin__card-balance?user_card_id=${props.row.usuDummy}`">
                        <q-item-main>Card Balance History</q-item-main>
                      </q-item>
                      <q-item v-close-overlay
                              @click.native="c.deleteConfirm(`/admin/user_delete/delete?user_id=${props.row.id}`, reload)">
                        <q-item-main>Delete</q-item-main>
                      </q-item>
                      <q-item v-close-overlay
                              v-if="p('user_managment__user__login_at')"
                              @click.native="c.deleteConfirm(`/admin/user_delete/erase/${props.row.id}`, reload)">
                        <q-item-main>Delete and Erase data</q-item-main>
                      </q-item>
                      <q-item v-close-overlay
                              :to="`/a/i/admin__user-ipusage?user_id=${props.row.id}`">
                        <q-item-main>IP Usage</q-item-main>
                      </q-item>
                      <q-item v-if="p('user_managment__user__sms_pin')"
                              v-close-overlay
                              :to="`/a/i/admin__user-smspin?user_id=${props.row.id}`">
                        <q-item-main>SMS PIN</q-item-main>
                      </q-item>
                      <q-item v-if="c.isConsumer(props.row) && props.row._fv"
                              v-close-overlay
                              :to="`/a/i/admin__user__fv-auto-create-log__${props.row.id}`">
                        <q-item-main>FV Auto-Create Log</q-item-main>
                      </q-item>
                      <q-item v-if="props.row['sumsubKyc'] == 'Reject'" v-close-overlay
                              @click.native="c.deleteConfirm(`/admin/user_reset/sumsub?user_id=${props.row.id}`, reload)">
                        <q-item-main>Reset Sumsub KYC</q-item-main>
                      </q-item>
                    </template>
                  </template>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"
                       :max="downloadMax"></BatchExportDialog>

    <template v-if="esSoloMenu">
      <CardLoadUnloadDialog></CardLoadUnloadDialog>
    </template>
  </q-page>
</template>

<script>
import { request, EventHandlerMixin, notify, notifyForm } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import UserMenuMixin from '../../../mixins/esSolo/UserMenuMixin'
import { chartName } from '../../../const'
import eventBus from '../../../eventBus'

export default {
  mixins: [
    ListPageMixin,
    UserMenuMixin,
    EventHandlerMixin('admin_user_quick_search', 'search')
  ],
  data () {
    return {
      title: 'User Management',
      url: '/a/user-management/user',
      filtersUrl: '/admin/user_search/filters',
      downloadUrl: '/admin/user_search/export',
      downloadMax: 1000,
      timeField: 'ucCreated',
      cardProgramField: 'cardprogramselect',
      chartId: chartName.USER_CHART,
      autoReloadWhenUrlChanges: true,
      columns: [
        {
          field: 'id',
          label: 'ID',
          align: 'left'
        }, {
          field: 'username',
          label: 'Username',
          align: 'left'
        }, {
          field: 'firstName',
          label: 'First Name',
          align: 'left'
        }, {
          field: 'lastName',
          label: 'Last Name',
          align: 'left'
        }, {
          field: 'country',
          label: 'Country',
          align: 'left'
        }, {
          field: 'lastLoginAt',
          label: 'Last Login Time',
          align: 'left',
          hidden: true
        }, {
          field: 'legacyBalance',
          label: 'Legacy Balance',
          align: 'right'
        }, {
          field: 'accountBalance',
          label: 'Account Balance',
          align: 'right'
        }, {
          field: 'teams',
          label: 'Account Type',
          align: 'left'
        }, {
          field: 'sumsubKyc',
          label: 'Sumsub KYC',
          align: 'left'
        }, {
          field: 'paypalSubscription',
          label: 'PayPal Subscription Status',
          align: 'left'
        }, {
          field: 'paypalSubscriptionId',
          label: 'PayPal Subscription ID',
          align: 'left'
        }, {
          field: 'paypalSubscriptionType',
          label: 'PayPal Subscription Type',
          align: 'left'
        }, {
          field: 'status',
          label: 'Status',
          align: 'left'
        }, {
          field: 'legacy',
          label: 'Legacy User',
          align: 'left'
        }, {
          label: 'Actions',
          align: 'center'
        }
      ],
      quick: {
        count: 0
      },
      filterOptions: [
        {
          value: 'useridx',
          label: 'User ID'
        }, {
          value: 'firstname',
          label: 'First Name'
        }, {
          value: 'lastname',
          label: 'Last Name'
        }, {
          value: 'email',
          label: 'Email'
        }, {
          value: 'address',
          label: 'Address'
        }, {
          value: 'phone',
          label: 'Phone'
        }, {
          value: 'accountnumber',
          label: 'Card Account Number'
        }, {
          value: 'iddocumentnumber',
          label: 'ID Doc Number'
        }, {
          value: 'processorselect',
          label: 'Processor',
          options: [],
          source: 'processor'
        }, {
          value: 'programmanagerselect',
          label: 'Program Manager',
          options: [],
          source: 'programmanager'
        }, {
          value: 'countryselect',
          label: 'Country',
          search: true,
          options: [],
          source: 'countrys'
        }, {
          value: 'stateselect',
          label: 'State',
          options: [],
          search: true,
          source: 'states',
          map: {
            country: 'countryselect'
          }
        }, {
          value: 'city',
          label: 'City'
        }, {
          value: 'cardtypeselect',
          label: 'Card Type',
          options: [],
          source: 'cardtype'
        }, {
          value: 'accountselect',
          label: 'Role',
          options: [],
          source: 'roles'
        }, {
          value: 'cardprogramselect',
          label: 'Card Program',
          options: [],
          source: 'cardprograms'
        }, {
          value: 'accountstatusselect',
          label: 'Account Status',
          options: [],
          source: 'accountStatus'
        }, {
          value: 'flagsselect',
          label: 'Flags',
          options: [],
          source: 'flags'
        }, {
          value: 'cardissuedselect',
          label: 'Card issued',
          options: [
            {
              label: 'Yes',
              value: 'y'
            }, {
              label: 'No',
              value: 'n'
            }
          ]
        }, {
          value: 'cardstatusselect',
          label: 'Card Status',
          options: [
            {
              label: 'Active',
              value: 'active'
            }, {
              label: 'Inactive',
              value: 'inactive'
            }
          ]
        }, {
          value: 'billingstatusselect',
          label: 'Billing Address Status',
          options: [
            {
              label: 'Valid',
              value: 'valid'
            }, {
              label: 'Invalid',
              value: 'invalid'
            }
          ]
        }, {
          value: 'billingtypeselect',
          label: 'Billing Address Type',
          options: [],
          source: 'reshipperaddre'
        }, {
          value: 'genderselect',
          label: 'Gender',
          options: [
            {
              label: 'Male',
              value: 'Male'
            }, {
              label: 'Female',
              value: 'Female'
            }, {
              label: 'Not Specified',
              value: 'not_specified'
            }
          ]
        }, {
          value: 'regionselect',
          label: 'Region',
          options: [],
          source: 'regions'
        }, {
          value: 'kycProvider',
          label: 'KYC Provider',
          options: [],
          source: 'kycProviders'
        }, {
          value: 'idstatusselect',
          label: 'ID verification status',
          options: [],
          source: 'idStatus'
        }, {
          value: 'networktype',
          label: 'Network type',
          options: [],
          source: 'networks'
        }, {
          value: 'age',
          label: 'Age',
          options: [
            {
              label: '0-10',
              value: '1_10'
            }, {
              label: '11-20',
              value: '11_20'
            }, {
              label: '21-30',
              value: '21_30'
            }, {
              label: '31-40',
              value: '31_40'
            }, {
              label: '41-50',
              value: '41_50'
            }, {
              label: '51-60',
              value: '51_60'
            }, {
              label: '61-70',
              value: '61_70'
            }, {
              label: '71-80',
              value: '71_80'
            }, {
              label: '81-90',
              value: '81_90'
            }, {
              label: '91-100',
              value: '91_100'
            }, {
              label: '100+',
              value: '101_200'
            }
          ]
        }, {
          value: 'IDissuedate',
          label: 'ID issue date',
          options: [
            {
              label: 'Valid',
              value: 'valid'
            }, {
              label: 'Invalid',
              value: 'invalid'
            }
          ]
        }, {
          value: 'IDexpirationdate',
          label: 'ID expiration date',
          options: [
            {
              label: 'Valid',
              value: 'valid'
            }, {
              label: 'Invalid',
              value: 'invalid'
            }
          ]
        }, {
          value: 'affiliate_tenant_type',
          label: 'Affiliate Type',
          options: [],
          source: 'affiliateTypes'
        }, {
          value: 'affiliate_select',
          label: 'Affiliate',
          options: [],
          search: true,
          source: 'affiliates',
          map: {
            type: 'affiliate_tenant_type'
          }
        }, {
          value: 'merchant_custom_name',
          label: 'Merchant custom name'
        }, {
          value: 'email_status',
          label: 'Email status',
          options: [
            {
              label: 'Active',
              value: 'active'
            }, {
              label: 'Inactive',
              value: 'inactive'
            }
          ]
        }, {
          value: 'Registrationdates',
          label: 'Registration date',
          range: [{
            value: 'RegistrationdateA',
            type: 'date'
          }, {
            value: 'RegistrationdateB',
            type: 'date'
          }]
        }, {
          value: 'ucCreated',
          label: 'Card Create Date',
          range: [{
            value: 'ucCreatedFrom',
            type: 'date'
          }, {
            value: 'ucCreatedTo',
            type: 'date'
          }]
        }, {
          value: 'LoadPayments',
          label: 'Load Payment Date',
          range: [{
            value: 'LoadPaymentsA',
            type: 'date'
          }, {
            value: 'LoadPaymentsB',
            type: 'date'
          }]
        }, {
          value: 'LoadPaymentTimes',
          label: 'Load Payment Times',
          range: [{
            value: 'LoadPaymentsTimesA',
            type: 'number'
          }, {
            value: 'LoadPaymentsTimesB',
            type: 'number'
          }]
        }, {
          value: 'LoadPaymentAmount',
          label: 'Load Payment Amount',
          range: [{
            value: 'LoadPaymentsAmountA',
            type: 'amount'
          }, {
            value: 'LoadPaymentsAmountB',
            type: 'amount'
          }]
        }, {
          value: 'CardTransactions',
          label: 'Card Transaction Date',
          helper: 'POS transaction',
          range: [{
            value: 'CardTransactionsA',
            type: 'date'
          }, {
            value: 'CardTransactionsB',
            type: 'date'
          }]
        }, {
          value: 'CardTransactionsTimes',
          label: 'Card Transaction Times',
          range: [{
            value: 'CardTransactionsTimesA',
            type: 'number'
          }, {
            value: 'CardTransactionsTimesB',
            type: 'number'
          }]
        }, {
          value: 'CardTransactionAmount',
          label: 'Card Transaction Amount',
          range: [{
            value: 'CardTransactionsAmountA',
            type: 'amount'
          }, {
            value: 'CardTransactionsAmountB',
            type: 'amount'
          }]
        }, {
          value: 'MCC',
          label: 'MCC',
          helper: 'EG:A,B'
        }, {
          value: 'localbalance',
          label: 'Local balance',
          range: [{
            value: 'localbalancemin',
            type: 'amount'
          }, {
            value: 'localbalancemax',
            type: 'amount'
          }]
        }, {
          value: 'legacyBalance',
          label: 'Legacy balance',
          range: [{
            value: 'legacyBalanceFrom',
            type: 'amount'
          }, {
            value: 'legacyBalanceTo',
            type: 'amount'
          }]
        }, {
          value: 'accountBalance',
          label: 'Account balance',
          range: [{
            value: 'accountBalanceFrom',
            type: 'amount'
          }, {
            value: 'accountBalanceTo',
            type: 'amount'
          }]
        }, {
          value: 'vip',
          label: 'VIP',
          options: [
            {
              label: 'Yes',
              value: 'yes'
            }, {
              label: 'No',
              value: 'no'
            }
          ]
        },
        {
          value: 'legacy',
          label: 'Legacy User',
          options: [
            {
              label: 'Yes',
              value: 'yes'
            }, {
              label: 'No',
              value: 'no'
            }
          ]
        }
      ]
    }
  },
  computed: {
    isGranted () {
      return this.$store.state.User.isGranted
    },
    user () {
      return this.$store.state.User
    }
  },
  methods: {
    download (type = 'all') {
      if (typeof type !== 'string') {
        type = 'all'
      }
      this.downloadMax = type === 'min' ? ******** : 1000

      const data = this.mergeQuerySortParams(this.pagination)
      data.query_type = type
      this.$refs.exportDialog.show(this.downloadUrl, data)
    },

    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.getQueryParams()
      const resp = await request(`/admin/user_search/search/${pagination.page}/${pagination.rowsPerPage}`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.quick.count = resp.data.total
        this.pagination.rowsNumber = resp.data.total
        this.chartData = resp.data.chartData
        if (this.chartData) {
          eventBus.$emit('reload-top-chart', this.chartData)
        }
      }
    },

    async addPaypalSubscript (u) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to add the paypal subscription Id to this member:',
        prompt: {
          model: '',
          type: 'text' // optional
        },
        cancel: true
      }).then(async subscriptionId => {
        if (!subscriptionId) {
          notifyForm('Please input the subscript Id')
        } else {
          this.loading = true
          const resp = await request(`/admin/user_modify/addSubscriptionId`, 'post', {
            userId: u.id,
            subscriptionId
          })
          this.loading = false
          if (resp.success) {
            this.reload()
            notify(resp.message)
          }
        }
      }).catch(() => {})
    },

    async updateGtpPhone (u) {
      this.loading = true
      const resp = await request(`/admin/user/essolo/update-gtp-phone-number`, 'get', {
        userId: u.id
      })
      this.loading = false
      if (resp.success) {
        this.$q.dialog({
          title: 'Update GTP Phone Number',
          message: 'The phone number we use to communicate with GTP\'s API:',
          prompt: {
            model: resp.data
          },
          cancel: true
        }).then(async phone => {
          this.loading = true
          const resp = await request(`/admin/user/essolo/update-gtp-phone-number`, 'post', {
            userId: u.id,
            phone
          })
          this.loading = false
          if (resp.success) {
            notify(resp.message)
          }
        }).catch(() => {})
      }
    },

    async removeBlackList (u) {
      this.$q.dialog({
        title: 'Confirm',
        message: "Are you sure that you want to remove the member's email from the blacklist",
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request(`/admin/user_modify/removeEmailFromBlackList`, 'post', {
          userId: u.id
        })
        this.loading = false
        if (resp.success) {
          this.reload()
          notify(resp.message)
        }
      }).catch(() => {})
    },

    async waiveSubscriptionFee (u) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to waive subscription fee for the member',
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request(`/admin/user_modify/waiveSubscriptionFee`, 'post', {
          userId: u.id
        })
        this.loading = false
        if (resp.success) {
          this.reload()
          notify(resp.message)
        }
      }).catch(() => {})
    },

    async addAsInternalTester (u, op = 'add') {
      let message = op === 'add'
        ? 'Are you sure that you want to add this user to the internal tester list? Once added, the user can test unreleased changes and bypass the fees'
        : 'Are you sure that you want to remove this user from the internal tester list?'
      this.$q.dialog({
        title: 'Confirm',
        message,
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request(`/admin/user_modify/add-as-internal-tester`, 'post', {
          userId: u.id,
          op
        })
        this.loading = false
        if (resp.success) {
          this.reload()
          notify(resp.message)
        }
      }).catch(() => {})
    },

    editUser (row) {
      window.open(`/admin#/a/i/admin__user_modify__modify?user_id=${row.id}`, '_blank')
    }
  },
  mounted () {
    this.keyword = localStorage.getItem('quickSearch') ? localStorage.getItem('quickSearch') : ''
    localStorage.setItem('quickSearch', '')
    if (this.user.cpKey === 'cp_usu') {
      this.filterOptions.push({
        value: 'register_step',
        label: 'Onboard Status',
        options: [
          {
            value: 'register_consumer_created',
            label: 'Onboard (email)'
          },
          {
            value: 'register_consumer_email_confirmed',
            label: 'Onboard (home)'
          },
          {
            value: 'pending',
            label: 'Pending Member'
          },
          {
            value: 'active_no_kyc',
            label: 'Active Member (no KYC)'
          },
          {
            value: 'active',
            label: 'Active Member (KYC)'
          },
          {
            value: 'negative_balance',
            label: 'Negative Balance'
          },
          {
            value: 'inactive',
            label: 'Inactive'
          }
        ]
      })
    }
  }
}
</script>

<style lang="scss">
@import "../../../css/variable";

#user_management__user_page {
}
</style>
