<template>
  <q-dialog class="wilen-agent-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-18 mb-2">{{ edit ? 'Edit' : 'Create' }} New Agent</div>
      <div class="font-14 normal text-dark">Please fill in the information below about the agent.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-6 col-xs-12">
          <q-input float-label="First Name"
                   autocomplete="no"
                   :error="$v.entity['First Name'].$error"
                   @blur="$v.entity['First Name'].$touch"
                   v-model="entity['First Name']"></q-input>
        </div>
        <div class="col-sm-6 col-xs-12">
          <q-input float-label="Last Name"
                   autocomplete="no"
                   :error="$v.entity['Last Name'].$error"
                   @blur="$v.entity['Last Name'].$touch"
                   v-model="entity['Last Name']"></q-input>
        </div>
        <div class="col-sm-6 col-xs-12">
          <q-input float-label="Email"
                   autocomplete="no"
                   :error="$v.entity['Email Address'].$error"
                   @blur="$v.entity['Email Address'].$touch"
                   v-model="entity['Email Address']"></q-input>
        </div>
        <div class="col-sm-6 col-xs-12">
          <q-input float-label="Mobile Phone Number"
                   autocomplete="no"
                   :error="$v.entity['Phone Number'].$error"
                   @blur="$v.entity['Phone Number'].$touch"
                   v-model="entity['Phone Number']"></q-input>
        </div>
        <div v-if="entity['Agent Type'] === 'Agent'"
             class="col-12 pt-20">
          <q-select autocomplete="no"
                    float-label="Assign to Program*"
                    :options="programList"
                    multiple
                    :error="$v.entity['ProgramIds'].$error"
                    @blur="$v.entity['ProgramIds'].$touch"
                    v-model="entity['ProgramIds']"></q-select>
        </div>
        <div class="pt-20"
             :class="edit ? 'col-sm-6 col-xs-12 text-left' : 'col-sm-12 col-xs-12'">
          <div class="bold">Role</div>
          <div class="mt-8">
            <q-radio v-model="entity['Agent Type']"
                     color="blue"
                     :error="$v.entity['Agent Type'].$error"
                     @change="$v.entity['Agent Type'].$touch"
                     val="Administrator"
                     label="Administrator"></q-radio>
            <q-radio v-model="entity['Agent Type']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Agent Type'].$error"
                     @change="$v.entity['Agent Type'].$touch"
                     val="Agent"
                     label="Agent"></q-radio>
          </div>
        </div>
        <div class="col-sm-6 col-xs-12 text-left pt-20"
             v-show="edit">
          <div class="bold">Status</div>
          <div class="mt-8">
            <q-radio v-model="entity['Status']"
                     color="blue"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Active"
                     label="Active"></q-radio>
            <q-radio v-model="entity['Status']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Inactive"
                     label="Inactive"></q-radio>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks"
           v-if="edit">
        <div class="row"
             v-if="origin">
          <q-btn :label="origin['Status'] === 'Archived' ? 'Activate' : 'Archive'"
                 no-caps
                 :color="origin['Status'] === 'Archived' ? 'blue' : 'orange' "
                 @click="toggleStatus" />
          <q-btn label="Save Changes"
                 no-caps
                 class="change"
                 color="positive"
                 @click="save" />
        </div>
        <q-btn label="Cancel"
               no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
      </div>
      <template v-else>
        <q-btn label="Cancel"
               no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
        <q-btn label="Create Agent"
               no-caps
               class="main"
               @click="save" />
      </template>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request, toSelectOptions } from '../../../common'
import { required, minLength, email, helpers } from 'vuelidate/lib/validators'
import _ from 'lodash'
const alpha = helpers.regex('alpha', /^[0-9+-\s]*$/)
export default {
  name: 'wilen-agent-detail-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        'Agent Type': 'Agent',
        'Status': 'Active',
        'ProgramIds': []
      },
      programList: []
    }
  },
  computed: {
    edit () {
      return this.entity['User ID']
    }
  },
  validations () {
    let verify = {
      entity: {
        'First Name': {
          required
        },
        'Last Name': {
          required
        },
        'Email Address': {
          required,
          email
        },
        'Phone Number': {
          required,
          alpha
        },
        'Agent Type': {
          required
        },
        'ProgramIds': {
          required,
          minLength: minLength(1)
        },
        'Status': {
          required
        }
      }
    }
    if (this.entity['Agent Type'] !== 'Agent') {
      verify.entity['ProgramIds'] = {}
    }
    return verify
  },
  methods: {
    onSuccess (resp) {
      notify(resp.message)

      if (this.origin && this.origin['User ID']) {
        _.assignIn(this.origin, resp.data)
      }

      this.$root.$emit('reload-wilen-agents')
      this._hide()
    },
    async show () {
      this.$q.loading.show()
      const res = await request('/admin/wilen/programs/listAll', 'get')
      if (res.success) {
        this.programList = toSelectOptions(res.data)
      }
      this.$q.loading.hide()
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      if (this.entity['Agent Type'] !== 'Agent') {
        this.entity['ProgramIds'] = []
      }
      const resp = await request(`/admin/wilen/agents/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    },
    async toggleStatus () {
      if (!this.origin) {
        return
      }
      this.$q.loading.show()
      const resp = await request(`/admin/wilen/agents/${this.entity['ID']}/toggle-status`, 'post', {
        Status: this.origin['Status'] === 'Archived' ? 'Active' : 'Archived'
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    }
  }
}
</script>

<style lang="scss">
.wilen-agent-detail-dialog {
  .modal-content {
    width: 550px;
  }
  .q-btn.main,
  .q-btn.change {
    background: #005e92 !important;
    color: #fff;
  }
}
</style>
