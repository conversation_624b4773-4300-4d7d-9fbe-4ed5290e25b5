<template>
  <q-page id="wilen__agents__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>

    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3 col-xs-6">
          <q-card>
            <q-card-title>
              <span>Active Agents</span>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy mb-10">{{ quick.totalActive ? quick.totalActive : 0 }}</div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-xs-6">
          <q-card>
            <q-card-title>
              <span>Inactive Agents</span>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy mb-10">{{ quick.totalInactive ? quick.totalInactive : 0 }}</div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-xs-6">
          <q-card>
            <q-card-title>
              <span>Archived Agents</span>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy mb-10">{{ quick.totalArchived ? quick.totalArchived : 0 }}</div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong class="mt-10">Agents Report</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-account-plus-outline"
                 color="positive"
                 label="Create New Agent"
                 @click="add"
                 class="btn-sm mr-10 mt-10 create-btn"
                 no-caps></q-btn>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8 mt-10 export-btn"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 mt-10 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10 mt-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot mt-10"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mb-10"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-if="$store.state.User.teams.indexOf('MasterAdmin') !== -1"
                          v-close-overlay
                          @click.native="$c.loginAs(props.row['User ID'])">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div v-if="props.row['Status'] !='Active'"
                   class="blue-item"
                   notranslate="">{{ _.get(props.row, col.field) }}</div>

              <div v-else
                   notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

    <DetailDialog></DetailDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import DetailDialog from './detail'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'

export default {
  name: 'wilen-agents',
  mixins: [
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-wilen-agents')
  ],
  components: {
    DetailDialog
  },
  data () {
    return {
      title: 'Agents',
      requestUrl: `/admin/wilen/agents/list`,
      downloadUrl: `/admin/wilen/agents/export`,
      keyword: '',
      columns: generateColumns([
        'First Name', 'Last Name', 'Phone Number', 'Email Address', 'Agent Type', 'Last login Date/Time',
        'Status', 'Actions'
      ], [], {
        'First Name': 'a.firstName',
        'Last Name': 'a.lastName',
        'Phone Number': 'a.phoneNumber',
        'Email Address': 'a.email',
        'Agent Type': 'a.agentType',
        'Last login Date/Time': 'u.lastLogin'
      }),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'Agent ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        }, {
          value: 'filter[u.mobilephone=]',
          label: 'Phone Number'
        }, {
          value: 'filter[u.status=]',
          label: 'Status',
          options: [
            { label: 'Active', value: 'active' },
            { label: 'Archived', value: 'closed' }
          ]
        }, {
          value: 'filter[t.name=]',
          label: 'Agent Type',
          options: [
            { label: 'Administrator', value: 'DigitalIncentives Admin' },
            { label: 'Agent', value: 'DigitalIncentives Agent' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    add () {
      this.$root.$emit('show-wilen-agent-detail-dialog')
    },
    edit (row) {
      this.$root.$emit('show-wilen-agent-detail-dialog', row)
    },
    statusClass (status) {
      const cls = []
      cls.push({
        'Archived': 'orange',
        'Active': 'positive',
        'Inactive': 'negative'
      }[status] || status)
      return cls
    }
  }
}
</script>
<style lang="scss">
#wilen__agents__index_page {
  .q-btn.create-btn {
    background: #3dd598 !important;
  }
  .q-btn.export-btn {
    background: #005e92 !important;
  }
  .q-chip.positive {
    background-color: rgba($color: #00d993, $alpha: 0.1);
    color: #00d993 !important;
  }
  .q-chip.negative {
    background-color: rgba($color: #ff4852, $alpha: 0.1);
    color: #ff4852 !important;
  }
  .q-chip.orange {
    background-color: rgba($color: #ff974a, $alpha: 0.1);
    color: #ff974a !important;
  }
  .blue-item {
    color: #979797;
  }
}
</style>
