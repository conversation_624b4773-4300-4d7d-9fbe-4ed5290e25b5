<template>
  <q-page id="wilen__campaign_detail__index_page">
    <p class="back"
       @click="viewCampaigns()"><i class="mdi mdi-arrow-left"></i>Go Back to Campaigns Report</p>
    <div class="page-header">
      <div class="title">{{quick.name}} {{ title }}</div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3 col-xs-6">
          <q-card>
            <q-card-title>
              <span>Number of Recipients</span>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy mb-10">{{ quick.total ?  quick.total : 0 }}</div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-xs-6">
          <q-card>
            <q-card-title>
              <span>Campaign Budget</span>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy mb-10">{{ quick.totalBudget ?  quick.totalBudget : 0 | moneyFormat}}</div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-xs-6">
          <q-card>
            <q-card-title>
              <span>Total Amount Send</span>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy mb-10">{{ quick.totalAmount ?  quick.totalAmount : 0 | moneyFormat}}</div>
            </q-card-main>
          </q-card>
        </div>

        <div class="col-sm-3 col-xs-6">
          <q-card>
            <q-card-title>
              <span>Campaign Budget Remaining</span>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy mb-10">{{ quick.remainBudget ?  quick.remainBudget : 0  | moneyFormat}}</div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{quick.name}} {{ title }} Recipient List</strong>
        </template>
        <template slot="top-right">
          <q-btn v-if="quick.is_active"
                 icon="mdi-account-plus-outline"
                 color="positive"
                 label="Add/Import Recipients"
                 @click="add"
                 class="btn-sm mr-10 mt-10 create-btn"
                 no-caps></q-btn>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8 mt-10 export-btn"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense mt-10"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10 mt-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 class="mt-10"
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Promo Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Promo Status'])">
                {{ props.row['Promo Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Amount to Sent'">
              {{ props.row['Amount to Sent'] | moneyFormat}}
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list v-if="props.row['Promo Status'] === 'Pending'"
                        link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                </q-list>
                <q-list v-else>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>View</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="resent(props.row, 'Email')">
                    <q-item-main>Resend Email</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="resent(props.row, 'SMS')">
                    <q-item-main>Resend SMS</q-item-main>
                  </q-item>
                  <!-- <q-item v-close-overlay
                          @click.native="cancel(props.row)">
                    <q-item-main>Cancel Token</q-item-main>
                  </q-item> -->
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <ImportRecipient></ImportRecipient>
    <HtmlListDialog></HtmlListDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, request, notify } from '../../../common'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import ImportRecipient from './importRecipient'
import HtmlListDialog from '../../../components/HtmlListDialog'

export default {
  name: 'wilen-campaign-detail',
  mixins: [
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-wilen-campaign-detail')
  ],
  components: {
    ImportRecipient,
    HtmlListDialog
  },
  computed: {
    uid () {
      return this.$route.params.id
    }
  },
  data () {
    return {
      title: 'Program',
      requestUrl: `/admin/wilen/campaignRecipient/list`,
      downloadUrl: `/admin/wilen/campaignRecipient/export`,
      keyword: '',
      columns: generateColumns([
        'First Name', 'Last Name', 'Email Address', 'Amount to Sent', 'Promo Status', 'Actions'
      ], [], {
        'First Name': 'r.firstName',
        'Last Name': 'r.lastName',
        'Email Address': 'r.email',
        'Amount to Sent': 'r.amount'
      }),
      filterOptions: [
        {
          value: 'filter[r.firstName]',
          label: 'First Name'
        },
        {
          value: 'filter[r.lastName]',
          label: 'Last Name'
        },
        {
          value: 'filter[r.email]',
          label: 'Email Address'
        },
        {
          value: 'filter[r.phone]',
          label: 'Phone'
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  mounted () {
    this.downloadUrl = this.downloadUrl + '?id=' + this.uid
  },
  methods: {
    add () {
      this.$root.$emit('show-wilen-campaign-import-recipient-dialog', { CampaignId: this.uid })
    },
    edit (row) {
      this.$root.$emit('show-wilen-campaign-recipient-dialog', row)
    },
    // async cancel (row) {
    //   this.$q.dialog({
    //     title: 'Cancel',
    //     message: 'Are you sure that you want to cancel token?',
    //     color: 'negative',
    //     cancel: true
    //   }).then(async () => {
    //     this.$q.loading.show()
    //     const resp = await request(`/admin/wilen/campaign/recipient/cancelToken`, 'post', { id: row['ID'] })
    //     if (resp.success) {
    //       notify(resp.message)
    //       this.reload()
    //     }
    //     this.$q.loading.hide()
    //   }).catch(() => {})
    // },
    async resent (row, type) {
      this.$q.dialog({
        title: 'Resend',
        message: 'Are you sure that you want to resend ' + type + '?',
        color: 'positive',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/wilen/campaign/recipient/resend`, 'post', { id: row['ID'], type: type })
        if (resp.success) {
          notify(resp.message)
          this.reload()
        }
        this.$q.loading.hide()
      }).catch(() => {})
    },
    viewCampaigns () {
      this.$router.push(`/h/wilen/campaigns`)
    },
    async request ({ pagination }) {
      this.$q.loading.show()
      this.beforeRequest({ pagination })
      if (pagination.page <= 0) {
        return
      }

      if (this.requestUrl) {
        let params = this.mergeQuerySortParams(pagination)
        this.loading = true
        params.id = this.uid
        const resp = await request(`${this.requestUrl}/${pagination.page}/${pagination.rowsPerPage}`, 'get', params)
        this.loading = false
        if (resp.success) {
          this.pagination = pagination
          this.data = resp.data.data
          this.pagination.rowsNumber = resp.data.count
          this.quick = resp.data.quick || {}

          if (this.requestCb) {
            this.requestCb(resp.data, params, pagination)
          }
        }
      }
      this.$q.loading.hide()
    },
    statusClass (status) {
      const cls = []
      cls.push({
        'Pending': 'pending',
        'Sent': 'positive',
        'Active': 'positive',
        'Inactive': 'negative'
      }[status] || status)
      return cls
    }
  }
}
</script>
<style lang="scss">
#wilen__campaign_detail__index_page {
  .back {
    margin-bottom: 0;
    margin-top: 10px;
    color: #005e92;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    i {
      display: flex;
      margin-right: 5px;
    }
  }
  .q-btn.export-btn {
    background: #005e92 !important;
  }
  .q-btn.create-btn {
    background: #3dd598 !important;
  }
  .q-chip.pending {
    color: #0062ff;
    background: rgba($color: #0062ff, $alpha: 0.1);
  }
}
</style>
