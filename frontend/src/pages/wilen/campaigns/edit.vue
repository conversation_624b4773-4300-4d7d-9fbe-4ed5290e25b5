<template>
  <q-dialog class="wilen-campaign-edit-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-18 mb-2">{{ edit ? 'Edit' : 'Create' }} New Campaign</div>
      <div class="font-14 normal text-dark">Please fill in the information below to get your campaign started.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10">
        <div class="col-12 pt-20 ">
          <q-input float-label="Campaign Name"
                   autocomplete="no"
                   :error="$v.entity['Campaign Name'].$error"
                   @blur="$v.entity['Campaign Name'].$touch"
                   v-model="entity['Campaign Name']"></q-input>
        </div>
        <div class="col-12 pt-20">
          <q-select autocomplete="no"
                    float-label="Assign to Program*"
                    :options="programListOptions"
                    filter
                    :disable="edit"
                    autofocus-filter
                    :error="$v.entity['ProgramId'].$error"
                    @change="$v.entity['ProgramId'].$touch"
                    v-model="entity['ProgramId']"></q-select>
        </div>
        <div class="pt-20 col-12">
          <div class="bold pb-10">Campaign Send Date & Time*</div>
          <q-datetime type="datetime"
                      format="MM/DD/YYYY HH"
                      :disable="edit"
                      placeholder='Campaign Send Date & Time'
                      :error="$v.entity['Campaign send Date'].$error"
                      @blur="$v.entity['Campaign send Date'].$touch"
                      v-model="entity['Campaign send Date']"></q-datetime>
        </div>
        <div class="pt-20 col-12">
          <div class="bold pb-10">Campaign Expiration Date & Time*</div>
          <q-datetime type="datetime"
                      format="MM/DD/YYYY HH"
                      :min="entity['Campaign send Date']"
                      placeholder='Campaign Expiration Date & Time'
                      :error="$v.entity['Expiration Date'].$error"
                      @blur="$v.entity['Expiration Date'].$touch"
                      v-model="entity['Expiration Date']"></q-datetime>
        </div>
        <div class="pt-20 col-12">
          <div class="bold pb-10">Campaign Budget*</div>
          <q-field :error="$v.entity['Campaign budget'].$error"
                   :error-label="'The campaign budget must be greater than $' + campaignBudgetMin">
            <q-input float-label="Campaign Budget"
                     type="number"
                     :min="campaignBudgetMin"
                     prefix="$"
                     :disable="edit"
                     @blur="$v.entity['Campaign budget'].$touch"
                     v-model="entity['Campaign budget']"></q-input>
          </q-field>
        </div>
        <div class="col-12 content-area pt-0">
          <q-input float-label="Custom Message"
                   v-model="entity['Message']"
                   filled
                   rows="5"
                   :maxlength="250"
                   :max-height="140"
                   type="textarea" />
          <p class="limit-desc">Character Limit: {{entity['Message'] ? entity['Message'].length : 0}}/250</p>
        </div>
      </div>
      <div class="row">
        <div class="pt-10 col-12">
          <q-btn label="View/Edit the Program SMS"
                 no-caps
                 class="ml-0 sms-configure-btn"
                 @click="viewSms()" />
        </div>
        <div class="pt-10 col-12">
          <div class="bold">Enable SMS Notifications?</div>
          <div class="mt-8">
            <q-radio v-model="entity['SmsNotifications']"
                     color="blue"
                     :error="$v.entity['SmsNotifications'].$error"
                     @change="$v.entity['SmsNotifications'].$touch"
                     val="Active"
                     label="Enable"></q-radio>
            <q-radio v-model="entity['SmsNotifications']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['SmsNotifications'].$error"
                     @change="$v.entity['SmsNotifications'].$touch"
                     val="Disable"
                     label="Inactive"></q-radio>
          </div>
        </div>
      </div>
      <div v-if="!edit && !created"
           class="pt-20 row recipients-area">
        <div v-if="!entity['Recipients'].length"
             class="col-sm-6 col-xs-12">
          <div class="upload-area"
               @click="select"
               :class="{selected: file}">
            <img src="/static/wilen/document-icon.svg">
            <template v-if="file">
              <div class="mt-10">Selected file:</div>
              <div class="font-13 text-blue">
                <span>{{ file.name }}</span>
                <a href="javascript:"
                   class="ml-5 link"
                   @click.stop="file = null">
                  <q-icon name="mdi-close-circle-outline"
                          class="font-20"
                          color="negative"></q-icon>
                  <q-tooltip>Remove and reselect</q-tooltip>
                </a>
              </div>
            </template>
            <template v-else>
              <div class="mt-10">Upload .XLSX</div>
              <div class="font-13 text-dark">(Drop or click here)</div>
            </template>
          </div>
          <input type="file"
                 class="hide"
                 accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                 ref="file"
                 @change="selectedFile">
        </div>
        <div class="col-sm-6 col-xs-12"
             :class="entity['Recipients'].length ? 'margin-center' : ''">
          <div class="manual-create"
               @click="manualCreate">
            <img src="/static/wilen/add-icon.svg">
            <p>Manually Create Recipient</p>
          </div>
        </div>
        <div class="col-12 recipients-list"
             v-if="entity['Recipients'].length">
          <q-collapsible class="text-center"
                         label="Added Recipients List">
            <table>
              <tr>
                <th>First Name</th>
                <th>Last Name</th>
                <th>Address Email</th>
                <th>Phone</th>
                <th>Amount Recevice</th>
                <th></th>
              </tr>
              <tbody>
                <tr class="recipient-item"
                    v-for="(recipient, i) in entity['Recipients']"
                    :key="i">
                  <td>{{recipient['firstName']}}</td>
                  <td>{{recipient['lastName']}}</td>
                  <td>{{recipient['email']}}</td>
                  <td>{{recipient['phone']}}</td>
                  <td>{{recipient['amount']}}</td>
                  <td><i @click="removeRecipient(i)"
                       class="mdi mdi-close-circle-outline"></i></td>
                </tr>
              </tbody>
            </table>
          </q-collapsible>
        </div>
        <div class="pt-10 col-12">
          <q-btn label="Download XLS Template"
                 no-caps
                 class="ml-0 download-btn"
                 @click="downloadTemplate" />
        </div>
      </div>
      <!-- <div v-if="manuallyCreateRecipient"
           class="pt-20 row gutter-form mt--10 recipients-info-area">
        <div class="bold pb-10 col-12">Campaign Recipient Info</div>
        <div class="col-sm-6 col-xs-12 mt--10">
          <q-input float-label="Recipient First Name"
                   autocomplete="no"
                   v-model="entity['First Name']"></q-input>
        </div>
        <div class="col-sm-6 col-xs-12 mt--10">
          <q-input float-label="Recipient Last Name"
                   autocomplete="no"
                   v-model="entity['Last Name']"></q-input>
        </div>
        <div class="col-sm-6 col-xs-12 mt--10">
          <q-input float-label="Email"
                   autocomplete="no"
                   v-model="entity['Recipient Email Address']"></q-input>
        </div>
        <div class="col-sm-6 col-xs-12 mt--10">
          <q-input float-label="Amount to Receive ($)"
                   autocomplete="no"
                   type="number"
                   :min="0"
                   v-model="entity['Recipient Amount to Sent']"></q-input>
        </div>
      </div> -->
    </template>
    <template v-if="!created"
              slot="buttons">
      <q-btn label="Cancel"
             no-caps
             color="grey-3"
             text-color="tertiary"
             @click="_hide" />
      <q-btn v-if="edit"
             label="Save"
             no-caps
             class="main"
             @click="save" />
      <q-btn v-else
             label="Create Campaign"
             no-caps
             class="main"
             @click="save" />

      <Recipient></Recipient>
      <ConfigureSms></ConfigureSms>
    </template>
    <template v-else
              slot="buttons">
      <q-btn label="Close"
             no-caps
             color="grey-3"
             text-color="tertiary"
             @click="close" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request, toSelectOptions, notifyResponse, uploadAttachment } from '../../../common'
import { required, minValue } from 'vuelidate/lib/validators'
import $ from 'jquery'
import Recipient from './recipient'
import ConfigureSms from '../programs/configureSms'

export default {
  name: 'wilen-campaign-edit-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        'SmsNotifications': 'Active',
        'Campaign budget': 0,
        'Amount sent ($)': 0,
        'Message': '',
        'Recipients': [],
        'isCreateCampaign': false
      },
      campaignBudgetMin: 0,
      file: null,
      attachment: null,
      programList: [],
      programListOptions: [],
      created: false
    }
  },
  computed: {
    edit () {
      if (this.entity['ID']) {
        return true
      }
      return false
    }
  },
  components: {
    Recipient,
    ConfigureSms
  },
  validations () {
    let res = {
      entity: {
        'Campaign Name': {
          required
        },
        'ProgramId': {
          required
        },
        'SmsNotifications': {
          required
        },
        'Campaign budget': {
          required,
          minValue: minValue(0)
        },
        'Expiration Date': {
          required
        },
        'Campaign send Date': {
          required
        }
      }
    }
    if (this.edit) {
      res.entity['Campaign budget'] = {
        required,
        minValue: minValue(this.campaignBudgetMin)
      }
    }
    return res
  },
  methods: {
    viewSms () {
      if (!this.entity['ProgramId']) {
        this.$q.dialog({
          title: 'Notice',
          message: 'Please select a program first?',
          color: 'negative'
        }).then(() => {}).catch(() => {})
      } else {
        // let smsMessage = ''
        this.programList.forEach(e => {
          if (e.id === this.entity['ProgramId']) {
            this.entity['SmsMessage'] = e.SmsMessage
            this.entity['programId'] = e.id
          }
        })
        console.log(this.entity)
        this._hide()
        this.$root.$emit('show-wilen-program-configure-sms-dialog', {
          'campaign': this.entity
        })
      }
    },
    async show () {
      this.attachment = null
      this.file = null
      this.created = false
      if (!this.entity.isCreateCampaign) {
        this.$q.loading.show()
        this.entity['Campaign budget'] = this.entity['Campaign budget'] / 100
        this.campaignBudgetMin = this.entity['Amount sent ($)'] / 100
      }
      let res = null
      if (this.entity['ID']) {
        res = await request('/admin/wilen/programs/listAll', 'get')
      } else {
        res = await request('/admin/wilen/programs/listAll', 'get', { 'active': true })
      }
      if (res.success) {
        this.programList = res.data
        this.programListOptions = toSelectOptions(res.data)
      }
      this.$q.loading.hide()
    },
    removeRecipient (i) {
      let temp = []
      this.entity.Recipients.forEach((element, k) => {
        if (i !== k) {
          temp.push(element)
        }
      })
      this.entity.Recipients = temp
    },
    manualCreate () {
      this._hide()
      this.entity['isCreateCampaign'] = true
      this.$root.$emit('show-wilen-campaign-recipient-dialog', {
        'campaign': this.entity
      })
    },
    downloadTemplate () {
      location.href = `/download?path=static/wilen/template/recipient.xlsx`
    },
    onSuccess (resp) {
      notify(resp.message)

      this.$root.$emit('reload-wilen-campaigns')
      this._hide()
    },
    cancel () {
      this._hide()
    },
    close () {
      this.$root.$emit('reload-wilen-campaigns')
      this._hide()
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      if (!this.attachment && this.file) {
        this.$q.loading.show({ message: 'Uploading...' })
        const resp = await uploadAttachment(this.file, 'wilenRecipients')
        if (typeof resp === 'string') {
          this.$q.loading.hide()
          return notifyResponse(`Failed to upload: ${resp}`)
        }
        this.attachment = resp
        this.entity.attachment = this.attachment.id
        this.$q.loading.show({ message: 'Importing ...' })
      } else {
        this.$q.loading.show()
      }
      const resp = await request(`/admin/wilen/campaign/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      } else {
        if (!resp.data) {
          this.created = true
        }
      }
    },
    select () {
      if (this.file) {
        return
      }
      $(this.$refs.file).click()
    },
    selectedFile () {
      const input = this.$refs.file
      if (input.files.length) {
        this.file = input.files[0]
      }
      input.type = 'text'
      input.type = 'file'
    },
    getValidDragFile (e) {
      if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length) {
        const file = e.dataTransfer.files[0]
        console.log(file)
        if (file && file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          return file
        }
      }
      return null
    },
    setupDnd () {
      const area = $(this.$el).find('.upload-area'),
        dom = area.get(0)

      dom.addEventListener('dragover', e => {
        e.stopPropagation()
        e.preventDefault()
        area.addClass('dropping')
      }, false)

      dom.addEventListener('dragleave', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')
      }, false)

      dom.addEventListener('drop', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')

        const file = this.getValidDragFile(e)
        if (file) {
          this.file = file
        } else {
          notify('Please select a .xlsx file.', 'negative')
        }
      }, false)
    }
  }
}
</script>

<style lang="scss">
.wilen-campaign-edit-dialog {
  .modal-content {
    width: 550px;
    .modal-header {
      padding-bottom: 0;
    }
  }
  .q-btn.main,
  .q-btn.change {
    background: #005e92 !important;
    color: #fff;
  }
  .sms-configure-btn {
    background-color: #005e92;
    color: #fafafb;
    font-size: 16px;
    max-height: 32px;
    .q-btn-inner {
      line-height: 16px;
    }
  }
  .download-btn {
    background-color: #ff974a;
    color: #fafafb;
    font-size: 16px;
    max-height: 32px;
    .q-btn-inner {
      line-height: 16px;
    }
  }
  .content-area {
    position: relative;
    padding-top: 18px;
    .limit-desc {
      position: absolute;
      right: 10px;
      bottom: -10px;
      opacity: 0.5;
      font-size: 14px;
    }
  }
  .recipients-area {
    .col-sm-6 {
      padding: 5px;
    }
    .upload-area {
      max-width: 250px;
      margin: 0 auto;
    }
    .margin-center {
      margin: 0 auto;
    }
    .q-collapsible {
      .q-item-label {
        text-align: center;
        color: #000;
        font-size: 16px;
      }
      .recipient-item {
        border-bottom: 1px solid #e2e2eb;
        i {
          color: red;
          cursor: pointer;
        }
      }
    }
    .manual-create {
      text-align: center;
      max-width: 250px;
      margin: 0 auto;
      border: 1px solid #e2e2eb;
      border-radius: 10px;
      padding: 33px 0px;
      cursor: pointer;
      box-shadow: 0 0 20px 0 rgba(28, 20, 70, 0.1);
      p {
        font-size: 14px;
        color: #000;
      }
    }
  }
  .recipients-list {
    overflow: scroll;
  }
}
</style>
