<template>
  <q-dialog class="wilen-campaign-import-recipient-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-18 mb-2">Create/Upload Recipients</div>
      <div class="font-14 normal text-dark">Please select an option to create/add recipients.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-12">
          <div class="upload-area"
               @click="select"
               :class="{selected: file}">
            <img src="/static/wilen/document-icon.svg">
            <template v-if="file">
              <div class="mt-10">Selected file:</div>
              <div class="font-13 text-blue">
                <span>{{ file.name }}</span>
                <a href="javascript:"
                   class="ml-5 link"
                   @click.stop="file = null">
                  <q-icon name="mdi-close-circle-outline"
                          class="font-20"
                          color="negative"></q-icon>
                  <q-tooltip>Remove and reselect</q-tooltip>
                </a>
              </div>
            </template>
            <template v-else>
              <div class="mt-10">Upload .XLSX</div>
              <div class="font-13 text-dark">(Drop or click here)</div>
            </template>
          </div>
          <input type="file"
                 class="hide"
                 accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                 ref="file"
                 @change="selectedFile">
        </div>
        <div class="col-12">
          <div class="manual-create"
               @click="manualCreate">
            <img src="/static/wilen/add-icon.svg">
            <p>Manually Create Recipient</p>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn label="Continue"
               no-caps
               :disable="!file"
               class="continue-btn"
               @click="save" />
        <q-btn label="Download XLS Template"
               no-caps
               class="ml-0 mt-8 download-btn"
               @click="downloadTemplate" />
      </div>
      <Recipient></Recipient>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, notifyResponse, uploadAttachment, request } from '../../../common'
import $ from 'jquery'
import Recipient from './recipient'

export default {
  name: 'wilen-campaign-import-recipient-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      file: null,
      attachment: null
    }
  },
  components: {
    Recipient
  },
  computed: {
  },
  methods: {
    manualCreate () {
      this._hide()
      this.$root.$emit('show-wilen-campaign-recipient-dialog', this.entity)
    },
    onSuccess (resp) {
      notify(resp.message)
      this.$root.$emit('reload-wilen-campaign-detail')
      this._hide()
    },
    show () {
      this.file = null
    },
    async save () {
      if (!this.file) {
        return
      }
      if (!this.attachment) {
        this.$q.loading.show({ message: 'Uploading...' })
        const resp = await uploadAttachment(this.file, 'wilenRecipients')
        if (typeof resp === 'string') {
          this.$q.loading.hide()
          return notifyResponse(`Failed to upload: ${resp}`)
        }
        this.attachment = resp
      }
      this.$q.loading.show({ message: 'Importing ...' })
      const resp = await request(`/admin/wilen/recipient/import`, 'post', {
        attachment: this.attachment.id,
        campaignId: this.entity.CampaignId
      }, true)
      this.$q.loading.hide()
      if (resp.success) {
        this.file = null
        this.attachment = null
        this.onSuccess(resp)
      } else {
        this.file = null
        this.attachment = null
        this.$root.$emit('show-html-list-dialog', {
          title: 'Error',
          html: resp.message
        })
      }
    },
    downloadTemplate () {
      location.href = `/download?path=static/wilen/template/recipient.xlsx`
    },
    select () {
      if (this.file) {
        return
      }
      $(this.$refs.file).click()
    },
    selectedFile () {
      const input = this.$refs.file
      if (input.files.length) {
        this.file = input.files[0]
      }
      input.type = 'text'
      input.type = 'file'
    },
    getValidDragFile (e) {
      if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length) {
        const file = e.dataTransfer.files[0]
        console.log(file)
        if (file && file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          return file
        }
      }
      return null
    },
    setupDnd () {
      const area = $(this.$el).find('.upload-area'),
        dom = area.get(0)

      dom.addEventListener('dragover', e => {
        e.stopPropagation()
        e.preventDefault()
        area.addClass('dropping')
      }, false)

      dom.addEventListener('dragleave', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')
      }, false)

      dom.addEventListener('drop', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')

        const file = this.getValidDragFile(e)
        if (file) {
          this.file = file
        } else {
          notify('Please select a .xlsx file.', 'negative')
        }
      }, false)
    }
  },
  mounted () {
    this.setupDnd()
  }
}
</script>

<style lang="scss">
.wilen-campaign-import-recipient-dialog {
  .modal-content {
    width: 550px;
  }
  .upload-area {
    max-width: 250px;
    text-align: center;
    margin: 0 auto;
  }
  .manual-create {
    text-align: center;
    max-width: 250px;
    margin: 0 auto;
    border: 1px solid #e2e2eb;
    border-radius: 10px;
    padding: 35px 0;
    cursor: pointer;
    box-shadow: 0 0 20px 0 rgba(28, 20, 70, 0.1);
    p {
      font-size: #e2e2eb;
      color: #000;
    }
  }
  .stacks {
    max-width: 340px;
    margin: 0 auto;
  }
  .continue-btn {
    background-color: #005e92;
    color: #fafafb;
    font-size: 16px;
    line-height: 48px;
  }
  .download-btn {
    background-color: #ff974a;
    color: #fafafb;
    font-size: 16px;
  }
}
</style>
