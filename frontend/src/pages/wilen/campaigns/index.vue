<template>
  <q-page id="wilen__campaigns__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3 col-xs-6"
             v-for="(n, i) in numbers"
             :key="i">
          <q-card>
            <q-card-title>
              <span>{{ n }}</span>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy mb-10">
                <span v-if="n === 'Active Campaigns'">{{quick.totalActive ? quick.totalActive : 0}}</span>
                <span v-else-if="n === 'Campaigns Budget'">{{ quick.totalBudget ?  quick.totalBudget : 0 | moneyFormat }}</span>
                <span v-else-if="n === 'Campaigns Budget Remaining'">{{ quick.totalRemain ?  quick.totalRemain : 0 | moneyFormat }}</span>
                <span v-else>{{ quick.totalSent ?  quick.totalSent : 0 | moneyFormat }}</span>
              </div>
              <div class="col-sm-3 col-xs-6">
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong class="mt-10">{{title}} Report</strong>
        </template>
        <template slot="top-right">
          <q-btn color="positive"
                 @click="add"
                 class="btn-sm mr-10 create-btn mt-10"
                 no-caps>
            <img style="max-width: 16px;"
                 src="static/wilen/campaign-icon-white.svg">
            <div>
              Create Campaign
            </div>
          </q-btn>
          <q-btn icon="mdi-file-download-outline"
                 label="Export as XLSX"
                 @click="download"
                 color="blue"
                 class="btn-sm mr-8 export-btn mt-10"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense mt-10"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10 mt-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 class="mt-10"
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Campaign budget'">
              {{ props.row['Campaign budget'] | moneyFormat}}
            </template>
            <template v-else-if="col.field === 'Campaign balance'">
              {{ props.row['Campaign balance'] | moneyFormat}}
            </template>
            <template v-else-if="col.field === 'Amount sent ($)'">
              {{ props.row['Amount sent ($)'] | moneyFormat}}
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="viewList(props.row)">
                    <q-item-main>View List</q-item-main>
                  </q-item>
                  <!-- <q-item v-if="props.row['Status'] === 'Active'"
                          v-close-overlay
                          @click.native="resend(props.row)">
                    <q-item-main>Resend</q-item-main>
                  </q-item> -->
                  <!-- <q-item v-if="props.row['Status'] === 'Active'"
                          v-close-overlay
                          @click.native="cancel(props.row)">
                    <q-item-main>Cancel</q-item-main>
                  </q-item> -->
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <EditCampaign></EditCampaign>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notify, request, toSelectOptions } from '../../../common'
import CowPageMixin from '../../../mixins/cow/CowPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import EditCampaign from './edit'

export default {
  name: 'wilen-campaigns',
  mixins: [
    CowPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-wilen-campaigns')
  ],
  components: {
    EditCampaign
  },
  data () {
    return {
      title: 'Campaigns',
      requestUrl: `/admin/wilen/campaigns/list`,
      downloadUrl: `/admin/wilen/campaigns/export`,
      keyword: '',
      numbers: ['Active Campaigns', 'Campaigns Budget', 'Total Amount Sent ($)', 'Campaigns Budget Remaining'],
      columns: generateColumns([
        'Program Name', 'Campaign Name', 'ID', 'Campaign send Date', 'Expiration Date', '# of Recipients', 'Campaign budget', 'Campaign balance', 'Amount sent ($)',
        'Status', 'Actions'
      ], [], {
        'Program Name': 'p.programName',
        'Campaign Name': 'c.campaignName',
        'ID': 'c.id',
        'Campaign send Date': 'c.sendDate',
        'Expiration Date': 'c.expDate',
        '# of Recipients': '',
        'Campaign budget': 'c.campaignBudget',
        'Campaign balance': 'c.availableBalance',
        'Amount sent ($)': 'c.amountSpent'
      }),
      filterOptions: [
        {
          value: 'filter[c.id=]',
          label: 'Campaign ID'
        },
        {
          value: 'filter[c.campaignName=]',
          label: 'Campaign Name'
        },
        {
          value: 'filter[p.id=]',
          label: 'Program',
          options: []
        },
        {
          value: 'filter[c.status=]',
          label: 'Status',
          options: [
            { label: 'Active', value: 'Active' },
            { label: 'Inactive', value: 'Inactive' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  mounted () {
    this.getProgram()
    if (localStorage.getItem('programId')) {
      this.filters.push(
        {
          'predicate': '=',
          'field': 'filter[p.id=]',
          'value': localStorage.getItem('programId')
        }
      )
      localStorage.setItem('programId', '')
    }
    this.reload()
  },
  methods: {
    async getProgram () {
      const res = await request('/admin/wilen/programs/listAll', 'get')
      if (res.success) {
        this.filterOptions[2]['options'] = toSelectOptions(res.data)
      }
    },
    add () {
      this.$root.$emit('show-wilen-campaign-edit-dialog')
    },
    edit (row) {
      this.$root.$emit('show-wilen-campaign-edit-dialog', row)
    },
    async cancel (row) {
      this.$q.dialog({
        title: 'Cancel',
        message: 'Are you sure that you want to cancel?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/wilen/campaign/cancel`, 'post', { id: row['ID'] })
        if (resp.success) {
          notify(resp.message)
          this.reload()
        }
        this.$q.loading.hide()
      }).catch(() => {})
    },
    viewList (row) {
      this.$router.push(`/h/wilen/campaigns/${row['ID']}`)
    },
    statusClass (status) {
      const cls = []
      cls.push({
        'Inactive': 'negative',
        'Active': 'positive'
      }[status] || status)
      return cls
    }
  }
}
</script>
<style lang="scss">
#wilen__campaigns__index_page {
  .q-btn.export-btn {
    background: #005e92 !important;
  }
}
</style>
