<template>
  <q-dialog class="wilen-campaign-recipient-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div v-if="entity['Promo Status'] === 'Pending'"
           class="font-18 mb-2">{{ edit ? 'Edit' : 'Create' }} New Recipient</div>
      <div v-else
           class="font-18 mb-2">View Recipient</div>
      <div class="font-14 normal text-dark">Please fill in the information below to create a recipient for this campaign.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-6 col-xs-12">
          <q-input float-label="First Name"
                   autocomplete="no"
                   :disable="entity['Promo Status'] !== 'Pending'"
                   :error="$v.entity['First Name'].$error"
                   @blur="$v.entity['First Name'].$touch"
                   v-model="entity['First Name']"></q-input>
        </div>
        <div class="col-sm-6 col-xs-12">
          <q-input float-label="Last Name"
                   autocomplete="no"
                   :disable="entity['Promo Status'] !== 'Pending'"
                   :error="$v.entity['Last Name'].$error"
                   @blur="$v.entity['Last Name'].$touch"
                   v-model="entity['Last Name']"></q-input>
        </div>
        <div class="col-sm-6 col-xs-12">
          <q-input float-label="Email"
                   autocomplete="no"
                   :disable="entity['Promo Status'] !== 'Pending'"
                   :error="$v.entity['Email Address'].$error"
                   @blur="$v.entity['Email Address'].$touch"
                   v-model="entity['Email Address']"></q-input>
        </div>
        <div class="col-sm-6 col-xs-12">
          <q-input float-label="Phone"
                   autocomplete="no"
                   type="number"
                   :disable="entity['Promo Status'] !== 'Pending'"
                   :min="1"
                   v-model="entity['Phone']"></q-input>
        </div>
        <div class="col-12">
          <q-input float-label="Amount to Receive ($)"
                   autocomplete="no"
                   type="number"
                   :disable="entity['Promo Status'] !== 'Pending'"
                   :min="1"
                   :error="$v.entity['Amount to Sent'].$error"
                   @blur="$v.entity['Amount to Sent'].$touch"
                   v-model="entity['Amount to Sent']"></q-input>
        </div>
        <div v-if="edit"
             class="pt-10 col-12">
          <div class="bold">Status</div>
          <div class="mt-8">
            <q-radio v-model="entity['Status']"
                     color="blue"
                     :disable="entity['Promo Status'] !== 'Pending'"
                     val="Active"
                     label="Enable"></q-radio>
            <q-radio v-model="entity['Status']"
                     color="blue"
                     class="ml-15"
                     :disable="entity['Promo Status'] !== 'Pending'"
                     val="Inactive"
                     label="Inactive"></q-radio>
          </div>
        </div>
      </div>
    </template>
    <template v-if="entity['Promo Status'] === 'Pending'"
              slot="buttons">
      <div class="stacks">
        <div v-if="!edit"
             class="row">
          <q-btn label="Back"
                 no-caps
                 class="back-btn"
                 @click="back" />
          <q-btn v-if="entity['campaign'] && entity['campaign']['isCreateCampaign']"
                 label="Add Recipient"
                 no-caps
                 class="create-btn"
                 @click="save" />
          <q-btn v-else
                 label="Create Recipient"
                 no-caps
                 class="create-btn"
                 @click="save" />
        </div>
        <div v-else
             class="row">
          <q-btn label="Cancel"
                 no-caps
                 class="back-btn"
                 @click="_hide" />
          <q-btn label="Save"
                 no-caps
                 class="create-btn"
                 @click="save" />
        </div>
      </div>
    </template>
    <template v-else
              slot="buttons">
      <q-btn label="Close"
             no-caps
             class="back-btn"
             @click="_hide" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request } from '../../../common'
import { required, minValue } from 'vuelidate/lib/validators'

export default {
  name: 'wilen-campaign-recipient-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        'Status': 'Active',
        'isCreateCampaign': false,
        'Amount to Sent': 0,
        'Promo Status': 'Pending'
      }
    }
  },
  computed: {
    edit () {
      if (this.entity['ID']) {
        return true
      }
      return false
    }
  },
  validations: {
    entity: {
      'First Name': {
        required
      },
      'Last Name': {
        required
      },
      'Email Address': {
        required
      },
      'Amount to Sent': {
        required,
        minValue: minValue(0.01)
      }
    }
  },
  methods: {
    show () {
      this.entity['Amount to Sent'] = this.entity['Amount to Sent'] / 100
    },
    back () {
      if (this.entity['campaign'] && this.entity['campaign']['isCreateCampaign']) {
        this._hide()
        this.$root.$emit('show-wilen-campaign-edit-dialog', this.entity.campaign)
      } else {
        this._hide()
        this.$root.$emit('show-wilen-campaign-import-recipient-dialog')
      }
    },
    onSuccess (resp) {
      notify(resp.message)

      this.$root.$emit('reload-wilen-campaign-detail')
      this._hide()
    },
    async save () {
      console.log(this.entity)
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      console.log(this.entity)
      if (this.entity['campaign'] && this.entity['campaign']['isCreateCampaign']) {
        this.entity['campaign'].Recipients.push({
          'firstName': this.entity['First Name'],
          'lastName': this.entity['Last Name'],
          'email': this.entity['Email Address'],
          'phone': this.entity['Phone'],
          'amount': this.entity['Amount to Sent']
        })
        this._hide()
        this.$root.$emit('show-wilen-campaign-edit-dialog', this.entity.campaign)
      } else {
        this.$q.loading.show()
        const resp = await request(`/admin/wilen/campaignRecipient/save`, 'post', this.entity)
        this.$q.loading.hide()
        if (resp.success) {
          this.onSuccess(resp)
        }
      }
    }
  }
}
</script>

<style lang="scss">
.wilen-campaign-recipient-dialog {
  .modal-content {
    width: 550px;
  }
  .q-btn.create-btn {
    background: #005e92 !important;
    color: #fff;
  }
  .q-btn.back-btn {
    background: #f1f1f6 !important;
    color: #696975;
  }
}
</style>
