<template>
  <q-card class="topAgents">
    <q-card-title>
      <div class="row">
        <div class="chart-title">
          <p>Top Agents</p>
        </div>
        <q-btn class="topAgent-btn agent-btn btn-sm ml-auto mr-8 square"
               @click="view()"><img src="/static/abel/icons/wilen_agents.svg"></q-btn>

      </div>
    </q-card-title>
    <q-card-main class="chart-item">
      <div id="topAgentsChart"></div>
      <div v-if="visible"
           class="loading">
        <q-spinner-hourglass color="primary"
                             size="3em"
                             :thickness="2" />
      </div>
    </q-card-main>
  </q-card>
</template>

<script>
import echarts from 'echarts'
import $ from 'jquery'
import { request, EventHandlerMixin, moneyFormat } from '../../../common'

export default {
  props: {
    'value': {
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  watch: {
    value () {
      this.getData()
    }
  },
  mixins: [
    EventHandlerMixin('reload-top-agents-chart', 'getData')
  ],
  data () {
    return {
      delta: 0,
      visible: false,
      chart: null,
      keys: [],
      programList: [],
      series: []
    }
  },
  async mounted () {
    this.getData()
  },
  methods: {
    resize () {
      this.chart.resize()
    },
    view () {
      this.$router.push(`/h/wilen/agents`)
    },
    async getData () {
      this.visible = true
      const resp = await request(`/admin/wilen/topAgentChart`, 'get')
      if (resp.success) {
        this.initChart(resp.data)
      }
      this.visible = false
    },
    initChart (chartData) {
      this.chart = echarts.init(document.getElementById('topAgentsChart'), 'primary')
      let positionStr = ''
      let colorList = ['#0062ff', '#ff974a', '#3dd598', '#ffc542', '#7a62f9']
      let labelList = []
      let seriesData = []
      chartData.forEach(element => {
        labelList.push(element['name'])
        seriesData.push(
          {
            'name': element['name'],
            'value': element['count']
          })
      })
      this.series = {
        data: seriesData,
        type: 'bar',
        itemStyle: {
          barBorderRadius: [10, 10, 0, 0],
          color: function (params) {
            return colorList[params.dataIndex]
          }
        },
        label: {
          show: true,
          position: 'bottom',
          color: '#92929e',
          formatter: function (params) {
            // build a color map as your need.
            return labelList[params.dataIndex]
          }
        },
        barMaxWidth: '40px'
      }
      let option = {
        legend: {
          bottom: '0px',
          icon: 'pin',
          data: ['']
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: '#ffffff',
          position: function (pos, params, dom, rect, size) {
            // 鼠标在左侧时 tooltip 显示到右侧，鼠标在右侧时 tooltip 显示到左侧。
            let obj = { top: pos[1] - 200 }
            if (pos[0] < size.viewSize[0] / 2) {
              obj['left'] = pos[0] - 50
              positionStr = 'left'
            } else {
              positionStr = 'right'
              obj['right'] = size.viewSize[0] - pos[0] - 50
            }
            // console.log(obj)
            return obj
          },
          padding: 0,
          formatter: function (params) {
            // console.log('234567uijhgfd')
            // console.log(params)
            return '<div class="tooltip-area-' + positionStr + '" style="color:#1c1446;font-weight: 600;box-shadow: 0 5px 15px 0 rgba(28, 20, 70, 0.1);border-radius:4px;padding:30px;padding-top:16px;font-family: Poppins;font-size: 14px;"><p style="text-align:center;margin:0px;">' + labelList[params.dataIndex] + '</p>' +
              '<div style="line-height:30px;min-width:235px;display:flex;justify-content:space-between;color:#231f20;"># of Programs<span style="color:#696975;font-weight: 500;">' + params.value + '</span></div>' +
              '<div style="line-height:30px;min-width:235px;display:flex;justify-content:space-between;color:#231f20;">$ in Campaigns Spend<span style="color:#696975;font-weight: 500;">' + moneyFormat(chartData[params.dataIndex].campaignSpend * 100) + '</span></div>' +
              '<div style="line-height:30px;min-width:235px;display:flex;justify-content:space-between;color:#231f20;"># of Campaigns Spend<span style="color:#696975;font-weight: 500;">' + chartData[params.dataIndex].campaignSpendCount + '</span></div>' +
              '<div style="line-height:30px;min-width:235px;display:flex;justify-content:space-between;color:#231f20;">Avg Campaigns Spend<span style="color:#696975;font-weight: 500;">' + moneyFormat(chartData[params.dataIndex].avgCampaignSpend * 100) + '</span></div>' +
              '</div>'
          }
        },
        xAxis: {
          type: 'category'
        },
        grid: {
          top: '10px',
          left: '12%',
          right: '4%',
          bottom: '40px'
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          }
        },
        series: this.series
      }
      // console.log(option)
      this.chart.setOption(option)

      $(window).on('resize', this.resize)
    }
  }
}
</script>
<style lang="scss">
.topAgents {
  .chart-title {
    max-width: calc(100% - 65px);
    p {
      margin: 0;
      font-size: 25px;
      font-weight: 600;
      line-height: 35px;
      margin-top: 10px;
    }
  }

  .topAgent-btn {
    height: 55px !important;
    width: 55px;
    color: #fff;
  }
  .agent-btn {
    background: #005e92;
  }

  #topAgentsChart {
    height: 300px;
  }
  .chart-item {
    position: relative;
  }
  .loading {
    height: 300px;
    line-height: 300px;
    width: calc(100% - 22px);
    text-align: center;
    position: absolute;
    top: 0;
    z-index: 100;
    background: rgba($color: #000000, $alpha: 0.1);
  }
  .tooltip-area-left :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    left: 40px;
  }
  .tooltip-area-right :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    right: 40px;
  }
}
</style>
