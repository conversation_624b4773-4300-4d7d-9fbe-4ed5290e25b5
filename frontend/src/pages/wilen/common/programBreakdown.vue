<template>
  <q-card class="programBreakdown">
    <q-card-title>
      <div class="row">
        <div class="chart-title">
          <p>Program Breakdown</p>
        </div>
        <q-btn class="programBreakdown-btn program-btn btn-sm ml-auto mr-8 square"
               @click="view()"><img src="/static/abel/icons/wilen_programs.svg"></q-btn>

      </div>
    </q-card-title>
    <q-card-main class="chart-item">
      <div id="programBreakdownChart"></div>
      <!-- <div class="programList">
        <p v-for="(item,i) in keys"
           :key="i">
          <i :style="'backgroundColor:' + colorList[i] + ';width:10px; height: 10px;border-radius:5px;display: inline-block;margin-right: 5px;'"></i>{{item}}
        </p>
      </div> -->
      <div v-if="visible"
           class="loading">
        <q-spinner-hourglass color="primary"
                             size="3em"
                             :thickness="2" />
      </div>
    </q-card-main>
  </q-card>
</template>

<script>
import echarts from 'echarts'
import $ from 'jquery'
import { request, EventHandlerMixin, moneyFormat } from '../../../common'

export default {
  props: {
    'value': {
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  watch: {
    value () {
      this.getData()
    }
  },
  mixins: [
    EventHandlerMixin('reload-program-breakdown-chart', 'getData')
  ],
  data () {
    return {
      delta: 0,
      visible: false,
      chart: null,
      keys: [],
      programList: [],
      series: {
        name: 'Program breakdown',
        type: 'pie',
        radius: ['60%', '80%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        labelLine: {
          show: false
        },
        data: []
      }
    }
  },
  async mounted () {
    this.getData()
  },
  methods: {
    resize () {
      this.chart.resize()
    },
    view () {
      this.$router.push(`/h/wilen/programs`)
    },
    async getData () {
      this.visible = true
      const resp = await request(`/admin/wilen/programBreakdownChart`, 'get')
      if (resp.success) {
        this.series.data = [{
          value: 0,
          name: 'Total',
          label: {
            show: true,
            fontSize: '14',
            fontWeight: '600',
            align: 'center',
            color: '#000000',
            formatter: [
              '{a|Total Program Balance}',
              '{b|' + moneyFormat(resp.data.balance) + '}'
            ].join('\n\n'),
            rich: {
              a: {
                color: '#000',
                fontSize: 12,
                lineHeight: 10
              },
              b: {
                fontSize: 16,
                fontWeight: 600,
                color: '#000'
              }
            }
          }
        }]
        this.keys = resp.data.key
        resp.data.data.forEach(element => {
          this.series.data.push(element)
        })
        // console.log(this.series)
        this.delta = resp.data.balance
        // this.keys = resp.data.key
        this.programList = resp.data.program
        this.initChart()
      }
      this.visible = false
    },
    initChart () {
      this.chart = echarts.init(document.getElementById('programBreakdownChart'), 'primary')
      let that = this
      let positionStr = ''
      this.series.emphasis = {
        label: {
          show: true,
          backgroundColor: '#fff',
          // padding: 20,
          formatter: function (params) {
            // console.log(params)
            if (params.name === 'Total') {
              return [
                '{a|' + params.name + ' Program Balance}',
                '{b|' + moneyFormat(that.delta) + '}'
              ].join('\n\n')
            } else {
              return [
                '{a|' + params.name + ' Program Balance}',
                '{b|' + moneyFormat(params.value) + '}'
              ].join('\n\n')
            }
          },
          rich: {
            a: {
              color: '#000',
              fontSize: 12,
              lineHeight: 12
            },
            b: {
              fontSize: 16,
              fontWeight: 600,
              color: '#000'
              // backgroundColor: '#fff',
              // width: '100%'
            }
          }
        }
      }
      let option = {
        tooltip: {
          trigger: 'item',
          backgroundColor: '#ffffff',
          padding: 0,
          show: true,
          position: function (pos, params, dom, rect, size) {
            // 鼠标在左侧时 tooltip 显示到右侧，鼠标在右侧时 tooltip 显示到左侧。
            let obj = { top: pos[1] - 150 }
            if (pos[0] < size.viewSize[0] / 2) {
              obj['left'] = pos[0] - 50
              positionStr = 'left'
            } else {
              positionStr = 'right'
              obj['right'] = size.viewSize[0] - pos[0] - 50
            }
            // console.log(obj)
            return obj
          },
          formatter: function (params) {
            // console.log(params)
            if (params.name === 'Total') {
              return ''
            }
            return '<div class="tooltip-area-' + positionStr + '" style="min-width: 300px;box-shadow: 0 5px 15px 0 rgba(28, 20, 70, 0.1);border-radius:4px;padding:30px;padding-top:16px;font-family: Poppins;font-size: 14px;"><p style="text-align:center;margin:0px;color:#231f20;font-weight: 600;">' + params.name + '</p>' +
            '<p style="text-align:left;margin:0px;color:#231f20;display:flex;justify-content:space-between;"><span># of Campaigns</span><span>' + that.programList[params.name]['count'] + '</span></p>' +
            '<p style="text-align:left;margin:0px;color:#231f20;display:flex;justify-content:space-between;"><span>$ in Campaigns Spend</span><span>' + moneyFormat(that.programList[params.name]['campaignSpend'] * 100) + '</span></p>' +
            '<p style="text-align:left;margin:0px;color:#231f20;display:flex;justify-content:space-between;"><span># of Campaign Spend</span><span>' + that.programList[params.name]['campaignSpendCount'] + '</span></p>' +
            '<p style="text-align:left;margin:0px;color:#231f20;display:flex;justify-content:space-between;"><span>Avg Campaign Spend</span><span>' + moneyFormat(that.programList[params.name]['avgCampaignSpend'] * 100) + '</span></p></div>'
          }
        },
        legend: {
          type: 'scroll',
          bottom: '0%',
          left: 'center',
          icon: 'circle',
          data: this.keys
        },
        grid: {
          left: '0',
          right: '0',
          bottom: '20px',
          top: '0'
        },
        series: this.series
      }
      // console.log(option)
      this.chart.setOption(option)

      $(window).on('resize', this.resize)
    }
  }
}
</script>
<style lang="scss">
.programBreakdown {
  .chart-title {
    max-width: calc(100% - 65px);
    p {
      margin: 0;
      font-size: 25px;
      font-weight: 600;
      line-height: 35px;
      margin-top: 10px;
    }
  }

  .programBreakdown-btn {
    height: 55px !important;
    width: 55px;
    color: #fff;
  }
  .program-btn {
    background: #005e92;
  }

  #programBreakdownChart {
    height: 300px;
  }
  .chart-item {
    position: relative;
  }
  .loading {
    height: 300px;
    line-height: 300px;
    width: calc(100% - 22px);
    text-align: center;
    position: absolute;
    top: 0;
    z-index: 100;
    background: rgba($color: #000000, $alpha: 0.1);
  }
  .programList {
    max-height: 60px;
    display: flex;
    justify-content: space-around;
    overflow: scroll;
  }
  .tooltip-area-left :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    left: 40px;
  }
  .tooltip-area-right :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    right: 40px;
  }
}
</style>
