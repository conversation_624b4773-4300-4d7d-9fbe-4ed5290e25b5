<template>
  <q-page id="wilen__dashboard__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div v-if="$store.state.User.teams.indexOf('Wilen Agent') !== -1"
           class="fun-group">
        <q-select v-model="selectedProgram"
                  class="dense"
                  @input="reload()"
                  :options="programList"></q-select>
      </div>
    </div>
    <div class="page-content">
      <div v-if="$store.state.User.teams.indexOf('Wilen Agent') === -1"
           class="row gutter-sm">
        <div class="col-sm-3 col-xs-6"
             v-for="(n, i) in numbers"
             :key="i">
          <q-card>
            <q-card-title>
              <span>{{ n.title }}</span>
            </q-card-title>
            <q-card-main>
              <div v-if="n.title != 'Campaign Sent ($)'"
                   class="font-22 heavy mb-10">{{ n.value }}</div>
              <div v-else
                   class="font-22 heavy mb-10">{{ n.value | moneyFormat}}</div>
              <a :href="n.url"
                 @click="viewReport(n.title)"
                 class="link bold font-12">{{ n.viewText }} <i class="mdi mdi-arrow-right"></i></a>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <div class="row m--10 mt-10">
        <div class="col-sm-4 col-xs-12">
          <q-card class="left-item">
            <div v-if="$store.state.User.teams.indexOf('Wilen Agent') === -1"
                 class="bank-info">
              <h2>Available Balance</h2>
              <h1>{{ balance | moneyFormat}}</h1>
              <div class="card-info">
                <div class="card-logo-area">
                  <img src="/static/wilen/wilen-emblem.svg">
                  <span class="blod">wilen</span><span class="fine">group</span>
                </div>
                <div class="card-number-area">
                  <div>
                    <p class="desc-item">Routing Number<i class="mdi mdi-eye-settings"></i></p>
                    <p v-if="hideNumber"
                       class="number">••• ••• •••</p>
                    <p v-else
                       class="number">*********</p>
                  </div>
                  <div>
                    <p class="desc-item">Account Number<i class="mdi mdi-eye-settings"></i></p>
                    <p v-if="hideNumber"
                       class="number">••• •••• ••••</p>
                    <p v-else
                       class="number">**********</p>
                  </div>
                </div>
                <div v-if="!hideNumber"
                     class="card-beneficiary-area">
                  <p class="desc-item">BeneficiaryInformation</p>
                  <p class="number">Swift Prepaid Solutions, Inc.2150 E. Lake Cook Rd. Suite 150Buffalo Grove, IL 60089</p>
                </div>
              </div>
              <q-btn v-if="hideNumber"
                     class="view-btn"
                     @click="viewDetail()">View Banking Details
              </q-btn>
              <q-btn v-else
                     class="view-btn"
                     @click="hideDetail()">Hide Banking Details</q-btn>
            </div>
            <div class="list-info">
              <div class="recent-header"
                   :class="$store.state.User.teams.indexOf('Wilen Agent') !== -1 ? 'center' : ''">
                <h1 v-if="$store.state.User.teams.indexOf('Wilen Agent') === -1">Campaigns</h1>
                <h1 v-else>Recent Campaigns</h1>
                <span @click="viewReport('Campaigns Sent')"
                      v-if="$store.state.User.teams.indexOf('Wilen Agent') === -1">View All</span>
              </div>
              <div class="recent-list">
                <q-list :class="$store.state.User.teams.indexOf('Wilen Agent') !== -1 ? 'agent' : ''">
                  <q-item v-for="(recent, key) in recentCampaign"
                          :key="key">
                    <q-item-side class="item-desc">
                      <span v-if="recent['isCompleted']"
                            class="campaign-icon completed-icon"><i class="mdi mdi-arrow-down"></i></span>
                      <span v-else
                            class="campaign-icon"><i class="mdi mdi-plus"></i></span>
                      <div>
                        <p class="item-header">{{recent['programName']}}#{{recent['campaignName']}}</p>
                        <p>Recipients: {{ recent['recipientCount']}}</p>
                        <p>{{recent['sendDate']}}</p>
                      </div>
                    </q-item-side>
                    <q-item-side>
                      <p>{{ recent['campaignBudget'] | moneyFormat}}</p>
                      <q-chip v-if="recent['isCompleted']"
                              class="font-12 status-item positive">
                        Completed
                      </q-chip>
                      <q-chip v-else
                              class="font-12 status-item negative">
                        Scheduled
                      </q-chip>
                    </q-item-side>
                  </q-item>
                </q-list>
              </div>
            </div>
          </q-card>
        </div>
        <div class="col-sm-8 col-xs-12">
          <q-card class="right-item">
            <AccountBalance v-if="$store.state.User.teams.indexOf('Wilen Agent') === -1"></AccountBalance>
            <CampaignDollars v-model="selectedProgram"
                             v-if="$store.state.User.teams.indexOf('Wilen Agent') !== -1"></CampaignDollars>
          </q-card>
          <div class="right-item">
            <div class="row">
              <div class="col-sm-6 col-xs-12">
                <ProgramBreakdown v-model="selectedProgram"></ProgramBreakdown>
              </div>
              <div class="col-sm-6 col-xs-12">
                <q-card v-if="$store.state.User.teams.indexOf('Wilen Agent') !== -1"
                        class="campaigns-list">
                  <p class="title">Upcoming Campaigns</p>
                  <div class="campaign-item">
                    <q-list v-if="comingCampaign.length">
                      <q-item v-for="(coming, key) in comingCampaign"
                              :key="key">
                        <q-item-side class="item-desc">
                          <span class="campaign-icon"><i class="mdi mdi-plus"></i></span>
                          <div>
                            <p class="item-header">{{coming['campaignName']}}</p>
                            <p>Recipients: {{ coming['recipientCount']}}</p>
                            <p>{{coming['sendDate']}}</p>
                          </div>
                        </q-item-side>
                        <q-item-side>
                          <p>{{ coming['campaignBudget'] | moneyFormat}}</p>
                          <q-chip class="font-12 status-item positive">
                            Active
                          </q-chip>
                        </q-item-side>
                      </q-item>
                    </q-list>
                    <div class="no-campaign"
                         v-else>
                      <p>There are currently no upcoming campaigns.</p>
                      <q-btn class="create-btn"
                             @click="createCampaign()">Create New Campaign
                      </q-btn>
                    </div>
                  </div>
                </q-card>
                <AgentTop v-else></AgentTop>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <EditCampaign></EditCampaign>
  </q-page>
</template>

<script>
import AccountBalance from '../common/accountBalance'
import ProgramBreakdown from '../common/programBreakdown'
import CampaignDollars from '../common/campaignDollars'
import { request, toSelectOptions } from '../../../common'
import EditCampaign from './../campaigns/edit'
import ListPageMixin from '../../../mixins/ListPageMixin'
import AgentTop from '../common/agentTop'

export default {
  name: 'wilen-Dashboard',
  mixins: [
    ListPageMixin
  ],
  components: {
    AccountBalance,
    ProgramBreakdown,
    CampaignDollars,
    EditCampaign,
    AgentTop
  },
  data () {
    return {
      title: 'Dashboard',
      numbers: [
        { title: 'Number of Agents', value: '0', url: 'javascript:', viewText: 'View Users' },
        { title: 'Active Programs', value: '0', url: 'javascript:', viewText: 'View Programs' },
        { title: 'Campaigns Sent', value: '0', url: 'javascript:', viewText: 'View Campaigns' },
        { title: 'Campaign Sent ($)', value: '0', url: 'javascript:', viewText: 'View Campaigns' }
      ],
      hideNumber: true,
      routingNumber: '',
      accountNumber: '',
      programList: [],
      selectedProgram: 'all',
      comingCampaign: [],
      recentCampaign: [],
      balance: 0
    }
  },
  methods: {
    async getPrograms () {
      const res = await request('/admin/wilen/programs/agentAll', 'get')
      if (res.success) {
        this.programList = toSelectOptions(res.data)
      }
    },
    async reload () {
      this.$q.loading.show()
      const res = await request('/admin/wilen/dashboard/static', 'get', {
        'programId': this.selectedProgram
      })
      if (res.success) {
        this.numbers[0].value = res.data.agentsCount
        this.numbers[1].value = res.data.activePrograms
        this.numbers[2].value = res.data.compaignsSendCount
        this.numbers[3].value = res.data.compaignsSendTotal
        this.comingCampaign = res.data.comingCampaign
        this.recentCampaign = res.data.recentCampaign
        this.balance = res.data.balance
      }
      this.$q.loading.hide()
    },
    hideDetail () {
      this.hideNumber = true
    },
    viewDetail () {
      this.hideNumber = false
    },
    viewReport (type) {
      if (type === 'Number of Agents') {
        this.$router.push(`/h/wilen/agents`)
      }
      if (type === 'Active Programs') {
        this.$router.push(`/h/wilen/programs`)
      }
      if (type === 'Campaigns Sent' || type === 'Campaign Sent ($)') {
        this.$router.push(`/h/wilen/campaigns`)
      }
    },
    createCampaign () {
      this.$root.$emit('show-wilen-campaign-edit-dialog')
    }
  },
  async mounted () {
    this.reload()
    if (this.$store.state.User.teams.indexOf('Wilen Agent') !== -1) {
      this.getPrograms()
    }
  }
}
</script>
<style lang="scss">
#wilen__dashboard__index_page {
  a.link {
    color: #005e92 !important;
    display: flex;
    align-items: center;
    i {
      display: flex;
      margin-left: 5px;
    }
  }
  .fun-group .q-select {
    min-width: 150px;
  }
  .q-card {
    margin: 10px;
  }
  .gutter-sm .q-card {
    margin: 0px;
  }
  .left-item {
    background: #fff;
    margin-top: 10px;
    .bank-info {
      text-align: center;
      padding-top: 30px;
      h2 {
        font-size: 25px;
        font-weight: 600;
        margin: 0;
      }
      h1 {
        font-size: 35px;
        margin: 0;
      }
      .card-info {
        min-height: 219px;
        max-width: 90%;
        margin: 0 auto;
        margin-top: 30px;
        -webkit-backdrop-filter: blur(41px);
        backdrop-filter: blur(41px);
        border-radius: 25px;
        background-image: linear-gradient(
          to bottom,
          rgba(0, 0, 0, 0.1),
          rgba(0, 0, 0, 0.6)
        );
        .card-logo-area {
          display: flex;
          align-items: center;
          padding-top: 2em;
          margin-left: 1em;
          font-size: 20px;
          text-transform: Uppercase;
          color: #fff;
          .blod {
            font-weight: 800;
            padding: 5px;
          }
          .fine {
            font-weight: 300;
          }
        }
        .card-number-area {
          display: flex;
          color: #fff;
          font-size: 14px;
          padding-top: 1em;
          font-weight: normal;
          justify-content: space-around;
          align-items: center;
          .desc-item {
            margin-bottom: 0;
            display: flex;
            align-items: center;
            i {
              margin-top: 4px;
              margin-left: 5px;
            }
          }
          .number {
            font-size: 20px;
          }
        }
        .card-beneficiary-area {
          color: #fff;
          font-size: 14px;
          .desc-item,
          .number {
            margin-bottom: 0;
            align-items: center;
          }
          .number {
            padding: 10px 20px;
          }
        }
      }
      .view-btn {
        background: #005e92;
        // margin-bottom: 30px;
        margin-top: 20px;
        color: #fff;
        text-transform: capitalize;
      }
    }
    .list-info {
      .recent-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30px 30px 20px 30px;
        //margin-bottom: 10px;
        h1 {
          font-size: 20px;
          font-weight: 600;
          margin: 0px;
        }
        span {
          color: #005e92;
          cursor: pointer;
        }
      }
      .recent-header.center {
        justify-content: center;
      }
    }
  }
  .right-item {
    margin-top: 10px;
    .campaigns-list {
      height: 414px;
      .q-list {
        border: none;
        max-height: calc(414px - 100px);
        overflow: scroll;
      }
      .q-item {
        border-top: 1px solid #ccc;
        padding-top: 20px;
        justify-content: space-between;
        align-items: flex-start;
      }
      .item-desc {
        display: flex;
      }
      .title {
        margin: 0;
        font-size: 25px;
        font-weight: 600;
        text-align: center;
        padding-top: 32px;
        color: #000;
        margin-bottom: 30px;
      }
      .item-header {
        font-size: 15px;
        font-weight: 600;
        word-break: break-all;
        color: #000000 !important;
      }
      .campaign-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 1em;
        max-height: 60px;
        background: rgba(#0062ff, 0.1);
        // position: relative;
        border-radius: 4em;
        margin-right: 10px;
        i {
          color: #0062ff;
          font-size: 34px;
          line-height: 2em;
          // left: 12px;
          // position: absolute;
        }
      }
      p {
        margin: 0;
      }
      .item-desc {
        max-width: 60%;
        p {
          font-size: 12px;
          color: #959595;
          margin-bottom: 6px;
        }
      }
      .no-campaign {
        text-align: center;
        max-width: 290px;
        margin: 0 auto;
        p {
          font-size: 14px;
          color: #696974;
        }
        .create-btn {
          border-radius: 10px;
          background-color: #005e92;
          margin-top: 20px;
          color: #fff;
          text-transform: capitalize;
        }
      }
    }
  }
  .recent-list {
    .q-list {
      border: none;
      height: 458px;
      overflow: scroll;
    }
    .q-list.agent {
      height: 878px;
    }
    .q-item {
      border-top: 1px solid #ccc;
      padding-top: 20px;
      justify-content: space-between;
      align-items: flex-start;
    }
    .item-desc {
      display: flex;
    }
    .title {
      margin: 0;
      font-size: 25px;
      font-weight: 600;
      text-align: center;
      padding-top: 32px;
      color: #000;
      margin-bottom: 30px;
    }
    .item-header {
      font-size: 15px;
      font-weight: 600;
      word-break: break-all;
      color: #000000 !important;
    }
    .campaign-icon {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 1em;
      max-height: 2em;
      background: rgba(#0062ff, 0.1);
      // position: relative;
      border-radius: 4em;
      margin-right: 10px;
      max-width: 60px;
      max-height: 60px;
      i {
        color: #0062ff;
        font-size: 34px;
        line-height: 2em;
        // left: 12px;
        // position: absolute;
      }
    }
    .completed-icon {
      background: rgba(#fc5a5a, 0.1);
      i {
        color: #fc5a5a;
      }
    }
    p {
      margin: 0;
    }
    .item-desc {
      max-width: 60%;
      p {
        font-size: 12px;
        color: #959595;
        margin-bottom: 6px;
      }
    }
  }
}
</style>
