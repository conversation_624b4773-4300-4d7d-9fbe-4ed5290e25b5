<template>
  <q-dialog class="wilen-program-recipient-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-18 mb-2">Configure SMS for Program</div>
      <div class="font-14 normal text-dark">Please fill in the information below you wish to include in your SMS.</div>
      <q-btn v-if="entity['campaign']"
             class="close"
             round
             flat
             @click="back"
             icon="close" />
      <q-btn v-else
             class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-12 content-area">
          <q-input float-label="Please Write Your Message"
                   :error="$v.entity['content'].$error"
                   @blur="$v.entity['content'].$touch"
                   v-model="entity['content']"
                   filled
                   rows="6"
                   :maxlength="260"
                   :max-height="140"
                   type="textarea" />
          <p class="limit-desc">Character Limit: {{entity['content'] ? entity['content'].length : 0}}/260</p>
        </div>
        <div class="col-12 desc-item">
          <h1>Population Keys</h1>
          <span class="first">{First Name} </span> <span class="second">{Amount}</span> <span class="third"> {Program}</span><span class="fourth"> {Email}</span>
        </div>
        <div class="col-12 desc-item">
          <h1>Note</h1>
          <p>SMS must be enabled per campaign</p>
          <p class="red-note red">SMS is configured at the PROGRAM not the campaign level</p>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <q-btn label="Configure"
               no-caps
               class="create-btn"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request } from '../../../common'
import { required } from 'vuelidate/lib/validators'

export default {
  name: 'wilen-program-configure-sms-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        'content': 'Hello {First name}, your {Amount} {Program} has been delivered to your email at {Email}.'
      }
    }
  },
  computed: {
  },
  validations: {
    entity: {
      'content': {
        required
      }
    }
  },
  methods: {
    onSuccess (resp) {
      notify(resp.message)
      if (this.entity['campaign']) {
        this.entity['campaign']['isCreateCampaign'] = false
        this.$root.$emit('show-wilen-campaign-edit-dialog', this.entity['campaign'])
      } else {
        this.$root.$emit('reload-wilen-program')
      }
      this._hide()
    },
    back () {
      this.entity['campaign']['isCreateCampaign'] = true
      this.$root.$emit('show-wilen-campaign-edit-dialog', this.entity['campaign'])
      this._hide()
    },
    show () {
      if (this.entity['campaign']) {
        this.entity['SmsMessage'] = this.entity['campaign']['SmsMessage']
      }
      if (this.entity['SmsMessage'].length) {
        this.entity['content'] = this.entity['SmsMessage']
      }
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      const resp = await request(`/admin/wilen/program/configureSms`, 'post', {
        'id': this.entity['campaign'] ? this.entity['campaign']['programId'] : this.entity['id'],
        'content': this.entity['content']
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    }
  }
}
</script>

<style lang="scss">
.wilen-program-recipient-dialog {
  .modal-content {
    width: 550px;
  }
  .q-btn.create-btn {
    background: #005e92 !important;
    color: #fff;
  }
  .content-area {
    position: relative;
    padding-top: 18px;
    .limit-desc {
      position: absolute;
      right: 10px;
      bottom: -10px;
      opacity: 0.5;
      font-size: 14px;
    }
  }
  .desc-item {
    font-size: 14px;
    padding-top: 14px;
    h1 {
      line-height: 20px;
      font-size: 14px;
      font-weight: 600;
      letter-spacing: normal;
      margin: 0;
    }
    .first {
      color: red;
    }
    .second {
      color: #00a918;
    }
    .third {
      color: #f1a100;
    }
    .fourth {
      color: red;
    }
    p {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .red-note {
      font-size: 6px;
      margin: 0;
    }
  }
}
</style>
