<template>
  <q-page id="wilen__progams__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>

    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3 col-xs-12">
          <q-card>
            <q-card-title>
              <span>Active Programs</span>
            </q-card-title>
            <q-card-main>
              <div class="font-22 heavy mb-10">{{ quick.totalActive ?  quick.totalActive : 0 }}</div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>Programs Report</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8 export-btn mt-10"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense mt-10"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10 mt-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mt-10"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Program budget'">
              {{ props.row['Program budget'] | moneyFormat}}
            </template>
            <template v-else-if="col.field === 'Program balance'">
              {{ props.row['Program balance'] | moneyFormat}}
            </template>
            <template v-else-if="col.field === 'Amount of budget used'">
              {{ props.row['Amount of budget used'] | moneyFormat}}
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-if="props.row['Status'] === 'Active'"
                          v-close-overlay
                          @click.native="configureSms(props.row)">
                    <q-item-main>Configure SMS</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="viewCampaigns(props.row)">
                    <q-item-main>View Campaigns</q-item-main>
                  </q-item>
                  <!-- <q-item v-if="props.row['Status'] === 'Active'"
                          v-close-overlay
                          @click.native="cancel(props.row)">
                    <q-item-main>Cancel</q-item-main>
                  </q-item> -->
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <ConfigureSms></ConfigureSms>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, request, notify } from '../../../common'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import ConfigureSms from './configureSms'

export default {
  name: 'wilen-program',
  mixins: [
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-wilen-program')
  ],
  components: {
    ConfigureSms
  },
  data () {
    return {
      title: 'Programs',
      requestUrl: `/admin/wilen/programs/list`,
      downloadUrl: `/admin/wilen/programs/export`,
      keyword: '',
      columns: generateColumns([
        'Program Name', 'Program budget', 'Program balance', 'Amount of budget used', 'Program Admin', '# of Campaigns', 'Status', 'Actions'
      ], [], {
        'Program Name': 'p.programName',
        'Program budget': 'p.budget',
        'Amount of budget used': 'p.budgetUsed',
        'Program balance': 'p.availableBalance',
        'Program Admin': 'p.programAdmin',
        '# of Campaigns': 'length(p.campaignIds)'
      }),
      filterOptions: [
        {
          value: 'filter[p.programName=]',
          label: 'Program Name'
        },
        {
          value: 'filter[p.id=]',
          label: 'Program ID'
        },
        {
          value: 'filter[p.status=]',
          label: 'Status',
          options: [
            { label: 'Active', value: 'Active' },
            { label: 'Inactive', value: 'Inactive' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    configureSms (row) {
      this.$root.$emit('show-wilen-program-configure-sms-dialog', row)
    },
    async cancel (row) {
      this.$q.dialog({
        title: 'Cancel',
        message: 'Are you sure that you want to cancel?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/wilen/program/cancel`, 'post', { id: row['Program ID'] })
        if (resp.success) {
          notify(resp.message)
          this.reload()
        }
        this.$q.loading.hide()
      }).catch(() => {})
    },
    viewCampaigns (row) {
      const { href } = this.$router.resolve({
        path: `/h/wilen/campaigns`
      })
      localStorage.setItem('programId', row['id'])
      window.open(href, '_blank')
    },
    statusClass (status) {
      const cls = []
      cls.push({
        'Archived': 'orange',
        'Active': 'positive',
        'Inactive': 'negative'
      }[status] || status)
      return cls
    }
  }
}
</script>
<style lang="scss">
#wilen__progams__index_page {
  .q-btn.export-btn {
    background: #005e92 !important;
  }
  .q-btn.create-btn {
    background: #3dd598 !important;
  }
}
</style>
