import echarts from 'echarts'
import Color from 'color'
import _ from 'lodash'

export default ({ Vue }) => {
  const defaults = [
    '#c12e34',
    '#e6b600',
    '#0098d9',
    '#2b821d',
    '#7c4fff',
    '#005eaa',
    '#cda819',
    '#32a487'
  ]

  if (!window.ternCustomizes) {
    window.ternCustomizes = {}
  }

  const customizes = window.ternCustomizes
  const primary = customizes.adminPrimaryColor || '#308fd0'

  generateTheme('primary', [
    primary,
    customizes.adminPrimaryPositiveColor || '#35cd68',
    customizes.adminPrimaryNegativeColor || '#ff6501',
    customizes.adminSecondaryColor || '#116CAB',
    customizes.adminSecondaryPositiveColor || '#ACEAC1',
    customizes.adminSecondaryNegativeColor || '#FCB62B'
  ])
  generateTheme('clf', [ '#6abf69', '#03a5ed', '#f6511d' ])
  generateTheme('fis', [ '#9A47FF', '#4C84FF', '#B87EFF', '#81A8FF', '#D1ABFF', '#AEC7FF' ])

  function generateTheme (name, colors = []) {
    const light = Color(primary).lighten(0.3).hex()
    const dark = Color(primary).darken(0.3).hex()

    colors = _.filter(colors, c => c)
    colors = colors.concat(defaults)

    colors.splice(6, 0, dark)

    echarts.registerTheme(name, {
      'color': colors,
      'backgroundColor': 'rgba(255,255,255,0)',
      'textStyle': {},
      'title': {
        'textStyle': {
          'color': '#000'
        },
        'subtextStyle': {
          'color': '#888'
        }
      },
      'line': {
        'itemStyle': {
          'normal': {
            'borderWidth': '2'
          }
        },
        'lineStyle': {
          'normal': {
            'width': '2'
          }
        },
        'symbolSize': '8',
        'symbol': 'circle',
        'smooth': true
      },
      'radar': {
        'itemStyle': {
          'normal': {
            'borderWidth': '2'
          }
        },
        'lineStyle': {
          'normal': {
            'width': '2'
          }
        },
        'symbolSize': '8',
        'symbol': 'circle',
        'smooth': true
      },
      'bar': {
        'itemStyle': {
          'normal': {
            'barBorderWidth': '0',
            'barBorderColor': '#b5b5b5'
          },
          'emphasis': {
            'barBorderWidth': '0',
            'barBorderColor': '#b5b5b5'
          }
        }
      },
      'pie': {
        'itemStyle': {
          'normal': {
            'borderWidth': '0',
            'borderColor': '#b5b5b5'
          },
          'emphasis': {
            'borderWidth': '0',
            'borderColor': '#b5b5b5'
          }
        }
      },
      'scatter': {
        'itemStyle': {
          'normal': {
            'borderWidth': '0',
            'borderColor': '#b5b5b5'
          },
          'emphasis': {
            'borderWidth': '0',
            'borderColor': '#b5b5b5'
          }
        }
      },
      'boxplot': {
        'itemStyle': {
          'normal': {
            'borderWidth': '0',
            'borderColor': '#b5b5b5'
          },
          'emphasis': {
            'borderWidth': '0',
            'borderColor': '#b5b5b5'
          }
        }
      },
      'parallel': {
        'itemStyle': {
          'normal': {
            'borderWidth': '0',
            'borderColor': '#b5b5b5'
          },
          'emphasis': {
            'borderWidth': '0',
            'borderColor': '#b5b5b5'
          }
        }
      },
      'sankey': {
        'itemStyle': {
          'normal': {
            'borderWidth': '0',
            'borderColor': '#b5b5b5'
          },
          'emphasis': {
            'borderWidth': '0',
            'borderColor': '#b5b5b5'
          }
        }
      },
      'funnel': {
        'itemStyle': {
          'normal': {
            'borderWidth': '0',
            'borderColor': '#b5b5b5'
          },
          'emphasis': {
            'borderWidth': '0',
            'borderColor': '#b5b5b5'
          }
        }
      },
      'gauge': {
        'itemStyle': {
          'normal': {
            'borderWidth': '0',
            'borderColor': '#b5b5b5'
          },
          'emphasis': {
            'borderWidth': '0',
            'borderColor': '#b5b5b5'
          }
        }
      },
      'candlestick': {
        'itemStyle': {
          'normal': {
            'color': primary,
            'color0': 'transparent',
            'borderColor': colors[1],
            'borderColor0': colors[2],
            'borderWidth': '1'
          }
        }
      },
      'graph': {
        'itemStyle': {
          'normal': {
            'borderWidth': '0',
            'borderColor': '#b5b5b5'
          }
        },
        'lineStyle': {
          'normal': {
            'width': '1',
            'color': '#cccccc'
          }
        },
        'symbolSize': '8',
        'symbol': 'circle',
        'smooth': true,
        'color': colors,
        'label': {
          'normal': {
            'textStyle': {
              'color': '#ffffff'
            }
          }
        }
      },
      'map': {
        'itemStyle': {
          'normal': {
            'areaColor': '#eeeeee',
            'borderColor': primary,
            'borderWidth': '0.5'
          },
          'emphasis': {
            'areaColor': light,
            'borderColor': primary,
            'borderWidth': '1'
          }
        },
        'label': {
          'normal': {
            'textStyle': {
              'color': '#ffffff'
            }
          },
          'emphasis': {
            'textStyle': {
              'color': '#000000'
            }
          }
        }
      },
      'geo': {
        'itemStyle': {
          'normal': {
            'areaColor': '#eeeeee',
            'borderColor': primary,
            'borderWidth': '0.5'
          },
          'emphasis': {
            'areaColor': light,
            'borderColor': primary,
            'borderWidth': '1'
          }
        },
        'label': {
          'normal': {
            'textStyle': {
              'color': '#ffffff'
            }
          },
          'emphasis': {
            'textStyle': {
              'color': '#000000'
            }
          }
        }
      },
      'categoryAxis': {
        'axisLine': {
          'show': true,
          'lineStyle': {
            'color': '#888888'
          }
        },
        'axisTick': {
          'show': false,
          'lineStyle': {
            'color': '#000'
          }
        },
        'axisLabel': {
          'show': true,
          'textStyle': {
            'color': '#000'
          }
        },
        'splitLine': {
          'show': true,
          'lineStyle': {
            'color': [
              '#cccccc'
            ]
          }
        },
        'splitArea': {
          'show': false,
          'areaStyle': {
            'color': [
              'rgba(250,250,250,0.05)',
              'rgba(200,200,200,0.02)'
            ]
          }
        }
      },
      'valueAxis': {
        'axisLine': {
          'show': true,
          'lineStyle': {
            'color': '#888888'
          }
        },
        'axisTick': {
          'show': false,
          'lineStyle': {
            'color': '#000'
          }
        },
        'axisLabel': {
          'show': true,
          'textStyle': {
            'color': '#000'
          }
        },
        'splitLine': {
          'show': true,
          'lineStyle': {
            'color': [
              '#cccccc'
            ]
          }
        },
        'splitArea': {
          'show': false,
          'areaStyle': {
            'color': [
              'rgba(250,250,250,0.05)',
              'rgba(200,200,200,0.02)'
            ]
          }
        }
      },
      'logAxis': {
        'axisLine': {
          'show': true,
          'lineStyle': {
            'color': '#888888'
          }
        },
        'axisTick': {
          'show': false,
          'lineStyle': {
            'color': '#000'
          }
        },
        'axisLabel': {
          'show': true,
          'textStyle': {
            'color': '#000'
          }
        },
        'splitLine': {
          'show': true,
          'lineStyle': {
            'color': [
              '#cccccc'
            ]
          }
        },
        'splitArea': {
          'show': false,
          'areaStyle': {
            'color': [
              'rgba(250,250,250,0.05)',
              'rgba(200,200,200,0.02)'
            ]
          }
        }
      },
      'timeAxis': {
        'axisLine': {
          'show': true,
          'lineStyle': {
            'color': '#888888'
          }
        },
        'axisTick': {
          'show': false,
          'lineStyle': {
            'color': '#000'
          }
        },
        'axisLabel': {
          'show': true,
          'textStyle': {
            'color': '#000'
          }
        },
        'splitLine': {
          'show': true,
          'lineStyle': {
            'color': [
              '#cccccc'
            ]
          }
        },
        'splitArea': {
          'show': false,
          'areaStyle': {
            'color': [
              'rgba(250,250,250,0.05)',
              'rgba(200,200,200,0.02)'
            ]
          }
        }
      },
      'toolbox': {
        'iconStyle': {
          'normal': {
            'borderColor': '#999999'
          },
          'emphasis': {
            'borderColor': '#666666'
          }
        }
      },
      'legend': {
        'textStyle': {
          'color': '#666666'
        }
      },
      'tooltip': {
        'axisPointer': {
          'lineStyle': {
            'color': '#000000',
            'width': '1'
          },
          'crossStyle': {
            'color': '#000000',
            'width': '1'
          }
        }
      },
      'timeline': {
        'lineStyle': {
          'color': primary,
          'width': '1'
        },
        'itemStyle': {
          'normal': {
            'color': primary,
            'borderWidth': '1'
          },
          'emphasis': {
            'color': dark
          }
        },
        'controlStyle': {
          'normal': {
            'color': primary,
            'borderColor': primary,
            'borderWidth': 0.5
          },
          'emphasis': {
            'color': dark,
            'borderColor': dark,
            'borderWidth': 0.5
          }
        },
        'checkpointStyle': {
          'color': dark,
          'borderColor': light
        },
        'label': {
          'normal': {
            'textStyle': {
              'color': primary
            }
          },
          'emphasis': {
            'textStyle': {
              'color': dark
            }
          }
        }
      },
      'visualMap': {
        'color': [
          light,
          dark
        ]
      },
      'dataZoom': {
        'backgroundColor': 'rgba(255,255,255,0)',
        'dataBackgroundColor': 'rgba(222,222,222,1)',
        'fillerColor': light,
        'handleColor': primary,
        'handleSize': '100%',
        'textStyle': {
          'color': '#999999'
        }
      },
      'markPoint': {
        'label': {
          'normal': {
            'textStyle': {
              'color': '#ffffff'
            }
          },
          'emphasis': {
            'textStyle': {
              'color': '#ffffff'
            }
          }
        }
      }
    })
  }
}
