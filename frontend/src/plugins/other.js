import _ from 'lodash'
import store from '../store'
import * as common from '../common'
import * as constants from '../const'
import VueLineClamp from 'vue-line-clamp'
import VueSweetAlert from 'vue-sweetalert2'
import moment from 'moment'
import sanitizeHtml from 'sanitize-html'

export default ({ Vue }) => {
  Vue.prototype._ = _
  Vue.prototype.c = common
  Vue.prototype.cc = constants
  Vue.prototype.window = window
  Vue.prototype.$c = _.assignIn({}, common, constants)
  Vue.prototype.moment = moment

  Vue.prototype.p = m => {
    const ps = store.state.User.permissions
    if (!ps || ps.length <= 0) {
      return false
    }
    return ps.indexOf(m) >= 0
  }

  Vue.prototype.e = m => {
    const ps = store.state.User.editableModules
    if (!ps || ps.length <= 0) {
      return false
    }
    return ps.indexOf(m) >= 0
  }

  sanitizeHtml.defaults.allowedTags = sanitizeHtml.defaults.allowedTags.concat([
    'img'
  ])
  sanitizeHtml.defaults.allowedAttributes['*'] = ['class', 'style']
  Vue.prototype.sanitize = sanitizeHtml

  Vue.use(VueLineClamp)
  Vue.use(VueSweetAlert)
}
