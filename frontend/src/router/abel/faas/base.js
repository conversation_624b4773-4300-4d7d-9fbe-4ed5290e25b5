export default [
  {
    path: 'faas/base/dashboard',
    component: () => import('pages/faas/base/dashboard')
  },
  {
    path: 'faas/base/partner',
    component: () => import('pages/faas/base/partner')
  },
  {
    path: 'faas/base/program',
    component: () => import('pages/faas/base/program')
  },
  {
    path: 'faas/base/agents',
    component: () => import('pages/faas/base/agents')
  },
  {
    path: 'faas/base/clients',
    component: () => import('pages/faas/base/clients')
  },
  {
    path: 'faas/base/clients/:id',
    component: () => import('pages/faas/base/clients/profile')
  },
  {
    path: 'faas/base/members',
    component: () => import('pages/faas/base/members')
  },
  {
    path: 'faas/base/members/:id',
    component: () => import('pages/faas/base/members/profile')
  },
  {
    path: 'faas/base/kyc-management',
    component: () => import('pages/faas/base/members/kyc.vue')
  },
  {
    path: 'faas/base/transfers',
    component: () => import('pages/faas/base/transfers')
  },
  {
    path: 'faas/base/settings',
    component: () => import('pages/faas/base/settings')
  },
  {
    path: 'faas/base/widgets',
    component: () => import('pages/faas/base/widgets')
  }
]
