export default [
  {
    path: 'mex/employer/reports/batchSummary',
    component: () => import('pages/mex/employerDashboard/reports/batch/index.vue')
  },
  {
    path: 'mex/employer/reports/batchSummary/detail/:id',
    component: () => import('pages/mex/employerDashboard/reports/batch/detail.vue')
  },
  {
    path: 'mex/employer/reports/daily',
    component: () => import('pages/mex/employerDashboard/reports/daily/daily.vue')
  },
  {
    path: 'mex/employer/reports/daily-master',
    component: () => import('pages/mex/employerDashboard/reports/daily/index.vue')
  },
  {
    path: 'mex/employer/reports/month',
    component: () => import('pages/mex/employerDashboard/reports/month/index.vue')
  },
  {
    path: 'mex/employer/reports/payroll_exceptions',
    component: () => import('pages/mex/employerDashboard/reports/payroll/index.vue')
  },
  {
    path: 'mex/employer/reports/running',
    component: () => import('pages/mex/employerDashboard/reports/running/index.vue')
  }
]
