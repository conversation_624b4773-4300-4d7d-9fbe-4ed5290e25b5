export default [
  {
    path: 'card-management/card',
    component: () => import('pages/card-management/card')
  },
  {
    path: 'card-management/inventory',
    component: () => import('pages/card-management/inventory'),
    meta: {
      search: {
        tip: 'Platform, Card Program, Batch ID, BIN, Client ID, Registration Key (Hash), User Email',
        event: 'admin_card_inventory_search'
      }
    }
  },
  {
    path: 'card-management/inventory/import',
    component: () => import('pages/card-management/inventory/import')
  },
  {
    path: 'card-management/load-unload',
    component: () => import('pages/card-management/load-unload/index')
  },
  {
    path: 'card-management/card-transfer',
    component: () => import('pages/card-management/card-transfer/index')
  },
  {
    path: 'card-management/transactions',
    component: () => import('pages/card-management/transactions/index')
  }
]
