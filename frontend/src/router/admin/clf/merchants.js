export default [
  {
    path: 'clf/merchants/:type',
    component: () => import('pages/clf/merchant/index'),
    meta: {
      search: {
        tip: 'Name, License Number, Address, Email, Phone, Status',
        event: 'admin_clf_merchants_search'
      }
    }
  },
  {
    path: 'clf/merchants/:type/documents/:id',
    component: () => import('pages/clf/merchant/documents')
  },
  {
    path: 'clf/merchants/:type/parameters/:id',
    component: () => import('pages/clf/merchant/parameters')
  }
]
