export default [
  {
    path: 'clf/sa',
    component: () => import('pages/clf/sa/index')
  },
  {
    path: 'clf/sa/dashboard',
    component: () => import('pages/clf/dashboard/sa/index')
  },
  {
    path: 'clf/sa/report/transactions',
    component: () => import('pages/clf/dashboard/common/transaction-report')
  },
  {
    path: 'clf/sa/report/fees',
    component: () => import('pages/clf/dashboard/common/fee-report')
  },
  {
    path: 'clf/sa/banks',
    component: () => import('pages/clf/sa/banks/index'),
    meta: {
      search: {
        tip: 'Name, Routing Number, Address, Admin, Status',
        event: 'admin_clf_sa_banks_search'
      }
    }
  },
  {
    path: 'clf/sa/banks/add',
    component: () => import('pages/clf/settings/index')
  },
  {
    path: 'clf/sa/banks/edit/:id',
    component: () => import('pages/clf/settings/index')
  },
  {
    path: 'clf/sa/merchants/:type',
    component: () => import('pages/clf/sa/merchant/index'),
    meta: {
      search: {
        tip: 'Name, License Number, Address, Email, Phone, Status',
        event: 'admin_clf_sa_merchants_search'
      }
    }
  },
  {
    path: 'clf/sa/merchants/:type/add',
    component: () => import('pages/clf/settings/index')
  },
  {
    path: 'clf/sa/merchants/:type/edit/:id',
    component: () => import('pages/clf/settings/index')
  },
  {
    path: 'clf/sa/patients',
    component: () => import('pages/clf/sa/patients/index'),
    meta: {
      search: {
        tip: 'Name, Patient ID, City, State',
        event: 'admin_clf_sa_patients_search'
      }
    }
  },
  {
    path: 'clf/sa/patients/add',
    component: () => import('pages/mobile/clf/settings/index')
  },
  {
    path: 'clf/sa/patients/edit/:id',
    component: () => import('pages/mobile/clf/settings/index')
  },
  {
    path: 'clf/sa/roles/:url',
    component: () => import('pages/common/frame')
  },
  {
    path: 'clf/sa/testing/pending-loads',
    component: () => import('pages/clf/sa/testing/pending-loads/index'),
    meta: {
      search: {
        tip: 'Tran ID, User Name / Email, Tran Type',
        event: 'admin_clf_sa_testing_pending_loads_search'
      }
    }
  },
  {
    path: 'clf/sa/testing/make-transaction',
    component: () => import('pages/clf/sa/testing/make-transaction/index'),
    meta: {
      search: {
        tip: 'Tran ID, Customer Name, Merchant Name, Merchant Employee Name, Tran Type, Tran Status',
        event: 'admin_clf_sa_testing_transactions_search'
      }
    }
  },
  {
    path: 'clf/sa/testing/reset-balance',
    component: () => import('pages/clf/sa/testing/reset-balance/index'),
    meta: {
      search: {
        tip: 'ID, User ID, Name, Email, Type, Patient/Merchant Number',
        event: 'admin_clf_sa_testing_reset_balance_search'
      }
    }
  },
  {
    path: 'clf/sa/settings',
    component: () => import('pages/clf/sa/settings/index')
  }
]
