export default [
  {
    path: 'report/card-account-activity',
    component: () => import('pages/report/account-activity')
  }, {
    path: 'report/affiliate',
    component: () => import('pages/report/affiliate')
  }, {
    path: 'report/fee',
    component: () => import('pages/report/fee')
  }, {
    path: 'report/negative-balance',
    component: () => import('pages/report/negative-balance')
  }, {
    path: 'report/privacy-card-creation',
    component: () => import('pages/report/privacy-card-creation')
  }, {
    path: 'report/revenue-old',
    component: () => import('pages/report/revenue/old')
  }, {
    path: 'report/revenue',
    component: () => import('pages/report/revenue/new')
  }, {
    path: 'report/loads_per_country',
    component: () => import('pages/report/loads-per-country/index.vue')
  }, {
    path: 'report/load-methods',
    component: () => import('pages/report/load-methods/index.vue')
  }, {
    path: 'report/total_account_balance',
    component: () => import('pages/report/total-account-balance/index.vue')
  }, {
    path: 'report/IDology_usage',
    component: () => import('pages/report/IDology-usage/index.vue')
  }, {
    path: 'report/twilio-report',
    component: () => import('pages/report/twilio-report/index.vue')
  }, {
    path: 'report/rain-report',
    component: () => import('pages/report/rain-report/index.vue')
  }

]
