export default [
  {
    path: 'system/platform-branding',
    component: () => import('pages/system/platform-branding')
  },
  {
    path: 'system/platform-branding/:id',
    component: () => import('pages/system/platform-branding/detail')
  },
  {
    path: 'system/platform-parameter/:id',
    component: () => import('pages/system/platform-parameter/detail')
  },
  {
    path: 'system/platform-email/:id',
    component: () => import('pages/system/platform-email/detail')
  },
  {
    path: 'system/api-documentation/:id',
    component: () => import('pages/system/api-documentation/index')
  },
  {
    path: 'system/kyc-check',
    component: () => import('pages/system/kyc-check/index')
  }
]
