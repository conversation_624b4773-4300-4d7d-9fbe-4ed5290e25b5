export default [
  {
    path: 'cow/dashboard',
    component: () => import('pages/cow/dashboard')
  },
  {
    path: 'cow/partner',
    component: () => import('pages/cow/partner')
  },
  {
    path: 'cow/program',
    component: () => import('pages/cow/program')
  },
  {
    path: 'cow/agents',
    component: () => import('pages/cow/agents')
  },
  {
    path: 'cow/members',
    component: () => import('pages/cow/members')
  },
  {
    path: 'cow/watch',
    component: () => import('pages/cow/members/watch')
  },
  {
    path: 'cow/kyc_pool',
    component: () => import('pages/cow/members/kyc')
  },
  {
    path: 'cow/members/:id',
    component: () => import('pages/cow/members/profile')
  },
  {
    path: 'cow/report/approved',
    component: () => import('pages/cow/report/approved')
  },
  {
    path: 'cow/report/declined',
    component: () => import('pages/cow/report/declined')
  },
  {
    path: 'cow/report/transaction_void',
    component: () => import('pages/cow/report/void_queue')
  },
  {
    path: 'cow/report/negative_account',
    component: () => import('pages/cow/report/account')
  },
  {
    path: 'cow/report/commissions',
    component: () => import('pages/cow/report/commissions')
  },
  {
    path: 'cow/report/member_loads',
    component: () => import('pages/cow/report/load')
  },
  {
    path: 'cow/report/program_fee',
    component: () => import('pages/cow/report/program_fee')
  },
  {
    path: 'cow/report/velocity_report',
    component: () => import('pages/cow/report/velocity')
  },
  {
    path: 'cow/settings',
    component: () => import('pages/cow/setting')
  },
  {
    path: 'cow/velocity_setting',
    component: () => import('pages/cow/setting/velocity')
  }
]
