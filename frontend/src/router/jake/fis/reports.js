export default [
  {
    path: 'fis/report/portfolio',
    component: () => import('pages/fis/report/portfolio')
  },
  {
    path: 'fis/report/ach',
    component: () => import('pages/fis/report/monetary')
  },
  {
    path: 'fis/report/balance',
    component: () => import('pages/fis/report/balance')
  },
  {
    path: 'fis/report/usage',
    component: () => import('pages/fis/report/usage')
  },
  {
    path: 'fis/report/transaction',
    component: () => import('pages/fis/report/transaction')
  },
  {
    path: 'fis/report/load',
    component: () => import('pages/fis/report/load')
  },
  {
    path: 'fis/report/fee',
    component: () => import('pages/fis/report/fee')
  },
  {
    path: 'fis/report/monetary',
    component: () => import('pages/fis/report/monetary')
  },
  {
    path: 'fis/report/auth',
    component: () => import('pages/fis/report/auth')
  },
  {
    path: 'fis/report/spend',
    component: () => import('pages/fis/report/spend')
  },
  {
    path: 'fis/report/dispute',
    component: () => import('pages/fis/report/dispute')
  },
  {
    path: 'fis/report/non-monetary',
    component: () => import('pages/fis/report/non-monetary')
  },
  {
    path: 'fis/report/negative',
    component: () => import('pages/fis/report/negative')
  }
]
