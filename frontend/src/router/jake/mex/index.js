export default [
  {
    path: 'mex/dashboard',
    component: () => import('pages/mex/dashboard')
  },
  {
    path: 'mex/promo',
    component: () => import('pages/mex/dashboard/promo.vue')
  },
  {
    path: 'mex/program',
    component: () => import('pages/mex/program')
  },
  {
    path: 'mex/agents',
    component: () => import('pages/mex/agents')
  },
  {
    path: 'mex/employers',
    component: () => import('pages/mex/employers')
  },
  {
    path: 'mex/employers/:id',
    component: () => import('pages/mex/employers/profile')
  },
  {
    path: 'mex/members',
    component: () => import('pages/mex/members')
  },
  {
    path: 'mex/members/:id',
    component: () => import('pages/mex/members/profile')
  },
  {
    path: 'mex/transfers',
    component: () => import('pages/mex/transfers')
  },
  {
    path: 'mex/transfers/:id',
    component: () => import('pages/mex/transfers')
  },
  {
    path: 'mex/fundings',
    component: () => import('pages/mex/fundings')
  },
  {
    path: 'mex/fundings/:id',
    component: () => import('pages/mex/fundings')
  },
  {
    path: 'mex/settings',
    component: () => import('pages/mex/settings')
  },
  {
    path: 'mex/settings/payers',
    component: () => import('pages/mex/settings/payers')
  },
  {
    path: 'mex/settings/locations',
    component: () => import('pages/mex/settings/locations')
  },
  {
    path: 'mex/settings/location-requests',
    component: () => import('pages/mex/settings/location-requests')
  },
  {
    path: 'mex/settings/branch',
    component: () => import('pages/mex/settings/intermex')
  },
  {
    path: 'mex/settings/webview',
    component: () => import('pages/mex/settings/webview')
  },
  {
    path: 'mex/admin/rapyd_history',
    component: () => import('pages/mex/admin/rapyd_history/index.vue')
  },
  {
    path: 'mex/admin/rapid_history',
    component: () => import('pages/mex/admin/rapid_history/index.vue')
  },
  {
    path: 'mex/admin/monthly/:scope',
    component: () => import('pages/mex/admin/monthly/index.vue')
  },
  {
    path: 'mex/revenue/:type',
    component: () => import('pages/mex/revenue/index.vue')
  },
  {
    path: 'mex/payroll_exceptions',
    component: () => import('pages/mex/payroll_exceptions/index.vue')
  },
  {
    path: 'mex/ach_report',
    component: () => import('pages/mex/ach_report/index.vue')
  },
  {
    path: 'mex/worker_report',
    component: () => import('pages/mex/worker_report/index.vue')
  },
  {
    path: 'mex/payroll_batch_report',
    component: () => import('pages/mex/payrollReports/batch/index.vue')
  },
  {
    path: 'mex/payroll_batch_report/detail/:id',
    component: () => import('pages/mex/payrollReports/batch/detail.vue')
  },
  {
    path: 'mex/payroll_detail_report',
    component: () => import('pages/mex/payrollReports/detail/index.vue')
  },
  {
    path: 'mex/load_transfer_report',
    component: () => import('pages/mex/load_and_transfer_report/index.vue')
  },
  {
    path: 'mex/settings/notify-email',
    component: () => import('pages/mex/settings/notify-emails')
  },
  {
    path: 'mex/admin/uniteller_history',
    component: () => import('pages/mex/admin/uniteller_history/index.vue')
  },
  {
    path: 'mex/summary/prefund-ach',
    component: () => import('pages/mex/summary/prefund-ach')
  },
  {
    path: 'mex/summary/prefund-load',
    component: () => import('pages/mex/summary/prefund-load')
  },
  {
    path: 'mex/summary/employer-ach',
    component: () => import('pages/mex/summary/employer-ach')
  },
  {
    path: 'mex/summary/employer-transfer',
    component: () => import('pages/mex/summary/employer-transfer')
  },
  {
    path: 'mex/summary/employer-report',
    component: () => import('pages/mex/summary/employer-report')
  },
  {
    path: 'mex/uniteller-fundings',
    component: () => import('pages/mex/uniTellerFundings')
  },
  {
    path: 'mex/admin/uniteller-transfers',
    component: () => import('pages/mex/admin/uniteller')
  },
  {
    path: 'mex/admin/intermex-transfers',
    component: () => import('pages/mex/admin/intermex')
  },
  {
    path: 'mex/admin/uniteller-transfer-report',
    component: () => import('pages/mex/admin/uniteller_report')
  },
  {
    path: 'mex/settings/splash-page',
    component: () => import('pages/mex/settings/splash-page')
  },
  {
    path: 'mex/message-center/batch',
    component: () => import('pages/mex/message/batch')
  },
  {
    path: 'mex/message-center/record',
    component: () => import('pages/mex/message/record')
  },
  {
    path: 'mex/kyc_exceptions',
    component: () => import('pages/mex/kyc_exceptions/index.vue')
  },
  // {
  //   path: 'mex/deposits/record',
  //   component: () => import('pages/mex/admin/deposit')
  // },
  {
    path: 'mex/settings/transaction-groups',
    component: () => import('pages/mex/settings/transaction-groups')
  },
  {
    path: 'mex/intermex-fundings',
    component: () => import('pages/mex/admin/intermexFundings')
  }
]
