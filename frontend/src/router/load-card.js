export default {
  path: '/load-card/:userCardId',
  component: () => import('layouts/load-card'),
  children: [
    {
      path: '',
      component: () => import('pages/load-card'),
      meta: {
        step: 1,
        title: 'Step 1: Load Card'
      }
    },
    {
      path: 'method',
      component: () => import('pages/load-card/method'),
      meta: {
        step: 2,
        title: 'Step 2: Choose Payment Method'
      }
    },
    {
      path: 'confirm',
      component: () => import('pages/load-card/confirm'),
      meta: {
        step: 3,
        title: 'Step 3: Payment Details'
      }
    },
    {
      path: 'pay',
      component: () => import('pages/load-card/pay'),
      meta: {
        step: 4,
        title: 'Step 4: Amount to Load'
      }
    },
    {
      path: 'status',
      component: () => import('pages/load-card/status'),
      meta: {
        step: 5,
        title: 'Step 5: Amount to Load'
      }
    }
  ]
}
