import store from '../../../store'

export default [
  {
    path: '/clf/login',
    component: () => import('layouts/mobile/empty'),
    children: [
      {
        path: '',
        component: () => import('pages/mobile/clf/login/index')
      },
      {
        path: 'forgot-password',
        component: () => import('pages/mobile/clf/login/forgot')
      },
      {
        path: 'config',
        component: () => import('pages/mobile/clf/login/config')
      }
    ]
  },
  {
    path: '/clf',
    component: () => import('layouts/mobile/clf/index'),
    children: [
      {
        path: '',
        redirect: () => {
          const type = store.state.User.account.type
          if (type === 'Patient') {
            return '/clf/wallet'
          }
          if (type === 'Dispensary') {
            return '/clf/dispensary/dashboard'
          }
          if (type === 'Vendor') {
            return '/clf/vendor/dashboard'
          }
          return '/clf/unknown'
        }
      },

      // ---------------------------- Patient ----------------------------
      {
        path: 'wallet',
        component: () => import('pages/mobile/clf/wallet/index')
      },
      {
        path: 'dispensary-locator',
        component: () => import('pages/mobile/clf/dispensary-locator/index')
      },
      {
        path: 'funds',
        component: () => import('pages/mobile/clf/funds/index')
      },
      {
        path: 'banks',
        component: () => import('pages/mobile/clf/banks/index')
      },
      {
        path: 'settings',
        component: () => import('pages/mobile/clf/settings/index'),
        meta: {
          name: 'My Settings'
        }
      },

      // --------------------------- Admin Common ---------------------------
      {
        path: 'admin/settings',
        component: () => import('pages/clf/settings/index')
      },

      // -------------------------- Merchant Common -------------------------
      {
        path: 'merchant/dashboard/fees',
        component: () => import('pages/mobile/clf/merchant/dashboard/fee-report'),
        meta: {
          name: 'Taxes & Fees'
        }
      },
      {
        path: 'merchant/employees',
        component: () => import('pages/mobile/clf/merchant/employees/index')
      },
      {
        path: 'merchant/due',
        component: () => import('pages/mobile/clf/merchant/due/index')
      },
      {
        path: 'merchant/invoice',
        component: () => import('pages/mobile/clf/merchant/invoice/index')
      },

      // ---------------------------- Dispensary ----------------------------
      {
        path: 'dispensary/dashboard',
        component: () => import('pages/clf/dashboard/dispensary/index')
      },
      {
        path: 'dispensary/dashboard/transactions',
        component: () => import('pages/mobile/clf/dispensary/dashboard/transaction-report'),
        meta: {
          name: 'Transactions Report'
        }
      },
      {
        path: 'dispensary/patients',
        component: () => import('pages/mobile/clf/dispensary/patients/index')
      },

      // ------------------------------ Vendor ------------------------------
      {
        path: 'vendor/dashboard',
        component: () => import('pages/clf/dashboard/vendor/index')
      }
    ]
  }
]
