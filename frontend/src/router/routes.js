import loadCard from './load-card'
import admin from './admin'
import mobileClf from './mobile/clf'
// import MobileDetect from 'mobile-detect'

export default [
  {
    path: '/',
    beforeEnter (to, from, next) {
      next('/a')
      // For old mobile app, we can use different user agent to navigate to /m
      // const md = new MobileDetect(window.navigator.userAgent)
      // if (md.mobile()) {
      //   next('/m')
      // } else {
      //   next('/a')
      // }
    }
  },
  {
    path: '/m',
    component: () => import('layouts/default'),
    children: [
      { path: '', component: () => import('pages/index') }
    ]
  },

  loadCard,
  admin,
  require('./jake').default,
  require('./abel').default,
  ...require('./abel/faas/global').default,
  ...require('./abel/spendr/global').default,
  ...mobileClf,

  { // Always leave this as last one
    path: '*',
    component: () => import('pages/404')
  }
]
