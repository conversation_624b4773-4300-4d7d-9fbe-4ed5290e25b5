const getters = {
  apiPrefix (state) {
    if (state.cpKey === 'cp_clf') {
      let prefix = '/clf'
      if (state.organization && state.organization.type) {
        prefix = `${prefix}/${state.organization.type.toLowerCase()}`
      } else if (state.currentRole === 'MasterAdmin') {
        prefix = `${prefix}/sa`
      }
      return prefix
    }
    return ''
  },
  organizationType (state) {
    if (state.cpKey === 'cp_clf') {
      if (state.organization && state.organization.type) {
        return state.organization.type
      }
    }
    return ''
  },
  organizationName (state) {
    if (state.cpKey === 'cp_clf') {
      if (state.organization && state.organization.name) {
        return state.organization.name
      }
    }
    return ''
  },
  clfAdmin (state) {
    if (state.cpKey === 'cp_clf') {
      if (state.organization && state.organization.type) {
        const type = state.organization.type
        return ['State', 'Bank'].includes(type)
      }
    }
    return false
  },
  masterAdmin (state) {
    return state && state.currentRole === 'MasterAdmin'
  },
  transferMexAdmin (state) {
    return state && state.currentRole === 'TransferMex Admin'
  }
}

export default {
  getters
}
