import Vue from 'vue'
import Vuex from 'vuex'
import createPersistedState from 'vuex-persistedstate'
import { deviceMode } from '../const'

let modules = {}
let persistConfig = {}
if (deviceMode === 'web') {
  modules = require('./modules').default
  persistConfig = {
    paths: [
      'Config.locale',
      'Config.sidebar'
    ]
  }
} else {
  modules = require(`./mobile/${deviceMode}`).default
  persistConfig = {}
}

for (const d of ['Config', 'User']) {
  if (!modules[d]) {
    modules[d] = require(`./modules/${d}`).default
  }
}

Vue.use(Vuex)

export default new Vuex.Store({
  modules,
  strict: process.env.NODE_ENV !== 'production',
  plugins: [
    createPersistedState(persistConfig)
  ]
})
