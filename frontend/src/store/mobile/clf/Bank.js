import _ from 'lodash'
import { request } from '../../../common'

const state = {
  data: [],
  all: [],
  canAddNewCard: false
}

const mutations = {
  update (state, o) {
    _.assignIn(state, o)
  }
}

const getters = {
}

const actions = {
  async init ({ commit }) {
    const resp = await request('/api/clf/banks')
    if (resp.success) {
      commit('update', resp.data)
    }
  },
  async initAll ({ commit }) {
    const resp = await request('/api/clf/banks/select')
    if (resp.success) {
      commit('update', {
        all: resp.data
      })
    } else {
      throw new Error(resp.message)
    }
  },
  clear ({ commit }) {
    commit('update', state)
  }
}

export default {
  namespaced: true,
  state: _.cloneDeep(state),
  mutations,
  getters,
  actions
}
