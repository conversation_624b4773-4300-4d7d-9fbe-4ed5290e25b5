import _ from 'lodash'
import { request } from '../../../common'

const state = {
  data: [],
  count: 0
}

const mutations = {
  update (state, o) {
    _.assignIn(state, o)
  }
}

const getters = {
}

const actions = {
  async init ({ commit, state }, params = {}) {
    params.page = params.page || 1
    const resp = await request(`/clf/merchant/employees/${params.page}`, 'get', params)
    if (resp.success) {
      if (params.page > 1) {
        resp.data.data = state.data.concat(resp.data.data)
      }
      commit('update', resp.data)
    }
  },
  clear ({ commit }) {
    commit('update', state)
  }
}

export default {
  namespaced: true,
  state: _.cloneDeep(state),
  mutations,
  getters,
  actions
}
