import _ from 'lodash'
import { request } from '../../../common'

const state = {
  pending: {
    data: [],
    count: 0
  },
  withdrawal: {
    data: [],
    count: 0
  }
}

const mutations = {
  update (state, o) {
    _.assignIn(state, o)
  }
}

const getters = {
}

const actions = {
  async initPending ({ commit, state }) {
    const resp = await request('/api/clf/funds/pending')
    if (resp.success) {
      commit('update', {
        pending: resp.data
      })
    }
  },
  async initWithdrawal ({ commit, state }, params = {
    page: 1,
    size: 10
  }) {
    const resp = await request('/api/clf/funds/withdrawal', 'get', params)
    if (resp.success) {
      if (params.page > 1) {
        resp.data.data = state.withdrawal.data.concat(resp.data.data)
      }
      commit('update', {
        withdrawal: resp.data
      })
    }
  },
  clear ({ commit }) {
    commit('update', state)
  }
}

export default {
  namespaced: true,
  state: _.cloneDeep(state),
  mutations,
  getters,
  actions
}
