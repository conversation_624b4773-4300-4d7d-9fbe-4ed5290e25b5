import _ from 'lodash'
import { request } from '../../../common'
import common from '../../common/User'

const state = {
  account: {},
  organization: null,
  country: null,
  state: null,
  flags: [],
  menus: [],
  preference: {},
  cpKey: null,
  token: null
}

const mutations = {
  update (state, o) {
    _.assignIn(state, o)
  }
}

const getters = {
  ...common.getters
}

const actions = {
  async init ({ commit }) {
    const resp = await request('/api/user/profile')
    if (resp.success) {
      commit('update', resp.data)

      window.mainApp.$root.$emit('loaded-user-profile', resp.data)
    }
  },
  clear ({ commit }) {
    commit('update', state)
  }
}

export default {
  namespaced: true,
  state: _.cloneDeep(state),
  mutations,
  getters,
  actions
}
