import _ from 'lodash'
import { request } from '../../common'

const state = {
  employees: []
}

const mutations = {
  update (state, o) {
    _.assignIn(state, o)
  }
}

const getters = {
}

const actions = {
  async initEmployees ({ commit }) {
    const resp = await request(`/clf/dispensary/transactions/prepare`)
    if (resp.success) {
      commit('update', {
        employees: resp.data.employees
      })
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  getters,
  actions
}
