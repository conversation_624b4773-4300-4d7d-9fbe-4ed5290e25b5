import _ from 'lodash'
import moment from 'moment-timezone'
import { request, toSelectOptions } from '../../common'
import { deviceMode } from '../../const'
import store from '../index'

const state = {
  locale: 'en',
  timezone: moment.tz.guess(),
  token: '',
  sidebar: true,
  globalAjaxBar: true,
  cardPrograms: [],
  selectedCp: null,
  usStates: []
}

const mutations = {
  update (state, o) {
    _.assignIn(state, o)
  }
}

const getters = {
  cardProgram: state => cardId => {
    if (!state.cardPrograms || state.cardPrograms.length <= 0) {
      return null
    }
    for (let cp of state.cardPrograms) {
      for (let card of cp.cards) {
        if (card && card.id === cardId) {
          return cp
        }
      }
    }
    return null
  },
  current (state) {
    if (state.selectedCp) {
      return _.find(state.cardPrograms, {
        id: state.selectedCp
      })
    }
    let name = 'US Unlocked'
    if (deviceMode === 'clf' || (store && store.state.User.cpKey === 'cp_clf')) {
      name = 'Universal Transaction Compliance'
    }
    return _.find(state.cardPrograms, {
      name
    })
  }
}

const actions = {
  async init ({ commit }) {
    let resp = await request('/card-programs', 'get', {
      platform: true
    })
    if (resp.success) {
      const all = (resp.data || []).map(item => {
        item.label = item.name
        item.value = item.id
        return item
      })
      const selectedCp = all.length ? all[0].id : null
      commit('update', {
        cardPrograms: all,
        selectedCp
      })
    }
    resp = await request('/provinces', 'get', {
      country: 'USA'
    })
    if (resp.success) {
      commit('update', {
        usStates: toSelectOptions(resp.data)
      })
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  getters,
  actions
}
