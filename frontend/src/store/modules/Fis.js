import _ from 'lodash'

const state = {
  graphs: {
    portfolio: null,
    balance: null,
    usage: null,
    transaction: null,
    load: null,
    fee: null
  },
  graphLoad: {
    Portfolio: false,
    Balance: false,
    Negative: false,
    Usage: false,
    Transaction: false,
    Load: false,
    Fee: false,
    Dispute: false,
    DisputeClaim: false,
    SettleNoAuth: false,
    Alert: false,
    CardSpend: false,
    Mcc: false,
    AchActivity: false,
    PeoplePay: false,
    CardMetrics: false
  }
}

const mutations = {
  update (state, o) {
    _.assignIn(state, o)
  },
  updateGraphLoad (state, o) {
    state.graphLoad[o.name] = o.value
  },
  updateGraph (state, o) {
    state.graphs[o.name] = o.value
  }
}

const getters = {
}

const actions = {
}

export default {
  namespaced: true,
  state,
  mutations,
  getters,
  actions
}
