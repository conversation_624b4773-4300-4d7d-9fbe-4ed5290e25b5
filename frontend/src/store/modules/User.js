import _ from 'lodash'
import { request } from '../../common'
import eventBus from '../../eventBus'
import common from '../common/User'

const state = {
  country: null,
  state: null,
  flags: [],
  menus: [],
  dashboard: null,
  permissions: [],
  editableModules: [],
  preference: {},
  cpKey: null,
  adminLayout: 'a',
  env: null,
  pinToken: null,
  fisAllPlatforms: {},
  fisPlatforms: [],
  fisDefaultPlatform: null,
  fisCurrentPlatform: null,
  fisPlatform: null,
  KYCRequired: false,
  clientsRequired: false,
  enableLoadTypeConfigure: false,
  isMasterLogin: false,
  uPermissions: [], // User level permissions
  payrollInfo: null,
  hasW2: false,
  isReadOnlyAdmin: false,
  subEmployers: [],
  currentEmployer: null
}

const mutations = {
  update (state, o) {
    _.assignIn(state, o)
  },
  setPreference (state, o) {
    _.assignIn(state.preference, o)
  },
  setPinToken (state, token) {
    state.pinToken = token
  }
}

const getters = {
  ...common.getters
}

const actions = {
  async init ({ commit, state }, o) {
    if (state.id && !(o && o.refresh)) {
      return
    }
    let url = '/admin/user/profile'
    if (window.location.href.indexOf('/faas/widget') >= 0) {
      url = '/faas/widget/user/profile'
    }
    const resp = await request(url)
    if (resp.success) {
      commit('update', resp.data)

      eventBus.$emit('loaded-user-profile', resp.data)

      // const host = window.location.hostname
      // if (_.endsWith(host, 'virtualcards.us')) {
      // window._user_id = resp.data.email
      // window._session_id = ''

      // const _sift = window._sift = window._sift || []
      // _sift.push(['_setAccount', host === 'staging.virtualcards.us' ? 'c5f9bbf098' : '76b83285c7'])
      // _sift.push(['_setUserId', window._user_id])
      // _sift.push(['_setSessionId', window._session_id])
      // _sift.push(['_trackPageview'])
      //
      // const e = window.document.createElement('script')
      // e.src = 'https://cdn.siftscience.com/s.js'
      // window.document.body.appendChild(e)
      // }
    }
  },
  setPinToken ({ commit, state }, token) {
    commit('setPinToken', token)
    setTimeout(() => {
      commit('setPinToken', null)
    }, 600000)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  getters,
  actions
}
