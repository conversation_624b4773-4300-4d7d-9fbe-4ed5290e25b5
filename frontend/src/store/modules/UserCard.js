import _ from 'lodash'
import { request } from '../../common'

const state = {
  card: null,
  coin: null
}

const mutations = {
  update (state, o) {
    _.assignIn(state, o)
  }
}

const getters = {
}

const actions = {
  async init ({ commit, state }, { userCardId, refresh }) {
    if (state.id && parseInt(state.id) === parseInt(userCardId) && !refresh) {
      return
    }
    const resp = await request('/api/card/detail', 'get', {
      userCardId
    })
    if (resp.success) {
      commit('update', resp.data)
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  getters,
  actions
}
