<?php

return [
    Symfony\Bundle\FrameworkBundle\FrameworkBundle::class => ['all' => true],
    Doctrine\Bundle\DoctrineBundle\DoctrineBundle::class => ['all' => true],
    Doctrine\Bundle\MigrationsBundle\DoctrineMigrationsBundle::class => ['all' => true],
    Symfony\Bundle\DebugBundle\DebugBundle::class => ['dev' => true],
    Symfony\Bundle\TwigBundle\TwigBundle::class => ['all' => true],
    Symfony\Bundle\WebProfilerBundle\WebProfilerBundle::class => ['dev' => true, 'test' => true],
    Twig\Extra\TwigExtraBundle\TwigExtraBundle::class => ['all' => true],
    Symfony\Bundle\SecurityBundle\SecurityBundle::class => ['all' => true],
    Symfony\Bundle\MonologBundle\MonologBundle::class => ['all' => true],
    Symfony\Bundle\MakerBundle\MakerBundle::class => ['dev' => true],
    AdminBundle\AdminBundle::class => ['all' => true],
    ApiBundle\ApiBundle::class => ['all' => true],
    AppBundle\AppBundle::class => ['all' => true],
    CashOnWebBundle\CashOnWebBundle::class => ['all' => true],
    ClfBundle\ClfBundle::class => ['all' => true],
    CoreBundle\CoreBundle::class => ['all' => true],
    DevBundle\DevBundle::class => ['all' => true],
    EsSoloBundle\EsSoloBundle::class => ['all' => true],
    FaasBundle\FaasBundle::class => ['all' => true],
    FisBundle\FisBundle::class => ['all' => true],
    LeafLinkBundle\LeafLinkBundle::class => ['all' => true],
    MobileBundle\MobileBundle::class => ['all' => true],
    PortalBundle\PortalBundle::class => ['all' => true],
    PTOBundle\PTOBundle::class => ['all' => true],
    SalexUserBundle\SalexUserBundle::class => ['all' => true],
    SkuxBundle\SkuxBundle::class => ['all' => true],
    SpendrBundle\SpendrBundle::class => ['all' => true],
    TransferMexBundle\TransferMexBundle::class => ['all' => true],
    UsUnlockedBundle\UsUnlockedBundle::class => ['all' => true],
    WilenBundle\WilenBundle::class => ['all' => true],
    Enqueue\Bundle\EnqueueBundle::class => ['all' => true],
    Vich\UploaderBundle\VichUploaderBundle::class => ['all' => true],
    Aws\Symfony\AwsBundle::class => ['all' => true],
    SymfonyCasts\Bundle\VerifyEmail\SymfonyCastsVerifyEmailBundle::class => ['all' => true],
    SymfonyCasts\Bundle\ResetPassword\SymfonyCastsResetPasswordBundle::class => ['all' => true],
    FOS\RestBundle\FOSRestBundle::class => ['all' => true],
    FOS\UserBundle\FOSUserBundle::class => ['all' => true],
    JMS\SerializerBundle\JMSSerializerBundle::class => ['all' => true],
    Tetranz\Select2EntityBundle\TetranzSelect2EntityBundle::class => ['all' => true],
    Nelmio\ApiDocBundle\NelmioApiDocBundle::class => ['all' => true],
    SecIT\ImapBundle\ImapBundle::class => ['all' => true],
    Stof\DoctrineExtensionsBundle\StofDoctrineExtensionsBundle::class => ['all' => true],
    Knp\Bundle\PaginatorBundle\KnpPaginatorBundle::class => ['all' => true],
    Nelmio\CorsBundle\NelmioCorsBundle::class => ['all' => true],
    Snc\RedisBundle\SncRedisBundle::class => ['all' => true],
];
