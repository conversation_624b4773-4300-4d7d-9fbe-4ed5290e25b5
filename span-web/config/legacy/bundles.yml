imports:
    - { resource: "@AdminBundle/Resources/config/services.yml" }
    - { resource: "@ApiBundle/Resources/config/services.yml" }
    - { resource: "@AppBundle/Resources/config/services.yml" }
    - { resource: "@CashOnWebBundle/Resources/config/services.yml" }
    - { resource: "@ClfBundle/Resources/config/services.yml" }
    - { resource: "@CoreBundle/Resources/config/services.yml" }
#    - { resource: "@DevBundle/Resources/config/services.yml" }
#    - { resource: "@EsSoloBundle/Resources/config/services.yml" }
#    - { resource: "@FaasBundle/Resources/config/services.yml" }
#    - { resource: "@FisBundle/Resources/config/services.yml" }
#    - { resource: "@LeafLinkBundle/Resources/config/services.yml" }
#    - { resource: "@MobileBundle/Resources/config/services.yml" }
    - { resource: "@PortalBundle/Resources/config/services.yml" }
#    - { resource: "@PTOBundle/Resources/config/services.yml" }
    - { resource: "@SalexUserBundle/Resources/config/services.yml" }
    - { resource: "@SkuxBundle/Resources/config/services.yml" }
    - { resource: "@SpendrBundle/Resources/config/services.yml" }
    - { resource: "@TransferMexBundle/Resources/config/services.yml" }
    - { resource: "@UsUnlockedBundle/Resources/config/services.yml" }
#    - { resource: "@WilenBundle/Resources/config/services.yml" }
