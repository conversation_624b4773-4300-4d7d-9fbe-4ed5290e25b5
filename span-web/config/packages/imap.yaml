imap:
    connections:
#        example_connection:
#            mailbox: "{localhost:993/imap/ssl/novalidate-cert}INBOX"
#            username: "<EMAIL>"
#            password: "save_in_parameters.yml"

#        another_connection:
#            mailbox: "{localhost:143}INBOX"
#            username: "username"
#            password: "save_in_parameters.yml"
#            attachments_dir: "%kernel.root_dir%/../var/imap/attachments"
#            server_encoding: "UTF-8"

        wilen_email:
            mailbox: "{imap.gmail.com:993/imap/ssl}INBOX"
            username: "%wilen_email_username%"
            password: "%wilen_email_password%"

        ewb_email:
            mailbox: "{imap.gmail.com:993/imap/ssl}INBOX"
            username: "%ewb_email_username%"
            password: "%ewb_email_password%"

        ffb_spendr_email:
            mailbox: "{outlook.office365.com:993/imap/ssl}INBOX"
            username: "%ffb_spendr_username%"
            password: "%ffb_spendr_password%"
