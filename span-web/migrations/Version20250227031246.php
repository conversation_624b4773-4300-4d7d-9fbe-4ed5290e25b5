<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250227031246 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE mex_import_employee_update_last_four_ssn_error_record (id INT AUTO_INCREMENT NOT NULL, import_last_four_ssn_record_id INT DEFAULT NULL, reason VARCHAR(255) NOT NULL, data VARCHAR(1025) NOT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, INDEX IDX_61482CAC6162BBFF (import_last_four_ssn_record_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE mex_import_employee_update_last_four_ssn_error_record ADD CONSTRAINT FK_61482CAC6162BBFF FOREIGN KEY (import_last_four_ssn_record_id) REFERENCES mex_import_employee_update_last_four_ssn_record (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE mex_import_employee_update_last_four_ssn_error_record DROP FOREIGN KEY FK_61482CAC6162BBFF');
        $this->addSql('DROP TABLE mex_import_employee_update_last_four_ssn_error_record');
    }
}
