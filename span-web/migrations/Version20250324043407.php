<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250324043407 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE user_devices DROP FOREIGN KEY FK_490A5090A15303B9');
        $this->addSql('ALTER TABLE user_devices ADD CONSTRAINT FK_490A5090A15303B9 FOREIGN KEY (user_token_id) REFERENCES user_token (id) ON DELETE SET NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE `user_devices` DROP FOREIGN KEY FK_490A5090A15303B9');
        $this->addSql('ALTER TABLE `user_devices` ADD CONSTRAINT FK_490A5090A15303B9 FOREIGN KEY (user_token_id) REFERENCES user_token (id)');
    }
}
