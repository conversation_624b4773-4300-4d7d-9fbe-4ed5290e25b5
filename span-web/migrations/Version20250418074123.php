<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250418074123 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE mex_employer_transfer_free_config (id INT AUTO_INCREMENT NOT NULL, employer_id INT DEFAULT NULL, transfer_type VARCHAR(255) NOT NULL COMMENT \'transfer method\', start_time DATETIME DEFAULT NULL, days INT NOT NULL, type VARCHAR(255) NOT NULL, created_at DATETIME NOT NULL, create_by VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, INDEX IDX_36C4CB0941CD9E7A (employer_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE mex_employer_transfer_free_config ADD CONSTRAINT FK_36C4CB0941CD9E7A FOREIGN KEY (employer_id) REFERENCES users (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE mex_employer_transfer_free_config DROP FOREIGN KEY FK_36C4CB0941CD9E7A');
        $this->addSql('DROP TABLE mex_employer_transfer_free_config');
    }
}
