<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250427060116 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE user_card_balance ADD user_card_load_id INT DEFAULT NULL, ADD user_card_transaction_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE user_card_balance ADD CONSTRAINT FK_7B22E98A7618909F FOREIGN KEY (user_card_load_id) REFERENCES user_card_load (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_card_balance ADD CONSTRAINT FK_7B22E98A491ACEEA FOREIGN KEY (user_card_transaction_id) REFERENCES user_card_transaction (id) ON DELETE CASCADE');
        $this->addSql('CREATE INDEX IDX_7B22E98A7618909F ON user_card_balance (user_card_load_id)');
        $this->addSql('CREATE INDEX IDX_7B22E98A491ACEEA ON user_card_balance (user_card_transaction_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE user_card_balance DROP FOREIGN KEY FK_7B22E98A7618909F');
        $this->addSql('ALTER TABLE user_card_balance DROP FOREIGN KEY FK_7B22E98A491ACEEA');
        $this->addSql('DROP INDEX IDX_7B22E98A7618909F ON user_card_balance');
        $this->addSql('DROP INDEX IDX_7B22E98A491ACEEA ON user_card_balance');
        $this->addSql('ALTER TABLE user_card_balance DROP user_card_load_id, DROP user_card_transaction_id');
    }
}
