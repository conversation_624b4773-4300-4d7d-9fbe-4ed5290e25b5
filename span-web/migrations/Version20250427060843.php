<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250427060843 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE user_card_balance ADD transfer_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE user_card_balance ADD CONSTRAINT FK_7B22E98A537048AF FOREIGN KEY (transfer_id) REFERENCES transfer (id) ON DELETE CASCADE');
        $this->addSql('CREATE INDEX IDX_7B22E98A537048AF ON user_card_balance (transfer_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE user_card_balance DROP FOREIGN KEY FK_7B22E98A537048AF');
        $this->addSql('DROP INDEX IDX_7B22E98A537048AF ON user_card_balance');
        $this->addSql('ALTER TABLE user_card_balance DROP transfer_id');
    }
}
