<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250507230550 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE mex_uniteller_payers ADD country_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE mex_uniteller_payers ADD CONSTRAINT FK_EC386413F92F3E70 FOREIGN KEY (country_id) REFERENCES country (id)');
        $this->addSql('CREATE INDEX IDX_EC386413F92F3E70 ON mex_uniteller_payers (country_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE mex_uniteller_payers DROP FOREIGN KEY FK_EC386413F92F3E70');
        $this->addSql('DROP INDEX IDX_EC386413F92F3E70 ON mex_uniteller_payers');
        $this->addSql('ALTER TABLE mex_uniteller_payers DROP country_id');
    }
}
