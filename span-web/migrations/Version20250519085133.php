<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250519085133 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE user_config ADD rain_user_id VARCHAR(63) DEFAULT NULL');
        $this->addSql('CREATE INDEX IDX_B1D834412DA908F1 ON user_config (rain_user_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX IDX_B1D834412DA908F1 ON user_config');
        $this->addSql('ALTER TABLE user_config DROP rain_user_id');
    }
}
