<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250530072502 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE INDEX IDX_DAB792D812C1842A6F949845 ON user_card_snap (user_card_id, time)');
        $this->addSql('CREATE INDEX IDX_DAB792D86F94984512C1842A ON user_card_snap (time, user_card_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX IDX_DAB792D812C1842A6F949845 ON user_card_snap');
        $this->addSql('DROP INDEX IDX_DAB792D86F94984512C1842A ON user_card_snap');
    }
}
