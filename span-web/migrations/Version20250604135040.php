<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250604135040 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE rain_webhook_records (id INT AUTO_INCREMENT NOT NULL, action VARCHAR(255) DEFAULT NULL COMMENT \'webhook action\', resource VARCHAR(255) DEFAULT NULL COMMENT \'webhook resource\', type VARCHAR(255) DEFAULT NULL COMMENT \'webhook transaction type\', partner_id VARCHAR(255) NOT NULL COMMENT \'partner id\', meta LONGTEXT DEFAULT NULL, created_at DATETIME NOT NULL, create_by <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL, updated_at DATETIME NOT NULL, update_by VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE rain_webhook_records');
    }
}
