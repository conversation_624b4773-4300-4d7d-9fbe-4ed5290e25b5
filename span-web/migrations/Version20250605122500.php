<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250605122500 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE rain_webhook_records ADD received_at DATETIME DEFAULT NULL');
        $this->addSql('CREATE INDEX IDX_B8DE791F9393F8FE ON rain_webhook_records (partner_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX IDX_B8DE791F9393F8FE ON rain_webhook_records');
        $this->addSql('ALTER TABLE rain_webhook_records DROP received_at');
    }
}
