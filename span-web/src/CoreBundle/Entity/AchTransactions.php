<?php

namespace CoreBundle\Entity;

use DateTime;
use Doctrine\ORM\Mapping as ORM;
use CoreBundle\Utils\Util;

/**
 * AchTransactions
 *
 * @ORM\Table(name="ach_transactions", indexes={
 *    @ORM\Index(columns={"tranId"}),
 *    @ORM\Index(columns={"batchId"}),
 *    @ORM\Index(columns={"tranStatus"}),
 *    @ORM\Index(columns={"user_card_id"}),
 *    @ORM\Index(columns={"tran_code"}),
 *    @ORM\Index(columns={"group_type"})
 * })
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\AchTransactionsRepository")
 *
 */
class AchTransactions
{
    const TRAN_CODE_CREDIT = 'credit';
    const TRAN_CODE_DEBIT = 'debit';

    // todo: These constants will need to be retained for version 1.5, but they are not required for Legacy because they are dynamic in this table.
    const SPENDR_GROUP_TYPE_CONSUMER_LOAD = 'consumer_load';
    const SPENDR_GROUP_TYPE_PARTNER_LOAD = 'partner_load';
    const SPENDR_GROUP_TYPE_CONSUMER_UNLOAD = 'consumer_unload';
    const SPENDR_GROUP_TYPE_MERCHANT_UNLOAD = 'merchant_unload';
    const SPENDR_GROUP_TYPE_PARTNER_UNLOAD = 'partner_unload';

    public static function getAllSpendrGroupTypes($batchForTest = false)
    {
        if ($batchForTest) {
            return [
                self::SPENDR_GROUP_TYPE_CONSUMER_LOAD, 
                self::SPENDR_GROUP_TYPE_CONSUMER_UNLOAD
            ];
        }
        return [
            self::SPENDR_GROUP_TYPE_CONSUMER_LOAD, 
            self::SPENDR_GROUP_TYPE_PARTNER_LOAD, 
            self::SPENDR_GROUP_TYPE_CONSUMER_UNLOAD,
            self::SPENDR_GROUP_TYPE_MERCHANT_UNLOAD,
            self::SPENDR_GROUP_TYPE_PARTNER_UNLOAD
        ];
    }

    public function persist()
    {
        Util::persist($this);
    }

    /**
     * @param $traceNumber
     *
     * @return AchTransactions|null
     */
    public static function findTransactionByTraceId($traceNumber)
    {
        $rs = Util::em()->getRepository(__CLASS__)
            ->createQueryBuilder('a')
            ->where('a.traceNumber = :traceNumber')
            ->setParameter('traceNumber', $traceNumber)
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if (!$rs) {
            return null;
        }
        return end($rs);
    }

    /**
     * @param $tranId
     *
     * @return AchTransactions|null
     */
    public static function findTransactionByTranId($tranId)
    {
        $rs = Util::em()->getRepository(__CLASS__)
            ->createQueryBuilder('a')
            ->where('a.tranId = :tranId')
            ->setParameter('tranId', $tranId)
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if (!$rs) {
            return null;
        }
        return end($rs);
    }

    /**
     * @param string $tranIdSuffix
     *
     * @return static[]
     */
    public static function findByTranIdSuffix(string $tranIdSuffix)
    {
        if (strlen($tranIdSuffix) <= 16) {
            throw new \RuntimeException('Transaction ID must be longer than 16 characters');
        }

        return Util::em()->getRepository(static::class)
            ->createQueryBuilder('a')
            ->where('a.tranId like :tranId')
            ->setParameter('tranId', '%' . $tranIdSuffix)
            ->getQuery()
            ->getResult();
    }

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="bankAccountId", type="string", length=255)
     */
    private $bankAccountId;

    /**
     * @var string
     *
     * @ORM\Column(name="tranId", type="string", length=255)
     */
    private $tranId;

    /**
     * @var DateTime
     *
     * @ORM\Column(name="createdAt", type="datetime")
     */
    private $createdAt;

    /**
     * @var string
     *
     * @ORM\Column(name="tranStatus", type="string", length=255)
     */
    private $tranStatus;

    /**
     * @var int
     *
     * @ORM\Column(name="batchId", type="integer", nullable=true)
     */
    private $batchId;

    /**
     * @var int
     *
     * @ORM\Column(name="line_position", type="integer", nullable=true)
     */
    private $linePosition;

    /**
     * @var string
     *
     * @ORM\Column(name="trace_number", type="string", length=255, nullable=true)
     */
    private $traceNumber;

    /**
     * @var DateTime
     *
     * @ORM\Column(name="settle_date", type="datetime", nullable=true)
     */
    private $settleDate;

    /**
     * @var string
     *
     * @ORM\Column(name="bank_id", type="string", length=255, nullable=true)
     */
    private $bankId;

    /**
     * @var int
     *
     * @ORM\Column(name="day_count", type="integer", nullable=true)
     */
    private $dayCount;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\UserCard")
     * @ORM\JoinColumn(name="user_card_Id", referencedColumnName="id", onDelete="cascade")
     */
    private $userCard;

    /**
     * @var integer $amount
     * @ORM\Column(name="amount", type="bigint", nullable=true)
     */
    private $amount;

    /**
     * @var string $tranCode
     * @ORM\Column(name="tran_code", type="string", nullable=true)
     */
    private $tranCode;
    
    /**
     * @var string
     *
     * @ORM\Column(name="error", type="text", nullable=true)
     */
    private $error;

    /**
     * @var string $groupType
     * @ORM\Column(name="group_type", type="string", nullable=true)
     */
    private $groupType;

    /**
     * @var DateTime
     *
     * @ORM\Column(name="return_date", type="datetime", nullable=true)
     */
    private $returnDate;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set bankAccountId
     *
     * @param string $bankAccountId
     *
     * @return AchTransactions
     */
    public function setBankAccountId(string $bankAccountId)
    {
        $this->bankAccountId = $bankAccountId;

        return $this;
    }

    /**
     * Get bankAccountId
     *
     * @return string
     */
    public function getBankAccountId()
    {
        return $this->bankAccountId;
    }

    /**
     * Set tranId
     *
     * @param string $tranId
     *
     * @return AchTransactions
     */
    public function setTranId($tranId)
    {
        $this->tranId = $tranId;

        return $this;
    }

    /**
     * Get tranId
     *
     * @return int
     */
    public function getTranId()
    {
        return $this->tranId;
    }

    /**
     * Set createdAt
     *
     * @param DateTime $createdAt
     *
     * @return AchTransactions
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set tranStatus
     *
     * @param string $tranStatus
     *
     * @return AchTransactions
     */
    public function setTranStatus($tranStatus)
    {
        $this->tranStatus = $tranStatus;

        return $this;
    }

    /**
     * Get tranStatus
     *
     * @return string
     */
    public function getTranStatus()
    {
        return $this->tranStatus;
    }

    /**
     * Set batchId
     *
     * @param integer $batchId`
     *
     * @return AchTransactions
     */
    public function setBatchId($batchId)
    {
        $this->batchId = $batchId;

        return $this;
    }

    /**
     * Get batchId
     *
     * @return int
     */
    public function getBatchId()
    {
        return $this->batchId;
    }

    /**
     * Set linePosition
     *
     * @param integer $linePosition
     *
     * @return AchTransactions
     */
    public function setLinePosition(int $linePosition)
    {
        $this->linePosition = $linePosition;

        return $this;
    }

    /**
     * Get linePosition
     *
     * @return int
     */
    public function getLinePosition()
    {
        return $this->linePosition;
    }

    /**
     * Set traceNumber
     *
     * @param string $traceNumber
     *
     * @return AchTransactions
     */
    public function setTraceNumber(string $traceNumber)
    {
        $this->traceNumber = $traceNumber;

        return $this;
    }

    /**
     * Get traceNumber
     *
     * @return string
     */
    public function getTraceNumber()
    {
        return $this->traceNumber;
    }

    /**
     * Set settleDate
     *
     * @param DateTime $settleDate
     *
     * @return AchTransactions
     */
    public function setSettleDate(DateTime $settleDate)
    {
        $this->settleDate = $settleDate;

        return $this;
    }

    /**
     * Get settleDate
     *
     * @return DateTime
     */
    public function getSettleDate()
    {
        return $this->settleDate;
    }

    /**
     * Set bankId
     *
     * @param string $bankId
     *
     * @return AchTransactions
     */
    public function setBankId(string $bankId)
    {
        $this->bankId = $bankId;

        return $this;
    }

    /**
     * Get bankId
     *
     * @return string
     */
    public function getBankId()
    {
        return $this->bankId;
    }

    /**
     * Set dayCount
     *
     * @param integer $dayCount
     *
     * @return AchTransactions
     */
    public function setDayCount(int $dayCount)
    {
        $this->dayCount = $dayCount;

        return $this;
    }

    /**
     * Get dayCount
     *
     * @return int
     */
    public function getDayCount()
    {
        return $this->dayCount;
    }

    /**
     * Set userCard
     *
     * @param UserCard $userCard
     *
     * @return AchTransactions
     */
    public function setUserCard(UserCard $userCard = null)
    {
        $this->userCard = $userCard;
        return $this;
    }

    /**
     * Get userCard
     *
     * @return UserCard
     */
    public function getUserCard()
    {
        return $this->userCard;
    }

    /**
     * Set amount
     *
     * @param integer $amount
     *
     * @return AchTransactions
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;

        return $this;
    }

    /**
     * Get amount
     *
     * @return integer
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * Set tranCode
     *
     * @param string $tranCode
     *
     * @return AchTransactions
     */
    public function setTranCode($tranCode)
    {
        $this->tranCode = $tranCode;

        return $this;
    }

    /**
     * Get tranCode
     *
     * @return string
     */
    public function getTranCode()
    {
        return $this->tranCode;
    }
    
    /**
     * Set error
     *
     * @param string $error
     *
     * @return AchTransactions
     */
    public function setError($error)
    {
        $this->error = $error;

        return $this;
    }

    /**
     * Get error
     *
     * @return string
     */
    public function getError()
    {
        return $this->error;
    }

    /**
     * Set groupType
     *
     * @param string $groupType
     *
     * @return AchTransactions
     */
    public function setGroupType($groupType)
    {
        $this->groupType = $groupType;

        return $this;
    }

    /**
     * Get groupType
     *
     * @return string
     */
    public function getGroupType()
    {
        return $this->groupType;
    }

    /**
     * Set returnDate
     *
     * @param DateTime $returnDate
     *
     * @return AchTransactions
     */
    public function setReturnDate(DateTime $returnDate)
    {
        $this->returnDate = $returnDate;

        return $this;
    }

    /**
     * Get returnDate
     *
     * @return DateTime
     */
    public function getReturnDate()
    {
        return $this->returnDate;
    }
}

