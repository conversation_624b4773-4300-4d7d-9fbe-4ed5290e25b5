<?php


namespace LeafLinkBundle\Controller;


use CoreB<PERSON>le\Controller\Cron\NakedProtectedController;
use CoreBundle\Entity\AchBatch;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\NachaEntity;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\JsonResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use LeafLinkBundle\Entity\AchB2BTransaction;
use LeafLinkBundle\Entity\DailyCheck;
use LeafLinkBundle\Services\SlackService;
use LeafLinkBundle\Services\WebhookService;
use LeafLinkBundle\Services\YodleeService;
use Nacha\Batch;
use Nacha\Field\TransactionCode;
use Nacha\File;
use Nacha\Record\Addenda;
use Nacha\Record\CcdEntry;
use Nacha\Record\DebitEntry;
use phpseclib\Net\SFTP;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Entity\UserConfig;
use Symfony\Component\Routing\Annotation\Route;
use Stringy\Stringy;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;


class B2BAchServiceController extends NakedProtectedController
{
    const ACH_B2B_BATCH_SUCCESS = 'Success: B2B Transaction added to batch';
    const ACH_B2B_BATCH_FAILURE_BALANCE = "B2B Balance Check Failure: The originating account's checked balance was less than the transaction amount.";
    const ACH_B2B_BATCH_PREFED_SUCCESS = 'B2B Transaction has passed pre-fed validation.';
    const ACH_B2B_BATCH_AMOUNT_LIMIT = 'The transaction amount in the transaction was more than 10 digits.';
    const TERN_CODE_T01 = 'B2B Transaction amount is 0. Transaction has been canceled.';

    /** @var SFTP */
    public $sftp;

    protected $file;
    protected $batch;
    protected $creditEntry;
    protected $debitEntry;

    protected $allowFromApi = true;

    // Login for sFTP server. Called in submitACHBatch().
    public function login()
    {
//        $this->sftp = new SFTP(Util::getParameter('sl_ftp_host_2'), Util::getParameter('sl_ftp_port_2'), 90);
        $this->sftp = new SFTP(Util::getParameter('sl_ftp_host_2'), Util::getParameter('sl_ftp_port_2'), 90);
        if ($this->sftp === FALSE) {
            SlackService::alert('Leaflink FTP connection error!');
            return;
        }
        if (!$this->sftp->login(
            Util::getParameter('sl_ftp_username_2'),
            Util::getParameter('sl_ftp_password_2')
        )) {
            SlackService::alert('Failed to login the LeafLink ftp server (004)...', [
                'error' => $this->sftp->getLastSFTPError(),
            ]);
            return;
        }
    }

    public function getQuery()
    {
        //Creates an array of all ACH transactions with the status LL_SENT. This should only be transactions created via leaflink payment controller.
        return Util::em()->getRepository(AchB2BTransaction::class)
            ->createQueryBuilder('b2b')
            ->where('b2b.tranStatus = :received')
            ->setParameter('received', UserCardTransaction::STATUS_LL_RECEIVED)
            ->orderBy('b2b.createdAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @Route("/t/cron/leaflink/ach/b2b-batch")
     * @param Request $request
     * @param $retry
     * @return Response
     * @throws \LogicException
     */
    public function submitB2BACHBatch(Request $request, $retry = false)
    {
        Util::longRequest();

        if (!Util::isTodayBusinessDay())
        {
            SlackService::info('Today is not a business day, Leaflink B2B ACH Batch will be skipped.');
            return new SuccessResponse();
        }

        SlackService::info('Today is a business day, Leaflink B2B ACH Batch will not be skipped.');

        SlackService::clock('Leaflink B2B ACH batch is initializing.');

        // Check if query is being passed as an argument. If yes, use that query. If no, build a query off of the existing que.

        if ($retry === false)
        {
            $achQuery = $this->getQuery();
        } else {
            $achQuery = $retry;
        }

        if (count($achQuery) > 0) //Do not run if que is empty.
        {
            $check = DailyCheck::ensureDailyCheck();
            $check->setDate(new \DateTime())
                ->setSalalB2bStart(true)
                ->persist();

            $origin = Util::em()->getRepository(NachaEntity::class)
                ->findOneBy(['name' => 'SALAL CREDIT UNION B2B']);
            $destination = Util::em()->getRepository(NachaEntity::class)
                ->findOneBy(['name' => 'FRB SF']);
            $traceNumberEntity = Util::em()->getRepository(NachaEntity::class)
                ->findOneBy(['name' => 'SALAL CREDIT UNION']);

            // Create ACH Batch and set variables.
            $achBatch = new AchBatch();
            $achBatch->persist();
            $batchId = $achBatch->getId();
            SlackService::info($batchId, [
                'B2B ACH Count' => count($achQuery),
            ]);

            //trace number variables. Use entity from factoring to ensure unique numbers.
            $traceNumberBase = $traceNumberEntity->getTraceNumberBase();
            $traceNumberCount = $traceNumberEntity->getTraceNumberCount();

            //destination limit variables

            $dailyCount = $origin->getDailyDbCount();
            $weeklyCount = $origin->getWeeklyDbCount();
            $monthlyCount = $origin->getMonthlyDbCount();

            //params array for mail
            $params = [];
            $params['tranCount'] = 0;

            //linecount
            $lineCount = 3;
            $transactionSum = 0;

            // Uses origin, destination, and ach transaction query variables to generate a nacha file string.
            $this->file = new File();
            $this->file->getHeader()->setPriorityCode(1)
                ->setImmediateDestination($destination->getRoutingNumber())
                ->setImmediateOrigin($origin->getRoutingNumber())
                ->setFileCreationDate(date('ymd'))
                ->setFileCreationTime(date('Hi'))
                ->setFormatCode('1')
                ->setFileIdModifier('Y')
                ->setImmediateDestinationName($destination->getName())
                ->setImmediateOriginName($origin->getName())
                ->setReferenceCode('Tern B2B');

            $uctRepo = Util::em()->getRepository(UserCardTransaction::class);
            // Create new batch for each transaction.
            /** @var AchB2BTransaction $ach */
            foreach ($achQuery as $ach)
            {
                // Get TO variables
                $uctTo = $uctRepo->findOneBy(['tranId' => $ach->getTranId(), 'tranCode' => UserCardTransaction::LL_TYPE_B2B_TO]);
                $ucTo = $uctTo->getUserCard();
                $toUser = User::findByBankAccountId($ach->getToBankAccountId());
                $toConfig = $toUser->ensureConfig();

                // Get FROM variables
                $uctFrom = $uctRepo->findOneBy(['tranId' => $ach->getTranId(), 'tranCode' => UserCardTransaction::LL_TYPE_B2B_FROM]);
                $ucFrom = $uctFrom->getUserCard();
                $fromUser = User::findByBankAccountId($ach->getFromBankAccountId());
                $fromConfig = $fromUser->ensureConfig();
                $txnDescriptor = Util::meta($uctTo, 'txnDescriptor');
                // Get TXN variables
                $txnAmount = $uctTo->getTxnAmount(); //Can be TO or FROM

                $newDailyCount = $dailyCount + $txnAmount;
                $newWeeklyCount = $weeklyCount + $txnAmount;
                $newMonthlyCount = $monthlyCount + $txnAmount;

                $checkResult = AchServiceController::checkAchLimits($newDailyCount, $newWeeklyCount, $newMonthlyCount, $origin, AchServiceController::LIMIT_CHECK_DEBIT);

                //Check if this transaction exceeds limits.
                if ($checkResult === AchServiceController::LIMIT_CHECK_SUCCESS)
                {
                    //Yodlee Balance Check:
                    //Check and see if account is verified via Yodlee
                    if ($ucFrom->getAccountId() !== NULL && $ucFrom->getProviderAccountId() !== NULL) {
                        //Get account balance and check if txn will clear
                        $token = YodleeService::getYodleeToken($fromConfig->getBankAccountId());
                        $data = YodleeService::getYodleeBankingDetails($token, $ucFrom->getProviderAccountId(), $ucFrom->getAccountId());
                        $balance = $data["account"][0]["balance"]["amount"];
                        //Check if balance data was retrieved.
                        if ($balance)
                        {
                            SlackService::info('Retrieved balance:', [
                                'balance' => $balance,
                                'txnAmount' => ($txnAmount / 100),
                            ]);
                            //Cancel transaction and reset counters if balance is lower than txnAmount.
                            if (($balance * 100) < $txnAmount) {

                                $newDailyCount = $dailyCount;
                                $newWeeklyCount = $weeklyCount;
                                $newMonthlyCount = $monthlyCount;

                                SlackService::alert('B2B Transaction ID: ' . $ach->getTranId() . ' did not pass the balance check and will be canceled.');
                                $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
                                $uctTo->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
                                $uctFrom->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
                                WebhookService::achB2BErrorWebhook($ach, 'T03', AchServiceController::TERN_CODE_T03);
                                continue;
                            }
                        } else {
                            //Unable to get data from Yodlee. Cancel transaction.

                            $newDailyCount = $dailyCount;
                            $newWeeklyCount = $weeklyCount;
                            $newMonthlyCount = $monthlyCount;

                            SlackService::alert('B2B Transaction ID: ' . $ach->getTranId() . ' : Yodlee connection failed. Transaction will be canceled.');
                            $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                ->persist();
                            $uctTo->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                ->persist();
                            $uctFrom->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                ->persist();
                            WebhookService::achB2BErrorWebhook($ach, 'T05', AchServiceController::TERN_CODE_T05);
                            continue;
                        }
                    }

                    //TODO: Replace return webhooks with errors. Add number check to live

                    // Check that number isn't too big
                    $num = $uctFrom->getTxnAmount();
                    $numlength = strlen((string)abs($num));
                    if ($numlength >= 11)
                    {
                        $newDailyCount = $dailyCount;
                        $newWeeklyCount = $weeklyCount;
                        $newMonthlyCount = $monthlyCount;

                        SlackService::alert('B2B Transaction ID: ' . $ach->getTranId() . ' is over the maximum processable transaction amount.');
                        $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                            ->persist();
                        $uctTo->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                            ->persist();
                        $uctFrom->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                            ->persist();
                        WebhookService::achB2BErrorWebhook($ach, 'T02', self::ACH_B2B_BATCH_AMOUNT_LIMIT);
                        continue;
                    }

                    //Check that number isn't 0.
                    if ($num === 0)
                    {
                        $newDailyCount = $dailyCount;
                        $newWeeklyCount = $weeklyCount;
                        $newMonthlyCount = $monthlyCount;

                        SlackService::alert('B2B Transaction ID: ' . $ach->getTranId() . ' is 0. Transaction is being canceled.');
                        $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                            ->persist();
                        $uctTo->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                            ->persist();
                        $uctFrom->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                            ->persist();
                        WebhookService::achB2BErrorWebhook($ach, 'T01', self::TERN_CODE_T01);
                        continue;
                    }

                    $this->batch = new Batch();
                    $this->batch->getHeader()->setBatchNumber(1)
                        ->setServiceClassCode('200')
                        ->setCompanyName('LL')
                        ->setCompanyId('B843524524')
//                        ->setCompanyId('B851294764') // Old value: B2B File Update: can you update LL's ACH Company ID in the B2B files from B851294764 to B843524524
                        ->setStandardEntryClassCode('CCD')
                        ->setCompanyEntryDescription('ACH')
                        ->setCompanyDiscretionaryData(substr($ach->getTranId(), -19)) // Use this to identify each transaction
                        ->setCompanyDescriptiveDate(date('ymd'))
                        ->setEffectiveEntryDate(date("ymd", strtotime('tomorrow')))
                        ->setOriginatorStatusCode('1')
                        ->setOriginatingDFiId(substr($origin->getRoutingNumber(), 0, 9));

                    //decrypt FROM data
                    $decryptedDbaNoFrom = SSLEncryptionService::decrypt($ucFrom->getDbaNo());
                    $decryptedAccountNumFrom = SSLEncryptionService::decrypt($ucFrom->getAccountNumber());

                    $traceNumberCount = str_pad(++$traceNumberCount, 7, "0", STR_PAD_LEFT); // Increases trace number count by 1 when line is added.

                    // Sanitize from company name
                    $sanitizedObject = Stringy::create($fromConfig->getCompanyName())->toAscii();
                    $sanitizedFromCompanyName = $sanitizedObject->__toString();

                    $this->debitEntry = (new DebitEntry)
                        ->setTransactionCode(TransactionCode::CHECKING_DEBIT)
                        ->setReceivingDfiId(substr($decryptedDbaNoFrom, 0, 8))
                        ->setCheckDigit(substr($decryptedDbaNoFrom, -1))
                        ->setDFiAccountNumber($decryptedAccountNumFrom)
                        ->setAmount(number_format(($uctFrom->getTxnAmount() / 100), 2, '.', ''))
                        ->setIndividualId(substr((string)$ach->getFromBankAccountId(), -15))
                        ->setIdividualName($sanitizedFromCompanyName)
                        ->setDiscretionaryData(substr($ach->getTranId(), -19))
                        ->setAddendaRecordIndicator(0)
                        ->setTraceNumber($traceNumberBase, $traceNumberCount);

                    $this->batch->addEntry($this->debitEntry);

                    $ach->setLinePositionFrom($lineCount)
                        ->setTraceNumberFrom($this->debitEntry->getTraceNumber());
                    ++$lineCount;

                    //Decrypt TO Data
                    $decryptedDbaNoTo = SSLEncryptionService::decrypt($ucTo->getDbaNo());
                    $decryptedAccountNumTo = SSLEncryptionService::decrypt($ucTo->getAccountNumber());

                    // Sanitize to company name
                    $sanitizedObject = Stringy::create($toConfig->getCompanyName())->toAscii();
                    $sanitizedToCompanyName = $sanitizedObject->__toString();

                    $traceNumberCount = str_pad(++$traceNumberCount,7,"0",STR_PAD_LEFT);
                    $this->creditEntry = (new CcdEntry)
                        ->setTransactionCode(TransactionCode::CHECKING_DEPOSIT)
                        ->setReceivingDfiId(substr($decryptedDbaNoTo, 0, 8))
                        ->setCheckDigit(substr($decryptedDbaNoTo, -1))
                        ->setReceivingDFiAccountNumber($decryptedAccountNumTo)
                        ->setAmount(number_format(($uctTo->getTxnAmount()/100), 2, '.', ''))
                        ->setReceivingCompanyId(substr((string)$ach->getToBankAccountId(), -15))
                        ->setReceivingCompanyName($sanitizedToCompanyName)
                        ->setDiscretionaryData(substr($ach->getTranId(), -19))
                        ->setAddendaRecordIndicator(0)
                        ->setTraceNumber($traceNumberBase, $traceNumberCount);

                    if ($txnDescriptor) {
                        $this->creditEntry->addAddenda((new Addenda())->setPaymentRelatedInformation(
                            $txnDescriptor
                        ));
                    }

                    $this->batch->addEntry($this->creditEntry);
                    $this->file->addBatch($this->batch);

                    // Update Transaction Records
                    $ach->setTranStatus(UserCardTransaction::STATUS_LL_PROCESSING)
                        ->setBatchId($batchId)
                        ->setLinePositionTo($lineCount)
                        ->setTraceNumberTo($this->creditEntry->getTraceNumber())
                        ->persist();

                    $uctTo->setAccountStatus(UserCardTransaction::STATUS_LL_PROCESSING)
                        ->setAchBatchId($batchId)
                        ->persist();
                    $uctFrom->setAccountStatus(UserCardTransaction::STATUS_LL_PROCESSING)
                        ->setAchBatchId($batchId)
                        ->persist();

                    SlackService::check('B2B Transaction ID: ' . $ach->getTranId() . ' has been added to the batch.');

                    WebhookService::achB2BReturnWebhook($ach, self::ACH_B2B_BATCH_SUCCESS);

                    //Updates counts for next transaction
                    $dailyCount = $newDailyCount;
                    $weeklyCount = $newWeeklyCount;
                    $monthlyCount = $newMonthlyCount;
                    $lineCount += 3;
                    $transactionSum += $txnAmount;
                    $params['tranCount'] += 2;

                } else {
                    // Limit check failed. Cancel transaction, send slack and webhook.

                    $newDailyCount = $dailyCount;
                    $newWeeklyCount = $weeklyCount;
                    $newMonthlyCount = $monthlyCount;

                    $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                        ->persist();
                    $uctTo->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                        ->persist();
                    $uctFrom->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                        ->persist();

                    //TODO: Possibly set up warning email.

                    $failureMessage = AchServiceController::getLimitErrorCode($checkResult);

                    SlackService::alert('Transaction ' . $ach->getTranId() . ' has failed a limit check.', [
                        'Transaction' => $ach->getTranId(),
                        'Error Code' => $checkResult,
                        'Error Description' => $failureMessage,
                    ]);

                    WebhookService::achB2BErrorWebhook($ach, $checkResult, $failureMessage);
                }
            }

            // Updates database with new trace number count and limit counts.
            $origin->setDailyDbCount($dailyCount)
                ->setWeeklyDbCount($weeklyCount)
                ->setMonthlyDbCount($monthlyCount)
                ->persist();

            $traceNumberEntity->setTraceNumberCount($traceNumberCount)
                ->persist();

            //Generates data string and file name for Nacha file.
            $batchString = $this->file->__toString();
            $fileName = 'b2b' . date("Y-m-d-H-i") . ".txt";

            //Store file name and encrypted string.
            $encryptedBatchString = SSLEncryptionService::encrypt($batchString);

            $achBatch->setBatchFileName($fileName)
                ->setBatchFileString($encryptedBatchString)
                ->setTransactionCount($transactionSum)
                ->setBatchStatus(UserCardTransaction::STATUS_LL_PROCESSING)
                ->setSentTime(new \DateTime())
                ->persist();

            $path = dirname(__dir__) . "/" . $fileName;

            // For generating local files rather than pushing to sFTP.
//            file_put_contents($path, $batchString);

            $this->login();
            $this->sftp->put($fileName, $batchString);

            // Store previous file submission for later retrieval.
            $origin->setPreviousFileName($fileName)
                ->persist();
            $this->sftp->put('./archiveb2b/' . $fileName, $batchString);

            SlackService::tada('B2B Nacha file ' . $fileName . ' has been created and sent successfully!');

            if (Util::isLive())
            {
                Email::sendWithTemplate(['<EMAIL>', '<EMAIL>'], Email::B2B_BATCH_REPORT, $params);
            }
        }
        return new Response();
    }

    /**
     * @Route("/t/cron/leaflink/ach/b2b-return")
     * @param Request $request
     * @return Response
     * @throws \LogicException
     * @throws \JsonException
     */
    public function return(Request $request)
    {
        Util::longRequest();

        // Get list of all files in sFTP
        $this->login();
        $dir = "./";
        $files = $this->sftp->nlist($dir);

        // Process all files.
        foreach ($files as $file)
        {
            //Only process files that start with Salal (which are the salal return files)
            if (strpos($file, "Salal_b2b") === 0)
            {
                Log::debug('Processing Salal b2b return file ' . $file);

                $path = $dir . $file;
                $dlFIle = $this->sftp->get($path);
                $returnArr = json_decode($dlFIle, true, 512, JSON_THROW_ON_ERROR);

                $fileName = $returnArr['file_name'];

                $achBatch = Util::em()->getRepository(AchBatch::class)
                    ->findOneBy(['batchFileName' => $fileName]);
                $batchId = $achBatch->getId();
                //testing only
//                $batchId = 1;

                //Check if error exists
                if ($returnArr['error_message'] !== "NO ERRORS")
                {
                    SlackService::alert('Ach Batch ' . $batchId . ' has returned with an error', $returnArr);

                    //TODO: Test and set up emails.

                    // If error message is expected, find line where error occurred.

                    if (strpos($returnArr['error_message'], 'FILE REJECTED LINE') !== false)
                    {
                        $start = strpos($returnArr['error_message'], 'FILE REJECTED LINE') + 20;
                        $end = strpos($returnArr['error_message'], '-') - 1;
                        $numString = substr($returnArr['error_message'], $start, $end);
                        $errorLine = intval($numString);

                        // Get array of transactions from the failed file

                        $achRepo = Util::em()->getRepository(AchB2BTransaction::class);
                        $achQuery = $achRepo->createQueryBuilder('ach')
                            ->where('ach.batchId = :batchId')
                            ->andWhere('ach.tranStatus = :processing')
                            ->setParameter('batchId', $batchId)
                            ->setParameter('processing', UserCardTransaction::STATUS_LL_PROCESSING)
                            ->getQuery()
                            ->getResult();

                        foreach ($achQuery as $ach)
                        {
                            // Handle Entire Batch
                            $ach->setTranStatus(UserCardTransaction::STATUS_LL_RETURNED)
                                ->persist();
                            //Report on transaction
                            if ($ach->getLinePosition() === $errorLine)
                            {
//                                WebhookService::achB2BReturnWebhook($ach, $returnArr['error_message']);
                                SlackService::alert('B2B Transaction ' . $ach->getTranId() . ' has been identified as containing a pre-fed error. Setting status to returned.', [
                                    'error_message' => $returnArr['error_message'],
                                ]);
                            } else {
//                                WebhookService::achB2BReturnWebhook($ach, $returnArr['error_message']);
                                SlackService::alert('B2B Transaction ' . $ach->getTranId() . ' is part of a batch that contains a pre-fed error. Setting status to returned.', [
                                    'error_message' => $returnArr['error_message'],
                                ]);
                            }


                            //Select Specific Transaction (Depreciated)
//                            if ($ach->getLinePosition() === $errorLine)
//                            {
//                                $ach->setTranStatus(UserCardTransaction::STATUS_LL_RETURNED)
//                                    ->persist();
//                                //Report on transaction
//                                WebhookService::achReturnWebhook($ach, $returnArr['error_message']);
//                                SlackService::alert($ach->getTranId() . ' has been identified as containing an error. Setting status to returned.', [
//                                    'error_message' => $returnArr['error_message']
//                                ]);
//
//                            }
                        }

//                        SlackService::alert('ACH batch ' . $batchId . 'will be retried without errored transactions.');
//
//                        $newQuery = Util::em()->getRepository(AchTransactions::class)
//                            ->createQueryBuilder('ach')
//                            ->where('ach.batchId = :batchId')
//                            ->andWhere('ach.tranStatus = :processing')
//                            ->setParameter('batchId', $batchId)
//                            ->setParameter('processing', UserCardTransaction::STATUS_LL_PROCESSING)
//                            ->getQuery()
//                            ->getResult();
//
//                        return $this->submitACHBatch($request, $newQuery);

                    }

                    $achBatch->setBatchReturnString($dlFIle)
                        ->setBatchStatus(UserCardTransaction::STATUS_LL_CANCELED)
                        ->persist();

                    $this->sftp->put($dir."archiveb2b".'/'.$file, $this->sftp->get($dir.'/'.$file));
                    $this->sftp->delete($path);
                    $this->sftp->delete($dir . $fileName);

                    return new FailedResponse('Ach Batch ' . $batchId . ' has failed for an unknown reason.', $returnArr);
                }

                // Get all ach transactions from file.
                $achQuery = Util::em()->getRepository(AchB2BTransaction::class)
                    ->createQueryBuilder('ach')
                    ->where('ach.batchId = :batchId')
                    ->andWhere('ach.tranStatus = :processing')
                    ->setParameter('batchId', $batchId)
                    ->setParameter('processing', UserCardTransaction::STATUS_LL_PROCESSING)
                    ->getQuery()
                    ->getResult();

                $uctRepo = Util::em()->getRepository(UserCardTransaction::class);

                //Process all transactions in connected ACH Batch
                /** @var AchB2BTransaction $ach */
                foreach ($achQuery as $ach)
                {
                    // Get UCT and set status of ACH Transaction
                    $tranId = $ach->getTranId();
                    $uctFrom = $uctRepo->findOneBy(['tranId' => $tranId, 'tranCode' => UserCardTransaction::LL_TYPE_B2B_FROM]);
                    $ucFrom = $uctFrom->getUserCard();
                    $fromUser = $ucFrom->getUser();
                    $fromConfig = $fromUser->getConfig();

                    $uctTo = $uctRepo->findOneBy(['tranId' => $tranId, 'tranCode' => UserCardTransaction::LL_TYPE_B2B_TO]);
                    $ucTo = $uctTo->getUserCard();
                    $toUser = $ucTo->getUser();
                    $ToConfig = $toUser->getConfig();

                    $ach->setTranStatus(UserCardTransaction::STATUS_LL_SENT)
                        ->setSettleDate(new \DateTime('+3 day'))
                        ->persist();
                    $uctFrom->setAccountStatus(UserCardTransaction::STATUS_LL_SENT)
                        ->persist();
                    $uctTo->setAccountStatus(UserCardTransaction::STATUS_LL_SENT)
                        ->persist();

                    //Send Slack notification
                    SlackService::tada('B2B ACH Transaction ' . $ach->getTranId() . ' has been sent to the FED!',
                        [
                            'Ach Transaction Id' => $ach->getTranId(),
                            'Batch Id' => $batchId,
                            'To Account' => $ach->getToBankAccountId(),
                            'From Account' => $ach->getFromBankAccountId(),
                            'Transaction Amount' => '$ ' . $uctTo->getTxnAmount()/100,
                        ]);

                    $check = DailyCheck::ensureDailyCheck();
                    $check->setDate(new \DateTime())
                        ->setSalalB2bCompleted(true)
                        ->persist();

                    WebhookService::achB2BReturnWebhook($ach, self::ACH_B2B_BATCH_PREFED_SUCCESS);
                }

                //Set batch return file string and status after all items have been processed
                $achBatch->setBatchReturnString($dlFIle)
                    ->setBatchStatus(UserCardTransaction::STATUS_LL_SENT)
                    ->persist();

                //Send slack notification for finished batch settlement
                SlackService::tada('B2B ACH Batch ' . $achBatch->getId() . ' has passed Pre-FED Validation!', [
                    'ACH Batch ID' => $achBatch->getId(),
                    'ACH Batch File Name' => $achBatch->getBatchFileName(),
                    'ACH Transaction Count' => $achBatch->getTransactionCount(),
                    'Status' => $achBatch->getBatchStatus(),
                ]);

                //Delete unnecessary files from the sFTP server. (Files are stored in archive folder and database)
                $this->sftp->put($dir."archiveb2b".'/'.$file, $this->sftp->get($dir.'/'.$file));
                $this->sftp->delete($path);
                $this->sftp->delete($dir . $fileName);

                return new SuccessResponse($returnArr, 'success');
            }
        }

        return new JsonResponse(true, $files);
//        return new Response();
    }

    /**
     * @Route("/t/cron/leaflink/ach/b2b-post-fed")
     * @param Request $request
     * @return Response
     * @throws \LogicException
     * @throws \JsonException
     */
    public function postFed(Request $request)
    {
        Util::longRequest();

        // Get list of all files in sFTP
        $this->login();
        $dir = "./";
        $files = $this->sftp->nlist($dir);
        // Process all files.
        foreach ($files as $file)
        {
            //Only process files that start with fed (which are the fed return files)
            if (strpos($file, "fed_return_B2B") === 0)
            {
                Log::debug('Processing fed return b2b file ' . $file);

                $path = $dir . $file;
                $dlFIle = $this->sftp->get($path);
                $returnArr = json_decode($dlFIle, true, 512, JSON_THROW_ON_ERROR);

                // Run through list of each error in file.
                foreach ($returnArr['ach_excp_items'] as $error)
                {
                    // Find transaction by trace number.
                    $traceNumber = $error['trace_number'];
                    $txn = AchB2BTransaction::findB2BTransactionByTraceId($traceNumber);

                    self::processReturnTransaction($error, $txn);
                }

                //Delete unnecessary files from the sFTP server. (Files are stored in archive folder and database)
                $this->sftp->put($dir."archiveb2b".'/'.$file, $this->sftp->get($dir.'/'.$file));
                $this->sftp->delete($path);

//                return new SuccessResponse($returnArr, 'success');
            }
//            return new FailedResponse('No files need to be processed.', [
//                'drive' => $files
//            ]);
        }

        return new JsonResponse(true, $files);
    }

    public static function findErrorAccount(array $error, AchB2BTransaction $txn) {
        $errorAccount = null;
        $traceNumber = $error['trace_number'] ?? '';
        // Figure out account that tripped error
        if ($traceNumber === $txn->getTraceNumberFrom()) {
            $errorAccount = $txn->getFromBankAccountId();
        } else if ($traceNumber === $txn->getTraceNumberTo()) {
            $errorAccount = $txn->getToBankAccountId();
        } else {
            if (!empty($error['ach_name'])) {
                $fromUc = UserConfig::findByBankAccountId($txn->getFromBankAccountId());
                if ($fromUc?->getCompanyName() === $error['ach_name']) {
                    $errorAccount = $txn->getFromBankAccountId();
                }
                if (!$errorAccount) {
                    $toUc = UserConfig::findByBankAccountId($txn->getToBankAccountId());
                    if ($toUc?->getCompanyName() === $error['ach_name']) {
                        $errorAccount = $txn->getToBankAccountId();
                    }
                }
            }
        }
        return $errorAccount;
    }

    public static function processReturnTransaction(array $error, AchB2BTransaction $txn = null)
    {
        // If transaction is found and error code starts with R (return error)
        if ($txn !== NULL && str_starts_with($error['return_code'], 'R'))
        {
            $txn->setTranStatus(UserCardTransaction::STATUS_LL_RETURNED)
                ->setSettleDate(new \DateTime('+99 year'))
                ->persist();

            $errorAccount = self::findErrorAccount($error, $txn);
            $errorDesc = AchServiceController::getFedErrorCode($error['return_code']);

            SlackService::alert('B2B Transaction ' . $txn->getTranId() . ' has returned from the fed with an error.', [
                'Transaction' => $txn->getTranId(),
                'Error Code' => $error['return_code'],
                'Error Description' => $errorDesc,
            ]);

            WebhookService::achB2BErrorWebhook($txn, $error['return_code'], $errorDesc, $errorAccount);
            // If transaction is found and error code starts with C (NOC Code)
        } elseif ($txn !== NULL && str_starts_with($error['return_code'], 'C')) {
            $errorAccount = self::findErrorAccount($error, $txn);
            $codeDesc = AchServiceController::getFedNOCCode($error['return_code']);

            SlackService::info('B2B Transaction ' . $txn->getTranId() . ' has returned from the fed with an NOC. Transaction will still be processed.', [
                'Transaction' => $txn->getTranId(),
                'NOC Code' => $error['return_code'],
                'NOC Description' => $codeDesc,
            ], [SlackService::MENTION_HANS]);

            WebhookService::achB2BErrorWebhook($txn, $error['return_code'], $codeDesc, $errorAccount);
            // Return code is not identified.
        } elseif ($txn !== NULL) {

            $txn->setTranStatus(UserCardTransaction::STATUS_LL_QUESTIONABLE)
                ->setSettleDate(new \DateTime('+99 year'))
                ->persist();

            SlackService::alert('B2BTransaction ' . $txn->getTranId() . ' has returned from the fed with an unknown return code.', [
                'Transaction' => $txn->getTranId(),
                'NOC Code' => $error['return_code'],
            ], [SlackService::MENTION_HANS]);

            WebhookService::achB2BReturnWebhook($txn, $error['return_code']);
        } else {
            SlackService::alert('Transaction could not be found.', [
                'Trace Number' => $error['trace_number'],
                'Error Code' => $error['return_code'],
            ], [SlackService::MENTION_HANS]);
        }
    }

    /**
     * @Route("/t/cron/leaflink/ach/b2b-set-settled")
     * @param Request $request
     * @return Response
     * @throws \LogicException
     * @throws \JsonException
     */
    public function setTransactionsToSettled(Request $request)
    {
        Util::longRequest();

        SlackService::clock('Initializing LeafLink B2B Transaction Settlement.');

        $achQuery = Util::em()->getRepository(AchB2BTransaction::class)
            ->createQueryBuilder('ach')
            ->where('ach.settleDate <= :currentDate')
            ->setParameter('currentDate', new \DateTime())
            ->orderBy('ach.createdAt', 'ASC')
            ->getQuery()
            ->getResult();

        if (is_array($achQuery))
        {
            SlackService::wave('Setting LeafLink B2B transactions to settled.', [
                'Transaction Count' => count($achQuery),
            ]);
        }

        foreach ($achQuery as $ach)
        {
            $ach->setTranStatus(UserCardTransaction::STATUS_LL_SETTLED)
                ->setSettleDate(new \DateTime('+99 year'))
                ->persist();

            SlackService::check('B2B Transaction ' . $ach->getTranId() . ' has been set to settled.', [
                'Transaction' => $ach->getTranId(),
                'Status' => $ach->getTranStatus(),
            ]);

            WebhookService::achB2BReturnWebhook($ach, 'ACH Transaction settled.');
        }

        return new SuccessResponse();
    }

    /**
     * @Route("/t/cron/leaflink/ach/b2b-daily-counter-reset")
     * @param Request $request
     * @return Response
     * @throws \LogicException
     */
    public function achB2BDailyReset(Request $request)
    {
        $nachaEntity = Util::em()->getRepository(NachaEntity::class)
            ->findOneBy(['name' => 'SALAL CREDIT UNION B2B']);

        $oldDbAmount = $nachaEntity->getDailyDbCount();
        $oldCrAmount = $nachaEntity->getDailyCrCount();

        if ($oldDbAmount !== 0)
        {
            $nachaEntity->setDailyDbCount(0)
                ->persist();
        }

        if ($oldCrAmount !== 0)
        {
            $nachaEntity->setDailyCrCount(0)
                ->persist();
        }

        SlackService::check('Salal daily B2B amount counters have been reset.', [
            'Previous Debit Amount' => $oldDbAmount,
            'Previous Credit Amount' => $oldCrAmount,
            'New Debit Amount' => $nachaEntity->getDailyDbCount(),
            'New Credit Amount' => $nachaEntity->getDailyCrCount(),
        ]);

        return new Response();
    }

    /**
     * @Route("/t/cron/leaflink/ach/b2b-weekly-counter-reset")
     * @param Request $request
     * @return Response
     * @throws \LogicException
     */
    public function achB2BWeeklyReset(Request $request)
    {
        $nachaEntity = Util::em()->getRepository(NachaEntity::class)
            ->findOneBy(['name' => 'SALAL CREDIT UNION B2B']);

        $oldDbAmount = $nachaEntity->getWeeklyDbCount();
        $oldCrAmount = $nachaEntity->getWeeklyCrCount();

        if ($oldDbAmount !== 0)
        {
            $nachaEntity->setWeeklyDbCount(0)
                ->persist();
        }

        if ($oldCrAmount !== 0)
        {
            $nachaEntity->setWeeklyCrCount(0)
                ->persist();
        }

        SlackService::check('Salal weekly B2B amount counters have been reset.', [
            'Previous Debit Amount' => $oldDbAmount,
            'Previous Credit Amount' => $oldCrAmount,
            'New Debit Amount' => $nachaEntity->getWeeklyDbCount(),
            'New Credit Amount' => $nachaEntity->getWeeklyCrCount(),
        ]);

        return new Response();
    }

    /**
     * @Route("/t/cron/leaflink/ach/b2b-monthly-counter-reset")
     * @param Request $request
     * @return Response
     * @throws \LogicException
     */
    public function achB2BMonthlyReset(Request $request)
    {
        $nachaEntity = Util::em()->getRepository(NachaEntity::class)
            ->findOneBy(['name' => 'SALAL CREDIT UNION B2B']);

        $oldDbAmount = $nachaEntity->getMonthlyDbCount();
        $oldCrAmount = $nachaEntity->getMonthlyCrCount();

        if ($oldDbAmount !== 0)
        {
            $nachaEntity->setMonthlyDbCount(0)
                ->persist();
        }

        if ($oldCrAmount !== 0)
        {
            $nachaEntity->setMonthlyCrCount(0)
                ->persist();
        }

        SlackService::check('Salal monthly B2B amount counters have been reset.', [
            'Previous Debit Amount' => $oldDbAmount,
            'Previous Credit Amount' => $oldCrAmount,
            'New Debit Amount' => $nachaEntity->getMonthlyDbCount(),
            'New Credit Amount' => $nachaEntity->getMonthlyCrCount(),
        ]);

        return new Response();
    }
}
