<?php

namespace LeafLinkBundle\Entity;

use CoreBundle\Utils\Util;
use Doctrine\ORM\Mapping as ORM;

/**
 * AchB2BTransaction
 *
 * @ORM\Table(name="ach_b2_b_transaction")
 * @ORM\Entity(repositoryClass="LeafLinkBundle\Repository\AchB2BTransactionRepository")
 */
class AchB2BTransaction
{
    public function persist()
    {
        Util::persist($this);
    }

    /**
     * @param $traceNumber
     *
     * @return AchB2BTransaction|null
     */
    public static function findB2BTransactionByTraceId($traceNumber)
    {
        $rs = Util::em()->getRepository(__CLASS__)
            ->createQueryBuilder('a')
            ->where('a.traceNumberFrom = :traceNumber')
            ->orWhere('a.traceNumberTo = :traceNumber')
            ->setParameter('traceNumber', $traceNumber)
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if (!$rs) {
            return null;
        }
        return end($rs);
    }

    /**
     * @param $tranId
     *
     * @return AchB2BTransaction|null
     */
    public static function findB2BTransactionByTranId($tranId)
    {
        $rs = Util::em()->getRepository(__CLASS__)
            ->createQueryBuilder('a')
            ->where('a.tranId = :tranId')
            ->orWhere('a.tranId = :tranId')
            ->setParameter('tranId', $tranId)
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if (!$rs) {
            return null;
        }
        return end($rs);
    }

    /**
     * @param string $tranIdSuffix
     *
     * @return static[]
     */
    public static function findByTranIdSuffix(string $tranIdSuffix)
    {
        if (strlen($tranIdSuffix) <= 16) {
            throw new \RuntimeException('Transaction ID must be longer than 16 characters');
        }

        return Util::em()->getRepository(static::class)
            ->createQueryBuilder('a')
            ->where('a.tranId like :tranId')
            ->setParameter('tranId', '%' . $tranIdSuffix)
            ->getQuery()
            ->getResult();
    }

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="to_bank_account_id", type="string", length=255)
     */
    private $toBankAccountId;

    /**
     * @var string
     *
     * @ORM\Column(name="from_bank_account_id", type="string", length=255)
     */
    private $fromBankAccountId;

    /**
     * @var string
     *
     * @ORM\Column(name="tran_id", type="string", length=255)
     */
    private $tranId;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="created_at", type="datetime")
     */
    private $createdAt;

    /**
     * @var string
     *
     * @ORM\Column(name="tran_status", type="string", length=255)
     */
    private $tranStatus;

    /**
     * @var int
     *
     * @ORM\Column(name="batch_id", type="integer", nullable=true)
     */
    private $batchId;

    /**
     * @var int
     *
     * @ORM\Column(name="line_position_to", type="integer", nullable=true)
     */
    private $linePositionTo;

    /**
     * @var int
     *
     * @ORM\Column(name="line_position_from", type="integer", nullable=true)
     */
    private $linePositionFrom;

    /**
     * @var string
     *
     * @ORM\Column(name="trace_number_to", type="string", length=255, nullable=true)
     */
    private $traceNumberTo;

    /**
     * @var string
     *
     * @ORM\Column(name="trace_number_from", type="string", length=255, nullable=true)
     */
    private $traceNumberFrom;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="settle_date", type="datetime", nullable=true)
     */
    private $settleDate;


    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set toBankAccountId
     *
     * @param string $toBankAccountId
     *
     * @return AchB2BTransaction
     */
    public function setToBankAccountId($toBankAccountId)
    {
        $this->toBankAccountId = $toBankAccountId;

        return $this;
    }

    /**
     * Get toBankAccountId
     *
     * @return string
     */
    public function getToBankAccountId()
    {
        return $this->toBankAccountId;
    }

    /**
     * Set fromBankAccountId
     *
     * @param string $fromBankAccountId
     *
     * @return AchB2BTransaction
     */
    public function setFromBankAccountId($fromBankAccountId)
    {
        $this->fromBankAccountId = $fromBankAccountId;

        return $this;
    }

    /**
     * Get fromBankAccountId
     *
     * @return string
     */
    public function getFromBankAccountId()
    {
        return $this->fromBankAccountId;
    }

    /**
     * Set tranId
     *
     * @param string $tranId
     *
     * @return AchB2BTransaction
     */
    public function setTranId($tranId)
    {
        $this->tranId = $tranId;

        return $this;
    }

    /**
     * Get tranId
     *
     * @return string
     */
    public function getTranId()
    {
        return $this->tranId;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return AchB2BTransaction
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set tranStatus
     *
     * @param string $tranStatus
     *
     * @return AchB2BTransaction
     */
    public function setTranStatus($tranStatus)
    {
        $this->tranStatus = $tranStatus;

        return $this;
    }

    /**
     * Get tranStatus
     *
     * @return string
     */
    public function getTranStatus()
    {
        return $this->tranStatus;
    }

    /**
     * Set batchId
     *
     * @param integer $batchId
     *
     * @return AchB2BTransaction
     */
    public function setBatchId($batchId)
    {
        $this->batchId = $batchId;

        return $this;
    }

    /**
     * Get batchId
     *
     * @return int
     */
    public function getBatchId()
    {
        return $this->batchId;
    }

    /**
     * Set linePositionTo
     *
     * @param integer $linePositionTo
     *
     * @return AchB2BTransaction
     */
    public function setLinePositionTo($linePositionTo)
    {
        $this->linePositionTo = $linePositionTo;

        return $this;
    }

    /**
     * Get linePositionTo
     *
     * @return int
     */
    public function getLinePositionTo()
    {
        return $this->linePositionTo;
    }

    /**
     * Set linePositionFrom
     *
     * @param integer $linePositionFrom
     *
     * @return AchB2BTransaction
     */
    public function setLinePositionFrom($linePositionFrom)
    {
        $this->linePositionFrom = $linePositionFrom;

        return $this;
    }

    /**
     * Get linePositionFrom
     *
     * @return int
     */
    public function getLinePositionFrom()
    {
        return $this->linePositionFrom;
    }

    /**
     * Set traceNumberTo
     *
     * @param string $traceNumberTo
     *
     * @return AchB2BTransaction
     */
    public function setTraceNumberTo($traceNumberTo)
    {
        $this->traceNumberTo = $traceNumberTo;

        return $this;
    }

    /**
     * Get traceNumberTo
     *
     * @return string
     */
    public function getTraceNumberTo()
    {
        return $this->traceNumberTo;
    }

    /**
     * Set traceNumberFrom
     *
     * @param string $traceNumberFrom
     *
     * @return AchB2BTransaction
     */
    public function setTraceNumberFrom($traceNumberFrom)
    {
        $this->traceNumberFrom = $traceNumberFrom;

        return $this;
    }

    /**
     * Get traceNumberFrom
     *
     * @return string
     */
    public function getTraceNumberFrom()
    {
        return $this->traceNumberFrom;
    }

    /**
     * Set settleDate
     *
     * @param \DateTime $settleDate
     *
     * @return AchB2BTransaction
     */
    public function setSettleDate($settleDate)
    {
        $this->settleDate = $settleDate;

        return $this;
    }

    /**
     * Get settleDate
     *
     * @return \DateTime
     */
    public function getSettleDate()
    {
        return $this->settleDate;
    }
}

